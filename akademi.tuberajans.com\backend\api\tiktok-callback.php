<?php
require_once dirname(__DIR__) . '/config/config.php';

if (!isset($conn)) {
    die(json_encode([
        'status' => 'error',
        'message' => 'Database connection not available'
    ]));
    }
    
    try {
    if (!isset($auth)) {
        $auth = new Auth($conn);
    }
    
    $currentUser = $auth->getCurrentUser();

    if (!$currentUser) {
        http_response_code(401);
        echo json_encode([
            'status' => 'error',
            'message' => 'Unauthorized access'
        ]);
        exit;
    }

    // TikTok API yanıtını al
    $code = $_GET['code'] ?? null;
    if (!$code) {
        throw new Exception('Authorization code not provided');
    }

    // TikTok API bilgileri
    $client_key = TIKTOK_CLIENT_KEY;
    $client_secret = TIKTOK_CLIENT_SECRET;
    $redirect_uri = TIKTOK_REDIRECT_URI;

    // Access token al
    $tokenUrl = 'https://open.tiktokapis.com/v2/oauth/token/';
    $tokenData = [
        'client_key' => $client_key,
        'client_secret' => $client_secret,
        'code' => $code,
        'grant_type' => 'authorization_code',
        'redirect_uri' => $redirect_uri
    ];

    $ch = curl_init($tokenUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($tokenData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Cache-Control: no-cache'
    ]);

    $tokenResponse = curl_exec($ch);
    $tokenInfo = json_decode($tokenResponse, true);

    if (!$tokenInfo || isset($tokenInfo['error'])) {
        error_log('TikTok token error: ' . print_r($tokenInfo, true));
        throw new Exception('Failed to get access token');
    }

    // Kullanıcı bilgilerini al
    $userInfoUrl = 'https://open.tiktokapis.com/v2/user/info/';
    $fields = 'open_id,union_id,avatar_url,display_name,bio_description,profile_deep_link,is_verified,follower_count,following_count,likes_count,video_count';

    $ch = curl_init($userInfoUrl . '?fields=' . $fields);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $tokenInfo['access_token'],
        'Content-Type: application/json'
    ]);

    $userResponse = curl_exec($ch);
    $userInfo = json_decode($userResponse, true);

    if (!$userInfo || isset($userInfo['error'])) {
        error_log('TikTok user info error: ' . print_r($userInfo, true));
        throw new Exception('Failed to get user info');
    }

    // Kullanıcı bilgilerini veritabanına kaydet
    $stmt = $conn->prepare("
        UPDATE users 
        SET 
            tiktok_open_id = ?,
            tiktok_union_id = ?,
                tiktok_username = ?,
                tiktok_display_name = ?,
                tiktok_avatar_url = ?,
                tiktok_bio = ?,
            is_verified = ?,
                follower_count = ?,
                following_count = ?,
                likes_count = ?,
                video_count = ?,
                access_token = ?,
            refresh_token = ?,
            token_expires_at = DATE_ADD(NOW(), INTERVAL ? SECOND),
                tiktok_linked_at = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
    ");

    $userData = $userInfo['data']['user'];
    $stmt->bind_param(
        'ssssssiiiiissis',
        $userData['open_id'],
        $userData['union_id'],
        $userData['display_name'], // TikTok API v2'de username alanı yok
        $userData['display_name'],
        $userData['avatar_url'],
        $userData['bio_description'],
        $userData['is_verified'],
        $userData['follower_count'],
        $userData['following_count'],
        $userData['likes_count'],
        $userData['video_count'],
        $tokenInfo['access_token'],
        $tokenInfo['refresh_token'],
        $tokenInfo['expires_in'],
        $currentUser['id']
    );

    if (!$stmt->execute()) {
        throw new Exception('Failed to update user data: ' . $stmt->error);
    }

    // Başarılı yanıt
    echo "<script>
        window.opener.postMessage({
            type: 'TIKTOK_AUTH_SUCCESS',
            data: " . json_encode($userData) . "
        }, '*');
        window.close();
    </script>";

    } catch (Exception $e) {
    error_log('TikTok callback error: ' . $e->getMessage());
    echo "<script>
        window.opener.postMessage({
            type: 'TIKTOK_AUTH_ERROR',
            error: " . json_encode($e->getMessage()) . "
        }, '*');
        window.close();
    </script>";
}