import{j as l}from"./reactDnd-CIvPAkL_.js";import{r as V,g as nr}from"./vendor-CnpYymF8.js";import{V as J,k as un,j as Vr,a as $r}from"./App-DhIV03Gw.js";import{R as rr,T as cn,t as Ur}from"./tr-Bk4hK31u.js";import{c as Z}from"./createLucideIcon-DxVmGoQf.js";import{S as tt,T as qr}from"./trash-2-C8zHBfrV.js";import{P as Yr}from"./plus-DjpIx7VF.js";import{X as Kr}from"./x-BlF-lTk7.js";import{E as dn}from"./eye-C0V96B8t.js";import{f as Gr}from"./utils-CtuI0RRe.js";import"./index-CVO3aNyS.js";import"./antd-gS---Efz.js";import"./charts-CXWFy-zF.js";const Jr=[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]],Wr=Z("bot",Jr);const Xr=[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]],Lt=Z("brain",Xr);const Qr=[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]],ot=Z("chart-no-axes-column-increasing",Qr);const Zr=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],ei=Z("check",Zr);const ti=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]],ir=Z("circle-alert",ti);const ni=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]],ar=Z("circle-help",ni);const ri=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}]],pn=Z("circle-minus",ri);const ii=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]],wt=Z("circle-plus",ii);const ai=[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]],Xe=Z("database",ai);const li=[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]],mn=Z("dollar-sign",li);const si=[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]],hn=Z("eye-off",si);const oi=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],We=Z("file-text",oi);const ui=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],lr=Z("globe",ui);const ci=[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]],di=Z("message-square",ci);const pi=[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]],mi=Z("send",pi);const hi=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],fi=Z("settings",hi);const gi=[["line",{x1:"4",x2:"4",y1:"21",y2:"14",key:"1p332r"}],["line",{x1:"4",x2:"4",y1:"10",y2:"3",key:"gb41h5"}],["line",{x1:"12",x2:"12",y1:"21",y2:"12",key:"hf2csr"}],["line",{x1:"12",x2:"12",y1:"8",y2:"3",key:"1kfi7u"}],["line",{x1:"20",x2:"20",y1:"21",y2:"16",key:"1lhrwl"}],["line",{x1:"20",x2:"20",y1:"12",y2:"3",key:"16vvfq"}],["line",{x1:"2",x2:"6",y1:"14",y2:"14",key:"1uebub"}],["line",{x1:"10",x2:"14",y1:"8",y2:"8",key:"1yglbp"}],["line",{x1:"18",x2:"22",y1:"16",y2:"16",key:"1jxqpz"}]],xi=Z("sliders-vertical",gi);const yi=[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]],ki=Z("table",yi);const bi=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]],wi=Z("target",bi);const vi=[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]],ji=Z("triangle-alert",vi);const Ni=[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]],fn=Z("upload",Ni);const Si=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],gn=Z("user",Si),xn=[{tableName:"users",description:"Sistem kullanıcıları",columns:[{name:"id",type:"int",description:"Kullanıcı ID"},{name:"username",type:"varchar",description:"Kullanıcı adı"},{name:"email",type:"varchar",description:"E-posta adresi"},{name:"password",type:"varchar",description:"Şifre (hash)"},{name:"role",type:"varchar",description:"Kullanıcı rolü"},{name:"created_at",type:"timestamp",description:"Oluşturulma tarihi"}]},{tableName:"yayinci",description:"Yayıncı temel bilgileri",columns:[{name:"id",type:"int",description:"Yayıncı ID"},{name:"kullanici_adi",type:"varchar",description:"TikTok kullanıcı adı"},{name:"ad_soyad",type:"varchar",description:"Gerçek adı ve soyadı"},{name:"telefon",type:"varchar",description:"İletişim telefonu"},{name:"email",type:"varchar",description:"E-posta adresi"},{name:"kayit_tarihi",type:"date",description:"Kayıt tarihi"}]},{tableName:"publisher_info",description:"Yayıncı detaylı bilgileri",columns:[{name:"id",type:"int",description:"Bilgi ID"},{name:"yayinci_id",type:"int",description:"Yayıncı ID (foreign key)"},{name:"toplam_takipci",type:"int",description:"Toplam takipçi sayısı"},{name:"toplam_begeni",type:"int",description:"Toplam beğeni sayısı"},{name:"kategori",type:"varchar",description:"İçerik kategorisi"},{name:"bio",type:"text",description:"Profil açıklaması"}]},{tableName:"performans",description:"Yayıncı performans metrikleri",columns:[{name:"id",type:"int",description:"Performans kaydı ID"},{name:"yayinci_id",type:"int",description:"Yayıncı ID (foreign key)"},{name:"tarih",type:"date",description:"Kayıt tarihi"},{name:"izlenme",type:"int",description:"İzlenme sayısı"},{name:"elmaslar",type:"int",description:"Kazanılan elmas miktarı"},{name:"yeni_takipciler",type:"int",description:"Yeni takipçi sayısı"}]},{tableName:"weekly_archive",description:"Haftalık performans arşivi",columns:[{name:"id",type:"int",description:"Arşiv kaydı ID"},{name:"yayinci_id",type:"int",description:"Yayıncı ID (foreign key)"},{name:"hafta_baslangici",type:"date",description:"Hafta başlangıç tarihi"},{name:"hafta_bitisi",type:"date",description:"Hafta bitiş tarihi"},{name:"toplam_elmas",type:"int",description:"Haftalık toplam elmas"},{name:"canli_yayin_gunu",type:"int",description:"Canlı yayın yapılan gün sayısı"}]},{tableName:"etkinlikler",description:"Ajans etkinlikleri",columns:[{name:"id",type:"int",description:"Etkinlik ID"},{name:"baslik",type:"varchar",description:"Etkinlik başlığı"},{name:"aciklama",type:"text",description:"Etkinlik açıklaması"},{name:"baslangic_tarihi",type:"datetime",description:"Başlangıç tarihi ve saati"},{name:"bitis_tarihi",type:"datetime",description:"Bitiş tarihi ve saati"}]},{tableName:"etkinlik_katilimcilar",description:"Etkinlik katılımcıları",columns:[{name:"id",type:"int",description:"Katılım kaydı ID"},{name:"etkinlik_id",type:"int",description:"Etkinlik ID (foreign key)"},{name:"yayinci_id",type:"int",description:"Yayıncı ID (foreign key)"},{name:"katilim_durumu",type:"varchar",description:"Katılım durumu (onaylandı, reddedildi, beklemede)"}]},{tableName:"gorevler",description:"Yayıncı görevleri",columns:[{name:"id",type:"int",description:"Görev ID"},{name:"yayinci_id",type:"int",description:"Yayıncı ID (foreign key)"},{name:"baslik",type:"varchar",description:"Görev başlığı"},{name:"aciklama",type:"text",description:"Görev açıklaması"},{name:"son_tarih",type:"datetime",description:"Son tamamlanma tarihi"},{name:"durum",type:"varchar",description:"Görev durumu (tamamlandı, devam ediyor, beklemede)"}]},{tableName:"pk_eslesmeleri",description:"PK maçları ve eşleştirmeleri",columns:[{name:"id",type:"int",description:"Eşleştirme ID"},{name:"yayinci1_id",type:"int",description:"Birinci yayıncı ID"},{name:"yayinci2_id",type:"int",description:"İkinci yayıncı ID"},{name:"tarih",type:"datetime",description:"Maç tarihi ve saati"},{name:"durum",type:"varchar",description:"Maç durumu (planlandı, tamamlandı, iptal edildi)"},{name:"kazanan_id",type:"int",description:"Kazanan yayıncı ID (maç sonunda doldurulur)"}]}],Ci=({onSave:e})=>{const[t,n]=V.useState([]),[r,i]=V.useState(!1),[a,s]=V.useState("idle"),[o,d]=V.useState(null),[u,c]=V.useState(!1);V.useEffect(()=>{const x=localStorage.getItem("ai_db_schema");if(x)try{n(JSON.parse(x))}catch(j){n(xn)}else n(xn)},[]);const m=()=>{const x={tableName:`yeni_tablo_${t.length+1}`,columns:[{name:"id",type:"int",description:"Otomatik ID"},{name:"ad",type:"varchar",description:"Ad alanı"}],description:"Yeni tablo açıklaması"};n([...t,x]),d(t.length)},g=x=>{if(window.confirm(`"${t[x].tableName}" tablosunu silmek istediğinize emin misiniz?`)){const j=[...t];j.splice(x,1),n(j),o===x?d(null):o&&o>x&&d(o-1)}},p=(x,j)=>{const M=[...t];M[x].tableName=j,n(M)},w=(x,j)=>{const M=[...t];M[x].description=j,n(M)},k=x=>{const j=[...t];j[x].columns.push({name:`yeni_kolon_${j[x].columns.length+1}`,type:"varchar",description:"Yeni kolon açıklaması"}),n(j)},A=(x,j)=>{const M=[...t];M[x].columns.splice(j,1),n(M)},b=(x,j,M)=>{const L=[...t];L[x].columns[j].name=M,n(L)},_=(x,j,M)=>{const L=[...t];L[x].columns[j].type=M,n(L)},C=(x,j,M)=>{const L=[...t];L[x].columns[j].description=M,n(L)},H=()=>{try{localStorage.setItem("ai_db_schema",JSON.stringify(t)),e(t),J.success("Veritabanı şeması başarıyla kaydedildi")}catch(x){J.error("Şema kaydedilirken bir hata oluştu")}},F=async()=>{s("loading"),i(!0);try{const x=await fetch("https://x.tuberajans.com/backend/x-site/db-schema.php");if(!x.ok)throw new Error(`API hatası: ${x.status}`);const j=await x.json();if(j.success&&j.tables)n(j.tables),s("success"),J.success("Veritabanı şeması başarıyla tespit edildi");else throw new Error(j.error||"Şema alınamadı")}catch(x){s("error"),J.error("Veritabanı şeması tespit edilirken bir hata oluştu")}finally{i(!1)}};return l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{className:"flex justify-between items-center bg-indigo-50 dark:bg-indigo-900 dark:bg-opacity-20 p-3 rounded-lg border-l-4 border-indigo-500",children:[l.jsxs("div",{className:"flex items-start",children:[l.jsx(Xe,{className:"flex-shrink-0 h-5 w-5 text-indigo-500 mt-1 mr-2"}),l.jsxs("div",{children:[l.jsx("h3",{className:"text-md font-medium text-indigo-800 dark:text-indigo-300",children:"AI için Veritabanı Erişimi"}),l.jsx("p",{className:"text-sm text-indigo-700 dark:text-indigo-400",children:"AI'nın veritabanı şemanızı ve tablolarınızı anlamasını sağlayarak daha doğru ve yerinde öneriler alın."})]})]}),l.jsx("button",{onClick:()=>c(!u),className:"text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300",children:l.jsx(ar,{className:"h-5 w-5"})})]}),u&&l.jsxs("div",{className:"bg-blue-50 dark:bg-blue-900 dark:bg-opacity-20 p-4 rounded-lg border border-blue-200 dark:border-blue-800",children:[l.jsx("h4",{className:"font-medium text-blue-800 dark:text-blue-300 mb-2",children:"Veritabanı Şeması Hakkında"}),l.jsx("p",{className:"text-sm text-blue-700 dark:text-blue-400 mb-2",children:"Bu bölümde, AI'nın veritabanınızı anlamasını sağlayacak şema bilgilerini tanımlayabilirsiniz."}),l.jsxs("ul",{className:"text-sm text-blue-700 dark:text-blue-400 list-disc list-inside",children:[l.jsx("li",{children:"AI, bu şema bilgilerini kullanarak veritabanınıza özgü öneriler verebilir"}),l.jsx("li",{children:"Tablolarınız, sütunlarınız ve ilişkileriniz hakkında detaylı bilgi sağlayın"}),l.jsx("li",{children:'"Otomatik Tespit" butonu, veritabanınızdaki tabloları otomatik olarak bulmaya çalışır'}),l.jsx("li",{children:"Manuel olarak tablo ve sütun ekleyebilir, düzenleyebilir veya kaldırabilirsiniz"}),l.jsx("li",{children:"Değişikliklerinizi kaydetmeyi unutmayın"})]})]}),l.jsxs("div",{className:"flex space-x-4 mb-4",children:[l.jsxs("button",{onClick:F,disabled:r,className:"px-4 py-2 bg-purple-600 text-white rounded-lg flex items-center hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed",children:[a==="loading"?l.jsx(rr,{className:"w-4 h-4 mr-2 animate-spin"}):l.jsx(Xe,{className:"w-4 h-4 mr-2"}),"Otomatik Tespit"]}),l.jsxs("button",{onClick:m,className:"px-4 py-2 bg-blue-600 text-white rounded-lg flex items-center hover:bg-blue-700",children:[l.jsx(wt,{className:"w-4 h-4 mr-2"}),"Tablo Ekle"]}),l.jsxs("button",{onClick:H,className:"px-4 py-2 bg-green-600 text-white rounded-lg flex items-center hover:bg-green-700 ml-auto",children:[l.jsx(tt,{className:"w-4 h-4 mr-2"}),"Kaydet"]})]}),a==="success"&&l.jsxs("div",{className:"bg-green-50 dark:bg-green-900 dark:bg-opacity-20 p-3 rounded-lg flex items-start mb-4",children:[l.jsx(ei,{className:"h-5 w-5 text-green-500 mr-2 mt-0.5"}),l.jsx("p",{className:"text-sm text-green-700 dark:text-green-300",children:"Veritabanı şeması başarıyla tespit edildi. Düzenlemeler yapabilir veya olduğu gibi kaydedebilirsiniz."})]}),a==="error"&&l.jsxs("div",{className:"bg-red-50 dark:bg-red-900 dark:bg-opacity-20 p-3 rounded-lg flex items-start mb-4",children:[l.jsx(ir,{className:"h-5 w-5 text-red-500 mr-2 mt-0.5"}),l.jsx("p",{className:"text-sm text-red-700 dark:text-red-300",children:"Veritabanı şeması tespit edilirken bir hata oluştu. Lütfen manuel olarak tanımlamayı deneyin."})]}),l.jsx("div",{className:"space-y-6",children:t.map((x,j)=>l.jsxs("div",{className:`border dark:border-gray-700 rounded-lg overflow-hidden ${o===j?"border-indigo-500 dark:border-indigo-400 ring-1 ring-indigo-500":""}`,children:[l.jsxs("div",{className:"bg-gray-50 dark:bg-gray-800 p-3 flex justify-between items-center",children:[l.jsxs("div",{className:"flex items-center",children:[l.jsx(ki,{className:"h-5 w-5 text-gray-500 dark:text-gray-400 mr-2"}),o===j?l.jsx("input",{type:"text",value:x.tableName,onChange:M=>p(j,M.target.value),className:"px-2 py-1 border rounded dark:bg-gray-700 dark:border-gray-600 dark:text-white"}):l.jsx("span",{className:"font-medium text-gray-800 dark:text-white",children:x.tableName})]}),l.jsxs("div",{className:"flex space-x-2",children:[l.jsx("button",{onClick:()=>d(o===j?null:j),className:"p-1 text-gray-500 hover:text-indigo-600 dark:text-gray-400 dark:hover:text-indigo-400",children:o===j?"Bitir":"Düzenle"}),l.jsx("button",{onClick:()=>g(j),className:"p-1 text-gray-500 hover:text-red-600 dark:text-gray-400 dark:hover:text-red-400",children:l.jsx(pn,{className:"h-5 w-5"})})]})]}),l.jsx("div",{className:"px-4 py-2 bg-gray-100 dark:bg-gray-750 border-t border-b dark:border-gray-700",children:l.jsxs("div",{className:"flex items-center text-sm text-gray-600 dark:text-gray-300",children:[l.jsx("span",{className:"font-medium mr-2",children:"Açıklama:"}),o===j?l.jsx("input",{type:"text",value:x.description||"",onChange:M=>w(j,M.target.value),className:"flex-1 px-2 py-1 border rounded dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"Tablo açıklaması"}):l.jsx("span",{children:x.description||"Açıklama yok"})]})}),l.jsx("div",{className:"overflow-x-auto",children:l.jsxs("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[l.jsx("thead",{className:"bg-gray-50 dark:bg-gray-700",children:l.jsxs("tr",{children:[l.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Kolon Adı"}),l.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Veri Tipi"}),l.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Açıklama"}),o===j&&l.jsx("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"İşlemler"})]})}),l.jsx("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:x.columns.map((M,L)=>l.jsxs("tr",{children:[l.jsx("td",{className:"px-4 py-2 whitespace-nowrap",children:o===j?l.jsx("input",{type:"text",value:M.name,onChange:z=>b(j,L,z.target.value),className:"w-full px-2 py-1 border rounded dark:bg-gray-700 dark:border-gray-600 dark:text-white"}):l.jsx("span",{className:"text-sm font-medium text-gray-800 dark:text-white",children:M.name})}),l.jsx("td",{className:"px-4 py-2 whitespace-nowrap",children:o===j?l.jsxs("select",{value:M.type,onChange:z=>_(j,L,z.target.value),className:"px-2 py-1 border rounded dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[l.jsx("option",{value:"int",children:"int"}),l.jsx("option",{value:"varchar",children:"varchar"}),l.jsx("option",{value:"text",children:"text"}),l.jsx("option",{value:"date",children:"date"}),l.jsx("option",{value:"datetime",children:"datetime"}),l.jsx("option",{value:"timestamp",children:"timestamp"}),l.jsx("option",{value:"decimal",children:"decimal"}),l.jsx("option",{value:"boolean",children:"boolean"})]}):l.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-300",children:M.type})}),l.jsx("td",{className:"px-4 py-2",children:o===j?l.jsx("input",{type:"text",value:M.description||"",onChange:z=>C(j,L,z.target.value),className:"w-full px-2 py-1 border rounded dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"Kolon açıklaması"}):l.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-300",children:M.description||"Açıklama yok"})}),o===j&&l.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-right",children:l.jsx("button",{onClick:()=>A(j,L),className:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300",children:l.jsx(pn,{className:"h-4 w-4"})})})]},L))})]})}),o===j&&l.jsx("div",{className:"p-3 border-t dark:border-gray-700 bg-gray-50 dark:bg-gray-800",children:l.jsxs("button",{onClick:()=>k(j),className:"px-3 py-1 bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:bg-opacity-30 dark:text-indigo-300 rounded flex items-center text-sm hover:bg-indigo-200 dark:hover:bg-opacity-50",children:[l.jsx(wt,{className:"h-4 w-4 mr-1"}),"Kolon Ekle"]})})]},j))}),t.length===0&&l.jsxs("div",{className:"border dark:border-gray-700 rounded-lg p-8 text-center",children:[l.jsx(Xe,{className:"h-12 w-12 text-gray-400 dark:text-gray-500 mx-auto mb-3"}),l.jsx("h3",{className:"text-lg font-medium text-gray-700 dark:text-gray-300",children:"Henüz Tablo Yok"}),l.jsx("p",{className:"text-gray-500 dark:text-gray-400 mt-1",children:'"Tablo Ekle" butonunu kullanarak veritabanı şemanızı tanımlamaya başlayın veya "Otomatik Tespit" ile mevcut tabloları bulmayı deneyin.'}),l.jsx("div",{className:"mt-4",children:l.jsxs("button",{onClick:m,className:"px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700",children:[l.jsx(wt,{className:"inline-block h-4 w-4 mr-1"}),"İlk Tabloyu Ekle"]})})]}),t.length>0&&l.jsx("div",{className:"flex justify-end mt-6",children:l.jsxs("button",{onClick:H,className:"px-4 py-2 bg-indigo-600 text-white rounded-lg flex items-center hover:bg-indigo-700 transition-colors",children:[l.jsx(tt,{className:"w-4 h-4 mr-2"}),"Veritabanı Şemasını Kaydet"]})})]})},Ei=({onSave:e})=>{const[t,n]=V.useState([]),[r,i]=V.useState(""),[a,s]=V.useState(""),[o,d]=V.useState(!1);V.useEffect(()=>{const p=localStorage.getItem("ai_knowledge_sources");if(p)try{const k=JSON.parse(p).map(A=>({...A,dateAdded:new Date(A.dateAdded)}));n(k)}catch(w){J.error("Bilgi kaynakları yüklenirken bir hata oluştu")}},[]);const u=p=>{try{localStorage.setItem("ai_knowledge_sources",JSON.stringify(p)),n(p),e(p),J.success("Bilgi kaynakları kaydedildi")}catch(w){J.error("Bilgi kaynakları kaydedilirken bir hata oluştu")}},c=()=>{if(!r.trim()){J.error("Not başlığı gereklidir");return}if(!a.trim()){J.error("Not içeriği gereklidir");return}const p={id:`note-${Date.now()}`,type:"note",title:r,content:a,dateAdded:new Date},w=[...t,p];u(w),i(""),s("")},m=p=>{var w;const k=(w=p.target.files)==null?void 0:w[0];if(!k)return;if(k.size>10*1024*1024){J.error("Dosya boyutu 10MB'yi geçemez");return}if(!["application/pdf","text/plain","application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","text/csv"].includes(k.type)){J.error("Desteklenmeyen dosya türü. PDF, TXT, Excel veya CSV dosyası yükleyin");return}const b=new FileReader;b.onload=()=>{const _={id:`file-${Date.now()}`,type:"file",title:k.name,fileName:k.name,fileUrl:URL.createObjectURL(k),dateAdded:new Date},C=[...t,_];u(C)},b.readAsDataURL(k)},g=p=>{const w=t.filter(k=>k.id!==p);u(w)};return l.jsxs("div",{className:"space-y-6",children:[o&&l.jsxs("div",{className:"bg-blue-50 dark:bg-blue-900 dark:bg-opacity-20 p-4 rounded-lg border border-blue-200 dark:border-blue-800 mb-4",children:[l.jsx("h4",{className:"font-medium text-blue-800 dark:text-blue-300 mb-2",children:"Bilgi Kaynakları Hakkında"}),l.jsx("p",{className:"text-sm text-blue-700 dark:text-blue-400 mb-2",children:"AI Danışmanınızın kullanabileceği bilgi kaynaklarını buradan ekleyebilirsiniz."}),l.jsxs("ul",{className:"text-sm text-blue-700 dark:text-blue-400 list-disc list-inside",children:[l.jsx("li",{children:"Notlar: Stratejiler, özel bilgiler, prosedürler gibi metin tabanlı bilgiler ekleyin"}),l.jsx("li",{children:"Dosyalar: PDF, Excel, CSV veya metin dosyaları yükleyerek referans materyaller sağlayın"}),l.jsx("li",{children:"AI, eklediğiniz bu bilgi kaynaklarını kullanarak daha spesifik yanıtlar verebilecek"}),l.jsx("li",{children:"Verileriniz hiçbir zaman başka kullanıcılarla paylaşılmaz"})]})]}),l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("h3",{className:"text-lg font-medium flex items-center",children:[l.jsx(We,{className:"w-5 h-5 text-indigo-500 mr-2"}),"Mevcut Bilgi Kaynakları"]}),l.jsx("button",{onClick:()=>d(!o),className:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300",children:l.jsx(ar,{className:"w-5 h-5"})})]}),t.length>0?l.jsx("div",{className:"border dark:border-gray-700 rounded-lg divide-y divide-gray-200 dark:divide-gray-700",children:t.map(p=>l.jsxs("div",{className:"p-4 flex items-start",children:[l.jsx("div",{className:"flex-shrink-0 mr-3",children:p.type==="note"?l.jsx(We,{className:"h-5 w-5 text-indigo-500"}):l.jsx(We,{className:"h-5 w-5 text-orange-500"})}),l.jsxs("div",{className:"flex-grow",children:[l.jsx("h4",{className:"font-medium text-gray-900 dark:text-white",children:p.title}),l.jsxs("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:[p.type==="note"?"Not":"Dosya"," •",` Eklenme: ${p.dateAdded.toLocaleDateString("tr-TR")}`]}),p.type==="note"&&p.content&&l.jsx("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-300 line-clamp-2",children:p.content})]}),l.jsx("button",{onClick:()=>g(p.id),className:"flex-shrink-0 ml-2 p-1 text-gray-500 hover:text-red-500 dark:text-gray-400",children:l.jsx(qr,{className:"h-4 w-4"})})]},p.id))}):l.jsxs("div",{className:"text-center p-8 border dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800",children:[l.jsx(We,{className:"h-12 w-12 text-gray-400 mx-auto mb-3"}),l.jsx("h3",{className:"text-lg font-medium text-gray-700 dark:text-gray-300",children:"Henüz bilgi kaynağı eklenmedi"}),l.jsx("p",{className:"text-gray-500 dark:text-gray-400 mt-1",children:"AI Danışmanınıza bilgi kaynakları ekleyerek daha iyi yanıtlar alın."})]}),l.jsxs("div",{className:"border dark:border-gray-700 rounded-lg p-4",children:[l.jsxs("h3",{className:"font-medium text-gray-900 dark:text-white mb-4 flex items-center",children:[l.jsx(Yr,{className:"w-4 h-4 mr-2"}),"Not Ekle"]}),l.jsxs("div",{className:"space-y-3",children:[l.jsx("div",{children:l.jsx("input",{type:"text",value:r,onChange:p=>i(p.target.value),placeholder:"Not Başlığı",className:"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})}),l.jsx("div",{children:l.jsx("textarea",{value:a,onChange:p=>s(p.target.value),placeholder:"Not İçeriği...",rows:5,className:"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})}),l.jsx("div",{className:"flex justify-end",children:l.jsxs("button",{onClick:c,className:"px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 flex items-center",children:[l.jsx(tt,{className:"w-4 h-4 mr-2"}),"Not Ekle"]})})]})]}),l.jsxs("div",{className:"border dark:border-gray-700 rounded-lg p-4",children:[l.jsxs("h3",{className:"font-medium text-gray-900 dark:text-white mb-4 flex items-center",children:[l.jsx(fn,{className:"w-4 h-4 mr-2"}),"Dosya Yükle"]}),l.jsx("div",{className:"flex items-center justify-center w-full",children:l.jsxs("label",{htmlFor:"file-upload",className:"flex flex-col items-center justify-center w-full h-40 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-gray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500",children:[l.jsxs("div",{className:"flex flex-col items-center justify-center pt-5 pb-6",children:[l.jsx(fn,{className:"w-8 h-8 mb-3 text-gray-500 dark:text-gray-400"}),l.jsxs("p",{className:"mb-2 text-sm text-gray-500 dark:text-gray-400",children:[l.jsx("span",{className:"font-semibold",children:"Dosya yüklemek için tıklayın"})," veya sürükleyip bırakın"]}),l.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"PDF, Excel, CSV veya metin dosyaları (Maks. 10MB)"})]}),l.jsx("input",{id:"file-upload",type:"file",className:"hidden",onChange:m,accept:".pdf,.txt,.csv,.xls,.xlsx"})]})})]})]})},Ai=({isOpen:e,onClose:t,systemPrompt:n,onSystemPromptChange:r,onSaveSettings:i,onSaveKnowledgeSources:a})=>{const[s,o]=V.useState("personality"),[d,u]=V.useState([]),[c,m]=V.useState(""),[g,p]=V.useState(""),[w,k]=V.useState(!1),[A,b]=V.useState(!1),[_,C]=V.useState([]),[H,F]=V.useState(!1),x=async()=>{try{F(!0);const z=await un.get("/X/api/ai-advisor.php?endpoint=get-system-prompt");z.data.success&&z.data.system_prompt&&(r(z.data.system_prompt),J.success("Sistem promptu veritabanından yüklendi"))}catch(z){J.error("Sistem promptu yüklenemedi")}finally{F(!1)}};V.useEffect(()=>{s==="personality"&&e&&x();const z=localStorage.getItem("ai_knowledge_sources");if(z)try{const I=JSON.parse(z).map(E=>({...E,dateAdded:new Date(E.dateAdded)}));u(I)}catch(P){}const D=localStorage.getItem("ai_web_search_enabled");D&&k(JSON.parse(D));const T=localStorage.getItem("ai_continuous_learning");T&&b(JSON.parse(T))},[s,e]);const j=z=>{localStorage.setItem("ai_knowledge_sources",JSON.stringify(z)),u(z),a&&a(z)},M=z=>{C(z),localStorage.setItem("ai_db_schema",JSON.stringify(z))},L=()=>{try{F(!0),localStorage.setItem("ai_web_search_enabled",JSON.stringify(w)),localStorage.setItem("ai_continuous_learning",JSON.stringify(A)),un.post("/X/api/ai-advisor.php?endpoint=save-system-prompt",{systemPrompt:n}).then(z=>{J.success("Ayarlar veritabanına kaydedildi"),i(),t()}).catch(z=>{J.error("Ayarlar kaydedilirken bir hata oluştu")}).finally(()=>{F(!1)})}catch(z){J.error("Ayarlar kaydedilirken bir hata oluştu"),F(!1)}};return e?l.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-60 overflow-y-auto h-full w-full z-50 backdrop-blur-sm",children:l.jsxs("div",{className:"relative top-10 mx-auto p-5 border w-4/5 max-w-4xl shadow-2xl rounded-xl bg-white dark:bg-gray-800 dark:border-gray-700",children:[l.jsxs("div",{className:"flex justify-between items-center mb-6",children:[l.jsx("h2",{className:"text-xl font-bold bg-gradient-to-r from-indigo-500 to-purple-600 bg-clip-text text-transparent",children:"AI Danışman Gelişmiş Ayarları"}),l.jsx("button",{onClick:t,className:"text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-gray-100 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700",children:l.jsx(Kr,{className:"w-6 h-6"})})]}),l.jsxs("div",{className:"flex space-x-4 border-b dark:border-gray-700 mb-6",children:[l.jsx(ut,{active:s==="personality",onClick:()=>o("personality"),icon:l.jsx(Lt,{className:"w-4 h-4 mr-2"}),label:"Kişilik ve Rol"}),l.jsx(ut,{active:s==="knowledge",onClick:()=>o("knowledge"),icon:l.jsx(We,{className:"w-4 h-4 mr-2"}),label:"Bilgi Kaynakları"}),l.jsx(ut,{active:s==="database",onClick:()=>o("database"),icon:l.jsx(Xe,{className:"w-4 h-4 mr-2"}),label:"Veritabanı Erişimi"}),l.jsx(ut,{active:s==="capabilities",onClick:()=>o("capabilities"),icon:l.jsx(lr,{className:"w-4 h-4 mr-2"}),label:"Yetenekler"})]}),s==="personality"&&l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Sistem Promptu (AI'nın rolü ve davranışı)"}),l.jsx("textarea",{value:n,onChange:z=>r(z.target.value),rows:12,className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"AI'nın rolünü, uzmanlık alanlarını ve davranış şeklini tanımlayın..."}),l.jsx("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Bu metin AI'nın nasıl davranacağını, hangi rolü benimseyeceğini ve bilgi tabanını nasıl kullanacağını belirler."})]}),l.jsx("div",{className:"p-3 bg-yellow-50 dark:bg-yellow-900 dark:bg-opacity-20 border-l-4 border-yellow-500 rounded",children:l.jsxs("div",{className:"flex",children:[l.jsx(ir,{className:"w-5 h-5 text-yellow-500 mr-2 flex-shrink-0"}),l.jsx("p",{className:"text-sm text-yellow-700 dark:text-yellow-200",children:"AI Danışmanın kişiliği ve uzmanlık alanları burada belirttiğiniz yönergelere göre şekillenecektir. Mümkün olduğunca açık, net ve detaylı yönergeler vermeye çalışın."})]})}),l.jsx("div",{className:"flex justify-end mt-2",children:l.jsx("button",{onClick:x,disabled:H,className:"text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 flex items-center",children:H?l.jsxs(l.Fragment,{children:[l.jsx("span",{className:"animate-spin mr-1",children:"⟳"}),"Yükleniyor..."]}):l.jsx(l.Fragment,{children:"⟳ Veritabanından yükle"})})})]}),s==="knowledge"&&l.jsx("div",{style:{maxHeight:"55vh",overflowY:"auto",marginBottom:16},children:l.jsx(Ei,{onSave:z=>{j(z),a&&a(z)}})}),s==="database"&&l.jsx(Ci,{onSave:M}),s==="capabilities"&&l.jsxs("div",{className:"space-y-6",children:[l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx("h3",{className:"text-md font-medium text-gray-900 dark:text-white",children:"Web Arama Yetenekleri"}),l.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"AI'nın internette arama yapmasına ve gerçek zamanlı veriler kullanmasına izin verin"})]}),l.jsxs("div",{className:"relative inline-block w-12 mr-2 align-middle select-none",children:[l.jsx("input",{type:"checkbox",id:"web-search",checked:w,onChange:()=>k(!w),className:"toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"}),l.jsx("label",{htmlFor:"web-search",className:`toggle-label block overflow-hidden h-6 rounded-full cursor-pointer ${w?"bg-indigo-500":"bg-gray-300 dark:bg-gray-700"}`})]})]}),l.jsx("div",{className:"p-3 bg-blue-50 dark:bg-blue-900 dark:bg-opacity-20 rounded-lg",children:l.jsx("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:"Bu özellik etkinleştirildiğinde, AI'nız soruları yanıtlarken internette arama yapabilir ve gerçek zamanlı bilgiler kullanabilir. Bu, ajans stratejileri ve sektör trendleri hakkında güncel bilgi almanızı sağlar."})})]}),l.jsxs("div",{className:"border-t dark:border-gray-700 pt-6 space-y-4",children:[l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx("h3",{className:"text-md font-medium text-gray-900 dark:text-white",children:"Sürekli Öğrenme"}),l.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"AI'nın konuşmalardan öğrenmesine ve zamanla gelişmesine izin verin"})]}),l.jsxs("div",{className:"relative inline-block w-12 mr-2 align-middle select-none",children:[l.jsx("input",{type:"checkbox",id:"continuous-learning",checked:A,onChange:()=>b(!A),className:"toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"}),l.jsx("label",{htmlFor:"continuous-learning",className:`toggle-label block overflow-hidden h-6 rounded-full cursor-pointer ${A?"bg-indigo-500":"bg-gray-300 dark:bg-gray-700"}`})]})]}),l.jsx("div",{className:"p-3 bg-blue-50 dark:bg-blue-900 dark:bg-opacity-20 rounded-lg",children:l.jsx("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:"Bu özellik etkinleştirildiğinde, AI konuşmalarınızdan öğrenir ve zaman içinde daha iyi hale gelir. Kullanıcı tercihleri, ajans stratejileri ve sık sorulan sorular hakkında bilgi toplar."})})]})]}),l.jsxs("div",{className:"mt-6 flex justify-end space-x-3",children:[l.jsx("button",{onClick:t,className:"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200 dark:hover:bg-gray-600",children:"İptal"}),l.jsx("button",{onClick:L,disabled:H,className:"px-4 py-2 rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 flex items-center",children:H?l.jsxs(l.Fragment,{children:[l.jsx("span",{className:"animate-spin mr-2",children:"⟳"}),"Kaydediliyor..."]}):l.jsxs(l.Fragment,{children:[l.jsx(tt,{className:"w-4 h-4 mr-2"}),"Ayarları Kaydet"]})})]})]})}):null},ut=({active:e,onClick:t,icon:n,label:r})=>l.jsxs("button",{onClick:t,className:`flex items-center px-4 py-2 border-b-2 text-sm font-medium ${e?"border-indigo-500 text-indigo-600 dark:text-indigo-400 dark:border-indigo-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600"}`,children:[n,r]}),Ii=`
  .toggle-checkbox:checked {
    transform: translateX(100%);
    border-color: #4f46e5;
  }
  .toggle-label {
    transition: background-color 0.2s ease-in;
  }
`;if(typeof document!="undefined"){const e=document.createElement("style");e.innerHTML=Ii,document.head.appendChild(e)}function zi(e,t){const n={};return(e[e.length-1]===""?[...e,""]:e).join((n.padRight?" ":"")+","+(n.padLeft===!1?"":" ")).trim()}const Ti=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,_i=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,Pi={};function yn(e,t){return(Pi.jsx?_i:Ti).test(e)}const Di=/[ \t\n\f\r]/g;function Mi(e){return typeof e=="object"?e.type==="text"?kn(e.value):!1:kn(e)}function kn(e){return e.replace(Di,"")===""}class rt{constructor(t,n,r){this.normal=n,this.property=t,r&&(this.space=r)}}rt.prototype.normal={};rt.prototype.property={};rt.prototype.space=void 0;function sr(e,t){const n={},r={};for(const i of e)Object.assign(n,i.property),Object.assign(r,i.normal);return new rt(n,r,t)}function Ot(e){return e.toLowerCase()}class de{constructor(t,n){this.attribute=n,this.property=t}}de.prototype.attribute="";de.prototype.booleanish=!1;de.prototype.boolean=!1;de.prototype.commaOrSpaceSeparated=!1;de.prototype.commaSeparated=!1;de.prototype.defined=!1;de.prototype.mustUseProperty=!1;de.prototype.number=!1;de.prototype.overloadedBoolean=!1;de.prototype.property="";de.prototype.spaceSeparated=!1;de.prototype.space=void 0;let Li=0;const B=De(),ee=De(),Rt=De(),v=De(),W=De(),Fe=De(),fe=De();function De(){return 2**++Li}const Ft=Object.freeze(Object.defineProperty({__proto__:null,boolean:B,booleanish:ee,commaOrSpaceSeparated:fe,commaSeparated:Fe,number:v,overloadedBoolean:Rt,spaceSeparated:W},Symbol.toStringTag,{value:"Module"})),vt=Object.keys(Ft);class Kt extends de{constructor(t,n,r,i){let a=-1;if(super(t,n),bn(this,"space",i),typeof r=="number")for(;++a<vt.length;){const s=vt[a];bn(this,vt[a],(r&Ft[s])===Ft[s])}}}Kt.prototype.defined=!0;function bn(e,t,n){n&&(e[t]=n)}function He(e){const t={},n={};for(const[r,i]of Object.entries(e.properties)){const a=new Kt(r,e.transform(e.attributes||{},r),i,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(a.mustUseProperty=!0),t[r]=a,n[Ot(r)]=r,n[Ot(a.attribute)]=r}return new rt(t,n,e.space)}const or=He({properties:{ariaActiveDescendant:null,ariaAtomic:ee,ariaAutoComplete:null,ariaBusy:ee,ariaChecked:ee,ariaColCount:v,ariaColIndex:v,ariaColSpan:v,ariaControls:W,ariaCurrent:null,ariaDescribedBy:W,ariaDetails:null,ariaDisabled:ee,ariaDropEffect:W,ariaErrorMessage:null,ariaExpanded:ee,ariaFlowTo:W,ariaGrabbed:ee,ariaHasPopup:null,ariaHidden:ee,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:W,ariaLevel:v,ariaLive:null,ariaModal:ee,ariaMultiLine:ee,ariaMultiSelectable:ee,ariaOrientation:null,ariaOwns:W,ariaPlaceholder:null,ariaPosInSet:v,ariaPressed:ee,ariaReadOnly:ee,ariaRelevant:null,ariaRequired:ee,ariaRoleDescription:W,ariaRowCount:v,ariaRowIndex:v,ariaRowSpan:v,ariaSelected:ee,ariaSetSize:v,ariaSort:null,ariaValueMax:v,ariaValueMin:v,ariaValueNow:v,ariaValueText:null,role:null},transform(e,t){return t==="role"?t:"aria-"+t.slice(4).toLowerCase()}});function ur(e,t){return t in e?e[t]:t}function cr(e,t){return ur(e,t.toLowerCase())}const Oi=He({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:Fe,acceptCharset:W,accessKey:W,action:null,allow:null,allowFullScreen:B,allowPaymentRequest:B,allowUserMedia:B,alt:null,as:null,async:B,autoCapitalize:null,autoComplete:W,autoFocus:B,autoPlay:B,blocking:W,capture:null,charSet:null,checked:B,cite:null,className:W,cols:v,colSpan:null,content:null,contentEditable:ee,controls:B,controlsList:W,coords:v|Fe,crossOrigin:null,data:null,dateTime:null,decoding:null,default:B,defer:B,dir:null,dirName:null,disabled:B,download:Rt,draggable:ee,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:B,formTarget:null,headers:W,height:v,hidden:Rt,high:v,href:null,hrefLang:null,htmlFor:W,httpEquiv:W,id:null,imageSizes:null,imageSrcSet:null,inert:B,inputMode:null,integrity:null,is:null,isMap:B,itemId:null,itemProp:W,itemRef:W,itemScope:B,itemType:W,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:B,low:v,manifest:null,max:null,maxLength:v,media:null,method:null,min:null,minLength:v,multiple:B,muted:B,name:null,nonce:null,noModule:B,noValidate:B,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:B,optimum:v,pattern:null,ping:W,placeholder:null,playsInline:B,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:B,referrerPolicy:null,rel:W,required:B,reversed:B,rows:v,rowSpan:v,sandbox:W,scope:null,scoped:B,seamless:B,selected:B,shadowRootClonable:B,shadowRootDelegatesFocus:B,shadowRootMode:null,shape:null,size:v,sizes:null,slot:null,span:v,spellCheck:ee,src:null,srcDoc:null,srcLang:null,srcSet:null,start:v,step:null,style:null,tabIndex:v,target:null,title:null,translate:null,type:null,typeMustMatch:B,useMap:null,value:ee,width:v,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:W,axis:null,background:null,bgColor:null,border:v,borderColor:null,bottomMargin:v,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:B,declare:B,event:null,face:null,frame:null,frameBorder:null,hSpace:v,leftMargin:v,link:null,longDesc:null,lowSrc:null,marginHeight:v,marginWidth:v,noResize:B,noHref:B,noShade:B,noWrap:B,object:null,profile:null,prompt:null,rev:null,rightMargin:v,rules:null,scheme:null,scrolling:ee,standby:null,summary:null,text:null,topMargin:v,valueType:null,version:null,vAlign:null,vLink:null,vSpace:v,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:B,disableRemotePlayback:B,prefix:null,property:null,results:v,security:null,unselectable:null},space:"html",transform:cr}),Ri=He({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:fe,accentHeight:v,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:v,amplitude:v,arabicForm:null,ascent:v,attributeName:null,attributeType:null,azimuth:v,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:v,by:null,calcMode:null,capHeight:v,className:W,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:v,diffuseConstant:v,direction:null,display:null,dur:null,divisor:v,dominantBaseline:null,download:B,dx:null,dy:null,edgeMode:null,editable:null,elevation:v,enableBackground:null,end:null,event:null,exponent:v,externalResourcesRequired:null,fill:null,fillOpacity:v,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:Fe,g2:Fe,glyphName:Fe,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:v,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:v,horizOriginX:v,horizOriginY:v,id:null,ideographic:v,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:v,k:v,k1:v,k2:v,k3:v,k4:v,kernelMatrix:fe,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:v,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:v,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:v,overlineThickness:v,paintOrder:null,panose1:null,path:null,pathLength:v,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:W,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:v,pointsAtY:v,pointsAtZ:v,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:fe,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:fe,rev:fe,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:fe,requiredFeatures:fe,requiredFonts:fe,requiredFormats:fe,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:v,specularExponent:v,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:v,strikethroughThickness:v,string:null,stroke:null,strokeDashArray:fe,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:v,strokeOpacity:v,strokeWidth:null,style:null,surfaceScale:v,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:fe,tabIndex:v,tableValues:null,target:null,targetX:v,targetY:v,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:fe,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:v,underlineThickness:v,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:v,values:null,vAlphabetic:v,vMathematical:v,vectorEffect:null,vHanging:v,vIdeographic:v,version:null,vertAdvY:v,vertOriginX:v,vertOriginY:v,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:v,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:ur}),dr=He({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform(e,t){return"xlink:"+t.slice(5).toLowerCase()}}),pr=He({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:cr}),mr=He({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform(e,t){return"xml:"+t.slice(3).toLowerCase()}}),Fi={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"},Bi=/[A-Z]/g,wn=/-[a-z]/g,Hi=/^data[-\w.:]+$/i;function Vi(e,t){const n=Ot(t);let r=t,i=de;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&n.slice(0,4)==="data"&&Hi.test(t)){if(t.charAt(4)==="-"){const a=t.slice(5).replace(wn,Ui);r="data"+a.charAt(0).toUpperCase()+a.slice(1)}else{const a=t.slice(4);if(!wn.test(a)){let s=a.replace(Bi,$i);s.charAt(0)!=="-"&&(s="-"+s),t="data"+s}}i=Kt}return new i(r,t)}function $i(e){return"-"+e.toLowerCase()}function Ui(e){return e.charAt(1).toUpperCase()}const qi=sr([or,Oi,dr,pr,mr],"html"),Gt=sr([or,Ri,dr,pr,mr],"svg");function Yi(e){return e.join(" ").trim()}var Oe={},jt,vn;function Ki(){if(vn)return jt;vn=1;var e=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,t=/\n/g,n=/^\s*/,r=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,i=/^:\s*/,a=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,s=/^[;\s]*/,o=/^\s+|\s+$/g,d=`
`,u="/",c="*",m="",g="comment",p="declaration";jt=function(k,A){if(typeof k!="string")throw new TypeError("First argument must be a string");if(!k)return[];A=A||{};var b=1,_=1;function C(P){var I=P.match(t);I&&(b+=I.length);var E=P.lastIndexOf(d);_=~E?P.length-E:_+P.length}function H(){var P={line:b,column:_};return function(I){return I.position=new F(P),M(),I}}function F(P){this.start=P,this.end={line:b,column:_},this.source=A.source}F.prototype.content=k;function x(P){var I=new Error(A.source+":"+b+":"+_+": "+P);if(I.reason=P,I.filename=A.source,I.line=b,I.column=_,I.source=k,!A.silent)throw I}function j(P){var I=P.exec(k);if(I){var E=I[0];return C(E),k=k.slice(E.length),I}}function M(){j(n)}function L(P){var I;for(P=P||[];I=z();)I!==!1&&P.push(I);return P}function z(){var P=H();if(!(u!=k.charAt(0)||c!=k.charAt(1))){for(var I=2;m!=k.charAt(I)&&(c!=k.charAt(I)||u!=k.charAt(I+1));)++I;if(I+=2,m===k.charAt(I-1))return x("End of comment missing");var E=k.slice(2,I-2);return _+=2,C(E),k=k.slice(I),_+=2,P({type:g,comment:E})}}function D(){var P=H(),I=j(r);if(I){if(z(),!j(i))return x("property missing ':'");var E=j(a),G=P({type:p,property:w(I[0].replace(e,m)),value:E?w(E[0].replace(e,m)):m});return j(s),G}}function T(){var P=[];L(P);for(var I;I=D();)I!==!1&&(P.push(I),L(P));return P}return M(),T()};function w(k){return k?k.replace(o,m):m}return jt}var jn;function Gi(){if(jn)return Oe;jn=1;var e=Oe&&Oe.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Oe,"__esModule",{value:!0}),Oe.default=n;var t=e(Ki());function n(r,i){var a=null;if(!r||typeof r!="string")return a;var s=(0,t.default)(r),o=typeof i=="function";return s.forEach(function(d){if(d.type==="declaration"){var u=d.property,c=d.value;o?i(u,c,d):c&&(a=a||{},a[u]=c)}}),a}return Oe}var qe={},Nn;function Ji(){if(Nn)return qe;Nn=1,Object.defineProperty(qe,"__esModule",{value:!0}),qe.camelCase=void 0;var e=/^--[a-zA-Z0-9_-]+$/,t=/-([a-z])/g,n=/^[^-]+$/,r=/^-(webkit|moz|ms|o|khtml)-/,i=/^-(ms)-/,a=function(u){return!u||n.test(u)||e.test(u)},s=function(u,c){return c.toUpperCase()},o=function(u,c){return"".concat(c,"-")},d=function(u,c){return c===void 0&&(c={}),a(u)?u:(u=u.toLowerCase(),c.reactCompat?u=u.replace(i,o):u=u.replace(r,o),u.replace(t,s))};return qe.camelCase=d,qe}var Ye,Sn;function Wi(){if(Sn)return Ye;Sn=1;var e=Ye&&Ye.__importDefault||function(i){return i&&i.__esModule?i:{default:i}},t=e(Gi()),n=Ji();function r(i,a){var s={};return!i||typeof i!="string"||(0,t.default)(i,function(o,d){o&&d&&(s[(0,n.camelCase)(o,a)]=d)}),s}return r.default=r,Ye=r,Ye}var Xi=Wi();const Qi=nr(Xi),hr=fr("end"),Jt=fr("start");function fr(e){return t;function t(n){const r=n&&n.position&&n.position[e]||{};if(typeof r.line=="number"&&r.line>0&&typeof r.column=="number"&&r.column>0)return{line:r.line,column:r.column,offset:typeof r.offset=="number"&&r.offset>-1?r.offset:void 0}}}function Zi(e){const t=Jt(e),n=hr(e);if(t&&n)return{start:t,end:n}}function Qe(e){return!e||typeof e!="object"?"":"position"in e||"type"in e?Cn(e.position):"start"in e||"end"in e?Cn(e):"line"in e||"column"in e?Bt(e):""}function Bt(e){return En(e&&e.line)+":"+En(e&&e.column)}function Cn(e){return Bt(e&&e.start)+"-"+Bt(e&&e.end)}function En(e){return e&&typeof e=="number"?e:1}class le extends Error{constructor(t,n,r){super(),typeof n=="string"&&(r=n,n=void 0);let i="",a={},s=!1;if(n&&("line"in n&&"column"in n?a={place:n}:"start"in n&&"end"in n?a={place:n}:"type"in n?a={ancestors:[n],place:n.position}:a={...n}),typeof t=="string"?i=t:!a.cause&&t&&(s=!0,i=t.message,a.cause=t),!a.ruleId&&!a.source&&typeof r=="string"){const d=r.indexOf(":");d===-1?a.ruleId=r:(a.source=r.slice(0,d),a.ruleId=r.slice(d+1))}if(!a.place&&a.ancestors&&a.ancestors){const d=a.ancestors[a.ancestors.length-1];d&&(a.place=d.position)}const o=a.place&&"start"in a.place?a.place.start:a.place;this.ancestors=a.ancestors||void 0,this.cause=a.cause||void 0,this.column=o?o.column:void 0,this.fatal=void 0,this.file,this.message=i,this.line=o?o.line:void 0,this.name=Qe(a.place)||"1:1",this.place=a.place||void 0,this.reason=this.message,this.ruleId=a.ruleId||void 0,this.source=a.source||void 0,this.stack=s&&a.cause&&typeof a.cause.stack=="string"?a.cause.stack:"",this.actual,this.expected,this.note,this.url}}le.prototype.file="";le.prototype.name="";le.prototype.reason="";le.prototype.message="";le.prototype.stack="";le.prototype.column=void 0;le.prototype.line=void 0;le.prototype.ancestors=void 0;le.prototype.cause=void 0;le.prototype.fatal=void 0;le.prototype.place=void 0;le.prototype.ruleId=void 0;le.prototype.source=void 0;const Wt={}.hasOwnProperty,ea=new Map,ta=/[A-Z]/g,na=new Set(["table","tbody","thead","tfoot","tr"]),ra=new Set(["td","th"]),gr="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function ia(e,t){if(!t||t.Fragment===void 0)throw new TypeError("Expected `Fragment` in options");const n=t.filePath||void 0;let r;if(t.development){if(typeof t.jsxDEV!="function")throw new TypeError("Expected `jsxDEV` in options when `development: true`");r=pa(n,t.jsxDEV)}else{if(typeof t.jsx!="function")throw new TypeError("Expected `jsx` in production options");if(typeof t.jsxs!="function")throw new TypeError("Expected `jsxs` in production options");r=da(n,t.jsx,t.jsxs)}const i={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:r,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:n,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:t.passKeys!==!1,passNode:t.passNode||!1,schema:t.space==="svg"?Gt:qi,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:t.tableCellAlignToStyle!==!1},a=xr(i,e,void 0);return a&&typeof a!="string"?a:i.create(e,i.Fragment,{children:a||void 0},void 0)}function xr(e,t,n){if(t.type==="element")return aa(e,t,n);if(t.type==="mdxFlowExpression"||t.type==="mdxTextExpression")return la(e,t);if(t.type==="mdxJsxFlowElement"||t.type==="mdxJsxTextElement")return oa(e,t,n);if(t.type==="mdxjsEsm")return sa(e,t);if(t.type==="root")return ua(e,t,n);if(t.type==="text")return ca(e,t)}function aa(e,t,n){const r=e.schema;let i=r;t.tagName.toLowerCase()==="svg"&&r.space==="html"&&(i=Gt,e.schema=i),e.ancestors.push(t);const a=kr(e,t.tagName,!1),s=ma(e,t);let o=Qt(e,t);return na.has(t.tagName)&&(o=o.filter(function(d){return typeof d=="string"?!Mi(d):!0})),yr(e,s,a,t),Xt(s,o),e.ancestors.pop(),e.schema=r,e.create(t,a,s,n)}function la(e,t){if(t.data&&t.data.estree&&e.evaluater){const r=t.data.estree.body[0];return r.type,e.evaluater.evaluateExpression(r.expression)}nt(e,t.position)}function sa(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);nt(e,t.position)}function oa(e,t,n){const r=e.schema;let i=r;t.name==="svg"&&r.space==="html"&&(i=Gt,e.schema=i),e.ancestors.push(t);const a=t.name===null?e.Fragment:kr(e,t.name,!0),s=ha(e,t),o=Qt(e,t);return yr(e,s,a,t),Xt(s,o),e.ancestors.pop(),e.schema=r,e.create(t,a,s,n)}function ua(e,t,n){const r={};return Xt(r,Qt(e,t)),e.create(t,e.Fragment,r,n)}function ca(e,t){return t.value}function yr(e,t,n,r){typeof n!="string"&&n!==e.Fragment&&e.passNode&&(t.node=r)}function Xt(e,t){if(t.length>0){const n=t.length>1?t:t[0];n&&(e.children=n)}}function da(e,t,n){return r;function r(i,a,s,o){const u=Array.isArray(s.children)?n:t;return o?u(a,s,o):u(a,s)}}function pa(e,t){return n;function n(r,i,a,s){const o=Array.isArray(a.children),d=Jt(r);return t(i,a,s,o,{columnNumber:d?d.column-1:void 0,fileName:e,lineNumber:d?d.line:void 0},void 0)}}function ma(e,t){const n={};let r,i;for(i in t.properties)if(i!=="children"&&Wt.call(t.properties,i)){const a=fa(e,i,t.properties[i]);if(a){const[s,o]=a;e.tableCellAlignToStyle&&s==="align"&&typeof o=="string"&&ra.has(t.tagName)?r=o:n[s]=o}}if(r){const a=n.style||(n.style={});a[e.stylePropertyNameCase==="css"?"text-align":"textAlign"]=r}return n}function ha(e,t){const n={};for(const r of t.attributes)if(r.type==="mdxJsxExpressionAttribute")if(r.data&&r.data.estree&&e.evaluater){const a=r.data.estree.body[0];a.type;const s=a.expression;s.type;const o=s.properties[0];o.type,Object.assign(n,e.evaluater.evaluateExpression(o.argument))}else nt(e,t.position);else{const i=r.name;let a;if(r.value&&typeof r.value=="object")if(r.value.data&&r.value.data.estree&&e.evaluater){const o=r.value.data.estree.body[0];o.type,a=e.evaluater.evaluateExpression(o.expression)}else nt(e,t.position);else a=r.value===null?!0:r.value;n[i]=a}return n}function Qt(e,t){const n=[];let r=-1;const i=e.passKeys?new Map:ea;for(;++r<t.children.length;){const a=t.children[r];let s;if(e.passKeys){const d=a.type==="element"?a.tagName:a.type==="mdxJsxFlowElement"||a.type==="mdxJsxTextElement"?a.name:void 0;if(d){const u=i.get(d)||0;s=d+"-"+u,i.set(d,u+1)}}const o=xr(e,a,s);o!==void 0&&n.push(o)}return n}function fa(e,t,n){const r=Vi(e.schema,t);if(!(n==null||typeof n=="number"&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?zi(n):Yi(n)),r.property==="style"){let i=typeof n=="object"?n:ga(e,String(n));return e.stylePropertyNameCase==="css"&&(i=xa(i)),["style",i]}return[e.elementAttributeNameCase==="react"&&r.space?Fi[r.property]||r.property:r.attribute,n]}}function ga(e,t){try{return Qi(t,{reactCompat:!0})}catch(n){if(e.ignoreInvalidStyle)return{};const r=n,i=new le("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:r,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw i.file=e.filePath||void 0,i.url=gr+"#cannot-parse-style-attribute",i}}function kr(e,t,n){let r;if(!n)r={type:"Literal",value:t};else if(t.includes(".")){const i=t.split(".");let a=-1,s;for(;++a<i.length;){const o=yn(i[a])?{type:"Identifier",name:i[a]}:{type:"Literal",value:i[a]};s=s?{type:"MemberExpression",object:s,property:o,computed:!!(a&&o.type==="Literal"),optional:!1}:o}r=s}else r=yn(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t};if(r.type==="Literal"){const i=r.value;return Wt.call(e.components,i)?e.components[i]:i}if(e.evaluater)return e.evaluater.evaluateExpression(r);nt(e)}function nt(e,t){const n=new le("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=gr+"#cannot-handle-mdx-estrees-without-createevaluater",n}function xa(e){const t={};let n;for(n in e)Wt.call(e,n)&&(t[ya(n)]=e[n]);return t}function ya(e){let t=e.replace(ta,ka);return t.slice(0,3)==="ms-"&&(t="-"+t),t}function ka(e){return"-"+e.toLowerCase()}const Nt={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]},ba={};function wa(e,t){const n=ba,r=typeof n.includeImageAlt=="boolean"?n.includeImageAlt:!0,i=typeof n.includeHtml=="boolean"?n.includeHtml:!0;return br(e,r,i)}function br(e,t,n){if(va(e)){if("value"in e)return e.type==="html"&&!n?"":e.value;if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return An(e.children,t,n)}return Array.isArray(e)?An(e,t,n):""}function An(e,t,n){const r=[];let i=-1;for(;++i<e.length;)r[i]=br(e[i],t,n);return r.join("")}function va(e){return!!(e&&typeof e=="object")}const In=document.createElement("i");function Zt(e){const t="&"+e+";";In.innerHTML=t;const n=In.textContent;return n.charCodeAt(n.length-1)===59&&e!=="semi"||n===t?!1:n}function Se(e,t,n,r){const i=e.length;let a=0,s;if(t<0?t=-t>i?0:i+t:t=t>i?i:t,n=n>0?n:0,r.length<1e4)s=Array.from(r),s.unshift(t,n),e.splice(...s);else for(n&&e.splice(t,n);a<r.length;)s=r.slice(a,a+1e4),s.unshift(t,0),e.splice(...s),a+=1e4,t+=1e4}function xe(e,t){return e.length>0?(Se(e,e.length,0,t),e):t}const zn={}.hasOwnProperty;function ja(e){const t={};let n=-1;for(;++n<e.length;)Na(t,e[n]);return t}function Na(e,t){let n;for(n in t){const i=(zn.call(e,n)?e[n]:void 0)||(e[n]={}),a=t[n];let s;if(a)for(s in a){zn.call(i,s)||(i[s]=[]);const o=a[s];Sa(i[s],Array.isArray(o)?o:o?[o]:[])}}}function Sa(e,t){let n=-1;const r=[];for(;++n<t.length;)(t[n].add==="after"?e:r).push(t[n]);Se(e,0,0,r)}function wr(e,t){const n=Number.parseInt(e,t);return n<9||n===11||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(n&65535)===65535||(n&65535)===65534||n>1114111?"�":String.fromCodePoint(n)}function Be(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}const Ne=_e(/[A-Za-z]/),ge=_e(/[\dA-Za-z]/),Ca=_e(/[#-'*+\--9=?A-Z^-~]/);function Ht(e){return e!==null&&(e<32||e===127)}const Vt=_e(/\d/),Ea=_e(/[\dA-Fa-f]/),Aa=_e(/[!-/:-@[-`{-~]/);function R(e){return e!==null&&e<-2}function ce(e){return e!==null&&(e<0||e===32)}function Y(e){return e===-2||e===-1||e===32}const Ia=_e(/\p{P}|\p{S}/u),za=_e(/\s/);function _e(e){return t;function t(n){return n!==null&&n>-1&&e.test(String.fromCharCode(n))}}function Ve(e){const t=[];let n=-1,r=0,i=0;for(;++n<e.length;){const a=e.charCodeAt(n);let s="";if(a===37&&ge(e.charCodeAt(n+1))&&ge(e.charCodeAt(n+2)))i=2;else if(a<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(a))||(s=String.fromCharCode(a));else if(a>55295&&a<57344){const o=e.charCodeAt(n+1);a<56320&&o>56319&&o<57344?(s=String.fromCharCode(a,o),i=1):s="�"}else s=String.fromCharCode(a);s&&(t.push(e.slice(r,n),encodeURIComponent(s)),r=n+i+1,s=""),i&&(n+=i,i=0)}return t.join("")+e.slice(r)}function X(e,t,n,r){const i=r?r-1:Number.POSITIVE_INFINITY;let a=0;return s;function s(d){return Y(d)?(e.enter(n),o(d)):t(d)}function o(d){return Y(d)&&a++<i?(e.consume(d),o):(e.exit(n),t(d))}}const Ta={tokenize:_a};function _a(e){const t=e.attempt(this.parser.constructs.contentInitial,r,i);let n;return t;function r(o){if(o===null){e.consume(o);return}return e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),X(e,t,"linePrefix")}function i(o){return e.enter("paragraph"),a(o)}function a(o){const d=e.enter("chunkText",{contentType:"text",previous:n});return n&&(n.next=d),n=d,s(o)}function s(o){if(o===null){e.exit("chunkText"),e.exit("paragraph"),e.consume(o);return}return R(o)?(e.consume(o),e.exit("chunkText"),a):(e.consume(o),s)}}const Pa={tokenize:Da},Tn={tokenize:Ma};function Da(e){const t=this,n=[];let r=0,i,a,s;return o;function o(C){if(r<n.length){const H=n[r];return t.containerState=H[1],e.attempt(H[0].continuation,d,u)(C)}return u(C)}function d(C){if(r++,t.containerState._closeFlow){t.containerState._closeFlow=void 0,i&&_();const H=t.events.length;let F=H,x;for(;F--;)if(t.events[F][0]==="exit"&&t.events[F][1].type==="chunkFlow"){x=t.events[F][1].end;break}b(r);let j=H;for(;j<t.events.length;)t.events[j][1].end={...x},j++;return Se(t.events,F+1,0,t.events.slice(H)),t.events.length=j,u(C)}return o(C)}function u(C){if(r===n.length){if(!i)return g(C);if(i.currentConstruct&&i.currentConstruct.concrete)return w(C);t.interrupt=!!(i.currentConstruct&&!i._gfmTableDynamicInterruptHack)}return t.containerState={},e.check(Tn,c,m)(C)}function c(C){return i&&_(),b(r),g(C)}function m(C){return t.parser.lazy[t.now().line]=r!==n.length,s=t.now().offset,w(C)}function g(C){return t.containerState={},e.attempt(Tn,p,w)(C)}function p(C){return r++,n.push([t.currentConstruct,t.containerState]),g(C)}function w(C){if(C===null){i&&_(),b(0),e.consume(C);return}return i=i||t.parser.flow(t.now()),e.enter("chunkFlow",{_tokenizer:i,contentType:"flow",previous:a}),k(C)}function k(C){if(C===null){A(e.exit("chunkFlow"),!0),b(0),e.consume(C);return}return R(C)?(e.consume(C),A(e.exit("chunkFlow")),r=0,t.interrupt=void 0,o):(e.consume(C),k)}function A(C,H){const F=t.sliceStream(C);if(H&&F.push(null),C.previous=a,a&&(a.next=C),a=C,i.defineSkip(C.start),i.write(F),t.parser.lazy[C.start.line]){let x=i.events.length;for(;x--;)if(i.events[x][1].start.offset<s&&(!i.events[x][1].end||i.events[x][1].end.offset>s))return;const j=t.events.length;let M=j,L,z;for(;M--;)if(t.events[M][0]==="exit"&&t.events[M][1].type==="chunkFlow"){if(L){z=t.events[M][1].end;break}L=!0}for(b(r),x=j;x<t.events.length;)t.events[x][1].end={...z},x++;Se(t.events,M+1,0,t.events.slice(j)),t.events.length=x}}function b(C){let H=n.length;for(;H-- >C;){const F=n[H];t.containerState=F[1],F[0].exit.call(t,e)}n.length=C}function _(){i.write([null]),a=void 0,i=void 0,t.containerState._closeFlow=void 0}}function Ma(e,t,n){return X(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}function _n(e){if(e===null||ce(e)||za(e))return 1;if(Ia(e))return 2}function en(e,t,n){const r=[];let i=-1;for(;++i<e.length;){const a=e[i].resolveAll;a&&!r.includes(a)&&(t=a(t,n),r.push(a))}return t}const $t={name:"attention",resolveAll:La,tokenize:Oa};function La(e,t){let n=-1,r,i,a,s,o,d,u,c;for(;++n<e.length;)if(e[n][0]==="enter"&&e[n][1].type==="attentionSequence"&&e[n][1]._close){for(r=n;r--;)if(e[r][0]==="exit"&&e[r][1].type==="attentionSequence"&&e[r][1]._open&&t.sliceSerialize(e[r][1]).charCodeAt(0)===t.sliceSerialize(e[n][1]).charCodeAt(0)){if((e[r][1]._close||e[n][1]._open)&&(e[n][1].end.offset-e[n][1].start.offset)%3&&!((e[r][1].end.offset-e[r][1].start.offset+e[n][1].end.offset-e[n][1].start.offset)%3))continue;d=e[r][1].end.offset-e[r][1].start.offset>1&&e[n][1].end.offset-e[n][1].start.offset>1?2:1;const m={...e[r][1].end},g={...e[n][1].start};Pn(m,-d),Pn(g,d),s={type:d>1?"strongSequence":"emphasisSequence",start:m,end:{...e[r][1].end}},o={type:d>1?"strongSequence":"emphasisSequence",start:{...e[n][1].start},end:g},a={type:d>1?"strongText":"emphasisText",start:{...e[r][1].end},end:{...e[n][1].start}},i={type:d>1?"strong":"emphasis",start:{...s.start},end:{...o.end}},e[r][1].end={...s.start},e[n][1].start={...o.end},u=[],e[r][1].end.offset-e[r][1].start.offset&&(u=xe(u,[["enter",e[r][1],t],["exit",e[r][1],t]])),u=xe(u,[["enter",i,t],["enter",s,t],["exit",s,t],["enter",a,t]]),u=xe(u,en(t.parser.constructs.insideSpan.null,e.slice(r+1,n),t)),u=xe(u,[["exit",a,t],["enter",o,t],["exit",o,t],["exit",i,t]]),e[n][1].end.offset-e[n][1].start.offset?(c=2,u=xe(u,[["enter",e[n][1],t],["exit",e[n][1],t]])):c=0,Se(e,r-1,n-r+3,u),n=r+u.length-c-2;break}}for(n=-1;++n<e.length;)e[n][1].type==="attentionSequence"&&(e[n][1].type="data");return e}function Oa(e,t){const n=this.parser.constructs.attentionMarkers.null,r=this.previous,i=_n(r);let a;return s;function s(d){return a=d,e.enter("attentionSequence"),o(d)}function o(d){if(d===a)return e.consume(d),o;const u=e.exit("attentionSequence"),c=_n(d),m=!c||c===2&&i||n.includes(d),g=!i||i===2&&c||n.includes(r);return u._open=!!(a===42?m:m&&(i||!g)),u._close=!!(a===42?g:g&&(c||!m)),t(d)}}function Pn(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}const Ra={name:"autolink",tokenize:Fa};function Fa(e,t,n){let r=0;return i;function i(p){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(p),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),a}function a(p){return Ne(p)?(e.consume(p),s):p===64?n(p):u(p)}function s(p){return p===43||p===45||p===46||ge(p)?(r=1,o(p)):u(p)}function o(p){return p===58?(e.consume(p),r=0,d):(p===43||p===45||p===46||ge(p))&&r++<32?(e.consume(p),o):(r=0,u(p))}function d(p){return p===62?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(p),e.exit("autolinkMarker"),e.exit("autolink"),t):p===null||p===32||p===60||Ht(p)?n(p):(e.consume(p),d)}function u(p){return p===64?(e.consume(p),c):Ca(p)?(e.consume(p),u):n(p)}function c(p){return ge(p)?m(p):n(p)}function m(p){return p===46?(e.consume(p),r=0,c):p===62?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(p),e.exit("autolinkMarker"),e.exit("autolink"),t):g(p)}function g(p){if((p===45||ge(p))&&r++<63){const w=p===45?g:m;return e.consume(p),w}return n(p)}}const gt={partial:!0,tokenize:Ba};function Ba(e,t,n){return r;function r(a){return Y(a)?X(e,i,"linePrefix")(a):i(a)}function i(a){return a===null||R(a)?t(a):n(a)}}const vr={continuation:{tokenize:Va},exit:$a,name:"blockQuote",tokenize:Ha};function Ha(e,t,n){const r=this;return i;function i(s){if(s===62){const o=r.containerState;return o.open||(e.enter("blockQuote",{_container:!0}),o.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(s),e.exit("blockQuoteMarker"),a}return n(s)}function a(s){return Y(s)?(e.enter("blockQuotePrefixWhitespace"),e.consume(s),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(s))}}function Va(e,t,n){const r=this;return i;function i(s){return Y(s)?X(e,a,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(s):a(s)}function a(s){return e.attempt(vr,t,n)(s)}}function $a(e){e.exit("blockQuote")}const jr={name:"characterEscape",tokenize:Ua};function Ua(e,t,n){return r;function r(a){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(a),e.exit("escapeMarker"),i}function i(a){return Aa(a)?(e.enter("characterEscapeValue"),e.consume(a),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(a)}}const Nr={name:"characterReference",tokenize:qa};function qa(e,t,n){const r=this;let i=0,a,s;return o;function o(m){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(m),e.exit("characterReferenceMarker"),d}function d(m){return m===35?(e.enter("characterReferenceMarkerNumeric"),e.consume(m),e.exit("characterReferenceMarkerNumeric"),u):(e.enter("characterReferenceValue"),a=31,s=ge,c(m))}function u(m){return m===88||m===120?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(m),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),a=6,s=Ea,c):(e.enter("characterReferenceValue"),a=7,s=Vt,c(m))}function c(m){if(m===59&&i){const g=e.exit("characterReferenceValue");return s===ge&&!Zt(r.sliceSerialize(g))?n(m):(e.enter("characterReferenceMarker"),e.consume(m),e.exit("characterReferenceMarker"),e.exit("characterReference"),t)}return s(m)&&i++<a?(e.consume(m),c):n(m)}}const Dn={partial:!0,tokenize:Ka},Mn={concrete:!0,name:"codeFenced",tokenize:Ya};function Ya(e,t,n){const r=this,i={partial:!0,tokenize:F};let a=0,s=0,o;return d;function d(x){return u(x)}function u(x){const j=r.events[r.events.length-1];return a=j&&j[1].type==="linePrefix"?j[2].sliceSerialize(j[1],!0).length:0,o=x,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),c(x)}function c(x){return x===o?(s++,e.consume(x),c):s<3?n(x):(e.exit("codeFencedFenceSequence"),Y(x)?X(e,m,"whitespace")(x):m(x))}function m(x){return x===null||R(x)?(e.exit("codeFencedFence"),r.interrupt?t(x):e.check(Dn,k,H)(x)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),g(x))}function g(x){return x===null||R(x)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),m(x)):Y(x)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),X(e,p,"whitespace")(x)):x===96&&x===o?n(x):(e.consume(x),g)}function p(x){return x===null||R(x)?m(x):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),w(x))}function w(x){return x===null||R(x)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),m(x)):x===96&&x===o?n(x):(e.consume(x),w)}function k(x){return e.attempt(i,H,A)(x)}function A(x){return e.enter("lineEnding"),e.consume(x),e.exit("lineEnding"),b}function b(x){return a>0&&Y(x)?X(e,_,"linePrefix",a+1)(x):_(x)}function _(x){return x===null||R(x)?e.check(Dn,k,H)(x):(e.enter("codeFlowValue"),C(x))}function C(x){return x===null||R(x)?(e.exit("codeFlowValue"),_(x)):(e.consume(x),C)}function H(x){return e.exit("codeFenced"),t(x)}function F(x,j,M){let L=0;return z;function z(E){return x.enter("lineEnding"),x.consume(E),x.exit("lineEnding"),D}function D(E){return x.enter("codeFencedFence"),Y(E)?X(x,T,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(E):T(E)}function T(E){return E===o?(x.enter("codeFencedFenceSequence"),P(E)):M(E)}function P(E){return E===o?(L++,x.consume(E),P):L>=s?(x.exit("codeFencedFenceSequence"),Y(E)?X(x,I,"whitespace")(E):I(E)):M(E)}function I(E){return E===null||R(E)?(x.exit("codeFencedFence"),j(E)):M(E)}}}function Ka(e,t,n){const r=this;return i;function i(s){return s===null?n(s):(e.enter("lineEnding"),e.consume(s),e.exit("lineEnding"),a)}function a(s){return r.parser.lazy[r.now().line]?n(s):t(s)}}const St={name:"codeIndented",tokenize:Ja},Ga={partial:!0,tokenize:Wa};function Ja(e,t,n){const r=this;return i;function i(u){return e.enter("codeIndented"),X(e,a,"linePrefix",5)(u)}function a(u){const c=r.events[r.events.length-1];return c&&c[1].type==="linePrefix"&&c[2].sliceSerialize(c[1],!0).length>=4?s(u):n(u)}function s(u){return u===null?d(u):R(u)?e.attempt(Ga,s,d)(u):(e.enter("codeFlowValue"),o(u))}function o(u){return u===null||R(u)?(e.exit("codeFlowValue"),s(u)):(e.consume(u),o)}function d(u){return e.exit("codeIndented"),t(u)}}function Wa(e,t,n){const r=this;return i;function i(s){return r.parser.lazy[r.now().line]?n(s):R(s)?(e.enter("lineEnding"),e.consume(s),e.exit("lineEnding"),i):X(e,a,"linePrefix",5)(s)}function a(s){const o=r.events[r.events.length-1];return o&&o[1].type==="linePrefix"&&o[2].sliceSerialize(o[1],!0).length>=4?t(s):R(s)?i(s):n(s)}}const Xa={name:"codeText",previous:Za,resolve:Qa,tokenize:el};function Qa(e){let t=e.length-4,n=3,r,i;if((e[n][1].type==="lineEnding"||e[n][1].type==="space")&&(e[t][1].type==="lineEnding"||e[t][1].type==="space")){for(r=n;++r<t;)if(e[r][1].type==="codeTextData"){e[n][1].type="codeTextPadding",e[t][1].type="codeTextPadding",n+=2,t-=2;break}}for(r=n-1,t++;++r<=t;)i===void 0?r!==t&&e[r][1].type!=="lineEnding"&&(i=r):(r===t||e[r][1].type==="lineEnding")&&(e[i][1].type="codeTextData",r!==i+2&&(e[i][1].end=e[r-1][1].end,e.splice(i+2,r-i-2),t-=r-i-2,r=i+2),i=void 0);return e}function Za(e){return e!==96||this.events[this.events.length-1][1].type==="characterEscape"}function el(e,t,n){let r=0,i,a;return s;function s(m){return e.enter("codeText"),e.enter("codeTextSequence"),o(m)}function o(m){return m===96?(e.consume(m),r++,o):(e.exit("codeTextSequence"),d(m))}function d(m){return m===null?n(m):m===32?(e.enter("space"),e.consume(m),e.exit("space"),d):m===96?(a=e.enter("codeTextSequence"),i=0,c(m)):R(m)?(e.enter("lineEnding"),e.consume(m),e.exit("lineEnding"),d):(e.enter("codeTextData"),u(m))}function u(m){return m===null||m===32||m===96||R(m)?(e.exit("codeTextData"),d(m)):(e.consume(m),u)}function c(m){return m===96?(e.consume(m),i++,c):i===r?(e.exit("codeTextSequence"),e.exit("codeText"),t(m)):(a.type="codeTextData",u(m))}}class tl{constructor(t){this.left=t?[...t]:[],this.right=[]}get(t){if(t<0||t>=this.left.length+this.right.length)throw new RangeError("Cannot access index `"+t+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return t<this.left.length?this.left[t]:this.right[this.right.length-t+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(t,n){const r=n==null?Number.POSITIVE_INFINITY:n;return r<this.left.length?this.left.slice(t,r):t>this.left.length?this.right.slice(this.right.length-r+this.left.length,this.right.length-t+this.left.length).reverse():this.left.slice(t).concat(this.right.slice(this.right.length-r+this.left.length).reverse())}splice(t,n,r){const i=n||0;this.setCursor(Math.trunc(t));const a=this.right.splice(this.right.length-i,Number.POSITIVE_INFINITY);return r&&Ke(this.left,r),a.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(t){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(t)}pushMany(t){this.setCursor(Number.POSITIVE_INFINITY),Ke(this.left,t)}unshift(t){this.setCursor(0),this.right.push(t)}unshiftMany(t){this.setCursor(0),Ke(this.right,t.reverse())}setCursor(t){if(!(t===this.left.length||t>this.left.length&&this.right.length===0||t<0&&this.left.length===0))if(t<this.left.length){const n=this.left.splice(t,Number.POSITIVE_INFINITY);Ke(this.right,n.reverse())}else{const n=this.right.splice(this.left.length+this.right.length-t,Number.POSITIVE_INFINITY);Ke(this.left,n.reverse())}}}function Ke(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function Sr(e){const t={};let n=-1,r,i,a,s,o,d,u;const c=new tl(e);for(;++n<c.length;){for(;n in t;)n=t[n];if(r=c.get(n),n&&r[1].type==="chunkFlow"&&c.get(n-1)[1].type==="listItemPrefix"&&(d=r[1]._tokenizer.events,a=0,a<d.length&&d[a][1].type==="lineEndingBlank"&&(a+=2),a<d.length&&d[a][1].type==="content"))for(;++a<d.length&&d[a][1].type!=="content";)d[a][1].type==="chunkText"&&(d[a][1]._isInFirstContentOfListItem=!0,a++);if(r[0]==="enter")r[1].contentType&&(Object.assign(t,nl(c,n)),n=t[n],u=!0);else if(r[1]._container){for(a=n,i=void 0;a--;)if(s=c.get(a),s[1].type==="lineEnding"||s[1].type==="lineEndingBlank")s[0]==="enter"&&(i&&(c.get(i)[1].type="lineEndingBlank"),s[1].type="lineEnding",i=a);else if(!(s[1].type==="linePrefix"||s[1].type==="listItemIndent"))break;i&&(r[1].end={...c.get(i)[1].start},o=c.slice(i,n),o.unshift(r),c.splice(i,n-i+1,o))}}return Se(e,0,Number.POSITIVE_INFINITY,c.slice(0)),!u}function nl(e,t){const n=e.get(t)[1],r=e.get(t)[2];let i=t-1;const a=[];let s=n._tokenizer;s||(s=r.parser[n.contentType](n.start),n._contentTypeTextTrailing&&(s._contentTypeTextTrailing=!0));const o=s.events,d=[],u={};let c,m,g=-1,p=n,w=0,k=0;const A=[k];for(;p;){for(;e.get(++i)[1]!==p;);a.push(i),p._tokenizer||(c=r.sliceStream(p),p.next||c.push(null),m&&s.defineSkip(p.start),p._isInFirstContentOfListItem&&(s._gfmTasklistFirstContentOfListItem=!0),s.write(c),p._isInFirstContentOfListItem&&(s._gfmTasklistFirstContentOfListItem=void 0)),m=p,p=p.next}for(p=n;++g<o.length;)o[g][0]==="exit"&&o[g-1][0]==="enter"&&o[g][1].type===o[g-1][1].type&&o[g][1].start.line!==o[g][1].end.line&&(k=g+1,A.push(k),p._tokenizer=void 0,p.previous=void 0,p=p.next);for(s.events=[],p?(p._tokenizer=void 0,p.previous=void 0):A.pop(),g=A.length;g--;){const b=o.slice(A[g],A[g+1]),_=a.pop();d.push([_,_+b.length-1]),e.splice(_,2,b)}for(d.reverse(),g=-1;++g<d.length;)u[w+d[g][0]]=w+d[g][1],w+=d[g][1]-d[g][0]-1;return u}const rl={resolve:al,tokenize:ll},il={partial:!0,tokenize:sl};function al(e){return Sr(e),e}function ll(e,t){let n;return r;function r(o){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),i(o)}function i(o){return o===null?a(o):R(o)?e.check(il,s,a)(o):(e.consume(o),i)}function a(o){return e.exit("chunkContent"),e.exit("content"),t(o)}function s(o){return e.consume(o),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,i}}function sl(e,t,n){const r=this;return i;function i(s){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(s),e.exit("lineEnding"),X(e,a,"linePrefix")}function a(s){if(s===null||R(s))return n(s);const o=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&o&&o[1].type==="linePrefix"&&o[2].sliceSerialize(o[1],!0).length>=4?t(s):e.interrupt(r.parser.constructs.flow,n,t)(s)}}function Cr(e,t,n,r,i,a,s,o,d){const u=d||Number.POSITIVE_INFINITY;let c=0;return m;function m(b){return b===60?(e.enter(r),e.enter(i),e.enter(a),e.consume(b),e.exit(a),g):b===null||b===32||b===41||Ht(b)?n(b):(e.enter(r),e.enter(s),e.enter(o),e.enter("chunkString",{contentType:"string"}),k(b))}function g(b){return b===62?(e.enter(a),e.consume(b),e.exit(a),e.exit(i),e.exit(r),t):(e.enter(o),e.enter("chunkString",{contentType:"string"}),p(b))}function p(b){return b===62?(e.exit("chunkString"),e.exit(o),g(b)):b===null||b===60||R(b)?n(b):(e.consume(b),b===92?w:p)}function w(b){return b===60||b===62||b===92?(e.consume(b),p):p(b)}function k(b){return!c&&(b===null||b===41||ce(b))?(e.exit("chunkString"),e.exit(o),e.exit(s),e.exit(r),t(b)):c<u&&b===40?(e.consume(b),c++,k):b===41?(e.consume(b),c--,k):b===null||b===32||b===40||Ht(b)?n(b):(e.consume(b),b===92?A:k)}function A(b){return b===40||b===41||b===92?(e.consume(b),k):k(b)}}function Er(e,t,n,r,i,a){const s=this;let o=0,d;return u;function u(p){return e.enter(r),e.enter(i),e.consume(p),e.exit(i),e.enter(a),c}function c(p){return o>999||p===null||p===91||p===93&&!d||p===94&&!o&&"_hiddenFootnoteSupport"in s.parser.constructs?n(p):p===93?(e.exit(a),e.enter(i),e.consume(p),e.exit(i),e.exit(r),t):R(p)?(e.enter("lineEnding"),e.consume(p),e.exit("lineEnding"),c):(e.enter("chunkString",{contentType:"string"}),m(p))}function m(p){return p===null||p===91||p===93||R(p)||o++>999?(e.exit("chunkString"),c(p)):(e.consume(p),d||(d=!Y(p)),p===92?g:m)}function g(p){return p===91||p===92||p===93?(e.consume(p),o++,m):m(p)}}function Ar(e,t,n,r,i,a){let s;return o;function o(g){return g===34||g===39||g===40?(e.enter(r),e.enter(i),e.consume(g),e.exit(i),s=g===40?41:g,d):n(g)}function d(g){return g===s?(e.enter(i),e.consume(g),e.exit(i),e.exit(r),t):(e.enter(a),u(g))}function u(g){return g===s?(e.exit(a),d(s)):g===null?n(g):R(g)?(e.enter("lineEnding"),e.consume(g),e.exit("lineEnding"),X(e,u,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),c(g))}function c(g){return g===s||g===null||R(g)?(e.exit("chunkString"),u(g)):(e.consume(g),g===92?m:c)}function m(g){return g===s||g===92?(e.consume(g),c):c(g)}}function Ze(e,t){let n;return r;function r(i){return R(i)?(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),n=!0,r):Y(i)?X(e,r,n?"linePrefix":"lineSuffix")(i):t(i)}}const ol={name:"definition",tokenize:cl},ul={partial:!0,tokenize:dl};function cl(e,t,n){const r=this;let i;return a;function a(p){return e.enter("definition"),s(p)}function s(p){return Er.call(r,e,o,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(p)}function o(p){return i=Be(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)),p===58?(e.enter("definitionMarker"),e.consume(p),e.exit("definitionMarker"),d):n(p)}function d(p){return ce(p)?Ze(e,u)(p):u(p)}function u(p){return Cr(e,c,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(p)}function c(p){return e.attempt(ul,m,m)(p)}function m(p){return Y(p)?X(e,g,"whitespace")(p):g(p)}function g(p){return p===null||R(p)?(e.exit("definition"),r.parser.defined.push(i),t(p)):n(p)}}function dl(e,t,n){return r;function r(o){return ce(o)?Ze(e,i)(o):n(o)}function i(o){return Ar(e,a,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(o)}function a(o){return Y(o)?X(e,s,"whitespace")(o):s(o)}function s(o){return o===null||R(o)?t(o):n(o)}}const pl={name:"hardBreakEscape",tokenize:ml};function ml(e,t,n){return r;function r(a){return e.enter("hardBreakEscape"),e.consume(a),i}function i(a){return R(a)?(e.exit("hardBreakEscape"),t(a)):n(a)}}const hl={name:"headingAtx",resolve:fl,tokenize:gl};function fl(e,t){let n=e.length-2,r=3,i,a;return e[r][1].type==="whitespace"&&(r+=2),n-2>r&&e[n][1].type==="whitespace"&&(n-=2),e[n][1].type==="atxHeadingSequence"&&(r===n-1||n-4>r&&e[n-2][1].type==="whitespace")&&(n-=r+1===n?2:4),n>r&&(i={type:"atxHeadingText",start:e[r][1].start,end:e[n][1].end},a={type:"chunkText",start:e[r][1].start,end:e[n][1].end,contentType:"text"},Se(e,r,n-r+1,[["enter",i,t],["enter",a,t],["exit",a,t],["exit",i,t]])),e}function gl(e,t,n){let r=0;return i;function i(c){return e.enter("atxHeading"),a(c)}function a(c){return e.enter("atxHeadingSequence"),s(c)}function s(c){return c===35&&r++<6?(e.consume(c),s):c===null||ce(c)?(e.exit("atxHeadingSequence"),o(c)):n(c)}function o(c){return c===35?(e.enter("atxHeadingSequence"),d(c)):c===null||R(c)?(e.exit("atxHeading"),t(c)):Y(c)?X(e,o,"whitespace")(c):(e.enter("atxHeadingText"),u(c))}function d(c){return c===35?(e.consume(c),d):(e.exit("atxHeadingSequence"),o(c))}function u(c){return c===null||c===35||ce(c)?(e.exit("atxHeadingText"),o(c)):(e.consume(c),u)}}const xl=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],Ln=["pre","script","style","textarea"],yl={concrete:!0,name:"htmlFlow",resolveTo:wl,tokenize:vl},kl={partial:!0,tokenize:Nl},bl={partial:!0,tokenize:jl};function wl(e){let t=e.length;for(;t--&&!(e[t][0]==="enter"&&e[t][1].type==="htmlFlow"););return t>1&&e[t-2][1].type==="linePrefix"&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e}function vl(e,t,n){const r=this;let i,a,s,o,d;return u;function u(f){return c(f)}function c(f){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(f),m}function m(f){return f===33?(e.consume(f),g):f===47?(e.consume(f),a=!0,k):f===63?(e.consume(f),i=3,r.interrupt?t:h):Ne(f)?(e.consume(f),s=String.fromCharCode(f),A):n(f)}function g(f){return f===45?(e.consume(f),i=2,p):f===91?(e.consume(f),i=5,o=0,w):Ne(f)?(e.consume(f),i=4,r.interrupt?t:h):n(f)}function p(f){return f===45?(e.consume(f),r.interrupt?t:h):n(f)}function w(f){const se="CDATA[";return f===se.charCodeAt(o++)?(e.consume(f),o===se.length?r.interrupt?t:T:w):n(f)}function k(f){return Ne(f)?(e.consume(f),s=String.fromCharCode(f),A):n(f)}function A(f){if(f===null||f===47||f===62||ce(f)){const se=f===47,ye=s.toLowerCase();return!se&&!a&&Ln.includes(ye)?(i=1,r.interrupt?t(f):T(f)):xl.includes(s.toLowerCase())?(i=6,se?(e.consume(f),b):r.interrupt?t(f):T(f)):(i=7,r.interrupt&&!r.parser.lazy[r.now().line]?n(f):a?_(f):C(f))}return f===45||ge(f)?(e.consume(f),s+=String.fromCharCode(f),A):n(f)}function b(f){return f===62?(e.consume(f),r.interrupt?t:T):n(f)}function _(f){return Y(f)?(e.consume(f),_):z(f)}function C(f){return f===47?(e.consume(f),z):f===58||f===95||Ne(f)?(e.consume(f),H):Y(f)?(e.consume(f),C):z(f)}function H(f){return f===45||f===46||f===58||f===95||ge(f)?(e.consume(f),H):F(f)}function F(f){return f===61?(e.consume(f),x):Y(f)?(e.consume(f),F):C(f)}function x(f){return f===null||f===60||f===61||f===62||f===96?n(f):f===34||f===39?(e.consume(f),d=f,j):Y(f)?(e.consume(f),x):M(f)}function j(f){return f===d?(e.consume(f),d=null,L):f===null||R(f)?n(f):(e.consume(f),j)}function M(f){return f===null||f===34||f===39||f===47||f===60||f===61||f===62||f===96||ce(f)?F(f):(e.consume(f),M)}function L(f){return f===47||f===62||Y(f)?C(f):n(f)}function z(f){return f===62?(e.consume(f),D):n(f)}function D(f){return f===null||R(f)?T(f):Y(f)?(e.consume(f),D):n(f)}function T(f){return f===45&&i===2?(e.consume(f),G):f===60&&i===1?(e.consume(f),U):f===62&&i===4?(e.consume(f),ae):f===63&&i===3?(e.consume(f),h):f===93&&i===5?(e.consume(f),ie):R(f)&&(i===6||i===7)?(e.exit("htmlFlowData"),e.check(kl,pe,P)(f)):f===null||R(f)?(e.exit("htmlFlowData"),P(f)):(e.consume(f),T)}function P(f){return e.check(bl,I,pe)(f)}function I(f){return e.enter("lineEnding"),e.consume(f),e.exit("lineEnding"),E}function E(f){return f===null||R(f)?P(f):(e.enter("htmlFlowData"),T(f))}function G(f){return f===45?(e.consume(f),h):T(f)}function U(f){return f===47?(e.consume(f),s="",re):T(f)}function re(f){if(f===62){const se=s.toLowerCase();return Ln.includes(se)?(e.consume(f),ae):T(f)}return Ne(f)&&s.length<8?(e.consume(f),s+=String.fromCharCode(f),re):T(f)}function ie(f){return f===93?(e.consume(f),h):T(f)}function h(f){return f===62?(e.consume(f),ae):f===45&&i===2?(e.consume(f),h):T(f)}function ae(f){return f===null||R(f)?(e.exit("htmlFlowData"),pe(f)):(e.consume(f),ae)}function pe(f){return e.exit("htmlFlow"),t(f)}}function jl(e,t,n){const r=this;return i;function i(s){return R(s)?(e.enter("lineEnding"),e.consume(s),e.exit("lineEnding"),a):n(s)}function a(s){return r.parser.lazy[r.now().line]?n(s):t(s)}}function Nl(e,t,n){return r;function r(i){return e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),e.attempt(gt,t,n)}}const Sl={name:"htmlText",tokenize:Cl};function Cl(e,t,n){const r=this;let i,a,s;return o;function o(h){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(h),d}function d(h){return h===33?(e.consume(h),u):h===47?(e.consume(h),F):h===63?(e.consume(h),C):Ne(h)?(e.consume(h),M):n(h)}function u(h){return h===45?(e.consume(h),c):h===91?(e.consume(h),a=0,w):Ne(h)?(e.consume(h),_):n(h)}function c(h){return h===45?(e.consume(h),p):n(h)}function m(h){return h===null?n(h):h===45?(e.consume(h),g):R(h)?(s=m,U(h)):(e.consume(h),m)}function g(h){return h===45?(e.consume(h),p):m(h)}function p(h){return h===62?G(h):h===45?g(h):m(h)}function w(h){const ae="CDATA[";return h===ae.charCodeAt(a++)?(e.consume(h),a===ae.length?k:w):n(h)}function k(h){return h===null?n(h):h===93?(e.consume(h),A):R(h)?(s=k,U(h)):(e.consume(h),k)}function A(h){return h===93?(e.consume(h),b):k(h)}function b(h){return h===62?G(h):h===93?(e.consume(h),b):k(h)}function _(h){return h===null||h===62?G(h):R(h)?(s=_,U(h)):(e.consume(h),_)}function C(h){return h===null?n(h):h===63?(e.consume(h),H):R(h)?(s=C,U(h)):(e.consume(h),C)}function H(h){return h===62?G(h):C(h)}function F(h){return Ne(h)?(e.consume(h),x):n(h)}function x(h){return h===45||ge(h)?(e.consume(h),x):j(h)}function j(h){return R(h)?(s=j,U(h)):Y(h)?(e.consume(h),j):G(h)}function M(h){return h===45||ge(h)?(e.consume(h),M):h===47||h===62||ce(h)?L(h):n(h)}function L(h){return h===47?(e.consume(h),G):h===58||h===95||Ne(h)?(e.consume(h),z):R(h)?(s=L,U(h)):Y(h)?(e.consume(h),L):G(h)}function z(h){return h===45||h===46||h===58||h===95||ge(h)?(e.consume(h),z):D(h)}function D(h){return h===61?(e.consume(h),T):R(h)?(s=D,U(h)):Y(h)?(e.consume(h),D):L(h)}function T(h){return h===null||h===60||h===61||h===62||h===96?n(h):h===34||h===39?(e.consume(h),i=h,P):R(h)?(s=T,U(h)):Y(h)?(e.consume(h),T):(e.consume(h),I)}function P(h){return h===i?(e.consume(h),i=void 0,E):h===null?n(h):R(h)?(s=P,U(h)):(e.consume(h),P)}function I(h){return h===null||h===34||h===39||h===60||h===61||h===96?n(h):h===47||h===62||ce(h)?L(h):(e.consume(h),I)}function E(h){return h===47||h===62||ce(h)?L(h):n(h)}function G(h){return h===62?(e.consume(h),e.exit("htmlTextData"),e.exit("htmlText"),t):n(h)}function U(h){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(h),e.exit("lineEnding"),re}function re(h){return Y(h)?X(e,ie,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(h):ie(h)}function ie(h){return e.enter("htmlTextData"),s(h)}}const tn={name:"labelEnd",resolveAll:zl,resolveTo:Tl,tokenize:_l},El={tokenize:Pl},Al={tokenize:Dl},Il={tokenize:Ml};function zl(e){let t=-1;const n=[];for(;++t<e.length;){const r=e[t][1];if(n.push(e[t]),r.type==="labelImage"||r.type==="labelLink"||r.type==="labelEnd"){const i=r.type==="labelImage"?4:2;r.type="data",t+=i}}return e.length!==n.length&&Se(e,0,e.length,n),e}function Tl(e,t){let n=e.length,r=0,i,a,s,o;for(;n--;)if(i=e[n][1],a){if(i.type==="link"||i.type==="labelLink"&&i._inactive)break;e[n][0]==="enter"&&i.type==="labelLink"&&(i._inactive=!0)}else if(s){if(e[n][0]==="enter"&&(i.type==="labelImage"||i.type==="labelLink")&&!i._balanced&&(a=n,i.type!=="labelLink")){r=2;break}}else i.type==="labelEnd"&&(s=n);const d={type:e[a][1].type==="labelLink"?"link":"image",start:{...e[a][1].start},end:{...e[e.length-1][1].end}},u={type:"label",start:{...e[a][1].start},end:{...e[s][1].end}},c={type:"labelText",start:{...e[a+r+2][1].end},end:{...e[s-2][1].start}};return o=[["enter",d,t],["enter",u,t]],o=xe(o,e.slice(a+1,a+r+3)),o=xe(o,[["enter",c,t]]),o=xe(o,en(t.parser.constructs.insideSpan.null,e.slice(a+r+4,s-3),t)),o=xe(o,[["exit",c,t],e[s-2],e[s-1],["exit",u,t]]),o=xe(o,e.slice(s+1)),o=xe(o,[["exit",d,t]]),Se(e,a,e.length,o),e}function _l(e,t,n){const r=this;let i=r.events.length,a,s;for(;i--;)if((r.events[i][1].type==="labelImage"||r.events[i][1].type==="labelLink")&&!r.events[i][1]._balanced){a=r.events[i][1];break}return o;function o(g){return a?a._inactive?m(g):(s=r.parser.defined.includes(Be(r.sliceSerialize({start:a.end,end:r.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(g),e.exit("labelMarker"),e.exit("labelEnd"),d):n(g)}function d(g){return g===40?e.attempt(El,c,s?c:m)(g):g===91?e.attempt(Al,c,s?u:m)(g):s?c(g):m(g)}function u(g){return e.attempt(Il,c,m)(g)}function c(g){return t(g)}function m(g){return a._balanced=!0,n(g)}}function Pl(e,t,n){return r;function r(m){return e.enter("resource"),e.enter("resourceMarker"),e.consume(m),e.exit("resourceMarker"),i}function i(m){return ce(m)?Ze(e,a)(m):a(m)}function a(m){return m===41?c(m):Cr(e,s,o,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(m)}function s(m){return ce(m)?Ze(e,d)(m):c(m)}function o(m){return n(m)}function d(m){return m===34||m===39||m===40?Ar(e,u,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(m):c(m)}function u(m){return ce(m)?Ze(e,c)(m):c(m)}function c(m){return m===41?(e.enter("resourceMarker"),e.consume(m),e.exit("resourceMarker"),e.exit("resource"),t):n(m)}}function Dl(e,t,n){const r=this;return i;function i(o){return Er.call(r,e,a,s,"reference","referenceMarker","referenceString")(o)}function a(o){return r.parser.defined.includes(Be(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(o):n(o)}function s(o){return n(o)}}function Ml(e,t,n){return r;function r(a){return e.enter("reference"),e.enter("referenceMarker"),e.consume(a),e.exit("referenceMarker"),i}function i(a){return a===93?(e.enter("referenceMarker"),e.consume(a),e.exit("referenceMarker"),e.exit("reference"),t):n(a)}}const Ll={name:"labelStartImage",resolveAll:tn.resolveAll,tokenize:Ol};function Ol(e,t,n){const r=this;return i;function i(o){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(o),e.exit("labelImageMarker"),a}function a(o){return o===91?(e.enter("labelMarker"),e.consume(o),e.exit("labelMarker"),e.exit("labelImage"),s):n(o)}function s(o){return o===94&&"_hiddenFootnoteSupport"in r.parser.constructs?n(o):t(o)}}const Rl={name:"labelStartLink",resolveAll:tn.resolveAll,tokenize:Fl};function Fl(e,t,n){const r=this;return i;function i(s){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(s),e.exit("labelMarker"),e.exit("labelLink"),a}function a(s){return s===94&&"_hiddenFootnoteSupport"in r.parser.constructs?n(s):t(s)}}const Ct={name:"lineEnding",tokenize:Bl};function Bl(e,t){return n;function n(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),X(e,t,"linePrefix")}}const mt={name:"thematicBreak",tokenize:Hl};function Hl(e,t,n){let r=0,i;return a;function a(u){return e.enter("thematicBreak"),s(u)}function s(u){return i=u,o(u)}function o(u){return u===i?(e.enter("thematicBreakSequence"),d(u)):r>=3&&(u===null||R(u))?(e.exit("thematicBreak"),t(u)):n(u)}function d(u){return u===i?(e.consume(u),r++,d):(e.exit("thematicBreakSequence"),Y(u)?X(e,o,"whitespace")(u):o(u))}}const ue={continuation:{tokenize:ql},exit:Kl,name:"list",tokenize:Ul},Vl={partial:!0,tokenize:Gl},$l={partial:!0,tokenize:Yl};function Ul(e,t,n){const r=this,i=r.events[r.events.length-1];let a=i&&i[1].type==="linePrefix"?i[2].sliceSerialize(i[1],!0).length:0,s=0;return o;function o(p){const w=r.containerState.type||(p===42||p===43||p===45?"listUnordered":"listOrdered");if(w==="listUnordered"?!r.containerState.marker||p===r.containerState.marker:Vt(p)){if(r.containerState.type||(r.containerState.type=w,e.enter(w,{_container:!0})),w==="listUnordered")return e.enter("listItemPrefix"),p===42||p===45?e.check(mt,n,u)(p):u(p);if(!r.interrupt||p===49)return e.enter("listItemPrefix"),e.enter("listItemValue"),d(p)}return n(p)}function d(p){return Vt(p)&&++s<10?(e.consume(p),d):(!r.interrupt||s<2)&&(r.containerState.marker?p===r.containerState.marker:p===41||p===46)?(e.exit("listItemValue"),u(p)):n(p)}function u(p){return e.enter("listItemMarker"),e.consume(p),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||p,e.check(gt,r.interrupt?n:c,e.attempt(Vl,g,m))}function c(p){return r.containerState.initialBlankLine=!0,a++,g(p)}function m(p){return Y(p)?(e.enter("listItemPrefixWhitespace"),e.consume(p),e.exit("listItemPrefixWhitespace"),g):n(p)}function g(p){return r.containerState.size=a+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(p)}}function ql(e,t,n){const r=this;return r.containerState._closeFlow=void 0,e.check(gt,i,a);function i(o){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,X(e,t,"listItemIndent",r.containerState.size+1)(o)}function a(o){return r.containerState.furtherBlankLines||!Y(o)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,s(o)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt($l,t,s)(o))}function s(o){return r.containerState._closeFlow=!0,r.interrupt=void 0,X(e,e.attempt(ue,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(o)}}function Yl(e,t,n){const r=this;return X(e,i,"listItemIndent",r.containerState.size+1);function i(a){const s=r.events[r.events.length-1];return s&&s[1].type==="listItemIndent"&&s[2].sliceSerialize(s[1],!0).length===r.containerState.size?t(a):n(a)}}function Kl(e){e.exit(this.containerState.type)}function Gl(e,t,n){const r=this;return X(e,i,"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5);function i(a){const s=r.events[r.events.length-1];return!Y(a)&&s&&s[1].type==="listItemPrefixWhitespace"?t(a):n(a)}}const On={name:"setextUnderline",resolveTo:Jl,tokenize:Wl};function Jl(e,t){let n=e.length,r,i,a;for(;n--;)if(e[n][0]==="enter"){if(e[n][1].type==="content"){r=n;break}e[n][1].type==="paragraph"&&(i=n)}else e[n][1].type==="content"&&e.splice(n,1),!a&&e[n][1].type==="definition"&&(a=n);const s={type:"setextHeading",start:{...e[r][1].start},end:{...e[e.length-1][1].end}};return e[i][1].type="setextHeadingText",a?(e.splice(i,0,["enter",s,t]),e.splice(a+1,0,["exit",e[r][1],t]),e[r][1].end={...e[a][1].end}):e[r][1]=s,e.push(["exit",s,t]),e}function Wl(e,t,n){const r=this;let i;return a;function a(u){let c=r.events.length,m;for(;c--;)if(r.events[c][1].type!=="lineEnding"&&r.events[c][1].type!=="linePrefix"&&r.events[c][1].type!=="content"){m=r.events[c][1].type==="paragraph";break}return!r.parser.lazy[r.now().line]&&(r.interrupt||m)?(e.enter("setextHeadingLine"),i=u,s(u)):n(u)}function s(u){return e.enter("setextHeadingLineSequence"),o(u)}function o(u){return u===i?(e.consume(u),o):(e.exit("setextHeadingLineSequence"),Y(u)?X(e,d,"lineSuffix")(u):d(u))}function d(u){return u===null||R(u)?(e.exit("setextHeadingLine"),t(u)):n(u)}}const Xl={tokenize:Ql};function Ql(e){const t=this,n=e.attempt(gt,r,e.attempt(this.parser.constructs.flowInitial,i,X(e,e.attempt(this.parser.constructs.flow,i,e.attempt(rl,i)),"linePrefix")));return n;function r(a){if(a===null){e.consume(a);return}return e.enter("lineEndingBlank"),e.consume(a),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n}function i(a){if(a===null){e.consume(a);return}return e.enter("lineEnding"),e.consume(a),e.exit("lineEnding"),t.currentConstruct=void 0,n}}const Zl={resolveAll:zr()},es=Ir("string"),ts=Ir("text");function Ir(e){return{resolveAll:zr(e==="text"?ns:void 0),tokenize:t};function t(n){const r=this,i=this.parser.constructs[e],a=n.attempt(i,s,o);return s;function s(c){return u(c)?a(c):o(c)}function o(c){if(c===null){n.consume(c);return}return n.enter("data"),n.consume(c),d}function d(c){return u(c)?(n.exit("data"),a(c)):(n.consume(c),d)}function u(c){if(c===null)return!0;const m=i[c];let g=-1;if(m)for(;++g<m.length;){const p=m[g];if(!p.previous||p.previous.call(r,r.previous))return!0}return!1}}}function zr(e){return t;function t(n,r){let i=-1,a;for(;++i<=n.length;)a===void 0?n[i]&&n[i][1].type==="data"&&(a=i,i++):(!n[i]||n[i][1].type!=="data")&&(i!==a+2&&(n[a][1].end=n[i-1][1].end,n.splice(a+2,i-a-2),i=a+2),a=void 0);return e?e(n,r):n}}function ns(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||e[n][1].type==="lineEnding")&&e[n-1][1].type==="data"){const r=e[n-1][1],i=t.sliceStream(r);let a=i.length,s=-1,o=0,d;for(;a--;){const u=i[a];if(typeof u=="string"){for(s=u.length;u.charCodeAt(s-1)===32;)o++,s--;if(s)break;s=-1}else if(u===-2)d=!0,o++;else if(u!==-1){a++;break}}if(t._contentTypeTextTrailing&&n===e.length&&(o=0),o){const u={type:n===e.length||d||o<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:a?s:r.start._bufferIndex+s,_index:r.start._index+a,line:r.end.line,column:r.end.column-o,offset:r.end.offset-o},end:{...r.end}};r.end={...u.start},r.start.offset===r.end.offset?Object.assign(r,u):(e.splice(n,0,["enter",u,t],["exit",u,t]),n+=2)}n++}return e}const rs={42:ue,43:ue,45:ue,48:ue,49:ue,50:ue,51:ue,52:ue,53:ue,54:ue,55:ue,56:ue,57:ue,62:vr},is={91:ol},as={[-2]:St,[-1]:St,32:St},ls={35:hl,42:mt,45:[On,mt],60:yl,61:On,95:mt,96:Mn,126:Mn},ss={38:Nr,92:jr},os={[-5]:Ct,[-4]:Ct,[-3]:Ct,33:Ll,38:Nr,42:$t,60:[Ra,Sl],91:Rl,92:[pl,jr],93:tn,95:$t,96:Xa},us={null:[$t,Zl]},cs={null:[42,95]},ds={null:[]},ps=Object.freeze(Object.defineProperty({__proto__:null,attentionMarkers:cs,contentInitial:is,disable:ds,document:rs,flow:ls,flowInitial:as,insideSpan:us,string:ss,text:os},Symbol.toStringTag,{value:"Module"}));function ms(e,t,n){let r={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0};const i={},a=[];let s=[],o=[];const d={attempt:j(F),check:j(x),consume:_,enter:C,exit:H,interrupt:j(x,{interrupt:!0})},u={code:null,containerState:{},defineSkip:k,events:[],now:w,parser:e,previous:null,sliceSerialize:g,sliceStream:p,write:m};let c=t.tokenize.call(u,d);return t.resolveAll&&a.push(t),u;function m(D){return s=xe(s,D),A(),s[s.length-1]!==null?[]:(M(t,0),u.events=en(a,u.events,u),u.events)}function g(D,T){return fs(p(D),T)}function p(D){return hs(s,D)}function w(){const{_bufferIndex:D,_index:T,line:P,column:I,offset:E}=r;return{_bufferIndex:D,_index:T,line:P,column:I,offset:E}}function k(D){i[D.line]=D.column,z()}function A(){let D;for(;r._index<s.length;){const T=s[r._index];if(typeof T=="string")for(D=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===D&&r._bufferIndex<T.length;)b(T.charCodeAt(r._bufferIndex));else b(T)}}function b(D){c=c(D)}function _(D){R(D)?(r.line++,r.column=1,r.offset+=D===-3?2:1,z()):D!==-1&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===s[r._index].length&&(r._bufferIndex=-1,r._index++)),u.previous=D}function C(D,T){const P=T||{};return P.type=D,P.start=w(),u.events.push(["enter",P,u]),o.push(P),P}function H(D){const T=o.pop();return T.end=w(),u.events.push(["exit",T,u]),T}function F(D,T){M(D,T.from)}function x(D,T){T.restore()}function j(D,T){return P;function P(I,E,G){let U,re,ie,h;return Array.isArray(I)?pe(I):"tokenize"in I?pe([I]):ae(I);function ae(te){return Ae;function Ae(we){const Ie=we!==null&&te[we],ze=we!==null&&te.null,Me=[...Array.isArray(Ie)?Ie:Ie?[Ie]:[],...Array.isArray(ze)?ze:ze?[ze]:[]];return pe(Me)(we)}}function pe(te){return U=te,re=0,te.length===0?G:f(te[re])}function f(te){return Ae;function Ae(we){return h=L(),ie=te,te.partial||(u.currentConstruct=te),te.name&&u.parser.constructs.disable.null.includes(te.name)?ye():te.tokenize.call(T?Object.assign(Object.create(u),T):u,d,se,ye)(we)}}function se(te){return D(ie,h),E}function ye(te){return h.restore(),++re<U.length?f(U[re]):G}}}function M(D,T){D.resolveAll&&!a.includes(D)&&a.push(D),D.resolve&&Se(u.events,T,u.events.length-T,D.resolve(u.events.slice(T),u)),D.resolveTo&&(u.events=D.resolveTo(u.events,u))}function L(){const D=w(),T=u.previous,P=u.currentConstruct,I=u.events.length,E=Array.from(o);return{from:I,restore:G};function G(){r=D,u.previous=T,u.currentConstruct=P,u.events.length=I,o=E,z()}}function z(){r.line in i&&r.column<2&&(r.column=i[r.line],r.offset+=i[r.line]-1)}}function hs(e,t){const n=t.start._index,r=t.start._bufferIndex,i=t.end._index,a=t.end._bufferIndex;let s;if(n===i)s=[e[n].slice(r,a)];else{if(s=e.slice(n,i),r>-1){const o=s[0];typeof o=="string"?s[0]=o.slice(r):s.shift()}a>0&&s.push(e[i].slice(0,a))}return s}function fs(e,t){let n=-1;const r=[];let i;for(;++n<e.length;){const a=e[n];let s;if(typeof a=="string")s=a;else switch(a){case-5:{s="\r";break}case-4:{s=`
`;break}case-3:{s=`\r
`;break}case-2:{s=t?" ":"	";break}case-1:{if(!t&&i)continue;s=" ";break}default:s=String.fromCharCode(a)}i=a===-2,r.push(s)}return r.join("")}function gs(e){const r={constructs:ja([ps,...(e||{}).extensions||[]]),content:i(Ta),defined:[],document:i(Pa),flow:i(Xl),lazy:{},string:i(es),text:i(ts)};return r;function i(a){return s;function s(o){return ms(r,a,o)}}}function xs(e){for(;!Sr(e););return e}const Rn=/[\0\t\n\r]/g;function ys(){let e=1,t="",n=!0,r;return i;function i(a,s,o){const d=[];let u,c,m,g,p;for(a=t+(typeof a=="string"?a.toString():new TextDecoder(s||void 0).decode(a)),m=0,t="",n&&(a.charCodeAt(0)===65279&&m++,n=void 0);m<a.length;){if(Rn.lastIndex=m,u=Rn.exec(a),g=u&&u.index!==void 0?u.index:a.length,p=a.charCodeAt(g),!u){t=a.slice(m);break}if(p===10&&m===g&&r)d.push(-3),r=void 0;else switch(r&&(d.push(-5),r=void 0),m<g&&(d.push(a.slice(m,g)),e+=g-m),p){case 0:{d.push(65533),e++;break}case 9:{for(c=Math.ceil(e/4)*4,d.push(-2);e++<c;)d.push(-1);break}case 10:{d.push(-4),e=1;break}default:r=!0,e=1}m=g+1}return o&&(r&&d.push(-5),t&&d.push(t),d.push(null)),d}}const ks=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function bs(e){return e.replace(ks,ws)}function ws(e,t,n){if(t)return t;if(n.charCodeAt(0)===35){const i=n.charCodeAt(1),a=i===120||i===88;return wr(n.slice(a?2:1),a?16:10)}return Zt(n)||e}const Tr={}.hasOwnProperty;function vs(e,t,n){return typeof t!="string"&&(n=t,t=void 0),js(n)(xs(gs(n).document().write(ys()(e,t,!0))))}function js(e){const t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:a(ne),autolinkProtocol:L,autolinkEmail:L,atxHeading:a(lt),blockQuote:a(ze),characterEscape:L,characterReference:L,codeFenced:a(Me),codeFencedFenceInfo:s,codeFencedFenceMeta:s,codeIndented:a(Me,s),codeText:a(at,s),codeTextData:L,data:L,codeFlowValue:L,definition:a(kt),definitionDestinationString:s,definitionLabelString:s,definitionTitleString:s,emphasis:a(bt),hardBreakEscape:a(st),hardBreakTrailing:a(st),htmlFlow:a(N,s),htmlFlowData:L,htmlText:a(N,s),htmlTextData:L,image:a(q),label:s,link:a(ne),listItem:a(me),listItemValue:g,listOrdered:a(oe,m),listUnordered:a(oe),paragraph:a(ke),reference:f,referenceString:s,resourceDestinationString:s,resourceTitleString:s,setextHeading:a(lt),strong:a(ve),thematicBreak:a(on)},exit:{atxHeading:d(),atxHeadingSequence:F,autolink:d(),autolinkEmail:Ie,autolinkProtocol:we,blockQuote:d(),characterEscapeValue:z,characterReferenceMarkerHexadecimal:ye,characterReferenceMarkerNumeric:ye,characterReferenceValue:te,characterReference:Ae,codeFenced:d(A),codeFencedFence:k,codeFencedFenceInfo:p,codeFencedFenceMeta:w,codeFlowValue:z,codeIndented:d(b),codeText:d(E),codeTextData:z,data:z,definition:d(),definitionDestinationString:H,definitionLabelString:_,definitionTitleString:C,emphasis:d(),hardBreakEscape:d(T),hardBreakTrailing:d(T),htmlFlow:d(P),htmlFlowData:z,htmlText:d(I),htmlTextData:z,image:d(U),label:ie,labelText:re,lineEnding:D,link:d(G),listItem:d(),listOrdered:d(),listUnordered:d(),paragraph:d(),referenceString:se,resourceDestinationString:h,resourceTitleString:ae,resource:pe,setextHeading:d(M),setextHeadingLineSequence:j,setextHeadingText:x,strong:d(),thematicBreak:d()}};_r(t,(e||{}).mdastExtensions||[]);const n={};return r;function r(y){let S={type:"root",children:[]};const O={stack:[S],tokenStack:[],config:t,enter:o,exit:u,buffer:s,resume:c,data:n},$=[];let K=-1;for(;++K<y.length;)if(y[K][1].type==="listOrdered"||y[K][1].type==="listUnordered")if(y[K][0]==="enter")$.push(K);else{const Q=$.pop();K=i(y,Q,K)}for(K=-1;++K<y.length;){const Q=t[y[K][0]];Tr.call(Q,y[K][1].type)&&Q[y[K][1].type].call(Object.assign({sliceSerialize:y[K][2].sliceSerialize},O),y[K][1])}if(O.tokenStack.length>0){const Q=O.tokenStack[O.tokenStack.length-1];(Q[1]||Fn).call(O,void 0,Q[0])}for(S.position={start:Te(y.length>0?y[0][1].start:{line:1,column:1,offset:0}),end:Te(y.length>0?y[y.length-2][1].end:{line:1,column:1,offset:0})},K=-1;++K<t.transforms.length;)S=t.transforms[K](S)||S;return S}function i(y,S,O){let $=S-1,K=-1,Q=!1,Pe,Ce,$e,Ue;for(;++$<=O;){const he=y[$];switch(he[1].type){case"listUnordered":case"listOrdered":case"blockQuote":{he[0]==="enter"?K++:K--,Ue=void 0;break}case"lineEndingBlank":{he[0]==="enter"&&(Pe&&!Ue&&!K&&!$e&&($e=$),Ue=void 0);break}case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:Ue=void 0}if(!K&&he[0]==="enter"&&he[1].type==="listItemPrefix"||K===-1&&he[0]==="exit"&&(he[1].type==="listUnordered"||he[1].type==="listOrdered")){if(Pe){let Le=$;for(Ce=void 0;Le--;){const Ee=y[Le];if(Ee[1].type==="lineEnding"||Ee[1].type==="lineEndingBlank"){if(Ee[0]==="exit")continue;Ce&&(y[Ce][1].type="lineEndingBlank",Q=!0),Ee[1].type="lineEnding",Ce=Le}else if(!(Ee[1].type==="linePrefix"||Ee[1].type==="blockQuotePrefix"||Ee[1].type==="blockQuotePrefixWhitespace"||Ee[1].type==="blockQuoteMarker"||Ee[1].type==="listItemIndent"))break}$e&&(!Ce||$e<Ce)&&(Pe._spread=!0),Pe.end=Object.assign({},Ce?y[Ce][1].start:he[1].end),y.splice(Ce||$,0,["exit",Pe,he[2]]),$++,O++}if(he[1].type==="listItemPrefix"){const Le={type:"listItem",_spread:!1,start:Object.assign({},he[1].start),end:void 0};Pe=Le,y.splice($,0,["enter",Le,he[2]]),$++,O++,$e=void 0,Ue=!0}}}return y[S][1]._spread=Q,O}function a(y,S){return O;function O($){o.call(this,y($),$),S&&S.call(this,$)}}function s(){this.stack.push({type:"fragment",children:[]})}function o(y,S,O){this.stack[this.stack.length-1].children.push(y),this.stack.push(y),this.tokenStack.push([S,O||void 0]),y.position={start:Te(S.start),end:void 0}}function d(y){return S;function S(O){y&&y.call(this,O),u.call(this,O)}}function u(y,S){const O=this.stack.pop(),$=this.tokenStack.pop();if($)$[0].type!==y.type&&(S?S.call(this,y,$[0]):($[1]||Fn).call(this,y,$[0]));else throw new Error("Cannot close `"+y.type+"` ("+Qe({start:y.start,end:y.end})+"): it’s not open");O.position.end=Te(y.end)}function c(){return wa(this.stack.pop())}function m(){this.data.expectingFirstListItemValue=!0}function g(y){if(this.data.expectingFirstListItemValue){const S=this.stack[this.stack.length-2];S.start=Number.parseInt(this.sliceSerialize(y),10),this.data.expectingFirstListItemValue=void 0}}function p(){const y=this.resume(),S=this.stack[this.stack.length-1];S.lang=y}function w(){const y=this.resume(),S=this.stack[this.stack.length-1];S.meta=y}function k(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)}function A(){const y=this.resume(),S=this.stack[this.stack.length-1];S.value=y.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}function b(){const y=this.resume(),S=this.stack[this.stack.length-1];S.value=y.replace(/(\r?\n|\r)$/g,"")}function _(y){const S=this.resume(),O=this.stack[this.stack.length-1];O.label=S,O.identifier=Be(this.sliceSerialize(y)).toLowerCase()}function C(){const y=this.resume(),S=this.stack[this.stack.length-1];S.title=y}function H(){const y=this.resume(),S=this.stack[this.stack.length-1];S.url=y}function F(y){const S=this.stack[this.stack.length-1];if(!S.depth){const O=this.sliceSerialize(y).length;S.depth=O}}function x(){this.data.setextHeadingSlurpLineEnding=!0}function j(y){const S=this.stack[this.stack.length-1];S.depth=this.sliceSerialize(y).codePointAt(0)===61?1:2}function M(){this.data.setextHeadingSlurpLineEnding=void 0}function L(y){const O=this.stack[this.stack.length-1].children;let $=O[O.length-1];(!$||$.type!=="text")&&($=be(),$.position={start:Te(y.start),end:void 0},O.push($)),this.stack.push($)}function z(y){const S=this.stack.pop();S.value+=this.sliceSerialize(y),S.position.end=Te(y.end)}function D(y){const S=this.stack[this.stack.length-1];if(this.data.atHardBreak){const O=S.children[S.children.length-1];O.position.end=Te(y.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(S.type)&&(L.call(this,y),z.call(this,y))}function T(){this.data.atHardBreak=!0}function P(){const y=this.resume(),S=this.stack[this.stack.length-1];S.value=y}function I(){const y=this.resume(),S=this.stack[this.stack.length-1];S.value=y}function E(){const y=this.resume(),S=this.stack[this.stack.length-1];S.value=y}function G(){const y=this.stack[this.stack.length-1];if(this.data.inReference){const S=this.data.referenceType||"shortcut";y.type+="Reference",y.referenceType=S,delete y.url,delete y.title}else delete y.identifier,delete y.label;this.data.referenceType=void 0}function U(){const y=this.stack[this.stack.length-1];if(this.data.inReference){const S=this.data.referenceType||"shortcut";y.type+="Reference",y.referenceType=S,delete y.url,delete y.title}else delete y.identifier,delete y.label;this.data.referenceType=void 0}function re(y){const S=this.sliceSerialize(y),O=this.stack[this.stack.length-2];O.label=bs(S),O.identifier=Be(S).toLowerCase()}function ie(){const y=this.stack[this.stack.length-1],S=this.resume(),O=this.stack[this.stack.length-1];if(this.data.inReference=!0,O.type==="link"){const $=y.children;O.children=$}else O.alt=S}function h(){const y=this.resume(),S=this.stack[this.stack.length-1];S.url=y}function ae(){const y=this.resume(),S=this.stack[this.stack.length-1];S.title=y}function pe(){this.data.inReference=void 0}function f(){this.data.referenceType="collapsed"}function se(y){const S=this.resume(),O=this.stack[this.stack.length-1];O.label=S,O.identifier=Be(this.sliceSerialize(y)).toLowerCase(),this.data.referenceType="full"}function ye(y){this.data.characterReferenceType=y.type}function te(y){const S=this.sliceSerialize(y),O=this.data.characterReferenceType;let $;O?($=wr(S,O==="characterReferenceMarkerNumeric"?10:16),this.data.characterReferenceType=void 0):$=Zt(S);const K=this.stack[this.stack.length-1];K.value+=$}function Ae(y){const S=this.stack.pop();S.position.end=Te(y.end)}function we(y){z.call(this,y);const S=this.stack[this.stack.length-1];S.url=this.sliceSerialize(y)}function Ie(y){z.call(this,y);const S=this.stack[this.stack.length-1];S.url="mailto:"+this.sliceSerialize(y)}function ze(){return{type:"blockquote",children:[]}}function Me(){return{type:"code",lang:null,meta:null,value:""}}function at(){return{type:"inlineCode",value:""}}function kt(){return{type:"definition",identifier:"",label:null,title:null,url:""}}function bt(){return{type:"emphasis",children:[]}}function lt(){return{type:"heading",depth:0,children:[]}}function st(){return{type:"break"}}function N(){return{type:"html",value:""}}function q(){return{type:"image",title:null,url:"",alt:null}}function ne(){return{type:"link",title:null,url:"",children:[]}}function oe(y){return{type:"list",ordered:y.type==="listOrdered",start:null,spread:y._spread,children:[]}}function me(y){return{type:"listItem",spread:y._spread,checked:null,children:[]}}function ke(){return{type:"paragraph",children:[]}}function ve(){return{type:"strong",children:[]}}function be(){return{type:"text",value:""}}function on(){return{type:"thematicBreak"}}}function Te(e){return{line:e.line,column:e.column,offset:e.offset}}function _r(e,t){let n=-1;for(;++n<t.length;){const r=t[n];Array.isArray(r)?_r(e,r):Ns(e,r)}}function Ns(e,t){let n;for(n in t)if(Tr.call(t,n))switch(n){case"canContainEols":{const r=t[n];r&&e[n].push(...r);break}case"transforms":{const r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{const r=t[n];r&&Object.assign(e[n],r);break}}}function Fn(e,t){throw e?new Error("Cannot close `"+e.type+"` ("+Qe({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+Qe({start:t.start,end:t.end})+") is open"):new Error("Cannot close document, a token (`"+t.type+"`, "+Qe({start:t.start,end:t.end})+") is still open")}function Ss(e){const t=this;t.parser=n;function n(r){return vs(r,{...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})}}function Cs(e,t){const n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)}function Es(e,t){const n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:`
`}]}function As(e,t){const n=t.value?t.value+`
`:"",r={};t.lang&&(r.className=["language-"+t.lang]);let i={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(i.data={meta:t.meta}),e.patch(t,i),i=e.applyData(t,i),i={type:"element",tagName:"pre",properties:{},children:[i]},e.patch(t,i),i}function Is(e,t){const n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function zs(e,t){const n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function Ts(e,t){const n=typeof e.options.clobberPrefix=="string"?e.options.clobberPrefix:"user-content-",r=String(t.identifier).toUpperCase(),i=Ve(r.toLowerCase()),a=e.footnoteOrder.indexOf(r);let s,o=e.footnoteCounts.get(r);o===void 0?(o=0,e.footnoteOrder.push(r),s=e.footnoteOrder.length):s=a+1,o+=1,e.footnoteCounts.set(r,o);const d={type:"element",tagName:"a",properties:{href:"#"+n+"fn-"+i,id:n+"fnref-"+i+(o>1?"-"+o:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(s)}]};e.patch(t,d);const u={type:"element",tagName:"sup",properties:{},children:[d]};return e.patch(t,u),e.applyData(t,u)}function _s(e,t){const n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function Ps(e,t){if(e.options.allowDangerousHtml){const n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}}function Pr(e,t){const n=t.referenceType;let r="]";if(n==="collapsed"?r+="[]":n==="full"&&(r+="["+(t.label||t.identifier)+"]"),t.type==="imageReference")return[{type:"text",value:"!["+t.alt+r}];const i=e.all(t),a=i[0];a&&a.type==="text"?a.value="["+a.value:i.unshift({type:"text",value:"["});const s=i[i.length-1];return s&&s.type==="text"?s.value+=r:i.push({type:"text",value:r}),i}function Ds(e,t){const n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return Pr(e,t);const i={src:Ve(r.url||""),alt:t.alt};r.title!==null&&r.title!==void 0&&(i.title=r.title);const a={type:"element",tagName:"img",properties:i,children:[]};return e.patch(t,a),e.applyData(t,a)}function Ms(e,t){const n={src:Ve(t.url)};t.alt!==null&&t.alt!==void 0&&(n.alt=t.alt),t.title!==null&&t.title!==void 0&&(n.title=t.title);const r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)}function Ls(e,t){const n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);const r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)}function Os(e,t){const n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return Pr(e,t);const i={href:Ve(r.url||"")};r.title!==null&&r.title!==void 0&&(i.title=r.title);const a={type:"element",tagName:"a",properties:i,children:e.all(t)};return e.patch(t,a),e.applyData(t,a)}function Rs(e,t){const n={href:Ve(t.url)};t.title!==null&&t.title!==void 0&&(n.title=t.title);const r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)}function Fs(e,t,n){const r=e.all(t),i=n?Bs(n):Dr(t),a={},s=[];if(typeof t.checked=="boolean"){const c=r[0];let m;c&&c.type==="element"&&c.tagName==="p"?m=c:(m={type:"element",tagName:"p",properties:{},children:[]},r.unshift(m)),m.children.length>0&&m.children.unshift({type:"text",value:" "}),m.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),a.className=["task-list-item"]}let o=-1;for(;++o<r.length;){const c=r[o];(i||o!==0||c.type!=="element"||c.tagName!=="p")&&s.push({type:"text",value:`
`}),c.type==="element"&&c.tagName==="p"&&!i?s.push(...c.children):s.push(c)}const d=r[r.length-1];d&&(i||d.type!=="element"||d.tagName!=="p")&&s.push({type:"text",value:`
`});const u={type:"element",tagName:"li",properties:a,children:s};return e.patch(t,u),e.applyData(t,u)}function Bs(e){let t=!1;if(e.type==="list"){t=e.spread||!1;const n=e.children;let r=-1;for(;!t&&++r<n.length;)t=Dr(n[r])}return t}function Dr(e){const t=e.spread;return t==null?e.children.length>1:t}function Hs(e,t){const n={},r=e.all(t);let i=-1;for(typeof t.start=="number"&&t.start!==1&&(n.start=t.start);++i<r.length;){const s=r[i];if(s.type==="element"&&s.tagName==="li"&&s.properties&&Array.isArray(s.properties.className)&&s.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}const a={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,a),e.applyData(t,a)}function Vs(e,t){const n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function $s(e,t){const n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)}function Us(e,t){const n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function qs(e,t){const n=e.all(t),r=n.shift(),i=[];if(r){const s={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],s),i.push(s)}if(n.length>0){const s={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},o=Jt(t.children[1]),d=hr(t.children[t.children.length-1]);o&&d&&(s.position={start:o,end:d}),i.push(s)}const a={type:"element",tagName:"table",properties:{},children:e.wrap(i,!0)};return e.patch(t,a),e.applyData(t,a)}function Ys(e,t,n){const r=n?n.children:void 0,a=(r?r.indexOf(t):1)===0?"th":"td",s=n&&n.type==="table"?n.align:void 0,o=s?s.length:t.children.length;let d=-1;const u=[];for(;++d<o;){const m=t.children[d],g={},p=s?s[d]:void 0;p&&(g.align=p);let w={type:"element",tagName:a,properties:g,children:[]};m&&(w.children=e.all(m),e.patch(m,w),w=e.applyData(m,w)),u.push(w)}const c={type:"element",tagName:"tr",properties:{},children:e.wrap(u,!0)};return e.patch(t,c),e.applyData(t,c)}function Ks(e,t){const n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}const Bn=9,Hn=32;function Gs(e){const t=String(e),n=/\r?\n|\r/g;let r=n.exec(t),i=0;const a=[];for(;r;)a.push(Vn(t.slice(i,r.index),i>0,!0),r[0]),i=r.index+r[0].length,r=n.exec(t);return a.push(Vn(t.slice(i),i>0,!1)),a.join("")}function Vn(e,t,n){let r=0,i=e.length;if(t){let a=e.codePointAt(r);for(;a===Bn||a===Hn;)r++,a=e.codePointAt(r)}if(n){let a=e.codePointAt(i-1);for(;a===Bn||a===Hn;)i--,a=e.codePointAt(i-1)}return i>r?e.slice(r,i):""}function Js(e,t){const n={type:"text",value:Gs(String(t.value))};return e.patch(t,n),e.applyData(t,n)}function Ws(e,t){const n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)}const Xs={blockquote:Cs,break:Es,code:As,delete:Is,emphasis:zs,footnoteReference:Ts,heading:_s,html:Ps,imageReference:Ds,image:Ms,inlineCode:Ls,linkReference:Os,link:Rs,listItem:Fs,list:Hs,paragraph:Vs,root:$s,strong:Us,table:qs,tableCell:Ks,tableRow:Ys,text:Js,thematicBreak:Ws,toml:ct,yaml:ct,definition:ct,footnoteDefinition:ct};function ct(){}const Mr=-1,xt=0,et=1,ht=2,nn=3,rn=4,an=5,ln=6,Lr=7,Or=8,$n=typeof self=="object"?self:globalThis,Qs=(e,t)=>{const n=(i,a)=>(e.set(a,i),i),r=i=>{if(e.has(i))return e.get(i);const[a,s]=t[i];switch(a){case xt:case Mr:return n(s,i);case et:{const o=n([],i);for(const d of s)o.push(r(d));return o}case ht:{const o=n({},i);for(const[d,u]of s)o[r(d)]=r(u);return o}case nn:return n(new Date(s),i);case rn:{const{source:o,flags:d}=s;return n(new RegExp(o,d),i)}case an:{const o=n(new Map,i);for(const[d,u]of s)o.set(r(d),r(u));return o}case ln:{const o=n(new Set,i);for(const d of s)o.add(r(d));return o}case Lr:{const{name:o,message:d}=s;return n(new $n[o](d),i)}case Or:return n(BigInt(s),i);case"BigInt":return n(Object(BigInt(s)),i);case"ArrayBuffer":return n(new Uint8Array(s).buffer,s);case"DataView":{const{buffer:o}=new Uint8Array(s);return n(new DataView(o),s)}}return n(new $n[a](s),i)};return r},Un=e=>Qs(new Map,e)(0),Re="",{toString:Zs}={},{keys:eo}=Object,Ge=e=>{const t=typeof e;if(t!=="object"||!e)return[xt,t];const n=Zs.call(e).slice(8,-1);switch(n){case"Array":return[et,Re];case"Object":return[ht,Re];case"Date":return[nn,Re];case"RegExp":return[rn,Re];case"Map":return[an,Re];case"Set":return[ln,Re];case"DataView":return[et,n]}return n.includes("Array")?[et,n]:n.includes("Error")?[Lr,n]:[ht,n]},dt=([e,t])=>e===xt&&(t==="function"||t==="symbol"),to=(e,t,n,r)=>{const i=(s,o)=>{const d=r.push(s)-1;return n.set(o,d),d},a=s=>{if(n.has(s))return n.get(s);let[o,d]=Ge(s);switch(o){case xt:{let c=s;switch(d){case"bigint":o=Or,c=s.toString();break;case"function":case"symbol":if(e)throw new TypeError("unable to serialize "+d);c=null;break;case"undefined":return i([Mr],s)}return i([o,c],s)}case et:{if(d){let g=s;return d==="DataView"?g=new Uint8Array(s.buffer):d==="ArrayBuffer"&&(g=new Uint8Array(s)),i([d,[...g]],s)}const c=[],m=i([o,c],s);for(const g of s)c.push(a(g));return m}case ht:{if(d)switch(d){case"BigInt":return i([d,s.toString()],s);case"Boolean":case"Number":case"String":return i([d,s.valueOf()],s)}if(t&&"toJSON"in s)return a(s.toJSON());const c=[],m=i([o,c],s);for(const g of eo(s))(e||!dt(Ge(s[g])))&&c.push([a(g),a(s[g])]);return m}case nn:return i([o,s.toISOString()],s);case rn:{const{source:c,flags:m}=s;return i([o,{source:c,flags:m}],s)}case an:{const c=[],m=i([o,c],s);for(const[g,p]of s)(e||!(dt(Ge(g))||dt(Ge(p))))&&c.push([a(g),a(p)]);return m}case ln:{const c=[],m=i([o,c],s);for(const g of s)(e||!dt(Ge(g)))&&c.push(a(g));return m}}const{message:u}=s;return i([o,{name:d,message:u}],s)};return a},qn=(e,{json:t,lossy:n}={})=>{const r=[];return to(!(t||n),!!t,new Map,r)(e),r},ft=typeof structuredClone=="function"?(e,t)=>t&&("json"in t||"lossy"in t)?Un(qn(e,t)):structuredClone(e):(e,t)=>Un(qn(e,t));function no(e,t){const n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function ro(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}function io(e){const t=typeof e.options.clobberPrefix=="string"?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||no,r=e.options.footnoteBackLabel||ro,i=e.options.footnoteLabel||"Footnotes",a=e.options.footnoteLabelTagName||"h2",s=e.options.footnoteLabelProperties||{className:["sr-only"]},o=[];let d=-1;for(;++d<e.footnoteOrder.length;){const u=e.footnoteById.get(e.footnoteOrder[d]);if(!u)continue;const c=e.all(u),m=String(u.identifier).toUpperCase(),g=Ve(m.toLowerCase());let p=0;const w=[],k=e.footnoteCounts.get(m);for(;k!==void 0&&++p<=k;){w.length>0&&w.push({type:"text",value:" "});let _=typeof n=="string"?n:n(d,p);typeof _=="string"&&(_={type:"text",value:_}),w.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+g+(p>1?"-"+p:""),dataFootnoteBackref:"",ariaLabel:typeof r=="string"?r:r(d,p),className:["data-footnote-backref"]},children:Array.isArray(_)?_:[_]})}const A=c[c.length-1];if(A&&A.type==="element"&&A.tagName==="p"){const _=A.children[A.children.length-1];_&&_.type==="text"?_.value+=" ":A.children.push({type:"text",value:" "}),A.children.push(...w)}else c.push(...w);const b={type:"element",tagName:"li",properties:{id:t+"fn-"+g},children:e.wrap(c,!0)};e.patch(u,b),o.push(b)}if(o.length!==0)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:a,properties:{...ft(s),id:"footnote-label"},children:[{type:"text",value:i}]},{type:"text",value:`
`},{type:"element",tagName:"ol",properties:{},children:e.wrap(o,!0)},{type:"text",value:`
`}]}}const Rr=function(e){if(e==null)return oo;if(typeof e=="function")return yt(e);if(typeof e=="object")return Array.isArray(e)?ao(e):lo(e);if(typeof e=="string")return so(e);throw new Error("Expected function, string, or object as test")};function ao(e){const t=[];let n=-1;for(;++n<e.length;)t[n]=Rr(e[n]);return yt(r);function r(...i){let a=-1;for(;++a<t.length;)if(t[a].apply(this,i))return!0;return!1}}function lo(e){const t=e;return yt(n);function n(r){const i=r;let a;for(a in e)if(i[a]!==t[a])return!1;return!0}}function so(e){return yt(t);function t(n){return n&&n.type===e}}function yt(e){return t;function t(n,r,i){return!!(uo(n)&&e.call(this,n,typeof r=="number"?r:void 0,i||void 0))}}function oo(){return!0}function uo(e){return e!==null&&typeof e=="object"&&"type"in e}const Fr=[],co=!0,Yn=!1,po="skip";function mo(e,t,n,r){let i;typeof t=="function"&&typeof n!="function"?(r=n,n=t):i=t;const a=Rr(i),s=r?-1:1;o(e,void 0,[])();function o(d,u,c){const m=d&&typeof d=="object"?d:{};if(typeof m.type=="string"){const p=typeof m.tagName=="string"?m.tagName:typeof m.name=="string"?m.name:void 0;Object.defineProperty(g,"name",{value:"node ("+(d.type+(p?"<"+p+">":""))+")"})}return g;function g(){let p=Fr,w,k,A;if((!t||a(d,u,c[c.length-1]||void 0))&&(p=ho(n(d,c)),p[0]===Yn))return p;if("children"in d&&d.children){const b=d;if(b.children&&p[0]!==po)for(k=(r?b.children.length:-1)+s,A=c.concat(b);k>-1&&k<b.children.length;){const _=b.children[k];if(w=o(_,k,A)(),w[0]===Yn)return w;k=typeof w[1]=="number"?w[1]:k+s}}return p}}}function ho(e){return Array.isArray(e)?e:typeof e=="number"?[co,e]:e==null?Fr:[e]}function Br(e,t,n,r){let i,a,s;typeof t=="function"?(a=void 0,s=t,i=n):(a=t,s=n,i=r),mo(e,a,o,i);function o(d,u){const c=u[u.length-1],m=c?c.children.indexOf(d):void 0;return s(d,m,c)}}const Ut={}.hasOwnProperty,fo={};function go(e,t){const n=t||fo,r=new Map,i=new Map,a=new Map,s={...Xs,...n.handlers},o={all:u,applyData:yo,definitionById:r,footnoteById:i,footnoteCounts:a,footnoteOrder:[],handlers:s,one:d,options:n,patch:xo,wrap:bo};return Br(e,function(c){if(c.type==="definition"||c.type==="footnoteDefinition"){const m=c.type==="definition"?r:i,g=String(c.identifier).toUpperCase();m.has(g)||m.set(g,c)}}),o;function d(c,m){const g=c.type,p=o.handlers[g];if(Ut.call(o.handlers,g)&&p)return p(o,c,m);if(o.options.passThrough&&o.options.passThrough.includes(g)){if("children"in c){const{children:k,...A}=c,b=ft(A);return b.children=o.all(c),b}return ft(c)}return(o.options.unknownHandler||ko)(o,c,m)}function u(c){const m=[];if("children"in c){const g=c.children;let p=-1;for(;++p<g.length;){const w=o.one(g[p],c);if(w){if(p&&g[p-1].type==="break"&&(!Array.isArray(w)&&w.type==="text"&&(w.value=Kn(w.value)),!Array.isArray(w)&&w.type==="element")){const k=w.children[0];k&&k.type==="text"&&(k.value=Kn(k.value))}Array.isArray(w)?m.push(...w):m.push(w)}}}return m}}function xo(e,t){e.position&&(t.position=Zi(e))}function yo(e,t){let n=t;if(e&&e.data){const r=e.data.hName,i=e.data.hChildren,a=e.data.hProperties;if(typeof r=="string")if(n.type==="element")n.tagName=r;else{const s="children"in n?n.children:[n];n={type:"element",tagName:r,properties:{},children:s}}n.type==="element"&&a&&Object.assign(n.properties,ft(a)),"children"in n&&n.children&&i!==null&&i!==void 0&&(n.children=i)}return n}function ko(e,t){const n=t.data||{},r="value"in t&&!(Ut.call(n,"hProperties")||Ut.call(n,"hChildren"))?{type:"text",value:t.value}:{type:"element",tagName:"div",properties:{},children:e.all(t)};return e.patch(t,r),e.applyData(t,r)}function bo(e,t){const n=[];let r=-1;for(t&&n.push({type:"text",value:`
`});++r<e.length;)r&&n.push({type:"text",value:`
`}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:`
`}),n}function Kn(e){let t=0,n=e.charCodeAt(t);for(;n===9||n===32;)t++,n=e.charCodeAt(t);return e.slice(t)}function Gn(e,t){const n=go(e,t),r=n.one(e,void 0),i=io(n),a=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return i&&a.children.push({type:"text",value:`
`},i),a}function wo(e,t){return e&&"run"in e?async function(n,r){const i=Gn(n,{file:r,...t});await e.run(i,r)}:function(n,r){return Gn(n,{file:r,...e||t})}}function Jn(e){if(e)throw e}var Et,Wn;function vo(){if(Wn)return Et;Wn=1;var e=Object.prototype.hasOwnProperty,t=Object.prototype.toString,n=Object.defineProperty,r=Object.getOwnPropertyDescriptor,i=function(u){return typeof Array.isArray=="function"?Array.isArray(u):t.call(u)==="[object Array]"},a=function(u){if(!u||t.call(u)!=="[object Object]")return!1;var c=e.call(u,"constructor"),m=u.constructor&&u.constructor.prototype&&e.call(u.constructor.prototype,"isPrototypeOf");if(u.constructor&&!c&&!m)return!1;var g;for(g in u);return typeof g=="undefined"||e.call(u,g)},s=function(u,c){n&&c.name==="__proto__"?n(u,c.name,{enumerable:!0,configurable:!0,value:c.newValue,writable:!0}):u[c.name]=c.newValue},o=function(u,c){if(c==="__proto__")if(e.call(u,c)){if(r)return r(u,c).value}else return;return u[c]};return Et=function d(){var u,c,m,g,p,w,k=arguments[0],A=1,b=arguments.length,_=!1;for(typeof k=="boolean"&&(_=k,k=arguments[1]||{},A=2),(k==null||typeof k!="object"&&typeof k!="function")&&(k={});A<b;++A)if(u=arguments[A],u!=null)for(c in u)m=o(k,c),g=o(u,c),k!==g&&(_&&g&&(a(g)||(p=i(g)))?(p?(p=!1,w=m&&i(m)?m:[]):w=m&&a(m)?m:{},s(k,{name:c,newValue:d(_,w,g)})):typeof g!="undefined"&&s(k,{name:c,newValue:g}));return k},Et}var jo=vo();const At=nr(jo);function qt(e){if(typeof e!="object"||e===null)return!1;const t=Object.getPrototypeOf(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}function No(){const e=[],t={run:n,use:r};return t;function n(...i){let a=-1;const s=i.pop();if(typeof s!="function")throw new TypeError("Expected function as last argument, not "+s);o(null,...i);function o(d,...u){const c=e[++a];let m=-1;if(d){s(d);return}for(;++m<i.length;)(u[m]===null||u[m]===void 0)&&(u[m]=i[m]);i=u,c?So(c,o)(...u):s(null,...u)}}function r(i){if(typeof i!="function")throw new TypeError("Expected `middelware` to be a function, not "+i);return e.push(i),t}}function So(e,t){let n;return r;function r(...s){const o=e.length>s.length;let d;o&&s.push(i);try{d=e.apply(this,s)}catch(u){const c=u;if(o&&n)throw c;return i(c)}o||(d&&d.then&&typeof d.then=="function"?d.then(a,i):d instanceof Error?i(d):a(d))}function i(s,...o){n||(n=!0,t(s,...o))}function a(s){i(null,s)}}const je={basename:Co,dirname:Eo,extname:Ao,join:Io,sep:"/"};function Co(e,t){if(t!==void 0&&typeof t!="string")throw new TypeError('"ext" argument must be a string');it(e);let n=0,r=-1,i=e.length,a;if(t===void 0||t.length===0||t.length>e.length){for(;i--;)if(e.codePointAt(i)===47){if(a){n=i+1;break}}else r<0&&(a=!0,r=i+1);return r<0?"":e.slice(n,r)}if(t===e)return"";let s=-1,o=t.length-1;for(;i--;)if(e.codePointAt(i)===47){if(a){n=i+1;break}}else s<0&&(a=!0,s=i+1),o>-1&&(e.codePointAt(i)===t.codePointAt(o--)?o<0&&(r=i):(o=-1,r=s));return n===r?r=s:r<0&&(r=e.length),e.slice(n,r)}function Eo(e){if(it(e),e.length===0)return".";let t=-1,n=e.length,r;for(;--n;)if(e.codePointAt(n)===47){if(r){t=n;break}}else r||(r=!0);return t<0?e.codePointAt(0)===47?"/":".":t===1&&e.codePointAt(0)===47?"//":e.slice(0,t)}function Ao(e){it(e);let t=e.length,n=-1,r=0,i=-1,a=0,s;for(;t--;){const o=e.codePointAt(t);if(o===47){if(s){r=t+1;break}continue}n<0&&(s=!0,n=t+1),o===46?i<0?i=t:a!==1&&(a=1):i>-1&&(a=-1)}return i<0||n<0||a===0||a===1&&i===n-1&&i===r+1?"":e.slice(i,n)}function Io(...e){let t=-1,n;for(;++t<e.length;)it(e[t]),e[t]&&(n=n===void 0?e[t]:n+"/"+e[t]);return n===void 0?".":zo(n)}function zo(e){it(e);const t=e.codePointAt(0)===47;let n=To(e,!t);return n.length===0&&!t&&(n="."),n.length>0&&e.codePointAt(e.length-1)===47&&(n+="/"),t?"/"+n:n}function To(e,t){let n="",r=0,i=-1,a=0,s=-1,o,d;for(;++s<=e.length;){if(s<e.length)o=e.codePointAt(s);else{if(o===47)break;o=47}if(o===47){if(!(i===s-1||a===1))if(i!==s-1&&a===2){if(n.length<2||r!==2||n.codePointAt(n.length-1)!==46||n.codePointAt(n.length-2)!==46){if(n.length>2){if(d=n.lastIndexOf("/"),d!==n.length-1){d<0?(n="",r=0):(n=n.slice(0,d),r=n.length-1-n.lastIndexOf("/")),i=s,a=0;continue}}else if(n.length>0){n="",r=0,i=s,a=0;continue}}t&&(n=n.length>0?n+"/..":"..",r=2)}else n.length>0?n+="/"+e.slice(i+1,s):n=e.slice(i+1,s),r=s-i-1;i=s,a=0}else o===46&&a>-1?a++:a=-1}return n}function it(e){if(typeof e!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}const _o={cwd:Po};function Po(){return"/"}function Yt(e){return!!(e!==null&&typeof e=="object"&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&e.auth===void 0)}function Do(e){if(typeof e=="string")e=new URL(e);else if(!Yt(e)){const t=new TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw t.code="ERR_INVALID_ARG_TYPE",t}if(e.protocol!=="file:"){const t=new TypeError("The URL must be of scheme file");throw t.code="ERR_INVALID_URL_SCHEME",t}return Mo(e)}function Mo(e){if(e.hostname!==""){const r=new TypeError('File URL host must be "localhost" or empty on darwin');throw r.code="ERR_INVALID_FILE_URL_HOST",r}const t=e.pathname;let n=-1;for(;++n<t.length;)if(t.codePointAt(n)===37&&t.codePointAt(n+1)===50){const r=t.codePointAt(n+2);if(r===70||r===102){const i=new TypeError("File URL path must not include encoded / characters");throw i.code="ERR_INVALID_FILE_URL_PATH",i}}return decodeURIComponent(t)}const It=["history","path","basename","stem","extname","dirname"];class Hr{constructor(t){let n;t?Yt(t)?n={path:t}:typeof t=="string"||Lo(t)?n={value:t}:n=t:n={},this.cwd="cwd"in n?"":_o.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let r=-1;for(;++r<It.length;){const a=It[r];a in n&&n[a]!==void 0&&n[a]!==null&&(this[a]=a==="history"?[...n[a]]:n[a])}let i;for(i in n)It.includes(i)||(this[i]=n[i])}get basename(){return typeof this.path=="string"?je.basename(this.path):void 0}set basename(t){Tt(t,"basename"),zt(t,"basename"),this.path=je.join(this.dirname||"",t)}get dirname(){return typeof this.path=="string"?je.dirname(this.path):void 0}set dirname(t){Xn(this.basename,"dirname"),this.path=je.join(t||"",this.basename)}get extname(){return typeof this.path=="string"?je.extname(this.path):void 0}set extname(t){if(zt(t,"extname"),Xn(this.dirname,"extname"),t){if(t.codePointAt(0)!==46)throw new Error("`extname` must start with `.`");if(t.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=je.join(this.dirname,this.stem+(t||""))}get path(){return this.history[this.history.length-1]}set path(t){Yt(t)&&(t=Do(t)),Tt(t,"path"),this.path!==t&&this.history.push(t)}get stem(){return typeof this.path=="string"?je.basename(this.path,this.extname):void 0}set stem(t){Tt(t,"stem"),zt(t,"stem"),this.path=je.join(this.dirname||"",t+(this.extname||""))}fail(t,n,r){const i=this.message(t,n,r);throw i.fatal=!0,i}info(t,n,r){const i=this.message(t,n,r);return i.fatal=void 0,i}message(t,n,r){const i=new le(t,n,r);return this.path&&(i.name=this.path+":"+i.name,i.file=this.path),i.fatal=!1,this.messages.push(i),i}toString(t){return this.value===void 0?"":typeof this.value=="string"?this.value:new TextDecoder(t||void 0).decode(this.value)}}function zt(e,t){if(e&&e.includes(je.sep))throw new Error("`"+t+"` cannot be a path: did not expect `"+je.sep+"`")}function Tt(e,t){if(!e)throw new Error("`"+t+"` cannot be empty")}function Xn(e,t){if(!e)throw new Error("Setting `"+t+"` requires `path` to be set too")}function Lo(e){return!!(e&&typeof e=="object"&&"byteLength"in e&&"byteOffset"in e)}const Oo=function(e){const r=this.constructor.prototype,i=r[e],a=function(){return i.apply(a,arguments)};return Object.setPrototypeOf(a,r),a},Ro={}.hasOwnProperty;class sn extends Oo{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=No()}copy(){const t=new sn;let n=-1;for(;++n<this.attachers.length;){const r=this.attachers[n];t.use(...r)}return t.data(At(!0,{},this.namespace)),t}data(t,n){return typeof t=="string"?arguments.length===2?(Dt("data",this.frozen),this.namespace[t]=n,this):Ro.call(this.namespace,t)&&this.namespace[t]||void 0:t?(Dt("data",this.frozen),this.namespace=t,this):this.namespace}freeze(){if(this.frozen)return this;const t=this;for(;++this.freezeIndex<this.attachers.length;){const[n,...r]=this.attachers[this.freezeIndex];if(r[0]===!1)continue;r[0]===!0&&(r[0]=void 0);const i=n.call(t,...r);typeof i=="function"&&this.transformers.use(i)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(t){this.freeze();const n=pt(t),r=this.parser||this.Parser;return _t("parse",r),r(String(n),n)}process(t,n){const r=this;return this.freeze(),_t("process",this.parser||this.Parser),Pt("process",this.compiler||this.Compiler),n?i(void 0,n):new Promise(i);function i(a,s){const o=pt(t),d=r.parse(o);r.run(d,o,function(c,m,g){if(c||!m||!g)return u(c);const p=m,w=r.stringify(p,g);Ho(w)?g.value=w:g.result=w,u(c,g)});function u(c,m){c||!m?s(c):a?a(m):n(void 0,m)}}}processSync(t){let n=!1,r;return this.freeze(),_t("processSync",this.parser||this.Parser),Pt("processSync",this.compiler||this.Compiler),this.process(t,i),Zn("processSync","process",n),r;function i(a,s){n=!0,Jn(a),r=s}}run(t,n,r){Qn(t),this.freeze();const i=this.transformers;return!r&&typeof n=="function"&&(r=n,n=void 0),r?a(void 0,r):new Promise(a);function a(s,o){const d=pt(n);i.run(t,d,u);function u(c,m,g){const p=m||t;c?o(c):s?s(p):r(void 0,p,g)}}}runSync(t,n){let r=!1,i;return this.run(t,n,a),Zn("runSync","run",r),i;function a(s,o){Jn(s),i=o,r=!0}}stringify(t,n){this.freeze();const r=pt(n),i=this.compiler||this.Compiler;return Pt("stringify",i),Qn(t),i(t,r)}use(t,...n){const r=this.attachers,i=this.namespace;if(Dt("use",this.frozen),t!=null)if(typeof t=="function")d(t,n);else if(typeof t=="object")Array.isArray(t)?o(t):s(t);else throw new TypeError("Expected usable value, not `"+t+"`");return this;function a(u){if(typeof u=="function")d(u,[]);else if(typeof u=="object")if(Array.isArray(u)){const[c,...m]=u;d(c,m)}else s(u);else throw new TypeError("Expected usable value, not `"+u+"`")}function s(u){if(!("plugins"in u)&&!("settings"in u))throw new Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");o(u.plugins),u.settings&&(i.settings=At(!0,i.settings,u.settings))}function o(u){let c=-1;if(u!=null)if(Array.isArray(u))for(;++c<u.length;){const m=u[c];a(m)}else throw new TypeError("Expected a list of plugins, not `"+u+"`")}function d(u,c){let m=-1,g=-1;for(;++m<r.length;)if(r[m][0]===u){g=m;break}if(g===-1)r.push([u,...c]);else if(c.length>0){let[p,...w]=c;const k=r[g][1];qt(k)&&qt(p)&&(p=At(!0,k,p)),r[g]=[u,p,...w]}}}}const Fo=new sn().freeze();function _t(e,t){if(typeof t!="function")throw new TypeError("Cannot `"+e+"` without `parser`")}function Pt(e,t){if(typeof t!="function")throw new TypeError("Cannot `"+e+"` without `compiler`")}function Dt(e,t){if(t)throw new Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function Qn(e){if(!qt(e)||typeof e.type!="string")throw new TypeError("Expected node, got `"+e+"`")}function Zn(e,t,n){if(!n)throw new Error("`"+e+"` finished async. Use `"+t+"` instead")}function pt(e){return Bo(e)?e:new Hr(e)}function Bo(e){return!!(e&&typeof e=="object"&&"message"in e&&"messages"in e)}function Ho(e){return typeof e=="string"||Vo(e)}function Vo(e){return!!(e&&typeof e=="object"&&"byteLength"in e&&"byteOffset"in e)}const $o="https://github.com/remarkjs/react-markdown/blob/main/changelog.md",er=[],tr={allowDangerousHtml:!0},Uo=/^(https?|ircs?|mailto|xmpp)$/i,qo=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"className",id:"remove-classname"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function Yo(e){const t=Ko(e),n=Go(e);return Jo(t.runSync(t.parse(n),n),e)}function Ko(e){const t=e.rehypePlugins||er,n=e.remarkPlugins||er,r=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...tr}:tr;return Fo().use(Ss).use(n).use(wo,r).use(t)}function Go(e){const t=e.children||"",n=new Hr;return typeof t=="string"&&(n.value=t),n}function Jo(e,t){const n=t.allowedElements,r=t.allowElement,i=t.components,a=t.disallowedElements,s=t.skipHtml,o=t.unwrapDisallowed,d=t.urlTransform||Wo;for(const c of qo)Object.hasOwn(t,c.from)&&(""+c.from+(c.to?"use `"+c.to+"` instead":"remove it")+$o+c.id,void 0);return Br(e,u),ia(e,{Fragment:l.Fragment,components:i,ignoreInvalidStyle:!0,jsx:l.jsx,jsxs:l.jsxs,passKeys:!0,passNode:!0});function u(c,m,g){if(c.type==="raw"&&g&&typeof m=="number")return s?g.children.splice(m,1):g.children[m]={type:"text",value:c.value},m;if(c.type==="element"){let p;for(p in Nt)if(Object.hasOwn(Nt,p)&&Object.hasOwn(c.properties,p)){const w=c.properties[p],k=Nt[p];(k===null||k.includes(c.tagName))&&(c.properties[p]=d(String(w||""),p,c))}}if(c.type==="element"){let p=n?!n.includes(c.tagName):a?a.includes(c.tagName):!1;if(!p&&r&&typeof m=="number"&&(p=!r(c,m,g)),p&&g&&typeof m=="number")return o&&c.children?g.children.splice(m,1,...c.children):g.children.splice(m,1),m}}}function Wo(e){const t=e.indexOf(":"),n=e.indexOf("?"),r=e.indexOf("#"),i=e.indexOf("/");return t===-1||i!==-1&&t>i||n!==-1&&t>n||r!==-1&&t>r||Uo.test(e.slice(0,t))?e:""}const Je=[{id:"gpt-4-turbo",name:"GPT-4 Turbo (OpenAI)",provider:"openai"},{id:"gpt-4",name:"GPT-4 (OpenAI)",provider:"openai"},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo (OpenAI)",provider:"openai"},{id:"claude-3-5-sonnet-20240620",name:"Claude 3.5 Sonnet (Anthropic)",provider:"anthropic"},{id:"claude-3-haiku-20240307",name:"Claude 3 Haiku (Anthropic)",provider:"anthropic"},{id:"claude-3-sonnet-20240229",name:"Claude 3 Sonnet (Anthropic)",provider:"anthropic"},{id:"claude-3-opus-20240229",name:"Claude 3 Opus (Anthropic)",provider:"anthropic"},{id:"claude-3-7-sonnet-20250219",name:"Claude 3.7 Sonnet (Anthropic)",provider:"anthropic"}];function cu(){var e;const[t,n]=V.useState("chat"),[r,i]=V.useState(!1),[a,s]=V.useState([{id:"welcome",role:"system",content:"Merhaba! Ben Ajans Danışmanınız. Size nasıl yardımcı olabilirim?",timestamp:new Date}]),[o,d]=V.useState(""),[u,c]=V.useState(!1),[m,g]=V.useState(!1),[p,w]=V.useState("gpt-4"),[k,A]=V.useState({}),[b,_]=V.useState(!1),[C,H]=V.useState(null),[F,x]=V.useState(`Sen profesyonel bir canlı yayın ajansı danışmanısın. TikTok canlı yayıncıları ile çalışan bir ajansa stratejik danışmanlık yapıyorsun.

- Ajansın temel iş modeli: Yayıncıları temsil etmek, onlara destek olmak ve gelirlerini artırmak
- Elmas/Jeton: TikTok platformu içi sanal para birimi (NOT: Elmas ve jeton aynı şeyi ifade eder)
- Yayıncı: TikTok'ta canlı yayın yapan içerik üreticisi
- Ajans hedefi: Yayıncı portföyünü büyütmek ve her yayıncının kazancını optimize etmek

Cevaplarında profesyonel, bilgili ve stratejik ol. Pratik, uygulanabilir tavsiyeler ver. Her ay için minimum %100 gelir artışı hedefleyen stratejiler öner.

Veritabanımızda bulunan tablolar ve içerikleri şöyledir:
- users: Sistem kullanıcıları
- yayinci, publisher_info: Yayıncı bilgileri ve profilleri
- influencer_info: Influencer detayları
- performans: Yayıncıların performans metrikleri (elmas, takipçi, abonelik)
- weekly_archive, weekly_tasks: Haftalık görevler ve arşiv
- etkinlikler, etkinlik_katilimcilar: Düzenlenen etkinlikler ve katılımcılar
- pk_eslesmeleri: PK maçları ve eşleştirmeler
- gorevler, task_settings: Yayıncı görevleri ve ayarlar
- email_send_logs: E-posta gönderim kayıtları

Bu veritabanı bilgilerini kullanarak gerçekçi ve uygulanabilir stratejiler öner.`),[j,M]=V.useState(!1),[L,z]=V.useState(!1),{darkMode:D}=Vr(),{topPerformers:T=[],contentTypePerformance:P=[],optimalTimeSlots:I=[],currentMonthRevenue:E=0,previousMonthRevenue:G=0,targetRevenue:U=0,targetGrowth:re=0,loading:ie,error:h}=$r(),ae=V.useRef(null),pe=V.useRef(null);V.useEffect(()=>{f(),se()},[]);const f=()=>{const N=localStorage.getItem("ai_advisor_api_keys");if(N)try{const ke=JSON.parse(N);A(ke)}catch(ke){J.error("API anahtarları yüklenirken bir hata oluştu")}const q=localStorage.getItem("ai_advisor_model");q&&Je.some(ve=>ve.id===q)&&w(q);const ne=localStorage.getItem("ai_system_prompt");ne&&x(ne);const oe=localStorage.getItem("ai_web_search_enabled");oe&&M(JSON.parse(oe));const me=localStorage.getItem("ai_continuous_learning");me&&z(JSON.parse(me))};V.useEffect(()=>{Object.keys(k).length>0&&localStorage.setItem("ai_advisor_api_keys",JSON.stringify(k))},[k]);const se=()=>{const N={id:"system-prompt",role:"system",content:F+`

Bu ay gelir: $${E||0}
Geçen ay gelir: $${G||0}
Hedef gelir: $${U||0} (${re||0}% büyüme)`,timestamp:new Date};a.length===1&&s(q=>[...q,N])};V.useEffect(()=>{ye()},[E,G,U,re]);const ye=()=>{const N=localStorage.getItem("ai_db_schema");let q="";if(N)try{const me=JSON.parse(N);q=`

Veritabanı tabloları ve sütunları:
`,me.forEach(ke=>{q+=`- ${ke.tableName}: ${ke.description}
  Sütunlar: `;const ve=ke.columns.map(be=>`${be.name} (${be.type})`).join(", ");q+=ve+`
`})}catch(me){}const ne=F+`

Bu ay gelir: $${E||0}
Geçen ay gelir: $${G||0}
Hedef gelir: $${U||0} (${re||0}% büyüme)`+q,oe=a.map(me=>me.role==="system"?{...me,content:ne,timestamp:new Date}:me);s(oe)};V.useEffect(()=>{var N;(N=ae.current)==null||N.scrollIntoView({behavior:"smooth"})},[a]);const te=()=>{try{localStorage.setItem("ai_advisor_api_keys",JSON.stringify(k)),localStorage.setItem("ai_advisor_model",p),c(!1),H(null),J.success("API ayarları başarıyla kaydedildi")}catch(N){J.error("API anahtarları kaydedilirken bir hata oluştu")}},Ae=(N,q)=>{A(ne=>({...ne,[N]:q}))},we=N=>{w(N)},Ie=N=>{x(N)},ze=()=>{try{localStorage.setItem("ai_system_prompt",F),localStorage.setItem("ai_web_search_enabled",JSON.stringify(j)),localStorage.setItem("ai_continuous_learning",JSON.stringify(L)),J.success("Gelişmiş ayarlar kaydedildi"),g(!1),ye()}catch(N){J.error("Ayarlar kaydedilirken bir hata oluştu")}},Me=N=>{try{const q=N.length>0?`Aşağıdaki bilgi kaynaklarından yararlanabilirsin:
`+N.map(oe=>oe.type==="note"?`- Not: ${oe.title}
  ${oe.content}`:`- Dosya: ${oe.title}`).join(`
`):"",ne=F+(q?`

`+q:"");x(ne),localStorage.setItem("ai_system_prompt",ne),ye(),J.success("Bilgi kaynakları entegre edildi")}catch(q){J.error("Bilgi kaynakları kaydedilirken bir hata oluştu")}},at=async()=>{if(o.trim()){i(!0),H(null);try{const N={id:`user-${Date.now()}`,role:"user",content:o,timestamp:new Date};s(Q=>[...Q,N]),d("");const q=a.filter(Q=>Q.role!=="system").map(Q=>({role:Q.role,content:Q.content})),ne=a.find(Q=>Q.role==="system"),oe=ne?{role:"system",content:ne.content}:{role:"system",content:F},me={role:"user",content:o},ke=[oe,...q,me];let ve="";const be=Je.find(Q=>Q.id===p);if((be==null?void 0:be.provider)==="openai"?ve=k.openai||"":(be==null?void 0:be.provider)==="anthropic"&&(ve=k.anthropic||""),!ve)throw new Error(`${(be==null?void 0:be.provider)==="openai"?"OpenAI":"Anthropic"} API anahtarı eksik. Lütfen ayarlardan ekleyin.`);const O=await fetch("https://x.tuberajans.com/backend/x-site/ai-advisor.php?endpoint=ai",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({messages:ke,model:p,apiKey:ve,webSearchEnabled:j,continuousLearningEnabled:L})});if(!O.ok)throw new Error(`API hatası: ${O.status} ${O.statusText}`);const $=await O.json(),K={id:`assistant-${Date.now()}`,role:"assistant",content:$.response||"Üzgünüm, bir cevap oluşturulamadı.",timestamp:new Date};s(Q=>[...Q,K]),L&&kt([...a,N,K])}catch(N){H("API isteği sırasında bir hata oluştu. Lütfen daha sonra tekrar deneyin."),J.error(N.message||"API hatası oluştu")}finally{i(!1),pe.current&&pe.current.focus(),setTimeout(()=>{ae.current&&ae.current.scrollIntoView({behavior:"smooth"})},100)}}},kt=async N=>{try{(await fetch("/api/save-conversation.php",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({messages:N.map(ne=>({role:ne.role,content:ne.content,timestamp:ne.timestamp}))})})).ok}catch(q){}},bt=N=>{N.key==="Enter"&&!N.shiftKey&&(N.preventDefault(),at())},lt=()=>{if(window.confirm("Konuşma geçmişini temizlemek istediğinize emin misiniz?")){const N=a.find(q=>q.id==="welcome");N&&(s([N]),se())}},st=()=>{g(!0);const N=localStorage.getItem("ai_db_schema");if(N)try{const q=JSON.parse(N);J.success("Veritabanı şeması yüklendi"),g(!0)}catch(q){J.error("Veritabanı şeması yüklenirken bir hata oluştu")}else J.success("Henüz bir veritabanı şeması kaydedilmemiş. Lütfen gelişmiş ayarlardan ekleyin."),g(!0)};return l.jsxs("div",{className:"space-y-6 h-full min-h-0",children:[l.jsxs("div",{className:"flex items-center justify-between border-b dark:border-gray-700",children:[l.jsxs("div",{className:"flex space-x-4",children:[l.jsx(Mt,{active:t==="chat",onClick:()=>n("chat"),icon:l.jsx(di,{}),label:"AI Danışman"}),l.jsx(Mt,{active:t==="insights",onClick:()=>n("insights"),icon:l.jsx(ot,{}),label:"İçgörüler"}),l.jsx(Mt,{active:t==="revenue",onClick:()=>n("revenue"),icon:l.jsx(mn,{}),label:"Gelir Stratejileri"})]}),l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsxs("div",{className:"flex items-center px-3 py-1 bg-indigo-50 dark:bg-indigo-900 dark:bg-opacity-30 rounded-lg text-sm text-indigo-700 dark:text-indigo-300",children:[l.jsx(xi,{size:14,className:"mr-2"}),l.jsx("span",{children:((e=Je.find(N=>N.id===p))==null?void 0:e.name)||"Model Seçilmedi"})]}),l.jsx("button",{onClick:()=>c(!u),className:"p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors",title:"Model Ayarları",children:l.jsx(fi,{size:20})}),l.jsx("button",{onClick:()=>g(!0),className:"p-2 text-indigo-600 dark:text-indigo-400 hover:bg-indigo-50 dark:hover:bg-indigo-900 dark:hover:bg-opacity-30 rounded-full transition-colors",title:"Gelişmiş Ayarlar",children:l.jsx(Lt,{size:20})}),l.jsx("button",{onClick:st,className:"p-2 text-purple-600 dark:text-purple-400 hover:bg-purple-50 dark:hover:bg-purple-900 dark:hover:bg-opacity-30 rounded-full transition-colors",title:"Veritabanı Şeması",children:l.jsx(Xe,{size:20})})]})]}),C&&l.jsxs("div",{className:"bg-red-50 dark:bg-red-900 dark:bg-opacity-20 border-l-4 border-red-500 p-4 flex items-start",children:[l.jsx(ji,{className:"text-red-500 mr-3 flex-shrink-0 mt-0.5",size:18}),l.jsx("p",{className:"text-red-700 dark:text-red-300 text-sm",children:C})]}),(j||L)&&l.jsx("div",{className:"bg-indigo-50 dark:bg-indigo-900 dark:bg-opacity-20 border-l-4 border-indigo-500 p-3 flex items-start",children:l.jsxs("div",{className:"text-indigo-700 dark:text-indigo-300 text-sm flex-1",children:[l.jsx("div",{className:"font-medium mb-1",children:"Etkin Yetenekler:"}),l.jsxs("div",{className:"flex flex-wrap gap-2",children:[j&&l.jsxs("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-indigo-100 dark:bg-indigo-800 text-indigo-800 dark:text-indigo-200",children:[l.jsx(lr,{className:"w-3 h-3 mr-1"})," Web Arama"]}),L&&l.jsxs("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 dark:bg-purple-800 text-purple-800 dark:text-purple-200",children:[l.jsx(Lt,{className:"w-3 h-3 mr-1"})," Sürekli Öğrenme"]})]})]})}),u&&l.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 border dark:border-gray-700",children:[l.jsx("h3",{className:"text-lg font-bold mb-4 dark:text-white",children:"AI Danışman Ayarları"}),l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium mb-1 dark:text-gray-300",children:"AI Modeli"}),l.jsxs("select",{value:p,onChange:N=>we(N.target.value),className:"w-full p-2 border rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[l.jsx("optgroup",{label:"OpenAI Modelleri",children:Je.filter(N=>N.provider==="openai").map(N=>l.jsx("option",{value:N.id,children:N.name},N.id))}),l.jsx("optgroup",{label:"Anthropic Modelleri",children:Je.filter(N=>N.provider==="anthropic").map(N=>l.jsx("option",{value:N.id,children:N.name},N.id))})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium mb-1 dark:text-gray-300",children:"OpenAI API Anahtarı"}),l.jsxs("div",{className:"relative",children:[l.jsx("input",{type:b?"text":"password",value:k.openai||"",onChange:N=>Ae("openai",N.target.value),placeholder:"OpenAI API anahtarınızı girin",className:"w-full p-2 border rounded-lg pr-10 dark:bg-gray-700 dark:border-gray-600 dark:text-white"}),l.jsx("button",{type:"button",onClick:()=>_(!b),className:"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400",children:b?l.jsx(hn,{size:18}):l.jsx(dn,{size:18})})]}),l.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"OpenAI modelleri için gerekli"})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium mb-1 dark:text-gray-300",children:"Anthropic API Anahtarı"}),l.jsxs("div",{className:"relative",children:[l.jsx("input",{type:b?"text":"password",value:k.anthropic||"",onChange:N=>Ae("anthropic",N.target.value),placeholder:"Anthropic API anahtarınızı girin",className:"w-full p-2 border rounded-lg pr-10 dark:bg-gray-700 dark:border-gray-600 dark:text-white"}),l.jsx("button",{type:"button",onClick:()=>_(!b),className:"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400",children:b?l.jsx(hn,{size:18}):l.jsx(dn,{size:18})})]}),l.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Claude modelleri için gerekli"})]}),l.jsx("div",{className:"flex justify-end",children:l.jsxs("button",{onClick:te,className:"px-4 py-2 bg-indigo-600 text-white rounded-lg flex items-center hover:bg-indigo-700 transition-colors",children:[l.jsx(tt,{size:16,className:"mr-2"}),"Kaydet"]})})]})]}),m&&l.jsx(Ai,{isOpen:m,onClose:()=>g(!1),systemPrompt:F,onSystemPromptChange:Ie,onSaveSettings:ze,onSaveKnowledgeSources:Me}),l.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow flex flex-col h-[calc(100vh-180px)] min-h-0 overflow-hidden",children:[t==="chat"&&l.jsxs("div",{className:"flex flex-col h-[calc(100vh-180px)] min-h-0 overflow-hidden",children:[l.jsxs("div",{className:"p-4 border-b dark:border-gray-700 flex justify-between items-center",children:[l.jsx("h2",{className:"text-lg font-semibold dark:text-white",children:"Ajans Stratejik Danışmanı"}),l.jsx("button",{onClick:lt,className:"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 text-sm",children:"Konuşmayı Temizle"})]}),l.jsxs("div",{className:"flex-1 min-h-0 overflow-y-auto p-4 space-y-4 dark:text-gray-200",children:[(a||[]).filter(N=>N.role!=="system").map(N=>l.jsx("div",{className:`flex ${N.role==="user"?"justify-end":"justify-start"}`,children:l.jsxs("div",{className:`max-w-3/4 rounded-lg p-3 ${N.role==="user"?"bg-indigo-100 dark:bg-indigo-900 dark:bg-opacity-50 text-gray-800 dark:text-gray-100":"bg-white dark:bg-gray-700 border dark:border-gray-600 text-gray-800 dark:text-gray-100"}`,children:[l.jsxs("div",{className:"flex items-center mb-1",children:[l.jsx("div",{className:`w-6 h-6 rounded-full flex items-center justify-center mr-2 ${N.role==="user"?"bg-indigo-500":"bg-gray-200 dark:bg-gray-600"}`,children:N.role==="user"?l.jsx(gn,{size:14,className:"text-white"}):l.jsx(Wr,{size:14,className:"text-gray-700 dark:text-gray-200"})}),l.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:Gr(N.timestamp,"HH:mm",{locale:Ur})})]}),l.jsx("div",{className:"whitespace-pre-wrap",children:N.role==="assistant"?l.jsx(Yo,{children:N.content}):N.content})]})},N.id)),l.jsx("div",{ref:ae})]}),l.jsxs("div",{className:"p-4 border-t dark:border-gray-700",children:[l.jsxs("div",{className:"relative",children:[l.jsx("textarea",{ref:pe,value:o,onChange:N=>d(N.target.value),onKeyDown:bt,placeholder:"Ajans stratejileri hakkında bir soru sorun...",className:"w-full p-3 pr-10 border dark:border-gray-600 rounded-lg resize-none dark:bg-gray-700 dark:text-white",rows:2}),l.jsx("button",{onClick:at,disabled:r||!o.trim(),className:"absolute right-3 bottom-3 text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300 disabled:text-gray-400 dark:disabled:text-gray-500",children:r?l.jsx(rr,{size:20,className:"animate-spin"}):l.jsx(mi,{size:20})})]}),l.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Enter tuşu ile gönder, yeni satır için Shift+Enter"})]})]}),t==="insights"&&l.jsxs("div",{className:"p-6 space-y-6",children:[l.jsx("h2",{className:"text-xl font-bold dark:text-white",children:"Yayıncı ve Performans İçgörüleri"}),ie&&l.jsx("div",{className:"flex items-center justify-center p-12",children:l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500"}),l.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"Veriler yükleniyor..."})]})}),h&&!ie&&l.jsx("div",{className:"bg-red-50 dark:bg-red-900 dark:bg-opacity-20 border-l-4 border-red-500 p-4",children:l.jsxs("div",{className:"flex",children:[l.jsx("div",{className:"flex-shrink-0",children:l.jsx("svg",{className:"h-5 w-5 text-red-500",viewBox:"0 0 20 20",fill:"currentColor",children:l.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),l.jsx("div",{className:"ml-3",children:l.jsx("p",{className:"text-sm text-red-700 dark:text-red-300",children:h})})]})}),!ie&&!h&&l.jsxs(l.Fragment,{children:[l.jsxs("div",{className:"bg-white dark:bg-gray-750 p-4 rounded-lg border dark:border-gray-700 shadow-sm mb-6",children:[l.jsxs("div",{className:"flex items-center justify-between mb-4",children:[l.jsxs("div",{children:[l.jsx("h3",{className:"text-lg font-bold text-gray-800 dark:text-white",children:"Bu Ay Gelir Hedefi"}),l.jsxs("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Şu anki: $",(E==null?void 0:E.toLocaleString())||"0"," / Hedef: $",(U==null?void 0:U.toLocaleString())||"0"]})]}),l.jsx("div",{className:"h-16 w-16 rounded-full bg-indigo-100 dark:bg-indigo-900 flex items-center justify-center",children:l.jsx(wi,{size:32,className:"text-indigo-600 dark:text-indigo-400"})})]}),l.jsxs("div",{className:"mb-2",children:[l.jsx("div",{className:"flex justify-between mb-1",children:l.jsxs("span",{className:"text-sm font-medium dark:text-gray-300",children:["İlerleme (",Math.round((E||0)/(U||1)*100),"%)"]})}),l.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5",children:l.jsx("div",{className:"bg-indigo-600 h-2.5 rounded-full",style:{width:`${Math.min(100,Math.round((E||0)/(U||1)*100))}%`}})})]}),l.jsxs("div",{className:"flex space-x-4 text-center mt-4",children:[l.jsxs("div",{className:"flex-1 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[l.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Geçen Ay"}),l.jsxs("p",{className:"text-xl font-bold text-gray-800 dark:text-white",children:["$",(G==null?void 0:G.toLocaleString())||"0"]})]}),l.jsxs("div",{className:"flex-1 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[l.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Bu Ay (şu ana kadar)"}),l.jsxs("p",{className:"text-xl font-bold text-gray-800 dark:text-white",children:["$",(E==null?void 0:E.toLocaleString())||"0"]})]}),l.jsxs("div",{className:"flex-1 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[l.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Hedeflenen Büyüme"}),l.jsxs("p",{className:"text-xl font-bold text-green-600",children:["+",re||0,"%"]})]})]})]}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[l.jsxs("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg border dark:border-gray-700 shadow-sm",children:[l.jsxs("div",{className:"flex items-center mb-4",children:[l.jsx(cn,{className:"text-green-500 mr-2",size:20}),l.jsx("h3",{className:"text-lg font-semibold dark:text-white",children:"En Yüksek Performanslı Yayıncılar"})]}),l.jsx("div",{className:"space-y-3",children:(T||[]).map((N,q)=>l.jsxs("div",{className:"flex justify-between items-center p-2 bg-gray-50 dark:bg-gray-700 rounded",children:[l.jsx("span",{className:"font-medium dark:text-white",children:N.username}),l.jsxs("div",{className:"flex items-center",children:[l.jsxs("span",{className:"text-green-600 dark:text-green-400 text-sm mr-2",children:["+",N.growth||0,"%"]}),l.jsxs("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:[((N.diamonds||0)/1e3).toFixed(1),"K Elmas"]})]})]},q))})]}),l.jsxs("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg border dark:border-gray-700 shadow-sm",children:[l.jsxs("div",{className:"flex items-center mb-4",children:[l.jsx(ot,{className:"text-blue-500 mr-2",size:20}),l.jsx("h3",{className:"text-lg font-semibold dark:text-white",children:"İçerik Türü Başarı Analizi"})]}),l.jsx("div",{className:"space-y-3",children:(P||[]).map((N,q)=>l.jsxs("div",{className:"mb-2",children:[l.jsxs("div",{className:"flex justify-between mb-1",children:[l.jsx("span",{className:"text-sm font-medium dark:text-gray-300",children:N.type}),l.jsxs("span",{className:"text-sm font-medium dark:text-gray-300",children:[N.performance||0,"%"]})]}),l.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:l.jsx("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${N.performance||0}%`}})})]},q))})]}),l.jsxs("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg border dark:border-gray-700 shadow-sm",children:[l.jsxs("div",{className:"flex items-center mb-4",children:[l.jsx(ot,{className:"text-purple-500 mr-2",size:20}),l.jsx("h3",{className:"text-lg font-semibold dark:text-white",children:"Optimal Yayın Saatleri"})]}),l.jsxs("div",{className:"space-y-2",children:[(I||[]).map((N,q)=>l.jsxs("div",{className:"flex justify-between items-center",children:[l.jsx("span",{className:"font-medium dark:text-white",children:N.activity}),l.jsx("span",{className:"text-purple-600 dark:text-purple-400",children:N.timeSlot})]},q)),l.jsx("div",{className:"mt-4 p-3 bg-purple-50 dark:bg-purple-900 dark:bg-opacity-20 rounded-lg",children:l.jsxs("p",{className:"text-sm text-purple-800 dark:text-purple-300",children:[l.jsx("strong",{children:"Öneri:"})," Yayıncılarınızın programlarını 20:00 - 23:00 arasında yoğunlaştırın. Bu saatler arası etkileşim ve kazanç optimizasyonu için ideal zaman dilimi."]})})]})]}),l.jsxs("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg border dark:border-gray-700 shadow-sm",children:[l.jsxs("div",{className:"flex items-center mb-4",children:[l.jsx(gn,{className:"text-indigo-500 mr-2",size:20}),l.jsx("h3",{className:"text-lg font-semibold dark:text-white",children:"Hedef Demografiler"})]}),l.jsxs("div",{className:"space-y-3",children:[l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsx("span",{className:"font-medium dark:text-white",children:"Yaş Grubu"}),l.jsx("span",{className:"bg-indigo-100 dark:bg-indigo-900 dark:bg-opacity-50 text-indigo-800 dark:text-indigo-300 px-2 py-1 rounded text-sm",children:"18-24 (%42)"})]}),l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsx("span",{className:"font-medium dark:text-white",children:"Coğrafya"}),l.jsx("span",{className:"bg-indigo-100 dark:bg-indigo-900 dark:bg-opacity-50 text-indigo-800 dark:text-indigo-300 px-2 py-1 rounded text-sm",children:"İstanbul, Ankara, İzmir"})]}),l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsx("span",{className:"font-medium dark:text-white",children:"İlgi Alanları"}),l.jsx("span",{className:"bg-indigo-100 dark:bg-indigo-900 dark:bg-opacity-50 text-indigo-800 dark:text-indigo-300 px-2 py-1 rounded text-sm",children:"Müzik, Gaming, Lifestyle"})]}),l.jsx("div",{className:"mt-4 p-3 bg-indigo-50 dark:bg-indigo-900 dark:bg-opacity-20 rounded-lg",children:l.jsxs("p",{className:"text-sm text-indigo-800 dark:text-indigo-300",children:[l.jsx("strong",{children:"Strateji:"})," 18-24 yaş arası genç kitlelere yönelik eğlence ve müzik içeriklerini artırın. İstanbul merkezli kampanyalar daha yüksek ROI sağlayabilir."]})})]})]})]}),l.jsxs("div",{className:"mt-6 p-4 bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900 dark:from-opacity-20 dark:to-purple-900 dark:to-opacity-20 rounded-lg border border-indigo-100 dark:border-indigo-800",children:[l.jsx("h3",{className:"text-lg font-bold mb-2 text-indigo-700 dark:text-indigo-300",children:"AI Danışman Önerisi"}),l.jsxs("p",{className:"text-sm text-indigo-900 dark:text-indigo-200",children:[l.jsx("strong",{children:"İçgörü:"})," Verilerinize göre müzik içeriği üreten yayıncıların ortalama izleyici etkileşimi %28 daha yüksek ve elmas dönüşüm oranları %35 daha fazla. Akşam 20:00-23:00 arası prime-time diliminde yayın yapan yayıncılar ortalama %42 daha fazla kazanç elde ediyor."]}),l.jsxs("div",{className:"mt-3 space-y-2",children:[l.jsx("p",{className:"text-sm font-medium text-indigo-800 dark:text-indigo-300",children:"Önerilen Aksiyonlar:"}),l.jsxs("ul",{className:"list-disc list-inside text-sm text-indigo-900 dark:text-indigo-200 space-y-1",children:[l.jsx("li",{children:"Müzik ve interaktif içerik üreten yayıncı havuzunuzu genişletin"}),l.jsx("li",{children:"Prime-time yayın saatlerinde slot'larınızı en güçlü yayıncılarınıza tahsis edin"}),l.jsx("li",{children:"İzleyici demografisine uygun, etkileşimi yüksek içerik formatları için şablonlar oluşturun"}),l.jsx("li",{children:"18-24 yaş grubu hedefli özel kampanyalar planlayın"})]})]})]})]})]}),t==="revenue"&&l.jsxs("div",{className:"p-6 space-y-6",children:[l.jsx("h2",{className:"text-xl font-bold dark:text-white",children:"Gelir Artırma Stratejileri"}),ie&&l.jsx("div",{className:"flex items-center justify-center p-12",children:l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500"}),l.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"Veriler yükleniyor..."})]})}),h&&!ie&&l.jsx("div",{className:"bg-red-50 dark:bg-red-900 dark:bg-opacity-20 border-l-4 border-red-500 p-4",children:l.jsxs("div",{className:"flex",children:[l.jsx("div",{className:"flex-shrink-0",children:l.jsx("svg",{className:"h-5 w-5 text-red-500",viewBox:"0 0 20 20",fill:"currentColor",children:l.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),l.jsx("div",{className:"ml-3",children:l.jsx("p",{className:"text-sm text-red-700 dark:text-red-300",children:h})})]})}),!ie&&!h&&l.jsxs(l.Fragment,{children:[l.jsxs("div",{className:"bg-white dark:bg-gray-800 border dark:border-gray-700 rounded-lg shadow p-4 mb-6",children:[l.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between mb-4",children:[l.jsxs("div",{children:[l.jsx("h3",{className:"text-lg font-bold text-gray-800 dark:text-white",children:"Gelir Optimizasyonu"}),l.jsxs("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Hedef: $",(G==null?void 0:G.toLocaleString())||"0"," → $",(U==null?void 0:U.toLocaleString())||"0"," (",re||0,"% artış)"]})]}),l.jsxs("div",{className:"mt-4 md:mt-0 bg-green-100 dark:bg-green-900 dark:bg-opacity-30 text-green-800 dark:text-green-300 px-4 py-2 rounded-full text-sm font-medium",children:["Potansiyel kazanç artışı: $",((U||0)-(E||0)).toLocaleString()]})]}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[l.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 p-3 rounded-lg",children:[l.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Geçen Ay"}),l.jsxs("p",{className:"text-xl font-bold text-gray-800 dark:text-white",children:["$",(G==null?void 0:G.toLocaleString())||"0"]})]}),l.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 p-3 rounded-lg",children:[l.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Şu Anki Durum"}),l.jsxs("p",{className:"text-xl font-bold text-indigo-600 dark:text-indigo-400",children:["$",(E==null?void 0:E.toLocaleString())||"0"]})]}),l.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 p-3 rounded-lg",children:[l.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Hedef"}),l.jsxs("p",{className:"text-xl font-bold text-green-600 dark:text-green-400",children:["$",(U==null?void 0:U.toLocaleString())||"0"]})]})]}),l.jsxs("div",{className:"mb-2",children:[l.jsx("div",{className:"flex justify-between mb-1",children:l.jsxs("span",{className:"text-sm font-medium dark:text-gray-300",children:["İlerleme Durumu (",Math.round((E||0)/(U||1)*100),"%)"]})}),l.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5",children:l.jsx("div",{className:"bg-indigo-600 h-2.5 rounded-full",style:{width:`${Math.min(100,Math.round((E||0)/(U||1)*100))}%`}})})]})]}),l.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[l.jsxs("div",{className:"bg-white dark:bg-gray-800 p-5 rounded-lg border dark:border-gray-700 shadow-sm",children:[l.jsxs("div",{className:"flex items-center mb-4",children:[l.jsx(mn,{className:"text-green-500 mr-2",size:24}),l.jsx("h3",{className:"text-lg font-semibold dark:text-white",children:"Kısa Vadeli Stratejiler"})]}),l.jsxs("div",{className:"space-y-3",children:[l.jsxs("div",{className:"p-3 bg-green-50 dark:bg-green-900 dark:bg-opacity-20 rounded-lg",children:[l.jsx("h4",{className:"font-medium text-green-800 dark:text-green-300 mb-1",children:"Yayıncı Hedef Programı"}),l.jsx("p",{className:"text-sm text-green-700 dark:text-green-300",children:"Her yayıncı için haftalık minimum yayın saati ve elmas hedefleri belirleyin. Hedefi aşanlara %5 bonus teklif edin."}),l.jsxs("div",{className:"mt-2 flex justify-between text-sm",children:[l.jsx("span",{className:"text-green-600 dark:text-green-400 font-medium",children:"Tahmini Artış"}),l.jsx("span",{className:"text-green-600 dark:text-green-400 font-medium",children:"+15-20%"})]})]}),l.jsxs("div",{className:"p-3 bg-green-50 dark:bg-green-900 dark:bg-opacity-20 rounded-lg",children:[l.jsx("h4",{className:"font-medium text-green-800 dark:text-green-300 mb-1",children:"Haftalık Challenge Etkinlikleri"}),l.jsx("p",{className:"text-sm text-green-700 dark:text-green-300",children:"Her hafta belirli bir temada challenge etkinlikleri düzenleyerek izleyici etkileşimini artırın."}),l.jsxs("div",{className:"mt-2 flex justify-between text-sm",children:[l.jsx("span",{className:"text-green-600 dark:text-green-400 font-medium",children:"Tahmini Artış"}),l.jsx("span",{className:"text-green-600 dark:text-green-400 font-medium",children:"+10-15%"})]})]}),l.jsxs("div",{className:"p-3 bg-green-50 dark:bg-green-900 dark:bg-opacity-20 rounded-lg",children:[l.jsx("h4",{className:"font-medium text-green-800 dark:text-green-300 mb-1",children:"Cross-Promotion Stratejisi"}),l.jsx("p",{className:"text-sm text-green-700 dark:text-green-300",children:"Benzer izleyici kitlesine sahip yayıncılar arasında çapraz tanıtım programı başlatın."}),l.jsxs("div",{className:"mt-2 flex justify-between text-sm",children:[l.jsx("span",{className:"text-green-600 dark:text-green-400 font-medium",children:"Tahmini Artış"}),l.jsx("span",{className:"text-green-600 dark:text-green-400 font-medium",children:"+8-12%"})]})]})]})]}),l.jsxs("div",{className:"bg-white dark:bg-gray-800 p-5 rounded-lg border dark:border-gray-700 shadow-sm",children:[l.jsxs("div",{className:"flex items-center mb-4",children:[l.jsx(cn,{className:"text-blue-500 mr-2",size:24}),l.jsx("h3",{className:"text-lg font-semibold dark:text-white",children:"Orta Vadeli Stratejiler"})]}),l.jsxs("div",{className:"space-y-3",children:[l.jsxs("div",{className:"p-3 bg-blue-50 dark:bg-blue-900 dark:bg-opacity-20 rounded-lg",children:[l.jsx("h4",{className:"font-medium text-blue-800 dark:text-blue-300 mb-1",children:"Yayıncı Akademisi"}),l.jsx("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:"Yayıncılarınız için düzenli eğitim ve mentorluk programı başlatın. Etkileşimi artırma, topluluk yönetimi ve içerik stratejisi eğitimleri verin."}),l.jsxs("div",{className:"mt-2 flex justify-between text-sm",children:[l.jsx("span",{className:"text-blue-600 dark:text-blue-400 font-medium",children:"Tahmini Artış"}),l.jsx("span",{className:"text-blue-600 dark:text-blue-400 font-medium",children:"+25-30%"})]})]}),l.jsxs("div",{className:"p-3 bg-blue-50 dark:bg-blue-900 dark:bg-opacity-20 rounded-lg",children:[l.jsx("h4",{className:"font-medium text-blue-800 dark:text-blue-300 mb-1",children:"Özel İçerik Paketleri"}),l.jsx("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:"Premium izleyiciler için özel içerik ve ayrıcalıklar sunun. VIP yayınlar, özel etkinlikler ve kişiselleştirilmiş etkileşimler."}),l.jsxs("div",{className:"mt-2 flex justify-between text-sm",children:[l.jsx("span",{className:"text-blue-600 dark:text-blue-400 font-medium",children:"Tahmini Artış"}),l.jsx("span",{className:"text-blue-600 dark:text-blue-400 font-medium",children:"+20-25%"})]})]}),l.jsxs("div",{className:"p-3 bg-blue-50 dark:bg-blue-900 dark:bg-opacity-20 rounded-lg",children:[l.jsx("h4",{className:"font-medium text-blue-800 dark:text-blue-300 mb-1",children:"Marka İşbirlikleri"}),l.jsx("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:"Yayıncılarınız için özel marka işbirliği fırsatları geliştirin. Sponsorlu yayınlar ve ürün yerleştirme ile ek gelir yaratın."}),l.jsxs("div",{className:"mt-2 flex justify-between text-sm",children:[l.jsx("span",{className:"text-blue-600 dark:text-blue-400 font-medium",children:"Tahmini Artış"}),l.jsx("span",{className:"text-blue-600 dark:text-blue-400 font-medium",children:"+30-40%"})]})]})]})]}),l.jsxs("div",{className:"bg-white dark:bg-gray-800 p-5 rounded-lg border dark:border-gray-700 shadow-sm",children:[l.jsxs("div",{className:"flex items-center mb-4",children:[l.jsx(ot,{className:"text-purple-500 mr-2",size:24}),l.jsx("h3",{className:"text-lg font-semibold dark:text-white",children:"Uzun Vadeli Stratejiler"})]}),l.jsxs("div",{className:"space-y-3",children:[l.jsxs("div",{className:"p-3 bg-purple-50 dark:bg-purple-900 dark:bg-opacity-20 rounded-lg",children:[l.jsx("h4",{className:"font-medium text-purple-800 dark:text-purple-300 mb-1",children:"Exclusive Yayıncı Ekosistemi"}),l.jsx("p",{className:"text-sm text-purple-700 dark:text-purple-300",children:"En iyi performans gösteren yayıncılarınız için kapsamlı destek ekosistemi oluşturun. Özel içerik ekibi, teknik destek ve kariyer mentorluğu sağlayın."}),l.jsxs("div",{className:"mt-2 flex justify-between text-sm",children:[l.jsx("span",{className:"text-purple-600 dark:text-purple-400 font-medium",children:"Tahmini Artış"}),l.jsx("span",{className:"text-purple-600 dark:text-purple-400 font-medium",children:"+50-60%"})]})]}),l.jsxs("div",{className:"p-3 bg-purple-50 dark:bg-purple-900 dark:bg-opacity-20 rounded-lg",children:[l.jsx("h4",{className:"font-medium text-purple-800 dark:text-purple-300 mb-1",children:"Çok Platformlu Strateji"}),l.jsx("p",{className:"text-sm text-purple-700 dark:text-purple-300",children:"Yayıncılarınızın içeriklerini TikTok dışında diğer platformlara da taşıyın. YouTube, Instagram ve Twitch ile entegre bir içerik stratejisi geliştirin."}),l.jsxs("div",{className:"mt-2 flex justify-between text-sm",children:[l.jsx("span",{className:"text-purple-600 dark:text-purple-400 font-medium",children:"Tahmini Artış"}),l.jsx("span",{className:"text-purple-600 dark:text-purple-400 font-medium",children:"+70-100%"})]})]}),l.jsxs("div",{className:"p-3 bg-purple-50 dark:bg-purple-900 dark:bg-opacity-20 rounded-lg",children:[l.jsx("h4",{className:"font-medium text-purple-800 dark:text-purple-300 mb-1",children:"Özgün IP ve Medya Prodüksiyonu"}),l.jsx("p",{className:"text-sm text-purple-700 dark:text-purple-300",children:"Yayıncılarınızla özgün IP'ler ve medya prodüksiyonları geliştirin. Diziler, yarışmalar ve format şovlar oluşturun."}),l.jsxs("div",{className:"mt-2 flex justify-between text-sm",children:[l.jsx("span",{className:"text-purple-600 dark:text-purple-400 font-medium",children:"Tahmini Artış"}),l.jsx("span",{className:"text-purple-600 dark:text-purple-400 font-medium",children:"+100-150%"})]})]})]})]})]}),l.jsxs("div",{className:"bg-white dark:bg-gray-800 p-5 rounded-lg border dark:border-gray-700 shadow-sm",children:[l.jsx("h3",{className:"text-lg font-semibold mb-4 dark:text-white",children:"Gelir Artırma Projeksiyonu"}),l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{className:"mb-2",children:[l.jsxs("div",{className:"flex justify-between mb-1",children:[l.jsx("span",{className:"font-medium dark:text-white",children:"Kısa Vade (1-3 Ay)"}),l.jsx("span",{className:"font-medium text-green-600 dark:text-green-400",children:"+20-30%"})]}),l.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5",children:l.jsx("div",{className:"bg-green-500 h-2.5 rounded-full",style:{width:"25%"}})})]}),l.jsxs("div",{className:"mb-2",children:[l.jsxs("div",{className:"flex justify-between mb-1",children:[l.jsx("span",{className:"font-medium dark:text-white",children:"Orta Vade (3-6 Ay)"}),l.jsx("span",{className:"font-medium text-blue-600 dark:text-blue-400",children:"+50-70%"})]}),l.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5",children:l.jsx("div",{className:"bg-blue-500 h-2.5 rounded-full",style:{width:"60%"}})})]}),l.jsxs("div",{className:"mb-2",children:[l.jsxs("div",{className:"flex justify-between mb-1",children:[l.jsx("span",{className:"font-medium dark:text-white",children:"Uzun Vade (6-12 Ay)"}),l.jsx("span",{className:"font-medium text-purple-600 dark:text-purple-400",children:"+100-200%"})]}),l.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5",children:l.jsx("div",{className:"bg-purple-500 h-2.5 rounded-full",style:{width:"90%"}})})]})]}),l.jsxs("div",{className:"mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[l.jsx("h4",{className:"font-semibold text-gray-800 dark:text-white mb-2",children:"Öncelikli Uygulama Planı"}),l.jsxs("ol",{className:"list-decimal list-inside space-y-2 text-gray-700 dark:text-gray-300",children:[l.jsx("li",{className:"pl-2",children:"Haftalık Challenge Etkinlikleri başlatın (1. hafta)"}),l.jsx("li",{className:"pl-2",children:"Yayıncı Hedef Programı kurun ve duyurun (2. hafta)"}),l.jsx("li",{className:"pl-2",children:"Yayıncı Akademisi için içerik geliştirme (2-4. hafta)"}),l.jsx("li",{className:"pl-2",children:"İlk marka işbirliği tekliflerini hazırlayın (3-4. hafta)"}),l.jsx("li",{className:"pl-2",children:"Cross-Promotion stratejisi için yayıncı eşleştirmeleri yapın (2. hafta)"})]}),l.jsxs("p",{className:"mt-4 text-sm text-gray-600 dark:text-gray-400",children:[l.jsx("strong",{children:"Not:"})," Tüm stratejilerin uygulanması ile 6 ay içinde mevcut gelir düzeyinin iki katına çıkması hedeflenmelidir. Uzun vadeli stratejilerin başarılı uygulanması ile 12 ay içinde %200'den fazla artış mümkündür."]})]})]})]})]})]})]})}const Mt=({active:e,onClick:t,icon:n,label:r})=>l.jsxs("button",{onClick:t,className:`flex items-center space-x-2 px-4 py-3 border-b-2 transition-colors ${e?"border-indigo-600 text-indigo-600 dark:text-indigo-400 dark:border-indigo-400":"border-transparent text-gray-600 dark:text-gray-400 hover:text-indigo-500 dark:hover:text-indigo-300 hover:border-indigo-200 dark:hover:border-indigo-700"}`,children:[n,l.jsx("span",{children:r})]});export{cu as default};
