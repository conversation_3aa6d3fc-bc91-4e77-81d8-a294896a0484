<?php
/**
 * TikTok Analysis tablosuna average_saves alanını ekler
 */

require_once '../config/config.php';

try {
    $db = getDB();
    
    // Önce alanın var olup olmadığını kontrol et
    $stmt = $db->prepare("SHOW COLUMNS FROM tiktok_analysis LIKE 'average_saves'");
    $stmt->execute();
    $result = $stmt->fetch();
    
    if (!$result) {
        // Alan yoksa ekle
        $sql = "ALTER TABLE tiktok_analysis ADD COLUMN average_saves BIGINT DEFAULT 0 AFTER average_shares";
        $db->exec($sql);
        echo "✓ average_saves alanı tiktok_analysis tablosuna eklendi\n";
    } else {
        echo "✓ average_saves alanı zaten mevcut\n";
    }
    
    echo "\n🎉 Veritabanı güncelleme tamamlandı!\n";
    
} catch (Exception $e) {
    echo "❌ Hata: " . $e->getMessage() . "\n";
}
?>
