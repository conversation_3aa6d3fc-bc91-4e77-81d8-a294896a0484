<?php
// Son mesaj ID'si
$lastId = isset($_SERVER['HTTP_LAST_EVENT_ID']) 
    ? intval($_SERVER['HTTP_LAST_EVENT_ID']) 
    : (isset($_GET['lastId']) ? intval($_GET['lastId']) : 0);

// Hangi kişinin mesajlarını dinlediğimiz
$contactId = isset($_GET['contactId']) ? $_GET['contactId'] : null;

if (!$contactId) {
    echo "event: error\n";
    echo "data: " . json_encode(['error' => 'ContactId gerekli']) . "\n\n";
    exit;
}

// Önceki bağlantı kaldıysa öldür
if (connection_aborted()) {
    exit;
}

// Mesaj log dosyası
$logFile = __DIR__ . '/whatsapp_incoming_log.txt';

// SSE bağlantısını açık tutma süresi (30 dakika)
$endTime = time() + (30 * 60);

// Son kontrol edilen satır
$lastLine = 0;

echo "event: connected\n";
echo "data: " . json_encode(['message' => 'Bağlantı kuruldu', 'contactId' => $contactId]) . "\n\n";
ob_flush();
flush();

// ID 1'den başlat
$messageId = 1;

// Sürekli döngü
while (time() < $endTime) {
    // Cache'i temizle
    clearstatcache(true, $logFile);
    
    // Log dosyası var mı?
    if (file_exists($logFile)) {
        $lines = file($logFile);
        $lineCount = count($lines);
        
        // Yeni satır var mı?
        if ($lineCount > $lastLine) {
            // Yeni satırları işle
            for ($i = $lastLine; $i < $lineCount; $i++) {
                $line = $lines[$i];
                
                // Timestamp'i çıkar
                $json = trim(substr($line, 27));
                $data = json_decode($json, true);
                
                // Mesaj var mı ve doğru kişiye mi?
                if ($data && isset($data['entry'][0]['changes'][0]['value']['messages'][0])) {
                    $message = $data['entry'][0]['changes'][0]['value']['messages'][0];
                    $from = $message['from'] ?? '';
                    
                    // Bu bağlantı için dinlenen kişinin mesajı mı?
                    if ($from === $contactId) {
                        echo "id: " . $messageId . "\n";
                        echo "event: message\n";
                        echo "data: " . json_encode([
                            'type' => 'new_message',
                            'contactId' => $contactId,
                            'message' => $message
                        ]) . "\n\n";
                        
                        ob_flush();
                        flush();
                        $messageId++;
                    }
                }
            }
            
            // Son satır indeksini güncelle
            $lastLine = $lineCount;
        }
    }
    
    // 1 saniye bekle ve tekrar kontrol et
    sleep(1);
    
    // Bağlantı kesildi mi kontrol et
    if (connection_aborted()) {
        break;
    }
    
    // Periyodik olarak bağlantıyı canlı tutmak için boş mesaj gönder
    echo ": heartbeat\n\n";
    ob_flush();
    flush();
}

// Bağlantı kapanıyor
echo "event: close\n";
echo "data: " . json_encode(['message' => 'Bağlantı kapandı']) . "\n\n";
ob_flush();
flush();
?> 