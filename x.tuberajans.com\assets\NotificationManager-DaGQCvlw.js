import{u as i,j as e}from"./index-DYSQ_-oc.js";const s=()=>{const{darkMode:t}=i();return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex justify-between items-center",children:e.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Bildirim Yönetimi"})}),e.jsx("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg p-6",children:e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Bildirim yönetimi özelliği yakında eklenecektir."})})]})};export{s as default};
