const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/App-BJoNc_gH.js","assets/js/reactDnd-uQSTYBkW.js","assets/js/vendor-CnpYymF8.js","assets/js/antd-BfejY-CV.js","assets/js/utils-CtuI0RRe.js","assets/js/charts-6B1FLgFz.js","assets/css/App-YKNrpFJZ.css"])))=>i.map(i=>d[i]);
import{j as o}from"./reactDnd-uQSTYBkW.js";import{f as w,g as T,r as u,o as I,B as v}from"./vendor-CnpYymF8.js";import"./antd-BfejY-CV.js";(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const t of document.querySelectorAll('link[rel="modulepreload"]'))a(t);new MutationObserver(t=>{for(const s of t)if(s.type==="childList")for(const c of s.addedNodes)c.tagName==="LINK"&&c.rel==="modulepreload"&&a(c)}).observe(document,{childList:!0,subtree:!0});function e(t){const s={};return t.integrity&&(s.integrity=t.integrity),t.referrerPolicy&&(s.referrerPolicy=t.referrerPolicy),t.crossOrigin==="use-credentials"?s.credentials="include":t.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function a(t){if(t.ep)return;t.ep=!0;const s=e(t);fetch(t.href,s)}})();const k="modulepreload",L=function(n){return"/"+n},E={},b=function(r,e,a){let t=Promise.resolve();if(e&&e.length>0){let c=function(l){return Promise.all(l.map(d=>Promise.resolve(d).then(h=>({status:"fulfilled",value:h}),h=>({status:"rejected",reason:h}))))};document.getElementsByTagName("link");const i=document.querySelector("meta[property=csp-nonce]"),f=(i==null?void 0:i.nonce)||(i==null?void 0:i.getAttribute("nonce"));t=c(e.map(l=>{if(l=L(l),l in E)return;E[l]=!0;const d=l.endsWith(".css"),h=d?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${l}"]${h}`))return;const p=document.createElement("link");if(p.rel=d?"stylesheet":k,d||(p.as="script"),p.crossOrigin="",p.href=l,f&&p.setAttribute("nonce",f),document.head.appendChild(p),d)return new Promise((_,R)=>{p.addEventListener("load",_),p.addEventListener("error",()=>R(new Error(`Unable to preload CSS for ${l}`)))})}))}function s(c){const i=new Event("vite:preloadError",{cancelable:!0});if(i.payload=c,window.dispatchEvent(i),!i.defaultPrevented)throw c}return t.then(c=>{for(const i of c||[])i.status==="rejected"&&s(i.reason);return r().catch(s)})};var m={},S;function P(){if(S)return m;S=1;var n=w();return m.createRoot=n.createRoot,m.hydrateRoot=n.hydrateRoot,m}var O=P();const x=T(O),A=()=>"https://x.tuberajans.com/backend/x-site",C="https://x.tuberajans.com/backend/x-site",N="https://x.tuberajans.com/backend/site",G={X_SITE_BASE_URL:C,SITE_BASE_URL:N,BASE_URL:A(),AKADEMI_BASE_URL:"https://akademi.tuberajans.com/backend/akademi",TEST_URL:`${A()}/db-status.php`,ALWAYS_USE_MOCK_IN_DEV:!1,TIMEOUT:3e4,ENDPOINTS:{AUTH:"/auth.php",LOGOUT:"/auth.php?action=logout",DASHBOARD:"/dashboard.php",DASHBOARD_METRICS:"/dashboard-metrics.php",PUBLISHERS:"/publishers.php",REPORTS:"/reports.php",INFLUENCERS:"/influencers.php",USERS:"/user-management.php",EVENTS:"/events.php",TASKS:"/tasks.php",WEEKLY_TASKS:"/weekly_tasks.php",WEEKLY_ARCHIVE:"/weekly_archive.php",FETCH_WEEKLY_DATA:"/fetch_weekly_data.php",AUTO_ASSIGN_TASKS:"/auto-assign-tasks.php",UPDATE_FOLLOWERS:"/update-followers.php",PERFORMANCE:"/performance.php",PERFORMANCE_COMPARISON:"/performance_comparison.php",LOGIN_LOGS:"/login-logs.php",AKADEMI_DASHBOARD:"/akademi-dashboard.php",AKADEMI_DUYURULAR:"/akademi-duyurular.php",AKADEMI_EGITIMLER:"/akademi-egitimler.php",AKADEMI_DESTEK:"/akademi-destek.php",AKADEMI_KULLANICILAR:"/akademi-kullanicilar.php",AKADEMI_YAPAY_ZEKA:"/akademi-yapay-zeka.php",SITE_DASHBOARD:"/dashboard.php",SITE_ANASAYFA:"/anasayfa.php",SITE_YAYINCILAR:"/yayincilar.php",SITE_BASVURULAR:"/basvurular.php",SITE_ILETISIM_TALEPLERI:"/iletisimtalepleri.php",SITE_GERIARAMA_TALEPLERI:"/geriaramatalep.php",SITE_TOPLANTI_TALEPLERI:"/onlinetoplantitalep.php",SITE_TOPLU_SMS:"/toplu-sms.php",SITE_BLOG:"/blog.php",ETSY_DASHBOARD:"/etsy-dashboard.php",ETSY_TASARIMLAR:"/etsy-tasarimlar.php",ETSY_URUNLER:"/etsy-urunler.php",ETSY_AYARLAR:"/etsy-ayarlar.php",WHATSAPP_CONTACTS:"/whatsapp-contacts.php",WHATSAPP_MESSAGES:"/whatsapp-messages.php",WHATSAPP_TEMPLATES:"/whatsapp-templates.php",WHATSAPP_STATS:"/whatsapp-stats.php",WHATSAPP_SEND_MESSAGE:"/whatsapp-send.php",WHATSAPP_SETTINGS:"/whatsapp-settings.php",PUBLISHER_DISCOVERY:"/publisher-discovery.php",PUBLISHER_DISCOVERY_STATUS:"/publisher-discovery-status.php",PUBLISHER_DISCOVERY_CONTROL:"/publisher-discovery-control.php",PUBLISHER_DISCOVERY_QUERY:"/publisher-discovery-query.php",PUBLISHER_DISCOVERY_SEND_MESSAGE:"/publisher-discovery-send-message.php"}},U={NAME:"Tuber Ajans",ROUTER:{BASE_PATH:"/"}},F={TOKEN_KEY:"x_tuber_token",USER_KEY:"x_tuber_user",THEME_KEY:"x_tuber_theme"};class D{static async clearAllCaches(){try{if("caches"in window){const a=await caches.keys();await Promise.all(a.map(t=>caches.delete(t)))}const r=localStorage.getItem("user"),e=localStorage.getItem("token");localStorage.clear(),r&&localStorage.setItem("user",r),e&&localStorage.setItem("token",e),sessionStorage.clear()}catch(r){}}static async clearApiCaches(){try{if("caches"in window){const e=(await caches.keys()).filter(a=>a.includes("api")||a.includes("x-tuber"));await Promise.all(e.map(a=>caches.delete(a)))}}catch(r){}}static async reregisterServiceWorker(){try{if("serviceWorker"in navigator){const r=await navigator.serviceWorker.getRegistrations();await Promise.all(r.map(e=>e.unregister())),await navigator.serviceWorker.register("/service-worker.js")}}catch(r){}}static forceReload(){"location"in window&&window.location.reload()}static async fullCacheReset(){try{await this.clearAllCaches(),await this.reregisterServiceWorker(),setTimeout(()=>{this.forceReload()},1e3)}catch(r){this.forceReload()}}static async bypassAuthCache(){try{if("caches"in window){const a=(await caches.keys()).filter(t=>t.includes("auth")||t.includes("login")||t.includes("api"));await Promise.all(a.map(t=>caches.delete(t)))}Object.keys(localStorage).filter(e=>e.includes("auth")&&!e.includes("user")&&!e.includes("token")).forEach(e=>{localStorage.removeItem(e)})}catch(r){}}static async getCacheInfo(){try{const r={serviceWorkerSupported:"serviceWorker"in navigator,cacheSupported:"caches"in window,caches:[],localStorage:{keys:Object.keys(localStorage),size:JSON.stringify(localStorage).length},sessionStorage:{keys:Object.keys(sessionStorage),size:JSON.stringify(sessionStorage).length}};if("caches"in window){const e=await caches.keys();for(const a of e){const s=await(await caches.open(a)).keys();r.caches.push({name:a,entries:s.length,urls:s.map(c=>c.url)})}}return r}catch(r){return null}}}typeof window!="undefined"&&(window.CacheManager=D);const j=u.lazy(()=>b(()=>import("./App-BJoNc_gH.js").then(n=>n.U),__vite__mapDeps([0,1,2,3,4,5,6])));console.log=()=>{},console.debug=()=>{},console.info=()=>{};const g=()=>o.jsxs("div",{className:"loading-container",style:{width:"100%",height:"100vh",display:"flex",justifyContent:"center",alignItems:"center",background:"#f5f5f5"},children:[o.jsx("div",{className:"spinner",style:{width:"40px",height:"40px",border:"4px solid rgba(0, 0, 0, 0.1)",borderRadius:"50%",borderTop:"4px solid #1890ff",animation:"spin 1s linear infinite"}}),o.jsx("style",{children:`
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `})]}),Y=()=>o.jsx("div",{className:"error-container",style:{width:"100%",height:"100vh",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",background:"#f5f5f5",color:"#333"},children:o.jsxs("div",{style:{textAlign:"center",maxWidth:"80%"},children:[o.jsx("h1",{style:{fontSize:"2rem",marginBottom:"1rem"},children:"Uygulama Yüklenirken Bir Sorun Oluştu"}),o.jsx("p",{style:{fontSize:"1rem",marginBottom:"2rem"},children:"Lütfen sayfayı yenileyin veya daha sonra tekrar deneyin."}),o.jsx("button",{onClick:()=>window.location.reload(),style:{padding:"10px 20px",backgroundColor:"#1890ff",color:"white",border:"none",borderRadius:"4px",cursor:"pointer",fontSize:"1rem"},children:"Sayfayı Yenile"})]})}),B=()=>{const[n,r]=u.useState(!1),[e,a]=u.useState(!0);return u.useEffect(()=>{const t=()=>{r(!0)};return performance.mark("app-start"),window.addEventListener("error",t),setTimeout(()=>{a(!1),performance.mark("app-loaded"),performance.measure("app-loading-time","app-start","app-loaded")},500),()=>{window.removeEventListener("error",t)}},[]),n?o.jsx(Y,{}):e?o.jsx(g,{}):o.jsx(u.Suspense,{fallback:o.jsx(g,{}),children:o.jsx(v,{basename:U.ROUTER.BASE_PATH,children:o.jsx(j,{})})})},y=document.getElementById("root");if(!y)throw new Error("Root element bulunamadı! #root ID'li element mevcut değil.");const M=x.createRoot(y);M.render(o.jsx(I.StrictMode,{children:o.jsx(B,{})}));"serviceWorker"in navigator&&window.addEventListener("load",async()=>{try{const n=await navigator.serviceWorker.getRegistrations();for(const e of n)e.scope.includes("x-tuber-cache-v1")&&await e.unregister();if("caches"in window){const e=await caches.keys();await Promise.all(e.map(a=>{if(a.includes("x-tuber-cache-v1"))return caches.delete(a)}))}const r=await navigator.serviceWorker.register("/service-worker.js",{updateViaCache:"none",scope:"/"});await r.update(),r.addEventListener("updatefound",()=>{const e=r.installing;e&&e.addEventListener("statechange",()=>{e.state==="installed"&&navigator.serviceWorker.controller&&confirm("Yeni güncelleme mevcut. Sayfayı yenilemek ister misiniz?")&&window.location.reload()})})}catch(n){}});export{G as A,D as C,F as S,b as _,U as a};
