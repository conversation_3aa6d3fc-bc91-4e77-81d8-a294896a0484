<?php
// Detaylı hata ayıklama
ini_set('display_errors', 1); // Geçici olarak hataları göster
error_reporting(E_ALL);

// Debug: API'nin başladığını bildir
error_log("Publisher Discovery Status API başlatılıyor - " . date('Y-m-d H:i:s'));

// Config yolunu belirle
$configPath = __DIR__ . '/../config/config.php';
if (!file_exists($configPath)) {
    // Alternatif yolları dene
    $alternatives = [
        __DIR__ . '/../../config/config.php',
        '/home/<USER>/x.tuberajans.com/backend/config/config.php'
    ];
    
    foreach ($alternatives as $alt) {
        if (file_exists($alt)) {
            $configPath = $alt;
            break;
        }
    }
    
    if (!file_exists($configPath)) {
        die("Config dosyası bulunamadı: Denenen yollar: " . implode(', ', $alternatives));
    }
}

// Config dosyasını dahil et
require_once $configPath;

// Debug: API'nin buraya kadar çalıştığını bildir
error_log("Publisher Discovery Status API: Tüm include'lar tamamlandı - " . date('Y-m-d H:i:s'));

// CORS ayarları - alt domain yapısında daha basit CORS yapılandırması
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// OPTIONS isteği varsa hızlıca cevap ver
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// JSON yanıt fonksiyonu - config.php'de tanımlı değilse buradan tanımla
if (!function_exists('jsonResponse')) {
    function jsonResponse($data, $status = 200) {
        http_response_code($status);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}

// Auth kontrolü
if ($_SERVER['REQUEST_METHOD'] !== 'OPTIONS') {
    if (!checkAuth()) {
        error_log("Publisher Discovery Status API: Auth başarısız");
        jsonResponse(['error' => 'Unauthorized', 'message' => 'Bu işlemi gerçekleştirmek için giriş yapmalısınız.'], 401);
    }
    error_log("Publisher Discovery Status API: Auth başarılı");
}

try {
    // Database auto-detection kullan
    $databases = ['tuberaja_yayinci_takip', 'tuberaja_yayinci_akademi', 'tiktok_live_data'];
    $automationDatabase = null;
    
    foreach ($databases as $database) {
        try {
            $testQuery = "SELECT 1 FROM $database.automation_status LIMIT 1";
            $db->query($testQuery);
            $automationDatabase = $database;
            error_log("Publisher Discovery Status API: automation_status tablosu bulundu: $database");
            break;
        } catch (PDOException $e) {
            continue;
        }
    }
    
    if (!$automationDatabase) {
        error_log("Publisher Discovery Status API: automation_status tablosu bulunamadı");
        echo json_encode([
            'success' => true,
            'data' => [
                'running' => false,
                'startTime' => null,
                'pid' => null,
                'logOutput' => [],
                'status' => null,
                'lastHeartbeat' => null,
                'lastError' => null
            ]
        ]);
        exit();
    }
    
    $stmt = $db->query("SELECT * FROM $automationDatabase.automation_status WHERE id = 1 LIMIT 1");
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($row) {
        $lastHeartbeat = strtotime($row['last_heartbeat']);
        $now = time();
        $diff = $now - $lastHeartbeat;
        $isRunning = $diff < 15; // 15 saniyeden eskiyse kapalı kabul et
        
        // Config dosyasından ayarları oku
        $cycleTime = 1; // varsayılan
        $messageMode = 'both'; // varsayılan
        
        $configFile = __DIR__ . '/../automation_config.json';
        if (file_exists($configFile)) {
            $configContent = file_get_contents($configFile);
            $config = json_decode($configContent, true);
            if ($config && is_array($config)) {
                $cycleTime = $config['cycleTime'] ?? 1;
                $messageMode = $config['messageMode'] ?? 'both';
            }
        }
        
        echo json_encode([
            'success' => true,
            'data' => [
                'running' => $isRunning,
                'startTime' => $row['start_time'] ?? null,
                'pid' => $row['pid'] ?? null,
                'logOutput' => [], // Şimdilik boş array, ileride log eklenebilir
                'status' => $row['status'],
                'lastHeartbeat' => $row['last_heartbeat'],
                'lastError' => $row['last_error'],
                'cycleTime' => $cycleTime,
                'messageMode' => $messageMode,
                'configFile' => $configFile
            ]
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'data' => [
                'running' => false,
                'startTime' => null,
                'pid' => null,
                'logOutput' => [],
                'status' => null,
                'lastHeartbeat' => null,
                'lastError' => null
            ]
        ]);
    }
} catch (PDOException $e) {
    error_log("Veritabanı sorgu hatası: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'data' => [
            'running' => false,
            'startTime' => null,
            'pid' => null,
            'logOutput' => [],
            'status' => null,
            'lastHeartbeat' => null,
            'lastError' => null
        ],
        'error' => 'Veritabanı hatası: ' . $e->getMessage()
    ]);
} 