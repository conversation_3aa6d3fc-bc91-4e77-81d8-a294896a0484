<?php
// Veritabanı bağlantı testi
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// CORS başlıkları
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
header("Content-Type: application/json; charset=utf-8");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(204);
    exit();
}

try {
    require_once __DIR__ . '/../config/config.php';
    
    $response = [
        'status' => 'success',
        'message' => 'Veritabanı bağlantısı başarılı',
        'connections' => []
    ];
    
    // Ana veritabanı testi
    if (isset($db) && $db) {
        try {
            $stmt = $db->query("SELECT 1");
            $response['connections']['main_db'] = 'OK';
        } catch (Exception $e) {
            $response['connections']['main_db'] = 'ERROR: ' . $e->getMessage();
        }
    } else {
        $response['connections']['main_db'] = 'NOT_CONNECTED';
    }
    
    // Takip veritabanı testi
    if (isset($db_takip) && $db_takip) {
        try {
            $stmt = $db_takip->query("SELECT 1");
            $response['connections']['db_takip'] = 'OK';
            
            // Users tablosunu kontrol et
            $stmt = $db_takip->query("SELECT COUNT(*) as count FROM users");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $response['connections']['users_table'] = 'OK - ' . $result['count'] . ' kullanıcı';
            
            // User_tokens tablosunu kontrol et
            $stmt = $db_takip->query("SELECT COUNT(*) as count FROM user_tokens");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $response['connections']['tokens_table'] = 'OK - ' . $result['count'] . ' token';
            
        } catch (Exception $e) {
            $response['connections']['db_takip'] = 'ERROR: ' . $e->getMessage();
        }
    } else {
        $response['connections']['db_takip'] = 'NOT_CONNECTED';
    }
    
    // Akademi veritabanı testi
    if (isset($db_akademi) && $db_akademi) {
        try {
            $stmt = $db_akademi->query("SELECT 1");
            $response['connections']['db_akademi'] = 'OK';
        } catch (Exception $e) {
            $response['connections']['db_akademi'] = 'ERROR: ' . $e->getMessage();
        }
    } else {
        $response['connections']['db_akademi'] = 'NOT_CONNECTED';
    }
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Config dosyası yüklenemedi: ' . $e->getMessage()
    ], JSON_PRETTY_PRINT);
}
?>
