<?php
/**
 * Site Yönetimi Dashboard API
 * 
 * tuberajans.com kontrol paneli için verileri döndüren API endpoint
 */

// Mock istatistik verileri
$mockStats = [
    'basvuruSayisi' => 34,
    'onaylananBasvuru' => 159,
    'toplantıTalebi' => 43,
    'geriaramaTalebi' => 0,
    'iletisimTalebi' => 0,
    'bekleyenTalep' => 34,
    'toplamBasvuru' => 704
];

// Mock başvurular
$recentApplications = [
    [
        'id' => 1, 
        'name' => 'Ahmet Yılmaz',
        'email' => '<EMAIL>',
        'phone' => '0532 123 4567',
        'date' => '2023-05-01',
        'status' => 'Yeni'
    ],
    [
        'id' => 2,
        'name' => 'Ayşe Demir',
        'email' => '<EMAIL>',
        'phone' => '0533 765 4321',
        'date' => '2023-05-01',
        'status' => 'İnceleniyor'
    ],
    [
        'id' => 3,
        'name' => 'Mehmet <PERSON>',
        'email' => '<EMAIL>',
        'phone' => '0535 987 6543',
        'date' => '2023-04-30',
        'status' => 'Onaylandı'
    ],
    [
        'id' => 4,
        'name' => 'Zeynep Şahin',
        'email' => '<EMAIL>',
        'phone' => '0536 456 7890',
        'date' => '2023-04-30',
        'status' => 'Onaylandı'
    ],
    [
        'id' => 5,
        'name' => 'Ali Özkan',
        'email' => '<EMAIL>',
        'phone' => '0537 987 6543',
        'date' => '2023-04-29',
        'status' => 'Reddedildi'
    ]
];

// Yanıt verisi
$response = [
    'success' => true,
    'stats' => $mockStats,
    'recentApplications' => $recentApplications
];

// JSON olarak yanıt döndür
echo json_encode($response);
exit; 