<?php
/**
 * Events tablosunu kontrol etmek için test script
 */

ini_set('display_errors', 1);
error_reporting(E_ALL);

require_once __DIR__ . '/../config/config.php';

// CORS ayarları
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json');

try {
    // Tablo var mı kontrol et
    $stmt = $db_akademi->query("SHOW TABLES LIKE 'events'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        echo json_encode([
            'success' => false,
            'message' => 'events tablosu bulunamadı!',
            'action_needed' => 'Veritabanında events tablosunu oluşturmanız gerekiyor.'
        ]);
        exit;
    }
    
    // Tablo yapısını kontrol et
    $stmt = $db_akademi->query("DESCRIBE events");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Etkinlik sayısını kontrol et
    $stmt = $db_akademi->query("SELECT COUNT(*) as count FROM events");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // Örnek bir etkinlik al
    $sampleEvent = null;
    if ($count > 0) {
        $stmt = $db_akademi->query("SELECT * FROM events LIMIT 1");
        $sampleEvent = $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    echo json_encode([
        'success' => true,
        'table_exists' => true,
        'columns' => $columns,
        'event_count' => $count,
        'sample_event' => $sampleEvent
    ]);
    
} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Veritabanı hatası: ' . $e->getMessage()
    ]);
}
