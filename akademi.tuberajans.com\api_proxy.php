<?php
// Hata raporlamasını etkinleştir
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// CORS başlıkları
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Origin, Content-Type, Accept, Authorization');

// İstenen API endpoint'i al
$endpoint = isset($_GET['endpoint']) ? $_GET['endpoint'] : 'test';

// API dosyasının yolu
$api_file = __DIR__ . '/backend/api/' . $endpoint . '.php';

// API dosyası var mı kontrol et
if (file_exists($api_file)) {
    // API dosyasını çalıştır
    include($api_file);
} else {
    // Hata yanıtı
    $response = [
        'status' => 'error',
        'message' => 'API endpoint bulunamadı: ' . $endpoint,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    echo json_encode($response);
}
?>
