// React import'u kaldırıldı
import { Link } from 'react-router-dom';
import { FaArrowLeft, FaTiktok } from 'react-icons/fa';

const Terms = () => {
  return (
    <div className="min-h-screen bg-[#0a0a0a] text-white">
      {/* Header */}
      <header className="bg-gradient-to-r from-emerald-500 to-blue-600 py-10 relative overflow-hidden">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="container mx-auto px-4 relative z-10 text-center">
          <Link to="/">
            <img
              src="/images/logotuber1.png"
              alt="Tuber Akademi Logo"
              className="h-12 mx-auto mb-4"
            />
          </Link>
          <h1 className="text-3xl md:text-4xl font-bold mb-2">Kullanım Şartları</h1>
          <p className="text-lg text-white/80 max-w-2xl mx-auto">
            Tuber Akademi Yayıncı Portalı'nı kullanırken uymanız gereken şartlar ve koşullar
          </p>
        </div>
      </header>

      {/* Content */}
      <main className="container mx-auto px-4 py-10">
        <div className="max-w-4xl mx-auto bg-white/5 backdrop-blur-sm rounded-xl border border-white/10 p-6 md:p-8 shadow-xl">
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-emerald-400 pb-2 border-b border-white/10 mb-4">
              Kullanım Şartları ve Koşulları
            </h2>
            <p className="text-gray-300 mb-4">Son güncelleme: {new Date().toLocaleDateString('tr-TR')}</p>
            <p className="text-gray-300">
              Tuber Akademi Yayıncı Portalı'na hoş geldiniz. Bu portalı kullanarak aşağıdaki şartları ve koşulları kabul etmiş olursunuz. Lütfen bu şartları dikkatlice okuyunuz.
            </p>
          </div>

          <div className="bg-emerald-500/10 border-l-4 border-emerald-500 rounded-r-lg p-4 mb-8">
            <h3 className="flex items-center text-xl font-semibold text-emerald-400 mb-2">
              <FaTiktok className="mr-2" /> TikTok API Entegrasyonu Hakkında
            </h3>
            <p className="text-gray-300 mb-2">
              Tuber Akademi Yayıncı Portalı, TikTok API'sini kullanarak TikTok yayıncılarına özel hizmetler sunmaktadır. Portalımız, TikTok geliştiriciler için politikalarına ve TikTok Platformu Koşullarına uygun bir şekilde hizmet vermektedir.
            </p>
            <p className="text-gray-300 mb-2">Kullandığımız TikTok özellikleri ve kapsamları:</p>
            <ul className="list-disc pl-5 text-gray-300 mb-2">
              <li><strong>Login Kit:</strong> Kullanıcıların TikTok hesaplarıyla güvenli bir şekilde giriş yapmasını sağlar.</li>
              <li><strong>Share Kit:</strong> Kullanıcıların içeriklerini TikTok platformunda doğrudan paylaşabilmesini sağlar.</li>
              <li><strong>Display API:</strong> TikTok'taki içerik performansının portal üzerinde görüntülenmesini sağlar.</li>
              <li><strong>Analytics API:</strong> İçerik analizleri ve performans verilerinin portal üzerinden izlenebilmesini sağlar.</li>
            </ul>
            <p className="text-gray-300">
              Bu portalı kullanarak, TikTok'un kendi <a href="https://www.tiktok.com/legal/terms-of-service" target="_blank" rel="noopener noreferrer" className="text-emerald-400 hover:text-emerald-300">Kullanım Şartları</a> ve <a href="https://www.tiktok.com/legal/privacy-policy" target="_blank" rel="noopener noreferrer" className="text-emerald-400 hover:text-emerald-300">Gizlilik Politikası</a>'nı da kabul etmiş olursunuz.
            </p>
          </div>

          <div className="space-y-8">
            <section>
              <h3 className="text-xl font-semibold text-emerald-400 mb-3">1. Tanımlar</h3>
              <p className="text-gray-300 mb-2">Bu kullanım şartlarında geçen:</p>
              <ul className="list-disc pl-5 text-gray-300">
                <li>"Tuber Ajans", "biz", "bizim" veya "bizleri" ifadeleri Tuber Ajans'ı,</li>
                <li>"Portal", Yayıncı Portalı'nı,</li>
                <li>"Kullanıcı", "siz", "sizin" ifadeleri portala kayıt olan veya portalı ziyaret eden kişileri,</li>
                <li>"Yayıncı", TikTok platformunda içerik üreten ve portala kayıtlı olan kişileri,</li>
                <li>"TikTok", TikTok sosyal medya platformunu ifade eder.</li>
              </ul>
            </section>

            <section>
              <h3 className="text-xl font-semibold text-emerald-400 mb-3">2. Portal Kullanımı</h3>
              <p className="text-gray-300 mb-2">2.1. Portala üye olabilmek için 18 yaşını doldurmuş olmanız gerekmektedir. 18 yaşından küçükseniz, ebeveyn veya yasal velinizin onayıyla kayıt olabilirsiniz.</p>
              <p className="text-gray-300 mb-2">2.2. Kayıt olurken doğru ve güncel bilgiler sağlamakla yükümlüsünüz. Hesap bilgilerinizin güvenliğinden siz sorumlusunuz.</p>
              <p className="text-gray-300 mb-2">2.3. Hesabınıza erişimi sağlayan kullanıcı adı ve şifrenizin gizliliğini korumak sizin sorumluluğunuzdadır. Hesabınızda gerçekleşen tüm aktivitelerden siz sorumlusunuz.</p>
              <p className="text-gray-300">2.4. Portalı kullanırken yerel, ulusal ve uluslararası kanunlara ve düzenlemelere uymak zorundasınız.</p>
            </section>

            <section>
              <h3 className="text-xl font-semibold text-emerald-400 mb-3">3. Hesap Onayı ve Kayıt Süreci</h3>
              <p className="text-gray-300 mb-2">3.1. Portala kayıt olduktan sonra, hesabınız yönetici onayına tabi olacaktır. Onay sürecinde TikTok profiliniz incelenecek ve uygun bulunduğu takdirde hesabınız aktifleştirilecektir.</p>
              <p className="text-gray-300 mb-2">3.2. Kayıt sırasında TikTok kullanıcı adınızı ve e-posta adresinizi doğru bir şekilde belirtmeniz gerekmektedir.</p>
              <p className="text-gray-300">3.3. Kayıt işleminiz tamamlandıktan sonra, sistem TikTok hesabınızdan bazı temel bilgileri (profil resmi, takipçi sayısı, beğeni sayısı gibi) çekecektir.</p>
            </section>

            <section>
              <h3 className="text-xl font-semibold text-emerald-400 mb-3">4. Portal İçeriği ve Özellikler</h3>
              <p className="text-gray-300 mb-2">4.1. Portal, TikTok yayıncılarına yönelik eğitim içerikleri, istatistik analizi, topluluk desteği ve marka işbirliği fırsatları sunmaktadır.</p>
              <p className="text-gray-300 mb-2">4.2. Portal içerisindeki tüm içerikler telif hakkı ile korunmaktadır. İçerikleri izinsiz kopyalayamaz, dağıtamaz veya ticari amaçla kullanamazsınız.</p>
              <p className="text-gray-300">4.3. Tuber Ajans, portal üzerindeki içerikleri ve özellikleri önceden bildirmeksizin değiştirme, ekleme veya kaldırma hakkını saklı tutar.</p>
            </section>

            <div className="text-center mt-10">
              <p className="text-gray-300 mb-6 font-semibold">
                Bu kullanım şartlarını kabul ederek, içerdiği tüm koşulları okuduğunuzu, anladığınızı ve bunlara uymayı kabul ettiğinizi beyan etmiş olursunuz.
              </p>
              <Link
                to="/"
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-emerald-500 to-blue-600 rounded-lg text-white font-medium transition-all hover:from-emerald-600 hover:to-blue-700 transform hover:-translate-y-1"
              >
                <FaArrowLeft className="mr-2" /> Giriş Sayfasına Dön
              </Link>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Terms;
