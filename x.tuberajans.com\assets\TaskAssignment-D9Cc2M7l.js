import{r as s,u as _,j as e,U as A,R as k,S as w,V as i,a as L,b as O}from"./index-DYSQ_-oc.js";const I=({publishers:c,tasks:g,onAssigned:b})=>{const[l,f]=s.useState([]),[n,p]=s.useState([]),[x,N]=s.useState(""),[m,S]=s.useState(""),[h,o]=s.useState(!1),{darkMode:D}=_(),j=s.useMemo(()=>c.filter(a=>a.username.toLowerCase().includes(x.toLowerCase())),[c,x]),v=s.useMemo(()=>g.filter(a=>a.gorev_onerisi.toLowerCase().includes(m.toLowerCase())),[g,m]),T=a=>{f(r=>r.includes(a)?r.filter(d=>d!==a):[...r,a])},C=a=>{p(r=>r.includes(a)?r.filter(d=>d!==a):[...r,a])},G=async()=>{if(l.length===0){i.error("Lütfen en az bir yayıncı seçin");return}if(n.length===0){i.error("Lütfen en az bir görev seçin");return}o(!0);try{const a=n.flatMap(r=>l.map(d=>{const u=c.find(y=>y.id===d),t=g.find(y=>y.id===r);return{task_id:r,kullanici_adi:(u==null?void 0:u.username)||"",hafta_baslangici:(t==null?void 0:t.created_at)||new Date().toISOString().split("T")[0],hafta_bitisi:(t==null?void 0:t.bitis_tarihi)||new Date(Date.now()+6048e5).toISOString().split("T")[0]}}));await L(a),i.success("Görevler başarıyla atandı"),f([]),p([]),b()}catch(a){console.error("Görev atama hatası:",a),i.error("Görevler atanırken bir hata oluştu")}finally{o(!1)}},P=async()=>{o(!0);try{const a=new Date().toISOString().split("T")[0],r=new Date(Date.now()+7*24*60*60*1e3).toISOString().split("T")[0];await O(a,r),i.success("Görevler otomatik olarak atandı"),b()}catch(a){console.error("Otomatik atama hatası:",a),i.error("Otomatik atama sırasında bir hata oluştu")}finally{o(!1)}};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Görev Atama Paneli"}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsxs("button",{onClick:G,disabled:h||l.length===0||n.length===0,className:"flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed",children:[e.jsx(A,{className:"w-4 h-4 mr-2"}),"Seçili Görevleri Ata"]}),e.jsxs("button",{onClick:P,disabled:h,className:"flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed",children:[h?e.jsx(k,{className:"w-4 h-4 mr-2 animate-spin"}):e.jsx(k,{className:"w-4 h-4 mr-2"}),"Otomatik Ata"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"border dark:border-gray-700 rounded-lg overflow-hidden",children:[e.jsx("div",{className:"bg-gray-50 dark:bg-gray-700 px-4 py-3 border-b dark:border-gray-600",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("h3",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:["Yayıncılar (",l.length," seçili)"]}),e.jsxs("div",{className:"relative w-48",children:[e.jsx(w,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),e.jsx("input",{type:"text",placeholder:"Yayıncı ara...",value:x,onChange:a=>N(a.target.value),className:"pl-9 pr-3 py-1 w-full text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent dark:bg-gray-600 dark:text-white"})]})]})}),e.jsx("div",{className:"overflow-y-auto max-h-96 bg-white dark:bg-gray-800",children:j.length>0?e.jsx("ul",{className:"divide-y dark:divide-gray-700",children:j.map(a=>e.jsx("li",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:e.jsxs("label",{className:"flex items-center px-4 py-3 cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:l.includes(a.id),onChange:()=>T(a.id),className:"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"}),e.jsx("span",{className:"ml-3 text-sm font-medium text-gray-700 dark:text-gray-300",children:a.username})]})},a.id))}):e.jsx("div",{className:"p-4 text-center text-sm text-gray-500 dark:text-gray-400",children:"Yayıncı bulunamadı"})})]}),e.jsxs("div",{className:"border dark:border-gray-700 rounded-lg overflow-hidden",children:[e.jsx("div",{className:"bg-gray-50 dark:bg-gray-700 px-4 py-3 border-b dark:border-gray-600",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("h3",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:["Görevler (",n.length," seçili)"]}),e.jsxs("div",{className:"relative w-48",children:[e.jsx(w,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),e.jsx("input",{type:"text",placeholder:"Görev ara...",value:m,onChange:a=>S(a.target.value),className:"pl-9 pr-3 py-1 w-full text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent dark:bg-gray-600 dark:text-white"})]})]})}),e.jsx("div",{className:"overflow-y-auto max-h-96 bg-white dark:bg-gray-800",children:v.length>0?e.jsx("ul",{className:"divide-y dark:divide-gray-700",children:v.map(a=>e.jsx("li",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:e.jsxs("label",{className:"flex items-center px-4 py-3 cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:n.includes(a.id),onChange:()=>C(a.id),className:"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"}),e.jsxs("div",{className:"ml-3",children:[e.jsx("p",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:a.gorev_onerisi}),a.aciklama&&e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 truncate max-w-xs",children:a.aciklama})]})]})},a.id))}):e.jsx("div",{className:"p-4 text-center text-sm text-gray-500 dark:text-gray-400",children:"Görev bulunamadı"})})]})]})]})};export{I as default};
