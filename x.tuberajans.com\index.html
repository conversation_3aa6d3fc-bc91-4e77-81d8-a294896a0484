<!DOCTYPE html>
<html lang="tr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0" />
    <meta name="theme-color" content="#1890ff" />
    <title>Tuber Ajans - Yayıncı Yönetim Paneli</title>
    <meta name="description" content="TikTok yayıncıları için profesyonel yönetim ve analiz platformu" />
    <meta name="keywords" content="tuber ajans, tiktok, yayıncı yönetimi, influencer, sosyal medya" />
    <meta name="author" content="Tuber Ajans" />
    <meta property="og:title" content="Tuber Ajans - Yayıncı Yönetim Paneli" />
    <meta property="og:description" content="TikTok yayıncıları için profesyonel yönetim ve analiz platformu" />
    <meta property="og:url" content="https://x.tuberajans.com" />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />

    <!-- Preconnect -->
    <link rel="preconnect" href="https://x.tuberajans.com" />
    <link rel="dns-prefetch" href="https://x.tuberajans.com" />

    <!-- Kritik CSS inline -->
    <style>
      :root {
        --primary-color: #1890ff;
        --background-color: #f5f5f5;
      }
      body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        background-color: var(--background-color);
      }
      #root {
        min-height: 100vh;
      }
      .loading-splash {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        width: 100%;
        background-color: var(--background-color);
      }
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(0, 0, 0, 0.1);
        border-radius: 50%;
        border-top: 4px solid var(--primary-color);
        animation: spin 1s linear infinite;
      }
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
    <link rel="icon" type="image/svg+xml" href="/favicon-tx.svg" />
    <script type="module" crossorigin src="/assets/js/index-gEPTqZB-.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/js/vendor-CnpYymF8.js">
    <link rel="modulepreload" crossorigin href="/assets/js/antd-BfejY-CV.js">
    <link rel="modulepreload" crossorigin href="/assets/js/reactDnd-uQSTYBkW.js">
    <link rel="stylesheet" crossorigin href="/assets/css/index-GOmwo_Ey.css">
  </head>
  <body>
    <div id="root">
      <!-- Sayfa yüklenene kadar gösterilecek yükleme ekranı -->
      <div class="loading-splash">
        <div class="loading-spinner"></div>
      </div>
    </div>

    <noscript>Bu uygulamayı kullanmak için JavaScript'i etkinleştirmeniz gerekmektedir.</noscript>
  </body>
</html>