<?php
// CORS başlıkları
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
header("Access-Control-Max-Age: 86400");
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(204);
    exit();
}

header('Content-Type: application/json; charset=utf-8');
try {
    require_once __DIR__ . '/../config/config.php';
} catch (Exception $e) {
    error_log("Config dosyası yüklenemedi: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Server configuration error', 'message' => $e->getMessage()]);
    exit;
}

// Doğru veritabanı bağlantısı
$pdo = $db_akademi; // tuberaja_yayinci_akademi veritabanı için

// Veritabanı bağlantısını kontrol et
if (!isset($pdo) || !$pdo) {
    error_log("Akademi Dashboard API: Veritabanı bağlantısı bulunamadı");
    http_response_code(500);
    echo json_encode(['error' => 'Database connection failed', 'message' => 'Akademi veritabanı bağlantısı bulunamadı']);
    exit;
}

// Debug için bağlantı durumunu logla
error_log("Akademi Dashboard API: Veritabanı bağlantısı başarılı");

try {
    // Tabloların varlığını kontrol et ve gerekirse oluştur
    function ensureTableExists($pdo, $tableName, $createSQL) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$tableName'");
            if ($stmt->rowCount() == 0) {
                error_log("Akademi Dashboard: $tableName tablosu bulunamadı, oluşturuluyor...");
                $pdo->exec($createSQL);
                error_log("Akademi Dashboard: $tableName tablosu başarıyla oluşturuldu");
            }
        } catch (PDOException $e) {
            error_log("Akademi Dashboard: $tableName tablosu oluşturulamadı: " . $e->getMessage());
        }
    }

    // Gerekli tabloları oluştur
    ensureTableExists($pdo, 'users', "
        CREATE TABLE `users` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL,
            `email` varchar(100) NOT NULL,
            `password` varchar(255) NOT NULL,
            `name` varchar(100) DEFAULT NULL,
            `role` enum('admin','instructor','creator','student') DEFAULT 'student',
            `permissions` json DEFAULT NULL,
            `profile_image` varchar(255) DEFAULT NULL,
            `bio` text DEFAULT NULL,
            `social_media` json DEFAULT NULL,
            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            `last_login` datetime DEFAULT NULL,
            `status` enum('active','inactive','suspended') DEFAULT 'active',
            PRIMARY KEY (`id`),
            UNIQUE KEY `username` (`username`),
            UNIQUE KEY `email` (`email`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");

    ensureTableExists($pdo, 'courses', "
        CREATE TABLE `courses` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `title` varchar(255) NOT NULL,
            `description` text DEFAULT NULL,
            `content` longtext DEFAULT NULL,
            `category` varchar(100) DEFAULT 'general',
            `image` varchar(255) DEFAULT NULL,
            `icon` varchar(100) DEFAULT NULL,
            `featured` tinyint(1) DEFAULT 0,
            `created_by` int(11) NOT NULL,
            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            `status` enum('active','inactive','draft') DEFAULT 'active',
            PRIMARY KEY (`id`),
            KEY `created_by` (`created_by`),
            KEY `category` (`category`),
            KEY `status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");

    ensureTableExists($pdo, 'events', "
        CREATE TABLE `events` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `title` varchar(255) NOT NULL,
            `description` text DEFAULT NULL,
            `start_date` datetime NOT NULL,
            `end_date` datetime NOT NULL,
            `location` varchar(255) NOT NULL,
            `capacity` int(11) DEFAULT 0,
            `registered` int(11) DEFAULT 0,
            `thumbnail` varchar(255) DEFAULT NULL,
            `instructor` varchar(255) DEFAULT NULL,
            `category` varchar(100) DEFAULT NULL,
            `is_featured` tinyint(1) DEFAULT 0,
            `created_by` int(11) NOT NULL,
            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            `status` enum('active','cancelled','completed') DEFAULT 'active',
            PRIMARY KEY (`id`),
            KEY `created_by` (`created_by`),
            KEY `start_date` (`start_date`),
            KEY `status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");

    ensureTableExists($pdo, 'announcements', "
        CREATE TABLE `announcements` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `title` varchar(255) NOT NULL,
            `content` text NOT NULL,
            `type` enum('general','important','event','system') DEFAULT 'general',
            `status` enum('active','inactive','expired') DEFAULT 'active',
            `image` varchar(255) DEFAULT NULL,
            `created_by` int(11) NOT NULL,
            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            `expire_date` datetime DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `created_by` (`created_by`),
            KEY `type` (`type`),
            KEY `status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");

    ensureTableExists($pdo, 'support_tickets', "
        CREATE TABLE `support_tickets` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) DEFAULT NULL,
            `subject` varchar(255) NOT NULL,
            `message` text NOT NULL,
            `status` enum('open','pending','closed','resolved') DEFAULT 'open',
            `priority` enum('low','medium','high','urgent') DEFAULT 'medium',
            `assigned_to` int(11) DEFAULT NULL,
            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            `resolved_at` datetime DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `user_id` (`user_id`),
            KEY `status` (`status`),
            KEY `priority` (`priority`),
            KEY `assigned_to` (`assigned_to`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");

    // Güvenli sorgu fonksiyonu
    function safeQuery($pdo, $query, $defaultValue = 0) {
        try {
            $stmt = $pdo->query($query);
            return $stmt ? $stmt->fetch(PDO::FETCH_ASSOC) : ['total' => $defaultValue];
        } catch (PDOException $e) {
            error_log("Akademi Dashboard Query Error: " . $e->getMessage());
            return ['total' => $defaultValue];
        }
    }

    function safeQueryAll($pdo, $query, $defaultValue = []) {
        try {
            $stmt = $pdo->query($query);
            return $stmt ? $stmt->fetchAll(PDO::FETCH_ASSOC) : $defaultValue;
        } catch (PDOException $e) {
            error_log("Akademi Dashboard Query Error: " . $e->getMessage());
            return $defaultValue;
        }
    }

    // Toplam kullanıcı sayısı
    $result = safeQuery($pdo, "SELECT COUNT(*) as total FROM users");
    $total_users = $result['total'] ?? 0;

    // Toplam kurs sayısı (aktif olanlar)
    $result = safeQuery($pdo, "SELECT COUNT(*) as total FROM courses WHERE status = 'active'");
    $total_courses = $result['total'] ?? 0;

    // Yaklaşan etkinlik sayısı (gelecekteki aktif etkinlikler)
    $result = safeQuery($pdo, "SELECT COUNT(*) as total FROM events WHERE status = 'active' AND start_date > NOW()");
    $upcoming_events_count = $result['total'] ?? 0;

    // Açık destek talepleri
    $result = safeQuery($pdo, "SELECT COUNT(*) as open_tickets FROM support_tickets WHERE status = 'open'");
    $open_tickets = $result['open_tickets'] ?? 0;

    // Son kayıt olan kullanıcılar (son 5)
    $recent_users = safeQueryAll($pdo, "SELECT id, username, email, name, created_at FROM users ORDER BY created_at DESC LIMIT 5");

    // Son duyurular (son 3 aktif duyuru)
    $recent_announcements = safeQueryAll($pdo, "SELECT id, title, content, created_at FROM announcements ORDER BY created_at DESC LIMIT 3");

    // Yaklaşan etkinlikler (gelecekteki 3 etkinlik)
    $upcoming_events = safeQueryAll($pdo, "SELECT id, title, description, start_date, end_date, location FROM events WHERE status = 'active' AND start_date > NOW() ORDER BY start_date ASC LIMIT 3");

    // Son destek talepleri (son 5)
    $recent_tickets = safeQueryAll($pdo, "SELECT id, subject, status, priority, created_at FROM support_tickets ORDER BY created_at DESC LIMIT 5");

    echo json_encode([
        'success' => true,
        'stats' => [
            'total_users' => (int)$total_users,
            'total_courses' => (int)$total_courses,
            'upcoming_events_count' => (int)$upcoming_events_count,
            'open_tickets' => (int)$open_tickets
        ],
        'recent_users' => $recent_users,
        'recent_announcements' => $recent_announcements,
        'upcoming_events' => $upcoming_events,
        'recent_tickets' => $recent_tickets
    ]);
} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Veriler alınamadı',
        'error' => $e->getMessage()
    ]);
}