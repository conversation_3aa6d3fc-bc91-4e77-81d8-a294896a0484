import{j as e}from"./reactDnd-CIvPAkL_.js";import{r as o}from"./vendor-CnpYymF8.js";import{K as W,H as J,L as g,E as A,c as O,F as P,M as Q}from"./App-CRh63wQr.js";import{G as p,m as X,l as D,T as ee,k as I,K as ae,i as d,o as $,y as h,z as n,B as T,q as re,aa as K,x as R,I as se,v as b,H as w,s as te}from"./antd-gS---Efz.js";import"./tr-CwGFhkM0.js";import{R as le}from"./InfoCircleOutlined-u-fVVoWX.js";import"./index-CA4FAjHu.js";import"./utils-CtuI0RRe.js";import"./charts-CXWFy-zF.js";var ie={locale:"tr_TR",today:"Bugün",now:"<PERSON>imdi",backToToday:"<PERSON>ugüne Geri Dön",ok:"Tamam",clear:"Temizle",month:"Ay",year:"Yıl",timeSelect:"Zaman Seç",dateSelect:"Tarih Seç",monthSelect:"Ay Seç",yearSelect:"Yıl Seç",decadeSelect:"On Yıl Seç",yearFormat:"YYYY",dateFormat:"M/D/YYYY",dayFormat:"D",dateTimeFormat:"M/D/YYYY HH:mm:ss",monthBeforeYear:!0,previousMonth:"Önceki Ay (PageUp)",nextMonth:"Sonraki Ay (PageDown)",previousYear:"Önceki Yıl (Control + Sol)",nextYear:"Sonraki Yıl (Control + Sağ)",previousDecade:"Önceki On Yıl",nextDecade:"Sonraki On Yıl",previousCentury:"Önceki Yüzyıl",nextCentury:"Sonraki Yüzyıl"};const ne={placeholder:"Zaman seç",rangePlaceholder:["Başlangıç zamanı","Bitiş zamanı"]},oe={lang:Object.assign({placeholder:"Tarih seç",yearPlaceholder:"Yıl seç",quarterPlaceholder:"Çeyrek seç",monthPlaceholder:"Ay seç",weekPlaceholder:"Hafta seç",rangePlaceholder:["Başlangıç tarihi","Bitiş tarihi"],rangeYearPlaceholder:["Başlangıç yılı","Bitiş yılı"],rangeMonthPlaceholder:["Başlangıç ayı","Bitiş ayı"],rangeWeekPlaceholder:["Başlangıç haftası","Bitiş haftası"]},ie),timePickerLocale:Object.assign({},ne)};p.locale("tr");const{Title:de,Text:t,Paragraph:C}=ee,{TextArea:ce}=se,{TabPane:f}=$,{Option:be}=te,H=[{id:1,resimUrl:"https://picsum.photos/800/800?random=1",kucukResimUrl:"https://picsum.photos/200/200?random=1",durum:"beklemede",prompt:"Colorful abstract art with vibrant shapes and patterns",olusturmaTarihi:"2023-06-01T08:30:00",etiketler:["Abstract","Colorful","Digital Art"],kategori:"Soyut"},{id:2,resimUrl:"https://picsum.photos/800/800?random=2",kucukResimUrl:"https://picsum.photos/200/200?random=2",durum:"beklemede",prompt:"Surreal landscape with floating islands in the sky",olusturmaTarihi:"2023-06-01T09:15:00",etiketler:["Landscape","Surreal","Fantasy"],kategori:"Manzara"},{id:3,resimUrl:"https://picsum.photos/800/800?random=3",kucukResimUrl:"https://picsum.photos/200/200?random=3",durum:"onaylandi",prompt:"Futuristic city with neon lights and flying cars",aciklama:"Güzel bir konsept, bu tasarımı onaylıyorum.",olusturmaTarihi:"2023-06-01T10:20:00",onayTarihi:"2023-06-01T14:30:00",onaylayanKullanici:"Art Director",etiketler:["Futuristic","City","Neon"],kategori:"Şehir"},{id:4,resimUrl:"https://picsum.photos/800/800?random=4",kucukResimUrl:"https://picsum.photos/200/200?random=4",durum:"reddedildi",prompt:"Underwater scene with coral reefs and exotic fish",aciklama:"Renkler çok canlı değil, yeniden üretilmesi gerekiyor.",olusturmaTarihi:"2023-06-01T11:45:00",onayTarihi:"2023-06-01T15:10:00",onaylayanKullanici:"Art Director",etiketler:["Underwater","Marine","Nature"],kategori:"Doğa"},{id:5,resimUrl:"https://picsum.photos/800/800?random=5",kucukResimUrl:"https://picsum.photos/200/200?random=5",durum:"beklemede",prompt:"Mystical forest with glowing mushrooms and fairies",olusturmaTarihi:"2023-06-02T08:30:00",etiketler:["Forest","Fantasy","Mystical"],kategori:"Fantezi"},{id:6,resimUrl:"https://picsum.photos/800/800?random=6",kucukResimUrl:"https://picsum.photos/200/200?random=6",durum:"beklemede",prompt:"Mountain landscape with dramatic clouds and sunset",olusturmaTarihi:"2023-06-02T09:45:00",etiketler:["Mountains","Landscape","Sunset"],kategori:"Manzara"},{id:7,resimUrl:"https://picsum.photos/800/800?random=7",kucukResimUrl:"https://picsum.photos/200/200?random=7",durum:"beklemede",prompt:"Cosmic space scene with nebulae and distant galaxies",olusturmaTarihi:"2023-06-02T10:15:00",etiketler:["Space","Cosmic","Galaxy"],kategori:"Uzay"},{id:8,resimUrl:"https://picsum.photos/800/800?random=8",kucukResimUrl:"https://picsum.photos/200/200?random=8",durum:"beklemede",prompt:"Minimalist geometric composition with bold colors",olusturmaTarihi:"2023-06-02T11:30:00",etiketler:["Minimalist","Geometric","Modern"],kategori:"Geometrik"}],fe=()=>{const{user:B}=W(),[L,E]=o.useState("bekleyen"),[Y,S]=o.useState([]),[m,v]=o.useState(!0),[r,G]=o.useState(null),[q,c]=o.useState(!1),[x,U]=o.useState(""),[M,V]=o.useState(p()),[me]=X.useForm(),F=async()=>{v(!0);try{setTimeout(()=>{S(H),v(!1)},1e3)}catch(a){b.error("Tasarımlar yüklenirken bir hata oluştu."),S(H),v(!1)}};o.useEffect(()=>{F()},[]);const l=a=>a==="tumu"?Y:Y.filter(s=>s.durum===a),i=a=>M?a.filter(s=>p(s.olusturmaTarihi).format("YYYY-MM-DD")===M.format("YYYY-MM-DD")):a,N=a=>{G(a),U(a.aciklama||""),c(!0)},z=async(a,s,j)=>{try{const k=Y.map(u=>u.id===a?{...u,durum:s,aciklama:j,onayTarihi:s!=="beklemede"?new Date().toISOString():void 0,onaylayanKullanici:s!=="beklemede"?B==null?void 0:B.name:void 0}:u);S(k),b.success(s==="onaylandi"?"Tasarım başarıyla onaylandı.":s==="reddedildi"?"Tasarım reddedildi.":"Tasarım durumu güncellendi.")}catch(k){b.error("Tasarım durumu güncellenirken bir hata oluştu.")}},Z=()=>{r&&(z(r.id,"onaylandi",x),c(!1))},_=()=>{if(r){if(!x){b.warning("Lütfen reddetme sebebini açıklamada belirtiniz.");return}z(r.id,"reddedildi",x),c(!1)}},y=({tasarim:a})=>{const s={beklemede:"blue",onaylandi:"green",reddedildi:"red"},j={beklemede:"Beklemede",onaylandi:"Onaylandı",reddedildi:"Reddedildi"};return e.jsx(D,{hoverable:!0,className:"h-full",cover:e.jsxs("div",{className:"relative",children:[e.jsx(K,{src:a.resimUrl,alt:a.prompt,className:"w-full object-cover",style:{height:"200px",objectFit:"cover"},preview:{src:a.resimUrl}}),e.jsx(T,{status:s[a.durum],text:j[a.durum],className:"absolute top-2 right-2 bg-white px-2 py-1 rounded shadow"})]}),actions:[e.jsx(w,{title:"Detaylar",children:e.jsx(d,{type:"text",icon:e.jsx(le,{}),onClick:()=>N(a)})}),e.jsx(w,{title:"Onayla",children:e.jsx(d,{type:"text",icon:e.jsx(O,{style:{color:"#52c41a"}}),onClick:()=>N(a),disabled:a.durum!=="beklemede"})}),e.jsx(w,{title:"Reddet",children:e.jsx(d,{type:"text",icon:e.jsx(P,{style:{color:"#f5222d"}}),onClick:()=>N(a),disabled:a.durum!=="beklemede"})})],children:e.jsx(D.Meta,{title:`Tasarım #${a.id}`,description:e.jsxs(e.Fragment,{children:[e.jsx(C,{ellipsis:{rows:2},children:a.prompt}),e.jsx(I,{wrap:!0,children:a.etiketler.map((k,u)=>e.jsx(R,{color:"blue",children:k},u))})]})})})};return e.jsxs("div",{className:"p-4",children:[e.jsxs(D,{children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx(de,{level:3,children:"ETSY Tasarım Onayları"}),e.jsxs(I,{children:[e.jsx(ae,{locale:oe,value:M,onChange:V,allowClear:!0,format:"DD MMMM YYYY",placeholder:"Tarih seçin",style:{width:180}}),e.jsx(d,{icon:e.jsx(J,{}),onClick:F,loading:m,children:"Yenile"})]})]}),e.jsxs($,{activeKey:L,onChange:E,children:[e.jsx(f,{tab:e.jsx(T,{count:i(l("beklemede")).length,offset:[15,0],children:e.jsx("span",{className:"mr-2",children:"Bekleyen Tasarımlar"})}),children:m?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(g,{style:{fontSize:24}}),e.jsx("p",{className:"mt-2",children:"Tasarımlar yükleniyor..."})]}):e.jsx(e.Fragment,{children:i(l("beklemede")).length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(A,{style:{fontSize:48,color:"#ccc"}}),e.jsx("p",{className:"mt-2",children:"Bu tarihte bekleyen tasarım bulunmuyor."})]}):e.jsx(h,{gutter:[16,16],children:i(l("beklemede")).map(a=>e.jsx(n,{xs:24,sm:12,md:8,lg:6,children:e.jsx(y,{tasarim:a})},a.id))})})},"bekleyen"),e.jsx(f,{tab:e.jsx(T,{count:i(l("onaylandi")).length,offset:[15,0],children:e.jsx("span",{className:"mr-2",children:"Onaylanan Tasarımlar"})}),children:m?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(g,{style:{fontSize:24}}),e.jsx("p",{className:"mt-2",children:"Tasarımlar yükleniyor..."})]}):e.jsx(e.Fragment,{children:i(l("onaylandi")).length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(O,{style:{fontSize:48,color:"#ccc"}}),e.jsx("p",{className:"mt-2",children:"Bu tarihte onaylanan tasarım bulunmuyor."})]}):e.jsx(h,{gutter:[16,16],children:i(l("onaylandi")).map(a=>e.jsx(n,{xs:24,sm:12,md:8,lg:6,children:e.jsx(y,{tasarim:a})},a.id))})})},"onaylanan"),e.jsx(f,{tab:e.jsx(T,{count:i(l("reddedildi")).length,offset:[15,0],children:e.jsx("span",{className:"mr-2",children:"Reddedilen Tasarımlar"})}),children:m?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(g,{style:{fontSize:24}}),e.jsx("p",{className:"mt-2",children:"Tasarımlar yükleniyor..."})]}):e.jsx(e.Fragment,{children:i(l("reddedildi")).length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(P,{style:{fontSize:48,color:"#ccc"}}),e.jsx("p",{className:"mt-2",children:"Bu tarihte reddedilen tasarım bulunmuyor."})]}):e.jsx(h,{gutter:[16,16],children:i(l("reddedildi")).map(a=>e.jsx(n,{xs:24,sm:12,md:8,lg:6,children:e.jsx(y,{tasarim:a})},a.id))})})},"reddedilen"),e.jsx(f,{tab:e.jsxs(e.Fragment,{children:[e.jsx(Q,{})," Tüm Tasarımlar"]}),children:m?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(g,{style:{fontSize:24}}),e.jsx("p",{className:"mt-2",children:"Tasarımlar yükleniyor..."})]}):e.jsx(e.Fragment,{children:i(l("tumu")).length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(A,{style:{fontSize:48,color:"#ccc"}}),e.jsx("p",{className:"mt-2",children:"Bu tarihte tasarım bulunmuyor."})]}):e.jsx(h,{gutter:[16,16],children:i(l("tumu")).map(a=>e.jsx(n,{xs:24,sm:12,md:8,lg:6,children:e.jsx(y,{tasarim:a})},a.id))})})},"tumu")]})]}),e.jsx(re,{title:r?`Tasarım #${r.id} Detayları`:"Tasarım Detayları",open:q,onCancel:()=>c(!1),footer:(r==null?void 0:r.durum)==="beklemede"?[e.jsx(d,{onClick:()=>c(!1),children:"İptal"},"back"),e.jsx(d,{danger:!0,onClick:_,children:"Reddet"},"reject"),e.jsx(d,{type:"primary",onClick:Z,children:"Onayla"},"submit")]:[e.jsx(d,{onClick:()=>c(!1),children:"Kapat"},"back")],width:800,children:r&&e.jsxs("div",{children:[e.jsx("div",{className:"mb-4",children:e.jsx(K,{src:r.resimUrl,alt:r.prompt,style:{width:"100%",maxHeight:"400px",objectFit:"contain"}})}),e.jsx("div",{className:"mb-4",children:e.jsxs(h,{gutter:[16,16],children:[e.jsxs(n,{span:12,children:[e.jsx(t,{strong:!0,children:"Durum:"}),e.jsx(R,{color:r.durum==="beklemede"?"blue":r.durum==="onaylandi"?"green":"red",className:"ml-2",children:r.durum==="beklemede"?"Beklemede":r.durum==="onaylandi"?"Onaylandı":"Reddedildi"})]}),e.jsxs(n,{span:12,children:[e.jsx(t,{strong:!0,children:"Kategori:"})," ",e.jsx(t,{children:r.kategori})]}),e.jsxs(n,{span:12,children:[e.jsx(t,{strong:!0,children:"Oluşturma Tarihi:"})," ",e.jsx(t,{children:p(r.olusturmaTarihi).format("DD MMMM YYYY, HH:mm")})]}),r.onayTarihi&&e.jsxs(n,{span:12,children:[e.jsx(t,{strong:!0,children:"Onay/Red Tarihi:"})," ",e.jsx(t,{children:p(r.onayTarihi).format("DD MMMM YYYY, HH:mm")})]}),r.onaylayanKullanici&&e.jsxs(n,{span:12,children:[e.jsx(t,{strong:!0,children:"İşlemi Yapan:"})," ",e.jsx(t,{children:r.onaylayanKullanici})]})]})}),e.jsxs("div",{className:"mb-4",children:[e.jsx(t,{strong:!0,children:"Prompt:"}),e.jsx(C,{children:r.prompt})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx(t,{strong:!0,children:"Etiketler:"}),e.jsx("div",{className:"mt-1",children:r.etiketler.map((a,s)=>e.jsx(R,{color:"blue",children:a},s))})]}),e.jsxs("div",{children:[e.jsx(t,{strong:!0,children:"Açıklama:"}),r.durum==="beklemede"?e.jsx(ce,{value:x,onChange:a=>U(a.target.value),placeholder:"Tasarım hakkında not ekleyin (onay/red sebebi)",rows:4,className:"mt-1"}):e.jsx(C,{children:r.aciklama||"Açıklama bulunmuyor."})]})]})})]})};export{fe as default};
