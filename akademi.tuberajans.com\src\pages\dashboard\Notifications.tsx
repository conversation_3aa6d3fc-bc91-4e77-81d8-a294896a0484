import React, { useState, useEffect } from 'react';
import { FaBell, FaCheckDouble } from 'react-icons/fa';
import { BsCalendar3 } from 'react-icons/bs';
import axios from 'axios';

interface Notification {
  id: number;
  title: string;
  message: string;
  date: string;
  read: boolean;
  type: string;
  source?: string;
  source_id?: string;
  link?: string;
}

const Notifications: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Bildirimleri API'den çek
  useEffect(() => {
    fetchNotifications();
  }, []);

  // Bildirimleri getir
  const fetchNotifications = async () => {
    setLoading(true);
    setError(null);
    try {
      // Kullanıcı ID'si - gerçek uygulamada auth sisteminden alınmalı
      const userId = 1; // Örnek kullanıcı ID'si

      const response = await axios.get('/backend/api/api_data.php', {
        params: {
          endpoint: 'notifications',
          user_id: userId
        }
      });

      if (response.data.status === 'success') {
        // API'den gelen verileri formatla
        const formattedNotifications = response.data.data.map((item: any) => ({
          id: item.id,
          title: item.title,
          message: item.message,
          date: formatDate(new Date(item.created_at)),
          read: item.read === 1,
          type: item.type,
          source: item.source,
          source_id: item.source_id,
          link: item.link
        }));

        setNotifications(formattedNotifications);
      } else {
        setError('Bildirimler alınamadı');
        console.error('Bildirimler alınamadı:', response.data.message);
      }
    } catch (err) {
      setError('Bildirimler yüklenirken bir hata oluştu');
      console.error('Bildirimler yüklenirken hata:', err);
    } finally {
      setLoading(false);
    }
  };

  // Tek bir bildirimi okundu yap
  const markAsRead = async (id: number) => {
    try {
      const response = await axios.put('/backend/api/api_data.php?endpoint=notifications', {
        id: id
      });

      if (response.data.status === 'success') {
        setNotifications(notifications.map(notification =>
          notification.id === id ? { ...notification, read: true } : notification
        ));
      }
    } catch (err) {
      console.error('Bildirim okundu olarak işaretlenirken hata:', err);
    }
  };

  // Tüm bildirimleri okundu yap
  const markAllAsRead = async () => {
    try {
      const response = await axios.put('/backend/api/api_data.php?endpoint=notifications', {
        mark_all: true
      });

      if (response.data.status === 'success') {
        setNotifications(notifications.map(notification => ({ ...notification, read: true })));
      }
    } catch (err) {
      console.error('Tüm bildirimler okundu olarak işaretlenirken hata:', err);
    }
  };

  // Tarih formatı için yardımcı fonksiyon
  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('tr-TR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const unreadCount = notifications.filter(notification => !notification.read).length;

  return (
    <div className="p-6">
      <div className="bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden mb-6">
        <div className="flex items-center justify-between px-4 py-3 border-b border-gray-100 dark:border-gray-700">
          <div className="flex items-center">
            <FaBell className="text-gray-700 dark:text-gray-300 mr-2" />
            <h2 className="text-base font-semibold text-gray-800 dark:text-white">Bildirimler</h2>
            {unreadCount > 0 && (
              <span className="ml-2 bg-[#FF3E71] text-white text-xs px-2 py-0.5 rounded-full">
                {unreadCount}
              </span>
            )}
          </div>
          {unreadCount > 0 && (
            <button
              onClick={markAllAsRead}
              className="text-[#FF3E71] text-sm font-medium hover:underline flex items-center"
            >
              <FaCheckDouble className="mr-1" />
              <span>Tümünü Okundu İşaretle</span>
            </button>
          )}
        </div>

        <div className="divide-y divide-gray-100 dark:divide-gray-700">
          {loading ? (
            <div className="text-center py-10">
              <div className="w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-300 rounded-full flex items-center justify-center mb-4 animate-pulse">
                <FaBell className="w-6 h-6" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Bildirimler Yükleniyor</h3>
              <p className="mt-1 text-gray-500 dark:text-gray-400">Lütfen bekleyin...</p>
            </div>
          ) : error ? (
            <div className="text-center py-10">
              <div className="w-16 h-16 mx-auto bg-red-100 dark:bg-red-900/30 text-red-500 dark:text-red-400 rounded-full flex items-center justify-center mb-4">
                <FaBell className="w-6 h-6" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Hata Oluştu</h3>
              <p className="mt-1 text-red-500 dark:text-red-400">{error}</p>
            </div>
          ) : notifications.length > 0 ? (
            notifications.map((notification) => (
              <div
                key={notification.id}
                className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors ${!notification.read ? 'bg-blue-50/50 dark:bg-blue-900/10' : ''}`}
                onClick={() => {
                  markAsRead(notification.id);
                  if (notification.link) {
                    window.location.href = notification.link;
                  }
                }}
              >
                <div className="flex items-start">
                  <div className={`min-w-[4px] h-16 ${!notification.read ? 'bg-[#FF3E71]' : 'bg-gray-200 dark:bg-gray-600'} rounded-full mr-3`}></div>
                  <div className="flex-1">
                    <div className="flex flex-wrap items-center text-xs text-gray-500 dark:text-gray-400 mb-1">
                      <BsCalendar3 className="text-gray-500 dark:text-gray-400 mr-1 text-xs" />
                      <span>{notification.date}</span>
                      {!notification.read && (
                        <span className="ml-2 px-2 py-0.5 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs">
                          Yeni
                        </span>
                      )}
                      {notification.type && (
                        <span className={`ml-2 px-2 py-0.5 rounded-full text-xs ${
                          notification.type === 'info' ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300' :
                          notification.type === 'success' ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300' :
                          notification.type === 'warning' ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300' :
                          notification.type === 'error' ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300' :
                          'bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-300'
                        }`}>
                          {notification.source === 'new_event' ? 'Etkinlik' :
                           notification.source === 'new_course' ? 'Eğitim' :
                           notification.source === 'ticket_reply' ? 'Destek' :
                           notification.type === 'info' ? 'Bilgi' :
                           notification.type === 'success' ? 'Başarılı' :
                           notification.type === 'warning' ? 'Uyarı' :
                           notification.type === 'error' ? 'Hata' :
                           notification.type}
                        </span>
                      )}
                    </div>
                    <h3 className="text-sm font-medium mb-1 text-gray-800 dark:text-white">{notification.title}</h3>
                    <p className="text-xs text-gray-500 dark:text-gray-400">{notification.message}</p>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-10">
              <div className="w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-300 rounded-full flex items-center justify-center mb-4">
                <FaBell className="w-6 h-6" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Bildirim Bulunamadı</h3>
              <p className="mt-1 text-gray-500 dark:text-gray-400">Henüz bildiriminiz bulunmamaktadır.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Notifications;