<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');

// OPTIONS isteği için
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Sadece POST isteklerini kabul et
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'status' => 'error',
        'message' => 'Method not allowed'
    ]);
    exit;
}

try {
    // Session'ı başlat
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Tüm session verilerini temizle
    $_SESSION = array();

    // Session cookie'sini sil
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }

    // Session'ı yok et
    session_destroy();

    // TikTok ile ilgili cookie'leri temizle
    setcookie('tiktok_access_token', '', time() - 3600, '/');
    setcookie('tiktok_refresh_token', '', time() - 3600, '/');
    setcookie('tiktok_user_id', '', time() - 3600, '/');

    error_log("User logged out successfully");

    echo json_encode([
        'status' => 'success',
        'message' => 'Başarıyla çıkış yapıldı'
    ]);

} catch (Exception $e) {
    error_log('Logout error: ' . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Çıkış işlemi sırasında hata oluştu'
    ]);
}
?>
