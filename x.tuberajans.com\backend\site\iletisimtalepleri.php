<?php
header('Content-Type: application/json; charset=utf-8');

// Hata ayıklama modunu aktifleştir
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/iletisimtalepleri_errors.log');

// CORS başlıkları
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
header("Access-Control-Max-Age: 86400");

// OPTIONS isteği için erken yanıt ver
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(204);
    exit();
}

try {
    // Config dosyasını dahil et
    require_once __DIR__ . '/../config/config.php';
    
    // Veritabanı bağlantısını kontrol et
    if (!isset($db) || !($db instanceof PDO)) {
        throw new Exception("Veritabanı bağlantısı kurulamadı");
    }
    
    // Tablo varlığını kontrol et
    try {
        $tableCheckStmt = $db->query("SHOW TABLES LIKE 'iletisimtalepleri'");
        if ($tableCheckStmt->rowCount() === 0) {
            // Tablo yoksa oluştur
            $createTable = $db->exec("CREATE TABLE IF NOT EXISTS iletisimtalepleri (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                mail VARCHAR(100) NOT NULL,
                message TEXT NOT NULL,
                unixts INT NOT NULL,
                ip VARCHAR(45) NOT NULL,
                isRead TINYINT(1) NOT NULL DEFAULT 0
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci");
            
            if ($createTable !== false) {
                error_log("iletisimtalepleri tablosu oluşturuldu.");
            } else {
                throw new Exception("Tablo oluşturma başarısız.");
            }
        }
    } catch (PDOException $e) {
        error_log("Tablo kontrolü hatası: " . $e->getMessage());
        // Hata var ama devam et, belki başka bir şekilde çalışır
    }
    
    // Veritabanı bağlantısını kur
    $pdo = $db;
    $action = isset($_GET['action']) ? $_GET['action'] : 'list';
    
    // "count" isteği - okunmamış talep sayısını döndür
    if ($action === 'count') {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM iletisimtalepleri WHERE isRead='0'");
            if ($stmt === false) {
                throw new Exception("Sorgu hatası");
            }
            $row = $stmt->fetch();
            echo json_encode(['success' => true, 'count' => intval($row['count'])]);
        } catch (PDOException $e) {
            error_log("İletisim talepleri count hatası: " . $e->getMessage());
            echo json_encode(['success' => false, 'message' => 'Sayı alınamadı', 'error' => $e->getMessage()]);
        }
        exit;
    }
    
    // "list" isteği - tüm talepleri listele
    if ($action === 'list') {
        try {
            $stmt = $pdo->query('SELECT * FROM iletisimtalepleri ORDER BY id DESC');
            $data = array_map(function($row) {
                return [
                    'id' => $row['id'],
                    'adsoyad' => $row['name'],
                    'mail' => $row['mail'],
                    'mesaj' => $row['message'],
                    'zaman' => $row['unixts'],
                    'isRead' => $row['isRead']
                ];
            }, $stmt->fetchAll(PDO::FETCH_ASSOC));
            echo json_encode(['success' => true, 'data' => $data]);
        } catch (PDOException $e) {
            error_log("İletisim talepleri list hatası: " . $e->getMessage());
            echo json_encode(['success' => false, 'message' => 'Veriler alınamadı', 'error' => $e->getMessage()]);
        }
        exit;
    }
    
    // "okundu" isteği - bir talebi okundu olarak işaretle
    if ($action === 'okundu') {
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        if ($id > 0) {
            try {
                $stmt = $pdo->prepare('UPDATE iletisimtalepleri SET isRead=1 WHERE id=?');
                $stmt->execute([$id]);
                echo json_encode(['success' => true]);
            } catch (PDOException $e) {
                error_log("İletisim talepleri okundu hatası: " . $e->getMessage());
                echo json_encode(['success' => false, 'message' => 'Okundu olarak işaretlenemedi', 'error' => $e->getMessage()]);
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'Geçersiz ID']);
        }
        exit;
    }
    
    // "delete" isteği - bir talebi sil
    if ($action === 'delete') {
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        if ($id > 0) {
            try {
                $stmt = $pdo->prepare('DELETE FROM iletisimtalepleri WHERE id=?');
                $stmt->execute([$id]);
                echo json_encode(['success' => true]);
            } catch (PDOException $e) {
                error_log("İletisim talepleri delete hatası: " . $e->getMessage());
                echo json_encode(['success' => false, 'message' => 'Silinemedi', 'error' => $e->getMessage()]);
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'Geçersiz ID']);
        }
        exit;
    }
    
    // Geçersiz aksiyon
    echo json_encode(['success' => false, 'message' => 'Geçersiz action']);
    exit;
    
} catch (Exception $e) {
    error_log("İletisim talepleri genel hata: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Bir hata oluştu', 'error' => $e->getMessage()]);
    exit;
}
?> 