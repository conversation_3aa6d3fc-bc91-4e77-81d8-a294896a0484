import React, { createContext, useState, useEffect, ReactNode } from 'react';

interface SidebarContextType {
  isSidebarOpen: boolean;
  setIsSidebarOpen: React.Dispatch<React.SetStateAction<boolean>>;
  isMobile: boolean;
  setIsMobile: React.Dispatch<React.SetStateAction<boolean>>;
  toggleSidebar: () => void;
  closeSidebar: () => void;
}

export const SidebarContext = createContext<SidebarContextType>({
  isSidebarOpen: true,
  setIsSidebarOpen: () => {},
  isMobile: false,
  setIsMobile: () => {},
  toggleSidebar: () => {},
  closeSidebar: () => {},
});

interface SidebarProviderProps {
  children: ReactNode;
}

export const SidebarProvider: React.FC<SidebarProviderProps> = ({ children }) => {
  const [isMobile, setIsMobile] = useState(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState(!isMobile);

  // Ekran boyutunu kontrol et
  useEffect(() => {
    const checkMobile = () => {
      const isMobileView = window.innerWidth < 1024;

      if (isMobileView !== isMobile) {
        setIsMobile(isMobileView);
        if (isMobileView) {
          setIsSidebarOpen(false);  // Mobilde kapalı başlat
        } else {
          setIsSidebarOpen(true);   // Masaüstünde açık başlat
        }
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, [isMobile]);

  // Body'ye sidebar class'ı ekle/çıkar
  useEffect(() => {
    if (isSidebarOpen) {
      document.body.classList.add('sidebar-open');
      document.body.classList.remove('sidebar-closed');
    } else {
      document.body.classList.add('sidebar-closed');
      document.body.classList.remove('sidebar-open');
    }
  }, [isSidebarOpen]);

  // Sidebar'ı aç/kapa toggle
  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // Sidebar'ı kapat
  const closeSidebar = () => {
    if (isMobile) {
      setIsSidebarOpen(false);
    }
  };

  return (
    <SidebarContext.Provider value={{ 
      isSidebarOpen, 
      setIsSidebarOpen, 
      isMobile, 
      setIsMobile,
      toggleSidebar,
      closeSidebar
    }}>
      {children}
    </SidebarContext.Provider>
  );
};
