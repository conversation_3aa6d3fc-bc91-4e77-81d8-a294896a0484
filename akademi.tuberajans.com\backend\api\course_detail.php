<?php
/**
 * Kurs Detay API
 * Bu API tek bir kursun detaylarını getirmek için kullanılır
 */

// CORS için header ayarları
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json; charset=UTF-8");

// OPTIONS isteği için erken yanıt ver
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Veritabanı bağlantı bilgileri
$db_host = "**************";
$db_username = "root";
$db_password = "Bebek845396!";
$db_name = "tuberaja_yayinci_akademi";

// Veritabanı bağlantısı
try {
    $db = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_username, $db_password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $db->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Veritabanı bağlantı hatası: ' . $e->getMessage()]);
    exit;
}

// İstek methodunu al
$method = $_SERVER['REQUEST_METHOD'];

// GET isteği - Kurs Detayını Getir
if ($method === 'GET') {
    try {
        // ID parametresi kontrolü
        if (!isset($_GET['id'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Kurs ID parametresi gerekli.']);
            exit;
        }

        $id = intval($_GET['id']);

        // Kurs bilgilerini al
        $stmt = $db->prepare("SELECT * FROM courses WHERE id = ?");
        $stmt->execute([$id]);
        $course = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($course) {
            // Önceki ve sonraki kursları bul
            $allCoursesStmt = $db->prepare("
                SELECT id FROM courses
                WHERE status = 'active'
                ORDER BY created_at DESC
            ");
            $allCoursesStmt->execute();
            $allCourses = $allCoursesStmt->fetchAll(PDO::FETCH_COLUMN);

            $currentIndex = array_search($id, $allCourses);
            $prevCourseId = ($currentIndex > 0) ? $allCourses[$currentIndex - 1] : null;
            $nextCourseId = ($currentIndex < count($allCourses) - 1) ? $allCourses[$currentIndex + 1] : null;

            $response = [
                'success' => true,
                'data' => $course,
                'navigation' => [
                    'prev_course_id' => $prevCourseId,
                    'next_course_id' => $nextCourseId
                ]
            ];

            echo json_encode($response);
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Kurs bulunamadı.']);
        }
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Veritabanı hatası: ' . $e->getMessage()]);
    }
} else {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Desteklenmeyen HTTP metodu.']);
}
