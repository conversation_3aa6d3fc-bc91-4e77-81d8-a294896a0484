import os
import sys
import time
import pandas as pd
import openpyxl
import pickle
from openpyxl.styles import PatternFill, Font, Alignment, Border, Side
from datetime import datetime, timedelta, date
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
import random
import re
import argparse
import json
import requests
import zipfile
import tempfile
# Webdriver Manager ekle
from webdriver_manager.chrome import ChromeDriverManager

def check_captcha(driver):
    """CAPTCHA'nın görünüp görünmediğini kontrol eder"""
    try:
        # CAPTCHA ile ilgili olabilecek elementleri kontrol et
        captcha_selectors = [
            "//div[contains(text(), 'CAPTCHA')]",
            "//div[contains(text(), 'captcha')]",
            "//div[contains(text(), 'robot')]",
            "//div[contains(text(), 'Robot')]",
            "//div[contains(@class, 'captcha')]",
            "//div[contains(@class, 'CAPTCHA')]",
            "//iframe[contains(@src, 'captcha')]",
            "//iframe[contains(@title, 'CAPTCHA')]",
            "//div[contains(@class, 'verify-bar-word')]",  # TikTok özel CAPTCHA elementi
            "//div[contains(text(), 'Doğrulama')]",       # Türkçe CAPTCHA metni
            "//div[contains(text(), 'güvenlik kontrolü')]", # Türkçe güvenlik kontrolü metni
            "//div[contains(text(), 'TikTok')]//div[contains(text(), 'giriş')]", # TikTok giriş popup'ı
            "//div[contains(text(), 'TikTok') and contains(text(), 'giriş')]", # TikTok giriş popup'ı (alternatif)
            "//h2[contains(text(), 'TikTok')]", # TikTok başlık popup'ı
            "//div[contains(text(), 'Drag the slider to fit the puzzle')]", # Puzzle slider CAPTCHA
            "//div[contains(text(), 'Drag the slider')]", # İngilizce slider CAPTCHA
            "//div[contains(text(), 'kaydırıcıyı')]",     # Türkçe slider CAPTCHA
            "//div[contains(@class, 'slider')]",          # Slider elementi
            "//div[contains(@class, 'verification')]",    # Doğrulama elementi
            "//div[contains(@class, 'challenge')]",       # Challenge elementi
            "//canvas",                                    # Canvas elementi (CAPTCHA için kullanılabilir)
            "//div[contains(text(), 'Verify')]",          # Verify metni
            "//div[contains(text(), 'Security')]",        # Security metni
            "//div[contains(text(), 'Güvenlik')]"         # Türkçe güvenlik metni
        ]

        for selector in captcha_selectors:
            elements = driver.find_elements(By.XPATH, selector)
            if elements and any(element.is_displayed() for element in elements):
                print(f"⚠️ CAPTCHA algılandı! Seçici: {selector}")
                return True

        # CAPTCHA iframe'lerini kontrol et
        iframes = driver.find_elements(By.TAG_NAME, "iframe")
        for iframe in iframes:
            try:
                src = iframe.get_attribute("src") or ""
                title = iframe.get_attribute("title") or ""
                if "captcha" in src.lower() or "captcha" in title.lower():
                    print("⚠️ CAPTCHA iframe'i algılandı!")
                    return True
            except:
                continue

        # Sayfa başlığını kontrol et
        try:
            page_title = driver.title.lower()
            if "captcha" in page_title or "verification" in page_title or "doğrulama" in page_title:
                print(f"⚠️ CAPTCHA sayfa başlığında algılandı: {driver.title}")
                return True
        except:
            pass

        # URL'yi kontrol et
        try:
            current_url = driver.current_url.lower()
            if "captcha" in current_url or "verification" in current_url or "challenge" in current_url:
                print(f"⚠️ CAPTCHA URL'de algılandı: {driver.current_url}")
                return True
        except:
            pass

        # JavaScript ile daha detaylı kontrol
        try:
            captcha_found = driver.execute_script("""
                // Tüm text içeriklerini kontrol et
                const allText = document.body.innerText.toLowerCase();
                const captchaKeywords = ['captcha', 'doğrulama', 'güvenlik kontrolü', 'verify', 'challenge', 'robot', 'slider', 'tiktok giriş', 'drag the slider', 'fit the puzzle', 'puzzle'];

                for (const keyword of captchaKeywords) {
                    if (allText.includes(keyword)) {
                        return keyword;
                    }
                }

                // Modal veya popup kontrol et
                const modals = document.querySelectorAll('[role="dialog"], .modal, .popup, .overlay');
                for (const modal of modals) {
                    if (modal.style.display !== 'none' && modal.offsetParent !== null) {
                        const modalText = modal.innerText.toLowerCase();
                        for (const keyword of captchaKeywords) {
                            if (modalText.includes(keyword)) {
                                return 'modal_' + keyword;
                            }
                        }
                    }
                }

                return null;
            """)

            if captcha_found:
                print(f"⚠️ JavaScript ile CAPTCHA algılandı: {captcha_found}")
                return True
        except Exception as js_error:
            print(f"⚠️ JavaScript CAPTCHA kontrolünde hata: {js_error}")

        return False
    except Exception as e:
        print(f"⚠️ CAPTCHA kontrolünde hata: {e}")
        return False

# Uygulamanın sabit değerleri
DEFAULT_CHROME_PROFILE_NAME = "TikTokHaftalik"
COOKIES_FILE = "tiktok_cookies.pkl"
LOCAL_CHROME_PATH = r"C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe"  # Chrome for Testing yolu
DEFAULT_PROFILE_PATH = r"C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data"  # Chrome for Testing profil yolu

class TikTokDailyLiveTracker:
    def __init__(self, chrome_profile_path, profile_name, chrome_driver_path, chrome_binary_path):
        self.chrome_profile_path = chrome_profile_path
        self.profile_name = profile_name
        self.chrome_driver_path = chrome_driver_path
        self.chrome_binary_path = chrome_binary_path
        self.driver = None
        self.data = {}
        self.video_data = {}
        self.violations_by_date = {}
        
        # Profile dizinini kontrol et ve gerekirse oluştur
        profile_dir = os.path.join(self.chrome_profile_path, self.profile_name)
        if not os.path.exists(profile_dir):
            print(f"⚠️ {self.profile_name} profili bulunamadı, ilk çalıştırmada oluşturulacak.")
        else:
            print(f"✓ {self.profile_name} profili bulundu: {profile_dir}")
    
    def _check_available_profiles(self):
        # Profil adını otomatik değiştirmesin, hiçbir şey yapmasın
        pass
    
    def setup_driver(self):
        try:
            print("\n1. Chrome ayarları yapılandırılıyor...")
            options = Options()
            
            # Temel profil ayarları
            options.add_argument(f"--user-data-dir={self.chrome_profile_path}")
            options.add_argument(f"--profile-directory={self.profile_name}")
            options.binary_location = self.chrome_binary_path

            # Pencere ve görünüm ayarları
            options.add_argument("--start-maximized")
            options.add_argument("--window-size=1920,1080")
            
            # Dil ve bölge ayarları
            options.add_argument("--lang=tr-TR")
            options.add_argument('--accept-lang=tr-TR')
            
            # İnsan benzeri davranış için performans ayarları
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_experimental_option("excludeSwitches", ["enable-automation", "enable-logging"])
            options.add_experimental_option("useAutomationExtension", False)
            
            # Çerezler ve önbellek ayarları
            options.add_argument("--enable-cookies")
            options.add_argument("--disable-web-security")
            options.add_argument("--allow-running-insecure-content")
            
            # Medya ayarları
            options.add_argument("--autoplay-policy=no-user-gesture-required")
            options.add_argument("--disable-notifications")
            options.add_argument("--disable-popup-blocking")
            
            # Performans optimizasyonları
            options.add_argument("--disable-gpu")
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-software-rasterizer")
            
            # Bellek yönetimi
            options.add_argument("--memory-pressure-off")
            options.add_argument("--disable-background-timer-throttling")
            options.add_argument("--disable-backgrounding-occluded-windows")
            options.add_argument("--disable-renderer-backgrounding")
            
            # Ağ ayarları
            options.add_argument("--disable-background-networking")
            options.add_argument("--metrics-recording-only")
            options.add_argument("--disable-prompt-on-repost")
            
            # Ek güvenlik ve gizlilik ayarları
            options.add_argument("--disable-sync")
            options.add_argument("--no-first-run")
            options.add_argument("--no-default-browser-check")
            options.add_argument("--disable-translate")
            
            # İnsan benzeri davranış için ek ayarlar
            options.add_argument("--touch-events=enabled")
            options.add_argument("--disable-client-side-phishing-detection")
            
            print("\n2. Chrome başlatılıyor...")
            # Manuel ChromeDriver yolu ile başlayalım (daha güvenilir)
            driver_started = False

            # Önce manuel ChromeDriver'ı deneyelim
            if os.path.exists(self.chrome_driver_path):
                try:
                    print(f"Manuel ChromeDriver deneniyor: {self.chrome_driver_path}")
                    service = Service(self.chrome_driver_path)
                    self.driver = webdriver.Chrome(service=service, options=options)
                    print("✓ Manuel ChromeDriver ile Chrome başlatıldı")
                    driver_started = True
                except Exception as manual_error:
                    print(f"Manuel ChromeDriver hatası: {manual_error}")
            else:
                print(f"Manuel ChromeDriver bulunamadı: {self.chrome_driver_path}")

            # Eğer manuel ChromeDriver çalışmazsa, otomatik indirme deneyelim
            if not driver_started:
                try:
                    print("ChromeDriverManager ile otomatik indirme deneniyor...")
                    chrome_version = "137.0.7151.68"  # Mevcut Chrome sürümünüz
                    download_url = f"https://storage.googleapis.com/chrome-for-testing-public/{chrome_version}/win64/chromedriver-win64.zip"
                    print(f"ChromeDriver indiriliyor: {download_url}")

                    with tempfile.TemporaryDirectory() as temp_dir:
                        zip_path = os.path.join(temp_dir, "chromedriver.zip")
                        response = requests.get(download_url)
                        response.raise_for_status()

                        with open(zip_path, 'wb') as f:
                            f.write(response.content)

                        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                            zip_ref.extractall(temp_dir)

                        chromedriver_exe = None
                        for root, _, files in os.walk(temp_dir):
                            for file in files:
                                if file == "chromedriver.exe":
                                    chromedriver_exe = os.path.join(root, file)
                                    break
                            if chromedriver_exe:
                                break

                        if chromedriver_exe:
                            print(f"✓ ChromeDriver bulundu: {chromedriver_exe}")
                            service = Service(chromedriver_exe)
                            self.driver = webdriver.Chrome(service=service, options=options)
                            print("✓ İndirilen ChromeDriver ile Chrome başlatıldı")
                            driver_started = True
                        else:
                            raise Exception("İndirilen zip'te chromedriver.exe bulunamadı")

                except Exception as download_error:
                    print(f"Otomatik indirme hatası: {download_error}")

            if not driver_started:
                raise Exception("Hiçbir ChromeDriver yöntemi çalışmadı!")

            # Sayfa yükleme ve etkileşim zaman aşımı ayarları
            self.driver.set_page_load_timeout(60)
            self.driver.implicitly_wait(10)

            # İnsan benzeri davranış için JavaScript enjeksiyonları
            stealth_js = """
                // WebDriver özelliğini gizle
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined
                });
                
                // Platform bilgisini ayarla
                Object.defineProperty(navigator, 'platform', {
                    get: () => 'Win32'
                });
                
                // Dil ayarlarını güncelle
                Object.defineProperty(navigator, 'language', {
                    get: () => 'tr-TR'
                });
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['tr-TR', 'tr', 'en-US', 'en']
                });
                
                // Plugins ve mimeTypes ekle
                const pluginArray = [];
                const mimeTypeArray = [];
                Object.defineProperty(navigator, 'plugins', {
                    get: () => pluginArray
                });
                Object.defineProperty(navigator, 'mimeTypes', {
                    get: () => mimeTypeArray
                });
                
                // Ekran çözünürlüğü ayarla
                Object.defineProperty(screen, 'width', {
                    get: () => 1920
                });
                Object.defineProperty(screen, 'height', {
                    get: () => 1080
                });
                Object.defineProperty(screen, 'availWidth', {
                    get: () => 1920
                });
                Object.defineProperty(screen, 'availHeight', {
                    get: () => 1040
                });
                Object.defineProperty(screen, 'colorDepth', {
                    get: () => 24
                });
                Object.defineProperty(screen, 'pixelDepth', {
                    get: () => 24
                });
            """
            
            try:
                self.driver.execute_script(stealth_js)
                print("✓ İnsan benzeri davranış için JavaScript enjeksiyonları yapıldı")
            except Exception as js_error:
                print(f"JavaScript enjeksiyon hatası: {js_error}")
            
            return self.driver
            
        except Exception as e:
            print(f"Chrome başlatılırken hata: {e}")
            return None
    
    def save_cookies(self, cookie_file=COOKIES_FILE):
        """Mevcut oturumdaki çerezleri kaydeder"""
        try:
            cookies = self.driver.get_cookies()
            pickle.dump(cookies, open(cookie_file, "wb"))
            print(f"✅ Cookies başarıyla kaydedildi: {cookie_file}")
            return True
        except Exception as e:
            print(f"❌ Cookies kaydedilirken hata oluştu: {e}")
            return False
    
    def load_cookies(self, cookie_file=COOKIES_FILE):
        """Kaydedilmiş çerezleri yükler"""
        if not os.path.exists(cookie_file):
            print(f"⚠️ Cookie dosyası bulunamadı: {cookie_file}")
            return False
        
        try:
            cookies = pickle.load(open(cookie_file, "rb"))
            for cookie in cookies:
                if 'expiry' in cookie:
                    del cookie['expiry']
                try:
                    self.driver.add_cookie(cookie)
                except:
                    continue
            print(f"✅ Cookies başarıyla yüklendi: {cookie_file}")
            return True
        except Exception as e:
            print(f"❌ Cookies yüklenirken hata oluştu: {e}")
            return False
    
    def check_login_status(self):
        """TikTok'a giriş durumunu kontrol eder"""
        try:
            # Doğrudan backstage sayfasına git, tiktok.com'a değil
            self.driver.get("https://live-backstage.tiktok.com/")
            time.sleep(3)
            
            # Login durumunu kontrol et (backstage sayfasındaki öğeler)
            try:
                # Backstage sayfasında giriş yapılmış mı kontrol et
                WebDriverWait(self.driver, 5).until(
                    EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'liveplatform-data-suite-card')] | //a[contains(@href, '/portal')]"))
                )
                print("✅ TikTok Backstage'e giriş yapılmış.")
                return True
            except:
                print("⚠️ TikTok'a giriş yapılmamış veya oturum süresi dolmuş.")
                return False
        except Exception as e:
            print(f"❌ Giriş durumu kontrol edilirken hata oluştu: {e}")
            return False
    
    def login_to_backstage(self):
        """Backstage'e giriş yapar (eğer otomatik giriş yapılmıyorsa)"""
        try:
            self.driver.get("https://live-backstage.tiktok.com/portal/data/data")
            
            # Sayfanın yüklenmesini bekle
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'liveplatform-data-suite-card')]"))
            )
            
            # Checkbox kontrolü (gerekiyorsa)
            try:
                checkbox = self.driver.find_element(By.XPATH, "//input[@class='semi-switch-native-control' and @role='switch']")
                if checkbox.is_selected():
                    self.driver.execute_script("arguments[0].click();", checkbox)
                    print("✅ Checkbox başarıyla kapatıldı.")
                else:
                    print("⚠️ Checkbox zaten kapalı.")
            except:
                print("⚠️ Checkbox bulunamadı, devam ediliyor.")
            
            print("✅ Backstage sayfasına başarıyla giriş yapıldı.")
            return True
        except Exception as e:
            print(f"❌ Backstage'e giriş yapılırken hata oluştu: {e}")
            return False
    
    def select_date(self, date_str):
        """Verilen tarihi Backstage tarih seçicide ayarlar (URL parametreleri ile)"""
        try:
            # Tarih stringini datetime nesnesine çevir
            date_obj = datetime.strptime(date_str, "%d.%m.%Y")
            
            # İlgili günün başlangıç ve bitiş zamanını Unix timestamp olarak hesapla
            # Türkiye saati (UTC+3) göz önünde bulundurulmalı
            # TikTok'un sıfırıncı saat UTC'yi baz alıyor olabilir
            # start_of_day = int(datetime(date_obj.year, date_obj.month, date_obj.day, 0, 0, 0).timestamp())
            # end_of_day = int(datetime(date_obj.year, date_obj.month, date_obj.day, 23, 59, 59).timestamp())
            
            # Günün başlangıcını ve sonunu gece yarısı olarak ayarla (UTC baz alınarak)
            # UTC+3 saati için, Türkiye'de gece yarısı UTC'de 21:00'dır (önceki gün)
            start_of_day = int(datetime(date_obj.year, date_obj.month, date_obj.day, 0, 0, 0).timestamp()) + (3 * 3600)  # UTC bazlı başlangıç
            end_of_day = int(datetime(date_obj.year, date_obj.month, date_obj.day, 23, 59, 59).timestamp()) + (3 * 3600)  # UTC bazlı bitiş
            
            print(f"✓ {date_str} için Unix zaman damgaları: Başlangıç={start_of_day}, Bitiş={end_of_day}")
            
            # URL oluştur ve doğrudan o URL'ye git
            page_url = f"https://live-backstage.tiktok.com/portal/data/data?anchorID=&endTime={end_of_day}&startTime={start_of_day}"
            self.driver.get(page_url)
            print(f"✓ Tarih URL'si ile doğrudan gezinildi: {page_url}")
            
            time.sleep(3)  # Sayfanın yüklenmesi için bekle
            
            # Sayfa başı gösterimi 100'e çıkar
            try:
                # jQuery benzeri ":contains" seçicileri vanilla JavaScript'te çalışmaz
                # Daha geçerli CSS seçicileri kullanalım
                page_size_selectors = [
                    "div.semi-select-selection", 
                    "div.semi-select",
                    "div[aria-haspopup='listbox']",
                    "div[tabindex='0'][aria-controls]",
                    "div[role='combobox']",
                    "span.semi-select-selection-text"
                ]
                
                # JavaScript ile elementi bul
                page_size_element = None
                for selector in page_size_selectors:
                    try:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        for element in elements:
                            element_text = element.text.strip()
                            if "Sayfa başı" in element_text or "öğe" in element_text:
                                page_size_element = element
                                print(f"✓ Sayfa başı gösterim seçicisi bulundu: '{element_text}'")
                                break
                        if page_size_element:
                            break
                    except:
                        continue
                
                # Eğer seçiciyle bulunamazsa, text içeriğiyle ara
                if not page_size_element:
                    # Text içeriği ile arama yöntemi
                    page_size_js = self.driver.execute_script("""
                        const allElements = document.querySelectorAll('div, span');
                        for (const el of allElements) {
                            if (el.textContent.includes('Sayfa başı') || el.textContent.includes('öğe: 20')) {
                                return el;
                            }
                        }
                        return null;
                    """)
                    
                    if page_size_js:
                        page_size_element = page_size_js
                        print("✓ JavaScript ile sayfa başı gösterim seçicisi bulundu.")
                    
                # Seçiciye tıkla
                if page_size_element:
                    self.driver.execute_script("arguments[0].click();", page_size_element)
                    time.sleep(1.5)
                    
                    # 100 seçeneğine tıkla (farklı yöntemler dene)
                    option_clicked = False
                    
                    # JavaScript ile 100 seçeneğini bul ve tıkla
                    option_clicked = self.driver.execute_script("""
                        const options = document.querySelectorAll('div[role="option"], .semi-select-option, div[title="100"]');
                        for (const option of options) {
                            if (option.textContent.includes('100')) {
                                option.click();
                                return true;
                            }
                        }
                        return false;
                    """)
                    
                    if option_clicked:
                        print("✓ JavaScript ile 100 gösterim seçeneği seçildi.")
                        time.sleep(2)
                    else:
                        print("⚠️ 100 gösterim seçeneği bulunamadı.")
                else:
                    print("⚠️ Sayfa başı gösterim seçicisi bulunamadı.")
            except Exception as e:
                print(f"⚠️ Sayfa başı gösterim sayısı değiştirilemedi: {e}")
            
            # Checkbox durumunu kontrol et ve gerekiyorsa kapat
            try:
                checkbox = self.driver.find_element(By.XPATH, "//input[@class='semi-switch-native-control' and @role='switch']")
                if checkbox.is_selected():
                    self.driver.execute_script("arguments[0].click();", checkbox)
                    time.sleep(2)
                    print("✅ Checkbox başarıyla kapatıldı.")
                else:
                    print("⚠️ Checkbox zaten kapalı.")
            except:
                print("⚠️ Checkbox bulunamadı, devam ediliyor.")
            
            # Verilerin yüklenmesini bekle
            time.sleep(5)
            
            # Ekran görüntüsü al - debug için
            try:
                screenshot_path = f"date_selection_{date_str.replace('.', '_')}.png"
                self.driver.save_screenshot(screenshot_path)
                print(f"✓ Tarih seçimi ekran görüntüsü kaydedildi: {screenshot_path}")
            except:
                pass
            
            return True
        except Exception as e:
            print(f"❌ Tarih seçerken hata oluştu: {e}")
            # Hata oluştuğunda ekran görüntüsü alalım
            try:
                screenshot_path = f"error_screenshot_{date_str.replace('.', '_')}.png"
                self.driver.save_screenshot(screenshot_path)
                print(f"✓ Hata ekran görüntüsü kaydedildi: {screenshot_path}")
            except:
                pass
            return False
    
    def get_daily_live_data(self, date_str):
        """Belirli bir gün için canlı yayın verilerini çeker"""
        try:
            # Tarihi seç (Artık URL ile doğrudan yönlendirme yapılıyor)
            if not self.select_date(date_str):
                return False
            
            print(f"🔍 {date_str} tarihi için veri toplama başlıyor...")
            
            # Modal kapatma işlemleri - her ihtimale karşı
            try:
                # Modal varsa kapat
                time.sleep(1)
                modal_selectors = [
                    "button[aria-label='Close']", 
                    ".tiktok-dialog__close",
                    ".tiktok-modal__close-icon",
                    "div[data-e2e='modal-close-inner-button']",
                    "div[role='dialog'] button"
                ]
                
                for selector in modal_selectors:
                    try:
                        close_buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        if close_buttons:
                            print(f"ℹ️ Modal kapatılıyor: {selector} seçici ile {len(close_buttons)} buton bulundu")
                            for button in close_buttons:
                                if button.is_displayed():
                                    self.driver.execute_script("arguments[0].click();", button)
                                    print("✓ Modal kapatıldı.")
                                    time.sleep(1)
                                    break
                    except:
                        continue
                        
                # ESC tuşu ile kapatmayı dene
                try:
                    webdriver.ActionChains(driver=self.driver).send_keys(Keys.ESCAPE).perform()
                    time.sleep(1)
                    print("✓ ESC tuşu ile modal kapatma denendi.")
                except:
                    pass
            except Exception as e:
                print(f"⚠️ Modal kapatma işleminde hata: {e}")
            
            # Sonuçları saklamak için dictionary
            daily_data = {}
            current_page = 1
            
            # URL kontrolü - doğru tarihi içeriyor mu?
            url_check_ok = False
            max_url_retries = 3
            url_retries = 0
            
            while not url_check_ok and url_retries < max_url_retries:
                try:
                    current_url = self.driver.current_url
                    print(f"ℹ️ Kontrol URL: {current_url}")
                    
                    # URL'de tarih parametreleri var mı kontrol et
                    if 'startTime=' in current_url and 'endTime=' in current_url:
                        print("✓ URL tarih parametreleri içeriyor.")
                        
                        # Tarih değerlerini URL'den çıkartıp kontrol et
                        try:
                            # URL'den Unix timestamp değerlerini çıkart
                            import re
                            start_match = re.search(r'startTime=(\d+)', current_url)
                            end_match = re.search(r'endTime=(\d+)', current_url)
                            
                            if start_match and end_match:
                                url_start_time = int(start_match.group(1))
                                url_end_time = int(end_match.group(1))
                                
                                # Beklenen tarih değerleri
                                expected_date = datetime.strptime(date_str, "%d.%m.%Y")
                                
                                # UTC bazlı timestamp hesapla (TikTok'un kullandığı format)
                                expected_start = int(datetime(expected_date.year, expected_date.month, expected_date.day, 0, 0, 0).timestamp()) + (3 * 3600)
                                expected_end = int(datetime(expected_date.year, expected_date.month, expected_date.day, 23, 59, 59).timestamp()) + (3 * 3600)
                                
                                # Fark makul sınırlar içinde mi?
                                start_diff = abs(url_start_time - expected_start)
                                end_diff = abs(url_end_time - expected_end)
                                
                                if start_diff > 86400 or end_diff > 86400:  # 1 günden fazla fark varsa
                                    print(f"⚠️ Tarih değerleri beklenenden çok farklı. Beklenen başlangıç: {expected_start}, URL'deki: {url_start_time}")
                                    raise Exception("Tarih değerleri beklenenden çok farklı")
                                else:
                                    print("✓ URL'deki tarih değerleri doğru aralıkta.")
                                    url_check_ok = True
                            else:
                                print("⚠️ URL'de tarih parametreleri bulunamadı.")
                                raise Exception("URL'de tarih parametreleri bulunamadı")
                        
                        except Exception as parse_error:
                            print(f"⚠️ URL tarih değerleri analiz edilirken hata: {parse_error}")
                            if url_retries < max_url_retries - 1:
                                print("Tarih seçimi tekrar deneniyor...")
                            
                    else:
                        print("⚠️ URL tarih parametreleri içermiyor, sayfa yenileniyor...")
                    
                    # Eğer URL kontrolü başarısızsa ve deneme hakkımız varsa yeniden tarih seç
                    if not url_check_ok:
                        url_retries += 1
                        if url_retries < max_url_retries:
                            print(f"({url_retries}/{max_url_retries}) Tarih seçimi tekrar deneniyor...")
                            if not self.select_date(date_str):
                                print(f"❌ Tarih seçme tekrar başarısız oldu - deneme {url_retries}")
                            time.sleep(3)  # Sayfanın yüklenmesi için bekle
                        else:
                            print(f"❌ Maksimum deneme sayısına ulaşıldı ({max_url_retries}). Mevcut URL ile devam ediliyor.")
                            url_check_ok = True  # Zorunlu olarak devam et
                
                except Exception as e:
                    print(f"⚠️ URL kontrolünde hata: {e}")
                    url_retries += 1
                    if url_retries >= max_url_retries:
                        print(f"❌ Maksimum deneme sayısına ulaşıldı ({max_url_retries}). Mevcut durumla devam edilecek.")
                        url_check_ok = True  # Zorunlu olarak devam et
            
            # Sayfa başı gösterimi 100'e çıkar
            try:
                # jQuery benzeri ":contains" seçicileri vanilla JavaScript'te çalışmaz
                # Daha geçerli CSS seçicileri kullanalım
                page_size_selectors = [
                    "div.semi-select-selection", 
                    "div.semi-select",
                    "div[aria-haspopup='listbox']",
                    "div[tabindex='0'][aria-controls]",
                    "div[role='combobox']",
                    "span.semi-select-selection-text"
                ]
                
                # JavaScript ile elementi bul
                page_size_element = None
                for selector in page_size_selectors:
                    try:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        for element in elements:
                            element_text = element.text.strip()
                            if "Sayfa başı" in element_text or "öğe" in element_text:
                                page_size_element = element
                                print(f"✓ Sayfa başı gösterim seçicisi bulundu: '{element_text}'")
                                break
                        if page_size_element:
                            break
                    except:
                        continue
                
                # Eğer seçiciyle bulunamazsa, text içeriğiyle ara
                if not page_size_element:
                    # Text içeriği ile arama yöntemi
                    page_size_js = self.driver.execute_script("""
                        const allElements = document.querySelectorAll('div, span');
                        for (const el of allElements) {
                            if (el.textContent.includes('Sayfa başı') || el.textContent.includes('öğe: 20')) {
                                return el;
                            }
                        }
                        return null;
                    """)
                    
                    if page_size_js:
                        page_size_element = page_size_js
                        print("✓ JavaScript ile sayfa başı gösterim seçicisi bulundu.")
                    
                # Seçiciye tıkla
                if page_size_element:
                    self.driver.execute_script("arguments[0].click();", page_size_element)
                    time.sleep(1.5)
                    
                    # 100 seçeneğine tıkla (farklı yöntemler dene)
                    option_clicked = False
                    
                    # JavaScript ile 100 seçeneğini bul ve tıkla
                    option_clicked = self.driver.execute_script("""
                        const options = document.querySelectorAll('div[role="option"], .semi-select-option, div[title="100"]');
                        for (const option of options) {
                            if (option.textContent.includes('100')) {
                                option.click();
                                return true;
                            }
                        }
                        return false;
                    """)
                    
                    if option_clicked:
                        print("✓ JavaScript ile 100 gösterim seçeneği seçildi.")
                        time.sleep(2)
                    else:
                        print("⚠️ 100 gösterim seçeneği bulunamadı.")
                else:
                    print("⚠️ Sayfa başı gösterim seçicisi bulunamadı.")
            except Exception as e:
                print(f"⚠️ Sayfa başı gösterim sayısı değiştirilemedi: {e}")
            
            # Checkbox durumunu kontrol et ve gerekiyorsa kapat
            try:
                checkbox = self.driver.find_element(By.XPATH, "//input[@class='semi-switch-native-control' and @role='switch']")
                if checkbox.is_selected():
                    self.driver.execute_script("arguments[0].click();", checkbox)
                    time.sleep(2)
                    print("✅ Checkbox başarıyla kapatıldı.")
                else:
                    print("⚠️ Checkbox zaten kapalı.")
            except:
                print("⚠️ Checkbox bulunamadı, devam ediliyor.")
            
            # Verilerin yüklenmesini bekle
            time.sleep(5)
            
            # Ekran görüntüsü al - debug için
            try:
                screenshot_path = f"date_selection_{date_str.replace('.', '_')}.png"
                self.driver.save_screenshot(screenshot_path)
                print(f"✓ Tarih seçimi ekran görüntüsü kaydedildi: {screenshot_path}")
            except:
                pass
            
            return True
        except Exception as e:
            print(f"❌ Tarih seçerken hata oluştu: {e}")
            # Hata oluştuğunda ekran görüntüsü alalım
            try:
                screenshot_path = f"error_screenshot_{date_str.replace('.', '_')}.png"
                self.driver.save_screenshot(screenshot_path)
                print(f"✓ Hata ekran görüntüsü kaydedildi: {screenshot_path}")
            except:
                pass
            return False
    
    def _get_date_range(self):
        """Bir önceki Pazartesi'den şimdiki Pazartesi'ye tarih aralığını döndürür."""
        today = datetime.today()
        print(f"Bugünkü tarih: {today.strftime('%d.%m.%Y')}")
        
        # Haftanın günü: Pazartesi=0, Salı=1, ..., Pazar=6
        current_weekday = today.weekday()
        print(f"Haftanın günü: {current_weekday} (0=Pazartesi, 6=Pazar)")
        
        # En yakın (geçmiş veya bugün) Pazartesi
        current_monday = today - timedelta(days=current_weekday)
        # Bir önceki Pazartesi
        previous_monday = current_monday - timedelta(days=7)
        
        start_date = previous_monday
        # Bitiş tarihini Pazar olarak ayarlayalım (rapor genellikle tamamlanmış haftayı kapsar)
        end_date = current_monday - timedelta(days=1) 

        print(f"Otomatik hesaplanan hafta: {start_date.strftime('%d.%m.%Y')} - {end_date.strftime('%d.%m.%Y')}")
        
        # Test tarih aralığı kaldırıldı - Artık gerçek tarihleri kullanıyoruz

        return start_date.strftime("%d.%m.%Y"), end_date.strftime("%d.%m.%Y")

    def create_excel_report(self, output_file):
        """Excel raporu oluştur veya güncelle"""
        try:
            # Excel dosyasını aç (varsa güncelle, yoksa yeni oluştur)
            try:
                workbook = openpyxl.load_workbook(output_file)
                sheet = workbook.active
                print(f"✓ Mevcut Excel dosyası açıldı: {output_file}")
            except FileNotFoundError:
                print(f"⚠️ Excel dosyası bulunamadı, yeni oluşturuluyor: {output_file}")
                workbook = openpyxl.Workbook()
                sheet = workbook.active
            
            # Stil tanımlamaları
            header_font = Font(bold=True)
            header_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")
            center_alignment = Alignment(horizontal='center', vertical='center')
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
            
            # Hafta başlıkları için sütun eşleştirmeleri
            week_columns = {
                'Week 1': {
                    'live_days': 'E',    # Haftada Kaç Gün Yayın Açtı
                    'live_hours': 'F',   # Haftada Kaç Saat Yayın Açtı
                    'video_count': 'G',  # Haftada en az 4 video paylaşımı
                    'pk_count': 'H'      # Haftada Kaç PK yaptı
                },
                'Week 2': {
                    'live_days': 'I',
                    'live_hours': 'J',
                    'video_count': 'K',
                    'pk_count': 'L'
                },
                'Week 3': {
                    'live_days': 'M',
                    'live_hours': 'N',
                    'video_count': 'O',
                    'pk_count': 'P'
                }
            }
            
            # A, B, C, D sütunlarına dokunma (mevcut değerleri koru)
            
            # Haftalık verileri doldur
            for week, columns in week_columns.items():
                for data_type, column in columns.items():
                    # Eğer veriler varsa doldur
                    if week in self.data and data_type in self.data[week]:
                        data = self.data[week][data_type]
                        # Kullanıcı adlarını A sütunundan al
                        for row in range(2, sheet.max_row + 1):
                            username = sheet.cell(row=row, column=1).value
                            if username and username in data:
                                cell = sheet[f"{column}{row}"]
                                value = data[username]
                                
                                # Video sayısı için boolean değer (zaten "Evet/Hayır" olarak geliyor)
                                if data_type == 'video_count':
                                    # Value zaten "Evet" veya "Hayır" string'i olarak geliyor
                                    if value in ["Evet", "Hayır"]:
                                        cell.value = value
                                    else:
                                        # Eğer farklı bir değer gelirse (integer vs.) dönüştür
                                        try:
                                            video_count = int(value) if value is not None else 0
                                            cell.value = "Evet" if video_count >= 4 else "Hayır"
                                        except (ValueError, TypeError):
                                            print(f"⚠️ Video sayısı dönüştürme hatası: {username} = {value}")
                                            cell.value = "Hayır"
                                elif data_type == 'live_hours':
                                    # Saat değerini "Xs Ydk" formatına çevir
                                    try:
                                        hours_decimal = float(value) if value is not None else 0
                                        hours = int(hours_decimal)
                                        minutes = int((hours_decimal - hours) * 60)

                                        if hours > 0 and minutes > 0:
                                            cell.value = f"{hours}s {minutes}dk"
                                        elif hours > 0:
                                            cell.value = f"{hours}s"
                                        elif minutes > 0:
                                            cell.value = f"{minutes}dk"
                                        else:
                                            cell.value = 0  # Sadece "0" yaz, "0dk" değil
                                    except (ValueError, TypeError):
                                        print(f"⚠️ Saat dönüştürme hatası: {username} = {value}")
                                        cell.value = 0  # Sadece "0" yaz, "0dk" değil
                                else:
                                    cell.value = value
                                
                                cell.alignment = center_alignment
                                cell.border = thin_border
            
            # Dosyayı kaydet
            workbook.save(output_file)
            print(f"✅ Excel raporu güncellendi: {output_file}")
            return True
            
        except Exception as e:
            print(f"❌ Excel raporu oluşturulurken hata: {e}")
            return False

    def close(self):
        """Chrome sürücüsünü kapatır"""
        if self.driver:
            self.driver.quit()
            print("✅ Chrome sürücüsü kapatıldı.")
    
    def run(self, start_date, end_date, output_file):
        """Ana çalıştırma fonksiyonu"""
        try:
            print("\n=== TikTok Veri Toplama İşlemi Başlıyor ===")
            
            print("\n--- Adım 1: Chrome Başlatılıyor ---")
            if not self.setup_driver():
                print("❌ Chrome sürücüsü başlatılamadı.")
                return False
            
            print("\n--- Adım 2: LiveBackstage Verileri Toplanıyor ---")
            # LiveBackstage'den verileri çek
            weekly_data = self.get_weekly_live_data(start_date, end_date)
            if not weekly_data:
                print("❌ LiveBackstage verileri toplanamadı.")
                self.close()
                return False
            
            # Kullanıcıya TikTok profil kontrolü yapılıp yapılmayacağını sor
            print("\n--- Adım 3: TikTok Profil Kontrolleri ---")
            print("Bu adımda her yayıncının TikTok profilinde haftalık 4+ video paylaşımı kontrolü yapılır.")
            print("Bu işlem uzun sürebilir ve CAPTCHA çıkabilir.")

            while True:
                user_choice = input("\nTikTok profil kontrolleri yapılsın mı? (evet/hayır): ").lower().strip()
                if user_choice in ['evet', 'e', 'yes', 'y']:
                    do_profile_check = True
                    break
                elif user_choice in ['hayır', 'h', 'no', 'n']:
                    do_profile_check = False
                    break
                else:
                    print("⚠️ Lütfen 'evet' veya 'hayır' yazın.")

            if do_profile_check:
                print("\n--- TikTok Profil Kontrolleri Yapılıyor ---")
                # Her kullanıcı için profil kontrolü yap
                for username in weekly_data['live_days'].keys():
                    print(f"\n🔍 {username} için profil kontrolü yapılıyor...")
                    has_4_videos = self.check_weekly_video_uploads(username, start_date, end_date)
                    weekly_data['video_count'][username] = "Evet" if has_4_videos else "Hayır"
            else:
                print("\n--- TikTok Profil Kontrolleri Atlandı ---")
                print("ℹ️ Video kontrolleri yapılmadı, tüm kullanıcılar için 'Hayır' değeri atanacak.")
                # Tüm kullanıcılar için "Hayır" değeri ata
                for username in weekly_data['live_days'].keys():
                    weekly_data['video_count'][username] = "Hayır"
            
            print("\n--- Adım 4: Excel Raporu Güncelleniyor ---")
            # Haftalık verileri sakla ve Excel'e yaz
            current_week = "Week 1"  # Dinamik olarak belirlenebilir
            self.data[current_week] = weekly_data
            
            if not self.create_excel_report(output_file):
                print("❌ Excel raporu güncellenirken hata oluştu.")
                self.close()
                return False
            
            print("\n✅ Tüm işlemler başarıyla tamamlandı!")
            self.close()
            return True
            
        except Exception as e:
            print(f"❌ Program çalışırken hata oluştu: {e}")
            self.close()
            return False

    def process_date_range(self, start_date_str, end_date_str):
        """Belirli bir tarih aralığı için tüm günleri işler"""
        try:
            # Tarih formatı: DD.MM.YYYY
            start_date = datetime.strptime(start_date_str, "%d.%m.%Y")
            end_date = datetime.strptime(end_date_str, "%d.%m.%Y")
            
            current_date = start_date
            while current_date <= end_date:
                date_str = current_date.strftime("%d.%m.%Y")
                print(f"\n🗓️ {date_str} tarihine ait verileri işleniyor...")
                
                self.get_daily_live_data(date_str)
                
                current_date += timedelta(days=1)
            
            return True
        except Exception as e:
            print(f"❌ Tarih aralığı işlenirken hata oluştu: {e}")
            return False

    def get_violations_by_date(self, report_start_date_str, report_end_date_str):
        """İhlal geçmişi sayfasından, belirtilen hafta içindeki ihlalleri tarihe göre gruplayarak çeker."""
        print("\n🚫 İhlal verileri çekiliyor...")
        violation_url = "https://live-backstage.tiktok.com/portal/anchor/violation"
        
        try:
            self.driver.get(violation_url)
            print(f"✓ İhlal sayfasına gidildi: {violation_url}")
            time.sleep(3)  # Sayfanın yüklenmesi için bekle
            
            # Sayfa başı gösterimi 100'e çıkar
            try:
                # JavaScript ile sayfa başı gösterim seçicisini bul
                page_size_element = None
                
                # Text içeriği ile arama
                page_size_js = self.driver.execute_script("""
                    const allElements = document.querySelectorAll('div, span');
                    for (const el of allElements) {
                        if (el.textContent.includes('Sayfa başı') || el.textContent.includes('öğe: 20')) {
                            return el;
                        }
                    }
                    return null;
                """)
                
                if page_size_js:
                    page_size_element = page_size_js
                    print("✓ JavaScript ile sayfa başı gösterim seçicisi bulundu.")
                
                # Eğer text içeriğiyle bulunamazsa, CSS seçicilerle dene
                if not page_size_element:
                    # jQuery benzeri ":contains" seçicileri yerine geçerli CSS seçicileri kullanalım
                    page_size_selectors = [
                        "div.semi-select-selection", 
                        "div.semi-select",
                        "div[aria-haspopup='listbox']",
                        "div[tabindex='0'][aria-controls]",
                        "div[role='combobox']",
                        "span.semi-select-selection-text"
                    ]
                    
                    for selector in page_size_selectors:
                        try:
                            elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                            for element in elements:
                                element_text = element.text.strip()
                                if "Sayfa başı" in element_text or "öğe" in element_text:
                                    page_size_element = element
                                    print(f"✓ Sayfa başı gösterim seçicisi bulundu: '{element_text}'")
                                    break
                            if page_size_element:
                                break
                        except:
                            continue
                
                # Seçiciye tıkla
                if page_size_element:
                    self.driver.execute_script("arguments[0].click();", page_size_element)
                    time.sleep(1.5)
                    
                    # 100 seçeneğine tıkla
                    option_clicked = self.driver.execute_script("""
                        const options = document.querySelectorAll('div[role="option"], .semi-select-option, div[title="100"]');
                        for (const option of options) {
                            if (option.textContent.includes('100')) {
                                option.click();
                                return true;
                            }
                        }
                        return false;
                    """)
                    
                    if option_clicked:
                        print("✓ JavaScript ile 100 gösterim seçeneği seçildi.")
                        time.sleep(2)
                    else:
                        print("⚠️ 100 gösterim seçeneği bulunamadı.")
                else:
                    print("⚠️ Sayfa başı gösterim seçicisi bulunamadı.")
            except Exception as e:
                print(f"⚠️ İhlal sayfası sayfa başı gösterim sayısı değiştirilemedi: {e}")
            
            report_start_date = datetime.strptime(report_start_date_str, "%d.%m.%Y").date()
            report_end_date = datetime.strptime(report_end_date_str, "%d.%m.%Y").date()
            print(f"ℹ️ Rapor Haftası Aralığı: {report_start_date} - {report_end_date}")

            violations_found = {} 
            current_page = 1
            stop_processing = False # Optimizasyon bayrağı

            while not stop_processing:
                print(f"📄 İhlal Sayfası {current_page} işleniyor...")
                
                try:
                    WebDriverWait(self.driver, 20).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "table tbody tr"))
                    )
                    time.sleep(3)
                except TimeoutException:
                    print("⚠️ İhlal tablosu yüklenemedi veya boş.")
                    # Eğer ilk sayfada tablo yoksa veya boşsa zaten hiç kayıt yoktur.
                    if current_page == 1: print("Hiç ihlal kaydı bulunamadı.") 
                    stop_processing = True # Başka sayfa arama
                    continue # While döngüsünün başına git (stop_processing kontrolü ile çıkacak)
                
                rows = self.driver.find_elements(By.XPATH, "//table/tbody/tr[@role='row']") # Daha spesifik satır seçici
                if not rows:
                    print("⚠️ Tabloda veri satırı bulunamadı (XPath ile).")
                    if current_page == 1: print("Hiç ihlal kaydı bulunamadı.")
                    stop_processing = True
                    continue

                print(f"-> {len(rows)} satır bulundu.")
                row_processed_count = 0
                for row_index, row in enumerate(rows):
                    if stop_processing: break # Eğer önceki satırda durdurma kararı alındıysa bu sayfayı bitir
                    
                    username = "[Bulunamadı]"
                    violation_time_str = "[Bulunamadı]"
                    try:
                        # Kullanıcı Adı (Doğrudan td metni - colindex=1)
                        username_element = row.find_element(By.XPATH, ".//td[@role='gridcell'][@aria-colindex='1']")
                        raw_username = username_element.text.strip()
                        # Sadece ilk satırı (asıl kullanıcı adını) al
                        username = raw_username.split('\n')[0]
                        
                        # Ceza Süresi (colindex=7, td metni)
                        violation_time_element = row.find_element(By.XPATH, ".//td[@role='gridcell'][@aria-colindex='7']") 
                        violation_time_str = violation_time_element.text.strip()
                        
                        print(f"  -> Satır {row_index+1}: Kullanıcı={username}, CezaSüresiText='{violation_time_str}'")

                        if not username or not violation_time_str or username == "-" or not violation_time_str:
                             print("     -> Geçersiz veri, atlanıyor.")
                             continue 

                        try:
                            violation_dt = datetime.strptime(violation_time_str, "%d.%m.%Y %H:%M:%S")
                            violation_date = violation_dt.date()
                            violation_date_str = violation_date.strftime("%d.%m.%Y")
                        except ValueError:
                            print(f"⚠️ Geçersiz tarih formatı: '{violation_time_str}' - Satır atlanıyor.")
                            continue

                        # Optimizasyon: Eğer ihlal tarihi rapor başlangıcından eskiyse, dur
                        if violation_date < report_start_date:
                            print(f"ℹ️ Rapor haftasından ({report_start_date}) eski tarih bulundu ({violation_date}). İşlem durduruluyor.")
                            stop_processing = True
                            break # Bu for döngüsünü bitir

                        # İhlal tarihi rapor haftası içinde mi?
                        if violation_date <= report_end_date: 
                            if violation_date_str not in violations_found:
                                violations_found[violation_date_str] = set()
                            # Temizlenmiş kullanıcı adını ekle
                            violations_found[violation_date_str].add(username)
                            print(f"    ✓ İhlal rapor haftası içinde: {username} - {violation_date_str}")
                            row_processed_count += 1
                            
                    except NoSuchElementException as e_nse:
                        print(f"❌ Satır {row_index+1} işlenirken element bulunamadı: {e_nse}")
                    except Exception as e_row:
                        print(f"❌ Satır {row_index+1} işlenirken genel hata oluştu: {e_row}")
                        continue
                
                print(f"-> Sayfa {current_page} işlendi, {row_processed_count} ilgili ihlal bulundu.")
                if stop_processing: break # While döngüsünü de bitir

                # Sonraki sayfa (Eğer optimizasyon nedeniyle durmadıysak) 
                # Artık Next butonu kullanıyoruz, li[@aria-label='Next']
                try:
                    # Doğru XPath: //li[@aria-label='Next']
                    next_page_li = WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, "//li[@aria-label='Next']")) 
                    )
                    # Etkin olup olmadığını aria-disabled özniteliği ile kontrol et
                    is_disabled = next_page_li.get_attribute('aria-disabled') == 'true'
                    
                    if not is_disabled:
                        print("-> Sonraki sayfaya geçiliyor...")
                        self.driver.execute_script("arguments[0].click();", next_page_li) # JS click
                        time.sleep(5) # Daha uzun bekleme (100 gösterim için)
                        current_page += 1
                    else:
                        print("✓ Sonraki sayfa butonu (li) devre dışı (aria-disabled='true'), son sayfa.")
                        stop_processing = True 
                except TimeoutException:
                    print("✓ Sonraki sayfa butonu (li[@aria-label='Next']) bulunamadı (Timeout), son sayfa.")
                    stop_processing = True
                except Exception as e_page:
                    print(f"❌ Sonraki sayfaya geçerken hata: {e_page}")
                    stop_processing = True 

            print(f"✅ İhlal verileri çekme tamamlandı. Toplam {len(violations_found)} farklı tarihte ihlal bulundu.")
            self.violations_by_date = violations_found 
            return True

        except Exception as e:
            print(f"❌ İhlal verileri çekilirken genel bir hata oluştu: {e}")
            import traceback
            print(traceback.format_exc())
            return False

    def convert_duration_to_hours(self, duration_text):
        """'Xs Ydk Zsn' formatındaki süreyi saat cinsine çevirir"""
        try:
            hours = 0
            minutes = 0
            seconds = 0
            
            # Boş veya 0sn ise 0 döndür
            if not duration_text or duration_text == '0sn':
                return 0
                
            parts = duration_text.split()
            for part in parts:
                if 's' in part and 'sn' not in part:  # Saat
                    hours = float(part.replace('s', ''))
                elif 'dk' in part:  # Dakika
                    minutes = float(part.replace('dk', ''))
                elif 'sn' in part:  # Saniye
                    seconds = float(part.replace('sn', ''))
            
            # Toplam saati hesapla
            total_hours = hours + (minutes / 60) + (seconds / 3600)
            return round(total_hours, 2)
            
        except Exception as e:
            print(f"⚠️ Süre dönüşümünde hata: {duration_text} -> {e}")
            return 0

    def get_weekly_live_data(self, start_date_str, end_date_str):
        """Belirli bir hafta için canlı yayın verilerini çeker"""
        try:
            # Tarihleri datetime objesine çevir
            start_date = datetime.strptime(start_date_str, "%d.%m.%Y")
            end_date = datetime.strptime(end_date_str, "%d.%m.%Y")
            
            print(f"🔍 {start_date_str} - {end_date_str} tarihleri arası haftalık veri toplama başlıyor...")
            
            # URL parametreleri için timestamp hesapla (UTC+3 için)
            start_timestamp = int(start_date.timestamp()) + (3 * 3600)  # Gün başlangıcı
            end_timestamp = int(end_date.timestamp()) + (3 * 3600) + (24 * 3600) - 1  # Gün sonu
            
            # LiveBackstage URL'sini oluştur ve doğrudan git
            url_with_params = f"https://live-backstage.tiktok.com/portal/data/data?anchorID=&endTime={end_timestamp}&startTime={start_timestamp}"
            self.driver.get(url_with_params)
            time.sleep(3)
            
            # Haftalık veri saklamak için dictionary
            weekly_data = {
                'live_days': {},      # Kaç gün yayın açtı
                'live_hours': {},     # Toplam yayın süresi (saat)
                'pk_count': {},       # PK sayısı
                'video_count': {}     # Video sayısı (TikTok profillerinden çekilecek)
            }
            
            # Sayfa başı gösterimi 100'e çıkar
            try:
                page_size_selectors = [
                    "div.semi-select-selection", 
                    "div.semi-select",
                    "div[aria-haspopup='listbox']",
                    "div[tabindex='0'][aria-controls]",
                    "div[role='combobox']"
                ]
                
                page_size_changed = False
                for selector in page_size_selectors:
                    try:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        for element in elements:
                            if "Sayfa başı" in element.text or "öğe" in element.text:
                                self.driver.execute_script("arguments[0].click();", element)
                                time.sleep(1.5)
                                
                                # 100 seçeneğine tıkla
                                option_clicked = self.driver.execute_script("""
                                    const options = document.querySelectorAll('div[role="option"], .semi-select-option');
                                    for (const option of options) {
                                        if (option.textContent.includes('100')) {
                                            option.click();
                                            return true;
                                        }
                                    }
                                    return false;
                                """)
                                
                                if option_clicked:
                                    print("✓ Sayfa başı gösterim sayısı 100 olarak ayarlandı")
                                    page_size_changed = True
                                    time.sleep(2)
                                    break
                    except:
                        continue
                    
                    if page_size_changed:
                        break
                    
            except Exception as e:
                print(f"⚠️ Sayfa başı gösterim sayısı değiştirilemedi: {e}")
            
            # Tablonun yüklenmesini bekle
            try:
                WebDriverWait(self.driver, 15).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "table tbody tr"))
                )
            except TimeoutException:
                print("⚠️ Tablo yüklenemedi veya boş.")
                return None
            
            # Tablodaki tüm satırları bul
            time.sleep(2)
            rows = self.driver.find_elements(By.CSS_SELECTOR, "table tbody tr")
            if not rows:
                print("⚠️ Tabloda veri satırı bulunamadı.")
                return None
            
            total_rows = len(rows)
            print(f"✓ Toplam {total_rows} yayıncı bulundu.")
            
            # Haftalık veri saklamak için dictionary
            weekly_data = {
                'live_days': {},      # Kaç gün yayın açtı
                'live_hours': {},     # Toplam yayın süresi (saat)
                'pk_count': {},       # PK sayısı
                'video_count': {}     # Video sayısı (TikTok profillerinden çekilecek)
            }
            
            # Her satırı işle
            for row_idx, row in enumerate(rows):
                try:
                    # Kullanıcı adını al
                    username_element = row.find_element(By.XPATH, ".//td[@role='gridcell'][@aria-colindex='1']")
                    username = username_element.text.strip().split('\n')[0]
                    
                    if not username or username == "-":
                        continue
                    
                    # Yayın gün sayısını al ve temizle
                    live_days_element = row.find_element(By.XPATH, ".//td[@role='gridcell'][2]")
                    live_days_text = live_days_element.text.strip()
                    live_days = int(''.join(filter(str.isdigit, live_days_text.split('\n')[0]))) if live_days_text else 0
                    
                    # Yayın süresini al ve saat cinsine çevir
                    live_hours_element = row.find_element(By.XPATH, ".//td[@role='gridcell'][3]")
                    live_hours_text = live_hours_element.text.strip().split('\n')[0]
                    live_hours = self.convert_duration_to_hours(live_hours_text)
                    
                    # Video sayısını al (aslında elmaslar - çekmeyeceğiz)
                    # video_count_element = row.find_element(By.XPATH, ".//td[@role='gridcell'][4]")
                    # video_count_text = video_count_element.text.strip()
                    # video_count = int(''.join(filter(str.isdigit, video_count_text.split('\n')[0]))) if video_count_text else 0

                    # PK sayısını al
                    pk_count_element = row.find_element(By.XPATH, ".//td[@role='gridcell'][5]")
                    pk_count_text = pk_count_element.text.strip()
                    pk_count = int(''.join(filter(str.isdigit, pk_count_text.split('\n')[0]))) if pk_count_text else 0

                    # Verileri sakla (video_count çekmiyoruz)
                    weekly_data['live_days'][username] = live_days
                    weekly_data['live_hours'][username] = live_hours
                    # weekly_data['video_count'][username] = video_count  # Bu satırı kaldırdık
                    weekly_data['pk_count'][username] = pk_count

                    print(f"✓ [{row_idx+1}/{total_rows}] {username}: {live_days} gün, "
                          f"{live_hours} saat ({live_hours_text}), "
                          f"{pk_count} PK")
                    
                except Exception as e:
                    print(f"❌ Satır {row_idx+1} işlenirken hata oluştu: {e}")
                    continue
            
            return weekly_data
            
        except Exception as e:
            print(f"❌ Haftalık veri çekilirken hata oluştu: {e}")
            return None

    def check_weekly_video_uploads(self, username, start_date_str, end_date_str):
        """Kullanıcının belirtilen tarih aralığında 4 veya daha fazla video paylaşıp paylaşmadığını kontrol eder"""
        try:
            print(f"🎥 {username} için video kontrolü yapılıyor...")

            # Tarihleri datetime objesine çevir
            start_date = datetime.strptime(start_date_str, "%d.%m.%Y")
            end_date = datetime.strptime(end_date_str, "%d.%m.%Y")

            # Kullanıcı profiline git
            profile_url = f"https://www.tiktok.com/@{username}"
            print(f"  -> Profil sayfasına gidiliyor: {profile_url}")
            self.driver.get(profile_url)
            time.sleep(3)  # Sayfanın yüklenmesini bekle

            # CAPTCHA kontrolü
            if check_captcha(self.driver):
                print(f"⚠️ {username} profil kontrolünde CAPTCHA algılandı!")
                wait_for_captcha_solution(self.driver)

            time.sleep(2)  # Ek bekleme

            # Sayfa içeriğini kontrol et
            try:
                page_source = self.driver.page_source
                print(f"  -> {username} profil sayfası yüklendi, sayfa uzunluğu: {len(page_source)} karakter")
            except:
                pass

            # Video elementlerini kontrol et
            video_selectors = [
                '[data-e2e="user-post-item"]',
                '[data-e2e="user-post-item-list"] > div',
                'a[href*="/video/"]',
                '.video-feed-item',
                '.user-post-item'
            ]

            video_found = False
            for selector in video_selectors:
                try:
                    videos = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if videos:
                        print(f"  -> {username} için {len(videos)} video elementi bulundu (seçici: {selector})")
                        video_found = True
                        break
                except Exception as e:
                    print(f"  -> {selector} seçici hatası: {e}")
                    continue

            if not video_found:
                print(f"  ℹ️ {username} için hiç video elementi bulunamadı")

                # İçerik kontrolü - Video olmadığında kontrol
                no_content_texts = [
                    "İçerik yok",
                    "Bu kullanıcı herhangi bir video yayınlamadı",
                    "No Content",
                    "This user hasn't posted any videos",
                    "Henüz video yok"
                ]

                for text in no_content_texts:
                    try:
                        no_content = self.driver.find_elements(By.XPATH, f"//*[contains(text(), '{text}')]")
                        if no_content:
                            print(f"  ℹ️ {username} henüz video paylaşmamış: '{text}' mesajı bulundu")
                            return False
                    except:
                        pass

                print(f"  ⚠️ {username} için video bulunamadı ama 'içerik yok' mesajı da yok")
                return False

            # İlk videoya tıkla
            first_video_clicked = False
            video_click_selectors = [
                '[data-e2e="user-post-item"]:first-child',
                '[data-e2e="user-post-item-list"] > div:first-child',
                'a[href*="/video/"]:first-child',
                '.video-feed-item:first-child',
                '.user-post-item:first-child'
            ]

            for selector in video_click_selectors:
                try:
                    print(f"  -> {username} için ilk videoya tıklanıyor (seçici: {selector})...")
                    first_video = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    first_video.click()
                    time.sleep(3)
                    print(f"  ✓ {username} için ilk videoya tıklandı")
                    first_video_clicked = True
                    break
                except Exception as e:
                    print(f"  -> {selector} ile tıklama başarısız: {e}")
                    continue

            if not first_video_clicked:
                print(f"  ❌ {username} için hiçbir video seçici ile tıklama başarılı olmadı")

                # JavaScript ile tıklamayı dene
                try:
                    print(f"  -> {username} için JavaScript ile video tıklama deneniyor...")
                    self.driver.execute_script("""
                        const videoElements = document.querySelectorAll('[data-e2e="user-post-item"], a[href*="/video/"]');
                        if (videoElements.length > 0) {
                            videoElements[0].click();
                            return true;
                        }
                        return false;
                    """)
                    time.sleep(3)
                    print(f"  ✓ {username} için JavaScript ile videoya tıklandı")
                    first_video_clicked = True
                except Exception as js_error:
                    print(f"  ❌ {username} için JavaScript tıklama da başarısız: {js_error}")

            if not first_video_clicked:
                print(f"  ❌ {username} için video tıklama tamamen başarısız")
                return False

            # CAPTCHA kontrolü
            if check_captcha(self.driver):
                print(f"⚠️ {username} video kontrolünde CAPTCHA algılandı!")
                wait_for_captcha_solution(self.driver)

            # Videoları gezinerek kontrol et
            checked_videos = 0
            videos_in_range = 0
            max_videos_to_check = 20  # Maksimum kontrol edilecek video sayısı

            print(f"  -> {username} için video tarihleri kontrol ediliyor...")

            while checked_videos < max_videos_to_check:
                # CAPTCHA kontrolü - her video için
                if check_captcha(self.driver):
                    print(f"⚠️ {username} video gezinirken CAPTCHA algılandı!")
                    wait_for_captcha_solution(self.driver)

                # Video sayacını artır
                checked_videos += 1

                # JavaScript ile tarih bilgisini bul
                date_text = self.driver.execute_script("""
                    const allSpans = Array.from(document.querySelectorAll('span'));
                    const timeSpan = allSpans.find(span =>
                        span.textContent.includes('sa önce') ||
                        span.textContent.includes('saat önce') ||
                        span.textContent.includes('gün önce') ||
                        span.textContent.includes('g önce') ||
                        span.textContent.includes('hafta önce') ||
                        span.textContent.includes('h önce') ||
                        span.textContent.includes(' · ') ||
                        span.textContent.includes('-') && /\\d{1,2}-\\d{1,2}/.test(span.textContent) ||
                        /\\d{4}-\\d{1,2}-\\d{1,2}/.test(span.textContent) ||
                        /\\d{1,2}\\.\\d{1,2}\\.\\d{4}/.test(span.textContent)
                    );
                    return timeSpan ? timeSpan.textContent : null;
                """)

                video_date = None

                if date_text:
                    # Kullanıcı adını temizle
                    if '·' in date_text:
                        date_text = date_text.split('·')[-1].strip()

                    # Tarih parse et
                    try:
                        import re
                        now = datetime.now()
                        date_text = date_text.strip()

                        if "sa önce" in date_text or "saat önce" in date_text:
                            hours = int(re.search(r'(\d+)\s*(?:sa|saat)', date_text).group(1))
                            video_date = now - timedelta(hours=hours)
                        elif "gün önce" in date_text or "g önce" in date_text:
                            days = int(re.search(r'(\d+)\s*(?:g|gün)', date_text).group(1))
                            video_date = now - timedelta(days=days)
                        elif "hafta önce" in date_text or "h önce" in date_text:
                            weeks = int(re.search(r'(\d+)\s*(?:h|hafta)', date_text).group(1))
                            video_date = now - timedelta(weeks=weeks)
                        elif "ay önce" in date_text:
                            months = int(re.search(r'(\d+)\s*ay', date_text).group(1))
                            video_date = now - timedelta(days=months*30)
                        elif re.search(r'\d{4}-\d{1,2}-\d{1,2}', date_text):
                            # YYYY-M-D veya YYYY-MM-DD formatı (örn: 2023-2-3, 2023-12-25)
                            match = re.search(r'(\d{4})-(\d{1,2})-(\d{1,2})', date_text)
                            if match:
                                year, month, day = int(match.group(1)), int(match.group(2)), int(match.group(3))
                                # Geçerli tarih kontrolü
                                if 1 <= month <= 12 and 1 <= day <= 31:
                                    try:
                                        video_date = datetime(year, month, day)
                                    except ValueError as date_error:
                                        print(f"    ⚠️ Geçersiz tarih: {year}-{month}-{day} - {date_error}")
                                else:
                                    print(f"    ⚠️ Geçersiz ay/gün: {month}/{day}")
                        elif re.search(r'\d{1,2}-\d{1,2}', date_text):
                            # MM-DD formatı (mevcut yıl varsayılır)
                            match = re.search(r'(\d{1,2})-(\d{1,2})', date_text)
                            if match:
                                month, day = int(match.group(1)), int(match.group(2))
                                if 1 <= month <= 12 and 1 <= day <= 31:
                                    try:
                                        video_date = datetime(now.year, month, day)
                                    except ValueError as date_error:
                                        print(f"    ⚠️ Geçersiz tarih: {now.year}-{month}-{day} - {date_error}")
                                else:
                                    print(f"    ⚠️ Geçersiz ay/gün: {month}/{day}")
                        elif re.search(r'\d{1,2}\.\d{1,2}\.\d{4}', date_text):
                            # DD.MM.YYYY formatı
                            match = re.search(r'(\d{1,2})\.(\d{1,2})\.(\d{4})', date_text)
                            if match:
                                day, month, year = int(match.group(1)), int(match.group(2)), int(match.group(3))
                                if 1 <= month <= 12 and 1 <= day <= 31:
                                    try:
                                        video_date = datetime(year, month, day)
                                    except ValueError as date_error:
                                        print(f"    ⚠️ Geçersiz tarih: {day}.{month}.{year} - {date_error}")
                                else:
                                    print(f"    ⚠️ Geçersiz ay/gün: {month}/{day}")
                    except Exception as parse_error:
                        print(f"    ⚠️ Tarih parse hatası: {date_text} - {parse_error}")

                # Video tarihini kontrol et
                if video_date:
                    if start_date <= video_date <= end_date:
                        videos_in_range += 1
                        print(f"    ✓ Video {checked_videos}: {date_text} -> Aralıkta ({videos_in_range})")
                    else:
                        print(f"    - Video {checked_videos}: {date_text} -> Aralık dışı")

                        # Eğer video çok eskiyse döngüyü kır
                        if video_date < start_date:
                            if checked_videos <= 3:
                                # İlk 3 video için devam ediyoruz (sabitlenmiş olabilirler)
                                print(f"    ℹ️ Video {checked_videos} eski ({date_text}) ama ilk 3 video, devam ediliyor (sabitlenmiş olabilir)")
                            else:
                                print(f"    ℹ️ Video tarihi aralık başlangıcından eski, kontrol durduruluyor")
                                break
                else:
                    print(f"    ⚠️ Video {checked_videos}: Tarih bulunamadı")

                # Sonraki videoya geç (aşağı ok tuşu)
                try:
                    self.driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.ARROW_DOWN)
                    time.sleep(2)  # Video yüklenmesini bekle
                except Exception as nav_error:
                    print(f"    ⚠️ Sonraki videoya geçiş hatası: {nav_error}")
                    break

            print(f"✓ {username} için {videos_in_range} video bulundu ({start_date_str} - {end_date_str} arası) - {checked_videos} video kontrol edildi")

            # 4 veya daha fazla video varsa True döndür
            return videos_in_range >= 4
            
        except Exception as e:
            print(f"❌ Video kontrolünde hata: {e}")
            return False

def wait_for_captcha_solution(driver):
    """CAPTCHA çözülene kadar bekler ve kullanıcı onayı ister, sonra cookies kaydeder"""
    print("\n⚠️ CAPTCHA algılandı! Lütfen tarayıcıda CAPTCHA'yı çözün...")
    print("⏳ Çözümü tamamladıktan sonra, konsola 'devam' yazıp Enter tuşuna basın...")

    # Ekran görüntüsü al (isteğe bağlı)
    try:
        screenshot_path = f"captcha_screenshot_{int(time.time())}.png"
        driver.save_screenshot(screenshot_path)
        print(f"✓ CAPTCHA ekran görüntüsü kaydedildi: {screenshot_path}")
    except Exception as e:
        print(f"❌ Ekran görüntüsü alınamadı: {e}")

    # Kullanıcıdan onay bekle
    while True:
        user_input = input("CAPTCHA çözüldü mü? ('devam' yazıp Enter tuşuna basın): ")
        if user_input.lower() == 'devam':
            break
        time.sleep(1)

    print("✅ CAPTCHA çözüldü, işlem devam ediyor...")

    # CAPTCHA çözüldükten sonra cookies kaydet
    try:
        cookies = driver.get_cookies()
        pickle.dump(cookies, open(COOKIES_FILE, "wb"))
        print(f"✅ CAPTCHA çözümü sonrası cookies başarıyla kaydedildi: {COOKIES_FILE}")
    except Exception as e:
        print(f"❌ CAPTCHA çözümü sonrası cookies kaydedilirken hata oluştu: {e}")

    time.sleep(2)  # Sayfanın yenilenmesi için kısa bir bekleme

# video_tarama_durum.json dosyasını kontrol et ve gerekirse oluştur
def check_and_create_checkpoint_file(checkpoint_file):
    """Checkpoint dosyasını kontrol eder ve yoksa oluşturur"""
    if not os.path.exists(checkpoint_file):
        try:
            with open(checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "start_date": "",
                    "end_date": "",
                    "video_counts": {},
                    "last_processed_user_index": 0,
                    "last_update": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }, f, ensure_ascii=False, indent=4)
            print(f"✓ Yeni checkpoint dosyası oluşturuldu: {checkpoint_file}")
            return True
        except Exception as e:
            print(f"❌ Checkpoint dosyası oluşturulamadı: {e}")
            return False
    return True

def scrape_weekly_video_counts(usernames, report_start_date_str, report_end_date_str, chrome_driver_path, chrome_binary_path=None):
    """Kullanıcıların profillerindeki haftalık video sayılarını çeker"""
    print("\n=== 3. Adım: Video Sayılarını Kontrol Ediliyor ===")
    weekly_video_counts = {}
    
    # Checkpoint dosyasının adı
    checkpoint_file = "video_tarama_durum.json"
    checkpoint_data = {}
    start_index = 0
    
    # Checkpoint dosyasını oluştur veya kontrol et
    if not check_and_create_checkpoint_file(checkpoint_file):
        print("⚠️ Checkpoint dosyası hazırlanamadı, işlem devam ediyor...")
    
    # Varsa checkpoint verisini yükle
    if os.path.exists(checkpoint_file):
        try:
            with open(checkpoint_file, 'r', encoding='utf-8') as f:
                checkpoint_data = json.load(f)
                
            # Kayıtlı tarihin aynı olduğundan emin ol
            if (checkpoint_data.get('start_date') == report_start_date_str and 
                checkpoint_data.get('end_date') == report_end_date_str):
                
                saved_counts = checkpoint_data.get('video_counts', {})
                for username, count in saved_counts.items():
                    weekly_video_counts[username] = count
                
                # Son işlenen kullanıcı indeksini al
                start_index = checkpoint_data.get('last_processed_user_index', 0)
                
                processed_count = len(saved_counts)
                remaining_count = len(usernames) - start_index
                
                print(f"✅ Checkpoint yüklendi: {processed_count} kullanıcı işlenmiş, {remaining_count} kullanıcı kaldı")
                print(f"ℹ️ Tarama {start_index}. kullanıcıdan devam edecek: {usernames[start_index] if start_index < len(usernames) else 'Liste sonu'}")
            else:
                print("⚠️ Kaydedilen tarih aralığı farklı, yeni tarama başlatılıyor")
                start_index = 0
        except Exception as e:
            print(f"⚠️ Checkpoint yüklenirken hata oluştu: {e}")
            start_index = 0

    # Checkpoint kaydetme fonksiyonu
    def save_checkpoint(current_index):
        try:
            with open(checkpoint_file, 'w', encoding='utf-8') as f:
                checkpoint_data = {
                    'start_date': report_start_date_str,
                    'end_date': report_end_date_str,
                    'video_counts': weekly_video_counts,
                    'last_processed_user_index': current_index,
                    'last_update': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                json.dump(checkpoint_data, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"❌ Checkpoint kaydedilirken hata oluştu: {e}")
            return False

    # Chrome profilini ayarla
    options = webdriver.ChromeOptions()
    options.add_argument("--disable-blink-features=AutomationControlled")  # Otomasyon belirteçlerini gizle
    options.add_argument("--disable-popup-blocking")  # Pop-up engellemeyi devre dışı bırak
    options.add_argument("--disable-extensions")  # Eklentileri devre dışı bırak
    options.add_argument("--disable-notifications")  # Bildirimleri devre dışı bırak
    options.add_argument("--disable-dev-shm-usage")  # /dev/shm kullanımını devre dışı bırak (Linux'ta)
    options.add_argument("--log-level=3")  # Log seviyesini düşür
    options.add_argument("--mute-audio")  # Sesi kapat
    options.add_argument("--window-size=1366,768")  # Pencere boyutunu ayarla

    # Tarihleri parse et
    try:
        report_start_date = datetime.strptime(report_start_date_str, "%d.%m.%Y").date()
        report_end_date = datetime.strptime(report_end_date_str, "%d.%m.%Y").date()
    except Exception as e:
        print(f"❌ Tarih formatı hatalı: {e}")
        return weekly_video_counts

    # Chrome profil dizini ve ismi ayarla (Chrome for Testing)
    chrome_profile_path = r"C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data"
    profile_name = "Profile 1"
    
    # Chrome profilini kullan 
    options.add_argument(f"user-data-dir={chrome_profile_path}")
    options.add_argument(f"profile-directory={profile_name}")
    
    # Chrome binary yolunu kontrol et - önce yerel, sonra sistem yolları
    if chrome_binary_path and os.path.exists(chrome_binary_path):
        options.binary_location = chrome_binary_path
        print(f"✓ Chrome binary yolu ayarlandı: {chrome_binary_path}")
    else:
        # Yerel Chrome'u kontrol et
        local_chrome = os.path.join(os.getcwd(), LOCAL_CHROME_PATH)
        if os.path.exists(local_chrome):
            options.binary_location = local_chrome
            print(f"✓ Yerel Chrome bulundu ve ayarlandı: {local_chrome}")
        else:
            # Standart Chrome yükleme yollarını kontrol et
            standard_paths = [
                os.path.join(os.environ.get('PROGRAMFILES', 'C:\\Program Files'), 'Google\\Chrome\\Application\\chrome.exe'),
                os.path.join(os.environ.get('PROGRAMFILES(X86)', 'C:\\Program Files (x86)'), 'Google\\Chrome\\Application\\chrome.exe'),
                os.path.join(os.environ.get('LOCALAPPDATA', ''), 'Google\\Chrome\\Application\\chrome.exe')
            ]
            
            for path in standard_paths:
                if os.path.exists(path):
                    options.binary_location = path
                    print(f"✓ Sistem Chrome bulundu ve ayarlandı: {path}")
                    break

    # Tarayıcıyı başlatma hata durumunu koru
    try:
        # Önce ChromeDriverManager ile deneyelim
        try:
            print("ChromeDriverManager ile sürücü başlatılıyor...")
            driver = webdriver.Chrome(
                service=Service(ChromeDriverManager().install()),
                options=options
            )
        except Exception as cdm_error:
            print(f"ChromeDriverManager hatası: {cdm_error}")
            print("Yerel ChromeDriver ile devam ediliyor...")
            # Yerel ChromeDriver ile deneyelim
            driver = webdriver.Chrome(
                service=Service(chrome_driver_path),
                options=options
            )
            
        # WebDriver belirteçlerini gizleme - test_video_dates.py'den alındı
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        print("✓ Profil tarama için Chrome sürücüsü başlatıldı.")
        
        # TikTok ana sayfasına git ve ilk CAPTCHA kontrolü yap
        driver.get("https://www.tiktok.com")
        time.sleep(3)
        
        if check_captcha(driver):
            print("⚠️ İlk açılışta CAPTCHA algılandı!")
            wait_for_captcha_solution(driver)
            captcha_counter = 0  # CAPTCHA göründüğünde sayacı sıfırla
        else:
            print("✓ İlk açılışta CAPTCHA algılanmadı, devam ediliyor...")
        
        # Cookies'leri yükleyelim (varsa)
        cookie_file = COOKIES_FILE
        if os.path.exists(cookie_file):
            try:
                # TikTok ana sayfasına git (çerezleri ekleyebilmek için)
                driver.get("https://www.tiktok.com")
                time.sleep(2)

                cookies = pickle.load(open(cookie_file, "rb"))
                for cookie in cookies:
                    if 'expiry' in cookie:
                        del cookie['expiry']
                    try:
                        driver.add_cookie(cookie)
                    except:
                        continue
                print(f"✅ Cookies başarıyla yüklendi: {cookie_file}")
                driver.refresh()
                time.sleep(3)

                # Cookies yüklendikten sonra CAPTCHA kontrolü
                if check_captcha(driver):
                    print("⚠️ Cookies yüklendikten sonra CAPTCHA algılandı!")
                    wait_for_captcha_solution(driver)
                else:
                    print("✓ Cookies yüklendikten sonra CAPTCHA algılanmadı")

            except Exception as e:
                print(f"❌ Cookies yüklenirken hata oluştu: {e}")
        else:
            print(f"⚠️ Cookies dosyası bulunamadı: {cookie_file}")
            print("ℹ️ İlk CAPTCHA çözümünden sonra cookies kaydedilecek")
        
    except WebDriverException as e:
        print(f"❌ Profil tarama sürücüsü başlatılamadı: {e}")
        return weekly_video_counts # Boş sözlük döndür
    except Exception as e:
        print(f"❌ Profil tarama sürücüsü başlatılırken beklenmedik hata: {e}")
        return weekly_video_counts

    # İşlenen kullanıcı takibi
    processed_users = 0
    captcha_counter = 0  # CAPTCHA sayacı
    total_users = len(usernames)
    
    try:
        # Kaldığı yerden başla
        for i in range(start_index, total_users):
            username = usernames[i]
            processed_users += 1
            captcha_counter += 1
            
            print(f"\n[{i+1}/{total_users}] Kullanıcı işleniyor: {username}")
            
            # Her 5-6 profil sonrası CAPTCHA kontrolü
            if captcha_counter >= 5:
                print("ℹ️ Periyodik CAPTCHA kontrolü yapılıyor...")
                driver.get("https://www.tiktok.com")
                time.sleep(2)
                
                if check_captcha(driver):
                    wait_for_captcha_solution(driver)
                    captcha_counter = 0  # Sayaç sıfırla
                else:
                    print("✓ CAPTCHA algılanmadı, devam ediliyor...")
                
                captcha_counter = 0  # Sayaç sıfırla
                time.sleep(1)  # Kısa bir bekleme
            
            # Doğrudan kullanıcı profiline git
            profile_url = f"https://www.tiktok.com/@{username}"
            video_count_in_week = 0
            
            try:
                print(f"  -> Profil sayfasına gidiliyor: {profile_url}")
                driver.get(profile_url)
                time.sleep(3)
                
                # CAPTCHA kontrolü
                if check_captcha(driver):
                    wait_for_captcha_solution(driver)
                    captcha_counter = 0  # CAPTCHA göründüğünde sayacı sıfırla
                
                # Sayfanın yüklenmesini bekleyelim
                try:
                    WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "h1, [data-e2e='user-title'], strong, h2"))
                    )
                except TimeoutException:
                    print("  ❌ Kullanıcı profili yüklenemedi")
                    weekly_video_counts[username] = 0
                    save_checkpoint(i + 1)  # Her kullanıcı sonrası kaydet
                    continue
                
                # "TikTok'a giriş yapın" kontrolü
                login_text_selectors = [
                    "//h2[contains(text(), 'giriş yapın')]", 
                    "//h2[contains(text(), 'Giriş yap')]",
                    "//div[contains(text(), 'giriş yapın')]", 
                    "//div[contains(text(), 'Giriş yap')]"
                ]
                
                login_required = False
                for selector in login_text_selectors:
                    try:
                        login_text = driver.find_elements(By.XPATH, selector)
                        if login_text:
                            login_required = True
                            print("  ⚠️ TikTok'a giriş yapılması gerekiyor.")
                            break
                    except:
                        pass
                        
                if login_required:
                    # İlk kez çalıştırılıyorsa, kullanıcıdan giriş yapmasını iste
                    input_text = input("\n⚠️ TikTok giriş gerekiyor. Giriş yaptıktan sonra Enter'a basın (veya 'skip' yazıp Enter'a basarak bu kullanıcıyı atlayın): ")
                    if input_text.lower() == 'skip':
                        print("  ℹ️ Bu kullanıcı atlanıyor.")
                        weekly_video_counts[username] = 0
                        save_checkpoint(i + 1)  # Her kullanıcı sonrası kaydet
                        continue
                    
                    # Cookies'leri kaydet
                    try:
                        cookies = driver.get_cookies()
                        pickle.dump(cookies, open(COOKIES_FILE, "wb"))
                        print(f"✅ Cookies başarıyla kaydedildi: {COOKIES_FILE}")
                    except Exception as e:
                        print(f"❌ Cookies kaydedilirken hata oluştu: {e}")
                        
                    # Sayfayı yenile
                    driver.refresh()
                    time.sleep(3)
                
                # İçerik kontrolü - Video olmadığında kontrol
                no_content_texts = [
                    "İçerik yok",
                    "Bu kullanıcı herhangi bir video yayınlamadı",
                    "No Content",
                    "This user hasn't posted any videos"
                ]
                
                for text in no_content_texts:
                    try:
                        no_content = driver.find_elements(By.XPATH, f"//*[contains(text(), '{text}')]")
                        if no_content:
                            print(f"  ℹ️ Kullanıcı henüz video paylaşmamış: {username}")
                            weekly_video_counts[username] = 0
                            save_checkpoint(i + 1)  # Her kullanıcı sonrası kaydet
                            continue
                    except:
                        pass
                
                # Modal varsa kapat
                try:
                    modal_selectors = [
                        "button[aria-label='Close']", 
                        ".tiktok-dialog__close",
                        ".tiktok-modal__close-icon",
                        "div[data-e2e='modal-close-inner-button']",
                        "div[role='dialog'] button"
                    ]
                    
                    for selector in modal_selectors:
                        try:
                            close_buttons = driver.find_elements(By.CSS_SELECTOR, selector)
                            for button in close_buttons:
                                if button.is_displayed():
                                    driver.execute_script("arguments[0].click();", button)
                                    time.sleep(1)
                                    break
                        except:
                            continue
                            
                    # ESC tuşu ile kapatmayı dene
                    try:
                        webdriver.ActionChains(driver).send_keys(Keys.ESCAPE).perform()
                        time.sleep(1)
                    except:
                        pass
                except:
                    pass
                
                # İlk videoya tıkla
                try:
                    first_video = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-e2e="user-post-item"]:first-child, [data-e2e="user-post-item-list"] > div:first-child, a[href*="/video/"]:first-child'))
                    )
                    first_video.click()
                    time.sleep(2)
                    print("  ✓ İlk videoya tıklandı")
                except Exception as e:
                    print(f"  ℹ️ İlk video bulunamadı veya kullanıcı henüz video paylaşmamış: {e}")
                    weekly_video_counts[username] = 0
                    save_checkpoint(i + 1)  # Her kullanıcı sonrası kaydet
                    continue
                
                # CAPTCHA kontrolü
                if check_captcha(driver):
                    wait_for_captcha_solution(driver)
                    captcha_counter = 0  # CAPTCHA göründüğünde sayacı sıfırla
                
                # Videoları gezinerek kontrol et
                checked_videos = 0
                videos_in_range = []
                
                while True:
                    # CAPTCHA kontrolü - her video için
                    if check_captcha(driver):
                        wait_for_captcha_solution(driver)
                        captcha_counter = 0  # CAPTCHA göründüğünde sayacı sıfırla
                    
                    # Mevcut video URL'ini al
                    current_url = driver.current_url
                    checked_videos += 1
                    
                    # JavaScript ile tarih bilgisini bul
                    date_text = driver.execute_script("""
                        const allSpans = Array.from(document.querySelectorAll('span'));
                        const timeSpan = allSpans.find(span => 
                            span.textContent.includes('sa önce') || 
                            span.textContent.includes('saat önce') ||
                            span.textContent.includes('gün önce') ||
                            span.textContent.includes('g önce') ||
                            span.textContent.includes('hafta önce') ||
                            span.textContent.includes('h önce') ||
                            span.textContent.includes(' · ') ||
                            span.textContent.includes('-') && /\\d{1,2}-\\d{1,2}/.test(span.textContent) ||
                            /\\d{4}-\\d{1,2}-\\d{1,2}/.test(span.textContent) ||
                            /\\d{1,2}\\.\\d{1,2}\\.\\d{4}/.test(span.textContent)
                        );
                        return timeSpan ? timeSpan.textContent : null;
                    """)
                    
                    video_date = None
                    
                    if date_text:
                        # Kullanıcı adını temizle
                        if '·' in date_text:
                            date_text = date_text.split('·')[-1].strip()
                        
                        # Tarih parse et
                        try:
                            now = datetime.now()
                            date_text = date_text.strip()
                            
                            if "sa önce" in date_text or "saat önce" in date_text:
                                hours = int(re.search(r'(\d+)\s*(?:sa|saat)', date_text).group(1))
                                video_date = now - timedelta(hours=hours)
                            elif "gün önce" in date_text or "g önce" in date_text:
                                days = int(re.search(r'(\d+)\s*(?:g|gün)', date_text).group(1))
                                video_date = now - timedelta(days=days)
                            elif "hafta önce" in date_text or "h önce" in date_text:
                                weeks = int(re.search(r'(\d+)\s*(?:h|hafta)', date_text).group(1))
                                video_date = now - timedelta(weeks=weeks)
                            elif "ay önce" in date_text:
                                months = int(re.search(r'(\d+)\s*ay', date_text).group(1))
                                video_date = now - timedelta(days=months*30)
                            elif re.search(r'\d{4}-\d{1,2}-\d{1,2}', date_text):  # Örn: 2023-6-8
                                date_match = re.search(r'(\d{4})-(\d{1,2})-(\d{1,2})', date_text)
                                year = int(date_match.group(1))
                                month = int(date_match.group(2))
                                day = int(date_match.group(3))
                                video_date = datetime(year, month, day)
                            elif re.search(r'\d{1,2}\.\d{1,2}\.\d{4}', date_text):  # Örn: 1.6.2023
                                date_match = re.search(r'(\d{1,2})\.(\d{1,2})\.(\d{4})', date_text)
                                day = int(date_match.group(1))
                                month = int(date_match.group(2))
                                year = int(date_match.group(3))
                                video_date = datetime(year, month, day)
                            elif re.search(r'\d{1,2}-\d{1,2}$', date_text):  # Örn: 4-7
                                month_day = re.search(r'(\d{1,2})-(\d{1,2})$', date_text)
                                month = int(month_day.group(1))
                                day = int(month_day.group(2))
                                year = now.year if month <= now.month else now.year - 1
                                video_date = datetime(year, month, day)
                        except Exception as e:
                            print(f"  ❌ Tarih parse hatası: {e} - {date_text}")
                    
                    if video_date:
                        print(f"  -> Video {checked_videos}: Tarih: {video_date.strftime('%Y-%m-%d')} ({date_text})")
                        
                        # Video ID
                        video_id = re.search(r'/video/(\d+)', current_url)
                        video_id = video_id.group(1) if video_id else "Bilinmiyor"
                        
                        # Tarih kontrolü
                        video_date_only = video_date.date()
                        
                        if report_start_date <= video_date_only <= report_end_date:
                            # Video tarih aralığı içinde
                            video_count_in_week += 1
                            videos_in_range.append({
                                "id": video_id,
                                "date": video_date.strftime('%Y-%m-%d'),
                                "url": current_url
                            })
                            print(f"  ✅ TARİH ARALIĞINDA: {video_date.strftime('%Y-%m-%d')} - Toplam: {video_count_in_week}")
                        elif video_date_only < report_start_date:
                            # Video tarih aralığı dışında (eski)
                            if checked_videos <= 3:
                                # İlk 3 video için devam ediyoruz (sabitlenmiş olabilirler)
                                print(f"  ℹ️ Tarih aralığı dışında (eski) video: {video_date.strftime('%Y-%m-%d')} (İlk 3 video, devam ediliyor)")
                            else:
                                # 4. videodan itibaren tarih kontrolü yapıyoruz
                                print(f"  ℹ️ Tarih aralığı dışında (eski) video: {video_date.strftime('%Y-%m-%d')} - 4. videodan sonra olduğu için işlem sonlandırılıyor")
                                break
                        else:
                            # Gelecek tarihli video (nadiren olur)
                            print(f"  ℹ️ Tarih aralığı dışında (gelecek tarih): {video_date.strftime('%Y-%m-%d')}")
                    else:
                        print(f"  ⚠️ Video {checked_videos}: Tarih bulunamadı/çıkarılamadı")
                    
                    # Sonraki videoya geç
                    try:
                        # Sağ ok tuşuna basma veya next butonu
                        try:
                            next_button = driver.find_element(By.CSS_SELECTOR, '[data-e2e="arrow-right"], .arrow-right, .tiktok-17krsri-ButtonBasicButtonContainer, button[aria-label="Go to next video"]')
                            if next_button:
                                driver.execute_script("arguments[0].click();", next_button)
                                time.sleep(1.5)
                        except:
                            # Alternatif: klavye ile sağ ok tuşu
                            body = driver.find_element(By.TAG_NAME, 'body')
                            body.send_keys(Keys.ARROW_RIGHT)
                            time.sleep(1.5)
                        
                        # URL değişti mi kontrol et
                        new_url = driver.current_url
                        if new_url == current_url:
                            print("  ℹ️ Videolarda sona ulaşıldı")
                            break
                        
                    except Exception as e:
                        print(f"  ℹ️ Sonraki videoya geçilemiyor, işlem sonlandırılıyor.")
                        break
                    
                    # Maksimum video sınırı (opsiyonel)
                    if checked_videos >= 20:
                        print("  ℹ️ Maksimum video sayısına ulaşıldı (20)")
                        break
                
                # Haftalık video sayısını kaydet
                weekly_video_counts[username] = video_count_in_week
                print(f"  📊 {username} için haftalık video sayısı: {video_count_in_week}")
                
                # Her kullanıcı sonrası checkpoint kaydet
                save_checkpoint(i + 1)  # Bir sonraki kullanıcı indeksi
                
                # Rastgele bekleme süresi (bot algılama önlemi)
                time.sleep(random.uniform(2, 4))
                
            except Exception as e:
                print(f"  ❌ Genel hata: {e}")
                weekly_video_counts[username] = 0
                save_checkpoint(i + 1)  # Hata durumunda da kaydet
                
    except KeyboardInterrupt:
        print("\n⚠️ İşlem kullanıcı tarafından kesildi! İlerleme kaydediliyor...")
        # Geçerli kullanıcı indeksini kaydet (i değeri yukarıdaki for döngüsünden geliyor)
        current_index = start_index + processed_users - 1 if processed_users > 0 else start_index
        save_checkpoint(current_index)
    except Exception as e:
        print(f"\n❌ Beklenmeyen hata: {e}")
        current_index = start_index + processed_users - 1 if processed_users > 0 else start_index
        save_checkpoint(current_index)
    finally:
        # Tarayıcıyı kapat
        if driver:
            try:
                driver.quit()
                print("✓ Chrome sürücüsü kapatıldı.")
            except:
                pass
    
    # Sonuç özeti
    print("\n=== Video Sayıları Özeti ===")
    successful_users = 0
    for count in weekly_video_counts.values():
        try:
            video_count = int(count) if count is not None else 0
            if video_count >= 4:
                successful_users += 1
        except (ValueError, TypeError):
            pass  # Geçersiz değerleri atla

    print(f"  Toplam: {len(weekly_video_counts)} kullanıcı işlendi, {successful_users} kullanıcı 4+ video görevini tamamladı")
    print(f"  Başarı oranı: %{(successful_users / len(weekly_video_counts) * 100) if weekly_video_counts else 0:.1f}")

    for username, count in weekly_video_counts.items():
        try:
            video_count = int(count) if count is not None else 0
            if video_count >= 4:
                print(f"  ✅ {username}: {video_count} video")
            else:
                print(f"  ❌ {username}: {video_count} video")
        except (ValueError, TypeError):
            print(f"  ⚠️ {username}: Geçersiz video sayısı ({count})")
        
    return weekly_video_counts

def test_video_date_in_range(video_date_str, start_date_str, end_date_str):
    """Test fonksiyonu: Bir video tarihinin belirli bir aralıkta olup olmadığını kontrol eder.
    Args:
        video_date_str: Video tarihi (YYYY-MM-DD formatında)
        start_date_str: Başlangıç tarihi (DD.MM.YYYY formatında)
        end_date_str: Bitiş tarihi (DD.MM.YYYY formatında)
    Returns:
        bool: Video tarihi belirtilen aralıkta ise True, değilse False
    """
    try:
        # Tarihleri datetime nesnelerine dönüştür
        video_date = datetime.strptime(video_date_str, "%Y-%m-%d").date()
        start_date = datetime.strptime(start_date_str, "%d.%m.%Y").date()
        end_date = datetime.strptime(end_date_str, "%d.%m.%Y").date()
        
        # Kontrol et ve sonucu yazdır
        result = start_date <= video_date <= end_date
        print(f"Video tarihi: {video_date}, Aralık: {start_date} - {end_date}, Sonuç: {result}")
        return result
    except Exception as e:
        print(f"Tarih kontrolünde hata: {e}")
        return False

# Adım 3: Excel Raporunu Oluştur

def main():
    print("===== TikTok Haftalık Ajans Görev Takip Sistemi =====")
    print("")
    
    # Chrome ve profil yollarını ayarla (Chrome for Testing)
    chrome_binary_path = r"C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe"
    chrome_profile_path = r"C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data"
    profile_name = "Profile 1"  # Chrome for Testing profil adı
    
    # ChromeDriver yolu (proje kök klasöründe)
    current_dir = os.path.dirname(os.path.abspath(__file__))
    chrome_driver_path = os.path.join(current_dir, "chromedriver.exe")
    
    # Excel çıktı dosyası
    output_file = "Tuber Medya.xlsx"
    
    print(f"✓ Chrome yolu: {chrome_binary_path}")
    print(f"✓ Chrome profil yolu: {chrome_profile_path}")
    print(f"✓ Profil adı: {profile_name}")
    print(f"✓ ChromeDriver yolu: {chrome_driver_path}")
    
    if not os.path.exists(chrome_binary_path):
        print("❌ Chrome bulunamadı! Lütfen Chrome'un yüklü olduğundan emin olun.")
        return
        
    if not os.path.exists(chrome_driver_path):
        print("❌ ChromeDriver bulunamadı! Lütfen chromedriver.exe'nin proje klasöründe olduğundan emin olun.")
        return
    
    # Varsayılan tarih aralığını hesapla - Bir önceki Pazartesi'den bu Pazartesi'ye
    today = datetime.now()
    print(f"Bugünkü tarih: {today.strftime('%d.%m.%Y')}")

    # Haftanın günü: Pazartesi=0, Salı=1, ..., Pazar=6
    current_weekday = today.weekday()
    print(f"Haftanın günü: {current_weekday} (0=Pazartesi, 6=Pazar)")

    # En yakın (geçmiş veya bugün) Pazartesi
    current_monday = today - timedelta(days=current_weekday)
    # Bir önceki Pazartesi
    previous_monday = current_monday - timedelta(days=7)

    # Başlangıç: Bir önceki Pazartesi
    default_start_date = previous_monday.strftime("%d.%m.%Y")
    # Bitiş: Bir önceki Pazar (bu haftanın Pazartesi'sinden bir gün önce)
    default_end_date = (current_monday - timedelta(days=1)).strftime("%d.%m.%Y")

    print("\n=== Tarih Aralığı Seçimi ===")
    print(f"Varsayılan tarih aralığı: {default_start_date} - {default_end_date}")
    print("(Bir önceki haftanın Pazartesi 00:00 - Bu haftanın Pazartesi 00:00 aralığı)")
    
    while True:
        print("\nTarih aralığını değiştirmek ister misiniz? (evet/hayır): ", end="")
        choice = input().strip().lower()
        
        if choice in ['hayır', 'h', 'no', 'n']:
            start_date = default_start_date
            end_date = default_end_date
            break
        elif choice in ['evet', 'e', 'yes', 'y']:
            while True:
                print("\nBaşlangıç tarihini girin (GG.AA.YYYY formatında): ", end="")
                start_date = input().strip()
                try:
                    # Tarih formatını kontrol et
                    datetime.strptime(start_date, "%d.%m.%Y")
                    break
                except ValueError:
                    print("❌ Hatalı tarih formatı! Örnek: 01.01.2024")
            
            while True:
                print("Bitiş tarihini girin (GG.AA.YYYY formatında): ", end="")
                end_date = input().strip()
                try:
                    # Tarih formatını kontrol et
                    end = datetime.strptime(end_date, "%d.%m.%Y")
                    start = datetime.strptime(start_date, "%d.%m.%Y")
                    if end < start:
                        print("❌ Bitiş tarihi başlangıç tarihinden önce olamaz!")
                        continue
                    break
                except ValueError:
                    print("❌ Hatalı tarih formatı! Örnek: 01.01.2024")
            break
        else:
            print("❌ Lütfen 'evet' veya 'hayır' yazın.")
    
    print(f"\n✓ Seçilen tarih aralığı: {start_date} - {end_date}")
    print(f"✓ Excel çıktı dosyası: {output_file}")
    
    # Kullanıcıya onay sor
    print("\nİşlemi başlatmak istiyor musunuz? (evet/hayır): ", end="")
    if input().strip().lower() not in ['evet', 'e', 'yes', 'y']:
        print("\nİşlem iptal edildi.")
        return
    
    # Tracker oluştur ve çalıştır
    tracker = TikTokDailyLiveTracker(chrome_profile_path, profile_name, chrome_driver_path, chrome_binary_path)
    if not tracker.run(start_date, end_date, output_file):
        print("\n❌ Program başarısız oldu.")
    else:
        print("\n✅ Program başarıyla tamamlandı!")

if __name__ == "__main__":
    main()