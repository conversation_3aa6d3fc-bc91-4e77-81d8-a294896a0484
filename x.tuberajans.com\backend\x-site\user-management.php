<?php
// Temel gereksinimleri dahil et
require_once __DIR__ . '/../config/config.php';

// Bearer Token'ı header'dan ayıkla
function getBearerToken() {
    $headers = null;
    if (isset($_SERVER['Authorization'])) {
        $headers = trim($_SERVER["Authorization"]);
    }
    else if (isset($_SERVER['HTTP_AUTHORIZATION'])) { // Nginx veya fast CGI
        $headers = trim($_SERVER["HTTP_AUTHORIZATION"]);
    } elseif (function_exists('apache_request_headers')) {
        $requestHeaders = apache_request_headers();
        // Anahtarlar büyük/küçük harf duyarlı olabilir
        $requestHeaders = array_combine(array_map('ucwords', array_keys($requestHeaders)), array_values($requestHeaders));
        if (isset($requestHeaders['Authorization'])) {
            $headers = trim($requestHeaders['Authorization']);
        }
    }
    // Bearer token'ı ayıkla
    if (!empty($headers)) {
        if (preg_match('/Bearer\s(\S+)/', $headers, $matches)) {
            return $matches[1];
        }
    }
    return null;
}

// CORS ve JSON header'ları
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=utf-8');

// OPTIONS istekleri için hızlıca 200 dön
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Hata ayıklama
error_log("User Management API başlatıldı. Method: " . $_SERVER['REQUEST_METHOD']);
error_log("URL: " . $_SERVER['REQUEST_URI']);

// Tüm headerları güvenli bir şekilde logla
$headers = getallheaders();
error_log("Tüm Headers: " . json_encode($headers));

// Veritabanı bağlantısını kontrol et
if (!isset($db) || !$db) {
    error_log("Veritabanı bağlantısı başarısız");
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Veritabanı bağlantısı kurulamadı']);
    exit();
}

// Token kontrolü
$token = getBearerToken();
error_log("Alınan token: " . ($token ? substr($token, 0, 10) . "..." : "Token bulunamadı"));

// Geçici olarak token kontrolünü atla - TEST İÇİN
$skipTokenCheck = true; // !!! SADECE TEST İÇİN !!!

// Content Type bilgisini güvenli şekilde al
$contentType = isset($_SERVER['CONTENT_TYPE']) ? $_SERVER['CONTENT_TYPE'] : 'Belirtilmemiş';
error_log("Content Type: " . $contentType);

// İzinleri normalize eden fonksiyon
function normalizePermissions($permissions) {
    if (isset($permissions['site'])) {
        $permissions['site_yonetimi'] = $permissions['site'];
        unset($permissions['site']);
    }
    if (isset($permissions['etsy'])) {
        $permissions['etsy_operasyonu'] = $permissions['etsy'];
        unset($permissions['etsy']);
    }
    return $permissions;
}

// Tüm kullanıcıları getir
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {
        error_log("=====================");
        error_log("Kullanıcıları getirme isteği: " . $_SERVER['REQUEST_URI']);
        error_log("HTTP Metodu: " . $_SERVER['REQUEST_METHOD']);
        error_log("Content Type: " . ($_SERVER['CONTENT_TYPE'] ?? 'Belirtilmemiş'));
        
        error_log("Kullanıcıları getirme işlemi başlatıldı");
        
        // Token kontrolü (test modunda devre dışı)
        $testMode = true; // Test için true, production'da false yapın
        if (!$testMode && !$skipTokenCheck && !$token) {
            error_log("Token bulunamadı - 401 yanıtı gönderiliyor");
            http_response_code(401);
            echo json_encode(['success' => false, 'error' => 'Yetkilendirme gerekli']);
            exit();
        }
        
        // Veritabanı sorgusu
        $stmt = $db->prepare("
            SELECT id, name, email, role, created_at, permissions 
            FROM users 
            ORDER BY created_at DESC
        ");
        $stmt->execute();
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($users as &$user) {
            $user['permissions'] = normalizePermissions(json_decode($user['permissions'], true));
        }
        
        error_log("Bulunan kullanıcı sayısı: " . count($users));
        
        // JSON çıktısından önce
        ob_clean(); // Tüm çıktı tamponlarını temizle
        echo json_encode(['success' => true, 'users' => $users]);
        
    } catch (PDOException $e) {
        error_log("Veritabanı hatası: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Kullanıcılar getirilirken bir hata oluştu: ' . $e->getMessage()]);
    }
    exit();
}

// Yeni kullanıcı oluştur
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        error_log("Kullanıcı oluşturma işlemi başlatıldı");
        
        // Token kontrolü (test modunda devre dışı)
        $testMode = true; // Test için true, production'da false yapın
        if (!$testMode && !$skipTokenCheck && !$token) {
            error_log("Token bulunamadı - 401 yanıtı gönderiliyor");
            http_response_code(401);
            echo json_encode(['success' => false, 'error' => 'Yetkilendirme gerekli']);
            exit();
        }
        
        // Gelen verileri al
        $data = json_decode(file_get_contents('php://input'), true);
        error_log("Alınan veriler: " . json_encode($data));
        
        // Gerekli alanları kontrol et
        if (!isset($data['name']) || !isset($data['email']) || !isset($data['password']) || !isset($data['role'])) {
            error_log("Eksik alanlar");
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Eksik alanlar: name, email, password, role gerekli']);
            exit();
        }
        
        // Email zaten var mı kontrol et
        $stmt = $db->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$data['email']]);
        if ($stmt->fetch()) {
            error_log("Email zaten kullanımda: " . $data['email']);
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Bu email zaten kullanımda']);
            exit();
        }
        
        // Şifreyi hashle
        $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
        
        if (isset($data['permissions'])) {
            $data['permissions'] = normalizePermissions($data['permissions']);
        }
        $stmt = $db->prepare("
            INSERT INTO users (name, email, password, role, permissions) 
            VALUES (?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $data['name'],
            $data['email'],
            $hashedPassword,
            $data['role'],
            json_encode($data['permissions'])
        ]);
        
        $newUserId = $db->lastInsertId();
        error_log("Yeni kullanıcı oluşturuldu: ID=" . $newUserId);
        
        // Oluşturulan kullanıcıyı getir (şifre hariç)
        $stmt = $db->prepare("
            SELECT id, name, email, role, created_at, permissions 
            FROM users 
            WHERE id = ?
        ");
        $stmt->execute([$newUserId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        $user['permissions'] = normalizePermissions(json_decode($user['permissions'], true));
        
        echo json_encode(['success' => true, 'user' => $user]);
        
    } catch (PDOException $e) {
        error_log("Veritabanı hatası: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Kullanıcı oluşturulurken bir hata oluştu: ' . $e->getMessage()]);
    }
    exit();
}

// Kullanıcı güncelle
if ($_SERVER['REQUEST_METHOD'] === 'PUT') {
    try {
        error_log("Kullanıcı güncelleme işlemi başlatıldı");
        $targetUserId = $_GET['id'] ?? null;
        if (!$targetUserId) {
            error_log("Kullanıcı ID'si eksik");
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Kullanıcı ID\'si gerekli']);
            exit();
        }
        $data = json_decode(file_get_contents('php://input'), true);
        error_log("Alınan veriler: " . json_encode($data));
        if (!isset($data['name']) || !isset($data['email'])) {
            error_log("Eksik alanlar");
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Eksik alanlar: name, email gerekli']);
            exit();
        }
        // Email başka kullanıcı tarafından kullanılıyor mu kontrol et
        $stmt = $db->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
        $stmt->execute([$data['email'], $targetUserId]);
        if ($stmt->fetch()) {
            error_log("Email zaten kullanımda: " . $data['email']);
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Bu email zaten kullanımda']);
            exit();
        }
        // Mevcut kullanıcıyı çek
        $stmt = $db->prepare("SELECT password FROM users WHERE id = ?");
        $stmt->execute([$targetUserId]);
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        $oldPassword = $row ? $row['password'] : null;
        // Şifreyi ayarla
        $hashedPassword = $oldPassword;
        if (isset($data['password']) && !empty($data['password'])) {
            $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
        }
        // Permissions'ı normalize et
        if (isset($data['permissions'])) {
            $data['permissions'] = normalizePermissions($data['permissions']);
        }
        error_log("Güncellenecek permissions: " . json_encode($data['permissions']));
        // Tek sorguda güncelle
        $stmt = $db->prepare("
            UPDATE users 
            SET name = ?, email = ?, password = ?, role = ?, permissions = ?
            WHERE id = ?
        ");
        $stmt->execute([
            $data['name'],
            $data['email'],
            $hashedPassword,
            $data['role'] ?? 'user',
            json_encode($data['permissions']),
            $targetUserId
        ]);
        // Güncellenmiş kullanıcıyı getir
        $stmt = $db->prepare("
            SELECT id, name, email, role, created_at, permissions 
            FROM users 
            WHERE id = ?
        ");
        $stmt->execute([$targetUserId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        $user['permissions'] = normalizePermissions(json_decode($user['permissions'], true));
        error_log("Güncellenmiş permissions: " . json_encode($user['permissions']));
        echo json_encode(['success' => true, 'user' => $user]);
    } catch (PDOException $e) {
        error_log("Veritabanı hatası: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Kullanıcı güncellenirken bir hata oluştu: ' . $e->getMessage()]);
    }
    exit();
}

// Kullanıcı sil (sadece admin)
if ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
    try {
        error_log("Kullanıcı silme işlemi başlatıldı");
        
        // Token kontrolü (test modunda devre dışı)
        $testMode = true; // Test için true, production'da false yapın
        if (!$testMode && !$skipTokenCheck && !$token) {
            error_log("Token bulunamadı - 401 yanıtı gönderiliyor");
            http_response_code(401);
            echo json_encode(['success' => false, 'error' => 'Yetkilendirme gerekli']);
            exit();
        }
        
        // Hedef kullanıcı ID'sini kontrol et
        $targetUserId = $_GET['id'] ?? null;
        
        if (!$targetUserId) {
            error_log("Kullanıcı ID'si eksik");
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Kullanıcı ID\'si gerekli']);
            exit();
        }
        
        // Kullanıcıyı sil
        $stmt = $db->prepare("DELETE FROM users WHERE id = ?");
        $stmt->execute([$targetUserId]);
        
        echo json_encode(['success' => true, 'message' => 'Kullanıcı başarıyla silindi']);
        
    } catch (PDOException $e) {
        error_log("Veritabanı hatası: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Kullanıcı silinirken bir hata oluştu: ' . $e->getMessage()]);
    }
    exit();
}

// Desteklenmeyen HTTP metodu
error_log("Desteklenmeyen HTTP metodu: " . $_SERVER['REQUEST_METHOD']);
http_response_code(405);
echo json_encode(['success' => false, 'error' => 'Desteklenmeyen HTTP metodu']);

error_log("API yanıtı gönderiliyor");