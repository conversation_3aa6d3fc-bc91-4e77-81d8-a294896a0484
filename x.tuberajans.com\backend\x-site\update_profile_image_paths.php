<?php
// Veritabanındaki eski profil resmi yollarını güncelleme scripti
ini_set('display_errors', 1);
error_reporting(E_ALL);

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/auth.php';

header('Content-Type: application/json');

function jsonResponse($data, $status = 200) {
    http_response_code($status);
    echo json_encode($data);
    exit;
}

try {
    // Token doğrulama - requireAuthToken() $db_takip kullanıyor, bu yüzden önce onu kontrol edelim
    if (!isset($db_takip) || !$db_takip) {
        jsonResponse(['success' => false, 'error' => 'Veritabanı bağlantısı bulunamadı.'], 500);
    }

    $userId = requireAuthToken();

    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        jsonResponse(['success' => false, 'error' => 'Sadece POST isteği destekleniyor.'], 405);
    }

    // Publisher'lar için eski /img/ yollarını /uploads/profile_images/ olarak güncelle
    $updateQuery = "UPDATE publisher_info SET profile_image = REPLACE(profile_image, '/img/', '/uploads/profile_images/') WHERE profile_image LIKE '/img/%'";
    $updateStmt = $db->prepare($updateQuery);
    $updateStmt->execute();
    $publisherUpdated = $updateStmt->rowCount();

    // Influencer'lar için de kontrol edelim (eğer varsa)
    $updateInfluencerQuery = "UPDATE influencer_info SET profile_image = REPLACE(profile_image, '/img/', '/uploads/influencers/') WHERE profile_image LIKE '/img/%'";
    $updateInfluencerStmt = $db->prepare($updateInfluencerQuery);
    $updateInfluencerStmt->execute();
    $influencerUpdated = $updateInfluencerStmt->rowCount();

    // Ayrıca NULL olan profil resimlerini de kontrol edelim ve username bazlı yol oluşturalım
    $nullUpdateQuery = "UPDATE publisher_info SET profile_image = CONCAT('/uploads/profile_images/', username, '.jpg') WHERE profile_image IS NULL AND username IS NOT NULL AND username != ''";
    $nullUpdateStmt = $db->prepare($nullUpdateQuery);
    $nullUpdateStmt->execute();
    $nullUpdated = $nullUpdateStmt->rowCount();

    jsonResponse([
        'success' => true,
        'message' => 'Profil resmi yolları başarıyla güncellendi.',
        'publisher_updated' => $publisherUpdated,
        'influencer_updated' => $influencerUpdated,
        'null_updated' => $nullUpdated
    ]);

} catch (Exception $e) {
    jsonResponse(['success' => false, 'error' => $e->getMessage()]);
}
