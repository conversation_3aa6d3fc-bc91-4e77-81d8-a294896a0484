<?php
/**
 * Akademi AI Servisi
 * Bu API AI modellerini ve konuşmaları yönetmek için kullanılır
 */

// Hata ayıklama
ini_set('display_errors', 1);
error_reporting(E_ALL);

// CORS ve JSON header'ları
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS, DELETE');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=utf-8');

// OPTIONS istekleri için hızlıca 200 dön
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Ortak config dosyasını dahil et
require_once __DIR__ . '/../config/config.php';

// JSON yanıt fonksiyonu
if (!function_exists('jsonResponse')) {
    function jsonResponse($data, $status = 200) {
        http_response_code($status);
header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}

// Auth kontrolü (checkAuth fonksiyonu - diğer API'ler gibi)
if (!function_exists('checkAuth')) {
    function checkAuth() {
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
        
        if (strpos($authHeader, 'Bearer ') === 0) {
            $token = substr($authHeader, 7);
            if (strlen($token) > 10) { // Basit token kontrolü
                return true;
            }
        }
        return false;
    }
}

try {
    $method = $_SERVER['REQUEST_METHOD'];
    $action = $_GET['action'] ?? '';

    // Veritabanı bağlantısını al
    $pdo = $db_akademi;

    // Veritabanı bağlantısını kontrol et
    if (!$pdo) {
        throw new Exception('Veritabanı bağlantısı kurulamadı');
    }

    // İlk çalıştırmada tabloları ve verileri oluştur
    initializeAITables($pdo);

    // Action'a göre auth kontrolü yap
    if ($method === 'POST' || $method === 'PUT' || $method === 'DELETE') {
        if (!checkAuth()) {
            jsonResponse(['success' => false, 'message' => 'Bu işlemi gerçekleştirmek için giriş yapmalısınız.'], 401);
        }
    }

    switch ($action) {
        case 'models':
            handleModels($pdo, $method);
            break;
        case 'settings':
            handleSettings($pdo, $method);
            break;
        case 'generate':
            handleGenerate($pdo, $method);
            break;
        case 'conversations':
            handleConversations($pdo, $method);
            break;
        case 'documents':
            handleDocuments($pdo, $method);
            break;
        default:
            jsonResponse(['success' => false, 'message' => 'Geçersiz action'], 400);
    }
} catch (Exception $e) {
    error_log("AI Service API error: " . $e->getMessage());
    jsonResponse(['success' => false, 'message' => $e->getMessage()], 500);
}

// AI tablolarını ve verilerini başlat
function initializeAITables($db) {
    // AI Modelleri tablosunu kontrol et ve oluştur
    $db->exec("CREATE TABLE IF NOT EXISTS ai_models (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        provider VARCHAR(50) NOT NULL,
        model_id VARCHAR(100) NOT NULL,
        display_name VARCHAR(150) NOT NULL,
        description TEXT,
        max_tokens INT DEFAULT 4000,
        cost_per_1k_tokens DECIMAL(10,6) DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        is_default BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");

    // AI Ayarları tablosunu kontrol et ve oluştur
    $db->exec("CREATE TABLE IF NOT EXISTS ai_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) NOT NULL UNIQUE,
        setting_value TEXT,
        description TEXT,
        is_encrypted BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");

    // AI Konuşmaları tablosunu kontrol et ve oluştur
    $db->exec("CREATE TABLE IF NOT EXISTS ai_conversations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        session_id VARCHAR(100),
        model_id INT,
        prompt TEXT NOT NULL,
        response TEXT,
        tokens_used INT DEFAULT 0,
        response_time DECIMAL(5,3) DEFAULT 0,
        status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
        error_message TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (model_id) REFERENCES ai_models(id) ON DELETE SET NULL
    )");

    // AI Eğitim Dokümanları tablosunu kontrol et ve oluştur
    $db->exec("CREATE TABLE IF NOT EXISTS ai_training_documents (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        category VARCHAR(100),
        content LONGTEXT,
        file_path VARCHAR(500),
        file_type VARCHAR(50),
        file_size INT,
        status ENUM('active', 'inactive', 'processing') DEFAULT 'active',
        uploaded_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");

    // Varsayılan modelleri kontrol et ve ekle
    $stmt = $db->query("SELECT COUNT(*) FROM ai_models");
    $modelCount = $stmt->fetchColumn();

    if ($modelCount == 0) {
        $models = [
            ['GPT-4 Turbo', 'openai', 'gpt-4-turbo-preview', 'GPT-4 Turbo', 'En gelişmiş OpenAI modeli, yüksek performans', 128000, 0.01, 1, 1],
            ['GPT-4', 'openai', 'gpt-4', 'GPT-4', 'OpenAI GPT-4 modeli, güvenilir performans', 8192, 0.03, 1, 0],
            ['GPT-3.5 Turbo', 'openai', 'gpt-3.5-turbo', 'GPT-3.5 Turbo', 'Hızlı ve ekonomik OpenAI modeli', 16385, 0.001, 1, 0],
            ['Claude 3 Sonnet', 'anthropic', 'claude-3-sonnet-20240229', 'Claude 3 Sonnet', 'Anthropic Claude 3 Sonnet, dengeli performans', 200000, 0.003, 1, 0],
            ['Claude 3 Haiku', 'anthropic', 'claude-3-haiku-20240307', 'Claude 3 Haiku', 'Hızlı ve ekonomik Anthropic modeli', 200000, 0.00025, 1, 0],
            ['Gemini Pro', 'google', 'gemini-pro', 'Gemini Pro', 'Google Gemini Pro modeli', 32768, 0.0005, 1, 0]
        ];

        $stmt = $db->prepare("INSERT INTO ai_models (name, provider, model_id, display_name, description, max_tokens, cost_per_1k_tokens, is_active, is_default) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");

        foreach ($models as $model) {
            $stmt->execute($model);
        }
    }

    // Varsayılan ayarları kontrol et ve ekle
    $stmt = $db->query("SELECT COUNT(*) FROM ai_settings");
    $settingCount = $stmt->fetchColumn();

    if ($settingCount == 0) {
        $settings = [
            ['openai_api_key', '', 'OpenAI API anahtarı', 1],
            ['anthropic_api_key', '', 'Anthropic API anahtarı', 1],
            ['google_api_key', '', 'Google AI API anahtarı', 1],
            ['default_model_id', '1', 'Varsayılan AI modeli ID', 0],
            ['max_conversation_history', '10', 'Maksimum konuşma geçmişi sayısı', 0],
            ['system_prompt', 'Sen Tuber Akademi\'nin AI asistanısın. Kullanıcılara TikTok içerik üretimi, canlı yayın stratejileri ve sosyal medya pazarlama konularında yardım ediyorsun. Türkçe yanıt ver ve profesyonel bir dil kullan.', 'Sistem prompt metni', 0]
        ];

        $stmt = $db->prepare("INSERT INTO ai_settings (setting_key, setting_value, description, is_encrypted) VALUES (?, ?, ?, ?)");

        foreach ($settings as $setting) {
            $stmt->execute($setting);
        }
    }
}

// AI Modelleri yönetimi
function handleModels($db, $method) {
    if ($method === 'GET') {
        $stmt = $db->query("SELECT * FROM ai_models ORDER BY is_default DESC, provider, name");
        $models = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Boolean değerleri düzelt
        foreach ($models as &$model) {
            $model['is_active'] = (bool)$model['is_active'];
            $model['is_default'] = (bool)$model['is_default'];
            $model['max_tokens'] = (int)$model['max_tokens'];
            $model['cost_per_1k_tokens'] = (float)$model['cost_per_1k_tokens'];
        }

        echo json_encode(['success' => true, 'data' => $models]);
    } elseif ($method === 'PUT') {
        $input = json_decode(file_get_contents('php://input'), true);
        $id = $input['id'];

        // Mevcut model bilgilerini al
        $stmt = $db->prepare("SELECT is_active, is_default FROM ai_models WHERE id = ?");
        $stmt->execute([$id]);
        $currentModel = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$currentModel) {
            echo json_encode(['success' => false, 'message' => 'Model bulunamadı']);
            return;
        }

        // Sadece gönderilen alanları güncelle, diğerleri mevcut değerlerini korusun
        $isActive = isset($input['is_active']) ? ($input['is_active'] ? 1 : 0) : $currentModel['is_active'];
        $isDefault = isset($input['is_default']) ? ($input['is_default'] ? 1 : 0) : $currentModel['is_default'];

        // Önce tüm modelleri default olmaktan çıkar (sadece default yapılıyorsa)
        if (isset($input['is_default']) && $input['is_default']) {
            $db->exec("UPDATE ai_models SET is_default = 0");
        }

        $stmt = $db->prepare("UPDATE ai_models SET is_active = ?, is_default = ? WHERE id = ?");
        $stmt->execute([$isActive, $isDefault, $id]);

        echo json_encode(['success' => true, 'message' => 'Model güncellendi']);
    }
}

// AI Ayarları yönetimi
function handleSettings($db, $method) {
    if ($method === 'GET') {
        $stmt = $db->query("SELECT setting_key, setting_value, description FROM ai_settings WHERE is_encrypted = FALSE");
        $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Şifrelenmiş ayarları maskele
        $stmt = $db->query("SELECT setting_key, description FROM ai_settings WHERE is_encrypted = TRUE");
        $encrypted = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($encrypted as $setting) {
            $settings[] = [
                'setting_key' => $setting['setting_key'],
                'setting_value' => '••••••••••••••••',
                'description' => $setting['description'],
                'is_encrypted' => true
            ];
        }

        echo json_encode(['success' => true, 'data' => $settings]);
    } elseif ($method === 'PUT') {
        $input = json_decode(file_get_contents('php://input'), true);

        foreach ($input['settings'] as $setting) {
            $stmt = $db->prepare("UPDATE ai_settings SET setting_value = ? WHERE setting_key = ?");
            $stmt->execute([$setting['value'], $setting['key']]);
        }

        echo json_encode(['success' => true, 'message' => 'Ayarlar güncellendi']);
    }
}

// AI Yanıt Oluşturma
function handleGenerate($db, $method) {
    if ($method !== 'POST') {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Sadece POST desteklenir']);
        return;
    }

    $input = json_decode(file_get_contents('php://input'), true);
    $prompt = $input['prompt'] ?? '';
    $modelId = $input['model_id'] ?? null;
    $userId = $input['user_id'] ?? 1;
    $sessionId = $input['session_id'] ?? uniqid();

    if (empty($prompt)) {
        echo json_encode(['success' => false, 'message' => 'Prompt gerekli']);
        return;
    }

    // Varsayılan model al
    if (!$modelId) {
        $stmt = $db->query("SELECT id FROM ai_models WHERE is_default = TRUE LIMIT 1");
        $defaultModel = $stmt->fetch(PDO::FETCH_ASSOC);
        $modelId = $defaultModel['id'] ?? 1;
    }

    // Model bilgilerini al
    $stmt = $db->prepare("SELECT * FROM ai_models WHERE id = ? AND is_active = TRUE");
    $stmt->execute([$modelId]);
    $model = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$model) {
        echo json_encode(['success' => false, 'message' => 'Geçersiz model']);
        return;
    }

    // Konuşmayı veritabanına kaydet
    $stmt = $db->prepare("INSERT INTO ai_conversations (user_id, session_id, model_id, prompt, status) VALUES (?, ?, ?, ?, 'pending')");
    $stmt->execute([$userId, $sessionId, $modelId, $prompt]);
    $conversationId = $db->lastInsertId();

    try {
        $startTime = microtime(true);

        // AI API çağrısı yap
        $response = callAIAPI($model, $prompt, $db);

        $endTime = microtime(true);
        $responseTime = round($endTime - $startTime, 3);

        // Yanıtı güncelle
        $stmt = $db->prepare("UPDATE ai_conversations SET response = ?, status = 'completed', response_time = ? WHERE id = ?");
        $stmt->execute([$response['content'], $responseTime, $conversationId]);

        echo json_encode([
            'success' => true,
            'data' => [
                'response' => $response['content'],
                'model' => $model['display_name'],
                'tokens_used' => $response['tokens_used'] ?? 0,
                'response_time' => $responseTime,
                'conversation_id' => $conversationId
            ]
        ]);

    } catch (Exception $e) {
        // Hata durumunu güncelle
        $stmt = $db->prepare("UPDATE ai_conversations SET status = 'failed', error_message = ? WHERE id = ?");
        $stmt->execute([$e->getMessage(), $conversationId]);

        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

// AI API çağrısı
function callAIAPI($model, $prompt, $db) {
    // Sistem prompt'unu al
    $stmt = $db->prepare("SELECT setting_value FROM ai_settings WHERE setting_key = 'system_prompt'");
    $stmt->execute();
    $systemPrompt = $stmt->fetchColumn() ?: 'Sen yardımcı bir AI asistanısın.';

    // API anahtarını al
    $apiKeyField = $model['provider'] . '_api_key';
    $stmt = $db->prepare("SELECT setting_value FROM ai_settings WHERE setting_key = ?");
    $stmt->execute([$apiKeyField]);
    $apiKey = $stmt->fetchColumn();

    if (empty($apiKey)) {
        throw new Exception($model['provider'] . ' API anahtarı bulunamadı');
    }

    // Provider'a göre API çağrısı
    switch ($model['provider']) {
        case 'openai':
            return callOpenAI($model, $systemPrompt, $prompt, $apiKey);
        case 'anthropic':
            return callAnthropic($model, $systemPrompt, $prompt, $apiKey);
        case 'google':
            return callGoogle($model, $systemPrompt, $prompt, $apiKey);
        default:
            throw new Exception('Desteklenmeyen provider: ' . $model['provider']);
    }
}

// OpenAI API çağrısı
function callOpenAI($model, $systemPrompt, $prompt, $apiKey) {
    $data = [
        'model' => $model['model_id'],
        'messages' => [
            ['role' => 'system', 'content' => $systemPrompt],
            ['role' => 'user', 'content' => $prompt]
        ],
        'max_tokens' => min($model['max_tokens'], 4000),
        'temperature' => 0.7
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api.openai.com/v1/chat/completions');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $apiKey
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode !== 200) {
        throw new Exception('OpenAI API hatası: ' . $response);
    }

    $result = json_decode($response, true);

    return [
        'content' => $result['choices'][0]['message']['content'] ?? 'Yanıt alınamadı',
        'tokens_used' => $result['usage']['total_tokens'] ?? 0
    ];
}

// Anthropic API çağrısı
function callAnthropic($model, $systemPrompt, $prompt, $apiKey) {
    $data = [
        'model' => $model['model_id'],
        'max_tokens' => min($model['max_tokens'], 4000),
        'system' => $systemPrompt,
        'messages' => [
            ['role' => 'user', 'content' => $prompt]
        ]
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api.anthropic.com/v1/messages');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'x-api-key: ' . $apiKey,
        'anthropic-version: 2023-06-01'
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode !== 200) {
        throw new Exception('Anthropic API hatası: ' . $response);
    }

    $result = json_decode($response, true);

    return [
        'content' => $result['content'][0]['text'] ?? 'Yanıt alınamadı',
        'tokens_used' => $result['usage']['input_tokens'] + $result['usage']['output_tokens'] ?? 0
    ];
}

// Google API çağrısı (basit implementasyon)
function callGoogle($model, $systemPrompt, $prompt, $apiKey) {
    // Google Gemini API implementasyonu
    // Bu basit bir örnek, gerçek implementasyon daha karmaşık olabilir

    return [
        'content' => 'Google Gemini API henüz tam olarak entegre edilmedi. Bu bir test yanıtıdır.',
        'tokens_used' => 50
    ];
}

// Konuşma geçmişi
function handleConversations($db, $method) {
    if ($method === 'GET') {
        $limit = intval($_GET['limit'] ?? 50);
        $stmt = $db->prepare("
            SELECT c.*, m.display_name as model_name
            FROM ai_conversations c
            LEFT JOIN ai_models m ON c.model_id = m.id
            ORDER BY c.created_at DESC
            LIMIT " . $limit
        );
        $stmt->execute();
        $conversations = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode(['success' => true, 'data' => $conversations]);
    }
}

// Doküman yönetimi
function handleDocuments($db, $method) {
    if ($method === 'GET') {
        $stmt = $db->query("SELECT * FROM ai_training_documents ORDER BY created_at DESC");
        $documents = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo json_encode(['success' => true, 'data' => $documents]);
    } elseif ($method === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);

        $stmt = $db->prepare("INSERT INTO ai_training_documents (title, category, content, status) VALUES (?, ?, ?, 'active')");
        $stmt->execute([$input['title'], $input['category'], $input['content']]);

        echo json_encode(['success' => true, 'message' => 'Doküman eklendi']);
    }
}
?>
