<?php
// Hızlı auth kontrolü - timeout sorunları için
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// OPTIONS isteği için
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Maksimum 5 saniye timeout
set_time_limit(5);

// Sadece check işlemi
if (isset($_GET['check'])) {
    // Hızlı yanıt - veritabanı olmadan
    echo json_encode([
        'authenticated' => false,
        'message' => 'Hızlı kontrol - veritabanı bağlantısı test edilmiyor',
        'timestamp' => date('Y-m-d H:i:s'),
        'server' => $_SERVER['SERVER_NAME'] ?? 'unknown'
    ]);
    exit;
}

// Diğer istekler
echo json_encode([
    'status' => 'quick_auth_ready',
    'message' => 'Hızlı auth servisi çalışıyor',
    'timestamp' => date('Y-m-d H:i:s')
]);
?>
