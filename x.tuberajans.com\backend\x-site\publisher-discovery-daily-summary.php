<?php
require_once __DIR__ . '/../config/config.php';

header('Content-Type: application/json; charset=utf-8');

if (!checkAuth()) {
    error_log("Publisher Discovery Daily Summary API: Auth başarısız");
    echo json_encode(['error' => 'Unauthorized', 'message' => 'Token required']);
    http_response_code(401);
    exit;
}
error_log("Publisher Discovery Daily Summary API: Auth başarılı");

try {
    // Database auto-detection kullan
    $databases = ['tuberaja_yayinci_takip', 'tuberaja_yayinci_akademi', 'tiktok_live_data'];
    $liveDataDatabase = null;
    
    foreach ($databases as $database) {
        try {
            $testQuery = "SELECT 1 FROM $database.live_data LIMIT 1";
            $db->query($testQuery);
            $liveDataDatabase = $database;
            error_log("Publisher Discovery Daily Summary API: live_data tablosu bulundu: $database");
            break;
        } catch (PDOException $e) {
            continue;
        }
    }
    
    if (!$liveDataDatabase) {
        throw new Exception("live_data tablosu bulunamadı");
    }

    // Günlük özet verileri
    $summaryStmt = $db->query("
        SELECT
            DATE(timestamp) as date,
            COUNT(*) as userCount,
            SUM(status = 'Uygun') as suitableUsers,
            SUM(status = 'Uygun Elite') as eliteUsers,
            SUM(message_log = 'Mesaj Gönderildi') as sentMessages,
            ROUND(AVG(viewer_count)) as avgViewer
        FROM $liveDataDatabase.live_data
        GROUP BY DATE(timestamp)
        ORDER BY date DESC
        LIMIT 7
    ");
    $summaryRows = $summaryStmt->fetchAll(PDO::FETCH_ASSOC);

    // Her gün için saatlik ortalama izleyici
    $hourlyStmt = $db->query("
        SELECT
            DATE(timestamp) as date,
            HOUR(timestamp) as hour,
            ROUND(AVG(viewer_count)) as avg_viewers
        FROM $liveDataDatabase.live_data
        GROUP BY DATE(timestamp), HOUR(timestamp)
        ORDER BY date DESC, hour ASC
    ");
    $hourlyRows = $hourlyStmt->fetchAll(PDO::FETCH_ASSOC);
    $hourlyByDate = [];
    foreach ($hourlyRows as $row) {
        $date = $row['date'];
        $hour = (int)$row['hour'];
        $avg = (int)$row['avg_viewers'];
        $hourlyByDate[$date][$hour] = $avg;
    }

    // Her gün için en yüksek ortalamalı 3 saatlik aralığı bul
    foreach ($summaryRows as &$day) {
        $date = $day['date'];
        $hours = $hourlyByDate[$date] ?? [];
        $maxAvg = -1;
        $bestStart = 0;
        for ($h = 0; $h <= 21; $h++) {
            $window = [ $hours[$h] ?? 0, $hours[$h+1] ?? 0, $hours[$h+2] ?? 0 ];
            $windowAvg = array_sum($window) / 3;
            if ($windowAvg > $maxAvg) {
                $maxAvg = $windowAvg;
                $bestStart = $h;
            }
        }
        $end = $bestStart + 3;
        $day['peakHourRange'] = sprintf('%02d:00 - %02d:00', $bestStart, $end);
    }

    // --- FİLTRELENEBİLİR ZİRVE ANALİZİ ---
    $peakStats = [];
    foreach ([7, 30, 60, 90] as $dayCount) {
        $peak60Days = [
            'range' => null,
            'date' => null,
            'weekday' => null,
            'avg' => null
        ];
        $peakMaxAvg = -1;
        $peakBestStart = 0;
        $peakDate = null;
        $peakHours = [];
        $peakWeekday = null;
        $peakAvg = null;
        $turkishDays = ['Pazar', 'Pazartesi', 'Salı', 'Çarşamba', 'Perşembe', 'Cuma', 'Cumartesi'];

        // Saatlik verileri grupla
        $allHourlyStmt = $db->query("
            SELECT
                DATE(timestamp) as date,
                HOUR(timestamp) as hour,
                ROUND(AVG(viewer_count)) as avg_viewers
            FROM $liveDataDatabase.live_data
            WHERE timestamp >= DATE_SUB(NOW(), INTERVAL $dayCount DAY)
            GROUP BY DATE(timestamp), HOUR(timestamp)
            ORDER BY date DESC, hour ASC
        ");
        $allHourlyRows = $allHourlyStmt->fetchAll(PDO::FETCH_ASSOC);
        $hourlyByDate = [];
        foreach ($allHourlyRows as $row) {
            $date = $row['date'];
            $hour = (int)$row['hour'];
            $avg = (int)$row['avg_viewers'];
            $hourlyByDate[$date][$hour] = $avg;
        }
        foreach ($hourlyByDate as $date => $hours) {
            for ($h = 0; $h <= 21; $h++) {
                $window = [ $hours[$h] ?? 0, $hours[$h+1] ?? 0, $hours[$h+2] ?? 0 ];
                $windowAvg = array_sum($window) / 3;
                if ($windowAvg > $peakMaxAvg) {
                    $peakMaxAvg = $windowAvg;
                    $peakBestStart = $h;
                    $peakDate = $date;
                    $peakHours = [$h, $h+1, $h+2];
                    $peakAvg = $windowAvg;
                }
            }
        }
        if ($peakDate !== null) {
            $end = $peakBestStart + 3;
            $peak60Days['range'] = sprintf('%02d:00 - %02d:00', $peakBestStart, $end);
            $peak60Days['date'] = $peakDate;
            $peak60Days['weekday'] = $turkishDays[date('w', strtotime($peakDate))];
            $peak60Days['avg'] = round($peakAvg);
        }
        // Haftanın 7 günü için en yüksek 3 saatlik aralıklar ve en verimli gün
        $weeklyPeaks = [];
        $weekdayStats = [];
        for ($w = 0; $w < 7; $w++) {
            $bestAvg = -1;
            $bestRange = null;
            $bestDate = null;
            $bestStart = 0;
            $allAverages = [];
            foreach ($hourlyByDate as $date => $hours) {
                $weekday = (int)date('w', strtotime($date));
                if ($weekday !== $w) continue;
                for ($h = 0; $h <= 21; $h++) {
                    $window = [ $hours[$h] ?? 0, $hours[$h+1] ?? 0, $hours[$h+2] ?? 0 ];
                    $windowAvg = array_sum($window) / 3;
                    $allAverages[] = $windowAvg;
                    if ($windowAvg > $bestAvg) {
                        $bestAvg = $windowAvg;
                        $bestRange = sprintf('%02d:00 - %02d:00', $h, $h+3);
                        $bestDate = $date;
                        $bestStart = $h;
                    }
                }
            }
            $weeklyPeaks[] = [
                'weekday' => $turkishDays[$w],
                'range' => $bestRange,
                'avg' => $bestAvg > 0 ? round($bestAvg) : 0
            ];
            $weekdayStats[$w] = count($allAverages) > 0 ? array_sum($allAverages) / count($allAverages) : 0;
        }
        $maxWeekday = array_keys($weekdayStats, max($weekdayStats));
        $mostEfficientDay = [
            'weekday' => $turkishDays[$maxWeekday[0]],
            'avg' => round($weekdayStats[$maxWeekday[0]])
        ];
        $peak60Days['weeklyPeaks'] = $weeklyPeaks;
        $peak60Days['mostEfficientDay'] = $mostEfficientDay;
        $peakStats[(string)$dayCount] = $peak60Days;
    }

    echo json_encode(['success' => true, 'data' => $summaryRows, 'peakStats' => $peakStats]);
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
} 