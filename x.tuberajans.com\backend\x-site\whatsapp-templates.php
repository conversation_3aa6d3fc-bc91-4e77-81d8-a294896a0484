<?php
header('Content-Type: application/json; charset=utf-8');

// WhatsApp Cloud API bilgilerin
$access_token = 'EAAOx5pJHA1oBO6SzKxxXYlISUGwgiIr6Gob7PF7XngGvJM2cUz6WTZAC8BF96Vykuw3BJAclP3zRvcxZC1E3gRB4b27vA2byilX3rnpFB3d3ZBRI3U1Cwem1BQiiZAV95EnT183umcA6zuUeivXagHGkRXfnZATjRDRBpiJST26zZCSX41nN5AZCEsfPHH1ZAAyzIsPh3qZAm8iW4oZBVQe1AjJ2E5NLN86Y3MhWh1d0zo'; // <-- Buraya kendi uzun süreli tokenini gir
$waba_id = '****************'; // <-- Buraya kendi WhatsApp Business Account ID'ni gir

$url = "https://graph.facebook.com/v18.0/$waba_id/message_templates?access_token=$access_token";

$ch = curl_init($url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = curl_exec($ch);
curl_close($ch);

echo $response; 