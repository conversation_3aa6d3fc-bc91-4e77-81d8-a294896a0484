import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Status_checker'daki ayar<PERSON>la aynı yollar
CHROME_DRIVER_PATH = "chromedriver.exe"
CHROME_PROFILE_PATH = r"C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data"
CHROME_PROFILE_DIRECTORY = "Profile 1"
CHROME_BINARY_PATH = r"C:\Users\<USER>\Downloads\chrome-win64\chrome-win64\chrome.exe"


def setup_driver():
    options = ChromeOptions()
    options.add_argument(f"user-data-dir={CHROME_PROFILE_PATH}")
    options.add_argument(f"profile-directory={CHROME_PROFILE_DIRECTORY}")
    options.binary_location = CHROME_BINARY_PATH
    options.add_argument("--start-maximized")
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_argument("--disable-notifications")
    options.add_argument("--disable-popup-blocking")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--disable-extensions")
    options.add_argument("--disable-gpu")
    options.add_argument("--no-first-run")
    options.add_argument("--no-default-browser-check")
    options.add_experimental_option("excludeSwitches", ["enable-automation", "enable-logging"])
    options.add_experimental_option("useAutomationExtension", False)
    options.add_experimental_option("detach", True)
    # options.add_argument("--headless=new")  # Headless mod kapalı

    service = ChromeService(executable_path=CHROME_DRIVER_PATH)
    driver = webdriver.Chrome(service=service, options=options)

    # Anti-bot için ek JS
    try:
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    except Exception:
        pass
    return driver


def wait_for_live_page_load(driver, timeout=60):
    """
    Yayın sayfasının yüklendiğini anlamak için farklı elementleri kontrol eder.
    Birden fazla seçici deneyerek sayfanın yüklendiğinden emin olur.
    """
    print("Yayın sayfasının yüklenmesi bekleniyor...")
    
    # Ekran görüntüsünden tespit edilen TikTok sohbet elementleri
    selectors = [
        "div[class*='DivChatRoom']",  # Herhangi bir chat room içeren div
        "div[class*='css-1rpw7mm-DivChatRoomContent']", # Ekran görüntüsündeki ana element
        "div[class*='DivChatRoomAnimation']",
        "div[class*='DivChatTopContainer']",
        "div[class*='DivLikeContainer']",   # Beğeni container'ı
        "div[id*='tiktok-live-main-container']", # Ana live container
        "main[id*='tiktok-live-main-container']" # Main tag'i
    ]
    
    for selector in selectors:
        try:
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, selector))
            )
            print(f"Element bulundu: {selector}")
            return True
        except TimeoutException:
            print(f"Element bulunamadı: {selector}")
    
    # Yukarıdaki tüm seçiciler başarısız olursa
    try:
        # Son bir kontrol olarak body'de "CANLI" kelimesini ara
        body_text = driver.find_element(By.TAG_NAME, "body").text
        if "CANLI" in body_text or "canlı" in body_text.lower():
            print("Sayfa metni içinde 'CANLI' kelimesi bulundu!")
            return True
    except:
        pass
    
    return False


def click_like_button(driver, interval=0.2):
    print("Beğeni butonuna otomatik tıklama başlatıldı. Çıkmak için Ctrl+C.")
    try:
        while True:
            try:
                like_btn = driver.find_element(By.CSS_SELECTOR, "div.css-1mk0i7a-DivLikeBtnIcon")
                ActionChains(driver).move_to_element(like_btn).click().perform()
                print("Beğeni gönderildi", end="\r")
            except Exception as e:
                print("Beğeni butonu bulunamadı veya tıklanamadı!", end="\r")
            time.sleep(interval)
    except KeyboardInterrupt:
        print("\nİşlem kullanıcı tarafından durduruldu.")


def main():
    print("TikTok kullanıcı adını girin (ör: ornek_kullanici): ", end="")
    username = input().strip().lstrip("@")
    if not username:
        print("Kullanıcı adı boş olamaz!")
        return
    url = f"https://www.tiktok.com/@{username}/live"
    print(f"Canlı yayına gidiliyor: {url}")

    driver = setup_driver()
    driver.get(url)
    
    # Sayfanın yüklenmesi için kısa bir bekleme
    time.sleep(5)
    
    # Yayın sayfasının yüklendiğini kontrol et
    if not wait_for_live_page_load(driver):
        print("Yayın yüklenemedi veya sohbet kutusu bulunamadı!")
        print("Kullanıcının canlı yayında olduğundan emin olun.")
        driver.quit()
        return
    
    print("Yayın yüklendi. L tuşuna sürekli basılı tutuluyor... Çıkmak için Ctrl+C")
    actions = ActionChains(driver)
    try:
        actions.key_down('l').perform()
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nİşlem kullanıcı tarafından durduruldu.")
    finally:
        actions.key_up('l').perform()
        driver.quit()

    # Yayın sayfasının yüklendiğini kontrol ettikten sonra:
    click_like_button(driver, interval=0.2)

if __name__ == "__main__":
    main() 