<?php
if (!isset($_GET['url'])) {
    http_response_code(400);
    exit('No URL');
}
$url = $_GET['url'];
$ch = curl_init($url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0');
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
$data = curl_exec($ch);
$contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
curl_close($ch);

if ($data) {
    header('Content-Type: ' . $contentType);
    echo $data;
} else {
    http_response_code(404);
    echo 'Image not found';
} 