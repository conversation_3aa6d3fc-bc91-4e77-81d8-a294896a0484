import{j as e}from"./reactDnd-uQSTYBkW.js";import{r as n}from"./vendor-CnpYymF8.js";import{I as O,p as Qe,m as ws,b as pe,l as ee,R as je,h as ge,o as ks,Q as Ss,O as Cs,d as be,S as Ze,T as es,w as Ms,c as ss,A as As}from"./App-C_UskXOj.js";import{A as se}from"./index-BVn_ohNQ.js";import{h as P}from"./utils-CtuI0RRe.js";import{ah as Es,ai as Is,k as p,x as u,B as Y,A as ts,$ as Rs,K as L,l as as,M as z,I,X as ns,j as ye,T as $s,U as Ts,p as te,w as D,n as K,Y as ae,t as zs,q as Os,u as Bs}from"./antd-BfejY-CV.js";import{R as Ps}from"./ExclamationCircleOutlined-D-Q2kGg7.js";import{R as rs}from"./MoreOutlined-A4OQaSTe.js";import{R as ne}from"./ArrowLeftOutlined-DsJRWt1u.js";import{R as Ls}from"./InfoCircleOutlined-C4X-YJMb.js";import{R as Ds}from"./EllipsisOutlined-BDDrCCML.js";import"./charts-6B1FLgFz.js";var _s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M842 454c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8 0 140.3-113.7 254-254 254S258 594.3 258 454c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8 0 168.7 126.6 307.9 290 327.6V884H326.7c-13.7 0-24.7 14.3-24.7 32v36c0 4.4 2.8 8 6.2 8h407.6c3.4 0 6.2-3.6 6.2-8v-36c0-17.7-11-32-24.7-32H548V782.1c165.3-18 294-158 294-328.1zM512 624c93.9 0 170-75.2 170-168V232c0-92.8-76.1-168-170-168s-170 75.2-170 168v224c0 92.8 76.1 168 170 168zm-94-392c0-50.6 41.9-92 94-92s94 41.4 94 92v224c0 50.6-41.9 92-94 92s-94-41.4-94-92V232z"}}]},name:"audio",theme:"outlined"};function Ne(){return Ne=Object.assign?Object.assign.bind():function(r){for(var l=1;l<arguments.length;l++){var t=arguments[l];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(r[o]=t[o])}return r},Ne.apply(this,arguments)}const Ws=(r,l)=>n.createElement(O,Ne({},r,{ref:l,icon:_s})),Us=n.forwardRef(Ws);function we(){return we=Object.assign?Object.assign.bind():function(r){for(var l=1;l<arguments.length;l++){var t=arguments[l];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(r[o]=t[o])}return r},we.apply(this,arguments)}const Hs=(r,l)=>n.createElement(O,we({},r,{ref:l,icon:Es})),re=n.forwardRef(Hs);var Ys={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M553.1 509.1l-77.8 99.2-41.1-52.4a8 8 0 00-12.6 0l-99.8 127.2a7.98 7.98 0 006.3 12.9H696c6.7 0 10.4-7.7 6.3-12.9l-136.5-174a8.1 8.1 0 00-12.7 0zM360 442a40 40 0 1080 0 40 40 0 10-80 0zm494.6-153.4L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file-image",theme:"outlined"};function ke(){return ke=Object.assign?Object.assign.bind():function(r){for(var l=1;l<arguments.length;l++){var t=arguments[l];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(r[o]=t[o])}return r},ke.apply(this,arguments)}const Ks=(r,l)=>n.createElement(O,ke({},r,{ref:l,icon:Ys})),Gs=n.forwardRef(Ks);function Se(){return Se=Object.assign?Object.assign.bind():function(r){for(var l=1;l<arguments.length;l++){var t=arguments[l];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(r[o]=t[o])}return r},Se.apply(this,arguments)}const Vs=(r,l)=>n.createElement(O,Se({},r,{ref:l,icon:Is})),ls=n.forwardRef(Vs);var Xs={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M878.3 392.1L631.9 145.7c-6.5-6.5-15-9.7-23.5-9.7s-17 3.2-23.5 9.7L423.8 306.9c-12.2-1.4-24.5-2-36.8-2-73.2 0-146.4 24.1-206.5 72.3a33.23 33.23 0 00-2.7 49.4l181.7 181.7-215.4 215.2a15.8 15.8 0 00-4.6 9.8l-3.4 37.2c-.9 9.4 6.6 17.4 15.9 17.4.5 0 1 0 1.5-.1l37.2-3.4c3.7-.3 7.2-2 9.8-4.6l215.4-215.4 181.7 181.7c6.5 6.5 15 9.7 23.5 9.7 9.7 0 19.3-4.2 25.9-12.4 56.3-70.3 79.7-158.3 70.2-243.4l161.1-161.1c12.9-12.8 12.9-33.8 0-46.8zM666.2 549.3l-24.5 24.5 3.8 34.4a259.92 259.92 0 01-30.4 153.9L262 408.8c12.9-7.1 26.3-13.1 40.3-17.9 27.2-9.4 55.7-14.1 84.7-14.1 9.6 0 19.3.5 28.9 1.6l34.4 3.8 24.5-24.5L608.5 224 800 415.5 666.2 549.3z"}}]},name:"pushpin",theme:"outlined"};function Ce(){return Ce=Object.assign?Object.assign.bind():function(r){for(var l=1;l<arguments.length;l++){var t=arguments[l];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(r[o]=t[o])}return r},Ce.apply(this,arguments)}const Js=(r,l)=>n.createElement(O,Ce({},r,{ref:l,icon:Xs})),Fs=n.forwardRef(Js);var qs={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM664 533h-48.1c-4.2 0-7.8 3.2-8.1 7.4C604 589.9 562.5 629 512 629s-92.1-39.1-95.8-88.6c-.3-4.2-3.9-7.4-8.1-7.4H360a8 8 0 00-8 8.4c4.4 84.3 74.5 151.6 160 151.6s155.6-67.3 160-151.6a8 8 0 00-8-8.4z"}}]},name:"smile",theme:"outlined"};function Me(){return Me=Object.assign?Object.assign.bind():function(r){for(var l=1;l<arguments.length;l++){var t=arguments[l];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(r[o]=t[o])}return r},Me.apply(this,arguments)}const Qs=(r,l)=>n.createElement(O,Me({},r,{ref:l,icon:qs})),Zs=n.forwardRef(Qs);var et={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3zM664.8 561.6l36.1 210.3L512 672.7 323.1 772l36.1-210.3-152.8-149L417.6 382 512 190.7 606.4 382l211.2 30.7-152.8 148.9z"}}]},name:"star",theme:"outlined"};function Ae(){return Ae=Object.assign?Object.assign.bind():function(r){for(var l=1;l<arguments.length;l++){var t=arguments[l];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(r[o]=t[o])}return r},Ae.apply(this,arguments)}const st=(r,l)=>n.createElement(O,Ae({},r,{ref:l,icon:et})),tt=n.forwardRef(st),{TabPane:At}=Os,{TextArea:at}=I,{Option:Et}=Bs,{Title:It,Text:d,Paragraph:ve}=$s,le=r=>!r||typeof r!="string"?"":DOMPurify.sanitize(r.trim()),is=r=>/^\+?[1-9]\d{1,14}$/.test(r.replace(/\s+/g,"")),nt=(r,l)=>{const t=[];return()=>{const o=Date.now(),y=o-l;for(;t.length>0&&t[0]<y;)t.shift();return t.length>=r?!1:(t.push(o),!0)}},rt=nt(10,6e4),lt=(r,l)=>{let t=null;return(...o)=>{t!==null&&(clearTimeout(t),t=null),t=setTimeout(()=>r(...o),l)}},it=(r,l)=>{const[t,o]=n.useState(!1),y=n.useRef(null);return n.useEffect(()=>{if(y.current&&(y.current.close(),y.current=null,o(!1)),r){const C=`/backend/x-site/whatsapp-sse.php?contactId=${r}`,w=new EventSource(C);w.onopen=()=>{o(!0)},w.addEventListener("connected",R=>{o(!0)}),w.addEventListener("message",R=>{try{const G=JSON.parse(R.data);G.type==="new_message"&&G.contactId===r&&l()}catch(G){}}),w.addEventListener("error",R=>{o(!1),w.close()}),w.addEventListener("close",()=>{o(!1)}),y.current=w}return()=>{y.current&&(y.current.close(),y.current=null,o(!1))}},[r,l]),{connected:t}},Rt=()=>{const[r,l]=n.useState([]),[t,o]=n.useState(null),[y,C]=n.useState([]),[w,R]=n.useState(""),[G,ct]=n.useState(!1),[os,V]=n.useState(!0),[cs,X]=n.useState(!1),[Ee,Ie]=n.useState(!1),[J,_]=n.useState(null),[ds,ie]=n.useState(null),[k,oe]=n.useState(null),[Re,ms]=n.useState([]),[ce,$e]=n.useState(null),[Te,W]=n.useState("1"),[ze,dt]=n.useState(""),[U,Oe]=n.useState(""),[F,de]=n.useState(!1),[Be,q]=n.useState(!1),[mt,ht]=n.useState(!1),[hs,Q]=n.useState(!1),[Pe,me]=n.useState(""),[Le,he]=n.useState(""),[B,us]=n.useState({totalMessages:0,messagesLast24h:0,activeChats:0,deliveryRate:0}),[E,De]=n.useState(null),[ut,xt]=n.useState([]),[N,_e]=n.useState(navigator.onLine),[ue,ft]=n.useState({}),S=n.useRef({messages:{},contacts:null}),xe=60,We=n.useRef(null),xs=n.useRef(null),Ue=n.useRef(null);n.useEffect(()=>{const s=()=>{_e(!0),ie(null),H(!0)},a=()=>{_e(!1),ie("İnternet bağlantısı kesildi")};return window.addEventListener("online",s),window.addEventListener("offline",a),()=>{window.removeEventListener("online",s),window.removeEventListener("offline",a)}},[]),n.useEffect(()=>()=>{Ue.current&&Ue.current.abort()},[]);const He=n.useCallback(lt(s=>{Oe(le(s))},300),[]);n.useEffect(()=>{He(ze)},[ze,He]);const Ye=n.useMemo(()=>{if(!U.trim())return r;const s=U.toLowerCase();return r.filter(a=>a.name.toLowerCase().includes(s)||a.phone.toLowerCase().includes(s))},[r,U]),Ke=n.useCallback(()=>{window.requestAnimationFrame(()=>{var s;(s=We.current)==null||s.scrollIntoView({behavior:"smooth"})})},[]),Ge=n.useRef(0),Ve=s=>P.unix(s).format("HH:mm");n.useEffect(()=>{y.length!==Ge.current&&(Ke(),Ge.current=y.length)},[y,Ke]);const fs=n.useCallback((s,a,i)=>{const f=a===0||i[a-1].fromMe!==s.fromMe,c=a===0||s.timestamp-i[a-1].timestamp>300;return e.jsxs("div",{children:[a>0&&c&&P.unix(s.timestamp).format("DD MMMM")!==P.unix(i[a-1].timestamp).format("DD MMMM")&&e.jsx("div",{className:"chat-date-separator",children:e.jsx("div",{className:"chat-date-bubble",children:P.unix(s.timestamp).format("DD MMMM YYYY")})}),e.jsx("div",{className:`flex ${s.fromMe?"justify-end":"justify-start"}
            ${!f&&!c?"mt-1":"mt-4"}`,children:e.jsxs("div",{className:`message-bubble max-w-xs md:max-w-sm rounded-lg px-3 py-2 shadow-sm ${s.fromMe?"bg-dcf8c6 rounded-tr-none":"bg-white rounded-tl-none"}`,style:{marginTop:"2px",maxWidth:window.innerWidth<640?"75%":"60%",wordBreak:"break-word"},children:[s.mediaUrl&&e.jsxs("div",{className:"mb-2 overflow-hidden",children:[s.mediaType==="image"&&e.jsxs("div",{className:"relative",children:[e.jsx("img",{src:s.mediaUrl,alt:"Media",className:"rounded max-w-full h-auto",loading:"lazy"}),e.jsx("div",{className:"absolute bottom-2 right-2",children:e.jsx(p,{type:"text",icon:e.jsx(Qe,{}),size:"small",className:"bg-black bg-opacity-50 text-white rounded-full h-8 w-8 flex items-center justify-center"})})]}),s.mediaType==="document"&&e.jsxs("div",{className:"flex items-center bg-gray-50 p-2 rounded",children:[e.jsx(ls,{className:"mr-2 text-blue-500"}),e.jsxs("div",{className:"flex-grow",children:[e.jsx("div",{className:"text-sm font-medium truncate",children:"Document"}),e.jsx("div",{className:"text-xs text-gray-500",children:"PDF • 2.4 MB"})]}),e.jsx(Qe,{className:"text-gray-500"})]})]}),e.jsx("div",{children:typeof s.text=="string"?s.text:s.text&&typeof s.text=="object"&&"body"in s.text?s.text.body:""}),e.jsxs("div",{className:"text-right mt-1 flex justify-end items-center",children:[e.jsx("span",{className:"message-time",children:Ve(s.timestamp)}),s.fromMe&&e.jsxs("span",{className:"message-status",children:[s.status==="sent"&&e.jsx("span",{className:"message-status-sent",children:"✓"}),s.status==="delivered"&&e.jsx("span",{className:"message-status-delivered",children:"✓✓"}),s.status==="read"&&e.jsx("span",{className:"message-status-read",children:"✓✓"})]})]})]})})]},s.id)},[Ve]),{connected:Xe}=it(t?t.id:null,()=>{t&&(Z(t.id,!0),window.updateWhatsAppUnreadCount&&window.updateWhatsAppUnreadCount(0))});n.useEffect(()=>{window.updateWhatsAppUnreadCount&&window.updateWhatsAppUnreadCount(0),fetch("/backend/x-site/whatsapp-stats.php?action=reset_unread_count",{method:"POST",credentials:"same-origin"}).catch(s=>{})},[]),n.useEffect(()=>(H(),gs(),bs(),ys(),()=>{}),[]),n.useEffect(()=>{if(!(()=>{try{return typeof EventSource!="undefined"}catch(i){return!1}})()){const i=setInterval(()=>{t&&Z(t.id,!0)},5e3);return()=>clearInterval(i)}},[]);const ps=s=>{o(s),q(!0),Z(s.id),l(a=>a.map(i=>i.id===s.id||i.phone===s.phone?{...i,unreadCount:0}:i))},H=async(s=!1)=>{s||V(!0);const a=S.current.contacts,i=Math.floor(Date.now()/1e3);if(a&&i-a.timestamp<xe&&!s){l(a.data),V(!1);return}const f=new AbortController,c=f.signal;try{const m=await fetch("/backend/x-site/whatsapp-contacts.php",{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`},signal:c});if(!m.ok)throw new Error(`API yanıt hatası: ${m.status} ${m.statusText}`);let g=(await m.json()).contacts||[];const M=JSON.parse(localStorage.getItem("customContacts")||"[]"),v=new Map;M.forEach(h=>{v.set(h.phone,h)}),g.forEach(h=>{if(v.has(h.phone)){const j=v.get(h.phone);v.set(h.phone,{...h,name:j.name||h.name||h.phone,lastMessage:j.lastMessage||h.lastMessage,unreadCount:j.unreadCount||0})}else v.set(h.phone,{...h,name:h.name||h.phone,unreadCount:0})});const $=Array.from(v.values());c.aborted||(S.current.contacts={data:$,timestamp:i},l($),s||$.forEach(async h=>{try{if(c.aborted)return;const j=S.current.messages[h.phone.replace(/^\+/,"")];if(j&&i-j.timestamp<xe){if(j.data.length>0){const b=j.data[j.data.length-1];l(fe=>fe.map(T=>T.phone===h.phone?{...T,lastMessage:{text:typeof b.text=="string"?b.text:b.text&&typeof b.text=="object"&&"body"in b.text?b.text.body:"",timestamp:b.timestamp}}:T))}return}const A=await ws.get(`${se.X_SITE_BASE_URL}${se.ENDPOINTS.WHATSAPP_MESSAGES}?contactId=${h.phone.replace(/^\+/,"")}`,{signal:c});if(A.data&&A.data.length>0&&!c.aborted){const b=A.data[A.data.length-1];S.current.messages[h.phone.replace(/^\+/,"")]={data:A.data,timestamp:i},l(fe=>fe.map(T=>T.phone===h.phone?{...T,lastMessage:{text:typeof b.text=="string"?b.text:b.text&&typeof b.text=="object"&&"body"in b.text?b.text.body:"",timestamp:b.timestamp}}:T))}}catch(j){j.name}})),!s&&!c.aborted&&V(!1)}catch(m){m.name!=="AbortError"&&(a&&l(a.data),!s&&!c.aborted&&(u.error("Kişiler yüklenirken hata oluştu: "+(m.message||"Bilinmeyen hata")),V(!1)))}return()=>{f.abort()}},Z=async(s,a=!1)=>{a||X(!0);const i=S.current.messages[s],f=Math.floor(Date.now()/1e3);if(i&&f-i.timestamp<xe&&!a){C(i.data),X(!1);return}const c=new AbortController,m=c.signal;try{let x;const g=`${se.X_SITE_BASE_URL}${se.ENDPOINTS.WHATSAPP_MESSAGES}?contactId=${s}`,M=await fetch(g,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`},signal:m});if(m.aborted)return;if(M.ok&&(x=await M.json(),x&&Array.isArray(x)&&(a?C(v=>{const $=new Set(v.map(j=>j.id)),h=[...v];return x.forEach(j=>{$.has(j.id)||h.push(j)}),h.sort((j,A)=>(j.timestamp||0)-(A.timestamp||0)),S.current.messages[s]={data:h,timestamp:f},h}):(S.current.messages[s]={data:x,timestamp:f},C(x)))),!a&&!m.aborted&&X(!1),!m.aborted&&x&&x.length>0){const v=x[x.length-1];l($=>$.map(h=>{var j;const A=(h.phone||"").replace(/^\+/,""),b=((t==null?void 0:t.phone)||"").replace(/^\+/,"");return A===b?{...h,lastMessage:{text:typeof v.text=="string"?v.text:((j=v.text)==null?void 0:j.body)||"",timestamp:v.timestamp}}:h})),a||js(s)}}catch(x){x.name!=="AbortError"&&(i&&C(i.data),!a&&!m.aborted&&(u.error("Mesajlar yüklenirken hata oluştu: "+(x.message||"Bilinmeyen hata")),X(!1)))}return()=>{c.abort()}},js=async s=>{const a=new AbortController,i=a.signal;try{const f=y.filter(m=>!m.fromMe&&!m.read).map(m=>m.id);if(f.length===0)return;f.length>0&&u.loading({content:"Mesajlar okundu olarak işaretleniyor...",key:"markAsRead",duration:.5});const c=await fetch("/backend/x-site/whatsapp-messages.php/mark-read",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`},body:JSON.stringify({messageIds:f,contactId:s}),signal:i});if(i.aborted)return;if(c.ok){const m=await c.json();m&&m.success?(u.success({content:"Mesajlar okundu olarak işaretlendi",key:"markAsRead",duration:.5}),C(x=>x.map(g=>({...g,read:g.fromMe?g.read:!0}))),l(x=>x.map(g=>g.id===s||g.phone.replace(/^\+/,"")===s?{...g,unreadCount:0}:g)),window.updateWhatsAppUnreadCount&&typeof window.updateWhatsAppUnreadCount=="function"&&window.updateWhatsAppUnreadCount(0)):u.info({content:"Mesajlar zaten okundu olarak işaretlenmiş",key:"markAsRead"})}else u.error({content:`Mesajlar işaretlenirken bir hata oluştu (${c.status})`,key:"markAsRead"})}catch(f){f.name!=="AbortError"&&(u.error({content:"Mesajlar işaretlenirken bir hata oluştu: "+f.message,key:"markAsRead"}),C(c=>c.map(m=>({...m,read:m.fromMe?m.read:!0}))))}return()=>{a.abort()}},gs=async()=>{try{const s=await fetch("/backend/x-site/whatsapp-settings.php?action=templates",{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`}});if(!s.ok)throw new Error(`API yanıt hatası: ${s.status} ${s.statusText}`);const a=await s.json();ms(a.data||[])}catch(s){u.error("Şablon mesajlar yüklenirken hata oluştu")}},bs=async()=>{try{const s=await fetch("/backend/x-site/whatsapp-stats.php?action=summary",{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`}});if(!s.ok)throw new Error(`API yanıt hatası: ${s.status} ${s.statusText}`);const a=await s.json();us(a)}catch(s){u.error("İstatistikler yüklenirken hata oluştu")}},ys=async()=>{try{const s=await fetch("/backend/x-site/whatsapp-settings.php?action=webhook",{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`}});if(!s.ok)throw new Error(`API yanıt hatası: ${s.status} ${s.statusText}`);const a=await s.json();De(a)}catch(s){De(null)}},Je=async()=>{if(!t){u.error("Lütfen bir kişi seçin");return}if(!N){u.error("İnternet bağlantısı yok");return}const s=le(w);if(!s.trim()&&!k){u.error("Mesaj alanı boş olamaz!");return}if(!rt()){u.error("Çok fazla mesaj gönderiyorsunuz. Lütfen bir dakika bekleyin.");return}if(!is(t.phone)){u.error("Geçersiz telefon numarası");return}Ie(!0),_(null);try{u.loading({content:"Mesaj gönderiliyor...",key:"sendMessage",duration:0});let a;ce?a={to:t.phone,type:"template",template_name:ce.name,template_language:ce.language||"en_US",template_parameters:[{type:"text",text:s}]}:a={to:t.phone,message:s,attachment:k};const i=new AbortController,f=setTimeout(()=>i.abort(),3e4);try{const c=await fetch("/backend/x-site/whatsapp-send.php",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`},body:JSON.stringify(a),signal:i.signal});if(clearTimeout(f),!c.ok)throw new Error(`API yanıt hatası: ${c.status} ${c.statusText}`);const m=await c.json();if(m.success){u.success({content:"Mesaj gönderildi",key:"sendMessage",duration:1.5}),R(""),oe(null),$e(null);const x={id:`temp-${Date.now()}`,text:s,timestamp:Math.floor(Date.now()/1e3),fromMe:!0,status:"sent",mediaUrl:k==null?void 0:k.url,mediaType:k==null?void 0:k.type};C(v=>[...v,x]);const g=Math.floor(Date.now()/1e3),M=t.id;S.current.messages[M]&&(S.current.messages[M]={data:[...S.current.messages[M].data,x],timestamp:g}),setTimeout(()=>{t&&Z(t.id)},1e3)}else{const x=m.error||"Bilinmeyen hata";_(x),u.error({content:"Mesaj gönderilemedi: "+x,key:"sendMessage"})}}catch(c){throw clearTimeout(f),c.name==="AbortError"?new Error("İstek zaman aşımına uğradı"):c}}catch(a){let i="Mesaj gönderilirken bir hata oluştu";typeof a=="object"&&a!==null&&"message"in a&&(i+=": "+a.message),_(i),u.error({content:i,key:"sendMessage"})}finally{Ie(!1)}},vs=s=>{$e(s);const a=(s.components||[]).find(i=>i.type==="BODY");R(a?a.text:"")},Fe=()=>{const s=le(Pe),a=le(Le);if(!s.trim()){u.error("Telefon numarası giriniz");return}let i=s.trim().replace(/\s+/g,"");if(i.startsWith("+")||(i="+"+i),!is(i)){u.error("Geçersiz telefon numarası formatı. Örnek: +90 ************");return}const f=r.find(c=>c.phone===i);if(f){u.warning("Bu telefon numarası zaten mevcut"),o(f),q(!0),Q(!1),me(""),he("");return}try{const m={id:`temp-${Date.now()}`,name:a||i,phone:i,isGroup:!1,unreadCount:0,profilePic:void 0,lastMessage:void 0};try{const x=JSON.parse(localStorage.getItem("customContacts")||"[]"),g=[m,...x];if(JSON.stringify(g).length>5*1024*1024){u.error("Çok fazla kişi kaydedildi. Eski kişileri silin.");return}localStorage.setItem("customContacts",JSON.stringify(g))}catch(x){u.error("Kişi kaydedilemedi. Tarayıcı depolama alanı dolu olabilir.");return}l([m,...r]),o(m),q(!0),Q(!1),me(""),he(""),u.success("Yeni sohbet başlatıldı")}catch(c){u.error("Yeni sohbet başlatılırken bir hata oluştu")}},Ns=()=>{q(!1)},qe=n.useCallback(({contact:s})=>{const a=ue[s.id]&&Date.now()-ue[s.id]<3e5;return e.jsx(Y,{dot:a,offset:[-5,38],color:"#25D366",style:{minWidth:"10px",height:"10px"},children:e.jsx(ts,{size:40,src:s.profilePic,icon:s.isGroup?e.jsx(pe,{}):e.jsx(ee,{}),className:"bg-gray-200",style:{backgroundColor:s.profilePic?"transparent":"#f0f0f0",color:"#666"}})})},[ue]);return J&&!N?e.jsx("div",{className:"h-screen w-full flex items-center justify-center bg-gray-50",children:e.jsxs("div",{className:"text-center p-8",children:[e.jsx(Rs,{message:"Bağlantı Hatası",description:ds||J,type:"error",showIcon:!0,style:{marginBottom:16}}),e.jsx(p,{type:"primary",icon:e.jsx(je,{}),onClick:()=>{_(null),ie(null),H()},children:"Yeniden Dene"})]})}):e.jsxs("div",{className:"whatsapp-manager",children:[e.jsxs("div",{className:"whatsapp-header",children:[e.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"whatsapp-logo",children:e.jsx(ge,{style:{fontSize:"28px",color:"var(--wa-teal)"}})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-xl font-semibold text-gray-800",children:"WhatsApp Business"}),e.jsxs("div",{className:"flex items-center space-x-2 text-sm",children:[e.jsx("div",{className:`status-indicator ${N?"online":"offline"}`}),e.jsx("span",{className:"text-gray-600",children:N?"Bağlı":"Bağlantı Yok"}),Xe&&e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"text-gray-400",children:"•"}),e.jsx("span",{className:"text-green-600",children:"Canlı"})]})]})]})]}),e.jsxs("div",{className:"hidden md:flex items-center space-x-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-lg font-semibold text-gray-800",children:r.length}),e.jsx("div",{className:"text-xs text-gray-500",children:"Kişi"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-lg font-semibold text-green-600",children:B.activeChats}),e.jsx("div",{className:"text-xs text-gray-500",children:"Aktif Sohbet"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-lg font-semibold text-blue-600",children:B.messagesLast24h}),e.jsx("div",{className:"text-xs text-gray-500",children:"24s Mesaj"})]})]})]}),J&&e.jsxs("div",{className:"error-banner",children:[e.jsx(Ps,{className:"mr-2"}),e.jsx("span",{children:J}),e.jsx(p,{type:"text",size:"small",onClick:()=>_(null),className:"ml-auto",children:e.jsx(re,{})})]})]}),e.jsxs("div",{className:"whatsapp-content",children:[e.jsxs("div",{className:`whatsapp-sidebar ${Be?"hidden md:flex":"flex"}`,children:[e.jsxs("div",{className:"sidebar-header",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("div",{className:"flex items-center space-x-2",children:e.jsx(p,{type:"primary",icon:e.jsx(ks,{}),onClick:()=>Q(!0),className:"new-chat-btn",size:"small",children:"Yeni Sohbet"})}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(L,{title:"Yenile",children:e.jsx(p,{type:"text",icon:e.jsx(je,{}),onClick:()=>H(),size:"small",className:"action-btn"})}),e.jsx(as,{overlay:e.jsxs(z,{children:[e.jsxs(z.Item,{onClick:()=>W("2"),children:[e.jsx(Ss,{className:"mr-2"}),"Şablonlar"]},"2"),e.jsxs(z.Item,{onClick:()=>W("3"),children:[e.jsx(Cs,{className:"mr-2"}),"Ayarlar"]},"3")]}),trigger:["click"],placement:"bottomRight",children:e.jsx(p,{type:"text",icon:e.jsx(rs,{}),size:"small",className:"action-btn"})})]})]}),e.jsx("div",{className:"search-container",children:e.jsx(I,{placeholder:"Kişi ara...",prefix:e.jsx(be,{className:"text-gray-400"}),value:U,onChange:s=>Oe(s.target.value),className:"search-input",size:"middle"})})]}),e.jsx("div",{className:"contacts-container",children:os?e.jsxs("div",{className:"loading-container",children:[e.jsx(ns,{size:"small"}),e.jsx("span",{className:"ml-2 text-sm text-gray-500",children:"Yükleniyor..."})]}):Ye.length>0?e.jsx("div",{className:"contacts-list",children:Ye.map(s=>{var a,i;return e.jsxs("div",{onClick:()=>ps(s),className:`contact-item ${(t==null?void 0:t.id)===s.id?"selected":""}`,children:[e.jsx("div",{className:"contact-avatar",children:e.jsx(qe,{contact:s})}),e.jsxs("div",{className:"contact-info",children:[e.jsxs("div",{className:"contact-header",children:[e.jsx("span",{className:"contact-name",children:s.name}),e.jsx("span",{className:"contact-time",children:(a=s.lastMessage)!=null&&a.timestamp?P.unix(Number(s.lastMessage.timestamp)).format("HH:mm"):""})]}),e.jsxs("div",{className:"contact-footer",children:[e.jsx("span",{className:"contact-message",children:((i=s.lastMessage)==null?void 0:i.text)||"Henüz mesaj yok"}),s.unreadCount>0&&e.jsx(Y,{count:s.unreadCount,className:"contact-badge"})]})]})]},s.id)})}):e.jsx("div",{className:"empty-container",children:e.jsx(ye,{image:ye.PRESENTED_IMAGE_SIMPLE,description:U?"Sonuç bulunamadı":"Henüz kişi yok",className:"empty-state"})})})]}),e.jsx("div",{className:`whatsapp-chat ${Be||window.innerWidth>=768?"flex":"hidden md:flex"}`,children:t?e.jsxs("div",{className:"chat-container",children:[e.jsxs("div",{className:"chat-header",children:[e.jsxs("div",{className:"flex items-center",children:[window.innerWidth<768&&e.jsx(p,{type:"text",icon:e.jsx(ne,{}),onClick:Ns,className:"back-btn"}),e.jsxs("div",{className:"flex items-center cursor-pointer",onClick:()=>de(!F),children:[e.jsx(qe,{contact:t}),e.jsxs("div",{className:"contact-details",children:[e.jsx("div",{className:"contact-name",children:t.name}),e.jsx("div",{className:"contact-status",children:Xe?e.jsxs("span",{className:"online-status",children:[e.jsx("span",{className:"status-dot online"}),"Çevrimiçi"]}):e.jsx("span",{className:"offline-status",children:"Son görülme bugün 12:45"})})]})]})]}),e.jsxs("div",{className:"chat-actions",children:[e.jsx(L,{title:"Ara",children:e.jsx(p,{type:"text",icon:e.jsx(be,{}),className:"action-btn",size:"small"})}),e.jsx(as,{overlay:e.jsxs(z,{children:[e.jsxs(z.Item,{onClick:()=>de(!F),children:[e.jsx(Ls,{className:"mr-2"}),"Kişi Bilgisi"]},"1"),e.jsxs(z.Item,{children:[e.jsx(Ze,{className:"mr-2"}),"Bildirimleri Kapat"]},"2"),e.jsxs(z.Item,{children:[e.jsx(Fs,{className:"mr-2"}),"Sohbeti Sabitle"]},"3")]}),trigger:["click"],placement:"bottomRight",children:e.jsx(p,{type:"text",icon:e.jsx(rs,{}),className:"action-btn",size:"small"})})]})]}),e.jsxs("div",{className:"chat-content",children:[e.jsxs("div",{className:`messages-area ${F?"with-sidebar":"full-width"}`,children:[e.jsx("div",{ref:xs,className:"messages-container",children:cs?e.jsxs("div",{className:"loading-messages",children:[e.jsx(ns,{size:"small"}),e.jsx("span",{className:"ml-2 text-sm text-gray-500",children:"Mesajlar yükleniyor..."})]}):y.length>0?e.jsxs("div",{className:"messages-list",children:[e.jsx("div",{className:"chat-date-separator",children:e.jsx("div",{className:"chat-date-bubble",children:P().format("DD MMMM YYYY")})}),y.map(fs),e.jsx("div",{ref:We})]}):e.jsx("div",{className:"empty-chat",children:e.jsxs("div",{className:"empty-chat-content",children:[e.jsx(es,{className:"empty-icon"}),e.jsx("p",{className:"empty-text",children:"Henüz mesaj yok"}),e.jsx("p",{className:"empty-subtext",children:"İlk mesajınızı gönderin"})]})})}),e.jsxs("div",{className:"p-2 bg-f0f2f5 border-t border-gray-200",children:[k&&e.jsxs("div",{className:"mb-2 p-2 bg-white rounded-lg flex justify-between items-center",children:[e.jsxs("div",{className:"flex items-center",children:[k.type==="image"?e.jsx("div",{className:"w-12 h-12 rounded overflow-hidden mr-2",children:e.jsx("img",{src:k.url,alt:"Preview",className:"w-full h-full object-cover"})}):e.jsx(Gs,{className:"text-blue-500 text-xl mr-2"}),e.jsx(d,{ellipsis:!0,className:"max-w-xs",children:k.name})]}),e.jsx(p,{type:"text",icon:e.jsx(re,{}),onClick:()=>oe(null),className:"rounded-full h-8 w-8 flex items-center justify-center hover:bg-gray-200"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex mr-2",children:e.jsx(L,{title:"Emoji",children:e.jsx(p,{type:"text",icon:e.jsx(Zs,{}),className:"rounded-full h-10 w-10 flex items-center justify-center hover:bg-gray-200"})})}),e.jsxs("div",{className:"flex-grow relative",children:[e.jsx(at,{value:w,onChange:s=>R(s.target.value),placeholder:"Mesaj yazın...",autoSize:{minRows:1,maxRows:4},className:"rounded-full pl-4 pr-10 py-2 resize-none",onPressEnter:s=>{s.shiftKey||(s.preventDefault(),Je())}}),e.jsx(L,{title:"Dosya ekle",children:e.jsx(Ts,{beforeUpload:s=>(oe({name:s.name,type:s.type.startsWith("image/")?"image":"document",file:s,url:URL.createObjectURL(s)}),!1),showUploadList:!1,children:e.jsx(p,{type:"text",icon:e.jsx(ls,{className:"text-gray-500"}),className:"absolute right-2 top-1/2 transform -translate-y-1/2 rounded-full h-8 w-8 flex items-center justify-center hover:bg-gray-200"})})})]}),e.jsx("div",{className:"ml-2",children:w.trim()||k?e.jsx(p,{type:"primary",icon:e.jsx(Ms,{}),onClick:Je,loading:Ee,disabled:!N||Ee,className:"circle-button",style:{backgroundColor:N?"var(--wa-teal)":"#ccc",borderColor:N?"var(--wa-teal)":"#ccc",opacity:N?1:.6}}):e.jsx(L,{title:N?"Sesli mesaj":"Çevrimdışı",children:e.jsx(p,{type:"primary",icon:e.jsx(Us,{}),disabled:!N,className:"circle-button",style:{backgroundColor:N?"var(--wa-teal)":"#ccc",borderColor:N?"var(--wa-teal)":"#ccc",opacity:N?1:.6}})})})]})]})]}),F&&e.jsxs("div",{className:"w-full md:w-1/3 bg-white border-l border-gray-200 flex flex-col h-full",children:[e.jsxs("div",{className:"p-4 bg-f0f2f5 border-b border-gray-200 flex justify-between items-center",children:[e.jsx(d,{strong:!0,children:"Kişi Bilgisi"}),e.jsx(p,{type:"text",icon:e.jsx(re,{}),onClick:()=>de(!1),className:"rounded-full h-8 w-8 flex items-center justify-center hover:bg-gray-200"})]}),e.jsxs("div",{className:"overflow-y-auto flex-grow p-4",children:[e.jsxs("div",{className:"flex flex-col items-center mb-6",children:[e.jsx(ts,{size:80,src:t.profilePic,icon:t.isGroup?e.jsx(pe,{}):e.jsx(ee,{}),className:"mb-2"}),e.jsx(d,{strong:!0,className:"text-lg",children:t.name}),e.jsx(d,{type:"secondary",children:t.phone}),e.jsx(p,{type:"primary",icon:e.jsx(ee,{}),className:"mt-3",onClick:()=>{const s={...t,name:window.prompt("Kişi adını girin:",t.name)||t.name},a=JSON.parse(localStorage.getItem("customContacts")||"[]");if(!(a.findIndex(f=>f.phone===s.phone)>=0))localStorage.setItem("customContacts",JSON.stringify([s,...a])),u.success("Kişi rehbere kaydedildi");else{const f=a.map(c=>c.phone===s.phone?s:c);localStorage.setItem("customContacts",JSON.stringify(f)),u.success("Kişi bilgileri güncellendi")}H()},style:{backgroundColor:"var(--wa-teal)",borderColor:"var(--wa-teal)"},children:"Rehbere Kaydet"})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx(ve,{className:"text-gray-500 mb-2",children:"Hakkında"}),e.jsx(ve,{children:"İşletme profili üzerinden kullanılabilir."})]}),e.jsx(te,{}),e.jsxs("div",{className:"mb-4",children:[e.jsx(ve,{className:"text-gray-500 mb-2",children:"Medya, bağlantılar ve belgeler"}),e.jsx("div",{className:"grid grid-cols-3 gap-1 mb-2",children:Array(3).fill(0).map((s,a)=>e.jsx("div",{className:"aspect-square bg-gray-200 rounded overflow-hidden"},a))}),e.jsx(p,{type:"link",className:"p-0",children:"Tümünü Gör"})]}),e.jsx(te,{}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(Ze,{className:"text-lg mr-3 text-gray-600"}),e.jsx(d,{children:"Bildirimleri Kapat"})]}),e.jsx(D,{size:"small"})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(tt,{className:"text-lg mr-3 text-gray-600"}),e.jsx(d,{children:"Yıldızlı Mesajlar"})]}),e.jsx("div",{children:e.jsx(p,{type:"text",icon:e.jsx(ne,{}),className:"text-gray-500"})})]}),e.jsxs("div",{className:"flex text-red-500 items-center cursor-pointer",children:[e.jsx(re,{className:"text-lg mr-3"}),e.jsx(d,{className:"text-red-500",children:"Kişiyi Engelle"})]}),e.jsxs("div",{className:"flex text-red-500 items-center cursor-pointer",children:[e.jsx(ot,{className:"text-lg mr-3"}),e.jsx(d,{className:"text-red-500",children:"Sohbeti Sil"})]})]})]})]})]})]}):e.jsx("div",{className:"empty-chat-welcome",children:e.jsxs("div",{className:"welcome-content",children:[e.jsx(ge,{className:"welcome-icon"}),e.jsx("h3",{className:"welcome-title",children:"WhatsApp Business"}),e.jsxs("p",{className:"welcome-text",children:["Müşterilerinizle kolayca iletişim kurun.",e.jsx("br",{}),"Bir kişi seçin ve mesajlaşmaya başlayın."]})]})})})]}),e.jsx("div",{className:`absolute top-0 left-0 right-0 bottom-0 bg-white z-10 ${Te==="2"?"block":"hidden"}`,children:e.jsxs("div",{className:"h-full flex flex-col",children:[e.jsxs("div",{className:"flex items-center p-4 bg-f0f2f5 border-b border-gray-200",children:[e.jsx(p,{type:"text",icon:e.jsx(ne,{}),onClick:()=>W("1"),className:"mr-3 rounded-full h-10 w-10 flex items-center justify-center hover:bg-gray-200"}),e.jsx(d,{strong:!0,children:"Onaylı Şablon Mesajlar"})]}),e.jsxs("div",{className:"flex-grow overflow-y-auto p-4",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx(d,{type:"secondary",className:"mb-2 block",children:"Bu mesajlar WhatsApp tarafından onaylanmıştır ve müşterilerinize ilk mesaj olarak gönderilebilir."}),e.jsx(I,{placeholder:"Şablon ara...",prefix:e.jsx(be,{className:"text-gray-400"}),className:"mb-4 rounded"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:Re.length>0?Re.map(s=>e.jsxs(K,{className:"hover:shadow-md transition-shadow",bodyStyle:{padding:"16px"},children:[e.jsxs("div",{className:"flex mb-2 items-center",children:[e.jsx(Y,{status:"success"}),e.jsx(d,{type:"secondary",className:"text-xs ml-2",children:"Onaylı"})]}),e.jsx(d,{strong:!0,className:"mb-2 block",children:s.name}),e.jsx(d,{className:"text-gray-600 block mb-4",children:s.content}),e.jsx("div",{className:"flex justify-end",children:e.jsx(p,{type:"primary",onClick:()=>{vs(s),W("1"),r.length>0&&!t&&o(r[0])},style:{backgroundColor:"#00a884",borderColor:"#00a884"},children:"Kullan"})})]},s.id)):e.jsx("div",{className:"col-span-full flex justify-center p-8",children:e.jsx(ye,{description:"Henüz şablon mesaj yok"})})})]})]})}),e.jsx("div",{className:`absolute top-0 left-0 right-0 bottom-0 bg-white z-10 ${Te==="3"?"block":"hidden"}`,children:e.jsxs("div",{className:"h-full flex flex-col",children:[e.jsxs("div",{className:"flex items-center p-4 bg-f0f2f5 border-b border-gray-200",children:[e.jsx(p,{type:"text",icon:e.jsx(ne,{}),onClick:()=>W("1"),className:"mr-3 rounded-full h-10 w-10 flex items-center justify-center hover:bg-gray-200"}),e.jsx(d,{strong:!0,children:"WhatsApp Business API Ayarları"})]}),e.jsx("div",{className:"flex-grow overflow-y-auto p-4",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[e.jsx(K,{title:"API Durumu",className:"shadow-sm",children:e.jsxs("div",{className:"flex flex-col space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(d,{children:"Bağlantı Durumu"}),e.jsx(Y,{status:"success",text:"Aktif"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(d,{children:"Telefon Numarası"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(d,{children:"+90 5XX XXX XX XX"}),e.jsx(p,{type:"text",icon:e.jsx(Ds,{}),className:"ml-2 text-gray-400"})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(d,{children:"İşletme Adı"}),e.jsx(d,{children:"Tuber Ajans"})]}),e.jsx(te,{}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(d,{children:"Günlük Mesaj Limiti"}),e.jsx(d,{children:"1,000 / 5,000"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(d,{children:"Kalite Puanı"}),e.jsx(d,{className:"text-green-500",children:"Mükemmel (98/100)"})]})]})}),e.jsx(K,{title:"Webhook Yapılandırması",className:"shadow-sm",children:e.jsxs("div",{className:"flex flex-col space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(d,{children:"Webhook Durumu"}),e.jsx(Y,{status:(E==null?void 0:E.webhook_status)==="Aktif"?"success":"error",text:(E==null?void 0:E.webhook_status)||"Bilinmiyor"})]}),e.jsxs("div",{children:[e.jsx(d,{className:"mb-2 block",children:"Webhook URL"}),e.jsxs(I.Group,{compact:!0,children:[e.jsx(I,{style:{width:"calc(100% - 70px)"},value:(E==null?void 0:E.webhook_url)||"https://api.example.com/whatsapp/webhook",disabled:!0}),e.jsx(p,{type:"primary",onClick:()=>{navigator.clipboard.writeText((E==null?void 0:E.webhook_url)||"https://api.example.com/whatsapp/webhook"),u.success("URL kopyalandı")},children:"Kopyala"})]})]}),e.jsxs("div",{children:[e.jsx(d,{className:"mb-2 block",children:"Webhook Secret"}),e.jsx(I.Password,{value:"●●●●●●●●●●●●●●●●",disabled:!0,addonAfter:e.jsx(L,{title:"Yenile",children:e.jsx(je,{className:"cursor-pointer"})})})]}),e.jsx(p,{type:"default",icon:e.jsx(ss,{}),className:"mt-2",children:"Webhook'u Test Et"})]})}),e.jsx(K,{title:"İstatistikler",className:"shadow-sm",children:e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx(ae,{title:"Toplam Mesaj",value:B.totalMessages,prefix:e.jsx(es,{})}),e.jsx(ae,{title:"Son 24 Saat",value:B.messagesLast24h,prefix:e.jsx(As,{})}),e.jsx(ae,{title:"Aktif Sohbetler",value:B.activeChats,prefix:e.jsx(pe,{})}),e.jsx(ae,{title:"Ulaşma Oranı",value:B.deliveryRate,precision:1,suffix:"%",prefix:e.jsx(ss,{})})]})}),e.jsx(K,{title:"Bildirim Ayarları",className:"shadow-sm",children:e.jsxs("div",{className:"flex flex-col space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(d,{children:"Masaüstü Bildirimleri"}),e.jsx(D,{defaultChecked:!0})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(d,{children:"Bildirim Sesi"}),e.jsx(D,{defaultChecked:!0})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(d,{children:"E-posta Bildirimleri"}),e.jsx(D,{})]}),e.jsx(te,{}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(d,{children:"Yeni Sohbet Bildirimleri"}),e.jsx(D,{defaultChecked:!0})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(d,{children:"Grup Bildirimleri"}),e.jsx(D,{defaultChecked:!0})]})]})})]})})]})}),e.jsx(zs,{title:"Yeni Sohbet Başlat",open:hs,onCancel:()=>Q(!1),onOk:Fe,okText:"Sohbet Başlat",cancelText:"İptal",okButtonProps:{style:{backgroundColor:"var(--wa-teal)",borderColor:"var(--wa-teal)"}},children:e.jsxs("div",{className:"space-y-4 fade-in",children:[e.jsxs("div",{children:[e.jsx(d,{className:"block mb-1",children:"Telefon Numarası"}),e.jsx(I,{placeholder:"+90 ************",value:Pe,onChange:s=>me(s.target.value),prefix:e.jsx(ge,{style:{color:"var(--wa-teal)"},className:"mr-2"}),className:"rounded",autoFocus:!0}),e.jsx(d,{type:"secondary",className:"text-xs block mt-1",children:"Uluslararası format kullanın (örn: +90 ************)"})]}),e.jsxs("div",{children:[e.jsx(d,{className:"block mb-1",children:"İsim (İsteğe Bağlı)"}),e.jsx(I,{placeholder:"Kişi veya Şirket İsmi",value:Le,onChange:s=>he(s.target.value),prefix:e.jsx(ee,{className:"text-gray-400 mr-2"}),className:"rounded",onPressEnter:Fe})]})]})})]})},ot=({className:r=""})=>e.jsx("svg",{viewBox:"0 0 24 24",width:"24",height:"24",className:r,children:e.jsx("path",{fill:"currentColor",d:"M6,19c0,1.1,0.9,2,2,2h8c1.1,0,2-0.9,2-2V7H6V19z M19,4h-3.5l-1-1h-5l-1,1H5v2h14V4z"})});export{Rt as default};
