<?php
/**
 * Kategori Yönetimi API (Courses tablosu ile)
 * Bu API kategorileri listelemek ve eklemek için kullanılır
 */

// Hata ayıklama
ini_set('display_errors', 1);
error_reporting(E_ALL);

// CORS ve JSON header'ları
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS, DELETE');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=utf-8');

// OPTIONS istekleri için hızlıca 200 dön
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Ortak config dosyasını dahil et
require_once __DIR__ . '/../config/config.php';

// JSON yanıt fonksiyonu
if (!function_exists('jsonResponse')) {
    function jsonResponse($data, $status = 200) {
        http_response_code($status);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}

// Auth kontrolü (checkAuth fonksiyonu - diğer API'ler gibi)
if (!function_exists('checkAuth')) {
    function checkAuth() {
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
        
        if (strpos($authHeader, 'Bearer ') === 0) {
            $token = substr($authHeader, 7);
            if (strlen($token) > 10) { // Basit token kontrolü
                return true;
            }
        }
        return false;
    }
}

// İstek methodunu al
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

// Veritabanı bağlantısı
try {
    if (!isset($db_akademi)) {
        $db = new PDO("mysql:host=$servername;dbname=tuberaja_yayinci_akademi;charset=utf8mb4", $db_username, $db_password);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    } else {
        $db = $db_akademi;
    }
} catch (PDOException $e) {
    jsonResponse(['success' => false, 'message' => 'Veritabanı bağlantı hatası: ' . $e->getMessage()], 500);
}

// Test verisi ekleme endpoint'i
if ($action === 'add_test_data') {
    // POST istekleri için auth gerekli
    if (!checkAuth()) {
        jsonResponse(['success' => false, 'message' => 'Bu işlemi gerçekleştirmek için giriş yapmalısınız.'], 401);
    }

    try {
        // Test eğitimleri ekle
        $testCourses = [
            [
                'title' => 'TikTok Algoritmasını Anlamak',
                'description' => 'TikTok algoritmasının temel çalışma prensipleri',
                'content' => 'Bu eğitimde TikTok algoritmasının nasıl çalıştığını öğreneceksiniz.',
                'category' => 'general',
                'status' => 'active'
            ],
            [
                'title' => 'Video Düzenleme Teknikleri',
                'description' => 'Profesyonel video düzenleme yöntemleri',
                'content' => 'Video düzenleme araçları ve teknikleri hakkında detaylı bilgi.',
                'category' => 'video-editing',
                'status' => 'active'
            ],
            [
                'title' => 'İçerik Üretimi Stratejileri',
                'description' => 'Etkili içerik üretme yöntemleri',
                'content' => 'Viral içerik üretme stratejileri ve ipuçları.',
                'category' => 'content-creation',
                'status' => 'active'
            ]
        ];

        foreach ($testCourses as $course) {
            $stmt = $db->prepare("
                INSERT INTO courses (title, description, content, category, status, created_at, updated_at, created_by)
                VALUES (?, ?, ?, ?, ?, NOW(), NOW(), 1)
            ");
            $stmt->execute([
                $course['title'],
                $course['description'],
                $course['content'],
                $course['category'],
                $course['status']
            ]);
        }

        jsonResponse(['success' => true, 'message' => 'Test verileri başarıyla eklendi']);
    } catch (PDOException $e) {
        error_log("Categories API - Add test data error: " . $e->getMessage());
        jsonResponse(['success' => false, 'message' => 'Test verisi ekleme hatası: ' . $e->getMessage()], 500);
    }
}

// GET isteği - Kategorileri Listele (PUBLIC)
if ($method === 'GET') {
    try {
        $stmt = $db->prepare("SELECT DISTINCT category FROM courses WHERE category IS NOT NULL AND category != ''");
        $stmt->execute();
        $categories = $stmt->fetchAll(PDO::FETCH_COLUMN);
        jsonResponse(['success' => true, 'data' => $categories]);
    } catch (PDOException $e) {
        error_log("Categories API - List error: " . $e->getMessage());
        jsonResponse(['success' => false, 'message' => 'Veritabanı hatası: ' . $e->getMessage()], 500);
    }
}

// POST isteği - Kategori Ekle
else if ($method === 'POST') {
    // POST istekleri için auth gerekli
    if (!checkAuth()) {
        jsonResponse(['success' => false, 'message' => 'Bu işlemi gerçekleştirmek için giriş yapmalısınız.'], 401);
    }

    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }

    if ($action === 'add') {
        if (!isset($input['category'])) {
            jsonResponse(['success' => false, 'message' => 'Kategori adı gerekli.'], 400);
        }

        try {
            $stmt = $db->prepare("SELECT COUNT(*) FROM courses WHERE category = ?");
            $stmt->execute([$input['category']]);
            $exists = $stmt->fetchColumn() > 0;

            if ($exists) {
                jsonResponse(['success' => false, 'message' => 'Bu kategori zaten mevcut.'], 400);
            }

            $stmt = $db->prepare("INSERT INTO courses (title, description, content, category, status, created_at, updated_at, created_by) VALUES (?, ?, ?, ?, ?, NOW(), NOW(), 1)");
            $stmt->execute([
                'Kategori: ' . $input['category'],
                '',
                '',
                $input['category'],
                'draft'
            ]);

            jsonResponse(['success' => true, 'message' => 'Kategori başarıyla eklendi.']);
        } catch (PDOException $e) {
            error_log("Categories API - Add category error: " . $e->getMessage());
            jsonResponse(['success' => false, 'message' => 'Veritabanı hatası: ' . $e->getMessage()], 500);
        }
    } else {
        jsonResponse(['success' => false, 'message' => 'Desteklenmeyen action parametresi: ' . $action], 400);
    }
}

// Desteklenmeyen istek metodu
else {
    jsonResponse(['success' => false, 'message' => 'Desteklenmeyen HTTP metodu.'], 405);
}
?>
