import React from 'react';

interface FeatureCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ title, description, icon }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm p-6 h-full flex flex-col items-center text-center transform transition-transform duration-300 hover:shadow-md hover:translate-y-[-2px]">
      <div className="w-14 h-14 rounded-full bg-tuber-pink flex items-center justify-center mb-4">
        <div className="text-white text-2xl">
          {icon}
        </div>
      </div>
      <h3 className="text-lg font-bold text-gray-800 mb-2">{title}</h3>
      <p className="text-sm text-gray-600 leading-relaxed">{description}</p>
    </div>
  );
};

export default FeatureCard;