<?php
header('Content-Type: application/json; charset=utf-8');
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Optimize edilmiş connection management
set_time_limit(10); // 10 saniye max

try {
    require_once __DIR__ . '/../includes/connection_manager.php';
    $pdo = getDbConnection();
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Veritabanı bağlantısı kurulamadı', 'error' => $e->getMessage()]);
    exit;
}
$action = isset($_GET['action']) ? $_GET['action'] : 'list';

function downloadTiktokImage($url, $username) {
    // Önce cache kontrol et
    $cacheKey = 'tiktok_image_' . md5($username);
    $cacheDir = __DIR__ . '/../cache/images/';
    $cacheFile = $cacheDir . $cacheKey . '.json';
    
    // Cache dizini yoksa oluştur
    if (!is_dir($cacheDir)) {
        mkdir($cacheDir, 0755, true);
    }
    
    // Önbellekte var mı kontrol et (24 saat cache)
    if (file_exists($cacheFile) && (time() - filemtime($cacheFile)) < 86400) {
        $cachedData = json_decode(file_get_contents($cacheFile), true);
        if ($cachedData && $cachedData['path']) {
            return $cachedData['path'];
        }
    }
    
    // Timeout ile resim indir
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'method' => 'GET',
            'header' => [
                'User-Agent: Mozilla/5.0 (compatible; TuberAjans/1.0)',
                'Accept: image/*'
            ]
        ]
    ]);
    
    $imgData = @file_get_contents($url, false, $context);
    if ($imgData) {
        // Ana site API'sine resmi gönder - Timeout ile
        $base64Image = base64_encode($imgData);

        $postData = json_encode([
            'username' => $username,
            'imageData' => $base64Image
        ]);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://tuberajans.com/upload_profile_image.php');
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 15); // 15 saniye timeout
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10); // 10 saniye connection timeout
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Origin: https://x.tuberajans.com'
        ]);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        if ($curlError) {
            error_log("CURL hatası (downloadTiktokImage): " . $curlError);
            return '';
        }

        if ($httpCode === 200) {
            $result = json_decode($response, true);
            if ($result && $result['success']) {
                // Başarılı sonucu cache'le
                file_put_contents($cacheFile, json_encode(['path' => $result['path'], 'timestamp' => time()]));
                return $result['path'];
            }
        } else {
            error_log("HTTP hatası (downloadTiktokImage): " . $httpCode . " - " . $response);
        }
    }
    return '';
}

function fetchTiktokProfileImage($username) {
    if (!$username) return '';

    // Cache kontrol et
    $cacheKey = 'tiktok_profile_' . md5($username);
    $cacheDir = __DIR__ . '/../cache/profiles/';
    $cacheFile = $cacheDir . $cacheKey . '.json';
    
    if (!is_dir($cacheDir)) {
        mkdir($cacheDir, 0755, true);
    }
    
    // 6 saat cache (profil resimleri daha az değişir)
    if (file_exists($cacheFile) && (time() - filemtime($cacheFile)) < 21600) {
        $cachedData = json_decode(file_get_contents($cacheFile), true);
        if ($cachedData && $cachedData['path']) {
            return $cachedData['path'];
        }
    }

    try {
        $tiktokUrl = 'https://www.tiktok.com/@' . $username;
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'timeout' => 15, // 15 saniye timeout
                'header' => [
                    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language: en-US,en;q=0.9'
                ]
            ]
        ]);

        $html = @file_get_contents($tiktokUrl, false, $context);
        if ($html && preg_match('/"avatarLarger":"(.*?)"/', $html, $m)) {
            $imgUrl = stripslashes($m[1]);
            $imgUrl = str_replace('\\u0026', '&', $imgUrl);

            $imgContext = stream_context_create([
                'http' => [
                    'timeout' => 10,
                    'method' => 'GET'
                ]
            ]);

            $imgData = @file_get_contents($imgUrl, false, $imgContext);
            if ($imgData) {
                // Ana site API'sine resmi gönder - Timeout ile
                $base64Image = base64_encode($imgData);

                $postData = json_encode([
                    'username' => $username,
                    'imageData' => $base64Image
                ]);

                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, 'https://tuberajans.com/upload_profile_image.php');
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_TIMEOUT, 15); // 15 saniye timeout
                curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10); // 10 saniye connection timeout
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Content-Type: application/json',
                    'Origin: https://x.tuberajans.com'
                ]);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $curlError = curl_error($ch);
                curl_close($ch);

                if ($curlError) {
                    error_log("CURL hatası (fetchTiktokProfileImage): " . $curlError);
                    return '';
                }

                if ($httpCode === 200) {
                    $result = json_decode($response, true);
                    if ($result && $result['success']) {
                        // Başarılı sonucu cache'le
                        file_put_contents($cacheFile, json_encode(['path' => $result['path'], 'timestamp' => time()]));
                        return $result['path'];
                    }
                } else {
                    error_log("HTTP hatası (fetchTiktokProfileImage): " . $httpCode . " - " . $response);
                }
            }
        }
    } catch (Exception $e) {
        error_log("TikTok profil resmi çekme hatası: " . $e->getMessage());
    }

    return '';
}

if ($action === 'list') {
    try {
        // Optimize edilmiş sorgu - sadece gerekli alanları çek ve LIMIT ekle
        $stmt = $pdo->query('SELECT id, username, fullname, image, isFeatured, order_number, featured_order 
                           FROM yayincilar_site 
                           ORDER BY isFeatured DESC, featured_order ASC, order_number ASC, id DESC 
                           LIMIT 1000');
        $data = $stmt->fetchAll();
        echo json_encode(['success' => true, 'data' => $data]);
    } catch (PDOException $e) {
        error_log("Yayıncılar listesi hatası: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Veriler alınamadı', 'error' => $e->getMessage()]);
    }
    exit;
}

if ($action === 'add') {
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        echo json_encode(['success' => false, 'message' => 'Geçersiz veri']);
        exit;
    }

    $image = $input['image'] ?? '';

    // Eğer resim URL'i verilmişse onu kullan
    if ($image && filter_var($image, FILTER_VALIDATE_URL) && !empty($input['username'])) {
        $image = downloadTiktokImage($image, $input['username']);
    }
    // Eğer resim verilmemişse TikTok'tan otomatik çek
    else if (!empty($input['username'])) {
        $image = fetchTiktokProfileImage($input['username']);
    }

    try {
        $stmt = $pdo->prepare('INSERT INTO yayincilar_site (username, fullname, image, isFeatured, order_number, featured_order) VALUES (?, ?, ?, ?, ?, ?)');
        $stmt->execute([
            $input['username'],
            $input['fullname'],
            $image,
            $input['isFeatured'],
            $input['order_number'] ?? 0,
            $input['featured_order'] ?? 0
        ]);
        echo json_encode(['success' => true, 'message' => 'Yayıncı eklendi']);
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'message' => 'Yayıncı eklenemedi', 'error' => $e->getMessage()]);
    }
    exit;
}

if ($action === 'update') {
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input || !isset($input['id'])) {
        echo json_encode(['success' => false, 'message' => 'Geçersiz veri']);
        exit;
    }
    $image = $input['image'] ?? '';
    if ($image && filter_var($image, FILTER_VALIDATE_URL) && !empty($input['username'])) {
        $image = downloadTiktokImage($image, $input['username']);
    }
    try {
        $stmt = $pdo->prepare('UPDATE yayincilar_site SET username=?, fullname=?, image=?, isFeatured=?, order_number=?, featured_order=? WHERE id=?');
        $stmt->execute([
            $input['username'],
            $input['fullname'],
            $image,
            $input['isFeatured'],
            $input['order_number'] ?? 0,
            $input['featured_order'] ?? 0,
            $input['id']
        ]);
        echo json_encode(['success' => true, 'message' => 'Yayıncı güncellendi']);
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'message' => 'Yayıncı güncellenemedi', 'error' => $e->getMessage()]);
    }
    exit;
}

if ($action === 'delete') {
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    if ($id > 0) {
        try {
            $stmt = $pdo->prepare('DELETE FROM yayincilar_site WHERE id=?');
            $stmt->execute([$id]);
            echo json_encode(['success' => true, 'message' => 'Yayıncı silindi']);
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'message' => 'Yayıncı silinemedi', 'error' => $e->getMessage()]);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Geçersiz ID']);
    }
    exit;
}

if ($action === 'update_order') {
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input || !isset($input['featured']) || !isset($input['normal'])) {
        echo json_encode(['success' => false, 'message' => 'Geçersiz sıralama verisi']);
        exit;
    }

    try {
        $pdo->beginTransaction();

        // Öne çıkan yayıncıları güncelle
        foreach ($input['featured'] as $index => $item) {
            $stmt = $pdo->prepare('UPDATE yayincilar_site SET isFeatured=?, featured_order=?, order_number=? WHERE id=?');
            $stmt->execute(['1', $index + 1, $index + 1, $item['id']]);
        }

        // Normal yayıncıları güncelle
        foreach ($input['normal'] as $index => $item) {
            $stmt = $pdo->prepare('UPDATE yayincilar_site SET isFeatured=?, featured_order=?, order_number=? WHERE id=?');
            $stmt->execute(['0', 0, $index + 1, $item['id']]);
        }

        $pdo->commit();
        echo json_encode(['success' => true, 'message' => 'Sıralama başarıyla güncellendi']);
    } catch (PDOException $e) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => 'Sıralama güncellenemedi', 'error' => $e->getMessage()]);
    }
    exit;
}

if ($action === 'update_all_images') {
    try {
        $stmt = $pdo->query('SELECT id, username FROM yayincilar_site');
        $publishers = $stmt->fetchAll();
        $updated = 0;
        foreach ($publishers as $pub) {
            $username = $pub['username'];
            if (!$username) continue;
            $tiktokUrl = 'https://www.tiktok.com/@' . $username;
            $context = stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'header' => [
                        'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                        'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                        'Accept-Language: en-US,en;q=0.9'
                    ]
                ]
            ]);
            $html = @file_get_contents($tiktokUrl, false, $context);
            if ($html && preg_match('/"avatarLarger":"(.*?)"/', $html, $m)) {
                $imgUrl = stripslashes($m[1]);
                $imgUrl = str_replace('\\u0026', '&', $imgUrl);
                $imgData = @file_get_contents($imgUrl);
                if ($imgData) {
                    // Ana site API'sine resmi gönder
                    $base64Image = base64_encode($imgData);

                    $postData = json_encode([
                        'username' => $username,
                        'imageData' => $base64Image
                    ]);

                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, 'https://tuberajans.com/upload_profile_image.php');
                    curl_setopt($ch, CURLOPT_POST, 1);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                    curl_setopt($ch, CURLOPT_HTTPHEADER, [
                        'Content-Type: application/json',
                        'Origin: https://x.tuberajans.com'
                    ]);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

                    $response = curl_exec($ch);
                    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    curl_close($ch);

                    if ($httpCode === 200) {
                        $result = json_decode($response, true);
                        if ($result && $result['success']) {
                            $dbPath = $result['path'];
                            $updateStmt = $pdo->prepare('UPDATE yayincilar_site SET image=? WHERE id=?');
                            $updateStmt->execute([$dbPath, $pub['id']]);
                            $updated++;
                        }
                    }
                }
            }
        }
        echo json_encode(['success' => true, 'message' => "$updated yayıncının profil fotoğrafı güncellendi."]);
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Toplu güncelleme başarısız', 'error' => $e->getMessage()]);
    }
    exit;
}

if ($action === 'sync_to_main') {
    try {
        // Ana site veritabanı bağlantısı için config dosyasını kontrol et
        $main_pdo = $pdo; // Aynı veritabanı kullanıyoruz

        $main_pdo->beginTransaction();

        // Önce ana sitedeki yayincilar tablosunu temizle
        $main_pdo->exec('DELETE FROM yayincilar');

        // yayincilar_site'den yayincilar'a kopyala - Doğru sıralama ile
        $stmt = $pdo->query('SELECT * FROM yayincilar_site ORDER BY isFeatured DESC, featured_order ASC, order_number ASC');
        $publishers = $stmt->fetchAll();

        $insertStmt = $main_pdo->prepare('INSERT INTO yayincilar (id, username, fullname, image, isFeatured, order_number) VALUES (?, ?, ?, ?, ?, ?)');

        foreach ($publishers as $pub) {
            $insertStmt->execute([
                $pub['id'],
                $pub['username'],
                $pub['fullname'],
                $pub['image'],
                $pub['isFeatured'],
                $pub['order_number']
            ]);
        }

        $main_pdo->commit();
        echo json_encode(['success' => true, 'message' => 'Ana site ile senkronizasyon tamamlandı', 'synced' => count($publishers)]);
    } catch (PDOException $e) {
        if ($main_pdo->inTransaction()) {
            $main_pdo->rollBack();
        }
        echo json_encode(['success' => false, 'message' => 'Senkronizasyon başarısız', 'error' => $e->getMessage()]);
    }
    exit;
}

echo json_encode(['success' => false, 'message' => 'Geçersiz action']);
exit;