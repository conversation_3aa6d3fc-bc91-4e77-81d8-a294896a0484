<?php
/**
 * Kurslar API
 * Bu API kursları listelemek, eklemek, düzenlemek ve silmek için kullanılır
 */

// CORS için header ayarları
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, DELETE, PUT, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json; charset=UTF-8");

// OPTIONS isteği için erken yanıt ver
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Veritabanı bağlantı bilgileri
$db_host = "**************";

$db_username = "root";
$db_password = "Bebek845396!";
$db_name = "tuberaja_yayinci_akademi";

// Veritabanı bağlantısı
try {
    $db = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_username, $db_password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $db->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Veritabanı bağlantı hatası: ' . $e->getMessage()]);
    exit;
}

// İstek methodunu al
$method = $_SERVER['REQUEST_METHOD'];

// GET isteği - Kursları Listele
if ($method === 'GET') {
    try {
        // ID parametresi varsa tek bir kursu getir
        if (isset($_GET['id'])) {
            $id = intval($_GET['id']);

            // Kurs bilgilerini al
            $stmt = $db->prepare("SELECT * FROM courses WHERE id = ?");
            $stmt->execute([$id]);
            $course = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($course) {
                // Tek bir courses tablosu kullanıyoruz, tüm içerik content alanında

                // Önceki ve sonraki kursları bul
                $allCoursesStmt = $db->prepare("
                    SELECT id FROM courses
                    WHERE status = 'active'
                    ORDER BY created_at DESC
                ");
                $allCoursesStmt->execute();
                $allCourses = $allCoursesStmt->fetchAll(PDO::FETCH_COLUMN);

                $currentIndex = array_search($id, $allCourses);
                $prevCourseId = ($currentIndex > 0) ? $allCourses[$currentIndex - 1] : null;
                $nextCourseId = ($currentIndex < count($allCourses) - 1) ? $allCourses[$currentIndex + 1] : null;

                $response = [
                    'success' => true,
                    'data' => $course,
                    'navigation' => [
                        'prev_course_id' => $prevCourseId,
                        'next_course_id' => $nextCourseId
                    ]
                ];

                echo json_encode($response);
            } else {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Kurs bulunamadı.']);
            }
        }
        // Kategori parametresi varsa o kategorideki kursları getir
        else if (isset($_GET['category'])) {
            $category = $_GET['category'];

            $stmt = $db->prepare("
                SELECT * FROM courses
                WHERE category = ? AND status = 'active'
                ORDER BY created_at DESC
            ");
            $stmt->execute([$category]);
            $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);

            echo json_encode(['success' => true, 'data' => $courses]);
        }
        // Hiçbir parametre yoksa tüm kursları listele
        else {
            $query = "SELECT * FROM courses";

            // Sadece aktif kursları getir
            if (isset($_GET['status']) && $_GET['status'] === 'active') {
                $query .= " WHERE status = 'active'";
            }

            // Sıralama
            $query .= " ORDER BY created_at DESC";

            $stmt = $db->prepare($query);
            $stmt->execute();
            $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);

            echo json_encode(['success' => true, 'data' => $courses]);
        }
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Veritabanı hatası: ' . $e->getMessage()]);
    }
}
// Desteklenmeyen istek metodu
else {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Desteklenmeyen HTTP metodu.']);
}
