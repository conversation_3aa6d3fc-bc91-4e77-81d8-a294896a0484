import{j as t,D as jt,H as wt,u as vt,e as St}from"./reactDnd-uQSTYBkW.js";import{o as I,r as m}from"./vendor-CnpYymF8.js";import{S as Ct,c as Tt,u as It,d as _t,g as zt,s as Nt}from"./api-BR4sNstt.js";import{A as Ee}from"./index-BVn_ohNQ.js";import{j as Ft,s as Pe,h as Re,n as Ot,R as Mt,l as Et}from"./App-C_UskXOj.js";import{o as p,Z as Pt,k as x,a6 as Rt,N as y,p as ke,x as h,l as At,M as G,s as $t,t as O,m as Ae,u as ie,I as b,$ as Kt,U as Bt,W as Dt,A as Wt,K as oe}from"./antd-BfejY-CV.js";import{X as Lt}from"./x-BlF-lTk7.js";import{c as We}from"./createLucideIcon-DxVmGoQf.js";import{R as Vt}from"./EllipsisOutlined-BDDrCCML.js";import{R as $e}from"./UploadOutlined-DndzAseN.js";import{S as Ke,T as ye}from"./trash-2-C8zHBfrV.js";import"./utils-CtuI0RRe.js";import"./charts-6B1FLgFz.js";const Ut=[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]],Gt=We("funnel",Ut);const Jt=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]],be=We("user-plus",Jt);var Le={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},Be=I.createContext&&I.createContext(Le),qt=["attr","size","title"];function Yt(r,i){if(r==null)return{};var a=Ht(r,i),o,j;if(Object.getOwnPropertySymbols){var C=Object.getOwnPropertySymbols(r);for(j=0;j<C.length;j++)o=C[j],!(i.indexOf(o)>=0)&&Object.prototype.propertyIsEnumerable.call(r,o)&&(a[o]=r[o])}return a}function Ht(r,i){if(r==null)return{};var a={};for(var o in r)if(Object.prototype.hasOwnProperty.call(r,o)){if(i.indexOf(o)>=0)continue;a[o]=r[o]}return a}function ce(){return ce=Object.assign?Object.assign.bind():function(r){for(var i=1;i<arguments.length;i++){var a=arguments[i];for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(r[o]=a[o])}return r},ce.apply(this,arguments)}function De(r,i){var a=Object.keys(r);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(r);i&&(o=o.filter(function(j){return Object.getOwnPropertyDescriptor(r,j).enumerable})),a.push.apply(a,o)}return a}function de(r){for(var i=1;i<arguments.length;i++){var a=arguments[i]!=null?arguments[i]:{};i%2?De(Object(a),!0).forEach(function(o){Xt(r,o,a[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(a)):De(Object(a)).forEach(function(o){Object.defineProperty(r,o,Object.getOwnPropertyDescriptor(a,o))})}return r}function Xt(r,i,a){return i=Zt(i),i in r?Object.defineProperty(r,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):r[i]=a,r}function Zt(r){var i=Qt(r,"string");return typeof i=="symbol"?i:i+""}function Qt(r,i){if(typeof r!="object"||!r)return r;var a=r[Symbol.toPrimitive];if(a!==void 0){var o=a.call(r,i);if(typeof o!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(i==="string"?String:Number)(r)}function Ve(r){return r&&r.map((i,a)=>I.createElement(i.tag,de({key:a},i.attr),Ve(i.child)))}function el(r){return i=>I.createElement(tl,ce({attr:de({},r.attr)},i),Ve(r.child))}function tl(r){var i=a=>{var{attr:o,size:j,title:C}=r,M=Yt(r,qt),S=j||a.size||"1em",_;return a.className&&(_=a.className),r.className&&(_=(_?_+" ":"")+r.className),I.createElement("svg",ce({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},a.attr,o,M,{className:_,style:de(de({color:r.color||a.color},a.style),r.style),height:S,width:S,xmlns:"http://www.w3.org/2000/svg"}),C&&I.createElement("title",null,C),r.children)};return Be!==void 0?I.createElement(Be.Consumer,null,a=>i(a)):i(Le)}function ll(r){return el({attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"},child:[]}]})(r)}const sl=({index:r,title:i,moveColumn:a})=>{const o=I.useRef(null),[{isDragging:j},C]=vt({type:"column",item:{index:r},collect:S=>({isDragging:S.isDragging()})}),[,M]=St({accept:"column",hover:S=>{S.index!==r&&(a(S.index,r),S.index=r)}});return C(M(o)),t.jsx("th",{ref:o,style:{cursor:"move",opacity:j?.5:1,padding:"12px 8px",fontWeight:"bold",fontSize:"13px",textTransform:"uppercase",borderBottom:"1px solid #eee",color:"#555"},children:i})};function rl(r){return new Promise((i,a)=>{const o=new FileReader;o.readAsDataURL(r),o.onload=()=>i(o.result),o.onerror=j=>a(j)})}const nl=()=>{const r=localStorage.getItem("profileImageProgress");if(r)try{return JSON.parse(r)}catch(i){return localStorage.removeItem("profileImageProgress"),{total:0,current:0,success:0,fail:0,running:!1}}return{total:0,current:0,success:0,fail:0,running:!1}};function vl({hideHeader:r=!1,isMobileView:i=!1,onDataChange:a,initialCount:o}){var j,C,M,S,_;const[J,Ue]=m.useState([]),[Ge,q]=m.useState(!1),[R]=p.useForm(),{darkMode:v}=Ft(),[Y,je]=m.useState(o?Array(o).fill({}):[]),[H,A]=m.useState([]),[$,we]=m.useState(""),[z,ve]=m.useState(null),[X,ue]=m.useState(""),[Je,K]=m.useState(!!o),[qe,Se]=m.useState(!1),[g,N]=m.useState({category:[],status:[],location:[],followers:[],hasTelefon:!1}),[Z,B]=m.useState([]),[Ce,Te]=m.useState(void 0),[Ye,E]=m.useState(!1),[al,il]=m.useState(null),[ol,cl]=m.useState(null),[D]=p.useForm(),[He,Q]=m.useState(!1),[ee]=p.useForm(),[W,P]=m.useState([]),[Ie,_e]=m.useState(void 0),[Xe,L]=m.useState(!1),[Ze,te]=m.useState(!1),[ze,me]=m.useState(null),[T]=p.useForm(),[le,Ne]=m.useState([]),[Qe,fe]=m.useState(!1),[et,tt]=m.useState(""),[Fe,lt]=m.useState(""),[st,pe]=m.useState(!1),[f,w]=m.useState([]),[rt,he]=m.useState(o||0),[nt,xe]=m.useState(!1),[se]=p.useForm(),[F,V]=m.useState(nl()),at=async()=>{try{const l=await se.validateFields();await Tt(l),h.success("Influencer başarıyla eklendi"),xe(!1),se.resetFields(),ae()}catch(l){h.error("Influencer eklenirken hata oluştu")}},Oe="https://x.tuberajans.com",ge=[{title:" ",dataIndex:"profile_image",key:"profile_image",width:48,render:(l,e)=>{const s=e.profile_image?`${Oe}${e.profile_image}`:`${Oe}/uploads/influencers/${e.username}.jpg`;return t.jsx(Wt,{src:s,icon:t.jsx(Et,{}),size:36,style:{backgroundColor:"#f0f0f0",marginRight:8},alt:e.username,onError:c=>{var n;const d=c.target;d.dataset.fallbackUsed||(d.dataset.fallbackUsed="true",d.src=`data:image/svg+xml;base64,${btoa(`
                  <svg width="36" height="36" xmlns="http://www.w3.org/2000/svg">
                    <rect width="36" height="36" fill="#f0f0f0"/>
                    <text x="18" y="24" font-family="Arial" font-size="16" fill="#999" text-anchor="middle">${((n=e.username)==null?void 0:n.charAt(0))||"?"}</text>
                  </svg>
                `)}`)}})},fixed:i?void 0:"left"},{title:"Kullanıcı Adı",dataIndex:"username",key:"username",sorter:(l,e)=>(l.username||"").localeCompare(e.username||""),width:150,render:(l,e)=>re(l,e,"username")},{title:"E-posta",dataIndex:"email",key:"email",sorter:(l,e)=>(l.email||"").localeCompare(e.email||""),width:200,render:(l,e)=>re(l,e,"email")},{title:"Takipçi Sayısı",dataIndex:"followers",key:"followers",defaultSortOrder:"descend",sorter:(l,e)=>(Number(l.followers)||0)-(Number(e.followers)||0),width:150,render:(l,e)=>re(l||"-",e,"followers")},{title:"Telefon",dataIndex:"telefon",key:"telefon",width:150,render:(l,e)=>re(l||"-",e,"telefon")},{title:"Profil Linki",dataIndex:"sosyal_medya",key:"sosyal_medya",width:180,render:(l,e)=>{if(z&&z.id===e.id&&z.field==="sosyal_medya")return t.jsx(b,{value:X,onChange:c=>ue(c.target.value),onBlur:()=>ne(e.id,"sosyal_medya"),onPressEnter:()=>ne(e.id,"sosyal_medya"),autoFocus:!0});const s=e.username?`https://www.tiktok.com/@${e.username}`:l!=null&&l.startsWith("http")?l:`https://${l}`;return t.jsx("div",{onDoubleClick:()=>Me(e.id,"sosyal_medya",e.sosyal_medya),children:e.username?t.jsx(oe,{title:"TikTok Profilini Aç",children:t.jsx("a",{href:s,target:"_blank",rel:"noopener noreferrer",className:"tiktok-profile-link",onClick:c=>c.stopPropagation(),style:{display:"inline-flex",alignItems:"center",justifyContent:"center"},children:I.createElement(ll,{size:24,color:"#000"})})}):"-"})}},{title:"İşlemler",key:"action",fixed:"right",width:150,align:"center",render:(l,e)=>t.jsxs("div",{className:"flex justify-center items-center space-x-3",children:[t.jsx(oe,{title:"WhatsApp'a Mesaj Gönder",children:t.jsx("button",{onClick:()=>pt(e),className:"text-green-500 hover:text-green-700 flex items-center justify-center transition-colors duration-200",style:{background:"none",border:"none",cursor:"pointer",padding:"4px",width:"32px",height:"32px",borderRadius:"4px"},children:t.jsx(Re,{style:{fontSize:"19px"}})})}),e.email&&t.jsx(oe,{title:"E-posta Gönder",children:t.jsx("button",{onClick:()=>ht(e),className:"text-blue-500 hover:text-blue-700 flex items-center justify-center transition-colors duration-200",style:{background:"none",border:"none",cursor:"pointer",padding:"4px",width:"32px",height:"32px",borderRadius:"4px"},children:t.jsx(Pe,{style:{fontSize:"19px"}})})}),t.jsx(oe,{title:"Sil",children:t.jsx("button",{onClick:()=>ot(e.id),className:"text-red-500 hover:text-red-700 flex items-center justify-center transition-colors duration-200",style:{background:"none",border:"none",cursor:"pointer",padding:"4px",width:"32px",height:"32px",borderRadius:"4px"},children:t.jsx(ye,{size:19})})})]})}],it=(l,e)=>{},re=(l,e,s)=>z&&z.id===e.id&&z.field===s?t.jsx(b,{value:X,onChange:c=>ue(c.target.value),onBlur:()=>ne(e.id,s),onPressEnter:()=>ne(e.id,s),autoFocus:!0}):t.jsx("div",{onDoubleClick:()=>Me(e.id,s,e[s]),children:l}),Me=(l,e,s)=>{ve({id:l,field:e}),ue(s)},ne=async(l,e)=>{var s;if(!z)return;const c=(s=H.find(n=>n.id===l))==null?void 0:s[e];try{const n=await It(l,{[e]:X});if(!n.success)throw new Error(n.error||"Güncelleme başarısız");A(d=>d.map(u=>u.id===l?{...u,[e]:X}:u)),h.success("Güncellendi")}catch(n){A(d=>d.map(u=>u.id===l?{...u,[e]:c}:u)),h.error("Güncelleme hatası: "+(n.message||""))}finally{ve(null)}},ot=async l=>{if(l)try{await _t(l),A(e=>e.filter(s=>s.id!==l)),h.success("Silindi")}catch(e){h.error("Silme hatası")}},ae=async()=>{K(!0);try{const l=await zt(),s=(Array.isArray(l.data)?l.data:[]).filter(c=>c.username!==""&&c.email!=="");je(s),A(s),he(s.length),a&&a(s.length)}catch(l){je([]),A([]),he(0),a&&a(0)}finally{K(!1)}};m.useEffect(()=>{ae()},[]),m.useEffect(()=>{const l=[{name:"Marka İş Birliği Teklifi",subject:"Yeni Marka İş Birliği Fırsatı",content:`Merhaba {username},

Markanız için yeni bir iş birliği fırsatı değerlendirmek üzere sizinle iletişime geçiyoruz. Detayları konuşmak ve sunumumuzu paylaşmak için müsait olduğunuz bir zaman dilimini bizimle paylaşabilir misiniz?

Sevgiler,`},{name:"Marka Brief",subject:"Marka Brief Dokümantasyonu",content:`Merhaba {username},

Yeni kampanyamız için size özel hazırladığımız brief dokümanını ekte bulabilirsiniz. Kampanyada yer almanızdan memnuniyet duyacağız.

Sorularınız için her zaman iletişime geçebilirsiniz.

Saygılarımızla,`},{name:"Etkinlik Daveti",subject:"Özel Etkinlik Daveti",content:`Merhaba {username},

[TARİH] tarihinde [YER] adresinde düzenleyeceğimiz özel etkinliğimize sizi davet etmekten mutluluk duyarız. Katılım durumunuzu en kısa sürede bizimle paylaşırsanız seviniriz.

Sevgiler,`}];try{const e=localStorage.getItem("emailTemplates");e?B(JSON.parse(e)):(B(l),localStorage.setItem("emailTemplates",JSON.stringify(l)))}catch(e){B(l),localStorage.setItem("emailTemplates",JSON.stringify(l))}},[]),m.useEffect(()=>{const l=[{name:"Marka İş Birliği Şablonu",content:"Merhaba {username}, yeni bir marka iş birliği fırsatımız var. Detaylar için dönüş yapabilir misiniz?"},{name:"Marka Brief Şablonu",content:"Merhaba {username}, size yeni bir marka briefingi göndermek istiyorum. Müsait olduğunuzda iletişime geçebilir miyiz?"}];try{const e=localStorage.getItem("whatsappTemplates");e?P(JSON.parse(e)):(P(l),localStorage.setItem("whatsappTemplates",JSON.stringify(l)))}catch(e){P(l),localStorage.setItem("whatsappTemplates",JSON.stringify(l))}},[]);const ct=l=>{const e={name:l.name,subject:l.subject,content:l.content},s=[...Z,e];B(s),localStorage.setItem("emailTemplates",JSON.stringify(s)),E(!1),D.resetFields()},dt=l=>{const e={name:l.name,content:l.content},s=[...W,e];P(s),localStorage.setItem("whatsappTemplates",JSON.stringify(s)),L(!1),T.resetFields(),h.success("Şablon eklendi")},U=H.filter(l=>{var e,s;const c=$?((e=l.username)==null?void 0:e.toLowerCase().includes($.toLowerCase()))||((s=l.email)==null?void 0:s.toLowerCase().includes($.toLowerCase())):!0;let n=!0;g.followers&&g.followers.length>0&&(n=g.followers.some(u=>{const k=Number(l.followers||0);return u==="5k-10k"?k>=5e3&&k<=1e4:u==="10k-50k"?k>=1e4&&k<=5e4:u==="50k-100k"?k>=5e4&&k<=1e5:u==="100k-500k"?k>=1e5&&k<=5e5:u==="500k+"?k>5e5:!0}));let d=!0;return g.hasTelefon&&(d=!!l.telefon),c&&n&&d}),ut=async()=>{K(!0);try{const l=H.map(n=>n.username).filter(Boolean),e=localStorage.getItem("x_tuber_token");if(!e){h.error("Oturum bilgileriniz bulunamadı. Lütfen tekrar giriş yapın."),K(!1),setTimeout(()=>{window.location.href="/login"},1500);return}const c=await(await fetch(`${Ee.X_SITE_BASE_URL}/update_followers.php`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${e}`},body:JSON.stringify({usernames:l})})).json();if(c.success){const n=c.results.filter(u=>u.success).length,d=c.results.length-n;h.success(`Takipçi sayıları güncellendi! Başarılı: ${n}, Başarısız: ${d}`)}else h.error(c.error||"Takipçi sayıları güncellenemedi.")}catch(l){h.error("Takipçi sayıları güncellenirken hata oluştu.")}finally{K(!1),ae()}},mt=async()=>{try{const l=await R.validateFields();pe(!0);let e=[];if(J.length===0){h.info("Hiçbir influencer seçmediniz. Filtrelenmiş tüm influencerlar işleme dahil edilecek.");let n=U;f.length>0&&(n=U.filter(d=>{const u=Number(d.followers||0);return f.includes("all")?!0:f.some(k=>k==="10k_less"?u<1e4:k==="10k_50k"?u>=1e4&&u<=5e4:k==="50k_100k"?u>=5e4&&u<=1e5:k==="100k_plus"?u>1e5:!1)})),e=n.filter(d=>d.email)}else{let n=Y.filter(d=>J.includes(d.id)&&d.email);f.length>0&&(n=n.filter(d=>{const u=Number(d.followers||0);return f.includes("all")?!0:f.some(k=>k==="10k_less"?u<1e4:k==="10k_50k"?u>=1e4&&u<=5e4:k==="50k_100k"?u>=5e4&&u<=1e5:k==="100k_plus"?u>1e5:!1)})),e=n}if(e.length===0){h.error("Geçerli e-posta adresi olan influencer bulunamadı"),pe(!1);return}const s=e.map(n=>n.email),c=await Promise.all(le.map(async n=>{const d=await rl(n.originFileObj);return{filename:n.name,content:d,contentType:n.type}}));h.loading({content:"Mail gönderiliyor...",key:"bulk-email"}),await Nt(s,l.subject,l.content),h.success({content:"Mailler gönderildi",key:"bulk-email"}),q(!1),R.resetFields(),Ue([]),Ne([]),w([])}catch(l){h.error({content:"Mail gönderilemedi",key:"bulk-email"})}finally{pe(!1)}},ft=async()=>{try{const l=await R.validateFields();lt(l.subject),tt(l.content),fe(!0)}catch(l){h.error("Lütfen tüm alanları doldurun")}},pt=l=>{if(!l.telefon){h.warning("Telefon numarası bulunamadı!");return}let e=l.telefon.toString().replace(/\s+/g,"");e.startsWith("0")&&(e=e.substring(1)),e.startsWith("+")||(e="+90"+e);const s=`Merhaba ${l.username},`,c=`https://wa.me/${e}?text=${encodeURIComponent(s)}`;window.open(c,"_blank"),h.success("WhatsApp mesajı açılıyor...")},ht=l=>{if(!l.email)return;const e=`mailto:${l.email}?subject=${encodeURIComponent("Merhaba")}&body=${encodeURIComponent(`Merhaba ${l.username},`)}`;window.open(e,"_blank")},xt=async()=>{try{const l=await ee.validateFields();let e=[];if(J.length===0){h.info("Hiçbir influencer seçmediniz. Filtrelenmiş tüm influencerlar işleme dahil edilecek.");let s=U;f.length>0&&(s=U.filter(c=>{const n=Number(c.followers||0);return f.includes("all")?!0:f.some(d=>d==="10k_less"?n<1e4:d==="10k_50k"?n>=1e4&&n<=5e4:d==="50k_100k"?n>=5e4&&n<=1e5:d==="100k_plus"?n>1e5:!1)})),e=s.filter(c=>c.telefon)}else{let s=Y.filter(c=>J.includes(c.id)&&c.telefon);f.length>0&&(s=s.filter(c=>{const n=Number(c.followers||0);return f.includes("all")?!0:f.some(d=>d==="10k_less"?n<1e4:d==="10k_50k"?n>=1e4&&n<=5e4:d==="50k_100k"?n>=5e4&&n<=1e5:d==="100k_plus"?n>1e5:!1)})),e=s}if(e.length===0){h.error("Geçerli telefon numarası olan influencer bulunamadı");return}e.forEach((s,c)=>{setTimeout(()=>{if(!s.telefon)return;let n=s.telefon.toString().replace(/\s+/g,"");n.startsWith("0")&&(n=n.substring(1)),n.startsWith("+")||(n="+90"+n);const d=l.content.replace("{username}",s.username||""),u=`https://wa.me/${n}?text=${encodeURIComponent(d)}`;window.open(u,"_blank")},c*300)}),h.success("WhatsApp mesajları açılıyor..."),Q(!1),ee.resetFields(),w([])}catch(l){h.error("WhatsApp mesajları gönderilemedi")}},gt=l=>{if(!ze)return;const e=W.map(s=>s.name===ze.name?{name:l.name,content:l.content}:s);P(e),localStorage.setItem("whatsappTemplates",JSON.stringify(e)),te(!1),me(null),T.resetFields(),h.success("Şablon güncellendi")},kt=l=>{const e=W.filter(s=>s.name!==l);P(e),localStorage.setItem("whatsappTemplates",JSON.stringify(e)),Ie===l&&_e(void 0),h.success("Şablon silindi")},yt=l=>{me(l),T.setFieldsValue(l),te(!0)};m.useEffect(()=>{a&&a(Y.length)},[Y,a]),m.useEffect(()=>{he(o||0)},[o]);const bt=async()=>{const l=H.map(n=>n.username).filter(Boolean);V({total:l.length,current:0,success:0,fail:0,running:!0}),localStorage.setItem("profileImageProgress",JSON.stringify({total:l.length,current:0,success:0,fail:0,running:!0}));const e=localStorage.getItem("x_tuber_token");if(!e){h.error("Oturum bilgileriniz bulunamadı. Lütfen tekrar giriş yapın."),V({total:0,current:0,success:0,fail:0,running:!1}),localStorage.removeItem("profileImageProgress"),setTimeout(()=>{window.location.href="/login"},1500);return}let s=0,c=0;for(let n=0;n<l.length;n++){try{const u=await(await fetch(`${Ee.X_SITE_BASE_URL}/fetch_profile_images.php`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${e}`},body:JSON.stringify({usernames:[l[n]],type:"influencer"})})).json();u.success&&u.results&&u.results[0]&&u.results[0].success?s++:c++}catch(d){c++}V({total:l.length,current:n+1,success:s,fail:c,running:!0}),localStorage.setItem("profileImageProgress",JSON.stringify({total:l.length,current:n+1,success:s,fail:c,running:!0}))}V({total:l.length,current:l.length,success:s,fail:c,running:!1}),localStorage.removeItem("profileImageProgress"),h.success(`Profil resimleri çekildi! Başarılı: ${s}, Başarısız: ${c}`),ae()};return m.useEffect(()=>{const l=localStorage.getItem("profileImageProgress");l&&V(JSON.parse(l))},[]),t.jsxs(jt,{backend:wt,children:[t.jsxs("div",{className:r?"p-0":"m-6",children:[!r&&t.jsxs("div",{className:"flex justify-between items-center mb-6",children:[t.jsx("h1",{className:"text-2xl font-semibold text-indigo-600",children:"Influencerlar"}),t.jsxs("span",{className:"bg-indigo-100 text-indigo-800 text-sm font-medium px-2.5 py-1 rounded",children:["Toplam: ",rt]})]}),t.jsxs("div",{className:`bg-white dark:bg-gray-800 ${r?"rounded-none shadow-none border-0":"rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700"}`,children:[F.running&&t.jsxs("div",{style:{marginBottom:16},children:[t.jsx(Pt,{percent:Math.round(F.current/F.total*100),status:"active"}),t.jsx("div",{style:{marginTop:8,fontSize:14},children:`Çekilen: ${F.current} / ${F.total} | Başarılı: ${F.success} | Hatalı: ${F.fail}`})]}),t.jsxs("div",{className:"flex flex-wrap items-center gap-4 mb-4 px-6 pt-6 pb-2",children:[t.jsxs("div",{className:"relative",style:{width:384},children:[t.jsx("input",{type:"text",placeholder:"Influencer ara (İsim, Mail, Kullanıcı Adı...)",value:$,onChange:l=>we(l.target.value),className:"pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent dark:bg-gray-700 dark:text-white"}),t.jsx(Ct,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),$&&t.jsx("button",{onClick:()=>we(""),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:t.jsx(Lt,{className:"w-4 h-4"})})]}),t.jsxs("div",{className:"flex flex-wrap gap-2 items-center",children:[t.jsxs(x,{className:"flex items-center",onClick:()=>q(!0),children:[t.jsx(Pe,{className:"h-4 w-4 mr-2"}),"Toplu Mail Gönder"]}),t.jsxs(x,{className:"flex items-center",onClick:()=>Q(!0),children:[t.jsx(Re,{className:"h-4 w-4 mr-2"}),"Toplu WhatsApp"]}),t.jsx(Rt,{content:t.jsxs("div",{className:"p-4",style:{width:"300px"},children:[t.jsx("h3",{className:"text-lg font-medium mb-3",children:"Filtreler"}),t.jsxs("div",{className:"mb-4",children:[t.jsx("h4",{className:"font-medium mb-2",children:"Takipçi Sayısı"}),t.jsxs("div",{className:"space-y-2",children:[t.jsx(y,{checked:(j=g.followers)==null?void 0:j.includes("5k-10k"),onChange:l=>{const e=[...g.followers||[]];if(l.target.checked)e.push("5k-10k");else{const s=e.indexOf("5k-10k");s>-1&&e.splice(s,1)}N({...g,followers:e})},children:"5K - 10K"}),t.jsx(y,{checked:(C=g.followers)==null?void 0:C.includes("10k-50k"),onChange:l=>{const e=[...g.followers||[]];if(l.target.checked)e.push("10k-50k");else{const s=e.indexOf("10k-50k");s>-1&&e.splice(s,1)}N({...g,followers:e})},children:"10K - 50K"}),t.jsx(y,{checked:(M=g.followers)==null?void 0:M.includes("50k-100k"),onChange:l=>{const e=[...g.followers||[]];if(l.target.checked)e.push("50k-100k");else{const s=e.indexOf("50k-100k");s>-1&&e.splice(s,1)}N({...g,followers:e})},children:"50K - 100K"}),t.jsx(y,{checked:(S=g.followers)==null?void 0:S.includes("100k-500k"),onChange:l=>{const e=[...g.followers||[]];if(l.target.checked)e.push("100k-500k");else{const s=e.indexOf("100k-500k");s>-1&&e.splice(s,1)}N({...g,followers:e})},children:"100K - 500K"}),t.jsx(y,{checked:(_=g.followers)==null?void 0:_.includes("500k+"),onChange:l=>{const e=[...g.followers||[]];if(l.target.checked)e.push("500k+");else{const s=e.indexOf("500k+");s>-1&&e.splice(s,1)}N({...g,followers:e})},children:"500K+"})]})]}),t.jsxs("div",{className:"mb-4",children:[t.jsx("h4",{className:"font-medium mb-2",children:"Telefon Numarası"}),t.jsx("div",{className:"space-y-2",children:t.jsx(y,{checked:g.hasTelefon,onChange:l=>{N({...g,hasTelefon:l.target.checked})},children:"Telefon Numarası Olanlar"})})]}),t.jsx(ke,{style:{margin:"12px 0"}}),t.jsxs("div",{className:"flex justify-between pt-2",children:[t.jsx(x,{size:"small",onClick:()=>{N({category:[],status:[],location:[],followers:[],hasTelefon:!1}),h.success("Filtreler sıfırlandı")},children:"Sıfırla"}),t.jsx(x,{type:"primary",size:"small",onClick:()=>{h.success("Filtreler uygulandı"),Se(!1)},style:{backgroundColor:"#1890ff",borderColor:"#1890ff",color:"white"},children:"Uygula"})]})]}),trigger:"click",open:qe,onOpenChange:Se,children:t.jsxs(x,{className:"flex items-center",children:[t.jsx(Gt,{className:"h-4 w-4 mr-2"}),"Filtrele"]})}),t.jsx(At,{overlay:t.jsxs(G,{children:[t.jsx(G.Item,{icon:t.jsx(Ot,{}),onClick:bt,children:"Profil Resimlerini Çek"},"fetchProfileImages"),t.jsx(G.Item,{icon:t.jsx(Mt,{}),onClick:ut,children:"Takipçi Sayılarını Güncelle"},"updateFollowers"),t.jsx(G.Item,{icon:t.jsx(be,{}),onClick:()=>xe(!0),children:"Yeni Influencer Ekle"},"1"),t.jsx(G.Item,{icon:t.jsx($e,{}),onClick:()=>{var l;return(l=document.getElementById("excel-import-input-influencer"))==null?void 0:l.click()},children:"Excel'den İçe Aktar"},"2")]}),trigger:["click"],children:t.jsx(x,{icon:t.jsx(Vt,{}),type:"text",style:{border:"none",background:"transparent",boxShadow:"none"}})})]})]}),t.jsx("div",{className:"px-0",children:t.jsx($t,{rowKey:"id",components:{header:{cell:({...l})=>{const e=ge.findIndex(s=>s.key===l["data-column-key"]);return e!==-1?t.jsx(sl,{index:e,title:ge[e].title,moveColumn:it}):t.jsx("th",{...l})}}},columns:ge,dataSource:U,loading:Je,pagination:{pageSize:50,showSizeChanger:!0,pageSizeOptions:[50,100,500],showTotal:(l,e)=>`${e[0]}-${e[1]} / ${l} kayıt`,position:["bottomCenter"]},className:"influencers-table w-full",scroll:{x:"max-content"},style:{width:"100%"}})})]})]}),t.jsx(O,{title:"Toplu E-posta Gönder",open:Ge,onCancel:()=>q(!1),closable:!0,footer:[t.jsx(x,{onClick:()=>q(!1),style:{borderColor:v?"#6b7280":"#d1d5db",color:v?"#e5e7eb":"#374151"},children:"İptal"},"back"),t.jsx(x,{onClick:ft,style:{borderColor:"#1890ff",color:"#1890ff"},children:"Önizle"},"preview"),t.jsx(x,{type:"primary",onClick:mt,loading:st,style:{backgroundColor:"#1890ff",borderColor:"#1890ff",color:"white"},children:"Gönder"},"submit")],width:700,centered:!0,bodyStyle:{paddingRight:16},style:{top:20},children:t.jsxs(p,{form:R,layout:"vertical",children:[t.jsx(p.Item,{label:"Şablon",children:t.jsxs(Ae,{children:[t.jsx(ie,{style:{width:220},placeholder:"Şablon seçin",value:Ce,onChange:l=>{Te(l);const e=Z.find(s=>s.name===l);e&&R.setFieldsValue({subject:e.subject,content:e.content})},dropdownRender:l=>t.jsxs(t.Fragment,{children:[l,t.jsx(ke,{style:{margin:"8px 0"}}),t.jsx("div",{style:{padding:"0 8px 4px"},children:t.jsx(x,{type:"text",icon:t.jsx(be,{size:14}),onClick:()=>E(!0),block:!0,children:"Yeni Şablon Ekle"})})]}),children:Z.map(l=>t.jsx(ie.Option,{value:l.name,children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsx("span",{children:l.name}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(x,{type:"text",size:"small",icon:t.jsx(Ke,{size:14}),onClick:e=>{e.stopPropagation(),D.setFieldsValue(l),E(!0)}}),t.jsx(x,{type:"text",size:"small",danger:!0,icon:t.jsx(ye,{size:14}),onClick:e=>{e.stopPropagation();const s=Z.filter(c=>c.name!==l.name);B(s),localStorage.setItem("emailTemplates",JSON.stringify(s)),Ce===l.name&&Te(void 0),h.success("Şablon silindi")}})]})]})},l.name))}),t.jsx(x,{type:"link",onClick:()=>E(!0),children:"Yeni Şablon"})]})}),t.jsx(p.Item,{label:"Takipçi Sayısı Aralığı",children:t.jsxs("div",{className:"flex flex-wrap gap-2",children:[t.jsx(y,{checked:f.includes("all"),onChange:l=>{l.target.checked?w(["all"]):w(f.filter(e=>e!=="all"))},children:"Tümü"}),t.jsx(y,{checked:f.includes("10k_less"),onChange:l=>{let e=[...f];l.target.checked?e=e.filter(s=>s!=="all").concat(["10k_less"]):e=e.filter(s=>s!=="10k_less"),w(e)},children:"10K'dan az"}),t.jsx(y,{checked:f.includes("10k_50k"),onChange:l=>{let e=[...f];l.target.checked?e=e.filter(s=>s!=="all").concat(["10k_50k"]):e=e.filter(s=>s!=="10k_50k"),w(e)},children:"10K - 50K"}),t.jsx(y,{checked:f.includes("50k_100k"),onChange:l=>{let e=[...f];l.target.checked?e=e.filter(s=>s!=="all").concat(["50k_100k"]):e=e.filter(s=>s!=="50k_100k"),w(e)},children:"50K - 100K"}),t.jsx(y,{checked:f.includes("100k_plus"),onChange:l=>{let e=[...f];l.target.checked?e=e.filter(s=>s!=="all").concat(["100k_plus"]):e=e.filter(s=>s!=="100k_plus"),w(e)},children:"100K+"})]})}),t.jsx(p.Item,{name:"subject",label:"Konu",rules:[{required:!0,message:"Konu giriniz"}],children:t.jsx(b,{})}),t.jsxs(p.Item,{name:"content",label:"Mesaj",rules:[{required:!0,message:"Mesaj giriniz"}],children:[t.jsx(b.TextArea,{rows:4,style:{marginBottom:"10px"}}),t.jsx(Kt,{message:"HTML Desteği",description:"E-posta içeriğinde HTML kullanabilirsiniz. Örneğin: <b>kalın</b>, <i>italik</i>, <a href='URL'>link</a>",type:"info",showIcon:!0,closable:!0,style:{marginTop:"10px",marginBottom:"10px"}})]}),t.jsxs(p.Item,{label:"Dosya Ekle",children:[t.jsx(Bt,{listType:"picture",fileList:le,onChange:({fileList:l})=>Ne(l),beforeUpload:()=>!1,multiple:!0,children:t.jsx(x,{icon:t.jsx($e,{}),children:"Dosya Seç"})}),t.jsx("div",{className:"text-xs text-gray-500 mt-1",children:"Desteklenen dosya türleri: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG, GIF (maks. 10MB)"})]})]})}),t.jsx(O,{title:"Yeni E-posta Şablonu",open:Ye,onCancel:()=>{E(!1),D.resetFields()},footer:[t.jsx(x,{onClick:()=>E(!1),style:{borderColor:v?"#6b7280":"#d1d5db",color:v?"#e5e7eb":"#374151"},children:"İptal"},"back"),t.jsx(x,{type:"primary",onClick:()=>D.submit(),style:{backgroundColor:"#1890ff",borderColor:"#1890ff",color:"white"},children:"Kaydet"},"submit")],width:600,centered:!0,bodyStyle:{paddingRight:16},style:{top:20},children:t.jsxs(p,{form:D,layout:"vertical",onFinish:ct,children:[t.jsx(p.Item,{name:"name",label:"Şablon Adı",rules:[{required:!0}],children:t.jsx(b,{})}),t.jsx(p.Item,{name:"subject",label:"Konu",rules:[{required:!0}],children:t.jsx(b,{})}),t.jsx(p.Item,{name:"content",label:"Mesaj",rules:[{required:!0}],children:t.jsx(b.TextArea,{rows:4})})]})}),t.jsx(O,{title:"Toplu WhatsApp Mesajı Gönder",open:He,onCancel:()=>Q(!1),footer:[t.jsx(x,{onClick:()=>Q(!1),style:{borderColor:v?"#6b7280":"#d1d5db",color:v?"#e5e7eb":"#374151"},children:"İptal"},"back"),t.jsx(x,{type:"primary",onClick:xt,style:{backgroundColor:"#25D366",borderColor:"#25D366",color:"white"},children:"Gönder"},"submit")],width:600,centered:!0,bodyStyle:{paddingRight:16},style:{top:20},children:t.jsxs(p,{form:ee,layout:"vertical",children:[t.jsx(p.Item,{label:"Şablon",children:t.jsxs(Ae,{children:[t.jsx(ie,{style:{width:220},placeholder:"Şablon seçin",value:Ie,onChange:l=>{_e(l);const e=W.find(s=>s.name===l);e&&ee.setFieldsValue({content:e.content})},dropdownRender:l=>t.jsxs(t.Fragment,{children:[l,t.jsx(ke,{style:{margin:"8px 0"}}),t.jsx("div",{style:{padding:"0 8px 4px"},children:t.jsx(x,{type:"text",icon:t.jsx(be,{size:14}),onClick:()=>L(!0),block:!0,children:"Yeni Şablon Ekle"})})]}),children:W.map(l=>t.jsx(ie.Option,{value:l.name,children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsx("span",{children:l.name}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(x,{type:"text",size:"small",icon:t.jsx(Ke,{size:14}),onClick:e=>{e.stopPropagation(),yt(l)}}),t.jsx(x,{type:"text",size:"small",danger:!0,icon:t.jsx(ye,{size:14}),onClick:e=>{e.stopPropagation(),kt(l.name)}})]})]})},l.name))}),t.jsx(x,{type:"link",onClick:()=>L(!0),children:"Yeni Şablon"})]})}),t.jsx(p.Item,{label:"Takipçi Sayısı Aralığı",children:t.jsxs("div",{className:"flex flex-wrap gap-2",children:[t.jsx(y,{checked:f.includes("all"),onChange:l=>{l.target.checked?w(["all"]):w(f.filter(e=>e!=="all"))},children:"Tümü"}),t.jsx(y,{checked:f.includes("10k_less"),onChange:l=>{let e=[...f];l.target.checked?e=e.filter(s=>s!=="all").concat(["10k_less"]):e=e.filter(s=>s!=="10k_less"),w(e)},children:"10K'dan az"}),t.jsx(y,{checked:f.includes("10k_50k"),onChange:l=>{let e=[...f];l.target.checked?e=e.filter(s=>s!=="all").concat(["10k_50k"]):e=e.filter(s=>s!=="10k_50k"),w(e)},children:"10K - 50K"}),t.jsx(y,{checked:f.includes("50k_100k"),onChange:l=>{let e=[...f];l.target.checked?e=e.filter(s=>s!=="all").concat(["50k_100k"]):e=e.filter(s=>s!=="50k_100k"),w(e)},children:"50K - 100K"}),t.jsx(y,{checked:f.includes("100k_plus"),onChange:l=>{let e=[...f];l.target.checked?e=e.filter(s=>s!=="all").concat(["100k_plus"]):e=e.filter(s=>s!=="100k_plus"),w(e)},children:"100K+"})]})}),t.jsx(p.Item,{name:"content",label:"Mesaj",initialValue:"Merhaba {username},",rules:[{required:!0,message:"Mesaj giriniz"}],children:t.jsx(b.TextArea,{rows:4,placeholder:"Mesajınızı yazın. {username} kullanıcı adını temsil eder."})})]})}),t.jsx(O,{title:"Yeni WhatsApp Şablonu",open:Xe,onCancel:()=>{L(!1),T.resetFields()},footer:[t.jsx(x,{onClick:()=>L(!1),style:{borderColor:v?"#6b7280":"#d1d5db",color:v?"#e5e7eb":"#374151"},children:"İptal"},"back"),t.jsx(x,{type:"primary",onClick:()=>T.submit(),style:{backgroundColor:"#25D366",borderColor:"#25D366",color:"white"},children:"Kaydet"},"submit")],width:600,centered:!0,bodyStyle:{paddingRight:16},style:{top:20},children:t.jsxs(p,{form:T,layout:"vertical",onFinish:dt,children:[t.jsx(p.Item,{name:"name",label:"Şablon Adı",rules:[{required:!0}],children:t.jsx(b,{})}),t.jsx(p.Item,{name:"content",label:"Mesaj",rules:[{required:!0}],children:t.jsx(b.TextArea,{rows:4,placeholder:"Mesajınızı yazın. {username} kullanıcı adını temsil eder."})})]})}),t.jsx(O,{title:"WhatsApp Şablonunu Düzenle",open:Ze,onCancel:()=>{te(!1),me(null),T.resetFields()},footer:[t.jsx(x,{onClick:()=>te(!1),style:{borderColor:v?"#6b7280":"#d1d5db",color:v?"#e5e7eb":"#374151"},children:"İptal"},"back"),t.jsx(x,{type:"primary",onClick:()=>T.submit(),style:{backgroundColor:"#25D366",borderColor:"#25D366",color:"white"},children:"Kaydet"},"submit")],width:600,centered:!0,bodyStyle:{paddingRight:16},style:{top:20},children:t.jsxs(p,{form:T,layout:"vertical",onFinish:gt,children:[t.jsx(p.Item,{name:"name",label:"Şablon Adı",rules:[{required:!0}],children:t.jsx(b,{})}),t.jsx(p.Item,{name:"content",label:"Mesaj",rules:[{required:!0}],children:t.jsx(b.TextArea,{rows:4,placeholder:"Mesajınızı yazın. {username} kullanıcı adını temsil eder."})})]})}),t.jsx(O,{title:`E-posta Önizleme: ${Fe}`,open:Qe,onCancel:()=>fe(!1),footer:[t.jsx(x,{onClick:()=>fe(!1),style:{borderColor:v?"#6b7280":"#d1d5db",color:v?"#e5e7eb":"#374151"},children:"Kapat"},"back")],width:700,children:t.jsx("div",{className:"email-preview-container",children:t.jsxs("div",{style:{border:"1px solid #e6e6e6",borderRadius:"4px",padding:"20px",backgroundColor:"#fff"},children:[t.jsxs("div",{style:{marginBottom:"15px",borderBottom:"1px solid #f0f0f0",paddingBottom:"10px"},children:[t.jsxs("div",{children:[t.jsx("strong",{children:"Konu:"})," ",Fe]}),t.jsxs("div",{children:[t.jsx("strong",{children:"Ekler:"})," ",le.length>0?le.map(l=>l.name).join(", "):"Yok"]})]}),t.jsx("div",{dangerouslySetInnerHTML:{__html:et},style:{padding:"10px",minHeight:"300px"}})]})})}),t.jsx(O,{title:"Yeni Influencer Ekle",open:nt,onCancel:()=>{xe(!1),se.resetFields()},onOk:at,okText:"Kaydet",cancelText:"İptal",width:500,centered:!0,bodyStyle:{padding:24},children:t.jsxs(p,{form:se,layout:"vertical",children:[t.jsxs(p.Item,{name:"username",label:"Kullanıcı Adı",rules:[{required:!0,message:"Kullanıcı adı zorunlu"}],children:[" ",t.jsx(b,{})," "]}),t.jsxs(p.Item,{name:"email",label:"E-posta",rules:[{required:!0,type:"email",message:"Geçerli e-posta girin"}],children:[" ",t.jsx(b,{})," "]}),t.jsxs(p.Item,{name:"followers",label:"Takipçi Sayısı",rules:[{required:!0,message:"Takipçi sayısı zorunlu"}],children:[" ",t.jsx(Dt,{min:0,style:{width:"100%"}})," "]}),t.jsxs(p.Item,{name:"telefon",label:"Telefon",children:[" ",t.jsx(b,{})," "]}),t.jsxs(p.Item,{name:"sosyal_medya",label:"Sosyal Medya Linki",children:[" ",t.jsx(b,{})," "]})]})})]})}export{vl as default};
