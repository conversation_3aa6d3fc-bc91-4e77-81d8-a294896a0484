<?php
header('Content-Type: application/json; charset=utf-8');

// Log dosyası
$logFile = __DIR__ . '/whatsapp_incoming_log.txt';

// İstek verilerini al
$payload = file_get_contents('php://input');
$timestamp = date('Y-m-d H:i:s');

// Doğrulama isteği kontrolü
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $mode = $_GET['hub_mode'] ?? '';
    $token = $_GET['hub_verify_token'] ?? '';
    $challenge = $_GET['hub_challenge'] ?? '';
    
    // Doğrulama token'ını kontrol et (kendi webhook token'ınızla değiştirin)
    if ($mode === 'subscribe' && $token === 'TUBER_WHATSAPP_WEBHOOK_TOKEN') {
        echo $challenge;
        exit;
    }
    
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Geçersiz token']);
    exit;
}

// POST isteği - WhatsApp'ten gelen mesaj
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Gelen veriyi logla
    file_put_contents($logFile, "[{$timestamp}] {$payload}" . PHP_EOL, FILE_APPEND);
    
    // JSON'ı çözümle
    $data = json_decode($payload, true);
    
    // Mesaj var mı kontrol et
    if ($data && isset($data['entry'][0]['changes'][0]['value']['messages'][0])) {
        $message = $data['entry'][0]['changes'][0]['value']['messages'][0];
        $contactId = $message['from'] ?? '';
        
        // WebSocket bildirim işlemi - mesaj geldiğinde WebSocket istemcilerine bildir
        notifyWebSocketClients($contactId, $message);
    }
    
    // WhatsApp API başarılı yanıt bekler
    echo json_encode(['success' => true]);
    exit;
}

// WebSocket istemcilerine bildirim gönder
function notifyWebSocketClients($contactId, $messageData) {
    // WebSocket bildirim server'ına HTTP isteği gönder
    $notificationPayload = json_encode([
        'action' => 'notify',
        'contactId' => $contactId,
        'message' => $messageData
    ]);
    
    // WebSocket notifikasyon API'sine istek gönder
    $ch = curl_init('http://localhost:8081/notify');
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
    curl_setopt($ch, CURLOPT_POSTFIELDS, $notificationPayload);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Content-Length: ' . strlen($notificationPayload)
    ]);
    
    // Bildirimi logla
    $logFile = __DIR__ . '/websocket_notification_log.txt';
    file_put_contents(
        $logFile, 
        "[" . date('Y-m-d H:i:s') . "] Bildirim gönderildi: {$contactId}" . PHP_EOL, 
        FILE_APPEND
    );
    
    $result = curl_exec($ch);
    curl_close($ch);
    
    return $result;
} 