<?php
require_once __DIR__ . '/../config/config.php';
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Database auto-detection kullan
$databases = ['tuberaja_yayinci_takip', 'tuberaja_yayinci_akademi', 'tiktok_live_data'];
$liveDataDatabase = null;

foreach ($databases as $database) {
try {
        $testQuery = "SELECT 1 FROM $database.live_data LIMIT 1";
        $db->query($testQuery);
        $liveDataDatabase = $database;
        error_log("Publisher Discovery Query API: live_data tablosu bulundu: $database");
        break;
    } catch (PDOException $e) {
        continue;
    }
}

if (!$liveDataDatabase) {
    die(json_encode(['success' => false, 'error' => 'live_data tablosu bulunamadı']));
}

// Automation commands tablosu için de aynı kontrolü yapalım
$automationDatabase = null;
foreach ($databases as $database) {
    try {
        $testQuery = "SELECT 1 FROM $database.automation_commands LIMIT 1";
        $db->query($testQuery);
        $automationDatabase = $database;
        error_log("Publisher Discovery Query API: automation_commands tablosu bulundu: $database");
        break;
    } catch (PDOException $e) {
        continue;
    }
}

if (!$automationDatabase) {
    die(json_encode(['success' => false, 'error' => 'automation_commands tablosu bulunamadı']));
}

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);
$usernames = $input['usernames'] ?? [];
$action = $input['action'] ?? '';
$status = $input['status'] ?? null;

if (!in_array($action, ['check_status', 'send_message'])) {
    echo json_encode(['success' => false, 'error' => 'Geçersiz aksiyon']);
    exit();
}

// YENİ: Statü filtresiyle toplu sorgulama
if ($status) {
    // Statüye göre tüm kullanıcıları çek
    $stmt = $db->prepare("SELECT username FROM $liveDataDatabase.live_data WHERE status = ?");
    $stmt->execute([$status]);
    $usernames = $stmt->fetchAll(PDO::FETCH_COLUMN);
    $usernames = array_map(function($u) { return ['username' => $u]; }, $usernames);
}

if (!is_array($usernames) || count($usernames) === 0) {
    echo json_encode(['success' => false, 'error' => 'Kullanıcı listesi boş']);
    exit();
}

try {
    $params = json_encode(['usernames' => $usernames]);
    $stmt = $db->prepare("INSERT INTO $automationDatabase.automation_commands (command, params) VALUES (:command, :params)");
    $stmt->execute([':command' => $action, ':params' => $params]);
    echo json_encode(['success' => true, 'message' => 'Komut veritabanına kaydedildi']);
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'error' => 'Komut kaydedilemedi: ' . $e->getMessage()]);
} 