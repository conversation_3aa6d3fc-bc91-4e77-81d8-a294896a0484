import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Table,
  Space,
  Badge,
  Statistic,
  Modal,
  Alert,
  Typography,
  Tabs,
  Tag,
  Input,
  DatePicker,
  Select,
  Checkbox,
  Spin,
  message,
  Tooltip,
  Radio
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  UserOutlined,
  SearchOutlined,
  EyeOutlined,
  MessageOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ReloadOutlined,
  FilterOutlined,
  HistoryOutlined,
  UsergroupAddOutlined,
  DatabaseOutlined,
  SyncOutlined,
  SendOutlined,
  LoadingOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { PageTitle } from '../components/ThemeStyles';
import { API_CONFIG } from '../config';
import apiClient from '../lib/axios';
import moment from 'moment';
import 'moment/locale/tr';
import { useData } from '../contexts/DataContext';
moment.locale('tr');

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;
const { Option } = Select;

// Yayıncı veri tipi tanımı
interface Publisher {
  id: number;
  username: string;
  viewerCount: number;
  status: 'uygun' | 'uygun_degil' | 'dm_gonderildi' | 'dm_gonderilemedi' | 'beklemede' | 'baska_ajans' | 'coklu_hesap' | 'bulunamadi' | 'desteklenmeyen_bolge' | 'diger_nedenler' | 'manuel_eklendi';
  createdAt: string;
  messageStatus: string;
  platform: string;
  followers?: number;
  category?: string;
  region?: string;
  lastChecked: string;
  selected?: boolean;
}

// Python program durumu
interface ProgramStatus {
  running: boolean;
  startTime?: string;
  pid?: number;
  logOutput: string[];
  status?: string;
}

// İstatistik verisi
interface StatsData {
  totalPublishers: number;
  suitablePublishers: number;
  messageSent: number;
  averageViewers: number;
  todayDiscovered: number;
  statusCounts: Record<string, number>;
}

const statusColors: { [key: string]: string } = {
  uygun: 'green',
  uygun_degil: 'red',
  dm_gonderildi: 'blue',
  dm_gonderilemedi: 'orange',
  beklemede: 'purple',
  baska_ajans: 'volcano',
  coklu_hesap: 'magenta',
  bulunamadi: 'grey',
  desteklenmeyen_bolge: 'cyan',
  diger_nedenler: 'gold',
  manuel_eklendi: 'lime'
};

const statusNames: { [key: string]: string } = {
  uygun: 'Uygun',
  uygun_degil: 'Uygun Değil',
  dm_gonderildi: 'DM Gönderildi',
  dm_gonderilemedi: 'DM Gönderilemedi',
  beklemede: 'Beklemede',
  baska_ajans: 'Başka bir ajansta',
  coklu_hesap: 'Çoklu hesap riski',
  bulunamadi: 'Bulunamadı',
  desteklenmeyen_bolge: 'Desteklenmeyen bölge',
  diger_nedenler: 'Diğer nedenler',
  manuel_eklendi: 'Manuel Eklendi'
};

// Türkçe ay isimleri için yardımcı fonksiyon
const getTurkishMonth = (month: number): string => {
  const turkishMonths = [
    'Ocak', 'Şubat', 'Mart', 'Nisan', 'Mayıs', 'Haziran',
    'Temmuz', 'Ağustos', 'Eylül', 'Ekim', 'Kasım', 'Aralık'
  ];
  return turkishMonths[month];
};

const formatTurkishDate = (date: string): string => {
  const momentDate = moment(date);
  const day = momentDate.date();
  const month = momentDate.month();
  const year = momentDate.year();
  const time = momentDate.format('HH:mm');

  return `${day} ${getTurkishMonth(month)} ${year} ${time}`;
};

const PublisherDiscovery: React.FC = () => {
  const { updatePublisherDiscoveryStatus } = useData();
  const [publishers, setPublishers] = useState<Publisher[]>([]);
  const [elitePublishers, setElitePublishers] = useState<Publisher[]>([]);
  const [elitePublishersLoading, setElitePublishersLoading] = useState(false);
  const [elitePageSize, setElitePageSize] = useState(10); // Elite modal için sayfa boyutu
  const [selectedPublishers, setSelectedPublishers] = useState<Publisher[]>([]);
  const [loading, setLoading] = useState(false);
  const [backgroundLoading, setBackgroundLoading] = useState(false);
  const [programStatus, setProgramStatus] = useState<ProgramStatus>({
    running: false,
    logOutput: []
  });
  const [activeTab, setActiveTab] = useState('genel');
  const [currentStatus, setCurrentStatus] = useState<string>('all');
  const [dateRange, setDateRange] = useState<[moment.Moment, moment.Moment] | null>(null);
  const [searchText, setSearchText] = useState('');
  const [logModalVisible, setLogModalVisible] = useState(false);
  const [sendMessageModalVisible, setSendMessageModalVisible] = useState(false);
  const [messageText, setMessageText] = useState('');
  const [sendingMessage, setSendingMessage] = useState(false);
  const [bulkQueryLoading, setBulkQueryLoading] = useState(false);
  const [queryType, setQueryType] = useState('check_status');
  const [cycleTime, setCycleTime] = useState('1');
  const [stats, setStats] = useState<StatsData & { todayElite?: number; todaySuitable?: number }>({
    totalPublishers: 0,
    suitablePublishers: 0,
    messageSent: 0,
    averageViewers: 0,
    todayDiscovered: 0,
    statusCounts: {},
    todayElite: 0,
    todaySuitable: 0
  });
  const [statusOptions, setStatusOptions] = useState<string[]>([]);
  const [pageSize, setPageSize] = useState(10);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const [dailySummary, setDailySummary] = useState<any[]>([]);
  const [peakStats, setPeakStats] = useState<any>({});
  const [peakStatsRange, setPeakStatsRange] = useState<'7' | '30' | '60' | '90'>('60');
  const [messageMode, setMessageMode] = useState<'none' | 'suitable' | 'elite' | 'both'>('both');
  const [eliteModalVisible, setEliteModalVisible] = useState(false);

  // Yayıncıları yükle
  const fetchPublishers = async (isBackground = false, statusOverride?: string) => {
    if (isBackground) {
      setBackgroundLoading(true);
    } else {
      setLoading(true);
    }
    try {
      const statusParam = typeof statusOverride === 'string' ? statusOverride : currentStatus;
      const response = await apiClient.get(`${API_CONFIG.X_SITE_BASE_URL}/publisher-discovery.php`, {
        params: { status: statusParam !== 'all' ? statusParam : undefined }
      });

      const publishersData = response.data && response.data.data && Array.isArray(response.data.data.publishers)
        ? response.data.data.publishers : [];
      const statsData = response.data && response.data.data && response.data.data.stats
        ? response.data.data.stats : {
            totalPublishers: 0,
            suitablePublishers: 0,
            messageSent: 0,
            averageViewers: 0,
            todayDiscovered: 0,
            statusCounts: {}
          };
      // Backend'den gelen todayElite ve todaySuitable verilerini kullan
      const todayElite = statsData.todayElite || 0;
      const todaySuitable = statsData.todaySuitable || 0;

      setPublishers(publishersData);
      setStats({ ...statsData, todayElite, todaySuitable });
    } catch (error) {
      console.error('Yayıncı verileri yüklenirken hata:', error);
      // Hata durumunda mock veri göster
      const mockData = generateMockData();
      setPublishers(mockData.publishers);
      setStats(mockData.stats);
    } finally {
      if (isBackground) {
        setBackgroundLoading(false);
      } else {
        setLoading(false);
      }
    }
  };

  // Program durumunu kontrol et
  const checkProgramStatus = async () => {
    try {
      const response = await apiClient.get(`${API_CONFIG.X_SITE_BASE_URL}/publisher-discovery-status.php`);

      if (response.data && response.data.success) {
        setProgramStatus(response.data.data);
        
        // Backend'den gelen mevcut ayarları güncelle
        if (response.data.data.cycleTime) {
          setCycleTime(response.data.data.cycleTime.toString());
        }
        if (response.data.data.messageMode) {
          setMessageMode(response.data.data.messageMode);
        }
        
        // Global durumu güncelle
        updatePublisherDiscoveryStatus({
          running: response.data.data.running,
          error: false
        });
      } else {
        // Mock durum (test amaçlı)
        const mockRunning = false; // Otomatik başlatmayı engellemek için false olarak ayarlandı
        setProgramStatus({
          running: mockRunning,
          startTime: mockRunning ? moment().subtract(Math.floor(Math.random() * 24), 'hours').format() : undefined,
          pid: mockRunning ? Math.floor(Math.random() * 10000) + 1000 : undefined,
          logOutput: mockRunning ? [
            '[INFO] Program başlatıldı',
            '[INFO] TikTok keşif modülü çalışıyor',
            '[INFO] 12 yeni yayıncı bulundu',
            '[INFO] 5 yayıncıya mesaj gönderildi'
          ] : [],
          status: mockRunning ? 'running' : undefined
        });

        // Global durumu güncelle
        updatePublisherDiscoveryStatus({
          running: mockRunning,
          error: false
        });
      }
    } catch (error) {
      console.error('Program durumu kontrol edilirken hata:', error);
      // Hata durumunda global state'i güncelle
      updatePublisherDiscoveryStatus({
        error: true
      });
    }
  };

  // Python programını başlat
  const startProgram = async () => {
    try {
      setLoading(true);
      console.log('Program başlatma isteği gönderiliyor...');
      const response = await apiClient.post(`${API_CONFIG.X_SITE_BASE_URL}/publisher-discovery-control.php`, {
        action: 'start',
        cycleTime: cycleTime,
        messageMode: messageMode,
        headless: false  // Headless modu devre dışı bırak
      });
      console.log('API yanıtı:', response.data);
      
      if (response.data && response.data.success) {
        setProgramStatus({
          ...programStatus,
          running: true,
          startTime: moment().format(),
          logOutput: [...programStatus.logOutput, '[INFO] Program başlatıldı'],
          status: 'running'
        });
        updatePublisherDiscoveryStatus({
          running: true,
          error: false
        });
        message.success('Program başarıyla başlatıldı');
      } else {
        message.error(response.data?.message || 'Program başlatılamadı');
        console.error('Program başlatma hatası:', response.data);
      }
    } catch (error) {
      console.error('Program başlatılırken hata:', error);
      updatePublisherDiscoveryStatus({ error: true });
      message.error('Program başlatılırken bir hata oluştu');
    } finally {
      setLoading(false);
      checkProgramStatus();
    }
  };

  // Python programını durdur
  const stopProgram = async () => {
    try {
      setLoading(true);
      const response = await apiClient.post(`${API_CONFIG.X_SITE_BASE_URL}/publisher-discovery-control.php`, {
        action: 'stop'
      });
      if (response.data && response.data.success) {
        setProgramStatus({
          ...programStatus,
          running: false,
          logOutput: [...programStatus.logOutput, '[INFO] Program durduruldu'],
          status: 'stopped'
        });
        updatePublisherDiscoveryStatus({
          running: false,
          error: false
        });
        message.success('Program başarıyla durduruldu');
      }
    } catch (error) {
      console.error('Program durdurulurken hata:', error);
      updatePublisherDiscoveryStatus({ error: true });
      message.error('Program durdurulurken bir hata oluştu');
    } finally {
      setLoading(false);
      checkProgramStatus();
    }
  };

  // Seçilen kullanıcılara toplu işlem
  const handleBulkQuery = async () => {
    console.log('DEBUG handleBulkQuery:', { currentStatus, selectedPublishers, queryType });
    // Statü filtresi seçiliyse (ve 'all' değilse), toplu sorgulama veya mesaj gönderme başlat
    if (currentStatus && currentStatus !== 'all') {
      try {
        setBulkQueryLoading(true);
        const response = await apiClient.post(`${API_CONFIG.X_SITE_BASE_URL}/publisher-discovery-query.php`, {
          action: queryType,
          status: currentStatus
        });
        if (response.data && response.data.success) {
          if (queryType === 'send_message') {
            message.success(`'${currentStatus}' statüsündeki tüm kullanıcılara mesaj gönderme başlatıldı`);
          } else {
            message.success(`'${currentStatus}' statüsündeki tüm kullanıcılar için sorgulama başlatıldı`);
          }
          setTimeout(() => {
            fetchPublishers();
            setSelectedPublishers([]);
          }, 2000);
        } else {
          message.error(response.data?.message || 'İşlem başlatılırken bir hata oluştu');
        }
      } catch (error) {
        console.error('Toplu işlem hatası:', error);
        message.error('İşlem sırasında bir hata oluştu');
      } finally {
        setBulkQueryLoading(false);
      }
      return; // Statü filtresiyle toplu işlem sonrası fonksiyondan çık
    }
    // Sadece seçili kullanıcılar için (statü filtresi yoksa)
    if (selectedPublishers.length === 0) {
      message.warning('Lütfen en az bir yayıncı seçin');
      return;
    }
    try {
      setBulkQueryLoading(true);
      const usernames = selectedPublishers.map(p => ({ username: p.username }));
      const response = await apiClient.post(`${API_CONFIG.X_SITE_BASE_URL}/publisher-discovery-query.php`, {
        usernames,
        action: queryType
      });
      if (response.data && response.data.success) {
        if (queryType === 'send_message') {
          message.success(`${usernames.length} kullanıcıya mesaj gönderme işlemi başlatıldı`);
        } else {
          message.success(`${usernames.length} yayıncı için ${queryType === 'check_status' ? 'durum kontrolü' : 'mesaj gönderimi'} işlemi başlatıldı`);
        }
        setTimeout(() => {
          fetchPublishers();
          setSelectedPublishers([]);
        }, 2000);
      } else {
        message.error(response.data?.message || 'İşlem başlatılırken bir hata oluştu');
      }
    } catch (error) {
      console.error('Toplu işlem hatası:', error);
      message.error('İşlem sırasında bir hata oluştu');
    } finally {
      setBulkQueryLoading(false);
    }
  };

  // Yayıncıya mesaj gönder
  const sendMessage = async (username: string) => {
    try {
      setSendingMessage(true);
      const response = await apiClient.post(`${API_CONFIG.X_SITE_BASE_URL}/publisher-discovery-send-message.php`, {
        username,
        message: messageText
      });

      if (response.data && response.data.success) {
        message.success(`${username} kullanıcısına mesaj gönderildi`);
        setSendMessageModalVisible(false);
        setMessageText('');

        // Listeyi güncelle - tip dönüşümü düzeltildi
        const updatedPublishers = publishers.map(p =>
          p.username === username ? { ...p, status: 'dm_gonderildi' as const, messageStatus: 'Başarılı' } : p
        );
        setPublishers(updatedPublishers);
      } else {
        message.error(response.data?.message || 'Mesaj gönderilirken bir hata oluştu');
      }
    } catch (error) {
      console.error('Mesaj gönderme hatası:', error);
      message.error('Mesaj gönderilirken bir hata oluştu');
    } finally {
      setSendingMessage(false);
    }
  };

  // Döngü süresi değiştiğinde çalışacak fonksiyon
  const handleCycleTimeChange = (value: string) => {
    setCycleTime(value);

    // Eğer program çalışıyorsa, güncelleme için API'yi çağırabiliriz
    if (programStatus.running) {
      apiClient.post(`${API_CONFIG.X_SITE_BASE_URL}/publisher-discovery-control.php`, {
        action: 'update_cycle_time',
        cycleTime: value
      }).then(response => {
        if (response.data && response.data.success) {
          message.success('Döngü süresi güncellendi');
        }
      }).catch(error => {
        console.error('Döngü süresi güncellenirken hata:', error);
        message.error('Döngü süresi güncellenirken bir hata oluştu');
      });
    }
  };

  // Dinamik status değerlerini çek
  useEffect(() => {
    apiClient.get(`${API_CONFIG.X_SITE_BASE_URL}/publisher-discovery.php?distinct_status=1`)
      .then(res => {
        if (res.data && res.data.success && Array.isArray(res.data.statuses)) {
          setStatusOptions(res.data.statuses);
        } else {
          setStatusOptions([]);
        }
      });
  }, []);

  // Filtreleme işlemi
  const filteredPublishers = publishers.filter(publisher => {
    // Tarih filtreleme
    if (dateRange && dateRange[0] && dateRange[1]) {
      const publisherDate = moment(publisher.createdAt);
      const startDate = dateRange[0].startOf('day');
      const endDate = dateRange[1].endOf('day');
      if (!publisherDate.isBetween(startDate, endDate, undefined, '[]')) {
        return false;
      }
    }

    // Durum filtreleme
    if (currentStatus !== 'all' && publisher.status.toLowerCase() !== currentStatus.toLowerCase()) {
      return false;
    }

    // Arama filtreleme
    if (searchText && !publisher.username.toLowerCase().includes(searchText.toLowerCase())) {
      return false;
    }

    return true;
  });

  // Yayıncı seçme işlemi
  const handleSelectPublisher = (record: Publisher) => {
    const isSelected = selectedPublishers.some(p => p.id === record.id);

    if (isSelected) {
      setSelectedPublishers(selectedPublishers.filter(p => p.id !== record.id));
    } else {
      setSelectedPublishers([...selectedPublishers, record]);
    }
  };

  // Tüm yayıncıları seç/kaldır
  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedPublishers(filteredPublishers);
    } else {
      setSelectedPublishers([]);
    }
  };

  // Mock veri oluştur (test amaçlı)
  const generateMockData = () => {
    const statuses = [
      'uygun', 'uygun_degil', 'dm_gonderildi', 'dm_gonderilemedi', 'beklemede',
      'baska_ajans', 'coklu_hesap', 'bulunamadi', 'desteklenmeyen_bolge', 'diger_nedenler'
    ];

    const mockPublishers = Array(50).fill(null).map((_, index) => {
      const status = statuses[Math.floor(Math.random() * statuses.length)] as Publisher['status'];

      return {
        id: index + 1,
        username: `tiktok_user_${index + 1}`,
        viewerCount: Math.floor(Math.random() * 1000) + 50,
        status,
        createdAt: moment().subtract(Math.floor(Math.random() * 30), 'days').format(),
        messageStatus: status === 'dm_gonderildi' ? 'Başarılı' : status === 'dm_gonderilemedi' ? 'Başarısız' : '-',
        platform: 'TikTok',
        followers: Math.floor(Math.random() * 100000) + 1000,
        category: ['Oyun', 'Müzik', 'Dans', 'Komedi', 'Yaşam Tarzı'][Math.floor(Math.random() * 5)],
        region: ['Türkiye', 'Azerbaycan', 'KKTC', 'Diğer'][Math.floor(Math.random() * 4)],
        lastChecked: moment().subtract(Math.floor(Math.random() * 24), 'hours').format()
      };
    });

    // Durum sayılarını hesapla
    const statusCounts: Record<string, number> = {};
    statuses.forEach(status => {
      statusCounts[status] = mockPublishers.filter(p => p.status === status).length;
    });

    return {
      publishers: mockPublishers,
      stats: {
        totalPublishers: mockPublishers.length,
        suitablePublishers: mockPublishers.filter(p => p.status === 'uygun').length,
        messageSent: mockPublishers.filter(p => p.status === 'dm_gonderildi').length,
        averageViewers: Math.floor(mockPublishers.reduce((sum, p) => sum + p.viewerCount, 0) / mockPublishers.length),
        todayDiscovered: mockPublishers.filter(p => moment(p.createdAt).isSame(moment(), 'day')).length,
        statusCounts
      }
    };
  };

  // Status Türkçeleştirme ve Baş Harf Büyütme fonksiyonu
  const statusLabel = (status: string): string => {
    if (!status) return status;
    const map: Record<string, string> = {
      uygun: 'Uygun',
      'uygun elite': 'Uygun Elite',
      uygun_degil: 'Uygun Değil',
      dm_gonderildi: 'DM Gönderildi',
      dm_gonderilemedi: 'DM Gönderilemedi',
      beklemede: 'Beklemede',
      'başka bir ajansta': 'Başka bir ajansta',
      baska_ajans: 'Başka bir ajansta',
      coklu_hesap: 'Çoklu hesap riski',
      'çoklu hesap riski': 'Çoklu hesap riski',
      bulunamadi: 'Bulunamadı',
      desteklenmeyen_bolge: 'Desteklenmeyen bölge',
      'desteklenmeyen bölge': 'Desteklenmeyen bölge',
      diger_nedenler: 'Diğer nedenler',
      'diğer nedenler': 'Diğer nedenler',
      manuel_eklendi: 'Manuel eklendi',
      'manuel eklendi': 'Manuel eklendi',
      'takipçi sınırının üstünde': 'Takipçi sınırının üstünde'
    };
    const mappedStatus = map[status.toLowerCase()];
    if (mappedStatus) {
      return mappedStatus;
    }
    // Eğer map içinde yoksa, _ ile ayrılmış kelimelerin baş harflerini büyüt
    return status
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  // Tablo sütunları
  const columns = [
    {
      title: <Checkbox
        onChange={(e) => handleSelectAll(e.target.checked)}
        checked={selectedPublishers.length > 0 && selectedPublishers.length === filteredPublishers.length}
        indeterminate={selectedPublishers.length > 0 && selectedPublishers.length < filteredPublishers.length}
      />,
      dataIndex: 'selected',
      key: 'selected',
      width: 50,
      render: (_: any, record: Publisher) => (
        <Checkbox
          checked={selectedPublishers.some(p => p.id === record.id)}
          onChange={() => handleSelectPublisher(record)}
        />
      )
    },
    {
      title: 'Kullanıcı Adı',
      dataIndex: 'username',
      key: 'username',
      width: 150,
      render: (text: string) => (
        <a href={`https://tiktok.com/@${text}`} target="_blank" rel="noopener noreferrer">
          {text}
        </a>
      )
    },
    {
      title: 'İzleyici Sayısı',
      dataIndex: 'viewerCount',
      key: 'viewerCount',
      width: 120,
      sorter: (a: Publisher, b: Publisher) => a.viewerCount - b.viewerCount,
      render: (count: number) => (
        <span>
          <EyeOutlined style={{ marginRight: 8 }} />
          {count}
        </span>
      )
    },
    {
      title: 'Durum',
      dataIndex: 'status',
      key: 'status',
      width: 130,
      render: (status: string) => {
        if (!status || status === '-') return <span>-</span>;
        const statusLower = status.toLowerCase();
        if (statusLower === 'uygun elite') {
          return <Tag style={{ background: '#fff7e6', color: '#ad8b00', borderColor: '#ad8b00', fontWeight: 700, fontSize: 14 }}>Uygun Elite</Tag>;
        }
        if (statusLower === 'uygun') {
          return <Tag color="#52c41a" style={{ background: '#f6ffed', color: '#52c41a', borderColor: '#52c41a', fontWeight: 600 }}>Uygun</Tag>;
        }
        if (statusLower === 'başka bir ajansta') {
          return <Tag color="#ff4d4f" style={{ background: '#fff1f0', color: '#ff4d4f', borderColor: '#ff4d4f', fontWeight: 600 }}>Başka bir ajansta</Tag>;
        }
        if (statusLower === 'diğer nedenler') {
          return <Tag color="#faad14" style={{ background: '#fffbe6', color: '#faad14', borderColor: '#faad14', fontWeight: 600 }}>Diğer nedenler</Tag>;
        }
        if (statusLower === 'desteklenmeyen bölge') {
          return <Tag color="#13c2c2" style={{ background: '#e6fffb', color: '#13c2c2', borderColor: '#13c2c2', fontWeight: 600 }}>Desteklenmeyen bölge</Tag>;
        }
        if (statusLower === 'çoklu hesap riski') {
          return <Tag color="#2f54eb" style={{ background: '#f0f5ff', color: '#2f54eb', borderColor: '#2f54eb', fontWeight: 600 }}>Çoklu hesap riski</Tag>;
        }
        if (statusLower === 'manuel eklendi') {
          return <Tag color="#722ed1" style={{ background: '#f9f0ff', color: '#722ed1', borderColor: '#722ed1', fontWeight: 600 }}>Manuel eklendi</Tag>;
        }
        if (statusLower === 'bekleniyor') {
          return <Tag color="#595959" style={{ background: '#fafafa', color: '#595959', borderColor: '#595959', fontWeight: 600 }}>Bekleniyor</Tag>;
        }
        if (statusLower === 'bulunamadı') {
          return <Tag color="#ff7875" style={{ background: '#fff1f0', color: '#ff7875', borderColor: '#ff7875', fontWeight: 600 }}>Bulunamadı</Tag>;
        }
        if (statusLower === 'takipçi sınırının üstünde') {
          return <Tag color="#1890ff" style={{ background: '#e6f7ff', color: '#1890ff', borderColor: '#1890ff', fontWeight: 600 }}>Takipçi sınırının üstünde</Tag>;
        }
        return <Tag color={statusColors[statusLower] || 'default'}>{statusLabel(status)}</Tag>;
      }
    },
    {
      title: 'Mesaj Durumu',
      dataIndex: 'messageStatus',
      key: 'messageStatus',
      width: 120,
      render: (msg: string) => (
        msg && msg !== '-' ? statusLabel(msg) : <span>-</span>
      )
    },
    {
      title: 'Bulunma Tarihi',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (date: string) => formatTurkishDate(date)
    },
    {
      title: 'Platform',
      dataIndex: 'platform',
      key: 'platform',
      width: 100
    },
    {
      title: <div style={{ textAlign: 'center', width: '100%' }}>İşlemler</div>,
      key: 'actions',
      width: 200,
      align: 'center' as const,
      render: (_: any, record: Publisher) => (
        <Space size="small">
          <Button
            type="link"
            icon={<MessageOutlined />}
            size="small"
            disabled={record.status === 'dm_gonderildi'}
            onClick={() => {
              setMessageText(`Merhaba @${record.username}, Tuber Ajans olarak TikTok yayınlarınızı takip ediyoruz. Sizinle çalışmak isteriz.`);
              setSendMessageModalVisible(true);
            }}
          >
            Mesaj Gönder
          </Button>
          <Button
            type="link"
            icon={<EyeOutlined />}
            size="small"
            onClick={() => window.open(`https://tiktok.com/@${record.username}`, '_blank')}
          >
            Profili Gör
          </Button>
        </Space>
      )
    }
  ];

  // Elite kullanıcıları getir - Mevcut API ile
  const fetchElitePublishers = async () => {
    setElitePublishersLoading(true);
    try {
      console.log('Elite kullanıcılar için API çağrısı yapılıyor...');

      // Mevcut çalışan API endpoint'ini kullanalım
      const response = await apiClient.get(`${API_CONFIG.X_SITE_BASE_URL}/publisher-discovery.php`, {
        params: {
          status: 'Uygun Elite',
          today_only: true
        }
      });

      // API yanıtını detaylı logla
      console.log('API yanıtı:', response.data);

      if (response.data && response.data.success) {
        // API yanıt yapısını kontrol et ve doğru veri yapısını seç
        let eliteUsers = [];

        if (Array.isArray(response.data.data)) {
          // Yanıt doğrudan dizi ise
          eliteUsers = response.data.data;
          console.log('API yanıtı doğrudan dizi içeriyor, kayıt sayısı:', eliteUsers.length);
        }
        else if (response.data.data && Array.isArray(response.data.data.publishers)) {
          // Yanıt publishers dizisi içeriyorsa
          eliteUsers = response.data.data.publishers;
          console.log('API yanıtı publishers dizisi içeriyor, kayıt sayısı:', eliteUsers.length);
        }

        // Veri boş değilse direkt kullan
        if (eliteUsers && eliteUsers.length > 0) {
          console.log('Elite kullanıcılar bulundu:', eliteUsers.length);
          setElitePublishers(eliteUsers);
        } else {
          console.log('API yanıtı boş veya beklenen formatta değil');
          message.info('Bugün eklenen Uygun Elite kullanıcı bulunamadı');
          setElitePublishers([]);
        }
      } else {
        console.log('API yanıtı başarısız:', response.data?.message);
        // Veri olmadığını belirt
        setElitePublishers([]);
        message.info('Bugün eklenen Uygun Elite kullanıcı bulunamadı');
      }
    } catch (error) {
      console.error('Elite kullanıcıları yüklenirken hata:', error);
      message.error('Elite kullanıcıları yüklenirken bir hata oluştu');
      setElitePublishers([]);
    } finally {
      setElitePublishersLoading(false);
    }
  };

  // Modal açıldığında elite kullanıcıları getir
  const handleEliteModalOpen = () => {
    fetchElitePublishers();
    setEliteModalVisible(true);
  };

  // Genel sekme içeriği
  function renderGeneralTabContent() {
    // Backend'den gelen doğru verileri kullan
    const todayElite = stats.todayElite || 0;
    const todaySuitable = stats.todaySuitable || 0;
    return (
      <div style={{ width: '100%' }}>
        <Card className="mb-4" style={{ width: '100%' }}>
          <div className="flex justify-between items-center mb-4 flex-wrap">
            <div className="w-full md:w-auto mb-4 md:mb-0">
              <Title level={4} className="mb-0">TikTok Canlı Yayın Veri Toplayıcı</Title>
              <Text type="secondary">
                Bu program, TikTok'taki canlı yayıncıları otomatik olarak keşfeder ve ajans uygunluğunu değerlendirir
              </Text>
            </div>
            {/* Döngü Süresi ve Butonlar aynı satırda */}
            <div style={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: 8 }}>
              {/* Döngü Süresi */}
              <div style={{ display: 'flex', alignItems: 'center', marginRight: 12 }}>
                <ClockCircleOutlined style={{ color: '#13c2c2', marginRight: 6, fontSize: 18 }} />
                <span style={{ fontWeight: 500, marginRight: 6 }}>Döngü Süresi</span>
                <Select
                  value={cycleTime}
                  onChange={handleCycleTimeChange}
                  style={{ width: 110, minWidth: 90 }}
                  className="cycle-select"
                  size="middle"
                >
                  <Option value="1">1 Dakika</Option>
                  <Option value="3">3 Dakika</Option>
                  <Option value="5">5 Dakika</Option>
                  <Option value="10">10 Dakika</Option>
                  <Option value="15">15 Dakika</Option>
                  <Option value="30">30 Dakika</Option>
                  <Option value="60">1 Saat</Option>
                  <Option value="180">3 Saat</Option>
                  <Option value="360">6 Saat</Option>
                  <Option value="720">12 Saat</Option>
                </Select>
              </div>
              {/* Mesaj Gönderme Modu Seçici */}
              <div style={{ display: 'flex', alignItems: 'center', marginRight: 12 }}>
                <span style={{ fontWeight: 500, marginRight: 6 }}>Mesaj Gönderme:</span>
                <Select
                  value={messageMode}
                  onChange={setMessageMode}
                  style={{ width: 220, minWidth: 180 }}
                  size="middle"
                >
                  <Option value="none">Hiç mesaj gönderme</Option>
                  <Option value="suitable">Sadece uygun olanlara mesaj gönder</Option>
                  <Option value="elite">Sadece uygun elite olanlara mesaj gönder</Option>
                  <Option value="both">Hem uygun hem uygun elite olanlara mesaj gönder</Option>
                </Select>
              </div>
              {/* Otomasyonu Başlat ve Durdur butonları */}
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={startProgram}
                loading={loading && programStatus.status !== 'running'}
                disabled={programStatus.status === 'running'}
                style={{ backgroundColor: '#52c41a', color: 'white', borderColor: '#52c41a', marginBottom: '8px' }}
              >
                Otomasyonu Başlat
              </Button>
              <Button
                type="primary"
                danger
                icon={<PauseCircleOutlined />}
                onClick={stopProgram}
                loading={loading && programStatus.status === 'running'}
                disabled={programStatus.status !== 'running'}
                style={{ marginBottom: '8px' }}
              >
                Otomasyonu Durdur
              </Button>
            </div>
          </div>

          {programStatus.running && programStatus.startTime && (
            <Alert
              type="info"
              message={
                <span>
                  Program {moment(programStatus.startTime).fromNow()} başlatıldı.
                  {programStatus.pid && ` (PID: ${programStatus.pid})`}
                </span>
              }
              className="mb-4"
            />
          )}

          <Row gutter={[16, 16]} className="stats-row">
            {/* Bugün Bulunan Uygun Elite */}
            <Col xs={24} sm={12} md={8} lg={6} xl={4}>
              <Card bordered={false} className="stat-card" style={{ background: 'linear-gradient(135deg, #fffbe6 0%, #ffe58f 100%)', border: '1px solid #FFD700' }} onClick={handleEliteModalOpen}>
                <Statistic
                  title={<span style={{ fontWeight: 500, fontSize: 16 }}><span style={{ color: '#FFD700', fontSize: 18, verticalAlign: 'middle', marginRight: 4 }}>★</span>Bugün Bulunan Uygun Elite</span>}
                  value={todayElite}
                  valueStyle={{ color: '#FFD700', fontWeight: 700 }}
                />
              </Card>
            </Col>
            {/* Bugün Bulunan Uygun */}
            <Col xs={24} sm={12} md={8} lg={6} xl={4}>
              <Card bordered={false} className="stat-card" style={{ background: 'linear-gradient(135deg, #f6ffed 0%, #b7eb8f 100%)', border: '1px solid #52c41a' }}>
                <Statistic
                  title={<span style={{ fontWeight: 500, fontSize: 16 }}>Bugün Bulunan Uygun</span>}
                  value={todaySuitable}
                  valueStyle={{ color: '#52c41a', fontWeight: 700 }}
                />
              </Card>
            </Col>
            {/* Toplam Kullanıcı */}
            <Col xs={24} sm={12} md={8} lg={6} xl={4}>
              <Card bordered={false} className="stat-card">
                <Statistic
                  title={<span style={{ fontWeight: 500, fontSize: 16 }}>Toplam Kullanıcı</span>}
                  value={stats.totalPublishers}
                  prefix={<UserOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            {/* Uygun Kullanıcılar */}
            <Col xs={24} sm={12} md={8} lg={6} xl={4}>
              <Card bordered={false} className="stat-card">
                <Statistic
                  title={<span style={{ fontWeight: 500, fontSize: 16 }}>Uygun Kullanıcılar</span>}
                  value={stats.suitablePublishers}
                  prefix={<CheckCircleOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            {/* Gönderilen Mesajlar */}
            <Col xs={24} sm={12} md={8} lg={6} xl={4}>
              <Card bordered={false} className="stat-card">
                <Statistic
                  title={<span style={{ fontWeight: 500, fontSize: 16 }}>Gönderilen Mesajlar</span>}
                  value={stats.messageSent}
                  prefix={<MessageOutlined />}
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
            {/* Ortalama İzleyici */}
            <Col xs={24} sm={12} md={8} lg={6} xl={4}>
              <Card bordered={false} className="stat-card">
                <Statistic
                  title={<span style={{ fontWeight: 500, fontSize: 16 }}>Ortalama İzleyici</span>}
                  value={stats.averageViewers}
                  prefix={<EyeOutlined />}
                  valueStyle={{ color: '#fa8c16' }}
                />
              </Card>
            </Col>
            {/* Bugün Keşfedilen */}
            <Col xs={24} sm={12} md={8} lg={6} xl={4}>
              <Card bordered={false} className="stat-card">
                <Statistic
                  title={<span style={{ fontWeight: 500, fontSize: 16 }}>Bugün Keşfedilen</span>}
                  value={stats.todayDiscovered}
                  prefix={<UsergroupAddOutlined />}
                  valueStyle={{ color: '#eb2f96' }}
                />
              </Card>
            </Col>
          </Row>
        </Card>

        <Card className="mb-4" style={{ background: '#fff', border: '1px solid #f0f0f0', boxShadow: '0 2px 8px rgba(0,0,0,0.03)' }}>
          <div className="p-4 rounded">
            <div className="text-lg font-semibold mb-2">Günlük İşlem Özeti</div>
            <div className="overflow-x-auto">
              <Table
                columns={[
                  {
                    title: 'Tarih',
                    dataIndex: 'date',
                    key: 'date',
                    render: (date: string) => date ? moment(date).format('DD.MM.YYYY') : '-'
                  },
                  { title: 'Kullanıcı Sayısı', dataIndex: 'userCount', key: 'userCount' },
                  { title: 'Uygun Kullanıcılar', dataIndex: 'suitableUsers', key: 'suitableUsers' },
                  { title: 'Gönderilen Mesajlar', dataIndex: 'sentMessages', key: 'sentMessages' },
                  { title: 'Ortalama İzleyici', dataIndex: 'avgViewer', key: 'avgViewer' },
                  { title: 'En Yüksek İzleyici Saat Aralığı', dataIndex: 'peakHourRange', key: 'peakHourRange' }
                ]}
                dataSource={dailySummary.map((row, i) => ({ ...row, key: i }))}
                pagination={false}
                size="small"
                scroll={{ x: 'max-content' }}
                className="responsive-daily-table"
              />
            </div>
          </div>
        </Card>
      </div>
    );
  }

  // İstatistikler sekme içeriği
  const renderStatsTabContent = (): React.ReactNode => {
    if (!Array.isArray(statusOptions) || statusOptions.length === 0) {
      return <Alert message="İstatistik verisi bulunamadı." type="info" showIcon />;
    }
    // Sıralama önceliği: Uygun Elite, Uygun, Diğer nedenler, Desteklenmeyen bölge, kalanlar alfabetik
    const customStatusOrder = [
      'Uygun Elite',
      'Uygun',
      'Diğer nedenler',
      'Bekleniyor',
      'Desteklenmeyen bölge'
    ];
    const sortedStatusOptions = [
      ...customStatusOrder.filter((key: string) => statusOptions.some((s: string) => s.toLowerCase() === key.toLowerCase())),
      ...statusOptions
        .filter((s: string) => !customStatusOrder.map((c: string) => c.toLowerCase()).includes(s.toLowerCase()))
        .sort((a: string, b: string) => a.localeCompare(b, 'tr'))
    ];
    // Debug amaçlı loglar
    console.log('statusOptions:', statusOptions);
    console.log('sortedStatusOptions:', sortedStatusOptions);
    console.log('stats:', stats);
    return (
      <div style={{ width: '100%' }}>
        <Card style={{ width: '100%', background: 'transparent', boxShadow: 'none', border: 'none' }}>
          <Title level={4} className="mb-4">İstatistikler</Title>
          <Row gutter={[16, 16]} className="stats-cards-row">
            {sortedStatusOptions.map((statusKey, idx) => {
              const displayStatus = statusLabel(statusKey);
              // Güvenli erişim
              const statusCount = (stats && stats.statusCounts && typeof stats.statusCounts[statusKey.toLowerCase()] !== 'undefined')
                ? stats.statusCounts[statusKey.toLowerCase()]
                : 0;
              // Özel stiller ve başlıklar
              let cardStyle: React.CSSProperties = {
                background: idx % 2 === 0 ? '#f5f5f5' : '#ffffff',
                borderRadius: 8,
                boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
                height: '100%'
              };
              let tagColor = statusColors[statusKey.toLowerCase()] || 'blue';
              let customTitle: React.ReactNode = displayStatus;
              if (statusKey.toLowerCase() === 'uygun elite') {
                // Altın renkli yıldız ve altın arka plan
                customTitle = <span><span style={{ color: '#FFD700', fontSize: 20, verticalAlign: 'middle', marginRight: 4 }}>★</span>Uygun Elite</span>;
                cardStyle = {
                  background: 'linear-gradient(135deg, #fffbe6 0%, #ffe58f 100%)',
                  borderRadius: 8,
                  boxShadow: '0 2px 8px rgba(255, 215, 0, 0.10)',
                  height: '100%',
                  border: '1px solid #FFD700'
                };
                tagColor = '#FFD700';
              } else if (statusKey.toLowerCase() === 'uygun') {
                // Zarif yeşil arka plan
                cardStyle = {
                  background: 'linear-gradient(135deg, #f6ffed 0%, #b7eb8f 100%)',
                  borderRadius: 8,
                  boxShadow: '0 2px 8px rgba(82, 196, 26, 0.10)',
                  height: '100%',
                  border: '1px solid #52c41a'
                };
                tagColor = '#52c41a';
              }
              return (
                <Col xs={24} sm={12} md={8} lg={6} key={statusKey}>
                  <Card
                    bordered={false}
                    style={cardStyle}
                    className="stat-status-card"
                    bodyStyle={{ padding: '16px', height: '100%' }}
                  >
                    <div>
                      <div style={{ marginBottom: 8 }}>
                        <Tag color={tagColor} style={statusKey.toLowerCase() === 'uygun elite' ? { background: '#fffbe6', color: '#FFD700', borderColor: '#FFD700', fontWeight: 600 } : statusKey.toLowerCase() === 'uygun' ? { background: '#f6ffed', color: '#52c41a', borderColor: '#52c41a', fontWeight: 600 } : {}}>
                          {customTitle}
                        </Tag>
                      </div>
                      <Statistic
                        value={statusCount}
                        valueStyle={{ color: tagColor, fontWeight: statusKey.toLowerCase() === 'uygun elite' ? 700 : undefined }}
                      />
                      <div style={{ marginTop: 16 }}>
                        <Button
                          size="small"
                          type="primary"
                          style={{ fontSize: '11px', backgroundColor: '#1890ff', color: 'white' }}
                          onClick={() => {
                            setCurrentStatus(statusKey);
                            setActiveTab('veritabani');
                          }}
                        >
                          Detay Gör
                        </Button>
                      </div>
                    </div>
                  </Card>
                </Col>
              );
            })}
          </Row>
          {/* Son İzleyici Zirveleri Kartı - İstatistikler sekmesinin altına geniş şekilde */}
          <div style={{ width: '100%', marginTop: 32 }}>
            <Card bordered={false} className="stat-card son-izleyici-zirveleri-card"
              style={{
                background: 'linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)',
                border: '1px solid #1890ff',
                width: '100%',
                minHeight: 120,
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'flex-start',
                padding: 0
              }}
            >
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8, padding: '8px 12px 0 12px' }}>
                <span style={{ fontWeight: 500, fontSize: 16 }}>Son İzleyici Zirveleri</span>
                <div>
                  <Button.Group size="small">
                    <Button type={peakStatsRange === '7' ? 'primary' : 'default'}
                      style={peakStatsRange === '7' ? { background: '#fff', color: '#1890ff', borderColor: '#1890ff', fontWeight: 600 } : {}}>7 Gün</Button>
                    <Button type={peakStatsRange === '30' ? 'primary' : 'default'}
                      style={peakStatsRange === '30' ? { background: '#fff', color: '#1890ff', borderColor: '#1890ff', fontWeight: 600 } : {}}>30 Gün</Button>
                    <Button type={peakStatsRange === '60' ? 'primary' : 'default'}
                      style={peakStatsRange === '60' ? { background: '#fff', color: '#1890ff', borderColor: '#1890ff', fontWeight: 600 } : {}}>60 Gün</Button>
                    <Button type={peakStatsRange === '90' ? 'primary' : 'default'}
                      style={peakStatsRange === '90' ? { background: '#fff', color: '#1890ff', borderColor: '#1890ff', fontWeight: 600 } : {}}>90 Gün</Button>
                  </Button.Group>
                </div>
              </div>
              {peakStats[peakStatsRange] ? (
                <div style={{ padding: '0 12px 12px 12px' }}>
                  <div style={{ fontWeight: 700, fontSize: 18, color: '#1890ff', marginBottom: 4 }}>
                    {peakStats[peakStatsRange].range}, {peakStats[peakStatsRange].weekday}
                  </div>
                  <div style={{ fontSize: 14, marginBottom: 6 }}>
                    <b>Haftanın En Verimli Günü:</b> {peakStats[peakStatsRange].mostEfficientDay?.weekday} (Ortalama: {peakStats[peakStatsRange].mostEfficientDay?.avg})
                  </div>
                  <div style={{ fontSize: 14, marginBottom: 4 }}><b>Haftanın 7 Günü İçin Zirve Saatler:</b></div>
                  <div className="zirve-kutucuklar-wrapper" style={{ display: 'flex', flexWrap: 'nowrap', gap: 10, flexDirection: 'row', width: '100%', overflowX: 'auto', justifyContent: 'flex-start', paddingBottom: 4 }}>
                    {peakStats[peakStatsRange].weeklyPeaks?.map((item: any) => (
                      <div key={item.weekday} className="zirve-kutucuk" style={{ background: '#fff', border: '1px solid #e6f7ff', borderRadius: 8, padding: '8px 12px', minWidth: 120, maxWidth: 160, textAlign: 'center', boxShadow: '0 1px 4px rgba(24,144,255,0.06)', flex: '1 1 120px', fontSize: 13 }}>
                        <div style={{ fontWeight: 600, color: '#1890ff', marginBottom: 2 }}>{item.weekday}</div>
                        <div style={{ fontSize: 13 }}>{item.range || '-'}</div>
                        <div style={{ fontSize: 12, color: '#888', marginTop: 2 }}>Ortalama: {item.avg}</div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div style={{ color: '#999', padding: 16 }}>Veri yok</div>
              )}
            </Card>
          </div>
        </Card>
      </div>
    );
  };

  // Veritabanı sekme içeriği
  const renderDatabaseTabContent = () => {
    // Status filtre sıralaması
    const customStatusOrder = [
      'Uygun Elite',
      'Uygun',
      'Diğer nedenler',
      'Bekleniyor',
      'Desteklenmeyen bölge'
    ];
    const sortedStatusOptions = [
      ...customStatusOrder.filter((key: string) => statusOptions.some((s: string) => s.toLowerCase() === key.toLowerCase())),
      ...statusOptions
        .filter((s: string) => !customStatusOrder.map((c: string) => c.toLowerCase()).includes(s.toLowerCase()))
        .sort((a: string, b: string) => a.localeCompare(b, 'tr'))
    ];
    return (
      <div style={{ width: '100%' }}>
        <div className="mb-4">
          {/* Filtre ve aksiyon çubuğu için responsive flex düzeni */}
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8, alignItems: 'center', marginBottom: 8 }}>
            {/* Kompakt Toplam Kullanıcı Gösterimi */}
            <Statistic
              title={<Text style={{ fontSize: '14px', color: 'rgba(0, 0, 0, 0.88)', marginRight: '6px' }}>Toplam Kullanıcı:</Text>}
              value={stats.totalPublishers ? stats.totalPublishers.toLocaleString('tr-TR') : '0'}
              valueStyle={{ fontSize: '14px', fontWeight: 'bold', color: '#1677ff' }}
              style={{ marginRight: 16, minWidth: 120 }}
            />

            <Input
              placeholder="Kullanıcı adı ara..."
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={e => setSearchText(e.target.value)}
              className="search-input"
              style={{ width: '100%', maxWidth: 250, minWidth: 120, marginBottom: 0 }}
            />

            <Select
              style={{ width: '100%', maxWidth: 250, minWidth: 120, marginBottom: 0 }}
              value={currentStatus}
              onChange={handleStatusChange}
              className="status-select"
              dropdownStyle={{ minWidth: 250 }}
            >
              <Option value="all" title="Tümü">Tümü</Option>
              {sortedStatusOptions.map((statusKey: string) => (
                <Option key={statusKey} value={statusKey} title={statusLabel(statusKey)}>
                  {statusLabel(statusKey)}
                </Option>
              ))}
            </Select>

            {/* Butonlar grubu */}
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8, alignItems: 'center' }}>
              <Button
                type="primary"
                icon={<SyncOutlined />}
                onClick={() => fetchPublishers()}
                loading={loading}
                style={{ backgroundColor: '#1890ff', color: 'white', borderColor: '#1890ff', marginBottom: 0 }}
              >
                Yenile
              </Button>

              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={() => {
                  // Status filtresi seçiliyse, manuel seçim gerektirmez
                  if (currentStatus && currentStatus !== 'all') {
                    setQueryType('check_status');
                    handleBulkQuery();
                    return;
                  }

                  // Status filtresi seçili değilse, manuel seçim gerekir
                  if (selectedPublishers.length === 0) {
                    message.warning('Lütfen bir status seçin veya en az bir yayıncı seçin');
                    return;
                  }
                  setQueryType('check_status');
                  handleBulkQuery();
                }}
                loading={bulkQueryLoading && queryType === 'check_status'}
                style={{ backgroundColor: '#1890ff', color: 'white', borderColor: '#1890ff', marginBottom: 0 }}
              >
                Tekrar Sorgula {currentStatus && currentStatus !== 'all' ? `(${currentStatus})` : selectedPublishers.length > 0 ? `(${selectedPublishers.length})` : ''}
              </Button>
              <Button
                type="primary"
                icon={<SendOutlined />}
                onClick={() => {
                  // Status filtresi seçiliyse, manuel seçim gerektirmez
                  if (currentStatus && currentStatus !== 'all') {
                    setQueryType('send_message');
                    handleBulkQuery();
                    return;
                  }

                  // Status filtresi seçili değilse, manuel seçim gerekir
                  if (selectedPublishers.length === 0) {
                    message.warning('Lütfen bir status seçin veya en az bir yayıncı seçin');
                    return;
                  }
                  setQueryType('send_message');
                  handleBulkQuery();
                }}
                loading={bulkQueryLoading && queryType === 'send_message'}
                style={{ backgroundColor: '#1890ff', color: 'white', borderColor: '#1890ff', marginBottom: 0 }}
              >
                Mesaj Gönder {currentStatus && currentStatus !== 'all' ? `(${currentStatus})` : selectedPublishers.length > 0 ? `(${selectedPublishers.length})` : ''}
              </Button>
              <Button
                type="primary"
                danger
                icon={<PauseCircleOutlined />}
                onClick={async () => {
                  try {
                    const response = await apiClient.post(`${API_CONFIG.X_SITE_BASE_URL}/publisher-discovery-control.php`, {
                      action: 'stop'
                    });
                    if (response.data && response.data.success) {
                      message.success('Otomasyon durduruldu');
                      // Program durumunu güncelle
                      setProgramStatus({
                        ...programStatus,
                        running: false,
                        status: 'stopped'
                      });
                      updatePublisherDiscoveryStatus({
                        running: false,
                        error: false
                      });
                    } else {
                      message.error('Durdurma işlemi başarısız');
                    }
                  } catch (error) {
                    console.error('Durdurma hatası:', error);
                    message.error('Durdurma sırasında bir hata oluştu');
                  }
                }}
                style={{ backgroundColor: '#ff4d4f', color: 'white', borderColor: '#ff4d4f', marginBottom: 0 }}
              >
                Durdur
              </Button>
            </div>
          </div>
        </div>
        {/* Seçili yayıncılar bildirimi */}
        {selectedPublishers.length > 0 && (
          <div className="mb-4">
            <Alert
              message={`${selectedPublishers.length} yayıncı seçildi`}
              type="info"
              showIcon
            />
          </div>
        )}
        <Table
          dataSource={filteredPublishers || []}
          columns={columns}
          rowKey="id"
          loading={backgroundLoading}
          pagination={{
            pageSize: pageSize,
            showSizeChanger: true,
            pageSizeOptions: ['10', '20', '50', '100'],
            onShowSizeChange: (current, size) => setPageSize(size),
          }}
          style={{ width: '100%' }}
          scroll={{ x: 'max-content' }}
          className="responsive-table"
        />
        <div className="flex justify-between mt-4">
          <Button type="default">Önceki Sayfa</Button>
          <Button type="default">Sonraki Sayfa</Button>
        </div>
      </div>
    );
  };

  // Status değişiminde önce state'i güncelle, sonra yeni değerle fetchPublishers çağır
  const handleStatusChange = (value: string) => {
    const newStatus = value;
    setCurrentStatus(newStatus);
    fetchPublishers(false, newStatus === 'all' ? undefined : newStatus);
  };

  useEffect(() => {
    checkProgramStatus();
    fetchPublishers();
  }, []);

  useEffect(() => {
    apiClient.get(`${API_CONFIG.X_SITE_BASE_URL}/publisher-discovery-daily-summary.php`)
      .then(res => {
        if (res.data && res.data.success) {
          setDailySummary(res.data.data);
          setPeakStats(res.data.peakStats || {});
        }
      });
  }, []);

  return (
    <div
      className="p-6 px-2 md:px-6"
      style={{
        maxWidth: '100%',
        overflowX: 'hidden',
        paddingTop: 12,
        marginTop: 0,
        minHeight: '100vh',
        boxSizing: 'border-box',
      }}
    >
      <Card
        style={{
          width: '100%',
          margin: 0,
          padding: 0,
          boxShadow: 'none',
          border: 'none',
          background: 'transparent',
        }}
        bodyStyle={{ padding: 0 }}
      >
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          tabBarStyle={{ marginBottom: 0 }}
          style={{ marginBottom: 0 }}
        >
          <TabPane tab="Genel" key="genel" />
          <TabPane tab="İstatistikler" key="istatistikler" />
          <TabPane tab="Veritabanı" key="veritabani" />
        </Tabs>
        {activeTab === 'genel' && <>{renderGeneralTabContent()}</>}
        {activeTab === 'istatistikler' && renderStatsTabContent()}
        {activeTab === 'veritabani' && renderDatabaseTabContent()}
      </Card>
      {/* Modal: Bugün Bulunan Uygun Elite Kullanıcılar */}
      <Modal
        open={eliteModalVisible}
        onCancel={() => setEliteModalVisible(false)}
        title="Bugün Bulunan Uygun Elite Kullanıcılar"
        footer={null}
        width={600}
      >
        <Table
          loading={elitePublishersLoading}
          dataSource={elitePublishers}
          columns={[
            {
              title: 'Kullanıcı Adı',
              dataIndex: 'username',
              key: 'username',
              sorter: (a, b) => a.username.localeCompare(b.username),
              render: (text) => <a href={`https://tiktok.com/@${text}`} target="_blank" rel="noopener noreferrer">{text}</a>
            },
            {
              title: 'İzleyici',
              dataIndex: 'viewerCount',
              key: 'viewerCount',
              sorter: (a, b) => a.viewerCount - b.viewerCount,
              defaultSortOrder: 'descend',
              render: (count) => count || 0
            },
            {
              title: 'Profil',
              dataIndex: 'username',
              key: 'profile',
              render: (text) => <a href={`https://tiktok.com/@${text}`} target="_blank" rel="noopener noreferrer">TikTok</a>
            }
          ]}
          rowKey="id"
          pagination={{
            pageSize: elitePageSize,
            showSizeChanger: true,
            pageSizeOptions: ['10', '20', '50', '100'],
            onShowSizeChange: (current, size) => setElitePageSize(size),
            showTotal: (total, range) => `${range[0]}-${range[1]} / ${total} kayıt`
          }}
          size="small"
        />
      </Modal>
      <style>{`
        @media (max-width: 768px) {
          .ant-row {
            flex-direction: column !important;
          }
          .stat-card {
            min-width: 90vw !important;
            margin-bottom: 16px !important;
          }
          .ant-card, .ant-table {
            width: 100% !important;
            min-width: 0 !important;
          }
          .ant-table-content {
            overflow-x: auto !important;
          }
          .ant-tabs-nav {
            flex-wrap: wrap !important;
          }
          .ant-statistic-title {
            font-size: 12px !important;
          }
          .ant-statistic-content {
            font-size: 20px !important;
          }
          .ant-space {
            flex-wrap: wrap !important;
            gap: 8px !important;
            margin-bottom: 10px !important;
          }
          .ant-space-item {
            margin-right: 0 !important;
            margin-bottom: 8px !important;
          }
          .ant-btn {
            padding: 4px 8px !important;
            height: auto !important;
            font-size: 12px !important;
          }
          .ant-input {
            font-size: 12px !important;
          }
          .ant-select {
            width: 100% !important;
            max-width: none !important;
          }
          .ant-picker {
            width: 100% !important;
          }
          .ant-card-body {
            padding: 12px 8px !important;
          }
          .ant-table-thead > tr > th,
          .ant-table-tbody > tr > td {
            padding: 8px 4px !important;
            font-size: 12px !important;
          }
          .ant-table-column-title {
            font-size: 12px !important;
          }
          .ant-table-cell .ant-btn {
            padding: 2px 6px !important;
            font-size: 11px !important;
          }
          .son-izleyici-zirveleri-card {
            padding: 0 !important;
          }
          .zirve-kutucuklar-wrapper {
            flex-wrap: nowrap !important;
            overflow-x: auto !important;
            padding-bottom: 4px !important;
          }
          .zirve-kutucuk {
            min-width: 140px !important;
            max-width: 180px !important;
            font-size: 12px !important;
            padding: 8px 8px !important;
          }
        }

        @media (max-width: 576px) {
          .p-6 {
            padding: 8px !important;
          }
          .ant-card-body {
            padding: 8px 4px !important;
          }
          .ant-tabs-tab {
            padding: 8px 4px !important;
            margin: 0 4px !important;
            font-size: 14px !important;
          }
          .ant-table-thead > tr > th,
          .ant-table-tbody > tr > td {
            padding: 6px 2px !important;
            font-size: 11px !important;
          }
          .ant-statistic-content {
            font-size: 18px !important;
          }
          /* İyileştirilmiş button stilleri */
          .ant-btn {
            padding: 2px 6px !important;
            font-size: 11px !important;
            white-space: nowrap !important;
            text-overflow: ellipsis !important;
            overflow: hidden !important;
            max-width: 100% !important;
          }
          /* Tablo için yatay kaydırma */
          .responsive-table, .responsive-daily-table {
            overflow-x: auto !important;
            width: 100% !important;
            display: block !important;
          }
          /* Stat kartları */
          .ant-col {
            padding: 4px !important;
          }
          .stat-card, .stat-status-card {
            margin-bottom: 8px !important;
          }
          /* Tab menüsü */
          .ant-tabs-nav-list {
            width: 100% !important;
          }
          .ant-tabs-tab {
            flex: 1 !important;
            text-align: center !important;
          }
          /* Filtre alanları */
          .filter-space, .action-space {
            margin-bottom: 8px !important;
          }
          .search-input, .status-select, .date-picker {
            width: 100% !important;
          }
          /* Döngü seçicisi */
          .cycle-select {
            flex: 1 !important;
          }
          /* Başlık alanı */
          .ant-typography {
            font-size: 90% !important;
          }
          h4.ant-typography {
            font-size: 16px !important;
          }
        }
      `}</style>
    </div>
  );
};

export default PublisherDiscovery;