import React from 'react';
import { FaLock, FaT<PERSON><PERSON>, FaUsers } from 'react-icons/fa';
import usePermissions from '../hooks/usePermissions';

interface PermissionGuardProps {
  endpoint: string;
  children: React.ReactNode;
  fallbackTitle?: string;
  fallbackDescription?: string;
}

const PermissionGuard: React.FC<PermissionGuardProps> = ({ 
  endpoint, 
  children, 
  fallbackTitle,
  fallbackDescription 
}) => {
  const { hasAccess, message, userInfo, loading, error } = usePermissions(endpoint);

  if (loading) {
    return (
      <div className="flex items-center justify-center py-16">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-[calc(100vh-4rem)] bg-gray-50 dark:bg-[#0d0c11] flex items-center justify-center p-4">
        <div className="max-w-lg w-full">
          <div className="bg-white dark:bg-[#16151c] rounded-xl shadow-lg overflow-hidden">
            {/* Header */}
            <div className="bg-gradient-to-r from-red-500 to-pink-600 px-6 py-4">
              <div className="flex items-center justify-center mb-3">
                <FaLock className="w-12 h-12 text-white" />
              </div>
              <h1 className="text-xl font-bold text-white text-center mb-1">
                {fallbackTitle || "Bu Sayfa Ajans Üyelerine Özeldir"}
              </h1>
              <p className="text-red-100 text-center text-sm">
                Özel İçerik Alanı
              </p>
            </div>

            {/* Content */}
            <div className="p-6">
              <div className="text-center mb-6">
                <h2 className="text-lg font-bold text-gray-800 dark:text-white mb-3">
                  Merhaba! 👋
                </h2>
                <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
                  {fallbackDescription || "Bu bölüm sadece Tuber Ajans yayıncıları için hazırlanmıştır. Ajansımıza katılarak bu özel içeriklere erişim sağlayabilirsiniz."}
                </p>
              </div>

              {/* CTA Section */}
              <div className="bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 rounded-lg p-4 border border-red-200 dark:border-red-700">
                <div className="text-center">
                  <h3 className="text-base font-bold text-gray-800 dark:text-white mb-2">
                    Tuber Ajans Ailesine Katılın!
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-4 text-sm">
                    Profesyonel içerik üreticisi olarak kariyerinizi bir üst seviyeye taşıyın.
                  </p>

                  <button
                    onClick={() => window.open('https://www.tiktok.com/t/ZS2AxxAxT/', '_blank')}
                    className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-red-500 to-pink-600 text-white font-semibold rounded-lg hover:from-red-600 hover:to-pink-700 transition-all duration-300 transform hover:scale-105 shadow-md hover:shadow-lg text-sm"
                  >
                    <FaUsers className="w-4 h-4 mr-2" />
                    Ajansa Başvur
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!hasAccess) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          {/* Ana Mesaj Kartı */}
          <div className="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 border border-orange-200 dark:border-orange-800 rounded-xl p-8 text-center mb-6">
            <div className="flex justify-center mb-4">
              <div className="bg-orange-100 dark:bg-orange-900/50 p-4 rounded-full">
                <FaLock className="text-orange-600 dark:text-orange-400 text-2xl" />
              </div>
            </div>
            
            <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-3">
              {fallbackTitle || 'Erişim Kısıtlı'}
            </h2>
            
            <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
              {fallbackDescription || message}
            </p>

            {userInfo && (
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 mb-6 border border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-center mb-2">
                  <FaTiktok className="text-black mr-2" />
                  <span className="font-medium text-gray-800 dark:text-white">
                    {userInfo.display_name}
                  </span>
                  {userInfo.is_verified && (
                    <div className="ml-2 bg-blue-500 rounded-full p-1">
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  )}
                </div>
                <p className="text-sm text-gray-500 dark:text-gray-400">@{userInfo.username}</p>
              </div>
            )}
          </div>

          {/* Ajansa Başvuru Butonu */}
          <div className="bg-white dark:bg-[#16151c] rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="text-center">
              <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-2">
                Tuber Ajans Ailesine Katılın!
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                Profesyonel içerik üreticisi olarak kariyerinizi bir üst seviyeye taşıyın.
              </p>

              <button
                onClick={() => window.open('https://www.tiktok.com/t/ZS2AxxAxT/', '_blank')}
                className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-red-500 to-pink-600 text-white font-semibold rounded-xl hover:from-red-600 hover:to-pink-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
              >
                <FaUsers className="w-5 h-5 mr-2" />
                Ajansa Başvur
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default PermissionGuard;
