#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TikTok Thumbnail Test Script
VDS'de thumbnail çekme işlemini test eder
"""

import time
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def setup_chrome_driver():
    """Chrome WebDriver'ı başlat"""
    try:
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # Chrome profil yolu
        profile_path = r"C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data\Profile 1"
        chrome_options.add_argument(f'--user-data-dir={profile_path}')
        
        # Chrome executable yolu
        chrome_options.binary_location = r"C:\Users\<USER>\Downloads\chrome-win64\chrome-win64\chrome.exe"
        
        # ChromeDriver yolu
        driver_path = r"C:\chromedriver.exe"
        
        driver = webdriver.Chrome(executable_path=driver_path, options=chrome_options)
        print("✅ Chrome WebDriver başlatıldı")
        return driver
        
    except Exception as e:
        print(f"❌ Chrome WebDriver başlatılamadı: {e}")
        return None

def test_profile_thumbnails(driver, username):
    """Profil sayfasından thumbnail'ları test et"""
    print(f"\n🔍 Profil sayfası thumbnail testi: @{username}")
    
    try:
        # Profil sayfasına git
        profile_url = f"https://www.tiktok.com/@{username}"
        print(f"📄 Profil sayfasına gidiliyor: {profile_url}")
        driver.get(profile_url)
        
        # Sayfa yüklensin
        time.sleep(5)
        
        # Video kartlarını bul
        video_cards = driver.find_elements(By.CSS_SELECTOR, '[data-e2e="user-post-item"], .tiktok-x6y88p-DivItemContainerV2')
        print(f"📹 {len(video_cards)} video kartı bulundu")
        
        if not video_cards:
            print("❌ Video kartı bulunamadı")
            return []
        
        thumbnails_found = []
        
        # İlk 5 video kartını test et
        for idx, card in enumerate(video_cards[:5]):
            print(f"\n--- Video {idx+1} Thumbnail Testi ---")
            
            try:
                # Video URL'sini al
                link_element = card.find_element(By.TAG_NAME, 'a')
                video_url = link_element.get_attribute('href')
                print(f"🔗 Video URL: {video_url}")
                
                # Thumbnail arama
                thumbnail_url = None
                
                # Gelişmiş selectors
                thumbnail_selectors = [
                    "img.tiktok-1itcwxg-ImgPoster",
                    "img[mode='0']",
                    "img[mode='1']", 
                    "img[mode='2']",
                    "img[class*='ImgPoster']",
                    "img[src*='tiktokcdn']",
                    "img[src*='muscdn']",
                    "img[src*='bytedance']",
                    "img[src*='image']",
                    ".tiktok-1jxhpnd-ImgPoster",
                    "[data-e2e='user-post-item'] img",
                    ".tiktok-x6y88p-DivItemContainerV2 img",
                    "img[alt*='video']",
                    "img[loading='lazy']",
                    "img[decoding='async']"
                ]

                for selector_idx, selector in enumerate(thumbnail_selectors):
                    try:
                        thumbnail_elements = card.find_elements(By.CSS_SELECTOR, selector)
                        print(f"  🔍 Selector {selector_idx+1} ({selector}): {len(thumbnail_elements)} element")
                        
                        for elem_idx, thumbnail_element in enumerate(thumbnail_elements):
                            img_src = thumbnail_element.get_attribute("src")
                            print(f"    📷 Element {elem_idx+1}: {img_src[:80] if img_src else 'None'}...")
                            
                            if img_src and img_src.startswith('http') and ('tiktok' in img_src or 'mus' in img_src or 'bytedance' in img_src or 'image' in img_src):
                                thumbnail_url = img_src
                                print(f"    ✅ THUMBNAIL BULUNDU! Selector: {selector}")
                                break
                        if thumbnail_url:
                            break
                    except Exception as e:
                        print(f"    ❌ Selector hatası: {e}")
                        continue
                
                # Eğer hala bulunamadıysa, tüm img elementlerini kontrol et
                if not thumbnail_url:
                    print("  🔍 Genel img arama...")
                    try:
                        all_imgs = card.find_elements(By.TAG_NAME, "img")
                        print(f"    📷 Toplam {len(all_imgs)} img elementi")
                        
                        for img_idx, img in enumerate(all_imgs):
                            img_src = img.get_attribute("src")
                            print(f"    📷 IMG {img_idx+1}: {img_src[:80] if img_src else 'None'}...")
                            
                            if img_src and img_src.startswith('http') and ('tiktok' in img_src or 'mus' in img_src or 'bytedance' in img_src or 'image' in img_src):
                                thumbnail_url = img_src
                                print(f"    ✅ THUMBNAIL BULUNDU! (Genel arama)")
                                break
                    except Exception as e:
                        print(f"    ❌ Genel arama hatası: {e}")
                
                # Sonuç
                if thumbnail_url:
                    print(f"✅ Video {idx+1} thumbnail: {thumbnail_url}")
                    thumbnails_found.append({
                        'video_idx': idx+1,
                        'video_url': video_url,
                        'thumbnail_url': thumbnail_url
                    })
                else:
                    print(f"❌ Video {idx+1} thumbnail bulunamadı")
                    
            except Exception as e:
                print(f"❌ Video {idx+1} işlenirken hata: {e}")
                continue
        
        return thumbnails_found
        
    except Exception as e:
        print(f"❌ Profil thumbnail testi hatası: {e}")
        return []

def test_video_page_thumbnails(driver, video_urls):
    """Video sayfalarından thumbnail'ları test et"""
    print(f"\n🎬 Video sayfası thumbnail testi")
    
    for idx, video_data in enumerate(video_urls[:3]):  # İlk 3 videoyu test et
        print(f"\n--- Video {video_data['video_idx']} Sayfa Testi ---")
        
        try:
            # Video sayfasına git
            print(f"📄 Video sayfasına gidiliyor: {video_data['video_url']}")
            driver.get(video_data['video_url'])
            
            # Sayfa yüklensin
            time.sleep(5)
            
            # Poster attribute testi
            print("🔍 Poster attribute testi:")
            video_elements = driver.find_elements(By.TAG_NAME, "video")
            for v_idx, video_elem in enumerate(video_elements):
                poster_url = video_elem.get_attribute('poster')
                print(f"  📹 Video {v_idx+1} poster: {poster_url[:80] if poster_url else 'None'}...")
            
            # IMG element testi
            print("🔍 IMG element testi:")
            img_selectors = [
                "img[src*='tiktokcdn']",
                "img[src*='muscdn']",
                "img[src*='bytedance']",
                "img[src*='image']",
                ".tiktok-1itcwxg-ImgPoster",
                "img[class*='ImgPoster']",
                "img[class*='poster']",
                "[data-e2e='video-player'] img",
                ".tiktok-1hskgat-DivPlayerContainer img",
                "img[mode]",
                "img[alt*='video']"
            ]
            
            found_thumbnail = None
            for selector in img_selectors:
                try:
                    img_elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    print(f"  🔍 {selector}: {len(img_elements)} element")
                    
                    for img_elem in img_elements:
                        img_url = img_elem.get_attribute('src')
                        if img_url and img_url.startswith('http') and ('tiktok' in img_url or 'mus' in img_url or 'bytedance' in img_url or 'image' in img_url):
                            print(f"    ✅ THUMBNAIL: {img_url[:80]}...")
                            found_thumbnail = img_url
                            break
                    if found_thumbnail:
                        break
                except:
                    continue
            
            if not found_thumbnail:
                print("  ❌ Video sayfasında thumbnail bulunamadı")
            
        except Exception as e:
            print(f"❌ Video sayfa testi hatası: {e}")

def main():
    """Ana test fonksiyonu"""
    print("🚀 TikTok Thumbnail Test Başlatılıyor...")
    
    # Test edilecek kullanıcı
    test_username = "askinelidegmisgibi"  # Bilinen bir kullanıcı
    
    # Chrome driver'ı başlat
    driver = setup_chrome_driver()
    if not driver:
        print("❌ Test başlatılamadı")
        return
    
    try:
        # 1. Profil sayfası thumbnail testi
        thumbnails = test_profile_thumbnails(driver, test_username)
        
        print(f"\n📊 PROFIL SONUÇLARI:")
        print(f"✅ {len(thumbnails)} thumbnail bulundu")
        for thumb in thumbnails:
            print(f"  Video {thumb['video_idx']}: {thumb['thumbnail_url'][:60]}...")
        
        # 2. Video sayfası thumbnail testi
        if thumbnails:
            test_video_page_thumbnails(driver, thumbnails)
        
        print(f"\n🎯 TEST TAMAMLANDI!")
        print(f"📈 Başarı oranı: {len(thumbnails)}/5 (%{len(thumbnails)*20})")
        
    except Exception as e:
        print(f"❌ Test hatası: {e}")
    
    finally:
        # Driver'ı kapat
        try:
            driver.quit()
            print("🔒 Chrome WebDriver kapatıldı")
        except:
            pass

if __name__ == '__main__':
    main()
