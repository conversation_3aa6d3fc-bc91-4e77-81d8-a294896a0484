<?php
/**
 * Influencers API - Gerçek Veritabanı ile Optimize Edilmiş
 * Frontend uyumlu, hızlı ve güvenilir influencer verileri
 */

// Hızlı ayarlar
set_time_limit(10); // 10 saniye max
ini_set('memory_limit', '128M');

// CORS ve Headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// OPTIONS kontrolü
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Config include (auth.php gerekli değil, config.php'de checkAuth var)
require_once __DIR__ . '/../config/config.php';

// Hata logları
error_log("Influencers API çağrıldı - " . date('Y-m-d H:i:s') . " - Method: " . $_SERVER['REQUEST_METHOD']);

// JSON yanıt fonksiyonu
if (!function_exists('jsonResponse')) {
    function jsonResponse($data, $status = 200) {
        http_response_code($status);
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
}

// Cache kontrol fonksiyonu
function getCachedInfluencers($key, $ttl = 60) {
    $cacheFile = sys_get_temp_dir() . "/influencers_$key.json";
    if (file_exists($cacheFile) && (time() - filemtime($cacheFile)) < $ttl) {
        return json_decode(file_get_contents($cacheFile), true);
    }
    return null;
}

function setCachedInfluencers($key, $data) {
    $cacheFile = sys_get_temp_dir() . "/influencers_$key.json";
    file_put_contents($cacheFile, json_encode($data, JSON_UNESCAPED_UNICODE));
}

try {
    // Auth kontrolü (GET istekleri için de gerekli)
    error_log("Influencers API: Auth kontrolü başlıyor...");
    $user = checkAuth();
    if (!$user) {
        error_log("Influencers API: Auth başarısız");
        jsonResponse([
            'success' => false,
            'error' => 'Yetkilendirme hatası',
            'message' => 'Oturum açmanız gerekiyor'
        ], 401);
    }
    
    error_log("Influencers API: Auth başarılı - User ID: " . $user['id']);

    // Veritabanı bağlantısını al
    error_log("Influencers API: Veritabanı bağlantısı alınıyor...");
    $db = getDB();
    if (!$db) {
        error_log("Influencers API: Veritabanı bağlantısı alınamadı");
        jsonResponse([
            'success' => false,
            'error' => 'Veritabanı hatası',
            'message' => 'Veritabanı bağlantısı kurulamadı'
        ], 500);
    }
    
    error_log("Influencers API: Veritabanı bağlantısı başarılı");

    // Influencer_info tablosunun hangi veritabanında olduğunu bul
    $databases_to_try = ['tuberaja_yayinci_takip', 'tuberaja_yayinci_akademi'];
    $influencer_database = null;
    
    foreach ($databases_to_try as $dbname) {
        try {
            error_log("Influencers API: $dbname veritabanı kontrol ediliyor...");
            
            // PDO ile doğrudan database.table notation kullan
            $testQuery = "SELECT COUNT(*) as count FROM $dbname.influencer_info LIMIT 1";
            $stmt = $db->prepare($testQuery);
            $stmt->execute();
            $result = $stmt->fetch();
            
            if ($result !== false) {
                $influencer_database = $dbname;
                error_log("Influencers API: influencer_info tablosu $dbname veritabanında bulundu (kayıt sayısı: " . $result['count'] . ")");
                break;
            }
        } catch (Exception $e) {
            error_log("Influencers API: $dbname veritabanı kontrol edilemedi: " . $e->getMessage());
            continue;
        }
    }
    
    if (!$influencer_database) {
        // Son çare: direkt tuberaja_yayinci_takip kullan
        error_log("Influencers API: Auto-detection başarısız, tuberaja_yayinci_takip kullanılacak");
        $influencer_database = 'tuberaja_yayinci_takip';
        
        // Test et
        try {
            $testQuery = "SELECT COUNT(*) as count FROM $influencer_database.influencer_info LIMIT 1";
            error_log("Influencers API: Test query: " . $testQuery);
            $stmt = $db->prepare($testQuery);
            $stmt->execute();
            $result = $stmt->fetch();
            error_log("Influencers API: Direct test başarılı - kayıt sayısı: " . $result['count']);
        } catch (Exception $e) {
            error_log("Influencers API: Direct test başarısız: " . $e->getMessage());
            jsonResponse([
                'success' => false,
                'error' => 'Tablo bulunamadı',
                'message' => 'Influencer verileri bulunamadı: ' . $e->getMessage(),
                'debug_info' => [
                    'tried_databases' => $databases_to_try,
                    'final_error' => $e->getMessage()
                ]
            ], 500);
        }
    }

    // GET: Influencerları listele
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $countOnly = isset($_GET['countOnly']) && $_GET['countOnly'] == '1';
        $cacheKey = $countOnly ? 'count' : 'all';
        
        // Cache kontrolü
        $cachedData = getCachedInfluencers($cacheKey, 30);
        if ($cachedData) {
            error_log("Influencers API: Cache'den veri döndürülüyor");
            jsonResponse($cachedData);
        }

        try {
            // Veritabanı bağlantısı kontrolü
            if (!isset($db) || !$db) {
                throw new Exception("Veritabanı bağlantısı bulunamadı");
            }

            if ($countOnly) {
                // Sadece sayı döndür
                $countStmt = $db->query("SELECT COUNT(*) as count FROM $influencer_database.influencer_info");
                $count = $countStmt->fetch(PDO::FETCH_ASSOC)['count'];
                
                $response = [
                    'success' => true,
                    'count' => (int)$count,
                    'cached' => false,
                    'timestamp' => date('Y-m-d H:i:s')
                ];
                
                setCachedInfluencers($cacheKey, $response);
                jsonResponse($response);
            } else {
                // Tüm influencer verilerini al (created_at kolonu yok, o yüzden çıkardık)
                $influencerQuery = "SELECT 
                    id,
                    username,
                    email,
                    category,
                    followers,
                    location,
                    status,
                    notes,
                    last_contact,
                    sosyal_medya,
                    telefon,
                    kampanya_gecmisi,
                    profile_image
             FROM $influencer_database.influencer_info
                ORDER BY id DESC";
                
                $stmt = $db->prepare($influencerQuery);
        $stmt->execute();
        $influencers = $stmt->fetchAll(PDO::FETCH_ASSOC);

                error_log("Influencers API: " . count($influencers) . " influencer bulundu");

                // Veri temizleme ve düzenleme
                $result = [];
                foreach ($influencers as $influencer) {
                    // ID'yi string yap (frontend uyumluluğu için)
                    $influencer['id'] = (string)$influencer['id'];
                    
                    // Null değerleri boş string yap
                    foreach ($influencer as $key => $value) {
                if ($value === null) {
                            $influencer[$key] = '';
                        }
                    }
                    
                    // Followers sayısını integer yap
                    $influencer['followers'] = (int)($influencer['followers'] ?? 0);
                    
                    // Status varsayılan değer
                    if (empty($influencer['status'])) {
                        $influencer['status'] = 'active';
                    }
                    
                    // Category varsayılan değer
                    if (empty($influencer['category'])) {
                        $influencer['category'] = 'general';
                    }
                    
                    $result[] = $influencer;
                }

                $response = [
                    'success' => true,
                    'data' => $result,
                    'total' => count($result),
                    'cached' => false,
                    'timestamp' => date('Y-m-d H:i:s')
                ];
                
                // Cache'e kaydet
                setCachedInfluencers($cacheKey, $response);
                
                error_log("Influencers API: " . count($result) . " influencer verisi döndürülüyor");
                jsonResponse($response);
            }

        } catch (PDOException $e) {
            error_log("Influencers API DB Hatası: " . $e->getMessage());
            jsonResponse([
                'success' => false,
                'error' => 'Veritabanı hatası',
                'message' => 'Influencer verileri alınırken hata oluştu'
            ], 500);
        }
    }

    // POST/PUT/DELETE istekleri için additional token kontrolü (isteğe bağlı)
    // Auth kontrolü zaten yukarıda yapıldı

    // POST: Yeni influencer ekle veya JSON action'ları
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Excel file upload kontrolü
        if (isset($_FILES['excelFile'])) {
            // Excel yükleme işlemi için PHPSpreadsheet gerekli
            $autoload = __DIR__ . '/../vendor/autoload.php';
    if (!file_exists($autoload)) {
        error_log("Influencers API: Composer autoload not found at $autoload");
                jsonResponse(['success' => false, 'error' => 'Server configuration error'], 500);
            }
            require_once $autoload;
            
            try {
                if ($_FILES['excelFile']['error'] != UPLOAD_ERR_OK) {
        throw new Exception('Dosya yüklenirken bir hata oluştu.');
    }

    $allowedTypes = [
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    'application/vnd.ms-excel',
                    'application/octet-stream',
                    'text/csv'
                ];

    $fileType = $_FILES['excelFile']['type'];
        $fileExt = strtolower(pathinfo($_FILES['excelFile']['name'], PATHINFO_EXTENSION));
                
                if (!in_array($fileType, $allowedTypes) && !in_array($fileExt, ['xlsx', 'xls', 'csv'])) {
                    throw new Exception('Yalnızca Excel (.xlsx, .xls) veya CSV dosyaları yüklenebilir.');
                }

    $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReaderForFile($_FILES['excelFile']['tmp_name']);
    $reader->setReadDataOnly(true);
    $spreadsheet = $reader->load($_FILES['excelFile']['tmp_name']);
    $worksheet = $spreadsheet->getActiveSheet();
    $rows = $worksheet->toArray();

    $headers = array_shift($rows);

                // Sütun eşleştirmeleri
    $columnMappings = [
        'Kullanıcı Adı' => 'username',
        'Email' => 'email',
                    'E-Mail' => 'email',
                    'E-mail' => 'email',
        'Takipçi Sayısı' => 'followers',
                    'Takipci Sayisi' => 'followers',
        'Profil Linki' => 'sosyal_medya',
        'Sosyal Medya' => 'sosyal_medya',
        'Kategori' => 'category',
        'Konum' => 'location',
        'Durum' => 'status',
        'Notlar' => 'notes',
        'Son İletişim' => 'last_contact',
        'Telefon' => 'telefon',
        'Kampanya Geçmişi' => 'kampanya_gecmisi'
    ];

    $columnIndexes = [];
    foreach ($headers as $index => $header) {
                    $header = trim($header);
                    if (isset($columnMappings[$header])) {
                        $columnIndexes[$columnMappings[$header]] = $index;
                    }
                }

                $successCount = 0;
                $errorCount = 0;
                $errors = [];

                foreach ($rows as $rowIndex => $row) {
                    try {
                        if (empty($row) || count(array_filter($row)) === 0) {
                            continue;
                        }

                        $influencerData = [];
                        foreach ($columnIndexes as $dbColumn => $excelIndex) {
                            $value = isset($row[$excelIndex]) ? trim($row[$excelIndex]) : '';
                            if ($dbColumn === 'followers' && !empty($value)) {
                                $value = (int)preg_replace('/[^0-9]/', '', $value);
                            }
                            $influencerData[$dbColumn] = $value;
                        }

                        if (empty($influencerData['username']) || empty($influencerData['email'])) {
                            $errors[] = "Satır " . ($rowIndex + 2) . ": Kullanıcı adı ve email zorunlu";
                            $errorCount++;
                            continue;
                        }

                        // Benzersizlik kontrolü
                        $checkStmt = $db->prepare("SELECT id FROM $influencer_database.influencer_info WHERE username = ? OR email = ?");
                        $checkStmt->execute([$influencerData['username'], $influencerData['email']]);
                        if ($checkStmt->rowCount() > 0) {
                            $errors[] = "Satır " . ($rowIndex + 2) . ": Bu kullanıcı adı veya email zaten mevcut";
                            $errorCount++;
                            continue;
                        }

                        $fields = array_keys($influencerData);
                        $placeholders = array_fill(0, count($fields), '?');
                        $sql = "INSERT INTO $influencer_database.influencer_info (" . implode(',', $fields) . ") VALUES (" . implode(',', $placeholders) . ")";
                        $stmt = $db->prepare($sql);
                        $stmt->execute(array_values($influencerData));
                        
                        $successCount++;
                    } catch (Exception $e) {
                        $errors[] = "Satır " . ($rowIndex + 2) . ": " . $e->getMessage();
                        $errorCount++;
                    }
                }

                // Cache'i temizle
                array_map('unlink', glob(sys_get_temp_dir() . '/influencers_*.json'));

                jsonResponse([
                    'success' => true,
                    'message' => "$successCount influencer başarıyla eklendi",
                    'successCount' => $successCount,
                    'errorCount' => $errorCount,
                    'errors' => $errors
                ]);

            } catch (Exception $e) {
                error_log("Influencers Excel Upload Hatası: " . $e->getMessage());
                jsonResponse(['success' => false, 'error' => $e->getMessage()], 500);
            }
        } else {
            // JSON action'ları
            $input = json_decode(file_get_contents('php://input'), true);
            if (!$input || !isset($input['action'])) {
                jsonResponse(['success' => false, 'error' => 'Geçersiz JSON verisi veya action eksik'], 400);
            }

            $action = $input['action'];
            try {
                if ($action === 'create') {
                    $data = $input['data'];
                    $allowed = ['username', 'email', 'category', 'followers', 'location', 'status', 'notes', 'last_contact', 'sosyal_medya', 'telefon', 'kampanya_gecmisi', 'profile_image'];
                    $data = array_intersect_key($data, array_flip($allowed));
                    
                    if (empty($data['username']) || empty($data['email'])) {
                        throw new Exception('Kullanıcı adı ve email zorunludur');
                    }

                    // Benzersizlik kontrolü
                    $checkStmt = $db->prepare("SELECT id FROM $influencer_database.influencer_info WHERE username = ? OR email = ?");
                    $checkStmt->execute([$data['username'], $data['email']]);
                    if ($checkStmt->rowCount() > 0) {
                        throw new Exception('Bu kullanıcı adı veya email zaten kullanılıyor');
                    }

                    $fields = array_keys($data);
                    $placeholders = array_fill(0, count($fields), '?');
                    $sql = "INSERT INTO $influencer_database.influencer_info (" . implode(',', $fields) . ") VALUES (" . implode(',', $placeholders) . ")";
                    $stmt = $db->prepare($sql);
                    $stmt->execute(array_values($data));
                    $newId = $db->lastInsertId();

                    // Cache'i temizle
                    array_map('unlink', glob(sys_get_temp_dir() . '/influencers_*.json'));

                    jsonResponse(['success' => true, 'id' => $newId, 'message' => 'Influencer başarıyla eklendi']);

                } elseif ($action === 'update') {
                    $data = $input['data'];
                    $allowed = ['username', 'email', 'category', 'followers', 'location', 'status', 'notes', 'last_contact', 'sosyal_medya', 'telefon', 'kampanya_gecmisi', 'profile_image'];
                    $data = array_intersect_key($data, array_flip($allowed));
                    
                    if (empty($input['data']['id'])) {
                        throw new Exception('ID zorunludur');
                    }
                    
                    $id = (int)$input['data']['id'];
                    $sets = [];
                    foreach ($data as $k => $v) {
                        $sets[] = "$k = ?";
                    }
                    
                    if (empty($sets)) {
                        jsonResponse(['success' => false, 'error' => 'Güncellenecek alan yok'], 400);
                    }
                    
                    $sql = "UPDATE $influencer_database.influencer_info SET " . implode(',', $sets) . " WHERE id = ?";
                    $stmt = $db->prepare($sql);
                    $stmt->execute(array_merge(array_values($data), [$id]));

                    // Cache'i temizle
                    array_map('unlink', glob(sys_get_temp_dir() . '/influencers_*.json'));

                    jsonResponse(['success' => true, 'updated' => true, 'id' => $id, 'message' => 'Influencer başarıyla güncellendi']);

                } elseif ($action === 'delete') {
                    $id = isset($input['id']) ? (int)$input['id'] : null;
                    if (!$id) {
                        throw new Exception('ID zorunludur');
                    }
                    
                    $stmt = $db->prepare("DELETE FROM $influencer_database.influencer_info WHERE id = ?");
                    $stmt->execute([$id]);
                    
                    if ($stmt->rowCount() === 0) {
                        jsonResponse(['success' => false, 'error' => 'Influencer bulunamadı'], 404);
                    }

                    // Cache'i temizle
                    array_map('unlink', glob(sys_get_temp_dir() . '/influencers_*.json'));

                    jsonResponse(['success' => true, 'deleted' => true, 'id' => $id, 'message' => 'Influencer başarıyla silindi']);

                } else {
                    jsonResponse(['success' => false, 'error' => 'Bilinmeyen action'], 400);
                }
            } catch (Exception $e) {
                error_log("Influencers API action error: " . $e->getMessage());
                jsonResponse(['success' => false, 'error' => $e->getMessage()], 500);
            }
        }
    }

    // PUT: Influencer güncelle (alternatif yöntem)
    elseif ($_SERVER['REQUEST_METHOD'] === 'PUT') {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            if (!$input || !isset($input['id'])) {
                jsonResponse(['success' => false, 'error' => 'ID gerekli'], 400);
            }

            $id = (int)$input['id'];
            $allowedFields = ['username', 'email', 'category', 'followers', 'location', 'status', 'notes', 'last_contact', 'sosyal_medya', 'telefon', 'kampanya_gecmisi', 'profile_image'];
            
            $updateFields = [];
            $params = [];
            
            foreach ($allowedFields as $field) {
                if (isset($input[$field])) {
                    $updateFields[] = "$field = ?";
                    $params[] = $input[$field];
                }
            }
            
            if (empty($updateFields)) {
                jsonResponse(['success' => false, 'error' => 'Güncellenecek alan yok'], 400);
            }
            
            $params[] = $id;
            $updateStmt = $db->prepare("UPDATE $influencer_database.influencer_info SET " . implode(', ', $updateFields) . " WHERE id = ?");
            $updateStmt->execute($params);

            // Cache'i temizle
            array_map('unlink', glob(sys_get_temp_dir() . '/influencers_*.json'));
            
            jsonResponse(['success' => true, 'message' => 'Influencer başarıyla güncellendi']);
            
        } catch (PDOException $e) {
            error_log("Influencers API PUT Hatası: " . $e->getMessage());
            jsonResponse(['success' => false, 'error' => 'Güncelleme sırasında hata oluştu'], 500);
        }
    }

    // DELETE: Influencer sil (alternatif yöntem)
    elseif ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            $id = isset($_GET['id']) ? (int)$_GET['id'] : (isset($input['id']) ? (int)$input['id'] : null);
            
            if (!$id) {
                jsonResponse(['success' => false, 'error' => 'ID gerekli'], 400);
            }
            
            $deleteStmt = $db->prepare("DELETE FROM $influencer_database.influencer_info WHERE id = ?");
            $deleteStmt->execute([$id]);
            
            if ($deleteStmt->rowCount() === 0) {
                jsonResponse(['success' => false, 'error' => 'Influencer bulunamadı'], 404);
            }

            // Cache'i temizle
            array_map('unlink', glob(sys_get_temp_dir() . '/influencers_*.json'));
            
            jsonResponse(['success' => true, 'message' => 'Influencer başarıyla silindi']);
            
        } catch (PDOException $e) {
            error_log("Influencers API DELETE Hatası: " . $e->getMessage());
            jsonResponse(['success' => false, 'error' => 'Silme sırasında hata oluştu'], 500);
        }
    }

    else {
        jsonResponse(['success' => false, 'error' => 'Desteklenmeyen HTTP metodu'], 405);
    }

} catch (Exception $e) {
    error_log("Influencers API Genel Hatası: " . $e->getMessage());
    
    if (strpos($e->getMessage(), 'Unauthorized') !== false) {
        jsonResponse([
            'success' => false,
            'error' => 'Yetkilendirme hatası',
            'message' => 'Oturum açmanız gerekiyor'
        ], 401);
    } else {
        jsonResponse([
            'success' => false,
            'error' => 'Sunucu hatası',
            'message' => 'Geçici bir hata oluştu, lütfen tekrar deneyin'
        ], 500);
    }
}
?>