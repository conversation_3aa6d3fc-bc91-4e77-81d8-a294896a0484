import React, { useState, useEffect, useContext } from 'react';
import { useParams, Link, useNavigate } from 'react-router-dom';
import { FaArrowLeft, FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import axios from 'axios';
import { SidebarContext } from '../../contexts/SidebarContext';

// Kurs için interface - tek bir courses tablosu kullanıyoruz
interface Course {
  id: number;
  title: string;
  description: string;
  content?: string;
  category?: string;
  image?: string;
  icon?: string;
  featured?: boolean;
  created_by?: number;
  created_at?: string;
  updated_at?: string;
  status?: string;
}

// Örnek veri (API bağlantısı başarısız olursa kullanılacak)
const fallbackCourseData = {
  1: {
    id: 1,
    title: 'TikTok Algoritmasını Anlamak',
    description: 'Bu eğitimde TikTok algoritmasının nasıl çalıştığını ve içeriklerinizin daha fazla kişiye ulaşması için neler yapabileceğinizi öğreneceksiniz.',
    category: 'Genel',
    image: 'course1.jpg',
    icon: 'chart-line',
    featured: true,
    created_by: 1,
    created_at: '2023-05-01 12:00:00',
    updated_at: '2023-05-01 12:00:00',
    status: 'active',
    content: '<h2>TikTok Algoritmasını Anlamak</h2><p>Bu eğitimde TikTok algoritmasının nasıl çalıştığını ve içeriklerinizin daha fazla kişiye ulaşması için neler yapabileceğinizi öğreneceksiniz.</p><h3>Algoritma Temelleri</h3><p>TikTok algoritması, kullanıcıların etkileşimlerine dayalı olarak çalışan karmaşık bir sistemdir. Algoritma, kullanıcıların izleme alışkanlıklarını, beğenilerini, yorumlarını ve paylaşımlarını analiz ederek kişiselleştirilmiş bir içerik akışı oluşturur.</p><p>Algoritmanın temel çalışma prensipleri şunlardır:</p><ul><li><strong>İçerik Analizi:</strong> Algoritma, videoların içeriğini, kullanılan müzikleri, hashtag\'leri ve açıklamaları analiz eder.</li><li><strong>Kullanıcı Etkileşimi:</strong> Kullanıcıların hangi videoları ne kadar süreyle izlediği, beğendiği, yorum yaptığı ve paylaştığı gibi etkileşimler önemlidir.</li><li><strong>Cihaz ve Hesap Ayarları:</strong> Kullanıcının cihaz türü, dil ayarları ve konum bilgisi gibi faktörler de algoritmanın kararlarını etkiler.</li></ul>'
  },
  2: {
    id: 2,
    title: 'Viral İçerik Oluşturma Teknikleri',
    description: 'Viral olma potansiyeli taşıyan içerikler oluşturmanın püf noktaları ve teknikler.',
    category: 'İçerik',
    image: 'course2.jpg',
    icon: 'fire',
    featured: true,
    created_by: 1,
    created_at: '2023-05-15 12:00:00',
    updated_at: '2023-05-15 12:00:00',
    status: 'active',
    content: '<h2>Viral İçerik Oluşturma Teknikleri</h2><p>Viral olma potansiyeli taşıyan içerikler oluşturmanın püf noktaları ve teknikler.</p><h3>Viral İçeriğin Temelleri</h3><p>Viral içerik, kısa sürede geniş kitlelere ulaşan ve hızla yayılan içerik türüdür. Viral içerikler genellikle duygusal tepki uyandıran, ilgi çekici ve paylaşılabilir özelliklere sahiptir.</p>'
  },
  3: {
    id: 3,
    title: 'TikTok İçin Video Düzenleme',
    description: 'Profesyonel görünümlü TikTok videoları oluşturmak için düzenleme teknikleri.',
    category: 'Teknik',
    image: 'course3.jpg',
    icon: 'video',
    featured: false,
    created_by: 1,
    created_at: '2023-06-10 12:00:00',
    updated_at: '2023-06-10 12:00:00',
    status: 'active',
    content: '<h2>TikTok İçin Video Düzenleme</h2><p>Profesyonel görünümlü TikTok videoları oluşturmak için düzenleme teknikleri.</p><h3>Temel Video Düzenleme</h3><p>Başarılı TikTok videoları oluşturmak için temel düzenleme becerilerine sahip olmak önemlidir. Bu bölümde, video kesme, birleştirme, ses ekleme ve efekt uygulama gibi temel düzenleme tekniklerini öğreneceksiniz.</p>'
  }
};



const CourseDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const courseId = Number(id);
  const navigate = useNavigate();

  const [course, setCourse] = useState<Course | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [prevCourseId, setPrevCourseId] = useState<number | null>(null);
  const [nextCourseId, setNextCourseId] = useState<number | null>(null);

  // Sidebar context'inden isMobile ve isSidebarOpen değerlerini al
  const { isMobile, isSidebarOpen } = useContext(SidebarContext);

  // Kurs verilerini API'den çek
  useEffect(() => {
    const fetchCourse = async () => {
      setLoading(true);
      setError(null);

      try {
        // Kurs detaylarını çek
        console.log('Kurs ID:', courseId);
        const response = await axios.get(`/backend/api/course_detail.php?id=${courseId}`);
        console.log('API yanıtı:', response.data);

        if (response.data.success && response.data.data) {
          console.log('Kurs verisi başarıyla alındı:', response.data.data);
          setCourse(response.data.data);

          // Önceki ve sonraki kurs ID'lerini ayarla
          if (response.data.navigation) {
            setPrevCourseId(response.data.navigation.prev_course_id);
            setNextCourseId(response.data.navigation.next_course_id);
          }
        } else {
          console.error('API başarısız yanıt döndü:', response.data);
          setError('Kurs verileri alınamadı: ' + (response.data.message || 'Bilinmeyen hata'));

          // Hata durumunda fallback verileri kullan
          console.log('Fallback veri kullanılıyor, courseId:', courseId);
          const fallbackCourse = fallbackCourseData[courseId as keyof typeof fallbackCourseData];
          if (fallbackCourse) {
            console.log('Fallback kurs verisi bulundu:', fallbackCourse);
            setCourse(fallbackCourse as unknown as Course);
          } else {
            console.error('Fallback kurs verisi bulunamadı');
          }
        }
      } catch (err: any) {
        console.error('Kurs verileri çekilirken hata oluştu:', err);
        console.error('Hata detayları:', err.message, err.response?.status, err.response?.data);
        setError('Kurs verileri çekilirken bir hata oluştu: ' + err.message);

        // Hata durumunda fallback verileri kullan
        console.log('Fallback veri kullanılıyor, courseId:', courseId);
        const fallbackCourse = fallbackCourseData[courseId as keyof typeof fallbackCourseData];
        if (fallbackCourse) {
          console.log('Fallback kurs verisi bulundu:', fallbackCourse);
          setCourse(fallbackCourse as unknown as Course);
        } else {
          console.error('Fallback kurs verisi bulunamadı');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchCourse();
  }, [courseId]);

  // Önceki eğitime git
  const goToPrevCourse = () => {
    if (prevCourseId !== null) {
      navigate(`/dashboard/courses/${prevCourseId}`);
    }
  };

  // Sonraki eğitime git
  const goToNextCourse = () => {
    if (nextCourseId !== null) {
      navigate(`/dashboard/courses/${nextCourseId}`);
    }
  };

  // Yükleme durumu
  if (loading) {
    return (
      <div className="p-8 flex flex-col items-center justify-center min-h-[60vh]">
        <div className="w-24 h-24 mx-auto bg-primary-50 dark:bg-primary-900/20 text-primary-500 dark:text-primary-400 rounded-full flex items-center justify-center mb-6 border-2 border-primary-100 dark:border-primary-800 animate-pulse">
          <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
          </svg>
        </div>
        <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-3">Eğitim Yükleniyor</h3>
        <p className="text-base text-gray-500 dark:text-gray-400 max-w-md text-center mb-8">
          Eğitim içeriği yükleniyor, lütfen bekleyin...
        </p>
      </div>
    );
  }

  // Hata durumu veya kurs bulunamadı
  if (!course || error) {
    return (
      <div className="p-8 flex flex-col items-center justify-center min-h-[60vh]">
        <div className="w-24 h-24 mx-auto bg-primary-50 dark:bg-primary-900/20 rounded-full flex items-center justify-center mb-6 border-2 border-primary-100 dark:border-primary-800">
          <svg className="w-12 h-12 text-gray-400 dark:text-gray-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-3">Eğitim Bulunamadı</h3>
        <p className="text-base text-gray-500 dark:text-gray-400 max-w-md text-center mb-8">
          {error || "Aradığınız eğitim içeriği mevcut değil veya kaldırılmış olabilir. Lütfen tüm eğitimler sayfasına dönüp başka bir eğitim seçin."}
        </p>
        <Link
          to="/dashboard/courses"
          className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white bg-[#FF3E71] hover:bg-[#FF5F87] dark:bg-primary-700 dark:hover:bg-primary-800 focus:outline-none transition-colors"
        >
          <FaArrowLeft className="mr-2" />
          Tüm Eğitimlere Dön
        </Link>
      </div>
    );
  }

  return (
    <div className="container" style={{
      maxWidth: isMobile ? '100%' : (isSidebarOpen ? 'calc(100vw - 280px - 30px)' : 'calc(100vw - 78px - 30px)'),
      overflowX: 'hidden'
    }}>

      {/* Video ve Eğitim İçeriği */}
      <div className="bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden mb-6">
        {/* Eğitim Başlık Alanı */}
        <div className="p-8 border-b border-gray-100 dark:border-gray-700 bg-gradient-to-r from-primary-50 to-white dark:from-[#16151c] dark:to-[#16151c]">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-800 dark:text-white mb-4">{course.title}</h1>
          <div className="flex flex-wrap items-center gap-3 mb-4">
            {course.category && (
              <div className="px-3 py-1.5 bg-primary-100 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400 rounded-full text-xs font-medium border border-primary-200 dark:border-primary-800">
                {course.category}
              </div>
            )}
            {course.featured && (
              <div className="px-3 py-1.5 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-xs font-medium">
                Öne Çıkan
              </div>
            )}
          </div>
          <p className="text-gray-700 dark:text-gray-300 text-base sm:text-lg">{course.description}</p>


        </div>
      </div>

      {/* Eğitim İçeriği */}
      <div className="bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden">
        <div className="p-8">
          {/* Eğitim İçeriği */}
          {course.content ? (
            <div className="prose prose-lg dark:prose-invert max-w-none prose-headings:font-bold prose-headings:text-[#FF3E71] dark:prose-headings:text-[#FF5F87] prose-a:text-[#FF3E71] dark:prose-a:text-[#FF5F87] prose-img:rounded-xl prose-img:shadow-md">
              <div dangerouslySetInnerHTML={{ __html: course.content }} />
            </div>
          ) : (
            <div className="prose prose-base dark:prose-invert max-w-none prose-headings:font-bold prose-headings:text-[#FF3E71] dark:prose-headings:text-[#FF5F87] prose-a:text-[#FF3E71] dark:prose-a:text-[#FF5F87] prose-img:rounded-xl prose-img:shadow-md">
              <p className="text-gray-500 dark:text-gray-400 italic text-center py-10 text-lg">Bu eğitim için henüz içerik eklenmemiş.</p>
            </div>
          )}
        </div>
      </div>

      {/* Navigasyon Butonları - Ayrı bir bölüm olarak */}
      <div className="bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden mt-6 p-6">
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
          <Link
            to="/dashboard/courses"
            className="inline-flex items-center px-5 py-3 rounded-lg text-sm font-medium bg-white dark:bg-[#16151c] text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 border border-gray-200 dark:border-gray-700 w-full sm:w-auto justify-center order-3 sm:order-1"
          >
            <FaArrowLeft className="mr-2" size={14} />
            <span>Tüm Eğitimlere Dön</span>
          </Link>

          <div className="flex flex-row gap-4 w-full sm:w-auto order-1 sm:order-2 sm:ml-auto">
            <Link
              to={prevCourseId !== null ? `/dashboard/courses/${prevCourseId}` : '#'}
              onClick={(e) => {
                if (prevCourseId === null) {
                  e.preventDefault();
                } else {
                  goToPrevCourse();
                }
              }}
              className={`flex items-center px-5 py-3 rounded-lg text-sm font-medium transition-colors justify-center ${
                prevCourseId === null
                  ? 'bg-gray-100 text-gray-400 dark:bg-gray-800 dark:text-gray-500 cursor-not-allowed'
                  : 'bg-[#FF3E71] text-white hover:bg-[#FF5F87]'
              }`}
              aria-disabled={prevCourseId === null}
            >
              <FaChevronLeft className="mr-2" size={14} />
              <span>Önceki Eğitim</span>
            </Link>

            <Link
              to={nextCourseId !== null ? `/dashboard/courses/${nextCourseId}` : '#'}
              onClick={(e) => {
                if (nextCourseId === null) {
                  e.preventDefault();
                } else {
                  goToNextCourse();
                }
              }}
              className={`flex items-center px-5 py-3 rounded-lg text-sm font-medium transition-colors justify-center ${
                nextCourseId === null
                  ? 'bg-gray-100 text-gray-400 dark:bg-gray-800 dark:text-gray-500 cursor-not-allowed'
                  : 'bg-[#FF3E71] text-white hover:bg-[#FF5F87]'
              }`}
              aria-disabled={nextCourseId === null}
            >
              <span>Sonraki Eğitim</span>
              <FaChevronRight className="ml-2" size={14} />
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourseDetail;