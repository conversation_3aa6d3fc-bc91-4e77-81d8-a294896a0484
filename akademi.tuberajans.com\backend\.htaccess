# PHP handler
AddHandler application/x-httpd-php .php

# Enable rewrite module
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /backend/

    # CORS Headers
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header set Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization"

    # Handle OPTIONS method
    RewriteCond %{REQUEST_METHOD} OPTIONS
    RewriteRule ^(.*)$ $1 [R=200,L]

    # API endpoints - Tüm API isteklerini /api dizinine yönlendir
    RewriteRule ^api/(.*)$ api/$1 [L,QSA]
</IfModule>

# Error handling
ErrorDocument 500 '{"status":"error","message":"Internal Server Error"}'
ErrorDocument 404 '{"status":"error","message":"Not Found"}'

# PHP settings
<IfModule mod_php.c>
    php_flag display_errors On
    php_value error_reporting E_ALL
</IfModule>
