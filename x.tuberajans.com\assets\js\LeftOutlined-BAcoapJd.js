import{r as a}from"./vendor-CnpYymF8.js";import{ae as s}from"./antd-BfejY-CV.js";import{I as f}from"./App-BJoNc_gH.js";function o(){return o=Object.assign?Object.assign.bind():function(r){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&(r[e]=n[e])}return r},o.apply(this,arguments)}const i=(r,t)=>a.createElement(f,o({},r,{ref:t,icon:s})),l=a.forwardRef(i);export{l as R};
