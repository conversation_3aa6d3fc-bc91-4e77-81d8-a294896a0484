<?php
header('Content-Type: application/json; charset=utf-8');
require_once __DIR__ . '/../config/config.php';

// Google Places API'den veri çekme
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    if (isset($input['action']) && $input['action'] === 'search') {
        $query = $input['query'] ?? '';
        $location = $input['location'] ?? '';
        $category_code = $input['category_code'] ?? '';
        $apiKey = 'AIzaSyD5mlaD_xPZDJ2g6xV748NCRTZACVcnnHw'; // Buraya kendi API anahtarını koy

        $search = urlencode(trim($query . ' ' . $location));
        $baseUrl = "https://maps.googleapis.com/maps/api/place/textsearch/json?query=$search&key=$apiKey&language=tr";

        $places = [];
        $page = 0;
        $nextPageToken = null;
        do {
            $url = $baseUrl;
            if ($nextPageToken) {
                $url .= "&pagetoken=$nextPageToken";
                // Google'ın next_page_token'ı aktifleşmesi için kısa bir bekleme gerekir
                usleep(2000000); // 2 saniye bekle
            }
            $response = file_get_contents($url);
            $data = json_decode($response, true);
            if (isset($data['results'])) {
                foreach ($data['results'] as $place) {
                    if (!isset($place['types']) || !in_array('establishment', $place['types'])) continue;
                    $places[] = [
                        'place_id' => $place['place_id'],
                        'name' => $place['name'],
                        'address' => $place['formatted_address'] ?? '',
                        'phone' => '', // Detay için ek API gerekir
                        'website' => '',
                        'email' => '',
                        'category' => $category_code ?: $query
                    ];
                }
            }
            $nextPageToken = $data['next_page_token'] ?? null;
            $page++;
        } while ($nextPageToken && $page < 3);

        echo json_encode(['success' => true, 'places' => $places, 'message' => count($places) . ' sonuç bulundu.']);
        exit;
    }
}

$pdo = $db_takip;

try {
    $stmt = $pdo->query("SELECT id, name, address, phone, website, email, category FROM business_db ORDER BY id DESC");
    $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo json_encode(['success' => true, 'data' => $data]);
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'Veriler alınamadı', 'error' => $e->getMessage()]);
} 