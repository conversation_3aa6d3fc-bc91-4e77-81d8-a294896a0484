<?php
/**
 * Etkinlik Kategori Yönetimi API
 * Bu API etkinlik kategorilerini listelemek, eklemek, düzenlemek ve silmek için kullanılır
 */

// CORS için header ayarları
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, DELETE, PUT, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json; charset=UTF-8");

// OPTIONS isteği için erken yanıt ver
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Config dosyasını dahil et
require_once __DIR__ . '/../config/config.php';

// İstek methodunu al
$method = $_SERVER['REQUEST_METHOD'];

// Veritabanı bağlantısı
try {
    if (!isset($db_akademi)) {
        $db = new PDO("mysql:host=$servername;dbname=tuberaja_yayinci_akademi;charset=utf8mb4", $db_username, $db_password);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    } else {
        $db = $db_akademi;
    }
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Veritabanı bağlantı hatası: ' . $e->getMessage()]);
    exit;
}

// Test verisi ekleme endpoint'i
if (isset($_GET['action']) && $_GET['action'] === 'add_test_data') {
    try {
        // Test etkinlikleri ekle
        $testEvents = [
            [
                'title' => 'Algoritma Güncelleme Webinarı',
                'description' => 'TikTok algoritmasındaki son güncellemeler hakkında bilgi',
                'start_date' => '2024-05-24 14:00:00',
                'end_date' => '2024-05-24 16:00:00',
                'location' => 'Online',
                'capacity' => 100,
                'category' => 'Webinar',
                'status' => 'active'
            ],
            [
                'title' => 'İçerik Üretimi Atölyesi',
                'description' => 'Yaratıcı içerik üretme teknikleri',
                'start_date' => '2024-05-25 10:00:00',
                'end_date' => '2024-05-25 17:00:00',
                'location' => 'İstanbul Ofis',
                'capacity' => 30,
                'category' => 'Atölye',
                'status' => 'active'
            ]
        ];

        foreach ($testEvents as $event) {
            $stmt = $db->prepare("
                INSERT INTO events (title, description, start_date, end_date, location, capacity, category, status, created_at, updated_at, created_by)
                VALUES (:title, :description, :start_date, :end_date, :location, :capacity, :category, :status, NOW(), NOW(), 1)
            ");
            $stmt->execute([
                ':title' => $event['title'],
                ':description' => $event['description'],
                ':start_date' => $event['start_date'],
                ':end_date' => $event['end_date'],
                ':location' => $event['location'],
                ':capacity' => $event['capacity'],
                ':category' => $event['category'],
                ':status' => $event['status']
            ]);
        }

        echo json_encode(['success' => true, 'message' => 'Test verileri başarıyla eklendi']);
        exit;
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'message' => 'Test verisi ekleme hatası: ' . $e->getMessage()]);
        exit;
    }
}

// GET isteği - Kategorileri Listele
if ($method === 'GET') {
    try {
        // Debug için log ekle
        error_log("Etkinlik kategorileri API çağrısı yapıldı");
        
        // Önce events tablosunda kaç kayıt olduğunu kontrol et
        $countStmt = $db->prepare("SELECT COUNT(*) FROM events");
        $countStmt->execute();
        $eventCount = $countStmt->fetchColumn();
        error_log("Events tablosunda toplam kayıt sayısı: " . $eventCount);
        
        // Veritabanından mevcut kategorileri al
        $stmt = $db->prepare("SELECT DISTINCT category FROM events WHERE category IS NOT NULL AND category != ''");
        $stmt->execute();
        $dbCategories = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // Debug için log ekle
        error_log("Veritabanından çekilen etkinlik kategorileri: " . json_encode($dbCategories));

        // Sabit kategoriler
        $defaultCategories = [
            ['id' => 1, 'name' => 'Webinar', 'value' => 'Webinar'],
            ['id' => 2, 'name' => 'Seminer', 'value' => 'Seminer'],
            ['id' => 3, 'name' => 'Buluşma', 'value' => 'Buluşma'],
            ['id' => 4, 'name' => 'Atölye', 'value' => 'Atölye'],
            ['id' => 5, 'name' => 'Eğitim', 'value' => 'Eğitim'],
            ['id' => 6, 'name' => 'Diğer', 'value' => 'Diğer']
        ];

        // Veritabanından gelen kategorileri ekle
        $allCategories = $defaultCategories;
        $existingValues = array_column($defaultCategories, 'value');

        foreach ($dbCategories as $category) {
            if (!in_array($category, $existingValues)) {
                $allCategories[] = [
                    'id' => count($allCategories) + 1,
                    'name' => $category,
                    'value' => $category
                ];
            }
        }

        // Her kategori için etkinlik sayısını hesapla
        foreach ($allCategories as &$category) {
            $stmt = $db->prepare("SELECT COUNT(*) FROM events WHERE category = ?");
            $stmt->execute([$category['value']]);
            $category['event_count'] = $stmt->fetchColumn();
        }

        // Debug için log ekle
        error_log("Gönderilen etkinlik kategorileri: " . json_encode($allCategories));
        
        echo json_encode(['success' => true, 'data' => $allCategories]);
    } catch (PDOException $e) {
        error_log("Etkinlik kategori API hatası: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Veritabanı hatası: ' . $e->getMessage()]);
    }
}

// POST isteği - Kategori İşlemleri
else if ($method === 'POST') {
    $action = $_GET['action'] ?? 'add';

    // JSON verilerini al
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        $input = $_POST;
    }

    if ($action === 'add') {
        // Kategori Ekle
        if (!isset($input['name']) || !isset($input['value'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Eksik alanlar: name ve value gerekli.']);
            exit;
        }

        try {
            // Kategori değerinin benzersiz olup olmadığını kontrol et
            $stmt = $db->prepare("SELECT COUNT(*) FROM events WHERE category = ?");
            $stmt->execute([$input['value']]);
            $exists = $stmt->fetchColumn() > 0;

            if ($exists) {
                echo json_encode(['success' => false, 'message' => 'Bu kategori zaten mevcut.']);
                exit;
            }

            // Yeni kategori başarıyla eklendi mesajı
            echo json_encode(['success' => true, 'message' => 'Kategori başarıyla eklendi.']);
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Veritabanı hatası: ' . $e->getMessage()]);
        }
    }

    else if ($action === 'update') {
        // Kategori Güncelle
        if (!isset($input['old_value']) || !isset($input['new_value']) || !isset($input['name'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Eksik alanlar: old_value, new_value ve name gerekli.']);
            exit;
        }

        try {
            // Eski kategori değerini yeni değerle güncelle
            $stmt = $db->prepare("UPDATE events SET category = ? WHERE category = ?");
            $stmt->execute([$input['new_value'], $input['old_value']]);

            echo json_encode(['success' => true, 'message' => 'Kategori başarıyla güncellendi.']);
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Veritabanı hatası: ' . $e->getMessage()]);
        }
    }

    else if ($action === 'delete') {
        // Kategori Sil
        if (!isset($input['value'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Eksik alan: value gerekli.']);
            exit;
        }

        try {
            // Bu kategorideki etkinlikleri 'Diğer' kategorisine taşı
            $stmt = $db->prepare("UPDATE events SET category = 'Diğer' WHERE category = ?");
            $stmt->execute([$input['value']]);

            echo json_encode(['success' => true, 'message' => 'Kategori silindi ve etkinlikler Diğer kategorisine taşındı.']);
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Veritabanı hatası: ' . $e->getMessage()]);
        }
    }

    else {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Desteklenmeyen action parametresi: ' . $action]);
    }
}

// Desteklenmeyen istek metodu
else {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Desteklenmeyen HTTP metodu.']);
}
?>
