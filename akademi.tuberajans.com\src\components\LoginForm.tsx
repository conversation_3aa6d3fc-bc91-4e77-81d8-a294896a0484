import React, { useState } from 'react';
import { <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>a<PERSON>ye, FaEyeSlash } from 'react-icons/fa';

const LoginForm: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log({ username, password, rememberMe });
    // Giriş işlemi burada yapılacak
  };

  // TikTok ile giriş yap fonksiyonu
  const handleTikTokLogin = () => {
    console.log("TikTok login tıklandı");
    const clientKey = 'awfw8k9nim1e8dmu';
    const redirectUri = encodeURIComponent('https://akademi.tuberajans.com/backend/api/tiktok-callback.php');
    const state = Math.random().toString(36).substring(2, 15);
    const scope = 'user.info.basic,user.info.profile,user.info.stats,video.list';
    const url = `https://www.tiktok.com/v2/auth/authorize/?client_key=${clientKey}&response_type=code&scope=${scope}&redirect_uri=${redirectUri}&state=${state}`;
    window.location.href = url;
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-6 w-full max-w-md">
      <div className="text-center mb-4">
        <h2 className="text-tuber-pink text-xs font-medium uppercase tracking-wide">PORTAL GİRİŞİ</h2>
        <h1 className="text-gray-800 text-2xl font-bold mt-1">Hoş Geldiniz!</h1>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="tiktok-username" className="block text-sm font-medium text-gray-700 mb-1">
            TikTok Kullanıcı Adı
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FaTiktok className="h-4 w-4 text-gray-400" />
            </div>
            <input
              id="tiktok-username"
              name="username"
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              required
              className="block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-1 focus:ring-tuber-pink focus:border-tuber-pink text-sm"
              placeholder="Kullanıcı adınızı girin"
            />
          </div>
        </div>

        <div>
          <div className="flex justify-between items-center mb-1">
            <label htmlFor="password" className="block text-sm font-medium text-gray-700">
              Şifre
            </label>
            <div className="text-xs">
              <a href="#" className="font-medium text-tuber-pink hover:text-tuber-purple">
                Şifremi Unuttum
              </a>
            </div>
          </div>
          <div className="relative">
            <input
              id="password"
              name="password"
              type={showPassword ? "text" : "password"}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="block w-full pr-10 py-2 px-3 border border-gray-200 rounded-md focus:outline-none focus:ring-1 focus:ring-tuber-pink focus:border-tuber-pink text-sm"
              placeholder="Şifrenizi girin"
            />
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="text-gray-400 hover:text-gray-600 focus:outline-none"
              >
                {showPassword ? <FaEyeSlash className="h-4 w-4" /> : <FaEye className="h-4 w-4" />}
              </button>
            </div>
          </div>
        </div>

        <div className="flex items-center">
          <input
            id="remember-me"
            name="remember-me"
            type="checkbox"
            checked={rememberMe}
            onChange={(e) => setRememberMe(e.target.checked)}
            className="h-4 w-4 text-tuber-pink focus:ring-tuber-pink border-gray-300 rounded"
          />
          <label htmlFor="remember-me" className="ml-2 block text-xs text-gray-700">
            Beni Hatırla
          </label>
        </div>

        <button
          type="submit"
          className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-tuber-pink hover:bg-tuber-pink/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-tuber-pink"
        >
          Giriş Yap
        </button>

        <div className="relative mt-4">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-200"></div>
          </div>
          <div className="relative flex justify-center text-xs">
            <span className="px-2 bg-white text-gray-500">veya</span>
          </div>
        </div>

        <button
          type="button"
          onClick={handleTikTokLogin}
          className="w-full flex items-center justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-tuber-pink"
        >
          <FaTiktok className="h-4 w-4 text-black mr-2" />
          TikTok ile Giriş Yap
        </button>
      </form>
    </div>
  );
};

export default LoginForm;