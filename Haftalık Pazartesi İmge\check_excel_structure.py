import pandas as pd
import json

# Excel dosyalarını oku
new_df = pd.read_excel('Tuber Medya.xlsx')
old_df = pd.read_excel('Haftalik_Ajans_Gorevleri.xlsx')

# Sütunları ve ilk satırları karşılaştır
result = {
    "new_columns": new_df.columns.tolist(),
    "old_columns": old_df.columns.tolist(),
    "new_data_sample": new_df.head(3).to_dict('records'),
    "old_data_sample": old_df.head(3).to_dict('records')
}

# Sonucu JSON formatında kaydet
with open('excel_comparison.json', 'w', encoding='utf-8') as f:
    json.dump(result, f, ensure_ascii=False, indent=4)

print("Karşılaştırma tamamlandı. Sonuçlar excel_comparison.json dosyasına kaydedildi.") 