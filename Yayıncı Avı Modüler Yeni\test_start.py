import pymysql
import json
from dotenv import load_dotenv
import os

# .env dosyasını yükle
load_dotenv()

DB_CONFIG = {
    'host': os.getenv('DB_HOST', os.getenv('MYSQL_HOST', 'localhost')),
    'user': os.getenv('DB_USER', os.getenv('MYSQL_USER', 'root')),
    'password': os.getenv('DB_PASSWORD', os.getenv('MYSQL_PASSWORD', '')),
    'database': os.getenv('DB_NAME', os.getenv('MYSQL_DATABASE', 'tiktok_live_data')),
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

try:
    conn = pymysql.connect(**DB_CONFIG)
    with conn.cursor() as cursor:
        params = json.dumps({'duration': 1, 'headless': False, 'messageMode': 'both'})
        cursor.execute('''
            INSERT INTO automation_commands (command, params, status, created_at) 
            VALUES (%s, %s, %s, NOW())
        ''', ('start', params, 'pending'))
    conn.commit()
    conn.close()
    print('✅ Start komutu eklendi')
except Exception as e:
    print(f'❌ Hata: {e}')
