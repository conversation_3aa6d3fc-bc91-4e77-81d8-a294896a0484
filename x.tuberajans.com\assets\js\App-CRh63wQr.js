const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/Dashboard-B4Obnw4d.js","assets/js/reactDnd-CIvPAkL_.js","assets/js/vendor-CnpYymF8.js","assets/js/utils-CtuI0RRe.js","assets/js/antd-gS---Efz.js","assets/js/charts-CXWFy-zF.js","assets/js/index-CA4FAjHu.js","assets/css/index-CSFqLbjD.css","assets/js/Publishers-CTlyjTfy.js","assets/js/x-BlF-lTk7.js","assets/js/createLucideIcon-DxVmGoQf.js","assets/js/EllipsisOutlined-Rck7ed4L.js","assets/js/DownloadOutlined-Bul1yQYQ.js","assets/js/UploadOutlined-DzQJU1Qb.js","assets/js/plus-DjpIx7VF.js","assets/js/Influencers-70hmI_5J.js","assets/js/api-Db9sI4AU.js","assets/js/trash-2-C8zHBfrV.js","assets/js/ContentCreators-X0fNTlV-.js","assets/js/PublisherPerformance-_-pF5EqC.js","assets/js/InfoCircleOutlined-u-fVVoWX.js","assets/js/tr-CwGFhkM0.js","assets/js/MoreOutlined-a3avmhyg.js","assets/js/eye-C0V96B8t.js","assets/js/AIAdvisor-tp1PB3a7.js","assets/js/tr-Bk4hK31u.js","assets/js/EventManagement-BtmkU9Rt.js","assets/js/event-api-B3F_YUMu.js","assets/js/AIEventAdvisor-9LfF0ZBE.js","assets/js/SaveOutlined-CORN5D0D.js","assets/js/FireOutlined-CFlVzAHI.js","assets/js/ExclamationCircleOutlined-BSxONRW4.js","assets/js/PKMatcher-BzjVSg2y.js","assets/js/NotFound-DoxyyHuC.js","assets/js/EventDetail-DHBhnmmb.js","assets/js/ThemeStyles-Ct0n9BwN.js","assets/js/LeftOutlined-BO5YFVcA.js","assets/js/EventWizard-D44LBqfP.js","assets/js/ArrowLeftOutlined-CecmKsrg.js","assets/js/Performance-BinvSQbn.js","assets/js/TournamentBracket-BjzZw5y1.js","assets/js/Unauthorized-Db9tJymV.js","assets/js/Dashboard-DMWGOHDy.js","assets/js/TasarimOnayi-DEyVG0P1.js","assets/js/Urunler-oTjyzi3X.js","assets/js/Ayarlar-BIRGft4J.js","assets/js/WhatsAppManager-D7FOKXCv.js","assets/css/WhatsAppManager-07LZlDFG.css"])))=>i.map(i=>d[i]);
var jf=Object.defineProperty;var vf=(t,s,r)=>s in t?jf(t,s,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[s]=r;var kt=(t,s,r)=>vf(t,typeof s!="symbol"?s+"":s,r);import{A as Be,S as Ze,a as kf,C as wf,_ as tt}from"./index-CA4FAjHu.js";import{j as e}from"./reactDnd-CIvPAkL_.js";import{r as u,u as _a,g as Sf,o as cn,h as $o,O as ws,L as ls,i as Bo,j as Se,N as ba}from"./vendor-CnpYymF8.js";import{c as _f,C as Tf,a as Cf,b as Of,D as zf,E as Rf,d as Ef,F as Df,L as Mf,P as Af,Q as If,R as Pf,S as Lf,e as Nf,f as Yf,g as fa,M as $f,B as Ge,h as or,i as I,j as dn,k as ce,A as $a,l as q,T as ht,m as O,I as me,n as Ua,o as Xe,p as qe,q as St,s as Ce,t as Fo,u as Ba,v as S,w as it,x as ae,y as ke,z as N,U as Ni,G as Fe,H as _t,J as wa,K as Ho,N as Pl,O as Bf,V as Ta,W as Te,X as ys,Y as Zt,Z as mt,_ as Ff,$ as Ll,a0 as Hf}from"./antd-gS---Efz.js";import{h as xt}from"./utils-CtuI0RRe.js";import{R as Wf,P as Uf,a as Vf,C as Kf,T as Gf,L as qf}from"./charts-CXWFy-zF.js";const Wo=u.createContext(void 0);function Jf({children:t}){const[s,r]=u.useState(()=>{try{const c=localStorage.getItem("darkMode");return c?JSON.parse(c):!1}catch(c){return!1}});u.useEffect(()=>{try{localStorage.setItem("darkMode",JSON.stringify(s)),s?(document.documentElement.classList.add("dark"),document.body.style.backgroundColor="#0f172a"):(document.documentElement.classList.remove("dark"),document.body.style.backgroundColor="#f3f4f6");const c=document.getElementById("root");c&&(c.dataset.theme=s?"dark":"light")}catch(c){}},[s]);const d={darkMode:s,toggleDarkMode:()=>{r(!s)}};return e.jsx(Wo.Provider,{value:d,children:t})}function fr(){const t=u.useContext(Wo);if(t===void 0)throw new Error("useTheme must be used within a ThemeProvider");return t}function Uo(t,s){return function(){return t.apply(s,arguments)}}const{toString:Zf}=Object.prototype,{getPrototypeOf:Yi}=Object,mr=(t=>s=>{const r=Zf.call(s);return t[r]||(t[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),Pt=t=>(t=t.toLowerCase(),s=>mr(s)===t),pr=t=>s=>typeof s===t,{isArray:Ka}=Array,Ss=pr("undefined");function Xf(t){return t!==null&&!Ss(t)&&t.constructor!==null&&!Ss(t.constructor)&&Tt(t.constructor.isBuffer)&&t.constructor.isBuffer(t)}const Vo=Pt("ArrayBuffer");function Qf(t){let s;return typeof ArrayBuffer!="undefined"&&ArrayBuffer.isView?s=ArrayBuffer.isView(t):s=t&&t.buffer&&Vo(t.buffer),s}const em=pr("string"),Tt=pr("function"),Ko=pr("number"),gr=t=>t!==null&&typeof t=="object",tm=t=>t===!0||t===!1,er=t=>{if(mr(t)!=="object")return!1;const s=Yi(t);return(s===null||s===Object.prototype||Object.getPrototypeOf(s)===null)&&!(Symbol.toStringTag in t)&&!(Symbol.iterator in t)},am=Pt("Date"),sm=Pt("File"),rm=Pt("Blob"),nm=Pt("FileList"),im=t=>gr(t)&&Tt(t.pipe),lm=t=>{let s;return t&&(typeof FormData=="function"&&t instanceof FormData||Tt(t.append)&&((s=mr(t))==="formdata"||s==="object"&&Tt(t.toString)&&t.toString()==="[object FormData]"))},om=Pt("URLSearchParams"),[cm,dm,um,hm]=["ReadableStream","Request","Response","Headers"].map(Pt),fm=t=>t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Ts(t,s,{allOwnKeys:r=!1}={}){if(t===null||typeof t=="undefined")return;let n,d;if(typeof t!="object"&&(t=[t]),Ka(t))for(n=0,d=t.length;n<d;n++)s.call(null,t[n],n,t);else{const c=r?Object.getOwnPropertyNames(t):Object.keys(t),h=c.length;let m;for(n=0;n<h;n++)m=c[n],s.call(null,t[m],m,t)}}function Go(t,s){s=s.toLowerCase();const r=Object.keys(t);let n=r.length,d;for(;n-- >0;)if(d=r[n],s===d.toLowerCase())return d;return null}const ja=typeof globalThis!="undefined"?globalThis:typeof self!="undefined"?self:typeof window!="undefined"?window:global,qo=t=>!Ss(t)&&t!==ja;function un(){const{caseless:t}=qo(this)&&this||{},s={},r=(n,d)=>{const c=t&&Go(s,d)||d;er(s[c])&&er(n)?s[c]=un(s[c],n):er(n)?s[c]=un({},n):Ka(n)?s[c]=n.slice():s[c]=n};for(let n=0,d=arguments.length;n<d;n++)arguments[n]&&Ts(arguments[n],r);return s}const mm=(t,s,r,{allOwnKeys:n}={})=>(Ts(s,(d,c)=>{r&&Tt(d)?t[c]=Uo(d,r):t[c]=d},{allOwnKeys:n}),t),pm=t=>(t.charCodeAt(0)===65279&&(t=t.slice(1)),t),gm=(t,s,r,n)=>{t.prototype=Object.create(s.prototype,n),t.prototype.constructor=t,Object.defineProperty(t,"super",{value:s.prototype}),r&&Object.assign(t.prototype,r)},xm=(t,s,r,n)=>{let d,c,h;const m={};if(s=s||{},t==null)return s;do{for(d=Object.getOwnPropertyNames(t),c=d.length;c-- >0;)h=d[c],(!n||n(h,t,s))&&!m[h]&&(s[h]=t[h],m[h]=!0);t=r!==!1&&Yi(t)}while(t&&(!r||r(t,s))&&t!==Object.prototype);return s},ym=(t,s,r)=>{t=String(t),(r===void 0||r>t.length)&&(r=t.length),r-=s.length;const n=t.indexOf(s,r);return n!==-1&&n===r},bm=t=>{if(!t)return null;if(Ka(t))return t;let s=t.length;if(!Ko(s))return null;const r=new Array(s);for(;s-- >0;)r[s]=t[s];return r},jm=(t=>s=>t&&s instanceof t)(typeof Uint8Array!="undefined"&&Yi(Uint8Array)),vm=(t,s)=>{const n=(t&&t[Symbol.iterator]).call(t);let d;for(;(d=n.next())&&!d.done;){const c=d.value;s.call(t,c[0],c[1])}},km=(t,s)=>{let r;const n=[];for(;(r=t.exec(s))!==null;)n.push(r);return n},wm=Pt("HTMLFormElement"),Sm=t=>t.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,n,d){return n.toUpperCase()+d}),Nl=(({hasOwnProperty:t})=>(s,r)=>t.call(s,r))(Object.prototype),_m=Pt("RegExp"),Jo=(t,s)=>{const r=Object.getOwnPropertyDescriptors(t),n={};Ts(r,(d,c)=>{let h;(h=s(d,c,t))!==!1&&(n[c]=h||d)}),Object.defineProperties(t,n)},Tm=t=>{Jo(t,(s,r)=>{if(Tt(t)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const n=t[r];if(Tt(n)){if(s.enumerable=!1,"writable"in s){s.writable=!1;return}s.set||(s.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},Cm=(t,s)=>{const r={},n=d=>{d.forEach(c=>{r[c]=!0})};return Ka(t)?n(t):n(String(t).split(s)),r},Om=()=>{},zm=(t,s)=>t!=null&&Number.isFinite(t=+t)?t:s;function Rm(t){return!!(t&&Tt(t.append)&&t[Symbol.toStringTag]==="FormData"&&t[Symbol.iterator])}const Em=t=>{const s=new Array(10),r=(n,d)=>{if(gr(n)){if(s.indexOf(n)>=0)return;if(!("toJSON"in n)){s[d]=n;const c=Ka(n)?[]:{};return Ts(n,(h,m)=>{const y=r(h,d+1);!Ss(y)&&(c[m]=y)}),s[d]=void 0,c}}return n};return r(t,0)},Dm=Pt("AsyncFunction"),Mm=t=>t&&(gr(t)||Tt(t))&&Tt(t.then)&&Tt(t.catch),Zo=((t,s)=>t?setImmediate:s?((r,n)=>(ja.addEventListener("message",({source:d,data:c})=>{d===ja&&c===r&&n.length&&n.shift()()},!1),d=>{n.push(d),ja.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",Tt(ja.postMessage)),Am=typeof queueMicrotask!="undefined"?queueMicrotask.bind(ja):typeof process!="undefined"&&process.nextTick||Zo,z={isArray:Ka,isArrayBuffer:Vo,isBuffer:Xf,isFormData:lm,isArrayBufferView:Qf,isString:em,isNumber:Ko,isBoolean:tm,isObject:gr,isPlainObject:er,isReadableStream:cm,isRequest:dm,isResponse:um,isHeaders:hm,isUndefined:Ss,isDate:am,isFile:sm,isBlob:rm,isRegExp:_m,isFunction:Tt,isStream:im,isURLSearchParams:om,isTypedArray:jm,isFileList:nm,forEach:Ts,merge:un,extend:mm,trim:fm,stripBOM:pm,inherits:gm,toFlatObject:xm,kindOf:mr,kindOfTest:Pt,endsWith:ym,toArray:bm,forEachEntry:vm,matchAll:km,isHTMLForm:wm,hasOwnProperty:Nl,hasOwnProp:Nl,reduceDescriptors:Jo,freezeMethods:Tm,toObjectSet:Cm,toCamelCase:Sm,noop:Om,toFiniteNumber:zm,findKey:Go,global:ja,isContextDefined:qo,isSpecCompliantForm:Rm,toJSONObject:Em,isAsyncFn:Dm,isThenable:Mm,setImmediate:Zo,asap:Am};function ze(t,s,r,n,d){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=t,this.name="AxiosError",s&&(this.code=s),r&&(this.config=r),n&&(this.request=n),d&&(this.response=d,this.status=d.status?d.status:null)}z.inherits(ze,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:z.toJSONObject(this.config),code:this.code,status:this.status}}});const Xo=ze.prototype,Qo={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(t=>{Qo[t]={value:t}});Object.defineProperties(ze,Qo);Object.defineProperty(Xo,"isAxiosError",{value:!0});ze.from=(t,s,r,n,d,c)=>{const h=Object.create(Xo);return z.toFlatObject(t,h,function(y){return y!==Error.prototype},m=>m!=="isAxiosError"),ze.call(h,t.message,s,r,n,d),h.cause=t,h.name=t.name,c&&Object.assign(h,c),h};const Im=null;function hn(t){return z.isPlainObject(t)||z.isArray(t)}function ec(t){return z.endsWith(t,"[]")?t.slice(0,-2):t}function Yl(t,s,r){return t?t.concat(s).map(function(d,c){return d=ec(d),!r&&c?"["+d+"]":d}).join(r?".":""):s}function Pm(t){return z.isArray(t)&&!t.some(hn)}const Lm=z.toFlatObject(z,{},null,function(s){return/^is[A-Z]/.test(s)});function xr(t,s,r){if(!z.isObject(t))throw new TypeError("target must be an object");s=s||new FormData,r=z.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(w,g){return!z.isUndefined(g[w])});const n=r.metaTokens,d=r.visitor||j,c=r.dots,h=r.indexes,y=(r.Blob||typeof Blob!="undefined"&&Blob)&&z.isSpecCompliantForm(s);if(!z.isFunction(d))throw new TypeError("visitor must be a function");function p(_){if(_===null)return"";if(z.isDate(_))return _.toISOString();if(!y&&z.isBlob(_))throw new ze("Blob is not supported. Use a Buffer instead.");return z.isArrayBuffer(_)||z.isTypedArray(_)?y&&typeof Blob=="function"?new Blob([_]):Buffer.from(_):_}function j(_,w,g){let H=_;if(_&&!g&&typeof _=="object"){if(z.endsWith(w,"{}"))w=n?w:w.slice(0,-2),_=JSON.stringify(_);else if(z.isArray(_)&&Pm(_)||(z.isFileList(_)||z.endsWith(w,"[]"))&&(H=z.toArray(_)))return w=ec(w),H.forEach(function(T,W){!(z.isUndefined(T)||T===null)&&s.append(h===!0?Yl([w],W,c):h===null?w:w+"[]",p(T))}),!1}return hn(_)?!0:(s.append(Yl(g,w,c),p(_)),!1)}const x=[],b=Object.assign(Lm,{defaultVisitor:j,convertValue:p,isVisitable:hn});function C(_,w){if(!z.isUndefined(_)){if(x.indexOf(_)!==-1)throw Error("Circular reference detected in "+w.join("."));x.push(_),z.forEach(_,function(H,U){(!(z.isUndefined(H)||H===null)&&d.call(s,H,z.isString(U)?U.trim():U,w,b))===!0&&C(H,w?w.concat(U):[U])}),x.pop()}}if(!z.isObject(t))throw new TypeError("data must be an object");return C(t),s}function $l(t){const s={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(t).replace(/[!'()~]|%20|%00/g,function(n){return s[n]})}function $i(t,s){this._pairs=[],t&&xr(t,this,s)}const tc=$i.prototype;tc.append=function(s,r){this._pairs.push([s,r])};tc.toString=function(s){const r=s?function(n){return s.call(this,n,$l)}:$l;return this._pairs.map(function(d){return r(d[0])+"="+r(d[1])},"").join("&")};function Nm(t){return encodeURIComponent(t).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ac(t,s,r){if(!s)return t;const n=r&&r.encode||Nm;z.isFunction(r)&&(r={serialize:r});const d=r&&r.serialize;let c;if(d?c=d(s,r):c=z.isURLSearchParams(s)?s.toString():new $i(s,r).toString(n),c){const h=t.indexOf("#");h!==-1&&(t=t.slice(0,h)),t+=(t.indexOf("?")===-1?"?":"&")+c}return t}class Bl{constructor(){this.handlers=[]}use(s,r,n){return this.handlers.push({fulfilled:s,rejected:r,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(s){this.handlers[s]&&(this.handlers[s]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(s){z.forEach(this.handlers,function(n){n!==null&&s(n)})}}const sc={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Ym=typeof URLSearchParams!="undefined"?URLSearchParams:$i,$m=typeof FormData!="undefined"?FormData:null,Bm=typeof Blob!="undefined"?Blob:null,Fm={isBrowser:!0,classes:{URLSearchParams:Ym,FormData:$m,Blob:Bm},protocols:["http","https","file","blob","url","data"]},Bi=typeof window!="undefined"&&typeof document!="undefined",fn=typeof navigator=="object"&&navigator||void 0,Hm=Bi&&(!fn||["ReactNative","NativeScript","NS"].indexOf(fn.product)<0),Wm=typeof WorkerGlobalScope!="undefined"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Um=Bi&&window.location.href||"http://localhost",Vm=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Bi,hasStandardBrowserEnv:Hm,hasStandardBrowserWebWorkerEnv:Wm,navigator:fn,origin:Um},Symbol.toStringTag,{value:"Module"})),ut={...Vm,...Fm};function Km(t,s){return xr(t,new ut.classes.URLSearchParams,Object.assign({visitor:function(r,n,d,c){return ut.isNode&&z.isBuffer(r)?(this.append(n,r.toString("base64")),!1):c.defaultVisitor.apply(this,arguments)}},s))}function Gm(t){return z.matchAll(/\w+|\[(\w*)]/g,t).map(s=>s[0]==="[]"?"":s[1]||s[0])}function qm(t){const s={},r=Object.keys(t);let n;const d=r.length;let c;for(n=0;n<d;n++)c=r[n],s[c]=t[c];return s}function rc(t){function s(r,n,d,c){let h=r[c++];if(h==="__proto__")return!0;const m=Number.isFinite(+h),y=c>=r.length;return h=!h&&z.isArray(d)?d.length:h,y?(z.hasOwnProp(d,h)?d[h]=[d[h],n]:d[h]=n,!m):((!d[h]||!z.isObject(d[h]))&&(d[h]=[]),s(r,n,d[h],c)&&z.isArray(d[h])&&(d[h]=qm(d[h])),!m)}if(z.isFormData(t)&&z.isFunction(t.entries)){const r={};return z.forEachEntry(t,(n,d)=>{s(Gm(n),d,r,0)}),r}return null}function Jm(t,s,r){if(z.isString(t))try{return(s||JSON.parse)(t),z.trim(t)}catch(n){if(n.name!=="SyntaxError")throw n}return(r||JSON.stringify)(t)}const Cs={transitional:sc,adapter:["xhr","http","fetch"],transformRequest:[function(s,r){const n=r.getContentType()||"",d=n.indexOf("application/json")>-1,c=z.isObject(s);if(c&&z.isHTMLForm(s)&&(s=new FormData(s)),z.isFormData(s))return d?JSON.stringify(rc(s)):s;if(z.isArrayBuffer(s)||z.isBuffer(s)||z.isStream(s)||z.isFile(s)||z.isBlob(s)||z.isReadableStream(s))return s;if(z.isArrayBufferView(s))return s.buffer;if(z.isURLSearchParams(s))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),s.toString();let m;if(c){if(n.indexOf("application/x-www-form-urlencoded")>-1)return Km(s,this.formSerializer).toString();if((m=z.isFileList(s))||n.indexOf("multipart/form-data")>-1){const y=this.env&&this.env.FormData;return xr(m?{"files[]":s}:s,y&&new y,this.formSerializer)}}return c||d?(r.setContentType("application/json",!1),Jm(s)):s}],transformResponse:[function(s){const r=this.transitional||Cs.transitional,n=r&&r.forcedJSONParsing,d=this.responseType==="json";if(z.isResponse(s)||z.isReadableStream(s))return s;if(s&&z.isString(s)&&(n&&!this.responseType||d)){const h=!(r&&r.silentJSONParsing)&&d;try{return JSON.parse(s)}catch(m){if(h)throw m.name==="SyntaxError"?ze.from(m,ze.ERR_BAD_RESPONSE,this,null,this.response):m}}return s}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:ut.classes.FormData,Blob:ut.classes.Blob},validateStatus:function(s){return s>=200&&s<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};z.forEach(["delete","get","head","post","put","patch"],t=>{Cs.headers[t]={}});const Zm=z.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Xm=t=>{const s={};let r,n,d;return t&&t.split(`
`).forEach(function(h){d=h.indexOf(":"),r=h.substring(0,d).trim().toLowerCase(),n=h.substring(d+1).trim(),!(!r||s[r]&&Zm[r])&&(r==="set-cookie"?s[r]?s[r].push(n):s[r]=[n]:s[r]=s[r]?s[r]+", "+n:n)}),s},Fl=Symbol("internals");function os(t){return t&&String(t).trim().toLowerCase()}function tr(t){return t===!1||t==null?t:z.isArray(t)?t.map(tr):String(t)}function Qm(t){const s=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=r.exec(t);)s[n[1]]=n[2];return s}const ep=t=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(t.trim());function Wr(t,s,r,n,d){if(z.isFunction(n))return n.call(this,s,r);if(d&&(s=r),!!z.isString(s)){if(z.isString(n))return s.indexOf(n)!==-1;if(z.isRegExp(n))return n.test(s)}}function tp(t){return t.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(s,r,n)=>r.toUpperCase()+n)}function ap(t,s){const r=z.toCamelCase(" "+s);["get","set","has"].forEach(n=>{Object.defineProperty(t,n+r,{value:function(d,c,h){return this[n].call(this,s,d,c,h)},configurable:!0})})}let bt=class{constructor(s){s&&this.set(s)}set(s,r,n){const d=this;function c(m,y,p){const j=os(y);if(!j)throw new Error("header name must be a non-empty string");const x=z.findKey(d,j);(!x||d[x]===void 0||p===!0||p===void 0&&d[x]!==!1)&&(d[x||y]=tr(m))}const h=(m,y)=>z.forEach(m,(p,j)=>c(p,j,y));if(z.isPlainObject(s)||s instanceof this.constructor)h(s,r);else if(z.isString(s)&&(s=s.trim())&&!ep(s))h(Xm(s),r);else if(z.isHeaders(s))for(const[m,y]of s.entries())c(y,m,n);else s!=null&&c(r,s,n);return this}get(s,r){if(s=os(s),s){const n=z.findKey(this,s);if(n){const d=this[n];if(!r)return d;if(r===!0)return Qm(d);if(z.isFunction(r))return r.call(this,d,n);if(z.isRegExp(r))return r.exec(d);throw new TypeError("parser must be boolean|regexp|function")}}}has(s,r){if(s=os(s),s){const n=z.findKey(this,s);return!!(n&&this[n]!==void 0&&(!r||Wr(this,this[n],n,r)))}return!1}delete(s,r){const n=this;let d=!1;function c(h){if(h=os(h),h){const m=z.findKey(n,h);m&&(!r||Wr(n,n[m],m,r))&&(delete n[m],d=!0)}}return z.isArray(s)?s.forEach(c):c(s),d}clear(s){const r=Object.keys(this);let n=r.length,d=!1;for(;n--;){const c=r[n];(!s||Wr(this,this[c],c,s,!0))&&(delete this[c],d=!0)}return d}normalize(s){const r=this,n={};return z.forEach(this,(d,c)=>{const h=z.findKey(n,c);if(h){r[h]=tr(d),delete r[c];return}const m=s?tp(c):String(c).trim();m!==c&&delete r[c],r[m]=tr(d),n[m]=!0}),this}concat(...s){return this.constructor.concat(this,...s)}toJSON(s){const r=Object.create(null);return z.forEach(this,(n,d)=>{n!=null&&n!==!1&&(r[d]=s&&z.isArray(n)?n.join(", "):n)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([s,r])=>s+": "+r).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(s){return s instanceof this?s:new this(s)}static concat(s,...r){const n=new this(s);return r.forEach(d=>n.set(d)),n}static accessor(s){const n=(this[Fl]=this[Fl]={accessors:{}}).accessors,d=this.prototype;function c(h){const m=os(h);n[m]||(ap(d,h),n[m]=!0)}return z.isArray(s)?s.forEach(c):c(s),this}};bt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);z.reduceDescriptors(bt.prototype,({value:t},s)=>{let r=s[0].toUpperCase()+s.slice(1);return{get:()=>t,set(n){this[r]=n}}});z.freezeMethods(bt);function Ur(t,s){const r=this||Cs,n=s||r,d=bt.from(n.headers);let c=n.data;return z.forEach(t,function(m){c=m.call(r,c,d.normalize(),s?s.status:void 0)}),d.normalize(),c}function nc(t){return!!(t&&t.__CANCEL__)}function Ga(t,s,r){ze.call(this,t==null?"canceled":t,ze.ERR_CANCELED,s,r),this.name="CanceledError"}z.inherits(Ga,ze,{__CANCEL__:!0});function ic(t,s,r){const n=r.config.validateStatus;!r.status||!n||n(r.status)?t(r):s(new ze("Request failed with status code "+r.status,[ze.ERR_BAD_REQUEST,ze.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function sp(t){const s=/^([-+\w]{1,25})(:?\/\/|:)/.exec(t);return s&&s[1]||""}function rp(t,s){t=t||10;const r=new Array(t),n=new Array(t);let d=0,c=0,h;return s=s!==void 0?s:1e3,function(y){const p=Date.now(),j=n[c];h||(h=p),r[d]=y,n[d]=p;let x=c,b=0;for(;x!==d;)b+=r[x++],x=x%t;if(d=(d+1)%t,d===c&&(c=(c+1)%t),p-h<s)return;const C=j&&p-j;return C?Math.round(b*1e3/C):void 0}}function np(t,s){let r=0,n=1e3/s,d,c;const h=(p,j=Date.now())=>{r=j,d=null,c&&(clearTimeout(c),c=null),t.apply(null,p)};return[(...p)=>{const j=Date.now(),x=j-r;x>=n?h(p,j):(d=p,c||(c=setTimeout(()=>{c=null,h(d)},n-x)))},()=>d&&h(d)]}const cr=(t,s,r=3)=>{let n=0;const d=rp(50,250);return np(c=>{const h=c.loaded,m=c.lengthComputable?c.total:void 0,y=h-n,p=d(y),j=h<=m;n=h;const x={loaded:h,total:m,progress:m?h/m:void 0,bytes:y,rate:p||void 0,estimated:p&&m&&j?(m-h)/p:void 0,event:c,lengthComputable:m!=null,[s?"download":"upload"]:!0};t(x)},r)},Hl=(t,s)=>{const r=t!=null;return[n=>s[0]({lengthComputable:r,total:t,loaded:n}),s[1]]},Wl=t=>(...s)=>z.asap(()=>t(...s)),ip=ut.hasStandardBrowserEnv?((t,s)=>r=>(r=new URL(r,ut.origin),t.protocol===r.protocol&&t.host===r.host&&(s||t.port===r.port)))(new URL(ut.origin),ut.navigator&&/(msie|trident)/i.test(ut.navigator.userAgent)):()=>!0,lp=ut.hasStandardBrowserEnv?{write(t,s,r,n,d,c){const h=[t+"="+encodeURIComponent(s)];z.isNumber(r)&&h.push("expires="+new Date(r).toGMTString()),z.isString(n)&&h.push("path="+n),z.isString(d)&&h.push("domain="+d),c===!0&&h.push("secure"),document.cookie=h.join("; ")},read(t){const s=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return s?decodeURIComponent(s[3]):null},remove(t){this.write(t,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function op(t){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t)}function cp(t,s){return s?t.replace(/\/?\/$/,"")+"/"+s.replace(/^\/+/,""):t}function lc(t,s,r){let n=!op(s);return t&&(n||r==!1)?cp(t,s):s}const Ul=t=>t instanceof bt?{...t}:t;function Sa(t,s){s=s||{};const r={};function n(p,j,x,b){return z.isPlainObject(p)&&z.isPlainObject(j)?z.merge.call({caseless:b},p,j):z.isPlainObject(j)?z.merge({},j):z.isArray(j)?j.slice():j}function d(p,j,x,b){if(z.isUndefined(j)){if(!z.isUndefined(p))return n(void 0,p,x,b)}else return n(p,j,x,b)}function c(p,j){if(!z.isUndefined(j))return n(void 0,j)}function h(p,j){if(z.isUndefined(j)){if(!z.isUndefined(p))return n(void 0,p)}else return n(void 0,j)}function m(p,j,x){if(x in s)return n(p,j);if(x in t)return n(void 0,p)}const y={url:c,method:c,data:c,baseURL:h,transformRequest:h,transformResponse:h,paramsSerializer:h,timeout:h,timeoutMessage:h,withCredentials:h,withXSRFToken:h,adapter:h,responseType:h,xsrfCookieName:h,xsrfHeaderName:h,onUploadProgress:h,onDownloadProgress:h,decompress:h,maxContentLength:h,maxBodyLength:h,beforeRedirect:h,transport:h,httpAgent:h,httpsAgent:h,cancelToken:h,socketPath:h,responseEncoding:h,validateStatus:m,headers:(p,j,x)=>d(Ul(p),Ul(j),x,!0)};return z.forEach(Object.keys(Object.assign({},t,s)),function(j){const x=y[j]||d,b=x(t[j],s[j],j);z.isUndefined(b)&&x!==m||(r[j]=b)}),r}const oc=t=>{const s=Sa({},t);let{data:r,withXSRFToken:n,xsrfHeaderName:d,xsrfCookieName:c,headers:h,auth:m}=s;s.headers=h=bt.from(h),s.url=ac(lc(s.baseURL,s.url,s.allowAbsoluteUrls),t.params,t.paramsSerializer),m&&h.set("Authorization","Basic "+btoa((m.username||"")+":"+(m.password?unescape(encodeURIComponent(m.password)):"")));let y;if(z.isFormData(r)){if(ut.hasStandardBrowserEnv||ut.hasStandardBrowserWebWorkerEnv)h.setContentType(void 0);else if((y=h.getContentType())!==!1){const[p,...j]=y?y.split(";").map(x=>x.trim()).filter(Boolean):[];h.setContentType([p||"multipart/form-data",...j].join("; "))}}if(ut.hasStandardBrowserEnv&&(n&&z.isFunction(n)&&(n=n(s)),n||n!==!1&&ip(s.url))){const p=d&&c&&lp.read(c);p&&h.set(d,p)}return s},dp=typeof XMLHttpRequest!="undefined",up=dp&&function(t){return new Promise(function(r,n){const d=oc(t);let c=d.data;const h=bt.from(d.headers).normalize();let{responseType:m,onUploadProgress:y,onDownloadProgress:p}=d,j,x,b,C,_;function w(){C&&C(),_&&_(),d.cancelToken&&d.cancelToken.unsubscribe(j),d.signal&&d.signal.removeEventListener("abort",j)}let g=new XMLHttpRequest;g.open(d.method.toUpperCase(),d.url,!0),g.timeout=d.timeout;function H(){if(!g)return;const T=bt.from("getAllResponseHeaders"in g&&g.getAllResponseHeaders()),G={data:!m||m==="text"||m==="json"?g.responseText:g.response,status:g.status,statusText:g.statusText,headers:T,config:t,request:g};ic(function(B){r(B),w()},function(B){n(B),w()},G),g=null}"onloadend"in g?g.onloadend=H:g.onreadystatechange=function(){!g||g.readyState!==4||g.status===0&&!(g.responseURL&&g.responseURL.indexOf("file:")===0)||setTimeout(H)},g.onabort=function(){g&&(n(new ze("Request aborted",ze.ECONNABORTED,t,g)),g=null)},g.onerror=function(){n(new ze("Network Error",ze.ERR_NETWORK,t,g)),g=null},g.ontimeout=function(){let W=d.timeout?"timeout of "+d.timeout+"ms exceeded":"timeout exceeded";const G=d.transitional||sc;d.timeoutErrorMessage&&(W=d.timeoutErrorMessage),n(new ze(W,G.clarifyTimeoutError?ze.ETIMEDOUT:ze.ECONNABORTED,t,g)),g=null},c===void 0&&h.setContentType(null),"setRequestHeader"in g&&z.forEach(h.toJSON(),function(W,G){g.setRequestHeader(G,W)}),z.isUndefined(d.withCredentials)||(g.withCredentials=!!d.withCredentials),m&&m!=="json"&&(g.responseType=d.responseType),p&&([b,_]=cr(p,!0),g.addEventListener("progress",b)),y&&g.upload&&([x,C]=cr(y),g.upload.addEventListener("progress",x),g.upload.addEventListener("loadend",C)),(d.cancelToken||d.signal)&&(j=T=>{g&&(n(!T||T.type?new Ga(null,t,g):T),g.abort(),g=null)},d.cancelToken&&d.cancelToken.subscribe(j),d.signal&&(d.signal.aborted?j():d.signal.addEventListener("abort",j)));const U=sp(d.url);if(U&&ut.protocols.indexOf(U)===-1){n(new ze("Unsupported protocol "+U+":",ze.ERR_BAD_REQUEST,t));return}g.send(c||null)})},hp=(t,s)=>{const{length:r}=t=t?t.filter(Boolean):[];if(s||r){let n=new AbortController,d;const c=function(p){if(!d){d=!0,m();const j=p instanceof Error?p:this.reason;n.abort(j instanceof ze?j:new Ga(j instanceof Error?j.message:j))}};let h=s&&setTimeout(()=>{h=null,c(new ze(`timeout ${s} of ms exceeded`,ze.ETIMEDOUT))},s);const m=()=>{t&&(h&&clearTimeout(h),h=null,t.forEach(p=>{p.unsubscribe?p.unsubscribe(c):p.removeEventListener("abort",c)}),t=null)};t.forEach(p=>p.addEventListener("abort",c));const{signal:y}=n;return y.unsubscribe=()=>z.asap(m),y}},fp=function*(t,s){let r=t.byteLength;if(r<s){yield t;return}let n=0,d;for(;n<r;)d=n+s,yield t.slice(n,d),n=d},mp=async function*(t,s){for await(const r of pp(t))yield*fp(r,s)},pp=async function*(t){if(t[Symbol.asyncIterator]){yield*t;return}const s=t.getReader();try{for(;;){const{done:r,value:n}=await s.read();if(r)break;yield n}}finally{await s.cancel()}},Vl=(t,s,r,n)=>{const d=mp(t,s);let c=0,h,m=y=>{h||(h=!0,n&&n(y))};return new ReadableStream({async pull(y){try{const{done:p,value:j}=await d.next();if(p){m(),y.close();return}let x=j.byteLength;if(r){let b=c+=x;r(b)}y.enqueue(new Uint8Array(j))}catch(p){throw m(p),p}},cancel(y){return m(y),d.return()}},{highWaterMark:2})},yr=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",cc=yr&&typeof ReadableStream=="function",gp=yr&&(typeof TextEncoder=="function"?(t=>s=>t.encode(s))(new TextEncoder):async t=>new Uint8Array(await new Response(t).arrayBuffer())),dc=(t,...s)=>{try{return!!t(...s)}catch(r){return!1}},xp=cc&&dc(()=>{let t=!1;const s=new Request(ut.origin,{body:new ReadableStream,method:"POST",get duplex(){return t=!0,"half"}}).headers.has("Content-Type");return t&&!s}),Kl=64*1024,mn=cc&&dc(()=>z.isReadableStream(new Response("").body)),dr={stream:mn&&(t=>t.body)};yr&&(t=>{["text","arrayBuffer","blob","formData","stream"].forEach(s=>{!dr[s]&&(dr[s]=z.isFunction(t[s])?r=>r[s]():(r,n)=>{throw new ze(`Response type '${s}' is not supported`,ze.ERR_NOT_SUPPORT,n)})})})(new Response);const yp=async t=>{if(t==null)return 0;if(z.isBlob(t))return t.size;if(z.isSpecCompliantForm(t))return(await new Request(ut.origin,{method:"POST",body:t}).arrayBuffer()).byteLength;if(z.isArrayBufferView(t)||z.isArrayBuffer(t))return t.byteLength;if(z.isURLSearchParams(t)&&(t=t+""),z.isString(t))return(await gp(t)).byteLength},bp=async(t,s)=>{const r=z.toFiniteNumber(t.getContentLength());return r==null?yp(s):r},jp=yr&&(async t=>{let{url:s,method:r,data:n,signal:d,cancelToken:c,timeout:h,onDownloadProgress:m,onUploadProgress:y,responseType:p,headers:j,withCredentials:x="same-origin",fetchOptions:b}=oc(t);p=p?(p+"").toLowerCase():"text";let C=hp([d,c&&c.toAbortSignal()],h),_;const w=C&&C.unsubscribe&&(()=>{C.unsubscribe()});let g;try{if(y&&xp&&r!=="get"&&r!=="head"&&(g=await bp(j,n))!==0){let G=new Request(s,{method:"POST",body:n,duplex:"half"}),J;if(z.isFormData(n)&&(J=G.headers.get("content-type"))&&j.setContentType(J),G.body){const[B,M]=Hl(g,cr(Wl(y)));n=Vl(G.body,Kl,B,M)}}z.isString(x)||(x=x?"include":"omit");const H="credentials"in Request.prototype;_=new Request(s,{...b,signal:C,method:r.toUpperCase(),headers:j.normalize().toJSON(),body:n,duplex:"half",credentials:H?x:void 0});let U=await fetch(_);const T=mn&&(p==="stream"||p==="response");if(mn&&(m||T&&w)){const G={};["status","statusText","headers"].forEach(X=>{G[X]=U[X]});const J=z.toFiniteNumber(U.headers.get("content-length")),[B,M]=m&&Hl(J,cr(Wl(m),!0))||[];U=new Response(Vl(U.body,Kl,B,()=>{M&&M(),w&&w()}),G)}p=p||"text";let W=await dr[z.findKey(dr,p)||"text"](U,t);return!T&&w&&w(),await new Promise((G,J)=>{ic(G,J,{data:W,headers:bt.from(U.headers),status:U.status,statusText:U.statusText,config:t,request:_})})}catch(H){throw w&&w(),H&&H.name==="TypeError"&&/fetch/i.test(H.message)?Object.assign(new ze("Network Error",ze.ERR_NETWORK,t,_),{cause:H.cause||H}):ze.from(H,H&&H.code,t,_)}}),pn={http:Im,xhr:up,fetch:jp};z.forEach(pn,(t,s)=>{if(t){try{Object.defineProperty(t,"name",{value:s})}catch(r){}Object.defineProperty(t,"adapterName",{value:s})}});const Gl=t=>`- ${t}`,vp=t=>z.isFunction(t)||t===null||t===!1,uc={getAdapter:t=>{t=z.isArray(t)?t:[t];const{length:s}=t;let r,n;const d={};for(let c=0;c<s;c++){r=t[c];let h;if(n=r,!vp(r)&&(n=pn[(h=String(r)).toLowerCase()],n===void 0))throw new ze(`Unknown adapter '${h}'`);if(n)break;d[h||"#"+c]=n}if(!n){const c=Object.entries(d).map(([m,y])=>`adapter ${m} `+(y===!1?"is not supported by the environment":"is not available in the build"));let h=s?c.length>1?`since :
`+c.map(Gl).join(`
`):" "+Gl(c[0]):"as no adapter specified";throw new ze("There is no suitable adapter to dispatch the request "+h,"ERR_NOT_SUPPORT")}return n},adapters:pn};function Vr(t){if(t.cancelToken&&t.cancelToken.throwIfRequested(),t.signal&&t.signal.aborted)throw new Ga(null,t)}function ql(t){return Vr(t),t.headers=bt.from(t.headers),t.data=Ur.call(t,t.transformRequest),["post","put","patch"].indexOf(t.method)!==-1&&t.headers.setContentType("application/x-www-form-urlencoded",!1),uc.getAdapter(t.adapter||Cs.adapter)(t).then(function(n){return Vr(t),n.data=Ur.call(t,t.transformResponse,n),n.headers=bt.from(n.headers),n},function(n){return nc(n)||(Vr(t),n&&n.response&&(n.response.data=Ur.call(t,t.transformResponse,n.response),n.response.headers=bt.from(n.response.headers))),Promise.reject(n)})}const hc="1.8.4",br={};["object","boolean","number","function","string","symbol"].forEach((t,s)=>{br[t]=function(n){return typeof n===t||"a"+(s<1?"n ":" ")+t}});const Jl={};br.transitional=function(s,r,n){function d(c,h){return"[Axios v"+hc+"] Transitional option '"+c+"'"+h+(n?". "+n:"")}return(c,h,m)=>{if(s===!1)throw new ze(d(h," has been removed"+(r?" in "+r:"")),ze.ERR_DEPRECATED);return r&&!Jl[h]&&(Jl[h]=!0),s?s(c,h,m):!0}};br.spelling=function(s){return(r,n)=>!0};function kp(t,s,r){if(typeof t!="object")throw new ze("options must be an object",ze.ERR_BAD_OPTION_VALUE);const n=Object.keys(t);let d=n.length;for(;d-- >0;){const c=n[d],h=s[c];if(h){const m=t[c],y=m===void 0||h(m,c,t);if(y!==!0)throw new ze("option "+c+" must be "+y,ze.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new ze("Unknown option "+c,ze.ERR_BAD_OPTION)}}const ar={assertOptions:kp,validators:br},Yt=ar.validators;let ka=class{constructor(s){this.defaults=s,this.interceptors={request:new Bl,response:new Bl}}async request(s,r){try{return await this._request(s,r)}catch(n){if(n instanceof Error){let d={};Error.captureStackTrace?Error.captureStackTrace(d):d=new Error;const c=d.stack?d.stack.replace(/^.+\n/,""):"";try{n.stack?c&&!String(n.stack).endsWith(c.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+c):n.stack=c}catch(h){}}throw n}}_request(s,r){typeof s=="string"?(r=r||{},r.url=s):r=s||{},r=Sa(this.defaults,r);const{transitional:n,paramsSerializer:d,headers:c}=r;n!==void 0&&ar.assertOptions(n,{silentJSONParsing:Yt.transitional(Yt.boolean),forcedJSONParsing:Yt.transitional(Yt.boolean),clarifyTimeoutError:Yt.transitional(Yt.boolean)},!1),d!=null&&(z.isFunction(d)?r.paramsSerializer={serialize:d}:ar.assertOptions(d,{encode:Yt.function,serialize:Yt.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),ar.assertOptions(r,{baseUrl:Yt.spelling("baseURL"),withXsrfToken:Yt.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let h=c&&z.merge(c.common,c[r.method]);c&&z.forEach(["delete","get","head","post","put","patch","common"],_=>{delete c[_]}),r.headers=bt.concat(h,c);const m=[];let y=!0;this.interceptors.request.forEach(function(w){typeof w.runWhen=="function"&&w.runWhen(r)===!1||(y=y&&w.synchronous,m.unshift(w.fulfilled,w.rejected))});const p=[];this.interceptors.response.forEach(function(w){p.push(w.fulfilled,w.rejected)});let j,x=0,b;if(!y){const _=[ql.bind(this),void 0];for(_.unshift.apply(_,m),_.push.apply(_,p),b=_.length,j=Promise.resolve(r);x<b;)j=j.then(_[x++],_[x++]);return j}b=m.length;let C=r;for(x=0;x<b;){const _=m[x++],w=m[x++];try{C=_(C)}catch(g){w.call(this,g);break}}try{j=ql.call(this,C)}catch(_){return Promise.reject(_)}for(x=0,b=p.length;x<b;)j=j.then(p[x++],p[x++]);return j}getUri(s){s=Sa(this.defaults,s);const r=lc(s.baseURL,s.url,s.allowAbsoluteUrls);return ac(r,s.params,s.paramsSerializer)}};z.forEach(["delete","get","head","options"],function(s){ka.prototype[s]=function(r,n){return this.request(Sa(n||{},{method:s,url:r,data:(n||{}).data}))}});z.forEach(["post","put","patch"],function(s){function r(n){return function(c,h,m){return this.request(Sa(m||{},{method:s,headers:n?{"Content-Type":"multipart/form-data"}:{},url:c,data:h}))}}ka.prototype[s]=r(),ka.prototype[s+"Form"]=r(!0)});let wp=class fc{constructor(s){if(typeof s!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(c){r=c});const n=this;this.promise.then(d=>{if(!n._listeners)return;let c=n._listeners.length;for(;c-- >0;)n._listeners[c](d);n._listeners=null}),this.promise.then=d=>{let c;const h=new Promise(m=>{n.subscribe(m),c=m}).then(d);return h.cancel=function(){n.unsubscribe(c)},h},s(function(c,h,m){n.reason||(n.reason=new Ga(c,h,m),r(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(s){if(this.reason){s(this.reason);return}this._listeners?this._listeners.push(s):this._listeners=[s]}unsubscribe(s){if(!this._listeners)return;const r=this._listeners.indexOf(s);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const s=new AbortController,r=n=>{s.abort(n)};return this.subscribe(r),s.signal.unsubscribe=()=>this.unsubscribe(r),s.signal}static source(){let s;return{token:new fc(function(d){s=d}),cancel:s}}};function Sp(t){return function(r){return t.apply(null,r)}}function _p(t){return z.isObject(t)&&t.isAxiosError===!0}const gn={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(gn).forEach(([t,s])=>{gn[s]=t});function mc(t){const s=new ka(t),r=Uo(ka.prototype.request,s);return z.extend(r,ka.prototype,s,{allOwnKeys:!0}),z.extend(r,s,null,{allOwnKeys:!0}),r.create=function(d){return mc(Sa(t,d))},r}const de=mc(Cs);de.Axios=ka;de.CanceledError=Ga;de.CancelToken=wp;de.isCancel=nc;de.VERSION=hc;de.toFormData=xr;de.AxiosError=ze;de.Cancel=de.CanceledError;de.all=function(s){return Promise.all(s)};de.spread=Sp;de.isAxiosError=_p;de.mergeConfig=Sa;de.AxiosHeaders=bt;de.formToJSON=t=>rc(z.isHTMLForm(t)?new FormData(t):t);de.getAdapter=uc.getAdapter;de.HttpStatusCode=gn;de.default=de;const{Axios:S2,AxiosError:_2,CanceledError:T2,isCancel:C2,CancelToken:O2,VERSION:z2,all:R2,Cancel:E2,isAxiosError:D2,spread:M2,toFormData:A2,AxiosHeaders:I2,HttpStatusCode:P2,formToJSON:L2,getAdapter:N2,mergeConfig:Y2}=de;let Tp={data:""},Cp=t=>typeof window=="object"?((t?t.querySelector("#_goober"):window._goober)||Object.assign((t||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:t||Tp,Op=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,zp=/\/\*[^]*?\*\/|  +/g,Zl=/\n+/g,ca=(t,s)=>{let r="",n="",d="";for(let c in t){let h=t[c];c[0]=="@"?c[1]=="i"?r=c+" "+h+";":n+=c[1]=="f"?ca(h,c):c+"{"+ca(h,c[1]=="k"?"":s)+"}":typeof h=="object"?n+=ca(h,s?s.replace(/([^,])+/g,m=>c.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,y=>/&/.test(y)?y.replace(/&/g,m):m?m+" "+y:y)):c):h!=null&&(c=/^--/.test(c)?c:c.replace(/[A-Z]/g,"-$&").toLowerCase(),d+=ca.p?ca.p(c,h):c+":"+h+";")}return r+(s&&d?s+"{"+d+"}":d)+n},Jt={},pc=t=>{if(typeof t=="object"){let s="";for(let r in t)s+=r+pc(t[r]);return s}return t},Rp=(t,s,r,n,d)=>{let c=pc(t),h=Jt[c]||(Jt[c]=(y=>{let p=0,j=11;for(;p<y.length;)j=101*j+y.charCodeAt(p++)>>>0;return"go"+j})(c));if(!Jt[h]){let y=c!==t?t:(p=>{let j,x,b=[{}];for(;j=Op.exec(p.replace(zp,""));)j[4]?b.shift():j[3]?(x=j[3].replace(Zl," ").trim(),b.unshift(b[0][x]=b[0][x]||{})):b[0][j[1]]=j[2].replace(Zl," ").trim();return b[0]})(t);Jt[h]=ca(d?{["@keyframes "+h]:y}:y,r?"":"."+h)}let m=r&&Jt.g?Jt.g:null;return r&&(Jt.g=Jt[h]),((y,p,j,x)=>{x?p.data=p.data.replace(x,y):p.data.indexOf(y)===-1&&(p.data=j?y+p.data:p.data+y)})(Jt[h],s,n,m),h},Ep=(t,s,r)=>t.reduce((n,d,c)=>{let h=s[c];if(h&&h.call){let m=h(r),y=m&&m.props&&m.props.className||/^go/.test(m)&&m;h=y?"."+y:m&&typeof m=="object"?m.props?"":ca(m,""):m===!1?"":m}return n+d+(h==null?"":h)},"");function jr(t){let s=this||{},r=t.call?t(s.p):t;return Rp(r.unshift?r.raw?Ep(r,[].slice.call(arguments,1),s.p):r.reduce((n,d)=>Object.assign(n,d&&d.call?d(s.p):d),{}):r,Cp(s.target),s.g,s.o,s.k)}let gc,xn,yn;jr.bind({g:1});let Qt=jr.bind({k:1});function Dp(t,s,r,n){ca.p=s,gc=t,xn=r,yn=n}function ma(t,s){let r=this||{};return function(){let n=arguments;function d(c,h){let m=Object.assign({},c),y=m.className||d.className;r.p=Object.assign({theme:xn&&xn()},m),r.o=/ *go\d+/.test(y),m.className=jr.apply(r,n)+(y?" "+y:"");let p=t;return t[0]&&(p=m.as||t,delete m.as),yn&&p[0]&&yn(m),gc(p,m)}return s?s(d):d}}var Mp=t=>typeof t=="function",ur=(t,s)=>Mp(t)?t(s):t,Ap=(()=>{let t=0;return()=>(++t).toString()})(),xc=(()=>{let t;return()=>{if(t===void 0&&typeof window<"u"){let s=matchMedia("(prefers-reduced-motion: reduce)");t=!s||s.matches}return t}})(),Ip=20,yc=(t,s)=>{switch(s.type){case 0:return{...t,toasts:[s.toast,...t.toasts].slice(0,Ip)};case 1:return{...t,toasts:t.toasts.map(c=>c.id===s.toast.id?{...c,...s.toast}:c)};case 2:let{toast:r}=s;return yc(t,{type:t.toasts.find(c=>c.id===r.id)?1:0,toast:r});case 3:let{toastId:n}=s;return{...t,toasts:t.toasts.map(c=>c.id===n||n===void 0?{...c,dismissed:!0,visible:!1}:c)};case 4:return s.toastId===void 0?{...t,toasts:[]}:{...t,toasts:t.toasts.filter(c=>c.id!==s.toastId)};case 5:return{...t,pausedAt:s.time};case 6:let d=s.time-(t.pausedAt||0);return{...t,pausedAt:void 0,toasts:t.toasts.map(c=>({...c,pauseDuration:c.pauseDuration+d}))}}},sr=[],va={toasts:[],pausedAt:void 0},Ca=t=>{va=yc(va,t),sr.forEach(s=>{s(va)})},Pp={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},Lp=(t={})=>{let[s,r]=u.useState(va),n=u.useRef(va);u.useEffect(()=>(n.current!==va&&r(va),sr.push(r),()=>{let c=sr.indexOf(r);c>-1&&sr.splice(c,1)}),[]);let d=s.toasts.map(c=>{var h,m,y;return{...t,...t[c.type],...c,removeDelay:c.removeDelay||((h=t[c.type])==null?void 0:h.removeDelay)||(t==null?void 0:t.removeDelay),duration:c.duration||((m=t[c.type])==null?void 0:m.duration)||(t==null?void 0:t.duration)||Pp[c.type],style:{...t.style,...(y=t[c.type])==null?void 0:y.style,...c.style}}});return{...s,toasts:d}},Np=(t,s="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:s,ariaProps:{role:"status","aria-live":"polite"},message:t,pauseDuration:0,...r,id:(r==null?void 0:r.id)||Ap()}),Os=t=>(s,r)=>{let n=Np(s,t,r);return Ca({type:2,toast:n}),n.id},st=(t,s)=>Os("blank")(t,s);st.error=Os("error");st.success=Os("success");st.loading=Os("loading");st.custom=Os("custom");st.dismiss=t=>{Ca({type:3,toastId:t})};st.remove=t=>Ca({type:4,toastId:t});st.promise=(t,s,r)=>{let n=st.loading(s.loading,{...r,...r==null?void 0:r.loading});return typeof t=="function"&&(t=t()),t.then(d=>{let c=s.success?ur(s.success,d):void 0;return c?st.success(c,{id:n,...r,...r==null?void 0:r.success}):st.dismiss(n),d}).catch(d=>{let c=s.error?ur(s.error,d):void 0;c?st.error(c,{id:n,...r,...r==null?void 0:r.error}):st.dismiss(n)}),t};var Yp=(t,s)=>{Ca({type:1,toast:{id:t,height:s}})},$p=()=>{Ca({type:5,time:Date.now()})},js=new Map,Bp=1e3,Fp=(t,s=Bp)=>{if(js.has(t))return;let r=setTimeout(()=>{js.delete(t),Ca({type:4,toastId:t})},s);js.set(t,r)},Hp=t=>{let{toasts:s,pausedAt:r}=Lp(t);u.useEffect(()=>{if(r)return;let c=Date.now(),h=s.map(m=>{if(m.duration===1/0)return;let y=(m.duration||0)+m.pauseDuration-(c-m.createdAt);if(y<0){m.visible&&st.dismiss(m.id);return}return setTimeout(()=>st.dismiss(m.id),y)});return()=>{h.forEach(m=>m&&clearTimeout(m))}},[s,r]);let n=u.useCallback(()=>{r&&Ca({type:6,time:Date.now()})},[r]),d=u.useCallback((c,h)=>{let{reverseOrder:m=!1,gutter:y=8,defaultPosition:p}=h||{},j=s.filter(C=>(C.position||p)===(c.position||p)&&C.height),x=j.findIndex(C=>C.id===c.id),b=j.filter((C,_)=>_<x&&C.visible).length;return j.filter(C=>C.visible).slice(...m?[b+1]:[0,b]).reduce((C,_)=>C+(_.height||0)+y,0)},[s]);return u.useEffect(()=>{s.forEach(c=>{if(c.dismissed)Fp(c.id,c.removeDelay);else{let h=js.get(c.id);h&&(clearTimeout(h),js.delete(c.id))}})},[s]),{toasts:s,handlers:{updateHeight:Yp,startPause:$p,endPause:n,calculateOffset:d}}},Wp=Qt`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,Up=Qt`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Vp=Qt`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,Kp=ma("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${t=>t.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Wp} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${Up} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${t=>t.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${Vp} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,Gp=Qt`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,qp=ma("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${t=>t.secondary||"#e0e0e0"};
  border-right-color: ${t=>t.primary||"#616161"};
  animation: ${Gp} 1s linear infinite;
`,Jp=Qt`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,Zp=Qt`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Xp=ma("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${t=>t.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Jp} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${Zp} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${t=>t.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,Qp=ma("div")`
  position: absolute;
`,e1=ma("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,t1=Qt`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,a1=ma("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${t1} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,s1=({toast:t})=>{let{icon:s,type:r,iconTheme:n}=t;return s!==void 0?typeof s=="string"?u.createElement(a1,null,s):s:r==="blank"?null:u.createElement(e1,null,u.createElement(qp,{...n}),r!=="loading"&&u.createElement(Qp,null,r==="error"?u.createElement(Kp,{...n}):u.createElement(Xp,{...n})))},r1=t=>`
0% {transform: translate3d(0,${t*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,n1=t=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${t*-150}%,-1px) scale(.6); opacity:0;}
`,i1="0%{opacity:0;} 100%{opacity:1;}",l1="0%{opacity:1;} 100%{opacity:0;}",o1=ma("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,c1=ma("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,d1=(t,s)=>{let r=t.includes("top")?1:-1,[n,d]=xc()?[i1,l1]:[r1(r),n1(r)];return{animation:s?`${Qt(n)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${Qt(d)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},u1=u.memo(({toast:t,position:s,style:r,children:n})=>{let d=t.height?d1(t.position||s||"top-center",t.visible):{opacity:0},c=u.createElement(s1,{toast:t}),h=u.createElement(c1,{...t.ariaProps},ur(t.message,t));return u.createElement(o1,{className:t.className,style:{...d,...r,...t.style}},typeof n=="function"?n({icon:c,message:h}):u.createElement(u.Fragment,null,c,h))});Dp(u.createElement);var h1=({id:t,className:s,style:r,onHeightUpdate:n,children:d})=>{let c=u.useCallback(h=>{if(h){let m=()=>{let y=h.getBoundingClientRect().height;n(t,y)};m(),new MutationObserver(m).observe(h,{subtree:!0,childList:!0,characterData:!0})}},[t,n]);return u.createElement("div",{ref:c,className:s,style:r},d)},f1=(t,s)=>{let r=t.includes("top"),n=r?{top:0}:{bottom:0},d=t.includes("center")?{justifyContent:"center"}:t.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:xc()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${s*(r?1:-1)}px)`,...n,...d}},m1=jr`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,Ws=16,p1=({reverseOrder:t,position:s="top-center",toastOptions:r,gutter:n,children:d,containerStyle:c,containerClassName:h})=>{let{toasts:m,handlers:y}=Hp(r);return u.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:Ws,left:Ws,right:Ws,bottom:Ws,pointerEvents:"none",...c},className:h,onMouseEnter:y.startPause,onMouseLeave:y.endPause},m.map(p=>{let j=p.position||s,x=y.calculateOffset(p,{reverseOrder:t,gutter:n,defaultPosition:s}),b=f1(j,x);return u.createElement(h1,{id:p.id,key:p.id,onHeightUpdate:y.updateHeight,className:p.visible?m1:"",style:b},p.type==="custom"?ur(p.message,p):d?d(p):u.createElement(u1,{toast:p,position:j}))}))},cs=st;const fe=de.create({baseURL:Be.BASE_URL,timeout:8e3,headers:{"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest"},maxRedirects:3,maxContentLength:5e7,maxBodyLength:5e7}),Fa=new Map;fe.interceptors.request.use(t=>{var s,r;const n=localStorage.getItem(Ze.TOKEN_KEY);n&&(t.headers.Authorization=`Bearer ${n}`);const d=`${t.method}-${t.url}-${JSON.stringify(t.params)}`;return Fa.has(d)?Fa.get(d):(((s=t.url)==null?void 0:s.includes("real-time"))||((r=t.url)==null?void 0:r.includes("tasks"))?(t.headers["Cache-Control"]="no-cache, no-store, must-revalidate",t.headers.Pragma="no-cache",t.headers.Expires="0"):t.headers["Cache-Control"]="max-age=30",t.url,Fa.set(d,t),setTimeout(()=>{Fa.delete(d)},5e3),t)},t=>Promise.reject(t));fe.interceptors.response.use(t=>{const s=`${t.config.method}-${t.config.url}-${JSON.stringify(t.config.params)}`;return Fa.delete(s),t},t=>{if(t.config){const s=`${t.config.method}-${t.config.url}-${JSON.stringify(t.config.params)}`;Fa.delete(s)}return t.response?t.response.status===401?(cs.error("Oturum süreniz dolmuş. Lütfen tekrar giriş yapın."),localStorage.removeItem(Ze.TOKEN_KEY),setTimeout(()=>{window.location.href="/login"},1500)):t.response.status===403?cs.error("Bu işlem için yetkiniz bulunmuyor."):t.response.status===404||t.response.status>=500&&cs.error("Sunucu hatası oluştu. Lütfen daha sonra tekrar deneyin."):t.request&&(t.code==="ECONNABORTED"?cs.error("İstek zaman aşımına uğradı. Lütfen tekrar deneyin."):t.message.includes("Network Error")&&cs.error("Ağ bağlantısı hatası. İnternet bağlantınızı kontrol edin.")),Promise.reject(t)});const Kr=!1;const bc=u.createContext(void 0),Ct=()=>{const t=u.useContext(bc);if(!t)throw new Error("useAuth must be used within an AuthProvider");return t},g1=({children:t})=>{const[s,r]=u.useState(null),[n,d]=u.useState(!0),c=_a(),h=async(j,x)=>{var b,C;try{const _=j.trim().toLowerCase(),g=(await fe.post("/auth.php",{email:_,password:x})).data;if(!g.success&&!g.token)throw new Error(g.error||"Giriş başarısız oldu");if(!g.token||!g.user&&!g.data)throw new Error("Sunucu yanıtı geçersiz");const H=g.user||g.data||{},U=H.permissions||{dashboard:!0},T={id:H.id,name:H.name||H.username||"Kullanıcı",email:H.email,role:H.role||"user",token:g.token,permissions:U};return localStorage.setItem(Ze.USER_KEY,JSON.stringify(T)),localStorage.setItem(Ze.TOKEN_KEY,T.token),r(T),{user:T}}catch(_){return{error:((C=(b=_.response)==null?void 0:b.data)==null?void 0:C.error)||_.message||"Giriş yapılamadı"}}},m=async()=>{try{await fe.post("/auth.php?action=logout")}catch(j){}finally{localStorage.removeItem(Ze.USER_KEY),localStorage.removeItem(Ze.TOKEN_KEY),r(null),c("/login")}},y=async()=>{var j;try{const x=window.location.pathname;if(x.endsWith("/login")||x.endsWith("/register")){d(!1);return}const b=localStorage.getItem(Ze.USER_KEY),C=localStorage.getItem(Ze.TOKEN_KEY);if(!b||!C){r(null),d(!1),c("/login");return}const _=JSON.parse(b);r(_),d(!1);try{const g=(await fe.get("/auth.php?check=true")).data;if(!g.authenticated)localStorage.removeItem(Ze.USER_KEY),localStorage.removeItem(Ze.TOKEN_KEY),r(null),c("/login");else{const H=((j=g.user)==null?void 0:j.permissions)||_.permissions||{},U={..._,permissions:H};localStorage.setItem(Ze.USER_KEY,JSON.stringify(U)),r(U)}}catch(w){}}catch(x){localStorage.removeItem(Ze.USER_KEY),localStorage.removeItem(Ze.TOKEN_KEY),r(null),d(!1),c("/login")}};u.useEffect(()=>{(async()=>{const x=window.location.pathname;if(x.endsWith("/login")||x.endsWith("/register")){d(!1);return}const b=localStorage.getItem(Ze.USER_KEY),C=localStorage.getItem(Ze.TOKEN_KEY);if(b&&C)try{const _=JSON.parse(b);if(r(_),d(!1),!Kr)try{(await fe.get("/auth.php?check=true")).data.authenticated||(localStorage.removeItem(Ze.USER_KEY),localStorage.removeItem(Ze.TOKEN_KEY),r(null),c("/login"))}catch(w){}}catch(_){localStorage.removeItem(Ze.USER_KEY),localStorage.removeItem(Ze.TOKEN_KEY),r(null),d(!1),c("/login")}else r(null),d(!1),c("/login")})()},[c]);const p={user:s,loading:n,login:h,logout:m,checkAuth:y};return e.jsx(bc.Provider,{value:p,children:t})},jc=u.createContext(void 0),x1={topPerformers:[],contentTypePerformance:[],optimalTimeSlots:[],currentMonthRevenue:0,previousMonthRevenue:0,targetRevenue:0,targetGrowth:0,tasks:[],overview:{total_publishers:0,total_tasks:0,completed_tasks:0,total_followers:0,total_diamonds:0,completion_rate:0},trend_data:[],publisherDiscoveryStatus:{running:!1,error:!1,lastChecked:null}};const y1=({children:t})=>{const{user:s}=Ct(),[r,n]=u.useState(x1),[d,c]=u.useState(!0),[h,m]=u.useState(null),y=!1,p=u.useRef(0),j=u.useRef(!1),x=u.useRef(null),b=u.useRef(0),C=2,_=5e3,w=u.useRef(null),g=async(T=!1)=>{var W,G,J,B;const M=Date.now();if(w.current&&w.current.abort(),!(j.current&&!T)&&!(!T&&M-p.current<_)){w.current=new AbortController,j.current=!0,c(!0),m(null);try{p.current=M;let X="/";typeof window!="undefined"&&typeof window.location.pathname=="string"&&(X=window.location.pathname);const oe=X==="/"||X==="/dashboard"||X==="/akademi/dashboard";if(!oe){c(!1),j.current=!1;return}if(oe)try{const K=[];K.push(fe.get(`${Be.X_SITE_BASE_URL}${Be.ENDPOINTS.DASHBOARD_METRICS}`,{signal:(W=w.current)==null?void 0:W.signal}));const ye=X.includes("/tasks")||X.includes("/performance");ye&&K.push(fe.get(`${Be.X_SITE_BASE_URL}${Be.ENDPOINTS.TASKS}`,{signal:(G=w.current)==null?void 0:G.signal}));const _e=await Promise.allSettled(K),Re=_e[0];if(Re.status==="fulfilled"&&((J=Re.value.data)!=null&&J.success)){const A=Re.value.data.metrics||{};n(F=>{var ie,je,De,Y,pe,Oe,be,Ee,Z,D,Q;return{...F,overview:{total_publishers:A.total_publishers||0,total_tasks:A.total_tasks||0,completed_tasks:A.completed_tasks||0,total_followers:A.total_followers||0,total_diamonds:A.total_diamonds||0,completion_rate:A.task_completion||0},trend_data:A.trend_data||[],topPerformers:((ie=A.top_publishers)==null?void 0:ie.map(k=>({id:k.id||k.username,username:k.username,growth:k.followers||0,diamonds:k.diamonds||0})))||[],contentTypePerformance:((je=A.category_distribution)==null?void 0:je.map(k=>({type:k.name,performance:k.value})))||[],optimalTimeSlots:((De=A.time_slots)==null?void 0:De.map(k=>({activity:k.activity||"Genel",timeSlot:k.timeSlot||k.time})))||[],currentMonthRevenue:(Y=A.current_month_revenue)!=null?Y:0,previousMonthRevenue:(pe=A.previous_month_revenue)!=null?pe:0,targetRevenue:(Oe=A.target_revenue)!=null?Oe:0,targetGrowth:(be=A.target_growth)!=null?be:0,publisherDiscoveryStatus:{running:((Ee=A.publisher_discovery_data)==null?void 0:Ee.program_status)==="running",error:((Z=A.publisher_discovery_data)==null?void 0:Z.program_status)==="error",lastChecked:new Date,today_discovered:((D=A.publisher_discovery_data)==null?void 0:D.today_discovered)||0,program_status:((Q=A.publisher_discovery_data)==null?void 0:Q.program_status)||"stopped"}}})}if(ye&&((B=_e[1])==null?void 0:B.status)==="fulfilled"){const A=_e[1];A.value.data&&Array.isArray(A.value.data.tasks)&&n(F=>({...F,tasks:A.value.data.tasks}))}c(!1);return}catch(K){m("API'ye erişim sırasında hata oluştu")}}catch(X){if(m("Veri yükleme sırasında beklenmeyen bir hata oluştu"),b.current<C){b.current+=1;const oe=Math.pow(2,b.current)*1e3;x.current&&clearTimeout(x.current),x.current=setTimeout(()=>{g(!0)},oe)}else b.current=0}finally{c(!1),j.current=!1}}},H=async()=>{await g(!0)},U=T=>{n(W=>({...W,publisherDiscoveryStatus:{...W.publisherDiscoveryStatus,...T,lastChecked:new Date}}))};return u.useEffect(()=>(s&&g(),()=>{x.current&&clearTimeout(x.current),w.current&&w.current.abort(),j.current=!1}),[s]),e.jsx(jc.Provider,{value:{...r,loading:d,error:h,refreshData:H,updatePublisherDiscoveryStatus:U},children:t})},Fi=()=>{const t=u.useContext(jc);if(!t)throw new Error("useData must be used within a DataProvider");return t};class b1 extends u.Component{constructor(s){super(s),this.state={hasError:!1,error:null}}static getDerivedStateFromError(s){return{hasError:!0,error:s}}componentDidCatch(s,r){}render(){return this.state.hasError?this.props.fallback?this.props.fallback:e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900",children:e.jsxs("div",{className:"bg-white dark:bg-gray-800 p-8 rounded-lg shadow-lg max-w-md w-full text-center",children:[e.jsx("div",{className:"text-red-500 text-5xl mb-4",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-16 w-16 mx-auto",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})})}),e.jsx("h1",{className:"text-2xl font-bold text-gray-800 dark:text-white mb-2",children:"Bir Şeyler Yanlış Gitti"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-6",children:"Uygulama bir hatayla karşılaştı. Lütfen sayfayı yenileyin veya daha sonra tekrar deneyin."}),e.jsxs("div",{className:"flex justify-center space-x-4",children:[e.jsx("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition",children:"Sayfayı Yenile"}),e.jsx("button",{onClick:()=>window.history.back(),className:"px-4 py-2 bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-white rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition",children:"Geri Dön"})]}),this.state.error&&!1]})}):this.props.children}}var ds={},Gr={exports:{}},Xl;function Hi(){return Xl||(Xl=1,function(t){function s(r){return r&&r.__esModule?r:{default:r}}t.exports=s,t.exports.__esModule=!0,t.exports.default=t.exports}(Gr)),Gr.exports}var us={},Ql;function j1(){if(Ql)return us;Ql=1,Object.defineProperty(us,"__esModule",{value:!0}),us.default=void 0;var t={items_per_page:"/ sayfa",jump_to:"Git",jump_to_confirm:"onayla",page:"Sayfa",prev_page:"Önceki Sayfa",next_page:"Sonraki Sayfa",prev_5:"Önceki 5 Sayfa",next_5:"Sonraki 5 Sayfa",prev_3:"Önceki 3 Sayfa",next_3:"Sonraki 3 Sayfa",page_size:"sayfa boyutu"};return us.default=t,us}var hs={},fs={},ms={},eo;function v1(){if(eo)return ms;eo=1,Object.defineProperty(ms,"__esModule",{value:!0}),ms.default=void 0;var t={locale:"tr_TR",today:"Bugün",now:"Şimdi",backToToday:"Bugüne Geri Dön",ok:"Tamam",clear:"Temizle",month:"Ay",year:"Yıl",timeSelect:"Zaman Seç",dateSelect:"Tarih Seç",monthSelect:"Ay Seç",yearSelect:"Yıl Seç",decadeSelect:"On Yıl Seç",yearFormat:"YYYY",dateFormat:"M/D/YYYY",dayFormat:"D",dateTimeFormat:"M/D/YYYY HH:mm:ss",monthBeforeYear:!0,previousMonth:"Önceki Ay (PageUp)",nextMonth:"Sonraki Ay (PageDown)",previousYear:"Önceki Yıl (Control + Sol)",nextYear:"Sonraki Yıl (Control + Sağ)",previousDecade:"Önceki On Yıl",nextDecade:"Sonraki On Yıl",previousCentury:"Önceki Yüzyıl",nextCentury:"Sonraki Yüzyıl"};return ms.default=t,ms}var ps={},to;function vc(){if(to)return ps;to=1,Object.defineProperty(ps,"__esModule",{value:!0}),ps.default=void 0;const t={placeholder:"Zaman seç",rangePlaceholder:["Başlangıç zamanı","Bitiş zamanı"]};return ps.default=t,ps}var ao;function kc(){if(ao)return fs;ao=1;var t=Hi();Object.defineProperty(fs,"__esModule",{value:!0}),fs.default=void 0;var s=t(v1()),r=t(vc());const n={lang:Object.assign({placeholder:"Tarih seç",yearPlaceholder:"Yıl seç",quarterPlaceholder:"Çeyrek seç",monthPlaceholder:"Ay seç",weekPlaceholder:"Hafta seç",rangePlaceholder:["Başlangıç tarihi","Bitiş tarihi"],rangeYearPlaceholder:["Başlangıç yılı","Bitiş yılı"],rangeMonthPlaceholder:["Başlangıç ayı","Bitiş ayı"],rangeWeekPlaceholder:["Başlangıç haftası","Bitiş haftası"]},s.default),timePickerLocale:Object.assign({},r.default)};return fs.default=n,fs}var so;function k1(){if(so)return hs;so=1;var t=Hi();Object.defineProperty(hs,"__esModule",{value:!0}),hs.default=void 0;var s=t(kc());return hs.default=s.default,hs}var ro;function w1(){if(ro)return ds;ro=1;var t=Hi();Object.defineProperty(ds,"__esModule",{value:!0}),ds.default=void 0;var s=t(j1()),r=t(k1()),n=t(kc()),d=t(vc());const c="${label} geçerli bir ${type} değil",h={locale:"tr",Pagination:s.default,DatePicker:n.default,TimePicker:d.default,Calendar:r.default,global:{placeholder:"Lütfen seçiniz"},Table:{filterTitle:"Filtre menüsü",filterConfirm:"Tamam",filterReset:"Sıfırla",filterEmptyText:"Filtre yok",selectAll:"Tüm sayfayı seç",selectInvert:"Tersini seç",selectionAll:"Tümünü seç",sortTitle:"Sırala",expand:"Satırı genişlet",collapse:"Satırı daralt",triggerDesc:"Azalan düzende sırala",triggerAsc:"Artan düzende sırala",cancelSort:"Sıralamayı kaldır"},Modal:{okText:"Tamam",cancelText:"İptal",justOkText:"Tamam"},Popconfirm:{okText:"Tamam",cancelText:"İptal"},Transfer:{titles:["",""],searchPlaceholder:"Arama",itemUnit:"Öğe",itemsUnit:"Öğeler",remove:"Kaldır",selectCurrent:"Tüm sayfayı seç",removeCurrent:"Sayfayı kaldır",selectAll:"Tümünü seç",removeAll:"Tümünü kaldır",selectInvert:"Tersini seç"},Upload:{uploading:"Yükleniyor...",removeFile:"Dosyayı kaldır",uploadError:"Yükleme hatası",previewFile:"Dosyayı önizle",downloadFile:"Dosyayı indir"},Empty:{description:"Veri Yok"},Icon:{icon:"ikon"},Text:{edit:"Düzenle",copy:"Kopyala",copied:"Kopyalandı",expand:"Genişlet"},PageHeader:{back:"Geri"},Form:{optional:"(opsiyonel)",defaultValidateMessages:{default:"Alan doğrulama hatası ${label}",required:"${label} gerekli bir alan",enum:"${label} şunlardan biri olmalı: [${enum}]",whitespace:"${label} sadece boşluk olamaz",date:{format:"${label} tarih biçimi geçersiz",parse:"${label} bir tarihe dönüştürülemedi",invalid:"${label} geçersiz bir tarih"},types:{string:c,method:c,array:c,object:c,number:c,date:c,boolean:c,integer:c,float:c,regexp:c,email:c,url:c,hex:c},string:{len:"${label} ${len} karakter olmalı",min:"${label} en az ${min} karakter olmalı",max:"${label} en çok ${max} karakter olmalı",range:"${label} ${min}-${max} karakter arası olmalı"},number:{len:"${label} ${len} olmalı",min:"${label} en az ${min} olmalı",max:"${label} en çok ${max} olmalı",range:"${label} ${min}-${max} arası olmalı"},array:{len:"${label} sayısı ${len} olmalı",min:"${label} sayısı en az ${min} olmalı",max:"${label} sayısı en çok ${max} olmalı",range:"${label} sayısı ${min}-${max} arası olmalı"},pattern:{mismatch:"${label} şu kalıpla eşleşmeli: ${pattern}"}}},Image:{preview:"Önizleme"}};return ds.default=h,ds}var qr,no;function S1(){return no||(no=1,qr=w1()),qr}var _1=S1();const T1=Sf(_1),C1={"akademi.destek":"/akademi/destek","site.basvuru":"/site/basvurular","site.iletisim":"/site/iletisim-talepleri","site.geriarama":"/site/geriarama-talepleri","site.toplanti":"/site/toplanti-talepleri","etsy.tasarim":"/etsy/tasarim-onaylari","whatsapp.mesaj":"/whatsapp"},wc=u.createContext(void 0),O1=({children:t})=>{Ct();const[s,r]=u.useState({akademi:{destek:0},site:{basvuru:0,iletisim:0,geriarama:0,toplanti:0},etsy:{tasarim:0},whatsapp:{mesaj:0}}),[n,d]=u.useState(0),[c,h]=u.useState([{id:"1",title:"Yeni WhatsApp Mesajı",message:"Ahmet K. size yeni bir mesaj gönderdi",time:new Date().toISOString(),read:!1,type:"info",source:"whatsapp.mesaj",sourceId:"123"},{id:"2",title:"Akademi Destek Talebi",message:"Destek talebiniz yanıtlandı",time:new Date(Date.now()-24*60*60*1e3).toISOString(),read:!0,type:"success",source:"akademi.destek",sourceId:"456"},{id:"3",title:"Yeni Başvuru",message:"Yeni bir kullanıcı ajans hakkında bilgi almak istiyor",time:new Date(Date.now()-2*24*60*60*1e3).toISOString(),read:!1,type:"info",source:"site.basvuru",sourceId:"789"}]),m=async()=>{const T=window.location.pathname;if(T==="/"||T==="/dashboard"||T==="/akademi/dashboard"||T==="/akademi/destek"||T==="/site/basvurular"||T==="/site/iletisim-talepleri"||T==="/site/geriarama-talepleri"||T==="/site/toplanti-talepleri")try{const G="/backend/x-site/dashboard-metrics.php?range=7";try{const J=await fetch(G,{method:"GET",headers:{Accept:"application/json","Content-Type":"application/json","Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"},credentials:"include"});if(!J.ok)throw new Error(`HTTP error! status: ${J.status}`);const B=await J.json();if(!B.success)throw new Error(B.error||"Dashboard metrics API'sinden veri alınamadı");const M=B.metrics,X=M.academy_pending_tickets||0,oe=M.pending_applications||0,K=M.pending_contact_requests||0,ye=M.pending_callback_requests||0,_e=M.pending_meetings||0,Re=M.pending_etsy_designs||0,A=M.unread_whatsapp_messages||0;r({akademi:{destek:X},site:{basvuru:oe,iletisim:K,geriarama:ye,toplanti:_e},etsy:{tasarim:Re},whatsapp:{mesaj:A}})}catch(J){}}catch(G){}},y=T=>{r(W=>({...W,whatsapp:{...W.whatsapp,mesaj:T}}))},p=async T=>{try{const W=new FormData;if(W.append("id",T),!(await fetch(`${Be.BASE_URL}/notifications/markAsRead.php`,{method:"POST",body:W})).ok)throw new Error("Bildirim okundu olarak işaretlenemedi");h(J=>J.map(B=>B.id===T?{...B,read:!0}:B)),m()}catch(W){h(G=>G.map(J=>J.id===T?{...J,read:!0}:J))}},j=async()=>{try{if(!(await fetch(`${Be.BASE_URL}/notifications/markAllAsRead.php`,{method:"POST"})).ok)throw new Error("Bildirimler okundu olarak işaretlenemedi");h(W=>W.map(G=>({...G,read:!0})))}catch(T){h(W=>W.map(G=>({...G,read:!0})))}},x=T=>{const W={...T,id:Date.now().toString(),time:new Date().toISOString(),read:!1};h(G=>[W,...G])},b=T=>{const{source:W,sourceId:G}=T,J=C1[W]||"/";if(W==="akademi.destek")return"/akademi/destek";if(W==="site.basvuru")return"/site/basvurular";switch(W){case"site.iletisim":return"/site/iletisim-talepleri";case"site.geriarama":return"/site/geriarama-talepleri";case"site.toplanti":return"/site/toplanti-talepleri";case"etsy.tasarim":return"/etsy/tasarim-onaylari";case"whatsapp.mesaj":return G?`${J}/chat/${G}`:J;default:return"/"}},C=T=>{},_=u.useRef(null),w=async()=>{try{let G=(await(await fetch("/backend/x-site/notifications.php",{credentials:"include"})).json()).data;if(typeof G=="string")try{G=JSON.parse(G)}catch(B){G=[]}if(!Array.isArray(G)){h([]),d(0);return}const J=G.map(B=>({id:B.id,title:B.title||"Bildirim",message:B.message||"",time:B.time,read:B.read===!0||B.read===1,type:["info","success","warning","error"].includes(B.type)?B.type:"info",source:B.source||"site.test",sourceId:B.sourceId||void 0}));h(J),d(J.filter(B=>!B.read).length)}catch(T){h([]),d(0)}},g=()=>{_.current||(w(),_.current=setInterval(w,3e4))},H=()=>{_.current&&(clearInterval(_.current),_.current=null)};u.useEffect(()=>{m()},[]);const U={notifications:c,unreadCount:n,totalUnreadCounts:s,markAsRead:p,markAllAsRead:j,addNotification:x,getNotificationRoute:b,updateWhatsAppUnreadCount:y,setUnreadNotifications:C,startPolling:g,stopPolling:H};return e.jsx(wc.Provider,{value:U,children:t})},vr=()=>{const t=u.useContext(wc);if(t===void 0)throw new Error("useNotifications must be used within a NotificationProvider");return t},Sc=u.createContext({}),z1={aliceblue:"9ehhb",antiquewhite:"9sgk7",aqua:"1ekf",aquamarine:"4zsno",azure:"9eiv3",beige:"9lhp8",bisque:"9zg04",black:"0",blanchedalmond:"9zhe5",blue:"73",blueviolet:"5e31e",brown:"6g016",burlywood:"8ouiv",cadetblue:"3qba8",chartreuse:"4zshs",chocolate:"87k0u",coral:"9yvyo",cornflowerblue:"3xael",cornsilk:"9zjz0",crimson:"8l4xo",cyan:"1ekf",darkblue:"3v",darkcyan:"rkb",darkgoldenrod:"776yz",darkgray:"6mbhl",darkgreen:"jr4",darkgrey:"6mbhl",darkkhaki:"7ehkb",darkmagenta:"5f91n",darkolivegreen:"3bzfz",darkorange:"9yygw",darkorchid:"5z6x8",darkred:"5f8xs",darksalmon:"9441m",darkseagreen:"5lwgf",darkslateblue:"2th1n",darkslategray:"1ugcv",darkslategrey:"1ugcv",darkturquoise:"14up",darkviolet:"5rw7n",deeppink:"9yavn",deepskyblue:"11xb",dimgray:"442g9",dimgrey:"442g9",dodgerblue:"16xof",firebrick:"6y7tu",floralwhite:"9zkds",forestgreen:"1cisi",fuchsia:"9y70f",gainsboro:"8m8kc",ghostwhite:"9pq0v",goldenrod:"8j4f4",gold:"9zda8",gray:"50i2o",green:"pa8",greenyellow:"6senj",grey:"50i2o",honeydew:"9eiuo",hotpink:"9yrp0",indianred:"80gnw",indigo:"2xcoy",ivory:"9zldc",khaki:"9edu4",lavenderblush:"9ziet",lavender:"90c8q",lawngreen:"4vk74",lemonchiffon:"9zkct",lightblue:"6s73a",lightcoral:"9dtog",lightcyan:"8s1rz",lightgoldenrodyellow:"9sjiq",lightgray:"89jo3",lightgreen:"5nkwg",lightgrey:"89jo3",lightpink:"9z6wx",lightsalmon:"9z2ii",lightseagreen:"19xgq",lightskyblue:"5arju",lightslategray:"4nwk9",lightslategrey:"4nwk9",lightsteelblue:"6wau6",lightyellow:"9zlcw",lime:"1edc",limegreen:"1zcxe",linen:"9shk6",magenta:"9y70f",maroon:"4zsow",mediumaquamarine:"40eju",mediumblue:"5p",mediumorchid:"79qkz",mediumpurple:"5r3rv",mediumseagreen:"2d9ip",mediumslateblue:"4tcku",mediumspringgreen:"1di2",mediumturquoise:"2uabw",mediumvioletred:"7rn9h",midnightblue:"z980",mintcream:"9ljp6",mistyrose:"9zg0x",moccasin:"9zfzp",navajowhite:"9zest",navy:"3k",oldlace:"9wq92",olive:"50hz4",olivedrab:"472ub",orange:"9z3eo",orangered:"9ykg0",orchid:"8iu3a",palegoldenrod:"9bl4a",palegreen:"5yw0o",paleturquoise:"6v4ku",palevioletred:"8k8lv",papayawhip:"9zi6t",peachpuff:"9ze0p",peru:"80oqn",pink:"9z8wb",plum:"8nba5",powderblue:"6wgdi",purple:"4zssg",rebeccapurple:"3zk49",red:"9y6tc",rosybrown:"7cv4f",royalblue:"2jvtt",saddlebrown:"5fmkz",salmon:"9rvci",sandybrown:"9jn1c",seagreen:"1tdnb",seashell:"9zje6",sienna:"6973h",silver:"7ir40",skyblue:"5arjf",slateblue:"45e4t",slategray:"4e100",slategrey:"4e100",snow:"9zke2",springgreen:"1egv",steelblue:"2r1kk",tan:"87yx8",teal:"pds",thistle:"8ggk8",tomato:"9yqfb",turquoise:"2j4r4",violet:"9b10u",wheat:"9ld4j",white:"9zldr",whitesmoke:"9lhpx",yellow:"9zl6o",yellowgreen:"61fzm"},nt=Math.round;function Jr(t,s){const r=t.replace(/^[^(]*\((.*)/,"$1").replace(/\).*/,"").match(/\d*\.?\d+%?/g)||[],n=r.map(d=>parseFloat(d));for(let d=0;d<3;d+=1)n[d]=s(n[d]||0,r[d]||"",d);return r[3]?n[3]=r[3].includes("%")?n[3]/100:n[3]:n[3]=1,n}const io=(t,s,r)=>r===0?t:t/100;function gs(t,s){const r=s||255;return t>r?r:t<0?0:t}class Ha{constructor(s){kt(this,"isValid",!0);kt(this,"r",0);kt(this,"g",0);kt(this,"b",0);kt(this,"a",1);kt(this,"_h");kt(this,"_s");kt(this,"_l");kt(this,"_v");kt(this,"_max");kt(this,"_min");kt(this,"_brightness");function r(n){return n[0]in s&&n[1]in s&&n[2]in s}if(s)if(typeof s=="string"){let d=function(c){return n.startsWith(c)};const n=s.trim();if(/^#?[A-F\d]{3,8}$/i.test(n))this.fromHexString(n);else if(d("rgb"))this.fromRgbString(n);else if(d("hsl"))this.fromHslString(n);else if(d("hsv")||d("hsb"))this.fromHsvString(n);else{const c=z1[n.toLowerCase()];c&&this.fromHexString(parseInt(c,36).toString(16).padStart(6,"0"))}}else if(s instanceof Ha)this.r=s.r,this.g=s.g,this.b=s.b,this.a=s.a,this._h=s._h,this._s=s._s,this._l=s._l,this._v=s._v;else if(r("rgb"))this.r=gs(s.r),this.g=gs(s.g),this.b=gs(s.b),this.a=typeof s.a=="number"?gs(s.a,1):1;else if(r("hsl"))this.fromHsl(s);else if(r("hsv"))this.fromHsv(s);else throw new Error("@ant-design/fast-color: unsupported input "+JSON.stringify(s))}setR(s){return this._sc("r",s)}setG(s){return this._sc("g",s)}setB(s){return this._sc("b",s)}setA(s){return this._sc("a",s,1)}setHue(s){const r=this.toHsv();return r.h=s,this._c(r)}getLuminance(){function s(c){const h=c/255;return h<=.03928?h/12.92:Math.pow((h+.055)/1.055,2.4)}const r=s(this.r),n=s(this.g),d=s(this.b);return .2126*r+.7152*n+.0722*d}getHue(){if(typeof this._h=="undefined"){const s=this.getMax()-this.getMin();s===0?this._h=0:this._h=nt(60*(this.r===this.getMax()?(this.g-this.b)/s+(this.g<this.b?6:0):this.g===this.getMax()?(this.b-this.r)/s+2:(this.r-this.g)/s+4))}return this._h}getSaturation(){if(typeof this._s=="undefined"){const s=this.getMax()-this.getMin();s===0?this._s=0:this._s=s/this.getMax()}return this._s}getLightness(){return typeof this._l=="undefined"&&(this._l=(this.getMax()+this.getMin())/510),this._l}getValue(){return typeof this._v=="undefined"&&(this._v=this.getMax()/255),this._v}getBrightness(){return typeof this._brightness=="undefined"&&(this._brightness=(this.r*299+this.g*587+this.b*114)/1e3),this._brightness}darken(s=10){const r=this.getHue(),n=this.getSaturation();let d=this.getLightness()-s/100;return d<0&&(d=0),this._c({h:r,s:n,l:d,a:this.a})}lighten(s=10){const r=this.getHue(),n=this.getSaturation();let d=this.getLightness()+s/100;return d>1&&(d=1),this._c({h:r,s:n,l:d,a:this.a})}mix(s,r=50){const n=this._c(s),d=r/100,c=m=>(n[m]-this[m])*d+this[m],h={r:nt(c("r")),g:nt(c("g")),b:nt(c("b")),a:nt(c("a")*100)/100};return this._c(h)}tint(s=10){return this.mix({r:255,g:255,b:255,a:1},s)}shade(s=10){return this.mix({r:0,g:0,b:0,a:1},s)}onBackground(s){const r=this._c(s),n=this.a+r.a*(1-this.a),d=c=>nt((this[c]*this.a+r[c]*r.a*(1-this.a))/n);return this._c({r:d("r"),g:d("g"),b:d("b"),a:n})}isDark(){return this.getBrightness()<128}isLight(){return this.getBrightness()>=128}equals(s){return this.r===s.r&&this.g===s.g&&this.b===s.b&&this.a===s.a}clone(){return this._c(this)}toHexString(){let s="#";const r=(this.r||0).toString(16);s+=r.length===2?r:"0"+r;const n=(this.g||0).toString(16);s+=n.length===2?n:"0"+n;const d=(this.b||0).toString(16);if(s+=d.length===2?d:"0"+d,typeof this.a=="number"&&this.a>=0&&this.a<1){const c=nt(this.a*255).toString(16);s+=c.length===2?c:"0"+c}return s}toHsl(){return{h:this.getHue(),s:this.getSaturation(),l:this.getLightness(),a:this.a}}toHslString(){const s=this.getHue(),r=nt(this.getSaturation()*100),n=nt(this.getLightness()*100);return this.a!==1?`hsla(${s},${r}%,${n}%,${this.a})`:`hsl(${s},${r}%,${n}%)`}toHsv(){return{h:this.getHue(),s:this.getSaturation(),v:this.getValue(),a:this.a}}toRgb(){return{r:this.r,g:this.g,b:this.b,a:this.a}}toRgbString(){return this.a!==1?`rgba(${this.r},${this.g},${this.b},${this.a})`:`rgb(${this.r},${this.g},${this.b})`}toString(){return this.toRgbString()}_sc(s,r,n){const d=this.clone();return d[s]=gs(r,n),d}_c(s){return new this.constructor(s)}getMax(){return typeof this._max=="undefined"&&(this._max=Math.max(this.r,this.g,this.b)),this._max}getMin(){return typeof this._min=="undefined"&&(this._min=Math.min(this.r,this.g,this.b)),this._min}fromHexString(s){const r=s.replace("#","");function n(d,c){return parseInt(r[d]+r[c||d],16)}r.length<6?(this.r=n(0),this.g=n(1),this.b=n(2),this.a=r[3]?n(3)/255:1):(this.r=n(0,1),this.g=n(2,3),this.b=n(4,5),this.a=r[6]?n(6,7)/255:1)}fromHsl({h:s,s:r,l:n,a:d}){if(this._h=s%360,this._s=r,this._l=n,this.a=typeof d=="number"?d:1,r<=0){const b=nt(n*255);this.r=b,this.g=b,this.b=b}let c=0,h=0,m=0;const y=s/60,p=(1-Math.abs(2*n-1))*r,j=p*(1-Math.abs(y%2-1));y>=0&&y<1?(c=p,h=j):y>=1&&y<2?(c=j,h=p):y>=2&&y<3?(h=p,m=j):y>=3&&y<4?(h=j,m=p):y>=4&&y<5?(c=j,m=p):y>=5&&y<6&&(c=p,m=j);const x=n-p/2;this.r=nt((c+x)*255),this.g=nt((h+x)*255),this.b=nt((m+x)*255)}fromHsv({h:s,s:r,v:n,a:d}){this._h=s%360,this._s=r,this._v=n,this.a=typeof d=="number"?d:1;const c=nt(n*255);if(this.r=c,this.g=c,this.b=c,r<=0)return;const h=s/60,m=Math.floor(h),y=h-m,p=nt(n*(1-r)*255),j=nt(n*(1-r*y)*255),x=nt(n*(1-r*(1-y))*255);switch(m){case 0:this.g=x,this.b=p;break;case 1:this.r=j,this.b=p;break;case 2:this.r=p,this.b=x;break;case 3:this.r=p,this.g=j;break;case 4:this.r=x,this.g=p;break;case 5:default:this.g=p,this.b=j;break}}fromHsvString(s){const r=Jr(s,io);this.fromHsv({h:r[0],s:r[1],v:r[2],a:r[3]})}fromHslString(s){const r=Jr(s,io);this.fromHsl({h:r[0],s:r[1],l:r[2],a:r[3]})}fromRgbString(s){const r=Jr(s,(n,d)=>d.includes("%")?nt(n/100*255):n);this.r=r[0],this.g=r[1],this.b=r[2],this.a=r[3]}}const Us=2,lo=.16,R1=.05,E1=.05,D1=.15,_c=5,Tc=4,M1=[{index:7,amount:15},{index:6,amount:25},{index:5,amount:30},{index:5,amount:45},{index:5,amount:65},{index:5,amount:85},{index:4,amount:90},{index:3,amount:95},{index:2,amount:97},{index:1,amount:98}];function oo(t,s,r){let n;return Math.round(t.h)>=60&&Math.round(t.h)<=240?n=r?Math.round(t.h)-Us*s:Math.round(t.h)+Us*s:n=r?Math.round(t.h)+Us*s:Math.round(t.h)-Us*s,n<0?n+=360:n>=360&&(n-=360),n}function co(t,s,r){if(t.h===0&&t.s===0)return t.s;let n;return r?n=t.s-lo*s:s===Tc?n=t.s+lo:n=t.s+R1*s,n>1&&(n=1),r&&s===_c&&n>.1&&(n=.1),n<.06&&(n=.06),Math.round(n*100)/100}function uo(t,s,r){let n;return r?n=t.v+E1*s:n=t.v-D1*s,n=Math.max(0,Math.min(1,n)),Math.round(n*100)/100}function A1(t,s={}){const r=[],n=new Ha(t),d=n.toHsv();for(let c=_c;c>0;c-=1){const h=new Ha({h:oo(d,c,!0),s:co(d,c,!0),v:uo(d,c,!0)});r.push(h)}r.push(n);for(let c=1;c<=Tc;c+=1){const h=new Ha({h:oo(d,c),s:co(d,c),v:uo(d,c)});r.push(h)}return s.theme==="dark"?M1.map(({index:c,amount:h})=>new Ha(s.backgroundColor||"#141414").mix(r[c],h).toHexString()):r.map(c=>c.toHexString())}const bn=["#e6f4ff","#bae0ff","#91caff","#69b1ff","#4096ff","#1677ff","#0958d9","#003eb3","#002c8c","#001d66"];bn.primary=bn[5];function I1(){return!!(typeof window!="undefined"&&window.document&&window.document.createElement)}function P1(t,s){if(!t)return!1;if(t.contains)return t.contains(s);let r=s;for(;r;){if(r===t)return!0;r=r.parentNode}return!1}const ho="data-rc-order",fo="data-rc-priority",L1="rc-util-key",jn=new Map;function Cc({mark:t}={}){return t?t.startsWith("data-")?t:`data-${t}`:L1}function Wi(t){return t.attachTo?t.attachTo:document.querySelector("head")||document.body}function N1(t){return t==="queue"?"prependQueue":t?"prepend":"append"}function Ui(t){return Array.from((jn.get(t)||t).children).filter(s=>s.tagName==="STYLE")}function Oc(t,s={}){if(!I1())return null;const{csp:r,prepend:n,priority:d=0}=s,c=N1(n),h=c==="prependQueue",m=document.createElement("style");m.setAttribute(ho,c),h&&d&&m.setAttribute(fo,`${d}`),r!=null&&r.nonce&&(m.nonce=r==null?void 0:r.nonce),m.innerHTML=t;const y=Wi(s),{firstChild:p}=y;if(n){if(h){const j=(s.styles||Ui(y)).filter(x=>{if(!["prepend","prependQueue"].includes(x.getAttribute(ho)))return!1;const b=Number(x.getAttribute(fo)||0);return d>=b});if(j.length)return y.insertBefore(m,j[j.length-1].nextSibling),m}y.insertBefore(m,p)}else y.appendChild(m);return m}function Y1(t,s={}){let{styles:r}=s;return r||(r=Ui(Wi(s))),r.find(n=>n.getAttribute(Cc(s))===t)}function $1(t,s){const r=jn.get(t);if(!r||!P1(document,r)){const n=Oc("",s),{parentNode:d}=n;jn.set(t,d),t.removeChild(n)}}function B1(t,s,r={}){var y,p,j;const n=Wi(r),d=Ui(n),c={...r,styles:d};$1(n,c);const h=Y1(s,c);if(h)return(y=c.csp)!=null&&y.nonce&&h.nonce!==((p=c.csp)==null?void 0:p.nonce)&&(h.nonce=(j=c.csp)==null?void 0:j.nonce),h.innerHTML!==t&&(h.innerHTML=t),h;const m=Oc(t,c);return m.setAttribute(Cc(c),s),m}function zc(t){var s;return(s=t==null?void 0:t.getRootNode)==null?void 0:s.call(t)}function F1(t){return zc(t)instanceof ShadowRoot}function H1(t){return F1(t)?zc(t):null}let vn={};const W1=t=>{};function U1(t,s){}function V1(t,s){}function K1(){vn={}}function Rc(t,s,r){!s&&!vn[r]&&(t(!1,r),vn[r]=!0)}function kr(t,s){Rc(U1,t,s)}function G1(t,s){Rc(V1,t,s)}kr.preMessage=W1;kr.resetWarned=K1;kr.noteOnce=G1;function q1(t){return t.replace(/-(.)/g,(s,r)=>r.toUpperCase())}function J1(t,s){kr(t,`[@ant-design/icons] ${s}`)}function mo(t){return typeof t=="object"&&typeof t.name=="string"&&typeof t.theme=="string"&&(typeof t.icon=="object"||typeof t.icon=="function")}function po(t={}){return Object.keys(t).reduce((s,r)=>{const n=t[r];switch(r){case"class":s.className=n,delete s.class;break;default:delete s[r],s[q1(r)]=n}return s},{})}function kn(t,s,r){return r?cn.createElement(t.tag,{key:s,...po(t.attrs),...r},(t.children||[]).map((n,d)=>kn(n,`${s}-${t.tag}-${d}`))):cn.createElement(t.tag,{key:s,...po(t.attrs)},(t.children||[]).map((n,d)=>kn(n,`${s}-${t.tag}-${d}`)))}function Ec(t){return A1(t)[0]}function Dc(t){return t?Array.isArray(t)?t:[t]:[]}const Z1=`
.anticon {
  display: inline-flex;
  align-items: center;
  color: inherit;
  font-style: normal;
  line-height: 0;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.anticon > * {
  line-height: 1;
}

.anticon svg {
  display: inline-block;
}

.anticon::before {
  display: none;
}

.anticon .anticon-icon {
  display: block;
}

.anticon[tabindex] {
  cursor: pointer;
}

.anticon-spin::before,
.anticon-spin {
  display: inline-block;
  -webkit-animation: loadingCircle 1s infinite linear;
  animation: loadingCircle 1s infinite linear;
}

@-webkit-keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
`,X1=t=>{const{csp:s,prefixCls:r,layer:n}=u.useContext(Sc);let d=Z1;r&&(d=d.replace(/anticon/g,r)),n&&(d=`@layer ${n} {
${d}
}`),u.useEffect(()=>{const c=t.current,h=H1(c);B1(d,"@ant-design-icons",{prepend:!n,csp:s,attachTo:h})},[])},vs={primaryColor:"#333",secondaryColor:"#E6E6E6",calculated:!1};function Q1({primaryColor:t,secondaryColor:s}){vs.primaryColor=t,vs.secondaryColor=s||Ec(t),vs.calculated=!!s}function e0(){return{...vs}}const qa=t=>{const{icon:s,className:r,onClick:n,style:d,primaryColor:c,secondaryColor:h,...m}=t,y=u.useRef();let p=vs;if(c&&(p={primaryColor:c,secondaryColor:h||Ec(c)}),X1(y),J1(mo(s),`icon should be icon definiton, but got ${s}`),!mo(s))return null;let j=s;return j&&typeof j.icon=="function"&&(j={...j,icon:j.icon(p.primaryColor,p.secondaryColor)}),kn(j.icon,`svg-${j.name}`,{className:r,onClick:n,style:d,"data-icon":j.name,width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true",...m,ref:y})};qa.displayName="IconReact";qa.getTwoToneColors=e0;qa.setTwoToneColors=Q1;function Mc(t){const[s,r]=Dc(t);return qa.setTwoToneColors({primaryColor:s,secondaryColor:r})}function t0(){const t=qa.getTwoToneColors();return t.calculated?[t.primaryColor,t.secondaryColor]:t.primaryColor}function wn(){return wn=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},wn.apply(this,arguments)}Mc(bn.primary);const le=u.forwardRef((t,s)=>{const{className:r,icon:n,spin:d,rotate:c,tabIndex:h,onClick:m,twoToneColor:y,...p}=t,{prefixCls:j="anticon",rootClassName:x}=u.useContext(Sc),b=_f(x,j,{[`${j}-${n.name}`]:!!n.name,[`${j}-spin`]:!!d||n.name==="loading"},r);let C=h;C===void 0&&m&&(C=-1);const _=c?{msTransform:`rotate(${c}deg)`,transform:`rotate(${c}deg)`}:void 0,[w,g]=Dc(y);return u.createElement("span",wn({role:"img","aria-label":n.name},p,{ref:s,tabIndex:C,onClick:m,className:b}),u.createElement(qa,{icon:n,primaryColor:w,secondaryColor:g,style:_}))});le.displayName="AntdIcon";le.getTwoToneColor=t0;le.setTwoToneColor=Mc;var a0={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M917.7 148.8l-42.4-42.4c-1.6-1.6-3.6-2.3-5.7-2.3s-4.1.8-5.7 2.3l-76.1 76.1a199.27 199.27 0 00-112.1-34.3c-51.2 0-102.4 19.5-141.5 58.6L432.3 308.7a8.03 8.03 0 000 11.3L704 591.7c1.6 1.6 3.6 2.3 5.7 2.3 2 0 4.1-.8 5.7-2.3l101.9-101.9c68.9-69 77-175.7 24.3-253.5l76.1-76.1c3.1-3.2 3.1-8.3 0-11.4zM769.1 441.7l-59.4 59.4-186.8-186.8 59.4-59.4c24.9-24.9 58.1-38.7 93.4-38.7 35.3 0 68.4 13.7 93.4 38.7 24.9 24.9 38.7 58.1 38.7 93.4 0 35.3-13.8 68.4-38.7 93.4zm-190.2 105a8.03 8.03 0 00-11.3 0L501 613.3 410.7 523l66.7-66.7c3.1-3.1 3.1-8.2 0-11.3L441 408.6a8.03 8.03 0 00-11.3 0L363 475.3l-43-43a7.85 7.85 0 00-5.7-2.3c-2 0-4.1.8-5.7 2.3L206.8 534.2c-68.9 69-77 175.7-24.3 253.5l-76.1 76.1a8.03 8.03 0 000 11.3l42.4 42.4c1.6 1.6 3.6 2.3 5.7 2.3s4.1-.8 5.7-2.3l76.1-76.1c33.7 22.9 72.9 34.3 112.1 34.3 51.2 0 102.4-19.5 141.5-58.6l101.9-101.9c3.1-3.1 3.1-8.2 0-11.3l-43-43 66.7-66.7c3.1-3.1 3.1-8.2 0-11.3l-36.6-36.2zM441.7 769.1a131.32 131.32 0 01-93.4 38.7c-35.3 0-68.4-13.7-93.4-38.7a131.32 131.32 0 01-38.7-93.4c0-35.3 13.7-68.4 38.7-93.4l59.4-59.4 186.8 186.8-59.4 59.4z"}}]},name:"api",theme:"outlined"};function Sn(){return Sn=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Sn.apply(this,arguments)}const s0=(t,s)=>u.createElement(le,Sn({},t,{ref:s,icon:a0})),Vs=u.forwardRef(s0);var r0={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 144H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H212V212h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H612V212h200v200zM464 544H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H212V612h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H612V612h200v200z"}}]},name:"appstore",theme:"outlined"};function _n(){return _n=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},_n.apply(this,arguments)}const n0=(t,s)=>u.createElement(le,_n({},t,{ref:s,icon:r0})),i0=u.forwardRef(n0);var l0={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M888 792H200V168c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h752c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm-600-80h56c4.4 0 8-3.6 8-8V560c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v144c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V384c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v320c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V462c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v242c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V304c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v400c0 4.4 3.6 8 8 8z"}}]},name:"bar-chart",theme:"outlined"};function Tn(){return Tn=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Tn.apply(this,arguments)}const o0=(t,s)=>u.createElement(le,Tn({},t,{ref:s,icon:l0})),c0=u.forwardRef(o0);var d0={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M816 768h-24V428c0-141.1-104.3-257.7-240-277.1V112c0-22.1-17.9-40-40-40s-40 17.9-40 40v38.9c-135.7 19.4-240 136-240 277.1v340h-24c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h216c0 61.8 50.2 112 112 112s112-50.2 112-112h216c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM512 888c-26.5 0-48-21.5-48-48h96c0 26.5-21.5 48-48 48zM304 768V428c0-55.6 21.6-107.8 60.9-147.1S456.4 220 512 220c55.6 0 107.8 21.6 147.1 60.9S720 372.4 720 428v340H304z"}}]},name:"bell",theme:"outlined"};function Cn(){return Cn=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Cn.apply(this,arguments)}const u0=(t,s)=>u.createElement(le,Cn({},t,{ref:s,icon:d0})),hr=u.forwardRef(u0);var h0={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-260 72h96v209.9L621.5 312 572 347.4V136zm220 752H232V136h280v296.9c0 3.3 1 6.6 3 9.3a15.9 15.9 0 0022.3 3.7l83.8-59.9 81.4 59.4c2.7 2 6 3.1 9.4 3.1 8.8 0 16-7.2 16-16V136h64v752z"}}]},name:"book",theme:"outlined"};function On(){return On=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},On.apply(this,arguments)}const f0=(t,s)=>u.createElement(le,On({},t,{ref:s,icon:h0})),wr=u.forwardRef(f0);var m0={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M632 888H392c-4.4 0-8 3.6-8 8v32c0 17.7 14.3 32 32 32h192c17.7 0 32-14.3 32-32v-32c0-4.4-3.6-8-8-8zM512 64c-181.1 0-328 146.9-328 328 0 121.4 66 227.4 164 284.1V792c0 17.7 14.3 32 32 32h264c17.7 0 32-14.3 32-32V676.1c98-56.7 164-162.7 164-284.1 0-181.1-146.9-328-328-328zm127.9 549.8L604 634.6V752H420V634.6l-35.9-20.8C305.4 568.3 256 484.5 256 392c0-141.4 114.6-256 256-256s256 114.6 256 256c0 92.5-49.4 176.3-128.1 221.8z"}}]},name:"bulb",theme:"outlined"};function zn(){return zn=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},zn.apply(this,arguments)}const p0=(t,s)=>u.createElement(le,zn({},t,{ref:s,icon:m0})),Ac=u.forwardRef(p0);function Rn(){return Rn=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Rn.apply(this,arguments)}const g0=(t,s)=>u.createElement(le,Rn({},t,{ref:s,icon:Tf})),It=u.forwardRef(g0);var x0={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"};function En(){return En=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},En.apply(this,arguments)}const y0=(t,s)=>u.createElement(le,En({},t,{ref:s,icon:x0})),ea=u.forwardRef(y0);function Dn(){return Dn=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Dn.apply(this,arguments)}const b0=(t,s)=>u.createElement(le,Dn({},t,{ref:s,icon:Cf})),j0=u.forwardRef(b0);function Mn(){return Mn=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Mn.apply(this,arguments)}const v0=(t,s)=>u.createElement(le,Mn({},t,{ref:s,icon:Of})),Vi=u.forwardRef(v0);var k0={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm0 76c-205.4 0-372 166.6-372 372s166.6 372 372 372 372-166.6 372-372-166.6-372-372-372zm128.01 198.83c.03 0 .05.01.09.06l45.02 45.01a.2.2 0 01.05.09.12.12 0 010 .07c0 .02-.01.04-.05.08L557.25 512l127.87 127.86a.27.27 0 01.05.06v.02a.12.12 0 010 .07c0 .03-.01.05-.05.09l-45.02 45.02a.2.2 0 01-.09.05.12.12 0 01-.07 0c-.02 0-.04-.01-.08-.05L512 557.25 384.14 685.12c-.04.04-.06.05-.08.05a.12.12 0 01-.07 0c-.03 0-.05-.01-.09-.05l-45.02-45.02a.2.2 0 01-.05-.09.12.12 0 010-.07c0-.02.01-.04.06-.08L466.75 512 338.88 384.14a.27.27 0 01-.05-.06l-.01-.02a.12.12 0 010-.07c0-.03.01-.05.05-.09l45.02-45.02a.2.2 0 01.09-.05.12.12 0 01.07 0c.02 0 .04.01.08.06L512 466.75l127.86-127.86c.04-.05.06-.06.08-.06a.12.12 0 01.07 0z"}}]},name:"close-circle",theme:"outlined"};function An(){return An=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},An.apply(this,arguments)}const w0=(t,s)=>u.createElement(le,An({},t,{ref:s,icon:k0})),rr=u.forwardRef(w0);var S0={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M624 706.3h-74.1V464c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v242.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.7a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9z"}},{tag:"path",attrs:{d:"M811.4 366.7C765.6 245.9 648.9 160 512.2 160S258.8 245.8 213 366.6C127.3 389.1 64 467.2 64 560c0 110.5 89.5 200 199.9 200H304c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8h-40.1c-33.7 0-65.4-13.4-89-37.7-23.5-24.2-36-56.8-34.9-90.6.9-26.4 9.9-51.2 26.2-72.1 16.7-21.3 40.1-36.8 66.1-43.7l37.9-9.9 13.9-36.6c8.6-22.8 20.6-44.1 35.7-63.4a245.6 245.6 0 0152.4-49.9c41.1-28.9 89.5-44.2 140-44.2s98.9 15.3 140 44.2c19.9 14 37.5 30.8 52.4 49.9 15.1 19.3 27.1 40.7 35.7 63.4l13.8 36.5 37.8 10C846.1 454.5 884 503.8 884 560c0 33.1-12.9 64.3-36.3 87.7a123.07 123.07 0 01-87.6 36.3H720c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h40.1C870.5 760 960 670.5 960 560c0-92.7-63.1-170.7-148.6-193.3z"}}]},name:"cloud-download",theme:"outlined"};function In(){return In=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},In.apply(this,arguments)}const _0=(t,s)=>u.createElement(le,In({},t,{ref:s,icon:S0})),T0=u.forwardRef(_0);var C0={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M573 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40zm-280 0c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40z"}},{tag:"path",attrs:{d:"M894 345a343.92 343.92 0 00-189-130v.1c-17.1-19-36.4-36.5-58-52.1-163.7-119-393.5-82.7-513 81-96.3 133-92.2 311.9 6 439l.8 132.6c0 3.2.5 6.4 1.5 9.4a31.95 31.95 0 0040.1 20.9L309 806c33.5 11.9 68.1 18.7 102.5 20.6l-.5.4c89.1 64.9 205.9 84.4 313 49l127.1 41.4c3.2 1 6.5 1.6 9.9 1.6 17.7 0 32-14.3 32-32V753c88.1-119.6 90.4-284.9 1-408zM323 735l-12-5-99 31-1-104-8-9c-84.6-103.2-90.2-251.9-11-361 96.4-132.2 281.2-161.4 413-66 132.2 96.1 161.5 280.6 66 412-80.1 109.9-223.5 150.5-348 102zm505-17l-8 10 1 104-98-33-12 5c-56 20.8-115.7 22.5-171 7l-.2-.1A367.31 367.31 0 00729 676c76.4-105.3 88.8-237.6 44.4-350.4l.6.4c23 16.5 44.1 37.1 62 62 72.6 99.6 68.5 235.2-8 330z"}},{tag:"path",attrs:{d:"M433 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40z"}}]},name:"comment",theme:"outlined"};function Pn(){return Pn=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Pn.apply(this,arguments)}const O0=(t,s)=>u.createElement(le,Pn({},t,{ref:s,icon:C0})),da=u.forwardRef(O0);var z0={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 128c-212.1 0-384 171.9-384 384v360c0 13.3 10.7 24 24 24h184c35.3 0 64-28.7 64-64V624c0-35.3-28.7-64-64-64H200v-48c0-172.3 139.7-312 312-312s312 139.7 312 312v48H688c-35.3 0-64 28.7-64 64v208c0 35.3 28.7 64 64 64h184c13.3 0 24-10.7 24-24V512c0-212.1-171.9-384-384-384zM328 632v192H200V632h128zm496 192H696V632h128v192z"}}]},name:"customer-service",theme:"outlined"};function Ln(){return Ln=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ln.apply(this,arguments)}const R0=(t,s)=>u.createElement(le,Ln({},t,{ref:s,icon:z0})),E0=u.forwardRef(R0);var D0={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M924.8 385.6a446.7 446.7 0 00-96-142.4 446.7 446.7 0 00-142.4-96C631.1 123.8 572.5 112 512 112s-119.1 11.8-174.4 35.2a446.7 446.7 0 00-142.4 96 446.7 446.7 0 00-96 142.4C75.8 440.9 64 499.5 64 560c0 132.7 58.3 257.7 159.9 343.1l1.7 1.4c5.8 4.8 13.1 7.5 20.6 7.5h531.7c7.5 0 14.8-2.7 20.6-7.5l1.7-1.4C901.7 817.7 960 692.7 960 560c0-60.5-11.9-119.1-35.2-174.4zM761.4 836H262.6A371.12 371.12 0 01140 560c0-99.4 38.7-192.8 109-263 70.3-70.3 163.7-109 263-109 99.4 0 192.8 38.7 263 109 70.3 70.3 109 163.7 109 263 0 105.6-44.5 205.5-122.6 276zM623.5 421.5a8.03 8.03 0 00-11.3 0L527.7 506c-18.7-5-39.4-.2-54.1 14.5a55.95 55.95 0 000 79.2 55.95 55.95 0 0079.2 0 55.87 55.87 0 0014.5-54.1l84.5-84.5c3.1-3.1 3.1-8.2 0-11.3l-28.3-28.3zM490 320h44c4.4 0 8-3.6 8-8v-80c0-4.4-3.6-8-8-8h-44c-4.4 0-8 3.6-8 8v80c0 4.4 3.6 8 8 8zm260 218v44c0 4.4 3.6 8 8 8h80c4.4 0 8-3.6 8-8v-44c0-4.4-3.6-8-8-8h-80c-4.4 0-8 3.6-8 8zm12.7-197.2l-31.1-31.1a8.03 8.03 0 00-11.3 0l-56.6 56.6a8.03 8.03 0 000 11.3l31.1 31.1c3.1 3.1 8.2 3.1 11.3 0l56.6-56.6c3.1-3.1 3.1-8.2 0-11.3zm-458.6-31.1a8.03 8.03 0 00-11.3 0l-31.1 31.1a8.03 8.03 0 000 11.3l56.6 56.6c3.1 3.1 8.2 3.1 11.3 0l31.1-31.1c3.1-3.1 3.1-8.2 0-11.3l-56.6-56.6zM262 530h-80c-4.4 0-8 3.6-8 8v44c0 4.4 3.6 8 8 8h80c4.4 0 8-3.6 8-8v-44c0-4.4-3.6-8-8-8z"}}]},name:"dashboard",theme:"outlined"};function Nn(){return Nn=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Nn.apply(this,arguments)}const M0=(t,s)=>u.createElement(le,Nn({},t,{ref:s,icon:D0})),Va=u.forwardRef(M0);function Yn(){return Yn=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Yn.apply(this,arguments)}const A0=(t,s)=>u.createElement(le,Yn({},t,{ref:s,icon:zf})),Et=u.forwardRef(A0);function $n(){return $n=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},$n.apply(this,arguments)}const I0=(t,s)=>u.createElement(le,$n({},t,{ref:s,icon:Rf})),yt=u.forwardRef(I0);function Bn(){return Bn=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Bn.apply(this,arguments)}const P0=(t,s)=>u.createElement(le,Bn({},t,{ref:s,icon:Ef})),Rt=u.forwardRef(P0);var L0={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M688 312v-48c0-4.4-3.6-8-8-8H296c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h384c4.4 0 8-3.6 8-8zm-392 88c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H296zm376 116c-119.3 0-216 96.7-216 216s96.7 216 216 216 216-96.7 216-216-96.7-216-216-216zm107.5 323.5C750.8 868.2 712.6 884 672 884s-78.8-15.8-107.5-44.5C535.8 810.8 520 772.6 520 732s15.8-78.8 44.5-107.5C593.2 595.8 631.4 580 672 580s78.8 15.8 107.5 44.5C808.2 653.2 824 691.4 824 732s-15.8 78.8-44.5 107.5zM761 656h-44.3c-2.6 0-5 1.2-6.5 3.3l-63.5 87.8-23.1-31.9a7.92 7.92 0 00-6.5-3.3H573c-6.5 0-10.3 7.4-6.5 12.7l73.8 102.1c3.2 4.4 9.7 4.4 12.9 0l114.2-158c3.9-5.3.1-12.7-6.4-12.7zM440 852H208V148h560v344c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V108c0-17.7-14.3-32-32-32H168c-17.7 0-32 14.3-32 32v784c0 17.7 14.3 32 32 32h272c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8z"}}]},name:"file-done",theme:"outlined"};function Fn(){return Fn=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Fn.apply(this,arguments)}const N0=(t,s)=>u.createElement(le,Fn({},t,{ref:s,icon:L0})),Y0=u.forwardRef(N0);var $0={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7c6 6 9.4 14.1 9.4 22.6V928c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V96c0-17.7 14.3-32 32-32h424.7c8.5 0 16.7 3.4 22.7 9.4l215.2 215.3zM790.2 326L602 137.8V326h188.2zM320 482a8 8 0 00-8 8v48a8 8 0 008 8h384a8 8 0 008-8v-48a8 8 0 00-8-8H320zm0 136a8 8 0 00-8 8v48a8 8 0 008 8h184a8 8 0 008-8v-48a8 8 0 00-8-8H320z"}}]},name:"file-text",theme:"filled"};function Hn(){return Hn=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Hn.apply(this,arguments)}const B0=(t,s)=>u.createElement(le,Hn({},t,{ref:s,icon:$0})),F0=u.forwardRef(B0);function Wn(){return Wn=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Wn.apply(this,arguments)}const H0=(t,s)=>u.createElement(le,Wn({},t,{ref:s,icon:Df})),Xt=u.forwardRef(H0);var W0={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880.1 154H143.9c-24.5 0-39.8 26.7-27.5 48L349 597.4V838c0 17.7 14.2 32 31.8 32h262.4c17.6 0 31.8-14.3 31.8-32V597.4L907.7 202c12.2-21.3-3.1-48-27.6-48zM603.4 798H420.6V642h182.9v156zm9.6-236.6l-9.5 16.6h-183l-9.5-16.6L212.7 226h598.6L613 561.4z"}}]},name:"filter",theme:"outlined"};function Un(){return Un=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Un.apply(this,arguments)}const U0=(t,s)=>u.createElement(le,Un({},t,{ref:s,icon:W0})),Ic=u.forwardRef(U0);var V0={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M904 512h-56c-4.4 0-8 3.6-8 8v320H184V184h320c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V520c0-4.4-3.6-8-8-8z"}},{tag:"path",attrs:{d:"M355.9 534.9L354 653.8c-.1 8.9 7.1 16.2 16 16.2h.4l118-2.9c2-.1 4-.9 5.4-2.3l415.9-415c3.1-3.1 3.1-8.2 0-11.3L785.4 114.3c-1.6-1.6-3.6-2.3-5.7-2.3s-4.1.8-5.7 2.3l-415.8 415a8.3 8.3 0 00-2.3 5.6zm63.5 23.6L779.7 199l45.2 45.1-360.5 359.7-45.7 1.1.7-46.4z"}}]},name:"form",theme:"outlined"};function Vn(){return Vn=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Vn.apply(this,arguments)}const K0=(t,s)=>u.createElement(le,Vn({},t,{ref:s,icon:V0})),Kn=u.forwardRef(K0);var G0={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.4 800.9c.2-.3.5-.6.7-.9C920.6 722.1 960 621.7 960 512s-39.4-210.1-104.8-288c-.2-.3-.5-.5-.7-.8-1.1-1.3-2.1-2.5-3.2-3.7-.4-.5-.8-.9-1.2-1.4l-4.1-4.7-.1-.1c-1.5-1.7-3.1-3.4-4.6-5.1l-.1-.1c-3.2-3.4-6.4-6.8-9.7-10.1l-.1-.1-4.8-4.8-.3-.3c-1.5-1.5-3-2.9-4.5-4.3-.5-.5-1-1-1.6-1.5-1-1-2-1.9-3-2.8-.3-.3-.7-.6-1-1C736.4 109.2 629.5 64 512 64s-224.4 45.2-304.3 119.2c-.3.3-.7.6-1 1-1 .9-2 1.9-3 2.9-.5.5-1 1-1.6 1.5-1.5 1.4-3 2.9-4.5 4.3l-.3.3-4.8 4.8-.1.1c-3.3 3.3-6.5 6.7-9.7 10.1l-.1.1c-1.6 1.7-3.1 3.4-4.6 5.1l-.1.1c-1.4 1.5-2.8 3.1-4.1 4.7-.4.5-.8.9-1.2 1.4-1.1 1.2-2.1 2.5-3.2 3.7-.2.3-.5.5-.7.8C103.4 301.9 64 402.3 64 512s39.4 210.1 104.8 288c.2.3.5.6.7.9l3.1 3.7c.4.5.8.9 1.2 1.4l4.1 4.7c0 .1.1.1.1.2 1.5 1.7 3 3.4 4.6 5l.1.1c3.2 3.4 6.4 6.8 9.6 10.1l.1.1c1.6 1.6 3.1 3.2 4.7 4.7l.3.3c3.3 3.3 6.7 6.5 10.1 9.6 80.1 74 187 119.2 304.5 119.2s224.4-45.2 304.3-119.2a300 300 0 0010-9.6l.3-.3c1.6-1.6 3.2-3.1 4.7-4.7l.1-.1c3.3-3.3 6.5-6.7 9.6-10.1l.1-.1c1.5-1.7 3.1-3.3 4.6-5 0-.1.1-.1.1-.2 1.4-1.5 2.8-3.1 4.1-4.7.4-.5.8-.9 1.2-1.4a99 99 0 003.3-3.7zm4.1-142.6c-13.8 32.6-32 62.8-54.2 90.2a444.07 444.07 0 00-81.5-55.9c11.6-46.9 18.8-98.4 20.7-152.6H887c-3 40.9-12.6 80.6-28.5 118.3zM887 484H743.5c-1.9-54.2-9.1-105.7-20.7-152.6 29.3-15.6 56.6-34.4 81.5-55.9A373.86 373.86 0 01887 484zM658.3 165.5c39.7 16.8 75.8 40 107.6 69.2a394.72 394.72 0 01-59.4 41.8c-15.7-45-35.8-84.1-59.2-115.4 3.7 1.4 7.4 2.9 11 4.4zm-90.6 700.6c-9.2 7.2-18.4 12.7-27.7 16.4V697a389.1 389.1 0 01115.7 26.2c-8.3 24.6-17.9 47.3-29 67.8-17.4 32.4-37.8 58.3-59 75.1zm59-633.1c11 20.6 20.7 43.3 29 67.8A389.1 389.1 0 01540 327V141.6c9.2 3.7 18.5 9.1 27.7 16.4 21.2 16.7 41.6 42.6 59 75zM540 640.9V540h147.5c-1.6 44.2-7.1 87.1-16.3 127.8l-.3 1.2A445.02 445.02 0 00540 640.9zm0-156.9V383.1c45.8-2.8 89.8-12.5 130.9-28.1l.3 1.2c9.2 40.7 14.7 83.5 16.3 127.8H540zm-56 56v100.9c-45.8 2.8-89.8 12.5-130.9 28.1l-.3-1.2c-9.2-40.7-14.7-83.5-16.3-127.8H484zm-147.5-56c1.6-44.2 7.1-87.1 16.3-127.8l.3-1.2c41.1 15.6 85 25.3 130.9 28.1V484H336.5zM484 697v185.4c-9.2-3.7-18.5-9.1-27.7-16.4-21.2-16.7-41.7-42.7-59.1-75.1-11-20.6-20.7-43.3-29-67.8 37.2-14.6 75.9-23.3 115.8-26.1zm0-370a389.1 389.1 0 01-115.7-26.2c8.3-24.6 17.9-47.3 29-67.8 17.4-32.4 37.8-58.4 59.1-75.1 9.2-7.2 18.4-12.7 27.7-16.4V327zM365.7 165.5c3.7-1.5 7.3-3 11-4.4-23.4 31.3-43.5 70.4-59.2 115.4-21-12-40.9-26-59.4-41.8 31.8-29.2 67.9-52.4 107.6-69.2zM165.5 365.7c13.8-32.6 32-62.8 54.2-90.2 24.9 21.5 52.2 40.3 81.5 55.9-11.6 46.9-18.8 98.4-20.7 152.6H137c3-40.9 12.6-80.6 28.5-118.3zM137 540h143.5c1.9 54.2 9.1 105.7 20.7 152.6a444.07 444.07 0 00-81.5 55.9A373.86 373.86 0 01137 540zm228.7 318.5c-39.7-16.8-75.8-40-107.6-69.2 18.5-15.8 38.4-29.7 59.4-41.8 15.7 45 35.8 84.1 59.2 115.4-3.7-1.4-7.4-2.9-11-4.4zm292.6 0c-3.7 1.5-7.3 3-11 4.4 23.4-31.3 43.5-70.4 59.2-115.4 21 12 40.9 26 59.4 41.8a373.81 373.81 0 01-107.6 69.2z"}}]},name:"global",theme:"outlined"};function Gn(){return Gn=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Gn.apply(this,arguments)}const q0=(t,s)=>u.createElement(le,Gn({},t,{ref:s,icon:G0})),Ki=u.forwardRef(q0);var J0={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M536.1 273H488c-4.4 0-8 3.6-8 8v275.3c0 2.6 1.2 5 3.3 6.5l165.3 120.7c3.6 2.6 8.6 1.9 11.2-1.7l28.6-39c2.7-3.7 1.9-8.7-1.7-11.2L544.1 528.5V281c0-4.4-3.6-8-8-8zm219.8 75.2l156.8 38.3c5 1.2 9.9-2.6 9.9-7.7l.8-161.5c0-6.7-7.7-10.5-12.9-6.3L752.9 334.1a8 8 0 003 14.1zm167.7 301.1l-56.7-19.5a8 8 0 00-10.1 4.8c-1.9 5.1-3.9 10.1-6 15.1-17.8 42.1-43.3 80-75.9 112.5a353 353 0 01-112.5 75.9 352.18 352.18 0 01-137.7 27.8c-47.8 0-94.1-9.3-137.7-27.8a353 353 0 01-112.5-75.9c-32.5-32.5-58-70.4-75.9-112.5A353.44 353.44 0 01171 512c0-47.8 9.3-94.2 27.8-137.8 17.8-42.1 43.3-80 75.9-112.5a353 353 0 01112.5-75.9C430.6 167.3 477 158 524.8 158s94.1 9.3 137.7 27.8A353 353 0 01775 261.7c10.2 10.3 19.8 21 28.6 32.3l59.8-46.8C784.7 146.6 662.2 81.9 524.6 82 285 82.1 92.6 276.7 95 516.4 97.4 751.9 288.9 942 524.8 942c185.5 0 343.5-117.6 403.7-282.3 1.5-4.2-.7-8.9-4.9-10.4z"}}]},name:"history",theme:"outlined"};function qn(){return qn=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},qn.apply(this,arguments)}const Z0=(t,s)=>u.createElement(le,qn({},t,{ref:s,icon:J0})),Jn=u.forwardRef(Z0);var X0={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M946.5 505L560.1 118.8l-25.9-25.9a31.5 31.5 0 00-44.4 0L77.5 505a63.9 63.9 0 00-18.8 46c.4 35.2 29.7 63.3 64.9 63.3h42.5V940h691.8V614.3h43.4c17.1 0 33.2-6.7 45.3-18.8a63.6 63.6 0 0018.7-45.3c0-17-6.7-33.1-18.8-45.2zM568 868H456V664h112v204zm217.9-325.7V868H632V640c0-22.1-17.9-40-40-40H432c-22.1 0-40 17.9-40 40v228H238.1V542.3h-96l370-369.7 23.1 23.1L882 542.3h-96.1z"}}]},name:"home",theme:"outlined"};function Zn(){return Zn=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Zn.apply(this,arguments)}const Q0=(t,s)=>u.createElement(le,Zn({},t,{ref:s,icon:X0})),eg=u.forwardRef(Q0);var tg={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M574 665.4a8.03 8.03 0 00-11.3 0L446.5 781.6c-53.8 53.8-144.6 59.5-204 0-59.5-59.5-53.8-150.2 0-204l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3l-39.8-39.8a8.03 8.03 0 00-11.3 0L191.4 526.5c-84.6 84.6-84.6 221.5 0 306s221.5 84.6 306 0l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3L574 665.4zm258.6-474c-84.6-84.6-221.5-84.6-306 0L410.3 307.6a8.03 8.03 0 000 11.3l39.7 39.7c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c53.8-53.8 144.6-59.5 204 0 59.5 59.5 53.8 150.2 0 204L665.3 562.6a8.03 8.03 0 000 11.3l39.8 39.8c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c84.5-84.6 84.5-221.5 0-306.1zM610.1 372.3a8.03 8.03 0 00-11.3 0L372.3 598.7a8.03 8.03 0 000 11.3l39.6 39.6c3.1 3.1 8.2 3.1 11.3 0l226.4-226.4c3.1-3.1 3.1-8.2 0-11.3l-39.5-39.6z"}}]},name:"link",theme:"outlined"};function Xn(){return Xn=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Xn.apply(this,arguments)}const ag=(t,s)=>u.createElement(le,Xn({},t,{ref:s,icon:tg})),sg=u.forwardRef(ag);function Qn(){return Qn=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Qn.apply(this,arguments)}const rg=(t,s)=>u.createElement(le,Qn({},t,{ref:s,icon:Mf})),Wa=u.forwardRef(rg);var ng={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 10-56 0z"}}]},name:"lock",theme:"outlined"};function ei(){return ei=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ei.apply(this,arguments)}const ig=(t,s)=>u.createElement(le,ei({},t,{ref:s,icon:ng})),Pc=u.forwardRef(ig);var lg={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 732h-70.3c-4.8 0-9.3 2.1-12.3 5.8-7 8.5-14.5 16.7-22.4 24.5a353.84 353.84 0 01-112.7 75.9A352.8 352.8 0 01512.4 866c-47.9 0-94.3-9.4-137.9-27.8a353.84 353.84 0 01-112.7-75.9 353.28 353.28 0 01-76-112.5C167.3 606.2 158 559.9 158 512s9.4-94.2 27.8-137.8c17.8-42.1 43.4-80 76-112.5s70.5-58.1 112.7-75.9c43.6-18.4 90-27.8 137.9-27.8 47.9 0 94.3 9.3 137.9 27.8 42.2 17.8 80.1 43.4 112.7 75.9 7.9 7.9 15.3 16.1 22.4 24.5 3 3.7 7.6 5.8 12.3 5.8H868c6.3 0 10.2-7 6.7-12.3C798 160.5 663.8 81.6 511.3 82 271.7 82.6 79.6 277.1 82 516.4 84.4 751.9 276.2 942 512.4 942c152.1 0 285.7-78.8 362.3-197.7 3.4-5.3-.4-12.3-6.7-12.3zm88.9-226.3L815 393.7c-5.3-4.2-13-.4-13 6.3v76H488c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h314v76c0 6.7 7.8 10.5 13 6.3l141.9-112a8 8 0 000-12.6z"}}]},name:"logout",theme:"outlined"};function ti(){return ti=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ti.apply(this,arguments)}const og=(t,s)=>u.createElement(le,ti({},t,{ref:s,icon:lg})),cg=u.forwardRef(og);var dg={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"}}]},name:"mail",theme:"outlined"};function ai(){return ai=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ai.apply(this,arguments)}const ug=(t,s)=>u.createElement(le,ai({},t,{ref:s,icon:dg})),Sr=u.forwardRef(ug);var hg={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 000 13.8z"}}]},name:"menu-fold",theme:"outlined"};function si(){return si=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},si.apply(this,arguments)}const fg=(t,s)=>u.createElement(le,si({},t,{ref:s,icon:hg})),go=u.forwardRef(fg);var mg={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM142.4 642.1L298.7 519a8.84 8.84 0 000-13.9L142.4 381.9c-5.8-4.6-14.4-.5-14.4 6.9v246.3a8.9 8.9 0 0014.4 7z"}}]},name:"menu-unfold",theme:"outlined"};function ri(){return ri=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ri.apply(this,arguments)}const pg=(t,s)=>u.createElement(le,ri({},t,{ref:s,icon:mg})),xo=u.forwardRef(pg);var gg={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 512a48 48 0 1096 0 48 48 0 10-96 0zm200 0a48 48 0 1096 0 48 48 0 10-96 0zm-400 0a48 48 0 1096 0 48 48 0 10-96 0zm661.2-173.6c-22.6-53.7-55-101.9-96.3-143.3a444.35 444.35 0 00-143.3-96.3C630.6 75.7 572.2 64 512 64h-2c-60.6.3-119.3 12.3-174.5 35.9a445.35 445.35 0 00-142 96.5c-40.9 41.3-73 89.3-95.2 142.8-23 55.4-34.6 114.3-34.3 174.9A449.4 449.4 0 00112 714v152a46 46 0 0046 46h152.1A449.4 449.4 0 00510 960h2.1c59.9 0 118-11.6 172.7-34.3a444.48 444.48 0 00142.8-95.2c41.3-40.9 73.8-88.7 96.5-142 23.6-55.2 35.6-113.9 35.9-174.5.3-60.9-11.5-120-34.8-175.6zm-151.1 438C704 845.8 611 884 512 884h-1.7c-60.3-.3-120.2-15.3-173.1-43.5l-8.4-4.5H188V695.2l-4.5-8.4C155.3 633.9 140.3 574 140 513.7c-.4-99.7 37.7-193.3 107.6-263.8 69.8-70.5 163.1-109.5 262.8-109.9h1.7c50 0 98.5 9.7 144.2 28.9 44.6 18.7 84.6 45.6 119 80 34.3 34.3 61.3 74.4 80 119 19.4 46.2 29.1 95.2 28.9 145.8-.6 99.6-39.7 192.9-110.1 262.7z"}}]},name:"message",theme:"outlined"};function ni(){return ni=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ni.apply(this,arguments)}const xg=(t,s)=>u.createElement(le,ni({},t,{ref:s,icon:gg})),ua=u.forwardRef(xg);var yg={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M489.5 111.66c30.65-1.8 45.98 36.44 22.58 56.33A243.35 243.35 0 00426 354c0 134.76 109.24 244 244 244 72.58 0 139.9-31.83 186.01-86.08 19.87-23.38 58.07-8.1 56.34 22.53C900.4 745.82 725.15 912 512.5 912 291.31 912 112 732.69 112 511.5c0-211.39 164.29-386.02 374.2-399.65l.2-.01zm-81.15 79.75l-4.11 1.36C271.1 237.94 176 364.09 176 511.5 176 697.34 326.66 848 512.5 848c148.28 0 274.94-96.2 319.45-230.41l.63-1.93-.11.07a307.06 307.06 0 01-159.73 46.26L670 662c-170.1 0-308-137.9-308-308 0-58.6 16.48-114.54 46.27-162.47z"}}]},name:"moon",theme:"outlined"};function ii(){return ii=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ii.apply(this,arguments)}const bg=(t,s)=>u.createElement(le,ii({},t,{ref:s,icon:yg})),jg=u.forwardRef(bg);var vg={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm-88-532h-48c-4.4 0-8 3.6-8 8v304c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V360c0-4.4-3.6-8-8-8zm224 0h-48c-4.4 0-8 3.6-8 8v304c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V360c0-4.4-3.6-8-8-8z"}}]},name:"pause-circle",theme:"outlined"};function li(){return li=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},li.apply(this,arguments)}const kg=(t,s)=>u.createElement(le,li({},t,{ref:s,icon:vg})),yo=u.forwardRef(kg);var wg={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M877.1 238.7L770.6 132.3c-13-13-30.4-20.3-48.8-20.3s-35.8 7.2-48.8 20.3L558.3 246.8c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l89.6 89.7a405.46 405.46 0 01-86.4 127.3c-36.7 36.9-79.6 66-127.2 86.6l-89.6-89.7c-13-13-30.4-20.3-48.8-20.3a68.2 68.2 0 00-48.8 20.3L132.3 673c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l106.4 106.4c22.2 22.2 52.8 34.9 84.2 34.9 6.5 0 12.8-.5 19.2-1.6 132.4-21.8 263.8-92.3 369.9-198.3C818 606 888.4 474.6 910.4 342.1c6.3-37.6-6.3-76.3-33.3-103.4zm-37.6 91.5c-19.5 117.9-82.9 235.5-178.4 331s-213 158.9-330.9 178.4c-14.8 2.5-30-2.5-40.8-13.2L184.9 721.9 295.7 611l119.8 120 .9.9 21.6-8a481.29 481.29 0 00285.7-285.8l8-21.6-120.8-120.7 110.8-110.9 104.5 104.5c10.8 10.8 15.8 26 13.3 40.8z"}}]},name:"phone",theme:"outlined"};function oi(){return oi=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},oi.apply(this,arguments)}const Sg=(t,s)=>u.createElement(le,oi({},t,{ref:s,icon:wg})),Gi=u.forwardRef(Sg);var _g={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 632H136v-39.9l138.5-164.3 150.1 178L658.1 489 888 761.6V792zm0-129.8L664.2 396.8c-3.2-3.8-9-3.8-12.2 0L424.6 666.4l-144-170.7c-3.2-3.8-9-3.8-12.2 0L136 652.7V232h752v430.2zM304 456a88 88 0 100-176 88 88 0 000 176zm0-116c15.5 0 28 12.5 28 28s-12.5 28-28 28-28-12.5-28-28 12.5-28 28-28z"}}]},name:"picture",theme:"outlined"};function ci(){return ci=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ci.apply(this,arguments)}const Tg=(t,s)=>u.createElement(le,ci({},t,{ref:s,icon:_g})),Cg=u.forwardRef(Tg);var Og={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M719.4 499.1l-296.1-215A15.9 15.9 0 00398 297v430c0 13.1 14.8 20.5 25.3 12.9l296.1-215a15.9 15.9 0 000-25.8zm-257.6 134V390.9L628.5 512 461.8 633.1z"}}]},name:"play-circle",theme:"outlined"};function di(){return di=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},di.apply(this,arguments)}const zg=(t,s)=>u.createElement(le,di({},t,{ref:s,icon:Og})),Rg=u.forwardRef(zg);function ui(){return ui=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ui.apply(this,arguments)}const Eg=(t,s)=>u.createElement(le,ui({},t,{ref:s,icon:Af})),wt=u.forwardRef(Eg);function hi(){return hi=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},hi.apply(this,arguments)}const Dg=(t,s)=>u.createElement(le,hi({},t,{ref:s,icon:If})),Mg=u.forwardRef(Dg);var Ag={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M758.2 839.1C851.8 765.9 912 651.9 912 523.9 912 303 733.5 124.3 512.6 124 291.4 123.7 112 302.8 112 523.9c0 125.2 57.5 236.9 147.6 310.2 3.5 2.8 8.6 2.2 11.4-1.3l39.4-50.5c2.7-3.4 2.1-8.3-1.2-11.1-8.1-6.6-15.9-13.7-23.4-21.2a318.64 318.64 0 01-68.6-101.7C200.4 609 192 567.1 192 523.9s8.4-85.1 25.1-124.5c16.1-38.1 39.2-72.3 68.6-101.7 29.4-29.4 63.6-52.5 101.7-68.6C426.9 212.4 468.8 204 512 204s85.1 8.4 124.5 25.1c38.1 16.1 72.3 39.2 101.7 68.6 29.4 29.4 52.5 63.6 68.6 101.7 16.7 39.4 25.1 81.3 25.1 124.5s-8.4 85.1-25.1 124.5a318.64 318.64 0 01-68.6 101.7c-9.3 9.3-19.1 18-29.3 26L668.2 724a8 8 0 00-14.1 3l-39.6 162.2c-1.2 5 2.6 9.9 7.7 9.9l167 .8c6.7 0 10.5-7.7 6.3-12.9l-37.3-47.9z"}}]},name:"redo",theme:"outlined"};function fi(){return fi=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},fi.apply(this,arguments)}const Ig=(t,s)=>u.createElement(le,fi({},t,{ref:s,icon:Ag})),Pg=u.forwardRef(Ig);function mi(){return mi=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},mi.apply(this,arguments)}const Lg=(t,s)=>u.createElement(le,mi({},t,{ref:s,icon:Pf})),qi=u.forwardRef(Lg);var Ng={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M300 328a60 60 0 10120 0 60 60 0 10-120 0zM852 64H172c-17.7 0-32 14.3-32 32v660c0 17.7 14.3 32 32 32h680c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-32 660H204V128h616v596zM604 328a60 60 0 10120 0 60 60 0 10-120 0zm250.2 556H169.8c-16.5 0-29.8 14.3-29.8 32v36c0 4.4 3.3 8 7.4 8h729.1c4.1 0 7.4-3.6 7.4-8v-36c.1-17.7-13.2-32-29.7-32zM664 508H360c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h304c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"robot",theme:"outlined"};function pi(){return pi=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},pi.apply(this,arguments)}const Yg=(t,s)=>u.createElement(le,pi({},t,{ref:s,icon:Ng})),ha=u.forwardRef(Yg);function gi(){return gi=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},gi.apply(this,arguments)}const $g=(t,s)=>u.createElement(le,gi({},t,{ref:s,icon:Lf})),ta=u.forwardRef($g);var Bg={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M931.4 498.9L94.9 79.5c-3.4-1.7-7.3-2.1-11-1.2a15.99 15.99 0 00-11.7 19.3l86.2 352.2c1.3 5.3 5.2 9.6 10.4 11.3l147.7 50.7-147.6 50.7c-5.2 1.8-9.1 6-10.3 11.3L72.2 926.5c-.9 3.7-.5 7.6 1.2 10.9 3.9 7.9 13.5 11.1 21.5 7.2l836.5-417c3.1-1.5 5.6-4.1 7.2-7.1 3.9-8 .7-17.6-7.2-21.6zM170.8 826.3l50.3-205.6 295.2-101.3c2.3-.8 4.2-2.6 5-5 1.4-4.2-.8-8.7-5-10.2L221.1 403 171 198.2l628 314.9-628.2 313.2z"}}]},name:"send",theme:"outlined"};function xi(){return xi=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},xi.apply(this,arguments)}const Fg=(t,s)=>u.createElement(le,xi({},t,{ref:s,icon:Bg})),Lc=u.forwardRef(Fg);var Hg={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 009.3-35.2l-.9-2.6a443.74 443.74 0 00-79.7-137.9l-1.8-2.1a32.12 32.12 0 00-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 00-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 00-25.8 25.7l-15.8 85.4a351.86 351.86 0 00-99 57.4l-81.9-29.1a32 32 0 00-35.1 9.5l-1.8 2.1a446.02 446.02 0 00-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 00-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0035.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0025.8 25.7l2.7.5a449.4 449.4 0 00159 0l2.7-.5a32.05 32.05 0 0025.8-25.7l15.7-85a350 350 0 0099.7-57.6l81.3 28.9a32 32 0 0035.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM788.3 465.9c2.5 15.1 3.8 30.6 3.8 46.1s-1.3 31-3.8 46.1l-6.6 40.1 74.7 63.9a370.03 370.03 0 01-42.6 73.6L721 702.8l-31.4 25.8c-23.9 19.6-50.5 35-79.3 45.8l-38.1 14.3-17.9 97a377.5 377.5 0 01-85 0l-17.9-97.2-37.8-14.5c-28.5-10.8-55-26.2-78.7-45.7l-31.4-25.9-93.4 33.2c-17-22.9-31.2-47.6-42.6-73.6l75.5-64.5-6.5-40c-2.4-14.9-3.7-30.3-3.7-45.5 0-15.3 1.2-30.6 3.7-45.5l6.5-40-75.5-64.5c11.3-26.1 25.6-50.7 42.6-73.6l93.4 33.2 31.4-25.9c23.7-19.5 50.2-34.9 78.7-45.7l37.9-14.3 17.9-97.2c28.1-3.2 56.8-3.2 85 0l17.9 97 38.1 14.3c28.7 10.8 55.4 26.2 79.3 45.8l31.4 25.8 92.8-32.9c17 22.9 31.2 47.6 42.6 73.6L781.8 426l6.5 39.9zM512 326c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 614c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 502c0-29.9 11.7-58 32.8-79.2C454 401.6 482.1 390 512 390c29.9 0 58 11.6 79.2 32.8A111.6 111.6 0 01624 502c0 29.9-11.7 58-32.8 79.2z"}}]},name:"setting",theme:"outlined"};function yi(){return yi=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},yi.apply(this,arguments)}const Wg=(t,s)=>u.createElement(le,yi({},t,{ref:s,icon:Hg})),_s=u.forwardRef(Wg);var Ug={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M882 272.1V144c0-17.7-14.3-32-32-32H174c-17.7 0-32 14.3-32 32v128.1c-16.7 1-30 14.9-30 31.9v131.7a177 177 0 0014.4 70.4c4.3 10.2 9.6 19.8 15.6 28.9v345c0 17.6 14.3 32 32 32h676c17.7 0 32-14.3 32-32V535a175 175 0 0015.6-28.9c9.5-22.3 14.4-46 14.4-70.4V304c0-17-13.3-30.9-30-31.9zM214 184h596v88H214v-88zm362 656.1H448V736h128v104.1zm234 0H640V704c0-17.7-14.3-32-32-32H416c-17.7 0-32 14.3-32 32v136.1H214V597.9c2.9 1.4 5.9 2.8 9 4 22.3 9.4 46 14.1 70.4 14.1s48-4.7 70.4-14.1c13.8-5.8 26.8-13.2 38.7-22.1.2-.1.4-.1.6 0a180.4 180.4 0 0038.7 22.1c22.3 9.4 46 14.1 70.4 14.1 24.4 0 48-4.7 70.4-14.1 13.8-5.8 26.8-13.2 38.7-22.1.2-.1.4-.1.6 0a180.4 180.4 0 0038.7 22.1c22.3 9.4 46 14.1 70.4 14.1 24.4 0 48-4.7 70.4-14.1 3-1.3 6-2.6 9-4v242.2zm30-404.4c0 59.8-49 108.3-109.3 108.3-40.8 0-76.4-22.1-95.2-54.9-2.9-5-8.1-8.1-13.9-8.1h-.6c-5.7 0-11 3.1-13.9 8.1A109.24 109.24 0 01512 544c-40.7 0-76.2-22-95-54.7-3-5.1-8.4-8.3-14.3-8.3s-11.4 3.2-14.3 8.3a109.63 109.63 0 01-95.1 54.7C233 544 184 495.5 184 435.7v-91.2c0-.3.2-.5.5-.5h655c.3 0 .5.2.5.5v91.2z"}}]},name:"shop",theme:"outlined"};function bi(){return bi=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},bi.apply(this,arguments)}const Vg=(t,s)=>u.createElement(le,bi({},t,{ref:s,icon:Ug})),ji=u.forwardRef(Vg);var Kg={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M625.9 115c-5.9 0-11.9 1.6-17.4 5.3L254 352H90c-8.8 0-16 7.2-16 16v288c0 8.8 7.2 16 16 16h164l354.5 231.7c5.5 3.6 11.6 5.3 17.4 5.3 16.7 0 32.1-13.3 32.1-32.1V147.1c0-18.8-15.4-32.1-32.1-32.1zM586 803L293.4 611.7l-18-11.7H146V424h129.4l17.9-11.7L586 221v582zm348-327H806c-8.8 0-16 7.2-16 16v40c0 8.8 7.2 16 16 16h128c8.8 0 16-7.2 16-16v-40c0-8.8-7.2-16-16-16zm-41.9 261.8l-110.3-63.7a15.9 15.9 0 00-21.7 5.9l-19.9 34.5c-4.4 7.6-1.8 17.4 5.8 21.8L856.3 800a15.9 15.9 0 0021.7-5.9l19.9-34.5c4.4-7.6 1.7-17.4-5.8-21.8zM760 344a15.9 15.9 0 0021.7 5.9L892 286.2c7.6-4.4 10.2-14.2 5.8-21.8L878 230a15.9 15.9 0 00-21.7-5.9L746 287.8a15.99 15.99 0 00-5.8 21.8L760 344z"}}]},name:"sound",theme:"outlined"};function vi(){return vi=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},vi.apply(this,arguments)}const Gg=(t,s)=>u.createElement(le,vi({},t,{ref:s,icon:Kg})),qg=u.forwardRef(Gg);function ki(){return ki=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ki.apply(this,arguments)}const Jg=(t,s)=>u.createElement(le,ki({},t,{ref:s,icon:Nf})),Zg=u.forwardRef(Jg);var Xg={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"}}]},name:"stop",theme:"outlined"};function wi(){return wi=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},wi.apply(this,arguments)}const Qg=(t,s)=>u.createElement(le,wi({},t,{ref:s,icon:Xg})),ex=u.forwardRef(Qg);var tx={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M548 818v126a16 16 0 01-16 16h-40a16 16 0 01-16-16V818c15.85 1.64 27.84 2.46 36 2.46 8.15 0 20.16-.82 36-2.46m205.25-115.66l89.1 89.1a16 16 0 010 22.62l-28.29 28.29a16 16 0 01-22.62 0l-89.1-89.1c12.37-10.04 21.43-17.95 27.2-23.71 5.76-5.77 13.67-14.84 23.71-27.2m-482.5 0c10.04 12.36 17.95 21.43 23.71 27.2 5.77 5.76 14.84 13.67 27.2 23.71l-89.1 89.1a16 16 0 01-22.62 0l-28.29-28.29a16 16 0 010-22.63zM512 278c129.24 0 234 104.77 234 234S641.24 746 512 746 278 641.24 278 512s104.77-234 234-234m0 72c-89.47 0-162 72.53-162 162s72.53 162 162 162 162-72.53 162-162-72.53-162-162-162M206 476c-1.64 15.85-2.46 27.84-2.46 36 0 8.15.82 20.16 2.46 36H80a16 16 0 01-16-16v-40a16 16 0 0116-16zm738 0a16 16 0 0116 16v40a16 16 0 01-16 16H818c1.64-15.85 2.46-27.84 2.46-36 0-8.15-.82-20.16-2.46-36zM814.06 180.65l28.29 28.29a16 16 0 010 22.63l-89.1 89.09c-10.04-12.37-17.95-21.43-23.71-27.2-5.77-5.76-14.84-13.67-27.2-23.71l89.1-89.1a16 16 0 0122.62 0m-581.5 0l89.1 89.1c-12.37 10.04-21.43 17.95-27.2 23.71-5.76 5.77-13.67 14.84-23.71 27.2l-89.1-89.1a16 16 0 010-22.62l28.29-28.29a16 16 0 0122.62 0M532 64a16 16 0 0116 16v126c-15.85-1.64-27.84-2.46-36-2.46-8.15 0-20.16.82-36 2.46V80a16 16 0 0116-16z"}}]},name:"sun",theme:"outlined"};function Si(){return Si=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Si.apply(this,arguments)}const ax=(t,s)=>u.createElement(le,Si({},t,{ref:s,icon:tx})),sx=u.forwardRef(ax);function _i(){return _i=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},_i.apply(this,arguments)}const rx=(t,s)=>u.createElement(le,_i({},t,{ref:s,icon:Yf})),nx=u.forwardRef(rx);var ix={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 01755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 01512.1 856a342.24 342.24 0 01-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 00-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 00-8-8.2z"}}]},name:"sync",theme:"outlined"};function Ti(){return Ti=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ti.apply(this,arguments)}const lx=(t,s)=>u.createElement(le,Ti({},t,{ref:s,icon:ix})),ox=u.forwardRef(lx);var cx={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M938 458.8l-29.6-312.6c-1.5-16.2-14.4-29-30.6-30.6L565.2 86h-.4c-3.2 0-5.7 1-7.6 2.9L88.9 557.2a9.96 9.96 0 000 14.1l363.8 363.8c1.9 1.9 4.4 2.9 7.1 2.9s5.2-1 7.1-2.9l468.3-468.3c2-2.1 3-5 2.8-8zM459.7 834.7L189.3 564.3 589 164.6 836 188l23.4 247-399.7 399.7zM680 256c-48.5 0-88 39.5-88 88s39.5 88 88 88 88-39.5 88-88-39.5-88-88-88zm0 120c-17.7 0-32-14.3-32-32s14.3-32 32-32 32 14.3 32 32-14.3 32-32 32z"}}]},name:"tag",theme:"outlined"};function Ci(){return Ci=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ci.apply(this,arguments)}const dx=(t,s)=>u.createElement(le,Ci({},t,{ref:s,icon:cx})),ux=u.forwardRef(dx);var hx={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M824.2 699.9a301.55 301.55 0 00-86.4-60.4C783.1 602.8 812 546.8 812 484c0-110.8-92.4-201.7-203.2-200-109.1 1.7-197 90.6-197 200 0 62.8 29 118.8 74.2 155.5a300.95 300.95 0 00-86.4 60.4C345 754.6 314 826.8 312 903.8a8 8 0 008 8.2h56c4.3 0 7.9-3.4 8-7.7 1.9-58 25.4-112.3 66.7-153.5A226.62 226.62 0 01612 684c60.9 0 118.2 23.7 161.3 66.8C814.5 792 838 846.3 840 904.3c.1 4.3 3.7 7.7 8 7.7h56a8 8 0 008-8.2c-2-77-33-149.2-87.8-203.9zM612 612c-34.2 0-66.4-13.3-90.5-37.5a126.86 126.86 0 01-37.5-91.8c.3-32.8 13.4-64.5 36.3-88 24-24.6 56.1-38.3 90.4-38.7 33.9-.3 66.8 12.9 91 36.6 24.8 24.3 38.4 56.8 38.4 91.4 0 34.2-13.3 66.3-37.5 90.5A127.3 127.3 0 01612 612zM361.5 510.4c-.9-8.7-1.4-17.5-1.4-26.4 0-15.9 1.5-31.4 4.3-46.5.7-3.6-1.2-7.3-4.5-8.8-13.6-6.1-26.1-14.5-36.9-25.1a127.54 127.54 0 01-38.7-95.4c.9-32.1 13.8-62.6 36.3-85.6 24.7-25.3 57.9-39.1 93.2-38.7 31.9.3 62.7 12.6 86 34.4 7.9 7.4 14.7 15.6 20.4 24.4 2 3.1 5.9 4.4 9.3 3.2 17.6-6.1 36.2-10.4 55.3-12.4 5.6-.6 8.8-6.6 6.3-11.6-32.5-64.3-98.9-108.7-175.7-109.9-110.9-1.7-203.3 89.2-203.3 199.9 0 62.8 28.9 118.8 74.2 155.5-31.8 14.7-61.1 35-86.5 60.4-54.8 54.7-85.8 126.9-87.8 204a8 8 0 008 8.2h56.1c4.3 0 7.9-3.4 8-7.7 1.9-58 25.4-112.3 66.7-153.5 29.4-29.4 65.4-49.8 104.7-59.7 3.9-1 6.5-4.7 6-8.7z"}}]},name:"team",theme:"outlined"};function Oi(){return Oi=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Oi.apply(this,arguments)}const fx=(t,s)=>u.createElement(le,Oi({},t,{ref:s,icon:hx})),ks=u.forwardRef(fx);var mx={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M848 359.3H627.7L825.8 109c4.1-5.3.4-13-6.3-13H436c-2.8 0-5.5 1.5-6.9 4L170 547.5c-3.1 5.3.7 12 6.9 12h174.4l-89.4 357.6c-1.9 7.8 7.5 13.3 13.3 7.7L853.5 373c5.2-4.9 1.7-13.7-5.5-13.7zM378.2 732.5l60.3-241H281.1l189.6-327.4h224.6L487 427.4h211L378.2 732.5z"}}]},name:"thunderbolt",theme:"outlined"};function zi(){return zi=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},zi.apply(this,arguments)}const px=(t,s)=>u.createElement(le,zi({},t,{ref:s,icon:mx})),Zr=u.forwardRef(px);var gx={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 160h-92v-40c0-4.4-3.6-8-8-8H256c-4.4 0-8 3.6-8 8v40h-92a44 44 0 00-44 44v148c0 81.7 60 149.6 138.2 162C265.7 630.2 359 721.7 476 734.5v105.2H280c-17.7 0-32 14.3-32 32V904c0 4.4 3.6 8 8 8h512c4.4 0 8-3.6 8-8v-32.3c0-17.7-14.3-32-32-32H548V734.5C665 721.7 758.3 630.2 773.8 514 852 501.6 912 433.7 912 352V204a44 44 0 00-44-44zM184 352V232h64v207.6a91.99 91.99 0 01-64-87.6zm520 128c0 49.1-19.1 95.4-53.9 130.1-34.8 34.8-81 53.9-130.1 53.9h-16c-49.1 0-95.4-19.1-130.1-53.9-34.8-34.8-53.9-81-53.9-130.1V184h384v296zm136-128c0 41-26.9 75.8-64 87.6V232h64v120z"}}]},name:"trophy",theme:"outlined"};function Ri(){return Ri=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ri.apply(this,arguments)}const xx=(t,s)=>u.createElement(le,Ri({},t,{ref:s,icon:gx})),yx=u.forwardRef(xx);var bx={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"unordered-list",theme:"outlined"};function Ei(){return Ei=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ei.apply(this,arguments)}const jx=(t,s)=>u.createElement(le,Ei({},t,{ref:s,icon:bx})),vx=u.forwardRef(jx);var kx={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"}}]},name:"user",theme:"outlined"};function Di(){return Di=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Di.apply(this,arguments)}const wx=(t,s)=>u.createElement(le,Di({},t,{ref:s,icon:kx})),ot=u.forwardRef(wx);var Sx={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M892 772h-80v-80c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v80h-80c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h80v80c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-80h80c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zM373.5 498.4c-.9-8.7-1.4-17.5-1.4-26.4 0-15.9 1.5-31.4 4.3-46.5.7-3.6-1.2-7.3-4.5-8.8-13.6-6.1-26.1-14.5-36.9-25.1a127.54 127.54 0 01-38.7-95.4c.9-32.1 13.8-62.6 36.3-85.6 24.7-25.3 57.9-39.1 93.2-38.7 31.9.3 62.7 12.6 86 34.4 7.9 7.4 14.7 15.6 20.4 24.4 2 3.1 5.9 4.4 9.3 3.2 17.6-6.1 36.2-10.4 55.3-12.4 5.6-.6 8.8-6.6 6.3-11.6-32.5-64.3-98.9-108.7-175.7-109.9-110.8-1.7-203.2 89.2-203.2 200 0 62.8 28.9 118.8 74.2 155.5-31.8 14.7-61.1 35-86.5 60.4-54.8 54.7-85.8 126.9-87.8 204a8 8 0 008 8.2h56.1c4.3 0 7.9-3.4 8-7.7 1.9-58 25.4-112.3 66.7-153.5 29.4-29.4 65.4-49.8 104.7-59.7 3.8-1.1 6.4-4.8 5.9-8.8zM824 472c0-109.4-87.9-198.3-196.9-200C516.3 270.3 424 361.2 424 472c0 62.8 29 118.8 74.2 155.5a300.95 300.95 0 00-86.4 60.4C357 742.6 326 814.8 324 891.8a8 8 0 008 8.2h56c4.3 0 7.9-3.4 8-7.7 1.9-58 25.4-112.3 66.7-153.5C505.8 695.7 563 672 624 672c110.4 0 200-89.5 200-200zm-109.5 90.5C690.3 586.7 658.2 600 624 600s-66.3-13.3-90.5-37.5a127.26 127.26 0 01-37.5-91.8c.3-32.8 13.4-64.5 36.3-88 24-24.6 56.1-38.3 90.4-38.7 33.9-.3 66.8 12.9 91 36.6 24.8 24.3 38.4 56.8 38.4 91.4-.1 34.2-13.4 66.3-37.6 90.5z"}}]},name:"usergroup-add",theme:"outlined"};function Mi(){return Mi=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Mi.apply(this,arguments)}const _x=(t,s)=>u.createElement(le,Mi({},t,{ref:s,icon:Sx})),Tx=u.forwardRef(_x);var Cx={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 302.3L784 376V224c0-35.3-28.7-64-64-64H128c-35.3 0-64 28.7-64 64v576c0 35.3 28.7 64 64 64h592c35.3 0 64-28.7 64-64V648l128 73.7c21.3 12.3 48-3.1 48-27.6V330c0-24.6-26.7-40-48-27.7zM712 792H136V232h576v560zm176-167l-104-59.8V458.9L888 399v226zM208 360h112c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H208c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}}]},name:"video-camera",theme:"outlined"};function Ai(){return Ai=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ai.apply(this,arguments)}const Ox=(t,s)=>u.createElement(le,Ai({},t,{ref:s,icon:Cx})),zx=u.forwardRef(Ox);var Rx={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M713.5 599.9c-10.9-5.6-65.2-32.2-75.3-35.8-10.1-3.8-17.5-5.6-24.8 5.6-7.4 11.1-28.4 35.8-35 43.3-6.4 7.4-12.9 8.3-23.8 2.8-64.8-32.4-107.3-57.8-150-131.1-11.3-19.5 11.3-18.1 32.4-60.2 3.6-7.4 1.8-13.7-1-19.3-2.8-5.6-24.8-59.8-34-81.9-8.9-21.5-18.1-18.5-24.8-18.9-6.4-.4-13.7-.4-21.1-.4-7.4 0-19.3 2.8-29.4 13.7-10.1 11.1-38.6 37.8-38.6 92s39.5 106.7 44.9 114.1c5.6 7.4 77.7 118.6 188.4 166.5 70 30.2 97.4 32.8 132.4 27.6 21.3-3.2 65.2-26.6 74.3-52.5 9.1-25.8 9.1-47.9 6.4-52.5-2.7-4.9-10.1-7.7-21-13z"}},{tag:"path",attrs:{d:"M925.2 338.4c-22.6-53.7-55-101.9-96.3-143.3a444.35 444.35 0 00-143.3-96.3C630.6 75.7 572.2 64 512 64h-2c-60.6.3-119.3 12.3-174.5 35.9a445.35 445.35 0 00-142 96.5c-40.9 41.3-73 89.3-95.2 142.8-23 55.4-34.6 114.3-34.3 174.9A449.4 449.4 0 00112 714v152a46 46 0 0046 46h152.1A449.4 449.4 0 00510 960h2.1c59.9 0 118-11.6 172.7-34.3a444.48 444.48 0 00142.8-95.2c41.3-40.9 73.8-88.7 96.5-142 23.6-55.2 35.6-113.9 35.9-174.5.3-60.9-11.5-120-34.8-175.6zm-151.1 438C704 845.8 611 884 512 884h-1.7c-60.3-.3-120.2-15.3-173.1-43.5l-8.4-4.5H188V695.2l-4.5-8.4C155.3 633.9 140.3 574 140 513.7c-.4-99.7 37.7-193.3 107.6-263.8 69.8-70.5 163.1-109.5 262.8-109.9h1.7c50 0 98.5 9.7 144.2 28.9 44.6 18.7 84.6 45.6 119 80 34.3 34.3 61.3 74.4 80 119 19.4 46.2 29.1 95.2 28.9 145.8-.6 99.6-39.7 192.9-110.1 262.7z"}}]},name:"whats-app",theme:"outlined"};function Ii(){return Ii=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ii.apply(this,arguments)}const Ex=(t,s)=>u.createElement(le,Ii({},t,{ref:s,icon:Rx})),Pi=u.forwardRef(Ex),{Sider:Dx}=fa,Mx=["11","12","13","13-1","14","15","16"],Ax=["21","22","23","24","25","26","27","28"],Ix=["31","32","33","34"],bo=(t,s)=>{if((t==null?void 0:t.role)==="admin")return!0;if(!t||!t.permissions)return!1;const r=s.split(".");let n=t.permissions;for(let d=0;d<r.length;d++)if(typeof n=="object"&&n!==null&&r[d]in n)n=n[r[d]];else return!1;return typeof n=="boolean"?n:!1},Nc=t=>{const{permission:s,...r}=t;return t.children?{...r,children:t.children.map(Nc)}:r},Px=(t,s,r=0,n=0,d={running:!1,error:!1},c=0,h=0,m=0,y=0,p=0,j="",x=[])=>{const b=c+h+m+y,C=p,_=Mx.includes(j)||x.includes("sub2"),w=Ax.includes(j)||x.includes("sub3"),g=Ix.includes(j)||x.includes("sub4");return[{key:"1",icon:e.jsx(Va,{}),label:"Dashboard",onClick:()=>t("/"),permission:"dashboard"},{key:"content-creators",icon:e.jsx(ks,{}),label:"İçerik Üreticileri",onClick:()=>t("/content-creators"),permission:"publishers"},{key:"4",icon:e.jsx(Xt,{}),label:"Yayıncı Performans",onClick:()=>t("/publisher-performance"),permission:"tasks"},{key:"7",icon:e.jsx(Ac,{}),label:"AI Danışmanı",onClick:()=>t("/ai-advisor"),permission:"ai_advisor"},{key:"7-1",icon:e.jsx(ta,{}),label:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"5px"},children:["Yayıncı Keşfi",d.error?e.jsx(Ge,{status:"error",style:{marginLeft:5}}):d.running?e.jsx(Ge,{status:"success",style:{marginLeft:5}}):e.jsx(Ge,{status:"warning",style:{marginLeft:5}})]}),onClick:()=>t("/publisher-discovery"),permission:"ai_advisor"},{key:"divider1",type:"divider",label:""},{key:"sub1",icon:e.jsx(It,{}),label:"Etkinlik Yönetimi",permission:"events.view",children:[{key:"8",label:"Etkinlikler",icon:e.jsx(i0,{}),onClick:()=>t("/events"),permission:"events.view"},{key:"10",label:"PK Eşleştirici",icon:e.jsx(nx,{}),onClick:()=>t("/pk-matcher"),permission:"events.pk_matcher"}]},{key:"sub2",icon:e.jsx(wr,{}),label:e.jsxs("span",{children:["Akademi",!_&&C>0&&e.jsx(Ge,{count:C,size:"small",style:{marginLeft:8},overflowCount:99})]}),permission:"akademi.dashboard",children:[{key:"11",label:"Dashboard",icon:e.jsx(Va,{}),onClick:()=>t("/akademi/dashboard"),permission:"akademi.dashboard"},{key:"12",label:"Duyurular",icon:e.jsx(qg,{}),onClick:()=>t("/akademi/duyurular"),permission:"akademi.duyurular"},{key:"13",label:"Eğitimler",icon:e.jsx(zx,{}),onClick:()=>t("/akademi/egitimler"),permission:"akademi.egitimler"},{key:"13-1",label:"Etkinlikler",icon:e.jsx(It,{}),onClick:()=>t("/akademi/etkinlikler"),permission:"akademi.etkinlikler"},{key:"14",label:e.jsxs("span",{children:["Destek Talepleri",p>0&&e.jsx(Ge,{count:p,size:"small",style:{marginLeft:8},overflowCount:99})]}),icon:e.jsx(E0,{}),onClick:()=>t("/akademi/destek"),permission:"akademi.destek"},{key:"15",label:"Kullanıcılar",icon:e.jsx(ot,{}),onClick:()=>t("/akademi/kullanicilar"),permission:"akademi.kullanicilar"},{key:"16",label:"Yapay Zeka",icon:e.jsx(ha,{}),onClick:()=>t("/akademi/yapay-zeka"),permission:"akademi.ai"}]},{key:"sub3",icon:e.jsx(Ki,{}),label:e.jsxs("span",{children:["Site Yönetimi",!w&&b>0&&e.jsx(Ge,{count:b,size:"small",style:{marginLeft:8},overflowCount:99})]}),permission:"site_yonetimi.anasayfa",children:[{key:"21",label:"Ana Sayfa",icon:e.jsx(eg,{}),onClick:()=>t("/site/anasayfa"),permission:"site_yonetimi.anasayfa"},{key:"22",label:"Yayıncılarımız",icon:e.jsx(ks,{}),onClick:()=>t("/site/yayincilar"),permission:"site_yonetimi.yayincilar"},{key:"23",label:e.jsxs("span",{children:["Başvurular",c>0&&e.jsx(Ge,{count:c,size:"small",style:{marginLeft:8},overflowCount:99,showZero:!1})]}),icon:e.jsx(F0,{}),onClick:()=>t("/site/basvurular"),permission:"site_yonetimi.basvurular"},{key:"24",label:e.jsxs("span",{children:["İletişim Talepleri",h>0&&e.jsx(Ge,{count:h,size:"small",style:{marginLeft:8},overflowCount:99,showZero:!1})]}),icon:e.jsx(Sr,{}),onClick:()=>t("/site/iletisim-talepleri"),permission:"site_yonetimi.iletisim"},{key:"25",label:e.jsxs("span",{children:["Geri Arama",m>0&&e.jsx(Ge,{count:m,size:"small",style:{marginLeft:8},overflowCount:99,showZero:!1})]}),icon:e.jsx(Gi,{}),onClick:()=>t("/site/geriarama-talepleri"),permission:"site_yonetimi.geriarama"},{key:"26",label:e.jsxs("span",{children:["Online Toplantı",y>0&&e.jsx(Ge,{count:y,size:"small",style:{marginLeft:8},overflowCount:99,showZero:!1})]}),icon:e.jsx(da,{}),onClick:()=>t("/site/toplanti-talepleri"),permission:"site_yonetimi.toplanti"},{key:"27",label:"Toplu SMS",icon:e.jsx(ua,{}),onClick:()=>t("/site/toplu-sms"),permission:"site_yonetimi.sms"},{key:"28",label:"Blog Yönetimi",icon:e.jsx(Xt,{}),onClick:()=>t("/site/blog"),permission:"site_yonetimi.blog"}]},{key:"sub4",icon:e.jsx(ji,{}),label:e.jsxs("span",{children:["ETSY Operasyonu",!g&&n>0&&e.jsx(Ge,{count:n,size:"small",style:{marginLeft:8},overflowCount:99})]}),permission:"etsy_operasyonu.dashboard",children:[{key:"31",label:"Dashboard",icon:e.jsx(Va,{}),onClick:()=>t("/etsy/dashboard"),permission:"etsy_operasyonu.dashboard"},{key:"32",label:e.jsxs("span",{children:["Tasarım Onayları",n>0&&e.jsx(Ge,{count:n,size:"small",style:{marginLeft:8},overflowCount:99})]}),icon:e.jsx(Cg,{}),onClick:()=>t("/etsy/tasarim-onaylari"),permission:"etsy_operasyonu.tasarim"},{key:"33",label:"Ürünler",icon:e.jsx(ux,{}),onClick:()=>t("/etsy/urunler"),permission:"etsy_operasyonu.urunler"},{key:"34",label:"Ayarlar",icon:e.jsx(_s,{}),onClick:()=>t("/etsy/ayarlar"),permission:"etsy_operasyonu.ayarlar"}]},{key:"whatsapp",icon:e.jsx(Pi,{}),label:e.jsxs("span",{children:["WhatsApp",r>0&&e.jsx(Ge,{count:r,size:"small",style:{marginLeft:8},overflowCount:99})]}),onClick:()=>t("/whatsapp"),permission:"whatsapp"},{key:"business-discovery",icon:e.jsx(ji,{}),label:"İşletme Keşfi",onClick:()=>t("/business-discovery"),permission:"dashboard"},{key:"divider2",type:"divider",label:""},{key:"99",icon:e.jsx(ks,{}),label:"Kullanıcı Yönetimi",onClick:()=>t("/users"),permission:"users.manage"}].filter(T=>{if(T.type==="divider"||!T.permission)return!0;if(!bo(s,T.permission))return!1;if(T.children){const W=T.children.filter(G=>!G.permission||bo(s,G.permission));if(W.length===0)return!1;T.children=W}return!0}).map(Nc)},jo=t=>t==="/"?"1":t==="/content-creators"?"content-creators":t==="/publishers"?"2":t==="/influencers"?"3":t==="/publisher-performance"?"4":t==="/upload"?"6":t==="/ai-advisor"?"7":t==="/publisher-discovery"?"7-1":t==="/business-discovery"?"business-discovery":t==="/events"?"8":t==="/ai-event-advisor"?"9":t==="/pk-matcher"?"10":t==="/akademi/dashboard"?"11":t==="/akademi/duyurular"?"12":t==="/akademi/egitimler"?"13":t==="/akademi/etkinlikler"?"13-1":t==="/akademi/destek"?"14":t==="/akademi/kullanicilar"?"15":t==="/akademi/yapay-zeka"?"16":t==="/site/anasayfa"?"21":t==="/site/yayincilar"?"22":t==="/site/basvurular"?"23":t==="/site/iletisim-talepleri"?"24":t==="/site/geriarama-talepleri"?"25":t==="/site/toplanti-talepleri"?"26":t==="/site/toplu-sms"?"27":t==="/site/blog"?"28":t==="/etsy/dashboard"?"31":t==="/etsy/tasarim-onaylari"?"32":t==="/etsy/urunler"?"33":t==="/etsy/ayarlar"?"34":t==="/whatsapp"?"whatsapp":t==="/users"?"99":"1",Lx=({collapsed:t,onCollapse:s,visible:r=!0,mobileView:n=!1,onMobileClose:d})=>{var c,h,m,y,p,j,x;const b=_a(),C=$o(),{user:_}=Ct(),w=!!_,[g,H]=u.useState(!1),[U,T]=u.useState([]),W=u.useRef(null),G=u.useRef(null),{publisherDiscoveryStatus:J}=Fi(),{totalUnreadCounts:B}=vr(),M=A=>{T(A)};if(C.pathname==="/login"||!w)return null;const X=Px(b,_,((c=B==null?void 0:B.whatsapp)==null?void 0:c.mesaj)||0,((h=B==null?void 0:B.etsy)==null?void 0:h.tasarim)||0,J,((m=B==null?void 0:B.site)==null?void 0:m.basvuru)||0,((y=B==null?void 0:B.site)==null?void 0:y.iletisim)||0,((p=B==null?void 0:B.site)==null?void 0:p.geriarama)||0,((j=B==null?void 0:B.site)==null?void 0:j.toplanti)||0,((x=B==null?void 0:B.akademi)==null?void 0:x.destek)||0,jo(C.pathname),U),oe=A=>{A&&A(),n&&d&&d()},K=n?X.map(A=>A!=null&&A.children?{...A,children:A.children.map(F=>F!=null&&F.onClick?{...F,onClick:()=>oe(F.onClick)}:F)}:A!=null&&A.onClick?{...A,onClick:()=>oe(A.onClick)}:A):X,ye=()=>{t&&!n&&(W.current&&clearTimeout(W.current),W.current=setTimeout(()=>{H(!0)},50))},_e=()=>{W.current&&(clearTimeout(W.current),W.current=null),H(!1)},Re=n?r?!1:t:t&&!g;return e.jsxs(Dx,{ref:G,trigger:null,collapsible:!0,collapsed:Re,width:240,collapsedWidth:n?0:80,className:`min-h-screen fixed left-0 shadow-lg flex flex-col hardware-accelerated ${n&&!r?"mobile-hidden":""}`,style:{height:"100vh",position:"fixed",left:0,zIndex:n?1001:999,background:"#001529",transition:"all 0.2s cubic-bezier(0.25, 1, 0.5, 1)",transform:n?r?"translateX(0) translateZ(0)":"translateX(-100%) translateZ(0)":"translateX(0) translateZ(0)",backfaceVisibility:"hidden",WebkitFontSmoothing:"subpixel-antialiased"},onMouseEnter:ye,onMouseLeave:_e,children:[n&&r&&e.jsx("div",{className:"mobile-overlay",style:{position:"fixed",top:0,left:0,right:0,bottom:0,background:"rgba(0,0,0,0.5)",zIndex:-1}}),e.jsxs("div",{className:"flex justify-center items-center h-16 flex-shrink-0 hardware-accelerated",style:{padding:"16px 0",marginBottom:"8px",transition:"all 0.2s cubic-bezier(0.25, 1, 0.5, 1)"},children:[e.jsx("span",{className:"text-white font-bold text-xl whitespace-nowrap hardware-accelerated",style:{transition:"all 0.2s cubic-bezier(0.25, 1, 0.5, 1)",opacity:Re?0:1,transform:Re?"scale(0.8)":"scale(1)",display:Re?"none":"block"},children:kf.NAME}),e.jsx("span",{className:"text-white font-bold text-xl hardware-accelerated",style:{transition:"all 0.2s cubic-bezier(0.25, 1, 0.5, 1)",opacity:Re?1:0,transform:Re?"scale(1)":"scale(0.8)",display:Re?"block":"none"},children:"TA"})]}),e.jsx("div",{style:{flexGrow:1,overflowY:"auto",overflowX:"hidden",height:"calc(100vh - 80px)",scrollbarWidth:"thin",scrollbarColor:"#3e4c5a #001529"},className:"custom-scrollbar",children:e.jsx($f,{theme:"dark",mode:"inline",selectedKeys:[jo(C.pathname)],openKeys:U,onOpenChange:M,items:K,style:{background:"transparent",borderRight:0}})})]})},Yc=({mobileView:t=!1})=>{const{darkMode:s,toggleDarkMode:r}=fr();return e.jsx("button",{className:`theme-toggle-icon-btn${t?" mobile":""}`,onClick:r,"aria-label":"Tema Değiştir",type:"button",children:s?e.jsx(sx,{style:{fontSize:18}}):e.jsx(jg,{style:{fontSize:18}})})},{Header:Nx}=fa,Yx=({collapsed:t,toggleCollapsed:s,mobileView:r=!1})=>{const{user:n,logout:d}=Ct(),c=_a(),[h,m]=u.useState(!1),{notifications:y,unreadCount:p,markAsRead:j,markAllAsRead:x,getNotificationRoute:b,startPolling:C,stopPolling:_}=vr(),w=async()=>{m(!0);try{await d(),setTimeout(()=>{c("/login")},500)}catch(J){}finally{m(!1)}},g=J=>{j(J);const B=y.find(M=>M.id===J);if(B){const M=b(B);c(M)}},H=u.useMemo(()=>{try{if(!y||y.length===0)return[{key:"header",label:e.jsx("div",{className:"notification-header",children:"Bildirimler"}),disabled:!0},{key:"empty",label:e.jsx("div",{className:"notification-empty",children:e.jsx(or,{image:or.PRESENTED_IMAGE_SIMPLE,description:e.jsx("span",{children:"BİLDİRİM YOK"})})}),disabled:!0}];const B=[...y].sort((M,X)=>new Date(X.time).getTime()-new Date(M.time).getTime()).map(M=>{const X=typeof M.source=="string"&&M.source?M.source.split(".")[0]:"site",oe=M.time?U(M.time):"";return{key:M.id,label:e.jsx("span",{children:e.jsxs("div",{className:`notification-item ${M.read?"":"notification-unread"}`,onClick:()=>g(M.id),children:[e.jsx("div",{className:`notification-border notification-${X}`}),e.jsxs("div",{className:"notification-content",children:[e.jsx("div",{className:"notification-title",children:M.title||W(M.source)}),e.jsx("div",{className:"notification-message",children:M.message||"Yeni bildiriminiz var."}),e.jsx("div",{className:"notification-time",children:oe})]})]})})}});return[{key:"header",label:e.jsxs("div",{className:"notification-header",children:[e.jsxs("div",{children:["Bildirimler (",p,")"]}),p>0&&e.jsx(I,{type:"link",size:"small",onClick:x,children:"Tümünü Okundu İşaretle"})]}),disabled:!0},...B,{key:"footer",label:e.jsx("div",{className:"view-all-notifications",children:e.jsx("a",{onClick:()=>c("/notifications"),children:"Tüm Bildirimleri Görüntüle"})}),disabled:!0}]}catch(J){return[{key:"header",label:e.jsx("div",{className:"notification-header",children:"Bildirimler"}),disabled:!0},{key:"error",label:e.jsx("div",{style:{color:"red"},children:JSON.stringify(y)}),disabled:!0}]}},[y,p,x,c]),U=J=>{try{const B=new Date(J);if(isNaN(B.getTime()))return J;const M=new Date,X=new Date(M);X.setHours(0,0,0,0);const oe=new Date(X);return oe.setDate(oe.getDate()-1),B>=X?`Bugün ${T(B.getHours())}:${T(B.getMinutes())}`:B>=oe?`Dün ${T(B.getHours())}:${T(B.getMinutes())}`:`${T(B.getDate())}.${T(B.getMonth()+1)} ${T(B.getHours())}:${T(B.getMinutes())}`}catch(B){return J}},T=J=>J<10?`0${J}`:`${J}`,W=J=>{switch(J){case"akademi.destek":return"Destek Talebi";case"site.basvuru":return"Yeni Başvuru";case"site.iletisim":return"İletişim Talebi";case"site.geriarama":return"Geri Arama Talebi";case"site.toplanti":return"Toplantı Talebi";case"etsy.tasarim":return"Tasarım Onayı";case"whatsapp.mesaj":return"WhatsApp Mesajı";default:return"Bildirim"}},G=[{key:"profile",icon:e.jsx(ot,{}),label:"Profilim",onClick:()=>c("/profile")},{key:"settings",icon:e.jsx(_s,{}),label:"Ayarlar",onClick:()=>c("/settings")},{key:"divider",type:"divider"},{key:"logout",icon:e.jsx(cg,{}),label:h?"Çıkış Yapılıyor...":"Çıkış Yap",disabled:h,onClick:w}];return e.jsxs(Nx,{className:`top-bar ${r?"mobile-top-bar":""}`,style:{padding:"0 24px"},children:[e.jsx("div",{className:"left-section",children:e.jsx(I,{type:"text",icon:r?t?e.jsx(xo,{}):e.jsx(go,{}):t?e.jsx(xo,{}):e.jsx(go,{}),onClick:s,className:"sidebar-toggle",style:{fontSize:r?"18px":"16px"}})}),e.jsxs("div",{className:"right-section",children:[e.jsx(Yc,{mobileView:r}),e.jsx(dn,{menu:{items:H},placement:"bottomRight",trigger:["click"],overlayClassName:"notification-dropdown",destroyPopupOnHide:!0,onOpenChange:J=>{J?C():_()},children:e.jsx(I,{type:"text",className:"notification-button",onClick:()=>{},children:e.jsx(Ge,{count:p,offset:[0,0],children:e.jsx(hr,{})})})}),e.jsx(dn,{menu:{items:G},placement:"bottomRight",trigger:["click"],children:e.jsxs(ce,{className:"user-menu",children:[e.jsx($a,{icon:e.jsx(ot,{})}),e.jsx("span",{className:`username ${r?"mobile-hidden":"desktop-only"}`,children:(n==null?void 0:n.name)||"Kullanıcı"})]})})]})]})},{Content:$x}=fa,Bx=()=>{const[t,s]=u.useState({width:window.innerWidth,height:window.innerHeight});return u.useEffect(()=>{const r=()=>{s({width:window.innerWidth,height:window.innerHeight})};return window.addEventListener("resize",r),()=>window.removeEventListener("resize",r)},[]),t};function Fx(){const[t,s]=u.useState(!1),[r,n]=u.useState(!1),[d,c]=u.useState(!1),{user:h,loading:m}=Ct(),{darkMode:y}=fr(),p=$o(),j=_a(),{width:x}=Bx();if(u.useEffect(()=>{x<=768?(n(!0),s(!0)):(n(!1),c(!1))},[x]),u.useEffect(()=>(r&&d?document.body.classList.add("mobile-sidebar-visible"):document.body.classList.remove("mobile-sidebar-visible"),()=>{document.body.classList.remove("mobile-sidebar-visible")}),[r,d]),u.useEffect(()=>{!m&&!h&&p.pathname!=="/login"&&j("/login")},[h,m,p.pathname,j]),p.pathname==="/login")return e.jsx(ws,{});if(m)return e.jsx("div",{className:"flex justify-center items-center h-screen bg-gray-100 dark:bg-gray-900",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"})});if(!h)return e.jsx("div",{className:"flex justify-center items-center h-screen bg-gray-100 dark:bg-gray-900",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"})});const b=()=>{r?c(!d):s(!t)},C=()=>{r&&d&&c(!1)};return e.jsxs(fa,{className:"min-h-screen",children:[r&&d&&e.jsx("div",{className:"mobile-overlay",onClick:C}),e.jsx(Lx,{collapsed:r?!0:t,visible:r?d:!0,onCollapse:s,mobileView:r,onMobileClose:()=>c(!1)}),e.jsxs(fa,{style:{marginLeft:r?0:t?80:240,transition:"all 0.3s"},children:[e.jsx(Yx,{collapsed:t,toggleCollapsed:b,mobileView:r}),e.jsx($x,{style:{margin:"0",padding:r?"0 16px":"0 24px",minHeight:280,borderRadius:"0",background:y?"#121212":"#fff",transition:"all 0.3s",position:"relative",top:0,overflow:"auto"},children:e.jsx(ws,{})})]})]})}const{Title:vo,Text:ko}=ht,Hx=()=>{const[t,s]=u.useState(!1),{darkMode:r}=fr(),{login:n}=Ct(),d=_a();u.useEffect(()=>{const h=localStorage.getItem(Ze.USER_KEY);if(h)try{JSON.parse(h),d("/")}catch(m){localStorage.removeItem(Ze.USER_KEY),localStorage.removeItem(Ze.TOKEN_KEY)}},[]);const c=async h=>{s(!0);try{const m=h.email.trim();await wf.bypassAuthCache();const y=await n(m,h.password);y&&y.user?(st.success("Giriş başarılı!"),d("/")):y&&y.error?st.error(`Giriş hatası: ${y.error}`):st.error("Giriş sırasında bilinmeyen bir hata oluştu.")}catch(m){st.error("Giriş sırasında beklenmedik bir hata oluştu.")}finally{s(!1)}};return e.jsxs("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 px-4",children:[e.jsx("div",{style:{position:"absolute",top:"20px",right:"20px"},children:e.jsx(Yc,{})}),e.jsxs(q,{className:"w-full max-w-md shadow-lg border-gray-200 dark:border-gray-700",style:{background:r?"#1f1f1f":"#fff",borderRadius:"12px"},children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsx("div",{className:"w-20 h-20 mx-auto mb-2 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 flex items-center justify-center",children:e.jsx(vo,{level:2,style:{color:"white",margin:0},children:"TA"})}),e.jsx(vo,{level:3,className:"bg-gradient-to-r from-indigo-500 to-purple-600 bg-clip-text text-transparent",children:"Tuber Ajans Panel"}),e.jsx(ko,{type:"secondary",children:"Lütfen giriş yapın"})]}),e.jsxs(O,{name:"login",initialValues:{remember:!0},onFinish:c,size:"large",layout:"vertical",children:[e.jsx(O.Item,{name:"email",rules:[{required:!0,message:"Lütfen e-posta adresinizi girin!"}],children:e.jsx(me,{prefix:e.jsx(ot,{className:"site-form-item-icon"}),placeholder:"E-posta adresi",className:"rounded-lg"})}),e.jsx(O.Item,{name:"password",rules:[{required:!0,message:"Lütfen şifrenizi girin!"}],children:e.jsx(me.Password,{prefix:e.jsx(Pc,{className:"site-form-item-icon"}),placeholder:"Şifre",className:"rounded-lg"})}),e.jsx(O.Item,{children:e.jsx(I,{type:"primary",htmlType:"submit",loading:t,className:"w-full h-12 rounded-lg",style:{background:"linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)",border:"none"},children:t?"Giriş yapılıyor...":"Giriş Yap"})})]}),e.jsx(Ua,{plain:!0,children:e.jsx(ko,{type:"secondary",children:"Tuber Ajans © 2024"})})]})]})},bs=[{key:"dashboard",label:"Dashboard",icon:Va,hasManagePermission:!0,description:"Ana gösterge paneli"},{key:"publishers",label:"Yayıncılar",icon:ks,hasManagePermission:!0,description:"Yayıncılar yönetimi"},{key:"influencers",label:"Influencerlar",icon:ot,hasManagePermission:!0,description:"Influencer yönetimi"},{key:"tasks",label:"Görevler",icon:Xt,hasManagePermission:!0,description:"Görev yönetimi"},{key:"reports",label:"Raporlar",icon:Va,hasManagePermission:!0,description:"Rapor yönetimi"},{key:"ai_advisor",label:"AI Danışmanı",icon:Ac,hasManagePermission:!0,description:"Yapay zeka danışman sistemi"},{key:"yayinci_kesfi",label:"Yayıncı Keşfi",icon:ta,hasManagePermission:!0,description:"Yayıncı keşif sistemi"},{key:"events",label:"Etkinlik Yönetimi",icon:It,hasManagePermission:!0,description:"Etkinlik yönetimi ana modülü",children:[{key:"events.view",label:"Etkinlikler",description:"Etkinlik görüntüleme",hasManagePermission:!0},{key:"events.ai_advisor",label:"AI Etkinlik Danışmanı",description:"Yapay zeka etkinlik danışmanı",hasManagePermission:!0},{key:"events.pk_matcher",label:"PK Eşleştirici",description:"PK eşleştirme aracı",hasManagePermission:!0}]},{key:"akademi",label:"Akademi",icon:wr,hasManagePermission:!0,description:"Akademi yönetim sistemi",children:[{key:"akademi.dashboard",label:"Dashboard",description:"Akademi gösterge paneli",hasManagePermission:!0},{key:"akademi.duyurular",label:"Duyurular",description:"Akademi duyuruları",hasManagePermission:!0},{key:"akademi.egitimler",label:"Eğitimler",description:"Akademi eğitimleri",hasManagePermission:!0},{key:"akademi.destek",label:"Destek Talepleri",description:"Akademi destek talepleri",hasManagePermission:!0},{key:"akademi.kullanicilar",label:"Kullanıcılar",description:"Akademi kullanıcıları",hasManagePermission:!0},{key:"akademi.ai",label:"Yapay Zeka",description:"Akademi yapay zeka",hasManagePermission:!0}]},{key:"site_yonetimi",label:"Site Yönetimi",icon:Ki,hasManagePermission:!0,description:"Site yönetim sistemi",children:[{key:"site.anasayfa",label:"Ana Sayfa",description:"Site ana sayfa yönetimi",hasManagePermission:!0},{key:"site.yayincilar",label:"Yayıncılarımız",description:"Site yayıncılar yönetimi",hasManagePermission:!0},{key:"site.basvurular",label:"Başvurular",description:"Site başvurular yönetimi",hasManagePermission:!0},{key:"site.iletisim",label:"İletişim Talepleri",description:"Site iletişim talepleri",hasManagePermission:!0},{key:"site.geriarama",label:"Geri Arama",description:"Site geri arama talepleri",hasManagePermission:!0},{key:"site.toplanti",label:"Online Toplantı",description:"Site online toplantı talepleri",hasManagePermission:!0},{key:"site.sms",label:"Toplu SMS",description:"Site toplu SMS gönderimi",hasManagePermission:!0},{key:"site.blog",label:"Blog Yönetimi",description:"Site blog yönetimi",hasManagePermission:!0}]},{key:"etsy_operasyonu",label:"ETSY Operasyonu",icon:ji,hasManagePermission:!0,description:"ETSY operasyon yönetimi",children:[{key:"etsy.dashboard",label:"Dashboard",description:"ETSY gösterge paneli",hasManagePermission:!0},{key:"etsy.tasarim",label:"Tasarım Onayları",description:"ETSY tasarım onayları",hasManagePermission:!0},{key:"etsy.urunler",label:"Ürünler",description:"ETSY ürünleri",hasManagePermission:!0},{key:"etsy.ayarlar",label:"Ayarlar",description:"ETSY ayarları",hasManagePermission:!0}]},{key:"whatsapp",label:"WhatsApp",icon:Pi,hasManagePermission:!0,description:"WhatsApp mesajlaşma sistemi"},{key:"users",label:"Kullanıcı Yönetimi",icon:ot,description:"Kullanıcı yönetimi",children:[{key:"users.view",label:"Görüntüleme",description:"Kullanıcıları görüntüleme",hasManagePermission:!1},{key:"users.manage",label:"Yönetim",description:"Kullanıcı yönetimi",hasManagePermission:!1}]},{key:"performance",label:"Performans",icon:Va,hasManagePermission:!0,description:"Performans yönetimi"},{key:"tournament",label:"Turnuva",icon:It,hasManagePermission:!0,description:"Turnuva yönetimi"},{key:"whatsapp_yonetimi",label:"WhatsApp Yönetimi",icon:Pi,hasManagePermission:!0,description:"WhatsApp işletme yönetimi"},{key:"kullanici_yonetimi",label:"Kullanıcı Yönetimi",icon:ot,hasManagePermission:!0,description:"Uygulama kullanıcıları yönetimi"},{key:"business_discovery",label:"İşletme Keşfi",icon:ta,hasManagePermission:!0,description:"İşletme keşfi modülü",children:[{key:"business_discovery.fetch",label:"Veri Toplama",description:"İşletme verisi toplama sekmesi",hasManagePermission:!0},{key:"business_discovery.list",label:"Kayıtlar",description:"İşletme kayıtları sekmesi",hasManagePermission:!0},{key:"business_discovery.stats",label:"İstatistikler",description:"İşletme istatistikleri sekmesi",hasManagePermission:!0},{key:"business_discovery.settings",label:"Ayarlar",description:"İşletme keşfi ayarları sekmesi",hasManagePermission:!0}]}],$c=()=>{const t={};return bs.forEach(s=>{s.children?(t[s.key]={},s.children.forEach(r=>{const[n,d]=r.key.split(".");if(t[n]||(t[n]={}),t[n][d]=!0,r.hasManagePermission){const c=`${d}_manage`;t[n][c]=!0}})):(t[s.key]=!0,s.hasManagePermission&&(t[`${s.key}_manage`]=!0))}),t},{Option:Xr}=Ce,{Panel:Wx}=Fo,{TabPane:wo}=Xe,Ux=[{id:1,name:"Admin Kullanıcı",email:"<EMAIL>",role:"admin",created_at:"2023-01-01T10:00:00",permissions:$c()},{id:2,name:"Editör Kullanıcı",email:"<EMAIL>",role:"editor",created_at:"2023-01-05T11:30:00",permissions:{dashboard:!0,publishers:!0,influencers:!0,tasks:!0,reports:!0,ai_advisor:!1,events:{view:!0,manage:!0,ai_advisor:!1,pk_matcher:!1},users:{view:!0,manage:!1},performance:!0,tournament:!1,akademi:{view:!0,manage:!1},site_yonetimi:{view:!0,manage:!1},etsy_operasyonu:{view:!0,manage:!1},whatsapp:{view:!0,manage:!1},yayinci_kesfi:{view:!0,manage:!1}}},{id:3,name:"İzleyici Kullanıcı",email:"<EMAIL>",role:"viewer",created_at:"2023-01-10T14:15:00",permissions:{dashboard:!0,publishers:!0,influencers:!1,tasks:!1,reports:!0,ai_advisor:!1,events:{view:!0,manage:!1,ai_advisor:!1,pk_matcher:!1},users:{view:!1,manage:!1},performance:!1,tournament:!1,akademi:{view:!1,manage:!1},site_yonetimi:{view:!1,manage:!1},etsy_operasyonu:{view:!1,manage:!1},whatsapp:{view:!1,manage:!1},yayinci_kesfi:{view:!1,manage:!1}}}],Vx=[{id:1,userId:1,userName:"Admin Kullanıcı",ipAddress:"***********",status:"success",timestamp:"2023-05-15T08:30:45",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",details:"Başarılı giriş"},{id:2,userId:2,userName:"Editör Kullanıcı",ipAddress:"***********",status:"success",timestamp:"2023-05-15T09:15:22",userAgent:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)",details:"Başarılı giriş"},{id:3,userId:3,userName:"İzleyici Kullanıcı",ipAddress:"***********",status:"fail",timestamp:"2023-05-15T10:05:18",userAgent:"Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X)",details:"Hatalı şifre"},{id:4,userId:1,userName:"Admin Kullanıcı",ipAddress:"***********",status:"success",timestamp:"2023-05-16T08:45:12",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",details:"Başarılı giriş"},{id:5,userId:0,userName:"Bilinmeyen Kullanıcı",ipAddress:"***********",status:"fail",timestamp:"2023-05-16T11:22:36",userAgent:"Mozilla/5.0 (Linux; Android 10)",details:"Kullanıcı bulunamadı"}];function Qr(t){const s={};for(const r of t)if(r.children){s[r.key]={};for(const n of r.children){const[d,c]=n.key.split(".");s[r.key][c]=!1,n.hasManagePermission&&(s[r.key][`${c}_manage`]=!1)}}else s[r.key]=!1,r.hasManagePermission&&(s[`${r.key}_manage`]=!1);return s}function nr(t,s){const r={...t};for(const n in s)typeof s[n]=="object"&&!Array.isArray(s[n])?r[n]=nr((t==null?void 0:t[n])||{},s[n]):n in r||(r[n]=!1);return r}const Kx=()=>{const{user:t}=Ct(),[s,r]=u.useState([]),[n,d]=u.useState(!0),[c,h]=u.useState(!1),[m,y]=u.useState(null),[p]=O.useForm(),[j,x]=u.useState("kullanicilar"),[b,C]=u.useState([]),[_,w]=u.useState(!1),[g,H]=u.useState(10),[U,T]=u.useState(10),[W,G]=u.useState(""),J=!1,B=async(A=!1)=>{d(!0);try{const ie=(await fe.get(`${Be.X_SITE_BASE_URL}${Be.ENDPOINTS.USERS}`)).data;if(ie.success===!0&&Array.isArray(ie.users))r(ie.users),A&&S.success(`${ie.users.length} kullanıcı yüklendi`);else throw new Error(ie.error||"Beklenmeyen veri formatı")}catch(F){S.error(`Hata: ${F.message||"Bilinmeyen hata"}`),r(Ux),S.warning("Test verileri gösteriliyor (gerçek veriler yüklenemedi)",3)}finally{d(!1)}},M=async(A=!1)=>{w(!0);try{const ie=(await fe.get(`${Be.X_SITE_BASE_URL}/login-logs.php`)).data;if(ie.success===!0&&Array.isArray(ie.logs))C(ie.logs),A&&S.success(`${ie.logs.length} giriş logu yüklendi`);else throw new Error(ie.error||"Beklenmeyen veri formatı")}catch(F){S.error(`Hata: ${F.message||"Bilinmeyen hata"}`),C(Vx),S.warning("Test verileri gösteriliyor (gerçek veriler yüklenemedi)",3)}finally{w(!1)}};u.useEffect(()=>{t&&t.token&&(B(!1),M(!1))},[t]);const X=A=>{x(A),A==="girisLoglari"&&b.length===0&&M(!0)},oe=A=>{y(A),h(!0);const F=Qr(bs);let ie=nr(A.permissions||{},F);p.setFieldsValue({name:A.name,email:A.email,role:A.role,permissions:ie})},K=async A=>{try{const ie=(await fe.delete(`${Be.X_SITE_BASE_URL}${Be.ENDPOINTS.USERS}?id=${A}`)).data;if(ie.success)S.success("Kullanıcı başarıyla silindi"),r(s.filter(je=>je.id!==A));else throw new Error(ie.error||"Kullanıcı silinemedi")}catch(F){S.error(`Silme işlemi başarısız: ${F.message||"Bilinmeyen hata"}`)}},ye=async A=>{try{if(m){const F=Qr(bs);let ie=nr(A.permissions||{},F),je={...A,permissions:ie};je.role!=="admin"&&je.permissions||je.role==="admin"&&(je.permissions=$c());const Y=(await fe.put(`${Be.X_SITE_BASE_URL}${Be.ENDPOINTS.USERS}?id=${m.id}`,je)).data;if(Y.success)S.success("Kullanıcı başarıyla güncellendi"),B(),h(!1);else throw new Error(Y.error||"Kullanıcı güncellenemedi")}else{const F=Qr(bs);let ie=nr(A.permissions||{},F),je={...A,permissions:ie};const Y=(await fe.post(`${Be.X_SITE_BASE_URL}${Be.ENDPOINTS.USERS}`,je)).data;if(Y.success)S.success("Kullanıcı başarıyla eklendi"),r([...s,Y.user]),h(!1);else throw new Error(Y.error||"Kullanıcı eklenemedi")}}catch(F){S.error(`İşlem başarısız: ${F.message||"Bilinmeyen hata"}`)}},_e=[{title:"İSİM",dataIndex:"name",key:"name",width:150},{title:"E-POSTA",dataIndex:"email",key:"email",width:200},{title:"YETKİ",dataIndex:"role",key:"role",width:120,render:A=>{let F="bg-blue-100 text-blue-800",ie="Admin";return A==="editor"?(F="bg-green-100 text-green-800",ie="Yönetici"):A==="viewer"&&(F="bg-purple-100 text-purple-800",ie="İzleyici"),e.jsx("span",{className:`px-2 py-1 rounded ${F}`,children:ie})}},{title:"KAYIT TARİHİ",dataIndex:"created_at",key:"created_at",width:150,render:A=>A?new Date(A).toLocaleDateString("tr-TR"):"-"},{title:"İŞLEMLER",key:"actions",width:100,render:(A,F)=>e.jsxs(ce,{size:"middle",children:[e.jsx(I,{type:"text",icon:e.jsx(yt,{style:{color:"#1890ff"}}),onClick:()=>oe(F),className:"hover:bg-blue-50"}),e.jsx(it,{title:"Bu kullanıcıyı silmek istediğinize emin misiniz?",onConfirm:()=>K(F.id),okText:"Evet",cancelText:"Hayır",children:e.jsx(I,{type:"text",icon:e.jsx(Et,{style:{color:"#ff4d4f"}}),className:"hover:bg-red-50"})})]})}],Re=[{title:"KULLANICI",dataIndex:"userName",key:"userName",width:150},{title:"IP ADRESİ",dataIndex:"ipAddress",key:"ipAddress",width:150},{title:"DURUM",dataIndex:"status",key:"status",width:120,render:A=>{const F=A==="success";return e.jsx(ae,{color:F?"success":"error",children:F?"Başarılı":"Başarısız"})}},{title:"GİRİŞ ZAMANI",dataIndex:"timestamp",key:"timestamp",width:180,render:A=>A?new Date(A).toLocaleString("tr-TR"):"-"},{title:"DETAY",dataIndex:"details",key:"details",width:180}];return e.jsxs("div",{className:"min-h-screen w-full bg-white p-0",children:[e.jsx("div",{className:"w-full px-2 sm:px-4 md:px-6 lg:px-8",children:e.jsxs(Xe,{activeKey:j,onChange:X,tabBarExtraContent:e.jsx(I,{type:"text",shape:"circle",size:"small",icon:e.jsx(wt,{style:{fontSize:16}}),onClick:()=>{h(!0),y(null),p.resetFields()},style:{marginLeft:4,boxShadow:"none",border:"none",background:"none"},"aria-label":"Yeni Kullanıcı Ekle",title:"Yeni Kullanıcı Ekle"}),className:"w-full px-0",children:[e.jsx(wo,{tab:e.jsxs("span",{children:[e.jsx(ot,{})," Kullanıcılar"]}),children:e.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-x-auto",children:e.jsx(qe,{columns:_e,dataSource:s,rowKey:"id",loading:n,pagination:{pageSize:g,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:(A,F)=>H(F),onChange:(A,F)=>H(F||10)},locale:{emptyText:"Henüz kullanıcı bulunmuyor"},scroll:{x:!0},className:"w-full custom-zebra-table",bordered:!0})})},"kullanicilar"),e.jsxs(wo,{tab:e.jsxs("span",{children:[e.jsx(Jn,{})," Giriş Logları"]}),children:[e.jsx("div",{className:"mb-4 flex justify-between",children:e.jsx(me,{placeholder:"Log ara...",prefix:e.jsx(ot,{className:"site-form-item-icon"}),className:"max-w-md",value:W,onChange:A=>G(A.target.value)})}),e.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-x-auto",children:e.jsx(qe,{columns:Re,dataSource:b.filter(A=>{var F,ie,je,De;return((F=A.userName)==null?void 0:F.toLowerCase().includes(W.toLowerCase()))||((ie=A.ipAddress)==null?void 0:ie.toLowerCase().includes(W.toLowerCase()))||((je=A.status)==null?void 0:je.toLowerCase().includes(W.toLowerCase()))||((De=A.timestamp)==null?void 0:De.toLowerCase().includes(W.toLowerCase()))||(A.details||"").toLowerCase().includes(W.toLowerCase())}),rowKey:"id",loading:_,pagination:{pageSize:U,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:(A,F)=>T(F),onChange:(A,F)=>T(F||10)},locale:{emptyText:"Henüz giriş logu bulunmuyor"},scroll:{x:!0},className:"w-full custom-zebra-table",bordered:!0})})]},"girisLoglari")]})}),e.jsx(St,{title:m?"Kullanıcı Düzenle":"Yeni Kullanıcı Ekle",open:c,onCancel:()=>h(!1),footer:null,width:700,centered:!0,bodyStyle:{maxHeight:"calc(100vh - 200px)",overflowY:"auto",paddingRight:16},style:{top:20},children:e.jsxs(O,{form:p,layout:"vertical",onFinish:ye,initialValues:{permissions:{dashboard:!0,events:{view:!0}}},children:[e.jsx(O.Item,{name:"name",label:"Ad Soyad",rules:[{required:!0,message:"Lütfen ad soyad girin!"}],children:e.jsx(me,{prefix:e.jsx(ot,{}),placeholder:"Ad Soyad"})}),e.jsx(O.Item,{name:"email",label:"E-posta",rules:[{required:!0,message:"Lütfen e-posta girin!"},{type:"email",message:"Geçerli bir e-posta adresi girin!"}],children:e.jsx(me,{prefix:e.jsx(Sr,{}),placeholder:"E-posta"})}),m&&e.jsx("div",{className:"mb-4 p-2 bg-blue-50 text-blue-700 rounded text-sm",children:e.jsx("p",{children:"Şifreyi değiştirmek istiyorsanız yeni şifreyi girin. Değiştirmek istemiyorsanız bu alanı boş bırakın."})}),e.jsx(O.Item,{name:"password",label:m?"Şifre (Değiştirmek istemiyorsanız boş bırakın)":"Şifre",rules:[{required:!m,message:"Lütfen şifre girin!"}],children:e.jsx(me.Password,{prefix:e.jsx(Pc,{}),placeholder:m?"Yeni şifre":"Şifre"})}),e.jsx(O.Item,{name:"role",label:"Yetki",rules:[{required:!0,message:"Lütfen yetki seçin!"}],children:e.jsxs(Ce,{placeholder:"Yetki seçin",children:[e.jsx(Xr,{value:"admin",children:"Admin"}),e.jsx(Xr,{value:"editor",children:"Yönetici"}),e.jsx(Xr,{value:"viewer",children:"İzleyici"})]})}),e.jsx(O.Item,{noStyle:!0,shouldUpdate:(A,F)=>A.role!==F.role,children:({getFieldValue:A})=>A("role")!=="admin"?e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"mb-2 font-medium",children:"Erişim İzinleri"}),e.jsxs("div",{className:"p-2 bg-gray-50 rounded border border-gray-200",children:[e.jsx("p",{className:"text-xs text-gray-500 mb-2",children:"Not: Admin kullanıcılar otomatik olarak tüm yetkilere sahiptir, bu ayarlar sadece diğer kullanıcı tipleri için geçerlidir."}),e.jsx(Fo,{bordered:!1,defaultActiveKey:["1"],className:"bg-transparent permission-collapse",expandIconPosition:"end",children:e.jsx(Wx,{header:"Sayfa Erişim İzinleri",className:"permission-panel",children:e.jsx("div",{className:"permission-table",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"bg-gray-100",children:[e.jsx("th",{className:"py-2 px-3 text-left",children:"Sayfa"}),e.jsx("th",{className:"py-2 px-3 text-center",children:"Görüntüleme"}),e.jsx("th",{className:"py-2 px-3 text-center",children:"Yönetim"})]})}),e.jsx("tbody",{children:bs.map(F=>e.jsxs(cn.Fragment,{children:[!F.children&&e.jsxs("tr",{className:"border-b border-gray-100",children:[e.jsx("td",{className:"py-2 px-3",children:F.label}),e.jsx("td",{className:"py-2 px-3 text-center",children:e.jsx(O.Item,{name:["permissions",F.key],valuePropName:"checked",className:"mb-0",noStyle:!0,children:e.jsx(Ba,{size:"small",className:"custom-switch"})})}),e.jsx("td",{className:"py-2 px-3 text-center",children:F.hasManagePermission&&e.jsx(O.Item,{name:["permissions",`${F.key}_manage`],valuePropName:"checked",className:"mb-0",noStyle:!0,children:e.jsx(Ba,{size:"small",className:"custom-switch"})})})]},F.key),F.children&&e.jsx("tr",{className:"border-b border-gray-100 bg-gray-50",children:e.jsx("td",{className:"py-2 px-3 font-medium",colSpan:3,children:F.label})},`${F.key}-header`),F.children&&F.children.map(ie=>{const[je,De]=ie.key.split(".");return e.jsxs("tr",{className:"border-b border-gray-100",children:[e.jsxs("td",{className:"py-2 px-3 pl-8",children:["• ",ie.label]}),e.jsx("td",{className:"py-2 px-3 text-center",children:e.jsx(O.Item,{name:["permissions",F.key,De],valuePropName:"checked",className:"mb-0",noStyle:!0,children:e.jsx(Ba,{size:"small",className:"custom-switch"})})}),e.jsx("td",{className:"py-2 px-3 text-center",children:ie.hasManagePermission&&e.jsx(O.Item,{name:["permissions",F.key,`${De}_manage`],valuePropName:"checked",className:"mb-0",noStyle:!0,children:e.jsx(Ba,{size:"small",className:"custom-switch"})})})]},ie.key)})]},F.key))})]})})},"1")})]})]}):null}),e.jsxs(O.Item,{className:"mb-0 text-right",children:[e.jsx(I,{onClick:()=>h(!1),style:{marginRight:8},children:"İptal"}),e.jsx(I,{type:"primary",htmlType:"submit",className:"bg-indigo-600",children:m?"Güncelle":"Ekle"})]})]})}),J,e.jsx("style",{children:`
        .custom-zebra-table .ant-table-tbody > tr:nth-child(odd) > td {
          background: #fafbfc;
        }
        .custom-zebra-table .ant-table-tbody > tr > td {
          border-bottom: 1px solid #f0f0f0;
        }
        .custom-zebra-table .ant-table-thead > tr > th {
          background: #f5f5f5;
          font-weight: 600;
        }
      `})]})},{Title:$2,Text:B2}=ht,{TextArea:Gx}=me,{Option:Ks}=Ce,{TabPane:So}=Xe,en=[{value:"general",label:"Genel",color:"blue"},{value:"event",label:"Etkinlik",color:"green"},{value:"important",label:"Önemli",color:"red"},{value:"update",label:"Güncelleme",color:"orange"},{value:"info",label:"Bilgilendirme",color:"purple"}],_o=[{value:"active",label:"Aktif",color:"green"},{value:"draft",label:"Taslak",color:"default"},{value:"expired",label:"Süresi Doldu",color:"red"}],qx=()=>{const[t,s]=u.useState([]),[r,n]=u.useState(!0),[d]=O.useForm(),[c,h]=u.useState("add"),[m,y]=u.useState(""),[p,j]=u.useState("all"),[x,b]=u.useState({total:0,views:0,users:0}),[C,_]=u.useState(!1),[w,g]=u.useState(!1),[H,U]=u.useState(""),[T,W]=u.useState(""),G=Y=>{U(Y.target.value),d.setFieldsValue({title:Y.target.value})},J=Y=>{W(Y.target.value),d.setFieldsValue({content:Y.target.value})},{user:B}=Ct(),[M,X]=u.useState(!1),oe=async()=>{n(!0);try{const pe=(await fe.get("https://x.tuberajans.com/backend/akademi/announcements.php")).data;pe.success?s(pe.data):S.error(pe.message||"Duyurular yüklenirken bir hata oluştu.")}catch(Y){S.error("Duyurular yüklenirken bir sorun oluştu.")}finally{n(!1)}},K=async()=>{try{const pe=(await fe.get("https://x.tuberajans.com/backend/akademi/announcements_stats.php")).data;pe.success&&b(pe.data)}catch(Y){}};u.useEffect(()=>{oe(),K()},[]);const ye=()=>{d.resetFields(),U(""),W("")},_e=async Y=>{X(!0);try{const pe={title:H||Y.title,content:T||Y.content,type:Y.type||"general",status:Y.status||"active",expire_date:Y.expire_date||null},be=(await fe.post("https://x.tuberajans.com/backend/akademi/announcements.php?action=add",pe)).data;be.success?(S.success("Duyuru başarıyla yayınlandı!"),oe(),K(),ye(),h("manage")):S.error(be.message||"İşlem sırasında bir hata oluştu.")}catch(pe){S.error("İşlem sırasında bir hata oluştu.")}finally{X(!1)}},Re=async Y=>{try{const Oe=(await fe.post("https://x.tuberajans.com/backend/akademi/announcements.php?action=delete",{id:Y})).data;Oe.success?(S.success("Duyuru başarıyla silindi!"),oe(),K()):S.error(Oe.message||"Duyuru silinirken bir sorun oluştu.")}catch(pe){S.error("Duyuru silinirken bir sorun oluştu.")}},A=Y=>{window.open(`https://x.tuberajans.com/announcement_detail.php?id=${Y.id}`,"_blank")},F=async()=>{_(!0);try{if(!H){S.warning("Lütfen önce başlık alanını doldurun."),_(!1);return}const Y=localStorage.getItem("ai_advisor_api_keys"),pe=localStorage.getItem("ai_advisor_model");if(!Y||!pe){S.warning("AI ayarları bulunamadı. Lütfen önce AI Danışmanı sekmesinden API anahtarlarınızı ve modelinizi ayarlayın."),_(!1);return}const Oe=JSON.parse(Y),be=pe;let Ee="";if(be.includes("gpt")||be.includes("openai")){if(Ee=Oe.openai,!Ee){S.warning("OpenAI API anahtarı bulunamadı. Lütfen AI Danışmanı ayarlarından ekleyin."),_(!1);return}}else if((be.includes("claude")||be.includes("anthropic"))&&(Ee=Oe.anthropic,!Ee)){S.warning("Anthropic API anahtarı bulunamadı. Lütfen AI Danışmanı ayarlarından ekleyin."),_(!1);return}const Z=`Bu duyuru başlığını daha profesyonel, dikkat çekici ve net bir şekilde iyileştir. Başlık: "${H}". Sadece iyileştirilmiş başlığı döndür, başka açıklama yapma.`,Q=(await fe.post("https://x.tuberajans.com/backend/x-site/ai-advisor.php?endpoint=ai",{messages:[{role:"user",content:Z}],model:be,apiKey:Ee,max_tokens:100})).data;if(Q.response){const k=Q.response.trim();U(k),d.setFieldsValue({title:k}),S.success("Başlık AI ile iyileştirildi!")}}catch(Y){S.error("AI ile iyileştirme sırasında bir hata oluştu.")}finally{_(!1)}},ie=async()=>{g(!0);try{if(!T){S.warning("Lütfen önce içerik alanını doldurun."),g(!1);return}const Y=localStorage.getItem("ai_advisor_api_keys"),pe=localStorage.getItem("ai_advisor_model");if(!Y||!pe){S.warning("AI ayarları bulunamadı. Lütfen önce AI Danışmanı sekmesinden API anahtarlarınızı ve modelinizi ayarlayın."),g(!1);return}const Oe=JSON.parse(Y),be=pe;let Ee="";if(be.includes("gpt")||be.includes("openai")){if(Ee=Oe.openai,!Ee){S.warning("OpenAI API anahtarı bulunamadı. Lütfen AI Danışmanı ayarlarından ekleyin."),g(!1);return}}else if((be.includes("claude")||be.includes("anthropic"))&&(Ee=Oe.anthropic,!Ee)){S.warning("Anthropic API anahtarı bulunamadı. Lütfen AI Danışmanı ayarlarından ekleyin."),g(!1);return}const Z=`Bu duyuru içeriğini daha açık, anlaşılır ve profesyonel bir dille yeniden yaz. İçerik: "${T}". Sadece iyileştirilmiş içeriği döndür, başka açıklama yapma.`,Q=(await fe.post("https://x.tuberajans.com/backend/x-site/ai-advisor.php?endpoint=ai",{messages:[{role:"user",content:Z}],model:be,apiKey:Ee,max_tokens:500})).data;if(Q.response){const k=Q.response.trim();W(k),d.setFieldsValue({content:k}),S.success("İçerik AI ile iyileştirildi!")}}catch(Y){S.error("AI ile iyileştirme sırasında bir hata oluştu.")}finally{g(!1)}},je=[{title:"ID",dataIndex:"id",key:"id",width:60,responsive:["md"]},{title:"Başlık",dataIndex:"title",key:"title",ellipsis:!0,render:Y=>e.jsx("div",{style:{maxWidth:250,whiteSpace:"normal",fontSize:"14px",fontWeight:500},children:Y})},{title:"Tür",dataIndex:"type",key:"type",width:100,responsive:["sm"],render:Y=>{const pe=en.find(Oe=>Oe.value===Y)||{label:Y,color:"default"};return e.jsx(ae,{color:pe.color,style:{fontSize:"11px"},children:pe.label})}},{title:"Durum",dataIndex:"status",key:"status",width:100,responsive:["lg"],render:Y=>{const pe=_o.find(Oe=>Oe.value===Y)||{label:Y,color:"default"};return e.jsx(ae,{color:pe.color,style:{fontSize:"11px"},children:pe.label})}},{title:"Tarih",dataIndex:"created_at",key:"created_at",width:120,responsive:["md"],render:Y=>e.jsx("span",{style:{fontSize:"12px",color:"#666"},children:Fe(Y).format("DD/MM/YY")})},{title:"İşlemler",key:"actions",width:100,align:"center",fixed:"right",render:(Y,pe)=>e.jsxs(ce,{size:8,style:{display:"flex",justifyContent:"center"},children:[e.jsx(_t,{title:"Önizle",children:e.jsx(I,{icon:e.jsx(Rt,{}),onClick:()=>A(pe),size:"small",type:"default",style:{borderRadius:"6px",width:"32px",height:"32px",display:"flex",alignItems:"center",justifyContent:"center",border:"1px solid #d9d9d9"}})}),e.jsx(_t,{title:"Sil",children:e.jsx(it,{title:"Bu duyuruyu silmek istediğinizden emin misiniz?",onConfirm:()=>Re(pe.id),okText:"Evet",cancelText:"Hayır",okButtonProps:{style:{backgroundColor:"#ff4d4f",borderColor:"#ff4d4f",color:"#fff"}},cancelButtonProps:{style:{backgroundColor:"#f5f5f5",borderColor:"#d9d9d9",color:"#000"}},placement:"topLeft",children:e.jsx(I,{icon:e.jsx(Et,{}),size:"small",danger:!0,style:{borderRadius:"6px",width:"32px",height:"32px",display:"flex",alignItems:"center",justifyContent:"center"}})})})]})}],De=t.filter(Y=>{const pe=Y.title.toLowerCase().includes(m.toLowerCase()),Oe=p==="all"||Y.type===p;return pe&&Oe});return e.jsx("div",{style:{padding:"24px",background:"#f5f5f5",height:"100vh",overflow:"hidden"},children:e.jsx(q,{style:{borderRadius:"12px",boxShadow:"0 4px 12px rgba(0,0,0,0.1)",height:"calc(100vh - 48px)"},children:e.jsxs(Xe,{activeKey:c,onChange:h,style:{height:"100%"},children:[e.jsx(So,{tab:"Yeni Duyuru Ekle",children:e.jsx("div",{style:{height:"calc(100vh - 160px)",overflow:"auto",padding:"0 24px 24px 24px"},children:e.jsx(q,{title:"Duyuru Bilgileri",bordered:!1,style:{borderRadius:"12px",boxShadow:"0 2px 8px rgba(0,0,0,0.06)",height:"100%",maxWidth:"800px",margin:"0"},children:e.jsxs(O,{form:d,layout:"vertical",onFinish:_e,initialValues:{status:"active",type:"general"},children:[e.jsx(O.Item,{name:"title",label:e.jsx("span",{style:{fontSize:"14px",fontWeight:600,color:"#333"},children:"Başlık"}),rules:[{required:!0,message:"Lütfen duyuru başlığını girin"}],children:e.jsxs("div",{style:{position:"relative"},children:[e.jsx(me,{value:H,onChange:G,placeholder:"Duyuru başlığını girin...",style:{borderRadius:"8px",padding:"12px 40px 12px 12px",fontSize:"14px"}}),e.jsx(I,{size:"small",icon:C?e.jsx(Wa,{}):e.jsx(ha,{}),onClick:()=>F(),disabled:C,style:{position:"absolute",right:8,top:"50%",transform:"translateY(-50%)",border:"none",background:"transparent",color:"#2563eb",zIndex:2,borderRadius:"6px"},title:"AI ile başlığı iyileştir"})]})}),e.jsx(O.Item,{name:"content",label:e.jsx("span",{style:{fontSize:"14px",fontWeight:600,color:"#333"},children:"İçerik"}),rules:[{required:!0,message:"Lütfen duyuru içeriğini girin"}],children:e.jsxs("div",{style:{position:"relative"},children:[e.jsx(Gx,{rows:6,value:T,onChange:J,placeholder:"Duyuru içeriğini detaylı bir şekilde yazın...",style:{borderRadius:"8px",padding:"12px 40px 12px 12px",fontSize:"14px",lineHeight:"1.6"}}),e.jsx(I,{size:"small",icon:w?e.jsx(Wa,{}):e.jsx(ha,{}),onClick:()=>ie(),disabled:w,style:{position:"absolute",right:8,top:12,border:"none",background:"transparent",color:"#2563eb",zIndex:2,borderRadius:"6px"},title:"AI ile içeriği iyileştir"})]})}),e.jsxs(ke,{gutter:16,children:[e.jsx(N,{xs:24,sm:12,children:e.jsx(O.Item,{name:"type",label:e.jsx("span",{style:{fontSize:"14px",fontWeight:600,color:"#333"},children:"Tür"}),children:e.jsx(Ce,{placeholder:"Duyuru türünü seçin",style:{borderRadius:"8px"},children:en.map(Y=>e.jsx(Ks,{value:Y.value,children:e.jsx(ae,{color:Y.color,style:{marginRight:8},children:Y.label})},Y.value))})})}),e.jsx(N,{xs:24,sm:12,children:e.jsx(O.Item,{name:"status",label:e.jsx("span",{style:{fontSize:"14px",fontWeight:600,color:"#333"},children:"Durum"}),children:e.jsx(Ce,{placeholder:"Durum seçin",style:{borderRadius:"8px"},children:_o.map(Y=>e.jsx(Ks,{value:Y.value,children:e.jsx(ae,{color:Y.color,style:{marginRight:8},children:Y.label})},Y.value))})})})]}),e.jsx(O.Item,{name:"image",label:e.jsx("span",{style:{fontSize:"14px",fontWeight:600,color:"#333"},children:"Görsel"}),valuePropName:"fileList",getValueFromEvent:Y=>Array.isArray(Y)?Y:Y&&Y.fileList,children:e.jsx(Ni,{name:"file",listType:"picture-card",maxCount:1,beforeUpload:()=>!1,onPreview:()=>{},style:{borderRadius:"8px"},children:e.jsxs("div",{style:{padding:"15px",textAlign:"center"},children:[e.jsx(wt,{style:{fontSize:"20px",color:"#999"}}),e.jsx("div",{style:{marginTop:6,color:"#666",fontSize:"12px"},children:"Görsel Yükle"})]})})}),e.jsx(O.Item,{style:{marginTop:"32px",textAlign:"right"},children:e.jsxs(ce,{size:"middle",children:[e.jsx(I,{onClick:()=>d.resetFields(),style:{borderRadius:"8px",padding:"0 24px"},children:"Temizle"}),e.jsx(I,{type:"primary",htmlType:"submit",loading:M,style:{background:"#2563eb",borderColor:"#2563eb",borderRadius:"8px",padding:"0 32px",height:"40px"},children:"Duyuruyu Yayınla"})]})})]})})})},"add"),e.jsxs(So,{tab:e.jsxs("span",{style:{display:"flex",alignItems:"center",gap:"8px"},children:["Duyuruları Yönet",e.jsx("span",{style:{color:"#666",fontWeight:500,fontSize:"12px",background:"#f0f0f0",borderRadius:"12px",padding:"2px 8px",minWidth:"20px",textAlign:"center"},children:x.total})]}),children:[e.jsxs(ke,{gutter:[16,16],style:{marginBottom:"24px"},children:[e.jsx(N,{xs:24,sm:12,md:10,children:e.jsx(me,{placeholder:"Başlıkta ara...",prefix:e.jsx(ta,{}),value:m,onChange:Y=>y(Y.target.value),style:{borderRadius:"8px",height:"40px"},allowClear:!0})}),e.jsx(N,{xs:24,sm:12,md:8,children:e.jsxs(Ce,{value:p,onChange:j,style:{width:"100%",borderRadius:"8px"},placeholder:"Tür seçin",size:"large",allowClear:!0,children:[e.jsx(Ks,{value:"all",children:"Tüm Türler"}),en.map(Y=>e.jsx(Ks,{value:Y.value,children:e.jsx(ae,{color:Y.color,style:{marginRight:8},children:Y.label})},Y.value))]})}),e.jsx(N,{xs:24,md:6,children:e.jsxs("div",{style:{display:"flex",justifyContent:"flex-end",alignItems:"center",height:"40px",fontSize:"14px",color:"#666"},children:["Toplam: ",De.length," duyuru"]})})]}),e.jsx(qe,{columns:je,dataSource:De,rowKey:"id",loading:r,pagination:{pageSize:10,showSizeChanger:!1,showQuickJumper:!1,responsive:!0},scroll:{x:800},size:"middle",style:{borderRadius:"8px",overflow:"hidden"}})]},"manage")]})})})},{Title:F2,Text:H2}=ht,{TextArea:Gs}=me,{Option:La}=Ce,{TabPane:To}=Xe,tn=[{value:"active",label:"Aktif",color:"green"},{value:"draft",label:"Taslak",color:"default"},{value:"archived",label:"Arşivlenmiş",color:"red"}],Jx=()=>{const[t,s]=u.useState([]),[r,n]=u.useState(!0),[d]=O.useForm(),[c,h]=u.useState("add"),[m,y]=u.useState(""),[p,j]=u.useState("all"),[x,b]=u.useState({total:0,active:0,draft:0,archived:0,featured:0}),[C,_]=u.useState(!1),[w,g]=u.useState(!1),[H,U]=u.useState(!1),[T,W]=u.useState(null),[G,J]=u.useState(!1),[B,M]=u.useState(""),[X,oe]=u.useState(""),[K,ye]=u.useState([]),[_e,Re]=u.useState(!1),[A,F]=u.useState(null),[ie]=O.useForm(),je=P=>{M(P.target.value),d.setFieldsValue({title:P.target.value})},De=P=>{oe(P.target.value),d.setFieldsValue({content:P.target.value})},{user:Y}=Ct(),[pe,Oe]=u.useState(!1),be=async()=>{try{const ue=(await fe.get("https://x.tuberajans.com/backend/akademi/categories.php")).data;ue.success?ye(ue.data):S.error(ue.message||"Kategoriler yüklenirken bir hata oluştu.")}catch(P){S.error("Kategoriler yüklenirken bir hata oluştu.")}},Ee=async()=>{n(!0);try{const ue=(await fe.get("https://x.tuberajans.com/backend/akademi/courses.php")).data;if(ue.success){s(ue.data);const xe=ue.data,Me={total:xe.length,active:xe.filter(R=>R.status==="active").length,draft:xe.filter(R=>R.status==="draft").length,archived:xe.filter(R=>R.status==="archived").length,featured:xe.filter(R=>R.featured===!0||R.featured===1).length};b(Me)}else S.error(ue.message||"Eğitimler yüklenirken bir hata oluştu.")}catch(P){S.error("Eğitimler yüklenirken bir sorun oluştu.")}finally{n(!1)}},Z=async P=>{try{const ue=A?"update":"add",xe=A?{old_value:A.value,new_value:P.value,name:P.name}:{name:P.name,value:P.value},R=(await fe.post(`https://x.tuberajans.com/backend/akademi/categories.php?action=${ue}`,xe)).data;R.success?(S.success(R.message),Re(!1),F(null),ie.resetFields(),be(),Ee()):S.error(R.message)}catch(ue){S.error("Kategori işlemi sırasında bir hata oluştu.")}},D=async P=>{try{const xe=(await fe.post("https://x.tuberajans.com/backend/akademi/categories.php?action=delete",{value:P.value})).data;xe.success?(S.success(xe.message),be(),Ee()):S.error(xe.message)}catch(ue){S.error("Kategori silinirken bir hata oluştu.")}},Q=P=>{P?(F(P),ie.setFieldsValue({name:P.name,value:P.value})):(F(null),ie.resetFields()),Re(!0)};u.useEffect(()=>{be(),Ee();const P=setTimeout(()=>{K.length===0&&ye([{id:1,name:"Genel",value:"general",course_count:0},{id:2,name:"Video Editörlüğü",value:"video-editing",course_count:0},{id:3,name:"İçerik Üretimi",value:"content-creation",course_count:0},{id:4,name:"Sosyal Medya",value:"social-media",course_count:0},{id:5,name:"Pazarlama",value:"marketing",course_count:0},{id:6,name:"Teknik",value:"technical",course_count:0},{id:7,name:"Yaratıcılık",value:"creativity",course_count:0},{id:8,name:"İş Geliştirme",value:"business",course_count:0}])},3e3);return()=>clearTimeout(P)},[]);const k=()=>{d.resetFields(),M(""),oe("")},ge=async P=>{Oe(!0);try{const ue={title:B||P.title,description:P.description,content:X||P.content,category:P.category,status:P.status||"active",featured:P.featured||!1},Me=(await fe.post("https://x.tuberajans.com/backend/akademi/courses.php?action=add",ue)).data;Me.success?(S.success("Eğitim başarıyla eklendi!"),Ee(),k(),h("manage")):S.error(Me.message||"İşlem sırasında bir hata oluştu.")}catch(ue){S.error("İşlem sırasında bir hata oluştu.")}finally{Oe(!1)}},$=async P=>{try{const xe=(await fe.post("https://x.tuberajans.com/backend/akademi/courses.php?action=delete",{id:P})).data;xe.success?(S.success("Eğitim başarıyla silindi!"),Ee()):S.error(xe.message||"Eğitim silinirken bir sorun oluştu.")}catch(ue){S.error("Eğitim silinirken bir sorun oluştu.")}},te=async()=>{_(!0);try{if(!B){S.warning("Lütfen önce başlık alanını doldurun."),_(!1);return}const P=localStorage.getItem("ai_advisor_api_keys"),ue=localStorage.getItem("ai_advisor_model");if(!P||!ue){S.warning("AI ayarları bulunamadı. Lütfen önce AI Danışmanı sekmesinden API anahtarlarınızı ve modelinizi ayarlayın."),_(!1);return}const xe=JSON.parse(P),Me=ue;let R="";if(Me.includes("gpt")||Me.includes("openai")){if(R=xe.openai,!R){S.warning("OpenAI API anahtarı bulunamadı. Lütfen AI Danışmanı ayarlarından ekleyin."),_(!1);return}}else if((Me.includes("claude")||Me.includes("anthropic"))&&(R=xe.anthropic,!R)){S.warning("Anthropic API anahtarı bulunamadı. Lütfen AI Danışmanı ayarlarından ekleyin."),_(!1);return}const re=`Bu eğitim başlığını daha profesyonel, dikkat çekici ve net bir şekilde iyileştir. Başlık: "${B}". Sadece iyileştirilmiş başlığı döndür, başka açıklama yapma.`,Je=(await fe.post("https://x.tuberajans.com/backend/x-site/ai-advisor.php?endpoint=ai",{messages:[{role:"user",content:re}],model:Me,apiKey:R,max_tokens:100})).data;if(Je.response){const He=Je.response.trim();M(He),d.setFieldsValue({title:He}),S.success("Başlık AI ile iyileştirildi!")}}catch(P){S.error("AI ile iyileştirme sırasında bir hata oluştu.")}finally{_(!1)}},Ye=async()=>{g(!0);try{if(!X){S.warning("Lütfen önce içerik alanını doldurun."),g(!1);return}const P=localStorage.getItem("ai_advisor_api_keys"),ue=localStorage.getItem("ai_advisor_model");if(!P||!ue){S.warning("AI ayarları bulunamadı. Lütfen önce AI Danışmanı sekmesinden API anahtarlarınızı ve modelinizi ayarlayın."),g(!1);return}const xe=JSON.parse(P),Me=ue;let R="";if(Me.includes("gpt")||Me.includes("openai")){if(R=xe.openai,!R){S.warning("OpenAI API anahtarı bulunamadı. Lütfen AI Danışmanı ayarlarından ekleyin."),g(!1);return}}else if((Me.includes("claude")||Me.includes("anthropic"))&&(R=xe.anthropic,!R)){S.warning("Anthropic API anahtarı bulunamadı. Lütfen AI Danışmanı ayarlarından ekleyin."),g(!1);return}const re=`Bu eğitim içeriğini daha açık, anlaşılır ve profesyonel bir dille yeniden yaz. İçerik: "${X}". Sadece iyileştirilmiş içeriği döndür, başka açıklama yapma.`,Je=(await fe.post("https://x.tuberajans.com/backend/x-site/ai-advisor.php?endpoint=ai",{messages:[{role:"user",content:re}],model:Me,apiKey:R,max_tokens:500})).data;if(Je.response){const He=Je.response.trim();oe(He),d.setFieldsValue({content:He}),S.success("İçerik AI ile iyileştirildi!")}}catch(P){S.error("AI ile iyileştirme sırasında bir hata oluştu.")}finally{g(!1)}},Ve=P=>{W(P),d.setFieldsValue({title:P.title,description:P.description,content:P.content,category:P.category,status:P.status,featured:P.featured}),M(P.title),oe(P.content),U(!0)},jt=()=>{U(!1),W(null),d.resetFields(),M(""),oe("")},aa=async P=>{if(T){J(!0);try{const ue={id:T.id,title:B||P.title,description:P.description,content:X||P.content,category:P.category,status:P.status,featured:P.featured||!1},Me=(await fe.post("https://x.tuberajans.com/backend/akademi/courses.php?action=update",ue)).data;Me.success?(S.success("Eğitim başarıyla güncellendi!"),Ee(),jt()):S.error(Me.message||"İşlem sırasında bir hata oluştu.")}catch(ue){S.error("İşlem sırasında bir hata oluştu.")}finally{J(!1)}}},Bt=[{title:"ID",dataIndex:"id",key:"id",width:60,responsive:["md"]},{title:"Başlık",dataIndex:"title",key:"title",ellipsis:!0,render:P=>e.jsx("div",{style:{maxWidth:250,whiteSpace:"normal",fontSize:"14px",fontWeight:500},children:P})},{title:"Kategori",dataIndex:"category",key:"category",width:120,responsive:["sm"],render:P=>{const ue=K.find(xe=>xe.value===P);return ue?e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[e.jsx(ae,{color:"blue",style:{fontSize:"11px"},children:ue.name}),e.jsxs("div",{style:{display:"flex",gap:"4px"},children:[e.jsx(I,{size:"small",icon:e.jsx(yt,{}),onClick:xe=>{xe.stopPropagation(),Q(ue)},style:{width:"24px",height:"24px",padding:0,border:"none",background:"transparent",color:"#1890ff"},title:"Kategoriyi düzenle"}),e.jsx(it,{title:"Bu kategoriyi silmek istediğinizden emin misiniz?",description:"Bu kategorideki tüm eğitimler 'Genel' kategorisine taşınacak.",onConfirm:xe=>{xe==null||xe.stopPropagation(),D(ue)},okText:"Evet",cancelText:"Hayır",onClick:xe=>xe==null?void 0:xe.stopPropagation(),children:e.jsx(I,{size:"small",icon:e.jsx(Et,{}),onClick:xe=>xe.stopPropagation(),style:{width:"24px",height:"24px",padding:0,border:"none",background:"transparent",color:"#ff4d4f"},title:"Kategoriyi sil"})})]})]}):e.jsx(ae,{color:"default",style:{fontSize:"11px"},children:P||"Genel"})}},{title:"Durum",dataIndex:"status",key:"status",width:100,responsive:["lg"],render:P=>{const ue=tn.find(xe=>xe.value===P)||{label:P,color:"default"};return e.jsx(ae,{color:ue.color,style:{fontSize:"11px"},children:ue.label})}},{title:"Öne Çıkan",dataIndex:"featured",key:"featured",width:100,responsive:["md"],render:P=>{const ue=P===!0||P===1;return e.jsx(ae,{color:ue?"gold":"default",style:{fontSize:"11px"},children:ue?"Evet":"Hayır"})}},{title:"Tarih",dataIndex:"created_at",key:"created_at",width:120,responsive:["md"],render:P=>e.jsx("span",{style:{fontSize:"12px",color:"#666"},children:Fe(P).format("DD/MM/YY")})},{title:"İşlemler",key:"actions",width:100,align:"center",fixed:"right",render:(P,ue)=>e.jsxs(ce,{size:8,style:{display:"flex",justifyContent:"center"},children:[e.jsx(_t,{title:"Düzenle",children:e.jsx(I,{icon:e.jsx(yt,{}),onClick:()=>Ve(ue),size:"small",type:"default",style:{borderRadius:"6px",width:"32px",height:"32px",display:"flex",alignItems:"center",justifyContent:"center",border:"1px solid #d9d9d9"}})}),e.jsx(_t,{title:"Sil",children:e.jsx(it,{title:"Bu eğitimi silmek istediğinizden emin misiniz?",onConfirm:()=>$(ue.id),okText:"Evet",cancelText:"Hayır",okButtonProps:{style:{backgroundColor:"#ff4d4f",borderColor:"#ff4d4f",color:"#fff"}},cancelButtonProps:{style:{backgroundColor:"#f5f5f5",borderColor:"#d9d9d9",color:"#000"}},placement:"topLeft",children:e.jsx(I,{icon:e.jsx(Et,{}),size:"small",danger:!0,style:{borderRadius:"6px",width:"32px",height:"32px",display:"flex",alignItems:"center",justifyContent:"center"}})})})]})}],sa=t.filter(P=>{const ue=P.title.toLowerCase().includes(m.toLowerCase());let xe=p==="all";return!xe&&P.category&&(xe=P.category===p),ue&&xe});return e.jsxs("div",{style:{padding:"16px",background:"#f5f5f5",height:"100vh",overflow:"hidden"},children:[e.jsx(q,{style:{borderRadius:"12px",boxShadow:"0 4px 12px rgba(0,0,0,0.1)",height:"calc(100vh - 32px)"},children:e.jsxs(Xe,{activeKey:c,onChange:h,style:{height:"100%"},children:[e.jsx(To,{tab:"Yeni Eğitim Ekle",children:e.jsx("div",{style:{height:"calc(100vh - 140px)",overflow:"auto",padding:"16px"},children:e.jsx(q,{title:"Eğitim Bilgileri",bordered:!1,style:{borderRadius:"12px",boxShadow:"0 2px 8px rgba(0,0,0,0.06)",width:"100%",maxWidth:"800px"},children:e.jsxs(O,{form:d,layout:"vertical",onFinish:ge,initialValues:{status:"active",category:"general",featured:!1},children:[e.jsx(O.Item,{name:"title",label:e.jsx("span",{style:{fontSize:"14px",fontWeight:600,color:"#333"},children:"Başlık"}),rules:[{required:!0,message:"Lütfen eğitim başlığını girin"}],children:e.jsxs("div",{style:{position:"relative"},children:[e.jsx(me,{value:B,onChange:je,placeholder:"Eğitim başlığını girin...",style:{borderRadius:"8px",padding:"12px 40px 12px 12px",fontSize:"14px"}}),e.jsx(I,{size:"small",icon:C?e.jsx(Wa,{}):e.jsx(ha,{}),onClick:()=>te(),disabled:C,style:{position:"absolute",right:8,top:"50%",transform:"translateY(-50%)",border:"none",background:"transparent",color:"#2563eb",zIndex:2,borderRadius:"6px"},title:"AI ile başlığı iyileştir"})]})}),e.jsx(O.Item,{name:"description",label:e.jsx("span",{style:{fontSize:"14px",fontWeight:600,color:"#333"},children:"Kısa Açıklama"}),rules:[{required:!0,message:"Lütfen eğitim açıklamasını girin"}],children:e.jsx(Gs,{rows:3,placeholder:"Eğitim hakkında kısa bir açıklama yazın...",style:{borderRadius:"8px",fontSize:"14px",lineHeight:"1.6"}})}),e.jsx(O.Item,{name:"content",label:e.jsx("span",{style:{fontSize:"14px",fontWeight:600,color:"#333"},children:"İçerik"}),rules:[{required:!0,message:"Lütfen eğitim içeriğini girin"}],children:e.jsxs("div",{style:{position:"relative"},children:[e.jsx(Gs,{rows:6,value:X,onChange:De,placeholder:"Eğitim içeriğini detaylı bir şekilde yazın...",style:{borderRadius:"8px",padding:"12px 40px 12px 12px",fontSize:"14px",lineHeight:"1.6"}}),e.jsx(I,{size:"small",icon:w?e.jsx(Wa,{}):e.jsx(ha,{}),onClick:()=>Ye(),disabled:w,style:{position:"absolute",right:8,top:12,border:"none",background:"transparent",color:"#2563eb",zIndex:2,borderRadius:"6px"},title:"AI ile içeriği iyileştir"})]})}),e.jsxs(ke,{gutter:16,children:[e.jsx(N,{xs:24,sm:12,children:e.jsx(O.Item,{name:"category",label:e.jsx("span",{style:{fontSize:"14px",fontWeight:600,color:"#333"},children:"Kategori"}),children:e.jsx(Ce,{placeholder:"Kategori seçin",style:{borderRadius:"8px"},loading:K.length===0,notFoundContent:K.length===0?"Kategoriler yükleniyor...":"Kategori bulunamadı",onDropdownVisibleChange:P=>{},children:K.map(P=>e.jsx(La,{value:P.value,children:P.name},P.value))})})}),e.jsx(N,{xs:24,sm:12,children:e.jsx(O.Item,{name:"status",label:e.jsx("span",{style:{fontSize:"14px",fontWeight:600,color:"#333"},children:"Durum"}),children:e.jsx(Ce,{placeholder:"Durum seçin",style:{borderRadius:"8px"},children:tn.map(P=>e.jsx(La,{value:P.value,children:e.jsx(ae,{color:P.color,style:{marginRight:8},children:P.label})},P.value))})})})]}),e.jsx(O.Item,{name:"image",label:e.jsx("span",{style:{fontSize:"14px",fontWeight:600,color:"#333"},children:"Görsel"}),valuePropName:"fileList",getValueFromEvent:P=>Array.isArray(P)?P:P&&P.fileList,children:e.jsx(Ni,{name:"file",listType:"picture-card",maxCount:1,beforeUpload:()=>!1,onPreview:()=>{},style:{borderRadius:"8px"},children:e.jsxs("div",{style:{padding:"15px",textAlign:"center"},children:[e.jsx(wt,{style:{fontSize:"20px",color:"#999"}}),e.jsx("div",{style:{marginTop:6,color:"#666",fontSize:"12px"},children:"Görsel Yükle"})]})})}),e.jsx(O.Item,{name:"featured",valuePropName:"checked",children:e.jsx(wa,{children:"Öne Çıkan Eğitim"})}),e.jsx(O.Item,{style:{marginTop:"32px",textAlign:"right"},children:e.jsxs(ce,{size:"middle",children:[e.jsx(I,{onClick:()=>d.resetFields(),style:{borderRadius:"8px",padding:"0 24px"},children:"Temizle"}),e.jsx(I,{type:"primary",htmlType:"submit",loading:pe,style:{background:"#2563eb",borderColor:"#2563eb",borderRadius:"8px",padding:"0 32px",height:"40px"},children:"Eğitimi Yayınla"})]})})]})})})},"add"),e.jsxs(To,{tab:e.jsxs("span",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[e.jsx(wr,{}),"Eğitimler",e.jsx("span",{style:{color:"#666",fontWeight:500,fontSize:"12px",background:"#f0f0f0",borderRadius:"12px",padding:"2px 8px",minWidth:"20px",textAlign:"center"},children:x.total})]}),children:[e.jsxs(ke,{gutter:[16,16],style:{marginBottom:"24px"},children:[e.jsx(N,{xs:24,sm:12,md:10,children:e.jsx(me,{placeholder:"Başlıkta ara...",prefix:e.jsx(ta,{}),value:m,onChange:P=>y(P.target.value),style:{borderRadius:"8px",height:"40px"},allowClear:!0})}),e.jsx(N,{xs:24,sm:12,md:6,children:e.jsxs(Ce,{value:p,onChange:j,style:{width:"100%",borderRadius:"8px"},placeholder:"Kategori seçin",size:"large",allowClear:!0,children:[e.jsx(La,{value:"all",children:"Tüm Kategoriler"}),K.map(P=>e.jsxs(La,{value:P.value,children:[P.name," (",P.course_count||0,")"]},P.value))]})}),e.jsx(N,{xs:24,sm:12,md:4,children:e.jsx(I,{type:"primary",icon:e.jsx(wt,{}),onClick:()=>Q(),style:{width:"100%",borderRadius:"8px",height:"40px",background:"#52c41a",borderColor:"#52c41a"},children:"Kategori Ekle"})}),e.jsx(N,{xs:24,md:4,children:e.jsxs("div",{style:{display:"flex",justifyContent:"flex-end",alignItems:"center",height:"40px",fontSize:"14px",color:"#666"},children:["Toplam: ",sa.length," eğitim"]})})]}),e.jsx(qe,{columns:Bt,dataSource:sa,rowKey:"id",loading:r,pagination:{pageSize:10,showSizeChanger:!1,showQuickJumper:!1,responsive:!0},scroll:{x:800},size:"middle",style:{borderRadius:"8px",overflow:"hidden"}})]},"manage")]})}),e.jsx(St,{title:"Eğitim Düzenle",open:H,onCancel:jt,footer:null,width:800,children:e.jsxs(O,{form:d,layout:"vertical",onFinish:aa,children:[e.jsx(O.Item,{name:"title",label:e.jsx("span",{style:{fontSize:"14px",fontWeight:600,color:"#333"},children:"Başlık"}),rules:[{required:!0,message:"Lütfen eğitim başlığını girin"}],children:e.jsxs("div",{style:{position:"relative"},children:[e.jsx(me,{value:B,onChange:je,placeholder:"Eğitim başlığını girin...",style:{borderRadius:"8px",padding:"12px 40px 12px 12px",fontSize:"14px"}}),e.jsx(I,{size:"small",icon:C?e.jsx(Wa,{}):e.jsx(ha,{}),onClick:()=>te(),disabled:C,style:{position:"absolute",right:8,top:"50%",transform:"translateY(-50%)",border:"none",background:"transparent",color:"#2563eb",zIndex:2,borderRadius:"6px"},title:"AI ile başlığı iyileştir"})]})}),e.jsx(O.Item,{name:"description",label:e.jsx("span",{style:{fontSize:"14px",fontWeight:600,color:"#333"},children:"Kısa Açıklama"}),rules:[{required:!0,message:"Lütfen eğitim açıklamasını girin"}],children:e.jsx(Gs,{rows:3,placeholder:"Eğitim hakkında kısa bir açıklama yazın...",style:{borderRadius:"8px",fontSize:"14px",lineHeight:"1.6"}})}),e.jsx(O.Item,{name:"content",label:e.jsx("span",{style:{fontSize:"14px",fontWeight:600,color:"#333"},children:"İçerik"}),rules:[{required:!0,message:"Lütfen eğitim içeriğini girin"}],children:e.jsxs("div",{style:{position:"relative"},children:[e.jsx(Gs,{rows:6,value:X,onChange:De,placeholder:"Eğitim içeriğini detaylı bir şekilde yazın...",style:{borderRadius:"8px",padding:"12px 40px 12px 12px",fontSize:"14px",lineHeight:"1.6"}}),e.jsx(I,{size:"small",icon:w?e.jsx(Wa,{}):e.jsx(ha,{}),onClick:()=>Ye(),disabled:w,style:{position:"absolute",right:8,top:12,border:"none",background:"transparent",color:"#2563eb",zIndex:2,borderRadius:"6px"},title:"AI ile içeriği iyileştir"})]})}),e.jsxs(ke,{gutter:16,children:[e.jsx(N,{xs:24,sm:12,children:e.jsx(O.Item,{name:"category",label:e.jsx("span",{style:{fontSize:"14px",fontWeight:600,color:"#333"},children:"Kategori"}),children:e.jsx(Ce,{placeholder:"Kategori seçin",style:{borderRadius:"8px"},loading:K.length===0,children:K.map(P=>e.jsx(La,{value:P.value,children:P.name},P.value))})})}),e.jsx(N,{xs:24,sm:12,children:e.jsx(O.Item,{name:"status",label:e.jsx("span",{style:{fontSize:"14px",fontWeight:600,color:"#333"},children:"Durum"}),children:e.jsx(Ce,{placeholder:"Durum seçin",style:{borderRadius:"8px"},children:tn.map(P=>e.jsx(La,{value:P.value,children:e.jsx(ae,{color:P.color,style:{marginRight:8},children:P.label})},P.value))})})})]}),e.jsx(O.Item,{name:"featured",valuePropName:"checked",children:e.jsx(wa,{children:"Öne Çıkan Eğitim"})}),e.jsx(O.Item,{style:{textAlign:"right",marginBottom:0},children:e.jsxs(ce,{children:[e.jsx(I,{onClick:jt,children:"İptal"}),e.jsx(I,{type:"primary",htmlType:"submit",loading:G,style:{background:"#2563eb",borderColor:"#2563eb",borderRadius:"8px"},children:"Güncelle"})]})})]})}),e.jsx(St,{title:A?"Kategori Düzenle":"Yeni Kategori Ekle",open:_e,onCancel:()=>{Re(!1),F(null),ie.resetFields()},footer:null,width:500,children:e.jsxs(O,{form:ie,layout:"vertical",onFinish:Z,children:[e.jsx(O.Item,{name:"name",label:"Kategori Adı",rules:[{required:!0,message:"Lütfen kategori adını girin"},{min:2,message:"Kategori adı en az 2 karakter olmalıdır"}],children:e.jsx(me,{placeholder:"Örn: Video Editörlüğü",style:{borderRadius:"8px"}})}),e.jsx(O.Item,{name:"value",label:"Kategori Değeri (URL için)",rules:[{required:!0,message:"Lütfen kategori değerini girin"},{pattern:/^[a-z0-9-]+$/,message:"Sadece küçük harf, rakam ve tire kullanın"}],children:e.jsx(me,{placeholder:"Örn: video-editing",style:{borderRadius:"8px"},onChange:P=>{const ue=P.target.value.toLowerCase().replace(/[^a-z0-9\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-");ie.setFieldsValue({value:ue})}})}),e.jsx(O.Item,{style:{textAlign:"right",marginBottom:0},children:e.jsxs(ce,{children:[e.jsx(I,{onClick:()=>{Re(!1),F(null),ie.resetFields()},children:"İptal"}),e.jsx(I,{type:"primary",htmlType:"submit",style:{background:A?"#1890ff":"#52c41a",borderColor:A?"#1890ff":"#52c41a",borderRadius:"8px"},children:A?"Güncelle":"Ekle"})]})})]})})]})},{Title:W2,Text:Zx}=ht,{TextArea:Xx}=me,{Option:xs}=Ce,{RangePicker:Qx}=Ho,{TabPane:Co}=Xe,ey=[{value:"active",label:"Aktif"},{value:"cancelled",label:"İptal Edildi"},{value:"completed",label:"Tamamlandı"}],ty=()=>{const[t,s]=u.useState([]),[r,n]=u.useState(!0),[d,c]=u.useState(!1),[h,m]=u.useState(null),[y]=O.useForm(),[p,j]=u.useState([]),[x,b]=u.useState(!1),[C,_]=u.useState("add"),[w,g]=u.useState([]),[H,U]=u.useState(!0),[T,W]=u.useState(!1),[G,J]=u.useState(null),[B]=O.useForm(),[M,X]=u.useState(""),[oe,K]=u.useState("all"),{user:ye}=Ct(),_e=async()=>{n(!0);try{const D=await fe.get("https://x.tuberajans.com/backend/akademi/events.php");D.data.success?s(D.data.data):S.error(D.data.message||"Etkinlikler yüklenirken bir hata oluştu.")}catch(D){S.error("Etkinlikler yüklenirken bir sorun oluştu.")}finally{n(!1)}},Re=async()=>{U(!0);try{const Q=(await fe.get("https://x.tuberajans.com/backend/akademi/events.php?action=list_categories")).data;Q.success?(g(Q.data||[]),setTimeout(()=>{},100)):(S.error(Q.message||"Kategoriler yüklenirken bir hata oluştu."),g([]))}catch(D){S.error("Kategoriler yüklenirken bir hata oluştu."),g([])}finally{U(!1)}};u.useEffect(()=>{_e(),Re()},[]);const A=D=>{m(D||null),y.resetFields(),D?(y.setFieldsValue({title:D.title,description:D.description,location:D.location,capacity:D.capacity,instructor:D.instructor,category:D.category,is_featured:D.is_featured,status:D.status,date_range:[Fe(D.start_date),Fe(D.end_date)],start_time:Fe(D.start_date),end_time:Fe(D.end_date)}),D.thumbnail?j([{uid:"-1",name:"thumbnail.png",status:"done",url:`https://x.tuberajans.com${D.thumbnail}`}]):j([])):(j([]),y.setFieldsValue({status:"active",is_featured:!1,capacity:0})),c(!0)},F=()=>{c(!1),m(null),y.resetFields(),j([])},ie=async D=>{b(!0);try{const Q=new FormData;Q.append("title",D.title),Q.append("description",D.description),Q.append("location",D.location),Q.append("capacity",D.capacity.toString()),Q.append("instructor",D.instructor||""),Q.append("category",D.category),Q.append("is_featured",D.is_featured?"1":"0"),Q.append("status",D.status);const k=D.date_range[0].format("YYYY-MM-DD"),ge=D.date_range[1].format("YYYY-MM-DD"),$=D.start_time.format("HH:mm:ss"),te=D.end_time.format("HH:mm:ss");Q.append("start_date",`${k} ${$}`),Q.append("end_date",`${ge} ${te}`),p.length>0&&p[0].originFileObj&&Q.append("thumbnail",p[0].originFileObj);let Ye;h?(Q.append("id",h.id.toString()),Ye=await fe.post("https://x.tuberajans.com/backend/akademi/events.php?action=update",Q)):Ye=await fe.post("https://x.tuberajans.com/backend/akademi/events.php?action=add",Q),Ye.data.success?(S.success(h?"Etkinlik başarıyla güncellendi!":"Etkinlik başarıyla eklendi!"),c(!1),_e()):S.error(Ye.data.message||"İşlem sırasında bir hata oluştu.")}catch(Q){S.error("Etkinlik kaydedilirken bir sorun oluştu.")}finally{b(!1)}},je=async D=>{try{const Q=await fe.post("https://x.tuberajans.com/backend/akademi/events.php?action=delete",{id:D});Q.data.success?(S.success("Etkinlik başarıyla silindi!"),_e()):S.error(Q.data.message||"Etkinlik silinirken bir hata oluştu.")}catch(Q){S.error("Etkinlik silinirken bir sorun oluştu.")}},De=D=>{window.open(`/akademi/etkinlik/${D.id}`,"_blank")},Y=D=>{J(D||null),B.resetFields(),D&&B.setFieldsValue({name:D.name}),W(!0)},pe=async D=>{try{if(G){const k=(await fe.post("https://x.tuberajans.com/backend/akademi/events.php?action=update_category",{old_category:G.value,new_category:D.name})).data;k.success?(S.success(k.message),W(!1),J(null),B.resetFields(),Re(),_e()):S.error(k.message)}else{const k=(await fe.post("https://x.tuberajans.com/backend/akademi/event_categories.php?action=add",{name:D.name,value:D.name})).data;k.success?(S.success(k.message),W(!1),J(null),B.resetFields(),Re()):S.error(k.message)}}catch(Q){S.error("Kategori işlemi sırasında bir hata oluştu.")}},Oe=async D=>{try{const k=(await fe.post("https://x.tuberajans.com/backend/akademi/events.php?action=delete_category",{category:D.value,move_to:"Diğer"})).data;k.success?(S.success(k.message),Re(),_e()):S.error(k.message)}catch(Q){S.error("Kategori silinirken bir hata oluştu.")}},be=[{title:"Başlık",dataIndex:"title",key:"title",render:(D,Q)=>e.jsxs("div",{className:"flex items-center",children:[Q.is_featured&&e.jsx(Zg,{className:"mr-1 text-yellow-500"}),e.jsx("span",{children:D})]})},{title:"Kategori",dataIndex:"category",key:"category",width:150,render:D=>{const Q=w.find(k=>k.value===D);return Q?e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[e.jsx(ae,{color:D==="Webinar"?"blue":D==="Seminer"?"purple":D==="Buluşma"?"green":D==="Atölye"?"orange":D==="Eğitim"?"cyan":"default",style:{fontSize:"11px"},children:Q.name}),e.jsxs("div",{style:{display:"flex",gap:"4px"},children:[e.jsx(I,{size:"small",icon:e.jsx(yt,{}),onClick:k=>{k.stopPropagation(),Y(Q)},style:{width:"24px",height:"24px",padding:0,border:"none",background:"transparent",color:"#1890ff"},title:"Kategoriyi düzenle"}),e.jsx(it,{title:"Bu kategoriyi silmek istediğinizden emin misiniz?",description:"Bu kategorideki tüm etkinlikler 'Diğer' kategorisine taşınacaktır.",onConfirm:k=>{k==null||k.stopPropagation(),Oe(Q)},okText:"Evet",cancelText:"Hayır",placement:"topLeft",children:e.jsx(I,{size:"small",icon:e.jsx(Et,{}),onClick:k=>k.stopPropagation(),style:{width:"24px",height:"24px",padding:0,border:"none",background:"transparent",color:"#ff4d4f"},title:"Kategoriyi sil"})})]})]}):e.jsx(ae,{children:D})}},{title:"Tarih",dataIndex:"start_date",key:"start_date",render:D=>Fe(D).format("DD.MM.YYYY HH:mm")},{title:"Konum",dataIndex:"location",key:"location"},{title:"Durum",dataIndex:"status",key:"status",render:D=>e.jsx(ae,{color:D==="active"?"green":D==="cancelled"?"red":D==="completed"?"blue":"default",children:D==="active"?"Aktif":D==="cancelled"?"İptal Edildi":D==="completed"?"Tamamlandı":D})},{title:"İşlemler",key:"actions",render:(D,Q)=>e.jsxs(ce,{size:"small",children:[e.jsx(_t,{title:"Düzenle",children:e.jsx(I,{icon:e.jsx(yt,{}),size:"small",onClick:()=>A(Q)})}),e.jsx(_t,{title:"Önizle",children:e.jsx(I,{icon:e.jsx(Rt,{}),size:"small",onClick:()=>De(Q)})}),e.jsx(it,{title:"Bu etkinliği silmek istediğinize emin misiniz?",onConfirm:()=>je(Q.id),okText:"Evet",cancelText:"Hayır",children:e.jsx(_t,{title:"Sil",children:e.jsx(I,{icon:e.jsx(Et,{}),size:"small",danger:!0})})})]})}],Ee=t.filter(D=>{const Q=D.title.toLowerCase().includes(M.toLowerCase());let k=oe==="all";return!k&&D.category&&(k=D.category===oe),Q&&k}),Z={total:t.length,active:t.filter(D=>D.status==="active").length,completed:t.filter(D=>D.status==="completed").length,cancelled:t.filter(D=>D.status==="cancelled").length};return e.jsxs("div",{style:{padding:"16px",background:"#f5f5f5",height:"100vh",overflow:"hidden"},children:[e.jsx(q,{style:{borderRadius:"12px",boxShadow:"0 4px 12px rgba(0,0,0,0.1)",height:"calc(100vh - 32px)"},children:e.jsxs(Xe,{activeKey:C,onChange:_,style:{height:"100%"},children:[e.jsx(Co,{tab:"Yeni Etkinlik Ekle",children:e.jsx("div",{style:{height:"calc(100vh - 140px)",overflow:"auto",padding:"16px"},children:e.jsx(q,{title:"Etkinlik Bilgileri",bordered:!1,style:{borderRadius:"12px",boxShadow:"0 2px 8px rgba(0,0,0,0.06)",width:"100%",maxWidth:"800px"},children:e.jsxs(O,{form:y,layout:"vertical",onFinish:ie,initialValues:{status:"active",is_featured:!1,capacity:0},children:[e.jsxs(ke,{gutter:16,children:[e.jsx(N,{span:16,children:e.jsx(O.Item,{name:"title",label:e.jsx("span",{style:{fontSize:"14px",fontWeight:600,color:"#333"},children:"Etkinlik Başlığı"}),rules:[{required:!0,message:"Lütfen etkinlik başlığını girin"}],children:e.jsx(me,{placeholder:"Etkinlik başlığını girin...",style:{borderRadius:"8px",fontSize:"14px"}})})}),e.jsx(N,{span:8,children:e.jsx(O.Item,{name:"category",label:e.jsx("span",{style:{fontSize:"14px",fontWeight:600,color:"#333"},children:"Kategori"}),rules:[{required:!0,message:"Lütfen kategori seçin"}],children:e.jsxs(Ce,{placeholder:"Kategori seçin",style:{borderRadius:"8px"},loading:H,notFoundContent:H?"Kategoriler yükleniyor...":"Kategori bulunamadı",dropdownRender:D=>e.jsxs(e.Fragment,{children:[D,e.jsx("div",{style:{padding:"8px 0",borderTop:"1px solid #f0f0f0"},children:e.jsx(I,{type:"text",icon:e.jsx(wt,{}),onClick:Q=>{Q.stopPropagation(),Y()},style:{width:"100%",textAlign:"left",color:"#1890ff",border:"none",boxShadow:"none"},children:"Yeni Kategori Ekle"})})]}),children:[void 0,w&&w.length>0?w.map(D=>e.jsx(xs,{value:D.value,children:e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",width:"100%"},children:[e.jsx("span",{style:{flex:1},children:D.name}),e.jsx("div",{onClick:Q=>{Q.stopPropagation(),Q.preventDefault(),Y(D)},style:{padding:"4px",borderRadius:"4px",cursor:"pointer",color:"#1890ff",fontSize:"12px",marginLeft:"8px"},title:"Kategoriyi düzenle",children:e.jsx(yt,{})})]})},D.value)):e.jsx(xs,{disabled:!0,value:"no-categories",children:'Henüz kategori yok - "Yeni Kategori Ekle" butonunu kullanın'})]})})})]}),e.jsx(O.Item,{name:"description",label:e.jsx("span",{style:{fontSize:"14px",fontWeight:600,color:"#333"},children:"Açıklama"}),rules:[{required:!0,message:"Lütfen etkinlik açıklamasını girin"}],children:e.jsx(Xx,{rows:4,placeholder:"Etkinlik hakkında detaylı açıklama yazın...",style:{borderRadius:"8px",fontSize:"14px",lineHeight:"1.6"}})}),e.jsxs(ke,{gutter:16,children:[e.jsx(N,{span:12,children:e.jsx(O.Item,{name:"date_range",label:e.jsx("span",{style:{fontSize:"14px",fontWeight:600,color:"#333"},children:"Tarih Aralığı"}),rules:[{required:!0,message:"Lütfen tarih aralığını seçin"}],children:e.jsx(Qx,{style:{width:"100%",borderRadius:"8px"}})})}),e.jsx(N,{span:6,children:e.jsx(O.Item,{name:"start_time",label:e.jsx("span",{style:{fontSize:"14px",fontWeight:600,color:"#333"},children:"Başlangıç Saati"}),rules:[{required:!0,message:"Lütfen başlangıç saatini seçin"}],children:e.jsx(Pl,{format:"HH:mm",style:{width:"100%",borderRadius:"8px"}})})}),e.jsx(N,{span:6,children:e.jsx(O.Item,{name:"end_time",label:e.jsx("span",{style:{fontSize:"14px",fontWeight:600,color:"#333"},children:"Bitiş Saati"}),rules:[{required:!0,message:"Lütfen bitiş saatini seçin"}],children:e.jsx(Pl,{format:"HH:mm",style:{width:"100%",borderRadius:"8px"}})})})]}),e.jsxs(ke,{gutter:16,children:[e.jsx(N,{span:16,children:e.jsx(O.Item,{name:"location",label:e.jsx("span",{style:{fontSize:"14px",fontWeight:600,color:"#333"},children:"Konum"}),rules:[{required:!0,message:"Lütfen etkinlik konumunu girin"}],children:e.jsx(me,{placeholder:"Etkinlik konumunu girin...",style:{borderRadius:"8px",fontSize:"14px"}})})}),e.jsx(N,{span:8,children:e.jsx(O.Item,{name:"capacity",label:e.jsx("span",{style:{fontSize:"14px",fontWeight:600,color:"#333"},children:"Kapasite"}),rules:[{required:!0,message:"Lütfen etkinlik kapasitesini girin"}],children:e.jsx(Bf,{min:0,placeholder:"0",style:{width:"100%",borderRadius:"8px"}})})})]}),e.jsx(O.Item,{name:"instructor",label:e.jsx("span",{style:{fontSize:"14px",fontWeight:600,color:"#333"},children:"Eğitmen / Konuşmacı"}),children:e.jsx(me,{placeholder:"Eğitmen veya konuşmacı adını girin...",style:{borderRadius:"8px",fontSize:"14px"}})}),e.jsxs(ke,{gutter:16,children:[e.jsx(N,{span:12,children:e.jsx(O.Item,{name:"status",label:e.jsx("span",{style:{fontSize:"14px",fontWeight:600,color:"#333"},children:"Durum"}),rules:[{required:!0,message:"Lütfen etkinlik durumunu seçin"}],children:e.jsx(Ce,{placeholder:"Durum seçin",style:{borderRadius:"8px"},children:ey.map(D=>e.jsx(xs,{value:D.value,children:D.label},D.value))})})}),e.jsx(N,{span:12,children:e.jsx(O.Item,{name:"is_featured",label:e.jsx("span",{style:{fontSize:"14px",fontWeight:600,color:"#333"},children:"Öne Çıkan"}),valuePropName:"checked",children:e.jsx(Ba,{checkedChildren:"Evet",unCheckedChildren:"Hayır"})})})]}),e.jsx(O.Item,{name:"thumbnail",label:e.jsx("span",{style:{fontSize:"14px",fontWeight:600,color:"#333"},children:"Etkinlik Görseli"}),valuePropName:"fileList",getValueFromEvent:D=>Array.isArray(D)?D:D&&D.fileList,children:e.jsx(Ni,{name:"thumbnail",listType:"picture-card",fileList:p,beforeUpload:()=>!1,onChange:({fileList:D})=>j(D),maxCount:1,style:{borderRadius:"8px"},children:p.length===0&&e.jsxs("div",{style:{padding:"15px",textAlign:"center"},children:[e.jsx(wt,{style:{fontSize:"20px",color:"#999"}}),e.jsx("div",{style:{marginTop:6,color:"#666",fontSize:"12px"},children:"Görsel Yükle"})]})})}),e.jsx(O.Item,{style:{marginTop:"32px",textAlign:"right"},children:e.jsxs(ce,{size:"middle",children:[e.jsx(I,{onClick:()=>y.resetFields(),style:{borderRadius:"8px",padding:"0 24px"},children:"Temizle"}),e.jsx(I,{type:"primary",htmlType:"submit",loading:x,style:{background:"#2563eb",borderColor:"#2563eb",borderRadius:"8px",padding:"0 32px",height:"40px"},children:"Etkinliği Yayınla"})]})})]})})})},"add"),e.jsxs(Co,{tab:e.jsxs("span",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[e.jsx(It,{}),"Etkinlikler",e.jsx("span",{style:{color:"#666",fontWeight:500,fontSize:"12px",background:"#f0f0f0",borderRadius:"12px",padding:"2px 8px",minWidth:"20px",textAlign:"center"},children:Z.total})]}),children:[e.jsxs(ke,{gutter:[16,16],style:{marginBottom:"24px"},children:[e.jsx(N,{xs:24,sm:12,md:10,children:e.jsx(me,{placeholder:"Başlıkta ara...",prefix:e.jsx(ta,{}),value:M,onChange:D=>X(D.target.value),style:{borderRadius:"8px",height:"40px"},allowClear:!0})}),e.jsx(N,{xs:24,sm:12,md:6,children:e.jsxs(Ce,{value:oe,onChange:K,style:{width:"100%",borderRadius:"8px"},placeholder:"Kategori seçin",size:"large",allowClear:!0,dropdownRender:D=>e.jsxs(e.Fragment,{children:[D,e.jsx("div",{style:{padding:"8px 0",borderTop:"1px solid #f0f0f0"},children:e.jsx(I,{type:"text",icon:e.jsx(wt,{}),onClick:Q=>{Q.stopPropagation(),Y()},style:{width:"100%",textAlign:"left",color:"#1890ff",border:"none",boxShadow:"none"},children:"Yeni Kategori Ekle"})})]}),children:[e.jsx(xs,{value:"all",children:"Tüm Kategoriler"}),w.map(D=>e.jsx(xs,{value:D.value,children:e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",width:"100%"},children:[e.jsxs("span",{style:{flex:1},children:[D.name," (",D.event_count||0,")"]}),e.jsx("div",{onClick:Q=>{Q.stopPropagation(),Q.preventDefault(),Y(D)},style:{padding:"4px",borderRadius:"4px",cursor:"pointer",color:"#1890ff",fontSize:"12px",marginLeft:"8px"},title:"Kategoriyi düzenle",children:e.jsx(yt,{})})]})},D.value))]})}),e.jsx(N,{xs:24,sm:12,md:4,children:e.jsx(I,{type:"primary",icon:e.jsx(wt,{}),onClick:()=>Y(),style:{width:"100%",borderRadius:"8px",height:"40px",background:"#52c41a",borderColor:"#52c41a"},children:"Kategori Ekle"})}),e.jsx(N,{xs:24,md:4,children:e.jsxs("div",{style:{display:"flex",justifyContent:"flex-end",alignItems:"center",height:"40px",fontSize:"14px",color:"#666"},children:["Toplam: ",Ee.length," etkinlik"]})})]}),e.jsx(qe,{columns:be,dataSource:Ee,rowKey:"id",loading:r,pagination:{pageSize:10,showSizeChanger:!1,showQuickJumper:!1,responsive:!0},scroll:{x:800},size:"middle",style:{borderRadius:"8px",overflow:"hidden"}})]},"manage")]})}),e.jsx(St,{title:"Etkinlik Düzenle",open:d,onCancel:F,footer:null,width:800,children:e.jsxs(O,{form:y,layout:"vertical",onFinish:ie,children:[e.jsx(O.Item,{name:"title",label:"Etkinlik Başlığı",rules:[{required:!0,message:"Lütfen etkinlik başlığını girin"}],children:e.jsx(me,{})}),e.jsx(O.Item,{style:{textAlign:"right",marginBottom:0},children:e.jsxs(ce,{children:[e.jsx(I,{onClick:F,children:"İptal"}),e.jsx(I,{type:"primary",htmlType:"submit",loading:x,style:{background:"#2563eb",borderColor:"#2563eb",borderRadius:"8px"},children:"Güncelle"})]})})]})}),e.jsx(St,{title:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[G?e.jsx(yt,{}):e.jsx(wt,{}),G?`"${G.name}" Kategorisini Düzenle`:"Yeni Kategori Ekle"]}),open:T,onCancel:()=>{W(!1),J(null),B.resetFields()},footer:null,width:500,children:e.jsxs(O,{form:B,layout:"vertical",onFinish:pe,children:[e.jsx(O.Item,{name:"name",label:"Kategori Adı",rules:[{required:!0,message:"Lütfen kategori adını girin"},{min:2,message:"Kategori adı en az 2 karakter olmalıdır"}],children:e.jsx(me,{placeholder:G?"Kategori adını düzenleyin...":"Örn: Workshop, Konferans, Eğitim",style:{borderRadius:"8px"}})}),G&&e.jsx("div",{style:{padding:"12px",background:"#f6ffed",border:"1px solid #b7eb8f",borderRadius:"8px",marginBottom:"16px"},children:e.jsxs(Zx,{type:"secondary",style:{fontSize:"12px"},children:[e.jsx("strong",{children:"Not:"})," Bu kategori adını değiştirdiğinizde, bu kategoriye ait tüm etkinlikler yeni kategori adıyla güncellenecektir."]})}),e.jsx(O.Item,{style:{textAlign:"right",marginBottom:0},children:e.jsxs(ce,{children:[e.jsx(I,{onClick:()=>{W(!1),J(null),B.resetFields()},children:"İptal"}),e.jsx(I,{type:"primary",htmlType:"submit",style:{background:"#2563eb",borderColor:"#2563eb",borderRadius:"8px"},children:G?"Güncelle":"Ekle"})]})})]})})]})},{Title:an,Text:Na,Paragraph:Oo}=ht,{TextArea:ay}=me,{Option:At}=Ce,{TabPane:sn}=Xe,ir=[{value:"open",label:"Açık",color:"green"},{value:"closed",label:"Kapatıldı",color:"default"}],sy=t=>{const s=ir.find(r=>r.value===t);return s?e.jsx(ae,{color:s.color,children:s.label}):e.jsx(ae,{color:"green",children:"Açık"})},qs=[{value:"account",label:"Hesap Yönetimi",color:"blue"},{value:"technical",label:"Teknik Sorun",color:"red"},{value:"content",label:"İçerik",color:"orange"},{value:"payment",label:"Ödeme",color:"green"},{value:"other",label:"Diğer",color:"purple"}],ry=[{value:"low",label:"Düşük",color:"default"},{value:"medium",label:"Orta",color:"blue"},{value:"high",label:"Yüksek",color:"orange"},{value:"urgent",label:"Acil",color:"red"}],ny=()=>{var t,s,r;const[n,d]=u.useState("open"),[c,h]=u.useState([]),[m,y]=u.useState(!0),[p,j]=u.useState(!1),[x,b]=u.useState(!1),[C,_]=u.useState(null),[w,g]=u.useState([]),[H,U]=u.useState(!1),[T]=O.useForm(),[W]=O.useForm(),[G,J]=u.useState(qs),[B,M]=u.useState(!1),[X,oe]=u.useState(!1),[K,ye]=u.useState(null),[_e]=O.useForm(),{user:Re}=Ct(),{updateUnreadCounts:A}=vr(),F=async($=!0)=>{y(!0);try{const te=await fe.get("https://x.tuberajans.com/backend/akademi/support.php?type=tickets&action=list");te.data.success?h(te.data.data):$&&S.error(te.data.message||"Destek talepleri yüklenirken bir sorun oluştu.")}catch(te){$&&S.error("Destek talepleri yüklenirken bir sorun oluştu.")}finally{y(!1)}};u.useEffect(()=>{F(),ie()},[]);const ie=async()=>{M(!0);try{const $=await fe.get("https://x.tuberajans.com/backend/akademi/support.php?type=categories&action=list");$.data.success?J($.data.data||qs):J(qs)}catch($){J(qs)}finally{M(!1)}},je=async $=>{try{const te=K?"update":"add",Ye=K?{old_value:K.value,new_value:$.value,name:$.name,color:$.color}:{name:$.name,value:$.value,color:$.color},jt=(await fe.post(`https://x.tuberajans.com/backend/akademi/support.php?type=categories&action=${te}`,Ye)).data;jt.success?(S.success(jt.message),oe(!1),ye(null),_e.resetFields(),ie(),F(!1)):S.error(jt.message)}catch(te){S.error("Kategori işlemi sırasında bir hata oluştu.")}},De=async $=>{try{const Ye=(await fe.post("https://x.tuberajans.com/backend/akademi/support.php?type=categories&action=delete",{value:$})).data;Ye.success?(S.success(Ye.message),ie(),F(!1)):S.error(Ye.message)}catch(te){S.error("Kategori silinirken bir hata oluştu.")}},Y=$=>{ye($),_e.setFieldsValue({name:$.label,value:$.value,color:$.color}),oe(!0)},pe=()=>{ye(null),_e.resetFields(),oe(!0)},Oe=async $=>{U(!0);try{const te=await fe.get(`https://x.tuberajans.com/backend/akademi/support.php?type=replies&action=list&ticket_id=${$}`);te.data.success&&g(te.data.data)}catch(te){S.error("Yanıtlar yüklenirken bir sorun oluştu.")}finally{U(!1)}},be=async $=>{_($),j(!0),T.resetFields(),await Oe($.id)},Ee=$=>{_($),W.setFieldsValue({status:$.status,priority:$.priority}),b(!0)},Z=async $=>{if(C)try{const te={ticket_id:C.id,message:$.message,is_admin:1},Ye=await fe.post("https://x.tuberajans.com/backend/akademi/support.php?type=replies&action=add",te);if(Ye.data.success){S.success("Yanıt başarıyla gönderildi"),T.resetFields(),Oe(C.id),F(!1);try{A()}catch(Ve){}}else S.error(Ye.data.message||"Yanıt gönderilirken bir sorun oluştu.")}catch(te){S.error("Yanıt gönderilirken bir sorun oluştu.")}},D=async $=>{if(!C)return;let te=$.status;$.status==="resolved"&&(te="closed");try{const Ye={ticket_id:C.id,status:te,priority:$.priority},Ve=await fe.post("https://x.tuberajans.com/backend/akademi/support.php?type=tickets&action=update_status",Ye);if(Ve.data.success){S.success("Durum başarıyla güncellendi"),b(!1),F(!1);try{A()}catch(jt){}}else S.error(Ve.data.message||"Durum güncellenirken bir sorun oluştu.")}catch(Ye){S.error("Durum güncellenirken bir sorun oluştu.")}},Q=async $=>{try{const te=await fe.post("https://x.tuberajans.com/backend/akademi/support.php?type=tickets&action=close",{ticket_id:$});if(te.data.success){S.success("Talep başarıyla kapatıldı"),F(!1);try{A()}catch(Ye){}p&&(C==null?void 0:C.id)===$&&j(!1)}else S.error(te.data.message||"Talep kapatılırken bir sorun oluştu.")}catch(te){S.error("Talep kapatılırken bir sorun oluştu.")}},k=$=>$==="all"?c:$==="open"?c.filter(te=>te.status==="open"||["in_progress","waiting","resolved"].includes(te.status)):c.filter(te=>te.status===$),ge=[{title:"ID",dataIndex:"id",key:"id",width:70},{title:"Konu",dataIndex:"subject",key:"subject",render:$=>e.jsx("div",{style:{maxWidth:250,whiteSpace:"normal"},children:$})},{title:"Kullanıcı",dataIndex:"username",key:"username",width:120},{title:"Kategori",dataIndex:"category",key:"category",width:130,render:$=>{const te=G.find(Ye=>Ye.value===$)||{label:$,color:"default"};return e.jsx(ae,{color:te.color,children:te.label})}},{title:"Tarih",dataIndex:"created_at",key:"created_at",width:150,render:$=>Fe($).format("DD/MM/YYYY HH:mm")},{title:"Durum",dataIndex:"status",key:"status",width:120,render:$=>sy($)},{title:"İşlemler",key:"actions",width:180,render:($,te)=>e.jsxs(ce,{children:[e.jsx(_t,{title:"Görüntüle & Yanıtla",children:e.jsx(I,{icon:e.jsx(Rt,{}),onClick:()=>be(te),size:"small",type:"primary",style:{backgroundColor:"#1890ff",borderColor:"#1890ff",color:"white"}})}),e.jsx(_t,{title:"Durumu Güncelle",children:e.jsx(I,{icon:e.jsx(Y0,{}),onClick:()=>Ee(te),size:"small"})}),e.jsx(_t,{title:"Kapat",children:e.jsx(it,{title:"Bu talebi kapatmak istediğinizden emin misiniz?",onConfirm:()=>Q(te.id),okText:"Evet",cancelText:"Hayır",disabled:te.status==="closed",okButtonProps:{style:{backgroundColor:"#1890ff",borderColor:"#1890ff",color:"white",fontWeight:"bold"}},cancelButtonProps:{style:{color:"#666",borderColor:"#d9d9d9"}},placement:"topRight",children:e.jsx(I,{icon:e.jsx(rr,{}),size:"small",danger:!0,disabled:te.status==="closed"})})})]})}];return e.jsxs("div",{className:"p-4",children:[e.jsx(q,{children:e.jsxs(Xe,{activeKey:n,onChange:d,children:[e.jsxs(sn,{tab:e.jsx(Ge,{count:k("open").length,offset:[15,0],children:e.jsxs("span",{className:"mr-2",children:[e.jsx(Mg,{})," Açık Talepler"]})}),children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx(an,{level:4,children:"Açık Destek Talepleri"}),e.jsx(ce,{children:e.jsx(I,{icon:e.jsx(Pg,{}),onClick:()=>F(),children:"Yenile"})})]}),e.jsx(qe,{columns:ge,dataSource:k("open"),rowKey:"id",loading:m,pagination:{pageSize:10}})]},"open"),e.jsxs(sn,{tab:e.jsxs(e.Fragment,{children:[e.jsx(rr,{})," Kapatılan Talepler"]}),children:[e.jsx("div",{className:"flex justify-between items-center mb-4",children:e.jsx(an,{level:4,children:"Kapatılan Destek Talepleri"})}),e.jsx(qe,{columns:ge,dataSource:k("closed"),rowKey:"id",loading:m,pagination:{pageSize:10}})]},"closed"),e.jsxs(sn,{tab:e.jsxs(e.Fragment,{children:[e.jsx(Ic,{})," Kategori Yönetimi"]}),children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx(an,{level:4,children:"Talep Kategorileri"}),e.jsx(I,{type:"primary",onClick:pe,style:{backgroundColor:"#1890ff",borderColor:"#1890ff"},children:"Yeni Kategori Ekle"})]}),e.jsx(qe,{columns:[{title:"Kategori Adı",dataIndex:"label",key:"label",render:($,te)=>e.jsx(ae,{color:te.color,children:$})},{title:"Değer",dataIndex:"value",key:"value"},{title:"Renk",dataIndex:"color",key:"color",render:$=>e.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[e.jsx("div",{style:{width:20,height:20,backgroundColor:$==="default"?"#d9d9d9":$,borderRadius:4,marginRight:8,border:"1px solid #ccc"}}),$]})},{title:"İşlemler",key:"actions",render:($,te)=>e.jsxs(ce,{children:[e.jsx(I,{size:"small",onClick:()=>Y(te),children:"Düzenle"}),e.jsx(it,{title:"Bu kategoriyi silmek istediğinizden emin misiniz?",onConfirm:()=>De(te.value),okText:"Evet",cancelText:"Hayır",okButtonProps:{style:{backgroundColor:"#1890ff",borderColor:"#1890ff",color:"white",fontWeight:"bold"}},cancelButtonProps:{style:{color:"#666",borderColor:"#d9d9d9"}},children:e.jsx(I,{size:"small",danger:!0,children:"Sil"})})]})}],dataSource:G,rowKey:"value",loading:B,pagination:{pageSize:10}})]},"categories")]})}),e.jsx(St,{title:C==null?void 0:C.subject,open:p,onCancel:()=>j(!1),footer:null,width:800,children:C&&e.jsxs("div",{children:[e.jsxs("div",{className:"mb-4 p-4 bg-gray-50 rounded-lg border",children:[e.jsxs("div",{className:"mb-2 flex justify-between",children:[e.jsxs(ce,{children:[e.jsx(Na,{strong:!0,children:"Kategori:"}),((t=G.find($=>$.value===C.category))==null?void 0:t.label)||C.category]}),e.jsxs(ce,{children:[e.jsx(Na,{strong:!0,children:"Durum:"}),e.jsx(ae,{color:((s=ir.find($=>$.value===C.status))==null?void 0:s.color)||"default",children:((r=ir.find($=>$.value===C.status))==null?void 0:r.label)||C.status})]})]}),e.jsxs("div",{className:"mb-2 flex justify-between",children:[e.jsxs(ce,{children:[e.jsx(Na,{strong:!0,children:"Kullanıcı:"}),C.username]}),e.jsxs(ce,{children:[e.jsx(Na,{strong:!0,children:"Oluşturulma:"}),Fe(C.created_at).format("DD/MM/YYYY HH:mm")]})]}),e.jsx(Ua,{style:{margin:"12px 0"}}),e.jsx("div",{children:e.jsx(Oo,{children:C.message})})]}),e.jsx(Ua,{children:"Yanıtlar"}),e.jsx("div",{className:"mb-4",children:H?e.jsx("div",{className:"text-center p-4",children:"Yanıtlar yükleniyor..."}):w.length>0?w.map($=>{var te,Ye;return e.jsxs("div",{className:`mb-4 p-4 rounded-lg border ${$.is_admin?"bg-blue-50":"bg-gray-50"}`,children:[e.jsxs("div",{className:"mb-2 flex justify-between",children:[e.jsxs(ce,{children:[e.jsx($a,{style:{backgroundColor:$.is_admin?"#1890ff":"#f56a00"},children:((Ye=(te=$.username)==null?void 0:te.charAt(0))==null?void 0:Ye.toUpperCase())||"U"}),e.jsx(Na,{strong:!0,children:$.is_admin?`${$.username} (Admin)`:$.username})]}),e.jsx(Na,{type:"secondary",children:Fe($.created_at).format("DD/MM/YYYY HH:mm")})]}),e.jsx("div",{className:"ml-8",children:e.jsx(Oo,{children:$.message})})]},$.id)}):e.jsx("div",{className:"text-center p-4 text-gray-500",children:"Henüz yanıt yok"})}),C.status!=="closed"&&e.jsxs(e.Fragment,{children:[e.jsx(Ua,{children:"Yanıt Yaz"}),e.jsxs(O,{form:T,layout:"vertical",onFinish:Z,children:[e.jsx(O.Item,{name:"message",rules:[{required:!0,message:"Lütfen yanıt mesajınızı girin"}],children:e.jsx(ay,{rows:4,placeholder:"Yanıtınızı buraya yazın..."})}),e.jsx(O.Item,{className:"text-right",children:e.jsxs(ce,{children:[e.jsx(I,{onClick:()=>j(!1),children:"İptal"}),e.jsx(I,{type:"primary",icon:e.jsx(da,{}),htmlType:"submit",children:"Yanıt Gönder"}),e.jsx(it,{title:"Talebi çözüldü olarak işaretlemek istediğinizden emin misiniz?",onConfirm:()=>{D({status:"closed",priority:C.priority}),j(!1)},okText:"Evet",cancelText:"Hayır",okButtonProps:{style:{backgroundColor:"#1890ff",borderColor:"#1890ff",color:"white",fontWeight:"bold"}},cancelButtonProps:{style:{color:"#666",borderColor:"#d9d9d9"}},children:e.jsx(I,{type:"default",icon:e.jsx(ea,{}),children:"Çözüldü"})}),e.jsx(it,{title:"Talebi kapatmak istediğinizden emin misiniz?",onConfirm:()=>{Q(C.id),j(!1)},okText:"Evet",cancelText:"Hayır",okButtonProps:{style:{backgroundColor:"#1890ff",borderColor:"#1890ff",color:"white",fontWeight:"bold"}},cancelButtonProps:{style:{color:"#666",borderColor:"#d9d9d9"}},children:e.jsx(I,{danger:!0,icon:e.jsx(rr,{}),children:"Kapat"})})]})})]})]})]})}),e.jsx(St,{title:"Talep Durumunu Güncelle",open:x,onCancel:()=>b(!1),footer:null,width:500,children:e.jsxs(O,{form:W,layout:"vertical",onFinish:D,initialValues:{status:C==null?void 0:C.status,priority:C==null?void 0:C.priority},children:[e.jsx(O.Item,{name:"status",label:"Durum",rules:[{required:!0,message:"Lütfen durum seçin"}],children:e.jsx(Ce,{children:ir.map($=>e.jsx(At,{value:$.value,children:e.jsx(ae,{color:$.color,children:$.label})},$.value))})}),e.jsx(O.Item,{name:"priority",label:"Öncelik",rules:[{required:!0,message:"Lütfen öncelik seçin"}],children:e.jsx(Ce,{children:ry.map($=>e.jsx(At,{value:$.value,children:e.jsx(ae,{color:$.color,children:$.label})},$.value))})}),e.jsx(O.Item,{className:"text-right",children:e.jsxs(ce,{children:[e.jsx(I,{onClick:()=>b(!1),children:"İptal"}),e.jsx(I,{type:"primary",htmlType:"submit",style:{background:"#1890ff",color:"#fff",borderColor:"#1890ff"},children:"Güncelle"})]})})]})}),e.jsx(St,{title:K?"Kategori Düzenle":"Yeni Kategori Ekle",open:X,onCancel:()=>oe(!1),footer:null,width:500,children:e.jsxs(O,{form:_e,layout:"vertical",onFinish:je,children:[e.jsx(O.Item,{name:"name",label:"Kategori Adı",rules:[{required:!0,message:"Lütfen kategori adını girin"}],children:e.jsx(me,{placeholder:"Örn: Teknik Sorun"})}),e.jsx(O.Item,{name:"value",label:"Kategori Değeri",rules:[{required:!0,message:"Lütfen kategori değerini girin"}],children:e.jsx(me,{placeholder:"Örn: technical"})}),e.jsx(O.Item,{name:"color",label:"Renk",rules:[{required:!0,message:"Lütfen renk seçin"}],children:e.jsxs(Ce,{placeholder:"Renk seçin",children:[e.jsx(At,{value:"blue",children:e.jsx(ae,{color:"blue",children:"Mavi"})}),e.jsx(At,{value:"red",children:e.jsx(ae,{color:"red",children:"Kırmızı"})}),e.jsx(At,{value:"green",children:e.jsx(ae,{color:"green",children:"Yeşil"})}),e.jsx(At,{value:"orange",children:e.jsx(ae,{color:"orange",children:"Turuncu"})}),e.jsx(At,{value:"purple",children:e.jsx(ae,{color:"purple",children:"Mor"})}),e.jsx(At,{value:"cyan",children:e.jsx(ae,{color:"cyan",children:"Camgöbeği"})}),e.jsx(At,{value:"magenta",children:e.jsx(ae,{color:"magenta",children:"Magenta"})}),e.jsx(At,{value:"gold",children:e.jsx(ae,{color:"gold",children:"Altın"})}),e.jsx(At,{value:"default",children:e.jsx(ae,{color:"default",children:"Varsayılan"})})]})}),e.jsx(O.Item,{className:"text-right",children:e.jsxs(ce,{children:[e.jsx(I,{onClick:()=>oe(!1),children:"İptal"}),e.jsx(I,{type:"primary",htmlType:"submit",style:{background:"#1890ff",color:"#fff",borderColor:"#1890ff"},children:K?"Güncelle":"Ekle"})]})})]})})]})},{Option:pt}=Ce,iy={admin:"red",editor:"blue",premium:"purple",user:"green"},ly={active:"success",inactive:"default",suspended:"error"},oy=()=>{const[t,s]=u.useState([]),[r,n]=u.useState(!1),[d,c]=u.useState({page:1,limit:10,total:0,total_pages:0}),[h,m]=u.useState(""),[y,p]=u.useState(""),[j,x]=u.useState(""),[b,C]=u.useState(!1),[_,w]=u.useState(!1),[g,H]=u.useState(null),[U]=O.useForm(),T=async(K=1,ye="",_e="",Re="")=>{var A;n(!0);try{const F=new URLSearchParams;ye&&F.append("search",ye),_e&&F.append("status",_e),Re&&F.append("role",Re),F.append("page",K.toString());const ie=`https://x.tuberajans.com/backend/akademi/get_users.php?${F.toString()}`,je=await de.get(ie,{headers:{"Content-Type":"application/json"}});je.data&&je.data.success?(s(je.data.data),je.data.pagination&&c(je.data.pagination)):S.error(((A=je.data)==null?void 0:A.message)||"Kullanıcılar yüklenirken bir hata oluştu.")}catch(F){S.error("Kullanıcılar yüklenirken bir sorun oluştu.")}finally{n(!1)}};u.useEffect(()=>{T(1,"","","")},[]),u.useEffect(()=>{if(h===""&&y===""&&j==="")return;const K=setTimeout(()=>{T(1,h,y,j)},500);return()=>clearTimeout(K)},[h,y,j]);const W=()=>{m(""),p(""),x(""),T(1,"","","")},G=K=>{T(K.current,h,y,j)},J=()=>{w(!1),H(null),U.resetFields(),C(!0)},B=K=>{w(!0),H(K),U.setFieldsValue({username:K.username,email:K.email,first_name:K.first_name,last_name:K.last_name,role:K.role,status:K.status,phone:K.phone,password:""}),C(!0)},M=async()=>{try{const K=await U.validateFields();if(n(!0),_&&g){const ye=await de.put(`/api/users.php?id=${g.id}`,K);ye.data.success?(S.success("Kullanıcı başarıyla güncellendi"),C(!1),T()):S.error(ye.data.message||"Kullanıcı güncellenirken bir hata oluştu")}else{const ye=await de.post("/api/users.php",K);ye.data.success?(S.success("Kullanıcı başarıyla oluşturuldu"),C(!1),T()):S.error(ye.data.message||"Kullanıcı oluşturulurken bir hata oluştu")}}catch(K){}finally{n(!1)}},X=async K=>{n(!0);try{const ye=await de.delete(`/api/users.php?id=${K}`);ye.data.success?(S.success("Kullanıcı başarıyla silindi"),T()):S.error(ye.data.message||"Kullanıcı silinirken bir hata oluştu")}catch(ye){S.error("Kullanıcı silinirken bir hata oluştu")}finally{n(!1)}},oe=[{title:"ID",dataIndex:"id",key:"id",width:70},{title:"Kullanıcı Adı",dataIndex:"username",key:"username"},{title:"E-posta",dataIndex:"email",key:"email"},{title:"Ad Soyad",key:"fullname",render:K=>`${K.first_name||""} ${K.last_name||""}`.trim()||"-"},{title:"Rol",dataIndex:"role",key:"role",render:K=>e.jsx(ae,{color:iy[K]||"default",children:K.toUpperCase()})},{title:"Durum",dataIndex:"status",key:"status",render:K=>e.jsx(ae,{color:ly[K]||"default",children:K.toUpperCase()})},{title:"Kayıt Tarihi",dataIndex:"created_at",key:"created_at",render:K=>new Date(K).toLocaleDateString("tr-TR")},{title:"İşlemler",key:"actions",render:K=>e.jsxs(ce,{children:[e.jsx(I,{type:"primary",icon:e.jsx(yt,{style:{color:"#fff"}}),onClick:()=>B(K),size:"small",style:{backgroundColor:"#1890ff",borderColor:"#1890ff",color:"#fff"}}),e.jsx(it,{title:"Bu kullanıcıyı silmek istediğinize emin misiniz?",onConfirm:()=>X(K.id),okText:"Evet",cancelText:"Hayır",children:e.jsx(I,{type:"primary",danger:!0,icon:e.jsx(Et,{}),size:"small"})})]})}];return e.jsx("div",{children:e.jsxs(q,{children:[e.jsx("div",{style:{marginBottom:16},children:e.jsxs(ce,{wrap:!0,children:[e.jsx(me,{placeholder:"Kullanıcı adı, e-posta veya ad soyad ara",value:h,onChange:K=>m(K.target.value),style:{width:300},prefix:e.jsx(ta,{})}),e.jsxs(Ce,{placeholder:"Tümü",style:{width:150},value:j||void 0,onChange:K=>{x(K||"")},allowClear:!0,children:[e.jsx(pt,{value:"admin",children:"Admin"}),e.jsx(pt,{value:"editor",children:"Editör"}),e.jsx(pt,{value:"premium",children:"Premium"}),e.jsx(pt,{value:"user",children:"Kullanıcı"})]}),e.jsxs(Ce,{placeholder:"Tümü",style:{width:150},value:y||void 0,onChange:K=>{p(K||"")},allowClear:!0,children:[e.jsx(pt,{value:"active",children:"Aktif"}),e.jsx(pt,{value:"inactive",children:"Pasif"}),e.jsx(pt,{value:"suspended",children:"Askıya Alınmış"})]}),e.jsx(I,{onClick:W,icon:e.jsx(qi,{}),children:"Sıfırla"}),e.jsx(I,{type:"primary",onClick:J,icon:e.jsx(wt,{}),style:{marginLeft:"auto",backgroundColor:"#1890ff",borderColor:"#1890ff",color:"#fff"},children:"Yeni Kullanıcı"})]})}),e.jsx(qe,{columns:oe,dataSource:t,rowKey:"id",loading:r,pagination:{current:d.page,pageSize:d.limit,total:d.total,showSizeChanger:!0,pageSizeOptions:["10","20","50"]},onChange:G}),e.jsx(St,{title:_?"Kullanıcı Düzenle":"Yeni Kullanıcı Ekle",open:b,onOk:M,onCancel:()=>C(!1),confirmLoading:r,okText:_?"Güncelle":"Kaydet",cancelText:"İptal",width:600,children:e.jsxs(O,{form:U,layout:"vertical",children:[e.jsx(O.Item,{name:"username",label:"Kullanıcı Adı",rules:[{required:!0,message:"Lütfen kullanıcı adı girin!"}],children:e.jsx(me,{})}),e.jsx(O.Item,{name:"email",label:"E-posta",rules:[{required:!0,message:"Lütfen e-posta girin!"},{type:"email",message:"Geçerli bir e-posta adresi girin!"}],children:e.jsx(me,{})}),e.jsx(O.Item,{name:"password",label:"Şifre",rules:[{required:!_,message:"Lütfen şifre girin!"}],children:e.jsx(me.Password,{placeholder:_?"Değiştirmek için yeni şifre girin":"Şifre girin"})}),e.jsxs("div",{style:{display:"flex",gap:16},children:[e.jsx(O.Item,{name:"first_name",label:"Ad",style:{flex:1},children:e.jsx(me,{})}),e.jsx(O.Item,{name:"last_name",label:"Soyad",style:{flex:1},children:e.jsx(me,{})})]}),e.jsxs("div",{style:{display:"flex",gap:16},children:[e.jsx(O.Item,{name:"role",label:"Rol",rules:[{required:!0,message:"Lütfen bir rol seçin!"}],style:{flex:1},initialValue:"user",children:e.jsxs(Ce,{children:[e.jsx(pt,{value:"admin",children:"Admin"}),e.jsx(pt,{value:"editor",children:"Editör"}),e.jsx(pt,{value:"premium",children:"Premium"}),e.jsx(pt,{value:"user",children:"Kullanıcı"})]})}),e.jsx(O.Item,{name:"status",label:"Durum",rules:[{required:!0,message:"Lütfen bir durum seçin!"}],style:{flex:1},initialValue:"active",children:e.jsxs(Ce,{children:[e.jsx(pt,{value:"active",children:"Aktif"}),e.jsx(pt,{value:"inactive",children:"Pasif"}),e.jsx(pt,{value:"suspended",children:"Askıya Alınmış"})]})})]}),e.jsx(O.Item,{name:"phone",label:"Telefon",children:e.jsx(me,{})}),e.jsx(O.Item,{name:"bio",label:"Hakkında",children:e.jsx(me.TextArea,{rows:3})})]})})]})})},{Title:Js,Text:U2,Paragraph:rn}=ht,{TextArea:nn}=me,{Option:ya}=Ce,cy=()=>{var t,s,r,n;const[d,c]=u.useState("chat"),[h,m]=u.useState([]),[y,p]=u.useState([]),[j,x]=u.useState([]),[b,C]=u.useState([]),[_,w]=u.useState(!1),[g,H]=u.useState(!1),[U,T]=u.useState(null),[W,G]=u.useState(""),[J,B]=u.useState([]),[M,X]=u.useState(!1),[oe]=O.useForm(),K="/backend/akademi";u.useEffect(()=>{ye(),_e(),Re(),A()},[]),u.useEffect(()=>{},[h,U]);const ye=async()=>{try{const k=await de.get(`${K}/ai_service.php?action=models`);if(k.data.success){m(k.data.data);const ge=k.data.data.find($=>$.is_default);ge&&T(ge.id)}}catch(k){}},_e=async()=>{try{const k=await de.get(`${K}/ai_service.php?action=settings`);k.data.success&&p(k.data.data)}catch(k){}},Re=async()=>{try{const k=await de.get(`${K}/ai_service.php?action=conversations&limit=20`);k.data.success&&x(k.data.data)}catch(k){}},A=async()=>{try{const k=await de.get(`${K}/ai_service.php?action=documents`);k.data.success&&C(k.data.data)}catch(k){}},F=async()=>{if(!W.trim()||!U){S.warning("Lütfen bir mesaj yazın ve model seçin");return}H(!0);const k={role:"user",content:W,timestamp:new Date};B(ge=>[...ge,k]);try{const ge=await de.post(`${K}/ai_service.php?action=generate`,{prompt:W,model_id:U,session_id:"akademi_chat_"+Date.now()});if(ge.data.success){const $={role:"assistant",content:ge.data.data.response,model:ge.data.data.model,tokens:ge.data.data.tokens_used,responseTime:ge.data.data.response_time,timestamp:new Date};B(te=>[...te,$]),G(""),Re()}else S.error("AI yanıtı alınamadı: "+ge.data.message)}catch(ge){S.error("Mesaj gönderilirken hata oluştu")}finally{H(!1)}},ie=async(k,ge,$)=>{try{await de.put(`${K}/ai_service.php?action=models`,{id:k,[ge]:$}),S.success("Model güncellendi"),ye()}catch(te){S.error("Model güncellenirken hata oluştu")}},je=async k=>{try{const ge=Object.keys(k).map($=>({key:$,value:k[$]}));await de.put(`${K}/ai_service.php?action=settings`,{settings:ge}),S.success("Ayarlar kaydedildi"),X(!1),_e()}catch(ge){S.error("Ayarlar kaydedilirken hata oluştu")}},De=async k=>{try{await de.post(`${K}/ai_service.php?action=documents`,k),S.success("Doküman eklendi"),oe.resetFields(),A()}catch(ge){S.error("Doküman eklenirken hata oluştu")}},Y=k=>{switch(k){case"openai":return"#10a37f";case"anthropic":return"#d97706";case"google":return"#4285f4";default:return"#666"}},pe=k=>{switch(k){case"openai":return"🤖";case"anthropic":return"🧠";case"google":return"🔍";default:return"⚡"}},Oe=e.jsxs(ke,{gutter:[24,24],children:[e.jsx(N,{span:16,children:e.jsxs(q,{title:e.jsxs(ce,{children:[e.jsx(ua,{}),e.jsx("span",{children:"AI Asistan Chat"}),U&&h.length>0&&e.jsxs(ae,{color:Y(((t=h.find(k=>k.id===U))==null?void 0:t.provider)||""),children:[pe(((s=h.find(k=>k.id===U))==null?void 0:s.provider)||"")," ",((r=h.find(k=>k.id===U))==null?void 0:r.display_name)||"Model Bulunamadı"]})]}),extra:e.jsx(I,{icon:e.jsx(_s,{}),onClick:()=>X(!0),type:"text",children:"Ayarlar"}),style:{height:"600px"},children:[e.jsxs("div",{style:{height:"450px",overflowY:"auto",marginBottom:"16px",padding:"16px",backgroundColor:"#fafafa",borderRadius:"8px"},children:[J.length===0?e.jsxs("div",{style:{textAlign:"center",padding:"60px 0",color:"#999"},children:[e.jsx(ha,{style:{fontSize:"48px",marginBottom:"16px"}}),e.jsx("div",{children:"AI asistanınızla konuşmaya başlayın"}),e.jsx("div",{style:{fontSize:"14px",marginTop:"8px"},children:"TikTok stratejileri, içerik üretimi ve sosyal medya pazarlama hakkında sorular sorabilirsiniz"})]}):J.map((k,ge)=>e.jsx("div",{style:{marginBottom:"16px"},children:e.jsx("div",{style:{display:"flex",justifyContent:k.role==="user"?"flex-end":"flex-start",marginBottom:"8px"},children:e.jsxs("div",{style:{maxWidth:"70%",padding:"12px 16px",borderRadius:"12px",backgroundColor:k.role==="user"?"#1890ff":"#fff",color:k.role==="user"?"#fff":"#000",border:k.role==="assistant"?"1px solid #d9d9d9":"none",boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},children:[e.jsx("div",{style:{whiteSpace:"pre-wrap"},children:k.content}),k.role==="assistant"&&e.jsx("div",{style:{marginTop:"8px",fontSize:"12px",color:"#666",borderTop:"1px solid #f0f0f0",paddingTop:"8px"},children:e.jsxs(ce,{size:"small",children:[e.jsxs("span",{children:["🤖 ",k.model]}),k.tokens&&e.jsxs("span",{children:["📊 ",k.tokens," token"]}),k.responseTime&&e.jsxs("span",{children:["⏱️ ",k.responseTime,"s"]})]})})]})})},ge)),g&&e.jsxs("div",{style:{textAlign:"center",padding:"20px"},children:[e.jsx(Ta,{size:"large"}),e.jsx("div",{style:{marginTop:"8px",color:"#666"},children:"AI yanıt oluşturuyor..."})]})]}),e.jsxs("div",{style:{display:"flex",gap:"8px"},children:[e.jsx(nn,{value:W,onChange:k=>G(k.target.value),placeholder:"Mesajınızı yazın... (TikTok stratejileri, içerik üretimi, sosyal medya pazarlama hakkında sorabilirsiniz)",autoSize:{minRows:2,maxRows:4},onPressEnter:k=>{k.shiftKey||(k.preventDefault(),F())},disabled:g}),e.jsx(I,{type:"primary",icon:e.jsx(Lc,{}),onClick:F,loading:g,disabled:!W.trim()||!U,style:{height:"auto"},children:"Gönder"})]})]})}),e.jsx(N,{span:8,children:e.jsxs(ce,{direction:"vertical",style:{width:"100%"},size:"large",children:[e.jsx(q,{title:"Model Seçimi",size:"small",children:e.jsx(Ce,{value:U,onChange:T,style:{width:"100%"},placeholder:"AI Model Seçin",showSearch:!0,optionFilterProp:"children",children:h.filter(k=>k.is_active).map(k=>e.jsxs(ya,{value:k.id,children:[pe(k.provider)," ",k.display_name,k.is_default&&" (Varsayılan)"]},k.id))})}),e.jsx(q,{title:"İstatistikler",size:"small",children:e.jsxs(ke,{gutter:16,children:[e.jsx(N,{span:12,children:e.jsx(Te,{title:"Toplam Konuşma",value:j.length,prefix:e.jsx(ua,{})})}),e.jsx(N,{span:12,children:e.jsx(Te,{title:"Aktif Model",value:h.filter(k=>k.is_active).length,prefix:e.jsx(Vs,{})})})]})}),e.jsx(q,{title:"Son Konuşmalar",size:"small",children:e.jsx("div",{style:{maxHeight:"200px",overflowY:"auto"},children:j.slice(0,5).map(k=>e.jsxs("div",{style:{marginBottom:"12px",padding:"8px",backgroundColor:"#f9f9f9",borderRadius:"6px"},children:[e.jsx("div",{style:{fontSize:"12px",color:"#666",marginBottom:"4px"},children:new Date(k.created_at).toLocaleString("tr-TR")}),e.jsxs("div",{style:{fontSize:"13px",fontWeight:500,marginBottom:"4px"},children:[k.prompt.substring(0,50),"..."]}),e.jsx("div",{style:{fontSize:"12px",color:"#999"},children:e.jsxs(ce,{size:"small",children:[e.jsx("span",{children:k.model_name}),e.jsxs("span",{children:[k.tokens_used," token"]}),e.jsxs("span",{children:[k.response_time,"s"]})]})})]},k.id))})})]})})]}),be=e.jsxs("div",{children:[e.jsxs("div",{style:{marginBottom:24},children:[e.jsx(Js,{level:4,children:"AI Model Yönetimi"}),e.jsx(rn,{children:"Farklı AI sağlayıcılarından modelleri yönetin. Modelleri aktif/pasif yapabilir, varsayılan model belirleyebilirsiniz."})]}),e.jsx(ke,{gutter:[16,16],children:h.map(k=>e.jsx(N,{span:8,children:e.jsxs(q,{size:"small",title:e.jsxs(ce,{children:[e.jsx("span",{style:{fontSize:"18px"},children:pe(k.provider)}),e.jsx("span",{children:k.display_name}),k.is_default&&e.jsx(Ge,{status:"success",text:"Varsayılan"})]}),extra:e.jsx(Ba,{checked:k.is_active,onChange:ge=>ie(k.id,"is_active",ge),size:"small"}),style:{borderColor:Y(k.provider),borderWidth:k.is_default?2:1},children:[e.jsxs("div",{style:{marginBottom:12},children:[e.jsx(ae,{color:Y(k.provider),style:{marginBottom:8},children:k.provider.toUpperCase()}),e.jsx("div",{style:{fontSize:"12px",color:"#666",marginBottom:8},children:k.description}),e.jsx("div",{style:{fontSize:"12px"},children:e.jsxs(ce,{direction:"vertical",size:"small",style:{width:"100%"},children:[e.jsxs("div",{children:[e.jsx("strong",{children:"Model ID:"})," ",k.model_id]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Max Tokens:"})," ",k.max_tokens.toLocaleString()]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Maliyet:"})," $",k.cost_per_1k_tokens,"/1K token"]})]})})]}),e.jsx("div",{style:{display:"flex",gap:8},children:e.jsx(I,{size:"small",type:k.is_default?"default":"primary",onClick:()=>ie(k.id,"is_default",!k.is_default),disabled:k.is_default,style:{flex:1},children:k.is_default?"Varsayılan":"Varsayılan Yap"})})]})},k.id))}),e.jsx(Ua,{}),e.jsxs(ke,{gutter:[24,24],children:[e.jsx(N,{span:12,children:e.jsx(q,{title:"Provider İstatistikleri",size:"small",children:e.jsx("div",{children:["openai","anthropic","google"].map(k=>{const ge=h.filter(te=>te.provider===k),$=ge.filter(te=>te.is_active).length;return e.jsxs("div",{style:{marginBottom:16},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsxs(ce,{children:[e.jsx("span",{style:{fontSize:"16px"},children:pe(k)}),e.jsx("span",{style:{fontWeight:500,textTransform:"capitalize"},children:k})]}),e.jsxs(ae,{color:Y(k),children:[$,"/",ge.length," aktif"]})]}),e.jsx(ys,{percent:ge.length>0?$/ge.length*100:0,strokeColor:Y(k),size:"small",showInfo:!1})]},k)})})})}),e.jsx(N,{span:12,children:e.jsxs(q,{title:"Kullanım İstatistikleri",size:"small",children:[e.jsxs(ke,{gutter:16,children:[e.jsx(N,{span:12,children:e.jsx(Te,{title:"Toplam Model",value:h.length,prefix:e.jsx(Vs,{})})}),e.jsx(N,{span:12,children:e.jsx(Te,{title:"Aktif Model",value:h.filter(k=>k.is_active).length,prefix:e.jsx(Zr,{}),valueStyle:{color:"#3f8600"}})})]}),e.jsx(Ua,{}),e.jsxs("div",{style:{fontSize:"12px",color:"#666"},children:[e.jsxs("div",{style:{marginBottom:8},children:[e.jsx("strong",{children:"Varsayılan Model:"})," ",((n=h.find(k=>k.is_default))==null?void 0:n.display_name)||"Seçilmemiş"]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Son Güncelleme:"})," ",new Date().toLocaleString("tr-TR")]})]})]})})]})]}),Ee=e.jsxs("div",{children:[e.jsxs("div",{style:{marginBottom:24},children:[e.jsx(Js,{level:4,children:"Eğitim Dokümanları"}),e.jsx(rn,{children:"AI modelinin eğitimi için kullanılacak dokümanları yönetin. Bu dokümanlar AI'ın daha iyi yanıtlar vermesini sağlar."})]}),e.jsxs(q,{style:{marginBottom:24},children:[e.jsx(Js,{level:5,style:{marginBottom:16},children:"Yeni Doküman Ekle"}),e.jsxs(O,{form:oe,layout:"vertical",onFinish:De,children:[e.jsxs(ke,{gutter:16,children:[e.jsx(N,{span:12,children:e.jsx(O.Item,{label:"Doküman Başlığı",name:"title",rules:[{required:!0,message:"Başlık gerekli"}],children:e.jsx(me,{placeholder:"Doküman başlığını girin"})})}),e.jsx(N,{span:12,children:e.jsx(O.Item,{label:"Kategori",name:"category",rules:[{required:!0,message:"Kategori seçin"}],children:e.jsxs(Ce,{placeholder:"Kategori seçin",children:[e.jsx(ya,{value:"tiktok_stratejileri",children:"TikTok Stratejileri"}),e.jsx(ya,{value:"icerik_uretimi",children:"İçerik Üretimi"}),e.jsx(ya,{value:"canli_yayin",children:"Canlı Yayın"}),e.jsx(ya,{value:"sosyal_medya",children:"Sosyal Medya Pazarlama"}),e.jsx(ya,{value:"genel",children:"Genel Bilgiler"})]})})})]}),e.jsx(O.Item,{label:"Doküman İçeriği",name:"content",rules:[{required:!0,message:"İçerik gerekli"}],children:e.jsx(nn,{rows:8,placeholder:"Doküman içeriğini buraya yazın. Bu bilgiler AI'ın eğitiminde kullanılacak..."})}),e.jsx("div",{style:{display:"flex",justifyContent:"flex-end"},children:e.jsx(I,{type:"primary",htmlType:"submit",icon:e.jsx(wt,{}),className:"dokuman-ekle-btn",children:"Doküman Ekle"})})]})]}),e.jsxs(q,{title:"Mevcut Dokümanlar",children:[e.jsx("div",{style:{marginBottom:16},children:e.jsxs(ke,{gutter:16,children:[e.jsx(N,{span:6,children:e.jsx(Te,{title:"Toplam Doküman",value:b.length,prefix:e.jsx(Xt,{})})}),e.jsx(N,{span:6,children:e.jsx(Te,{title:"Aktif Doküman",value:b.filter(k=>k.status==="active").length,prefix:e.jsx(Zr,{}),valueStyle:{color:"#3f8600"}})})]})}),e.jsx("div",{style:{maxHeight:"400px",overflowY:"auto"},children:b.length===0?e.jsxs("div",{style:{textAlign:"center",padding:"40px 0",color:"#999"},children:[e.jsx(Xt,{style:{fontSize:"48px",marginBottom:"16px"}}),e.jsx("div",{children:"Henüz doküman eklenmemiş"}),e.jsx("div",{style:{fontSize:"14px",marginTop:"8px"},children:"Yukarıdaki formu kullanarak ilk dokümanınızı ekleyin"})]}):b.map(k=>e.jsx(q,{size:"small",style:{marginBottom:12},children:e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start"},children:[e.jsxs("div",{style:{flex:1},children:[e.jsx("div",{style:{fontWeight:500,marginBottom:8},children:k.title}),e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(ae,{color:"blue",children:k.category}),e.jsx(ae,{color:k.status==="active"?"success":"default",children:k.status==="active"?"Aktif":"Pasif"})]}),e.jsxs("div",{style:{fontSize:"12px",color:"#666",marginBottom:8},children:[k.content.substring(0,150),"..."]}),e.jsxs("div",{style:{fontSize:"12px",color:"#999"},children:["Oluşturulma: ",new Date(k.created_at).toLocaleString("tr-TR")]})]}),e.jsx("div",{style:{marginLeft:16},children:e.jsxs(ce,{children:[e.jsx(I,{size:"small",icon:e.jsx(yt,{})}),e.jsx(I,{size:"small",danger:!0,icon:e.jsx(Et,{})})]})})]})},k.id))})]})]}),Z=e.jsxs("div",{children:[e.jsxs("div",{style:{marginBottom:24},children:[e.jsx(Js,{level:4,children:"Konuşma Geçmişi"}),e.jsx(rn,{children:"AI asistanı ile yapılan tüm konuşmaları görüntüleyin ve analiz edin."})]}),e.jsxs(ke,{gutter:[16,16],style:{marginBottom:24},children:[e.jsx(N,{span:6,children:e.jsx(q,{size:"small",children:e.jsx(Te,{title:"Toplam Konuşma",value:j.length,prefix:e.jsx(ua,{})})})}),e.jsx(N,{span:6,children:e.jsx(q,{size:"small",children:e.jsx(Te,{title:"Başarılı Yanıt",value:j.filter(k=>k.status==="completed").length,prefix:e.jsx(Zr,{}),valueStyle:{color:"#3f8600"}})})}),e.jsx(N,{span:6,children:e.jsx(q,{size:"small",children:e.jsx(Te,{title:"Toplam Token",value:j.reduce((k,ge)=>k+(ge.tokens_used||0),0),prefix:e.jsx(Vs,{})})})}),e.jsx(N,{span:6,children:e.jsx(q,{size:"small",children:e.jsx(Te,{title:"Ort. Yanıt Süresi",value:j.length>0?(j.reduce((k,ge)=>k+(ge.response_time||0),0)/j.length).toFixed(2):0,suffix:"s",prefix:e.jsx(Vi,{})})})})]}),e.jsx(q,{children:e.jsx("div",{style:{maxHeight:"500px",overflowY:"auto"},children:j.length===0?e.jsxs("div",{style:{textAlign:"center",padding:"60px 0",color:"#999"},children:[e.jsx(Jn,{style:{fontSize:"48px",marginBottom:"16px"}}),e.jsx("div",{children:"Henüz konuşma geçmişi bulunmuyor"}),e.jsx("div",{style:{fontSize:"14px",marginTop:"8px"},children:"Chat sekmesinden AI ile konuşmaya başlayın"})]}):j.map(k=>e.jsx(q,{size:"small",style:{marginBottom:16},children:e.jsxs("div",{style:{marginBottom:12},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:8},children:[e.jsxs(ce,{children:[e.jsxs(ae,{color:"blue",children:["#",k.id]}),e.jsx(ae,{color:k.status==="completed"?"success":k.status==="failed"?"error":"processing",children:k.status==="completed"?"Tamamlandı":k.status==="failed"?"Hata":"İşleniyor"}),e.jsx("span",{style:{fontSize:"12px",color:"#666"},children:new Date(k.created_at).toLocaleString("tr-TR")})]}),e.jsxs(ce,{children:[e.jsx(_t,{title:"Model",children:e.jsx(ae,{color:"purple",children:k.model_name})}),e.jsx(_t,{title:"Token Kullanımı",children:e.jsxs(ae,{children:[k.tokens_used," token"]})}),e.jsx(_t,{title:"Yanıt Süresi",children:e.jsxs(ae,{children:[k.response_time,"s"]})})]})]}),e.jsxs("div",{style:{marginBottom:12},children:[e.jsx("div",{style:{fontWeight:500,marginBottom:8,color:"#1890ff"},children:"👤 Kullanıcı:"}),e.jsx("div",{style:{padding:"12px",backgroundColor:"#f0f8ff",borderRadius:"8px",borderLeft:"4px solid #1890ff"},children:k.prompt})]}),k.response&&e.jsxs("div",{children:[e.jsx("div",{style:{fontWeight:500,marginBottom:8,color:"#52c41a"},children:"🤖 AI Asistan:"}),e.jsx("div",{style:{padding:"12px",backgroundColor:"#f6ffed",borderRadius:"8px",borderLeft:"4px solid #52c41a",whiteSpace:"pre-wrap"},children:k.response})]})]})},k.id))})})]}),D=e.jsx(St,{title:e.jsxs(ce,{children:[e.jsx(_s,{}),e.jsx("span",{children:"AI Ayarları"})]}),open:M,onCancel:()=>X(!1),footer:null,width:800,children:e.jsxs(O,{layout:"vertical",onFinish:je,initialValues:y.reduce((k,ge)=>(k[ge.setting_key]=ge.setting_value,k),{}),children:[e.jsx(Zt,{message:"API Anahtarları",description:"AI sağlayıcılarının API anahtarlarını buradan yönetebilirsiniz. Anahtarlar güvenli bir şekilde şifrelenerek saklanır.",type:"info",style:{marginBottom:24}}),e.jsxs(ke,{gutter:16,children:[e.jsx(N,{span:12,children:e.jsx(O.Item,{label:"OpenAI API Anahtarı",name:"openai_api_key",tooltip:"OpenAI GPT modellerini kullanmak için gerekli",children:e.jsx(me.Password,{placeholder:"sk-..."})})}),e.jsx(N,{span:12,children:e.jsx(O.Item,{label:"Anthropic API Anahtarı",name:"anthropic_api_key",tooltip:"Claude modellerini kullanmak için gerekli",children:e.jsx(me.Password,{placeholder:"sk-ant-..."})})})]}),e.jsxs(ke,{gutter:16,children:[e.jsx(N,{span:12,children:e.jsx(O.Item,{label:"Google AI API Anahtarı",name:"google_api_key",tooltip:"Gemini modellerini kullanmak için gerekli",children:e.jsx(me.Password,{placeholder:"AIza..."})})}),e.jsx(N,{span:12,children:e.jsx(O.Item,{label:"Varsayılan Model ID",name:"default_model_id",tooltip:"Sistem başlangıcında kullanılacak model",children:e.jsx(Ce,{children:h.map(k=>e.jsxs(ya,{value:k.id.toString(),children:[pe(k.provider)," ",k.display_name]},k.id))})})})]}),e.jsx(O.Item,{label:"Sistem Prompt",name:"system_prompt",tooltip:"AI'ın davranışını belirleyen temel talimat",children:e.jsx(nn,{rows:4,placeholder:"Sen Tuber Akademi'nin AI asistanısın..."})}),e.jsx(O.Item,{label:"Maksimum Konuşma Geçmişi",name:"max_conversation_history",tooltip:"Saklanacak maksimum konuşma sayısı",children:e.jsx(me,{type:"number",min:1,max:1e3})}),e.jsxs("div",{style:{display:"flex",justifyContent:"flex-end",gap:8},children:[e.jsx(I,{onClick:()=>X(!1),children:"İptal"}),e.jsx(I,{type:"primary",htmlType:"submit",className:"ai-kaydet-btn",children:"Kaydet"})]})]})}),Q=[{key:"chat",label:e.jsxs("span",{children:[e.jsx(ua,{}),"AI Chat"]}),children:Oe},{key:"models",label:e.jsxs("span",{children:[e.jsx(Vs,{}),"Model Yönetimi"]}),children:be},{key:"documents",label:e.jsxs("span",{children:[e.jsx(Xt,{}),"Dokümanlar"]}),children:Ee},{key:"history",label:e.jsxs("span",{children:[e.jsx(Jn,{}),"Konuşma Geçmişi"]}),children:Z}];return e.jsxs(e.Fragment,{children:[e.jsx(q,{children:e.jsx(Xe,{activeKey:d,items:Q,onChange:c,type:"card",size:"large",tabBarStyle:{marginBottom:24}})}),D]})},{Title:dy,Text:gt,Paragraph:zo}=ht,uy=()=>{const[t,s]=u.useState(!1),[r,n]=u.useState({total_users:0,total_courses:0,upcoming_events_count:0,open_tickets:0}),[d,c]=u.useState([]),[h,m]=u.useState([]),[y,p]=u.useState([]),[j,x]=u.useState([]),b=_a();u.useEffect(()=>{C()},[]);const C=async()=>{s(!0);try{const g=await de.get("/backend/akademi/dashboard-metrics.php");g.data.success?(n(g.data.stats),c(g.data.recent_users||[]),m(g.data.recent_announcements||[]),p(g.data.upcoming_events||[]),x(g.data.recent_tickets||[])):(n({total_users:0,total_courses:0,upcoming_events_count:0,open_tickets:0}),c([]),m([]),p([]),x([]))}catch(g){n({total_users:0,total_courses:0,upcoming_events_count:0,open_tickets:0}),c([]),m([]),p([]),x([])}finally{s(!1)}},_=g=>{switch(g.toLowerCase()){case"high":case"yüksek":return"#ff4d4f";case"medium":case"orta":return"#faad14";case"low":case"düşük":return"#52c41a";default:return"#d9d9d9"}},w=g=>{switch(g.toLowerCase()){case"open":case"açık":return"#ff4d4f";case"pending":case"beklemede":return"#faad14";case"closed":case"kapalı":return"#52c41a";default:return"#d9d9d9"}};return e.jsxs("div",{className:"akademi-dashboard",style:{padding:"24px",background:"#fff",minHeight:"100vh"},children:[e.jsx("div",{className:"dashboard-header",style:{marginBottom:24},children:e.jsx(ke,{align:"middle",children:e.jsxs(N,{children:[e.jsxs(dy,{level:2,style:{margin:0,color:"#1890ff"},children:[e.jsx(yx,{style:{marginRight:8}}),"Akademi Dashboard"]}),e.jsx(gt,{type:"secondary",children:"Akademi platformunuzun genel durumu ve istatistikleri"})]})})}),e.jsxs(ke,{gutter:[24,24],style:{marginBottom:32},children:[e.jsx(N,{xs:24,sm:12,lg:6,children:e.jsxs(q,{loading:t,className:"stat-card",style:{borderLeft:"4px solid #1890ff",boxShadow:"0 4px 12px rgba(0,0,0,0.1)",cursor:"pointer",height:"140px",borderRadius:"8px"},onClick:()=>b("/akademi/kullanicilar"),hoverable:!0,bodyStyle:{padding:"20px"},children:[e.jsx(Te,{title:"Toplam Kullanıcı",value:r.total_users,prefix:e.jsx(ot,{style:{color:"#1890ff",fontSize:"20px"}}),valueStyle:{color:"#1890ff",fontSize:"28px",fontWeight:"bold"}}),e.jsx("div",{style:{marginTop:12},children:e.jsxs(gt,{type:"secondary",style:{fontSize:"12px"},children:[e.jsx(ot,{style:{color:"#52c41a",marginRight:4}}),"Kayıtlı kullanıcılar"]})})]})}),e.jsx(N,{xs:24,sm:12,lg:6,children:e.jsxs(q,{loading:t,style:{borderLeft:"4px solid #52c41a",boxShadow:"0 4px 12px rgba(0,0,0,0.1)",cursor:"pointer",height:"140px",borderRadius:"8px"},onClick:()=>b("/akademi/egitimler"),hoverable:!0,bodyStyle:{padding:"20px"},children:[e.jsx(Te,{title:"Toplam Eğitim",value:r.total_courses,prefix:e.jsx(wr,{style:{color:"#52c41a",fontSize:"20px"}}),valueStyle:{color:"#52c41a",fontSize:"28px",fontWeight:"bold"}}),e.jsx("div",{style:{marginTop:12},children:e.jsxs(gt,{type:"secondary",style:{fontSize:"12px"},children:[e.jsx(Rt,{style:{color:"#1890ff",marginRight:4}}),"Aktif eğitimler"]})})]})}),e.jsx(N,{xs:24,sm:12,lg:6,children:e.jsxs(q,{loading:t,style:{borderLeft:"4px solid #722ed1",boxShadow:"0 4px 12px rgba(0,0,0,0.1)",cursor:"pointer",height:"140px",borderRadius:"8px"},onClick:()=>b("/akademi/etkinlikler"),hoverable:!0,bodyStyle:{padding:"20px"},children:[e.jsx(Te,{title:"Yaklaşan Etkinlikler",value:r.upcoming_events_count,prefix:e.jsx(It,{style:{color:"#722ed1",fontSize:"20px"}}),valueStyle:{color:"#722ed1",fontSize:"28px",fontWeight:"bold"}}),e.jsx("div",{style:{marginTop:12},children:e.jsxs(gt,{type:"secondary",style:{fontSize:"12px"},children:[e.jsx(Vi,{style:{color:"#722ed1",marginRight:4}}),"Gelecek etkinlikler"]})})]})}),e.jsx(N,{xs:24,sm:12,lg:6,children:e.jsxs(q,{loading:t,style:{borderLeft:"4px solid #ff4d4f",boxShadow:"0 4px 12px rgba(0,0,0,0.1)",cursor:"pointer",height:"140px",borderRadius:"8px"},onClick:()=>b("/akademi/destek-talepleri"),hoverable:!0,bodyStyle:{padding:"20px"},children:[e.jsx(Te,{title:"Açık Destek Talepleri",value:r.open_tickets,prefix:e.jsx(da,{style:{color:"#ff4d4f",fontSize:"20px"}}),valueStyle:{color:"#ff4d4f",fontSize:"28px",fontWeight:"bold"}}),e.jsx("div",{style:{marginTop:12},children:e.jsxs(gt,{type:"secondary",style:{fontSize:"12px"},children:[e.jsx(da,{style:{color:"#ff4d4f",marginRight:4}}),"Bekleyen talepler"]})})]})})]}),e.jsx(ke,{gutter:[24,24],style:{marginBottom:32},children:e.jsx(N,{xs:24,children:e.jsx(q,{title:e.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[e.jsxs("div",{children:[e.jsx(hr,{style:{marginRight:8,color:"#faad14"}}),"Son Duyurular"]}),e.jsx(I,{type:"link",size:"small",onClick:()=>b("/akademi/duyurular"),children:"Tümünü Gör"})]}),loading:t,style:{boxShadow:"0 4px 12px rgba(0,0,0,0.1)",borderRadius:"8px"},children:e.jsx(mt,{dataSource:h,renderItem:g=>e.jsx(mt.Item,{children:e.jsx(mt.Item.Meta,{avatar:e.jsx($a,{icon:e.jsx(hr,{}),style:{backgroundColor:"#faad14"}}),title:e.jsx(gt,{strong:!0,style:{fontSize:"14px"},children:g.title}),description:e.jsxs("div",{children:[e.jsx(zo,{ellipsis:{rows:2},style:{margin:0,fontSize:"12px",color:"#666"},children:g.content}),e.jsx(gt,{type:"secondary",style:{fontSize:"11px"},children:xt(g.created_at).format("DD/MM/YYYY HH:mm")})]})})}),locale:{emptyText:"Henüz duyuru bulunmuyor"}})})})}),e.jsxs(ke,{gutter:[16,16],children:[e.jsx(N,{xs:24,lg:8,children:e.jsx(q,{title:e.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[e.jsxs("div",{children:[e.jsx(It,{style:{marginRight:8,color:"#722ed1"}}),"Yaklaşan Etkinlikler"]}),e.jsx(I,{type:"link",size:"small",onClick:()=>b("/akademi/etkinlikler"),children:"Tümünü Gör"})]}),loading:t,style:{boxShadow:"0 2px 8px rgba(0,0,0,0.1)",height:"400px"},bodyStyle:{height:"320px",overflowY:"auto"},children:e.jsx(mt,{dataSource:y,renderItem:g=>e.jsx(mt.Item,{children:e.jsx(mt.Item.Meta,{avatar:e.jsx($a,{icon:e.jsx(It,{}),style:{backgroundColor:"#722ed1"}}),title:e.jsx(gt,{strong:!0,style:{fontSize:"14px"},children:g.title}),description:e.jsxs("div",{children:[e.jsx(zo,{ellipsis:{rows:2},style:{margin:0,fontSize:"12px",color:"#666"},children:g.description}),e.jsx("div",{style:{marginTop:4},children:e.jsx(ae,{color:"purple",style:{fontSize:"10px"},children:xt(g.start_date).format("DD/MM/YYYY HH:mm")})})]})})}),locale:{emptyText:"Yaklaşan etkinlik bulunmuyor"}})})}),e.jsx(N,{xs:24,lg:8,children:e.jsx(q,{title:e.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[e.jsxs("div",{children:[e.jsx(ot,{style:{marginRight:8,color:"#1890ff"}}),"Son Kayıt Olan Kullanıcılar"]}),e.jsx(I,{type:"link",size:"small",onClick:()=>b("/akademi/kullanicilar"),children:"Tümünü Gör"})]}),loading:t,style:{boxShadow:"0 2px 8px rgba(0,0,0,0.1)",height:"400px"},bodyStyle:{height:"320px",overflowY:"auto"},children:e.jsx(mt,{dataSource:d,renderItem:g=>e.jsx(mt.Item,{children:e.jsx(mt.Item.Meta,{avatar:e.jsx($a,{style:{backgroundColor:"#1890ff"},children:g.username.charAt(0).toUpperCase()}),title:e.jsx(gt,{strong:!0,style:{fontSize:"14px"},children:g.name||g.username}),description:e.jsxs("div",{children:[g.name&&e.jsxs(gt,{type:"secondary",style:{fontSize:"12px",display:"block"},children:["@",g.username]}),e.jsx(gt,{type:"secondary",style:{fontSize:"12px",display:"block"},children:g.email}),e.jsxs(gt,{type:"secondary",style:{fontSize:"11px"},children:["Kayıt: ",xt(g.created_at).format("DD/MM/YYYY HH:mm")]})]})})}),locale:{emptyText:"Henüz kullanıcı bulunmuyor"}})})}),e.jsx(N,{xs:24,lg:8,children:e.jsx(q,{title:e.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[e.jsxs("div",{children:[e.jsx(da,{style:{marginRight:8,color:"#ff4d4f"}}),"Son Destek Talepleri"]}),e.jsx(I,{type:"link",size:"small",onClick:()=>b("/akademi/destek-talepleri"),children:"Tümünü Gör"})]}),loading:t,style:{boxShadow:"0 2px 8px rgba(0,0,0,0.1)",height:"400px"},bodyStyle:{height:"320px",overflowY:"auto"},children:e.jsx(mt,{dataSource:j,renderItem:g=>e.jsx(mt.Item,{children:e.jsx(mt.Item.Meta,{avatar:e.jsx($a,{icon:e.jsx(da,{}),style:{backgroundColor:w(g.status)}}),title:e.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[e.jsx(gt,{strong:!0,style:{fontSize:"14px"},children:g.subject}),e.jsx(ae,{color:_(g.priority),style:{fontSize:"10px"},children:g.priority})]}),description:e.jsxs("div",{children:[e.jsx("div",{style:{marginBottom:4},children:e.jsx(ae,{color:w(g.status),style:{fontSize:"10px"},children:g.status})}),e.jsx(gt,{type:"secondary",style:{fontSize:"11px"},children:xt(g.created_at).format("DD/MM/YYYY HH:mm")})]})})}),locale:{emptyText:"Henüz destek talebi bulunmuyor"}})})})]})]})},{Title:Ro,Text:$t}=ht,{TabPane:V2}=Xe,hy=()=>{const[t,s]=u.useState(!1),[r,n]=u.useState({basvuruSayisi:0,onaylananBasvuru:0,toplantıTalebi:0,geriaramaTalebi:0,iletisimTalebi:0,bekleyenTalep:0,toplamBasvuru:0,okunmamisBasvuru:0,toplamToplanti:0,okunmamisToplanti:0,toplamGeriArama:0,okunmamisGeriArama:0,toplamIletisim:0,okunmamisIletisim:0}),[d,c]=u.useState([]);u.useEffect(()=>{h()},[]);const h=async()=>{s(!0);try{const y=await de.get(`${Be.SITE_BASE_URL}${Be.ENDPOINTS.SITE_DASHBOARD}`);y.data.success&&(n(y.data.stats),c(y.data.recentApplications))}catch(y){c([{id:1,name:"Ahmet Yılmaz",email:"<EMAIL>",phone:"0532 123 4567",date:"2023-05-01",status:"Yeni"},{id:2,name:"Ayşe Demir",email:"<EMAIL>",phone:"0533 765 4321",date:"2023-05-01",status:"İnceleniyor"},{id:3,name:"Mehmet Kaya",email:"<EMAIL>",phone:"0535 987 6543",date:"2023-04-30",status:"Onaylandı"}])}finally{s(!1)}},m=[{title:"Ad Soyad",dataIndex:"name",key:"name",render:y=>y||"-"},{title:"TikTok Kullanıcı Adı",key:"tiktok_username",render:y=>y.tiktok_username?e.jsx("a",{href:`https://www.tiktok.com/@${y.tiktok_username.toLowerCase()}`,target:"_blank",rel:"noopener noreferrer",children:y.tiktok_username.toLowerCase()}):"-"},{title:"İletişim",dataIndex:"phone",key:"phone",render:y=>y||"-"},{title:"Tarih",dataIndex:"created_at",key:"created_at",render:y=>y?Fe(y).format("DD.MM.YYYY HH:mm:ss"):"-"},{title:"Durum",dataIndex:"isRead",key:"isRead",render:y=>e.jsx("span",{style:{color:y===1||y==="1"?"#52c41a":"#ff4d4f"},children:y===1||y==="1"?"Okundu":"Okunmadı"})}];return e.jsxs("div",{className:"site-dashboard",style:{padding:"24px",background:"#f5f5f5",minHeight:"100vh"},children:[e.jsx("div",{className:"page-header",style:{marginBottom:32,background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",borderRadius:"16px",padding:"32px",color:"white",boxShadow:"0 8px 32px rgba(0,0,0,0.1)"},children:e.jsx(ke,{align:"middle",justify:"center",children:e.jsx(N,{children:e.jsxs(ce,{align:"center",size:"large",children:[e.jsx("div",{style:{background:"rgba(255,255,255,0.2)",borderRadius:"12px",padding:"12px",backdropFilter:"blur(10px)"},children:e.jsx(Ki,{style:{fontSize:"32px",color:"white"}})}),e.jsxs("div",{style:{textAlign:"center"},children:[e.jsx(Ro,{level:2,style:{margin:0,color:"white"},children:"Site Yönetim Paneli"}),e.jsx($t,{style:{color:"rgba(255,255,255,0.8)",fontSize:"16px"},children:"Tuber Ajans web sitesi yönetim merkezi"})]})]})})})}),e.jsxs(ke,{gutter:[16,16],style:{marginBottom:32},children:[e.jsx(N,{xs:24,sm:12,lg:6,children:e.jsxs(q,{loading:t,style:{borderRadius:"16px",border:"none",boxShadow:"0 4px 20px rgba(24, 144, 255, 0.1)",background:"linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%)",overflow:"hidden",height:"100%"},bodyStyle:{padding:"20px",height:"100%",display:"flex",flexDirection:"column"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:"16px",flex:1},children:[e.jsx("div",{style:{background:"linear-gradient(135deg, #1890ff, #096dd9)",borderRadius:"12px",padding:"12px",marginRight:"12px",flexShrink:0},children:e.jsx(Kn,{style:{fontSize:"20px",color:"white"}})}),e.jsxs("div",{style:{flex:1,minWidth:0},children:[e.jsx($t,{style:{fontSize:"13px",color:"#666",display:"block",lineHeight:"1.2"},children:"Yayıncı Başvuruları"}),e.jsxs($t,{style:{fontSize:"20px",fontWeight:"bold",color:"#1890ff",lineHeight:"1.2"},children:[r.okunmamisBasvuru||0," / ",r.toplamBasvuru||0]})]})]}),e.jsx(ys,{percent:r.toplamBasvuru?r.okunmamisBasvuru/r.toplamBasvuru*100:0,strokeColor:"#1890ff",showInfo:!1,style:{marginBottom:"12px"}}),e.jsx(ls,{to:"/site/basvurular",style:{color:"#1890ff",fontWeight:500,fontSize:"12px"},children:"Tüm Başvuruları Görüntüle →"})]})}),e.jsx(N,{xs:24,sm:12,lg:6,children:e.jsxs(q,{loading:t,style:{borderRadius:"16px",border:"none",boxShadow:"0 4px 20px rgba(82, 196, 26, 0.1)",background:"linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)",overflow:"hidden",height:"100%"},bodyStyle:{padding:"20px",height:"100%",display:"flex",flexDirection:"column"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:"16px",flex:1},children:[e.jsx("div",{style:{background:"linear-gradient(135deg, #52c41a, #389e0d)",borderRadius:"12px",padding:"12px",marginRight:"12px",flexShrink:0},children:e.jsx(da,{style:{fontSize:"20px",color:"white"}})}),e.jsxs("div",{style:{flex:1,minWidth:0},children:[e.jsx($t,{style:{fontSize:"13px",color:"#666",display:"block",lineHeight:"1.2"},children:"Online Toplantı"}),e.jsxs($t,{style:{fontSize:"20px",fontWeight:"bold",color:"#52c41a",lineHeight:"1.2"},children:[r.okunmamisToplanti||0," / ",r.toplamToplanti||0]})]})]}),e.jsx(ys,{percent:r.toplamToplanti?r.okunmamisToplanti/r.toplamToplanti*100:0,strokeColor:"#52c41a",showInfo:!1,style:{marginBottom:"12px"}}),e.jsx(ls,{to:"/site/toplanti-talepleri",style:{color:"#52c41a",fontWeight:500,fontSize:"12px"},children:"Talepleri Görüntüle →"})]})}),e.jsx(N,{xs:24,sm:12,lg:6,children:e.jsxs(q,{loading:t,style:{borderRadius:"16px",border:"none",boxShadow:"0 4px 20px rgba(114, 46, 209, 0.1)",background:"linear-gradient(135deg, #f9f0ff 0%, #f0f9ff 100%)",overflow:"hidden",height:"100%"},bodyStyle:{padding:"20px",height:"100%",display:"flex",flexDirection:"column"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:"16px",flex:1},children:[e.jsx("div",{style:{background:"linear-gradient(135deg, #722ed1, #531dab)",borderRadius:"12px",padding:"12px",marginRight:"12px",flexShrink:0},children:e.jsx(Sr,{style:{fontSize:"20px",color:"white"}})}),e.jsxs("div",{style:{flex:1,minWidth:0},children:[e.jsx($t,{style:{fontSize:"13px",color:"#666",display:"block",lineHeight:"1.2"},children:"İletişim Talepleri"}),e.jsxs($t,{style:{fontSize:"20px",fontWeight:"bold",color:"#722ed1",lineHeight:"1.2"},children:[r.okunmamisIletisim||0," / ",r.toplamIletisim||0]})]})]}),e.jsx(ys,{percent:r.toplamIletisim?r.okunmamisIletisim/r.toplamIletisim*100:0,strokeColor:"#722ed1",showInfo:!1,style:{marginBottom:"12px"}}),e.jsx(ls,{to:"/site/iletisim-talepleri",style:{color:"#722ed1",fontWeight:500,fontSize:"12px"},children:"Talepleri Görüntüle →"})]})}),e.jsx(N,{xs:24,sm:12,lg:6,children:e.jsxs(q,{loading:t,style:{borderRadius:"16px",border:"none",boxShadow:"0 4px 20px rgba(250, 140, 22, 0.1)",background:"linear-gradient(135deg, #fff7e6 0%, #f0f9ff 100%)",overflow:"hidden",height:"100%"},bodyStyle:{padding:"20px",height:"100%",display:"flex",flexDirection:"column"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:"16px",flex:1},children:[e.jsx("div",{style:{background:"linear-gradient(135deg, #fa8c16, #d46b08)",borderRadius:"12px",padding:"12px",marginRight:"12px",flexShrink:0},children:e.jsx(Gi,{style:{fontSize:"20px",color:"white"}})}),e.jsxs("div",{style:{flex:1,minWidth:0},children:[e.jsx($t,{style:{fontSize:"13px",color:"#666",display:"block",lineHeight:"1.2"},children:"Geri Arama"}),e.jsxs($t,{style:{fontSize:"20px",fontWeight:"bold",color:"#fa8c16",lineHeight:"1.2"},children:[r.okunmamisGeriArama||0," / ",r.toplamGeriArama||0]})]})]}),e.jsx(ys,{percent:r.toplamGeriArama?r.okunmamisGeriArama/r.toplamGeriArama*100:0,strokeColor:"#fa8c16",showInfo:!1,style:{marginBottom:"12px"}}),e.jsx(ls,{to:"/site/geriarama-talepleri",style:{color:"#fa8c16",fontWeight:500,fontSize:"12px"},children:"Talepleri Görüntüle →"})]})})]}),e.jsx(ke,{gutter:[24,24],style:{marginTop:32},children:e.jsx(N,{span:24,children:e.jsx(q,{title:e.jsxs(ce,{children:[e.jsx("div",{style:{background:"linear-gradient(135deg, #1890ff, #096dd9)",borderRadius:"8px",padding:"8px",display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(ks,{style:{fontSize:"16px",color:"white"}})}),e.jsx("span",{style:{fontSize:"18px",fontWeight:600},children:"Son Başvurular"}),e.jsx(Ge,{count:d.length,style:{backgroundColor:"#1890ff"}})]}),extra:e.jsx(I,{type:"primary",style:{borderRadius:"8px"},children:e.jsx(ls,{to:"/site/basvurular",style:{color:"white"},children:"Tümünü Gör →"})}),loading:t,style:{borderRadius:"16px",border:"none",boxShadow:"0 4px 20px rgba(0,0,0,0.08)"},bodyStyle:{padding:"24px"},children:d.length===0?e.jsxs("div",{style:{textAlign:"center",padding:"60px 0",color:"#999"},children:[e.jsx(Kn,{style:{fontSize:"48px",marginBottom:"16px",color:"#d9d9d9"}}),e.jsx(Ro,{level:4,style:{color:"#999",margin:0},children:"Henüz başvuru bulunmuyor"}),e.jsx($t,{style:{color:"#999"},children:"Yeni başvurular burada görünecek"})]}):e.jsx(qe,{dataSource:d,columns:m,rowKey:"id",pagination:!1,size:"middle",style:{background:"transparent"},rowClassName:(y,p)=>p%2===0?"table-row-light":"table-row-dark"})})})})]})},{TabPane:Zs}=Xe,{Title:fy,Text:my}=ht,py=()=>{const[t,s]=u.useState([]),[r,n]=u.useState(!0),[d,c]=u.useState(null),[h,m]=u.useState("unread"),y=async w=>{var g;n(!0),c(null);try{const H=await de.get("https://x.tuberajans.com/backend/site/basvurular.php?action=list");if(H.data&&H.data.success){let U=H.data.data;w==="read"?U=U.filter(T=>T.isRead===1||T.isRead==="1"):w==="rejected"&&(U=U.filter(T=>T.isReject===1||T.isReject==="1")),U.sort((T,W)=>T.isRead===W.isRead?0:T.isRead?1:-1),s(U)}else c(((g=H.data)==null?void 0:g.message)||"Başvurular yüklenemedi")}catch(H){c("Başvurular yüklenemedi")}n(!1)};u.useEffect(()=>{y(h)},[h]);const p=w=>{S.success("Başvuru okundu olarak işaretlendi (API entegrasyonu eklenmeli)"),y(h)},j=w=>{S.success("Başvuru reddedildi (API entegrasyonu eklenmeli)"),y(h)},x=w=>{S.success("Başvuru silindi (API entegrasyonu eklenmeli)"),y(h)},b=u.useMemo(()=>{const w=t.length,g=t.filter(T=>T.isRead===0||T.isRead==="0").length,H=t.filter(T=>T.isRead===1||T.isRead==="1").length,U=t.filter(T=>T.isReject===1||T.isReject==="1").length;return{total:w,unread:g,read:H,rejected:U}},[t]),C=u.useMemo(()=>{const w=t.filter(T=>T.isRead===0||T.isRead==="0"),g=t.filter(T=>T.isRead===1||T.isRead==="1"),H=t.filter(T=>T.isReject===1||T.isReject==="1"),U=[...t].sort((T,W)=>T.isRead===W.isRead?0:T.isRead?1:-1);return{unreadData:w,readData:g,rejectedData:H,allData:U}},[t]),_=[{title:"Ad Soyad",key:"fullname",render:w=>w.name||w.surname?`${w.name||""} ${w.surname||""}`.trim():"-"},{title:"Telefon",dataIndex:"phone",key:"phone"},{title:"TikTok Kullanıcı Adı",key:"tiktok_username",render:w=>w.tiktok_username?e.jsx("a",{href:`https://www.tiktok.com/@${w.tiktok_username.toLowerCase()}`,target:"_blank",rel:"noopener noreferrer",children:w.tiktok_username.toLowerCase()}):"-"},{title:"Takipçi",dataIndex:"follower_range",key:"follower_range"},{title:"Başvuru Tarihi",dataIndex:"created_at",key:"created_at",render:w=>w?Fe(w).format("DD/MM/YYYY HH:mm"):"-"},{title:"İşlemler",key:"actions",render:w=>e.jsxs(ce,{children:[e.jsx(I,{size:"small",onClick:()=>p(w.id),disabled:w.isRead===1||w.isRead==="1",children:"Okundu"}),e.jsx(I,{danger:!0,size:"small",onClick:()=>j(w.id),disabled:w.isReject===1||w.isReject==="1",children:"Reddet"}),e.jsx(it,{title:"Başvuruyu silmek istediğinize emin misiniz?",onConfirm:()=>x(w.id),okText:"Evet",cancelText:"Hayır",okButtonProps:{style:{backgroundColor:"#1890ff",color:"#fff",borderColor:"#1890ff"}},children:e.jsx(I,{danger:!0,size:"small",children:"Sil"})})]})}];return r?e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"60vh",flexDirection:"column"},children:e.jsx(Ta,{size:"large",tip:"Başvurular yükleniyor..."})}):d?e.jsx("div",{style:{padding:"24px"},children:e.jsx(Zt,{type:"error",message:"Hata",description:d,showIcon:!0,style:{borderRadius:"12px"}})}):e.jsxs("div",{className:"site-applications",style:{padding:"24px",background:"#f5f5f5",minHeight:"100vh"},children:[e.jsx("div",{style:{marginBottom:32,background:"linear-gradient(135deg, #1890ff 0%, #096dd9 100%)",borderRadius:"16px",padding:"32px",color:"white",boxShadow:"0 8px 32px rgba(0,0,0,0.1)"},children:e.jsx(ke,{align:"middle",justify:"space-between",children:e.jsx(N,{children:e.jsxs(ce,{align:"center",size:"large",children:[e.jsx("div",{style:{background:"rgba(255,255,255,0.2)",borderRadius:"12px",padding:"12px",backdropFilter:"blur(10px)"},children:e.jsx(Kn,{style:{fontSize:"32px",color:"white"}})}),e.jsxs("div",{children:[e.jsx(fy,{level:2,style:{margin:0,color:"white"},children:"Yayıncı Başvuruları"}),e.jsx(my,{style:{color:"rgba(255,255,255,0.8)",fontSize:"16px"},children:"Başvuru yönetimi ve takip sistemi"})]})]})})})}),e.jsxs(ke,{gutter:[16,16],style:{marginBottom:32},children:[e.jsx(N,{xs:24,sm:12,md:6,children:e.jsx(q,{style:{borderRadius:"12px",textAlign:"center"},children:e.jsx(Te,{title:"Toplam Başvuru",value:b.total,prefix:e.jsx(ot,{style:{color:"#1890ff"}}),valueStyle:{color:"#1890ff"}})})}),e.jsx(N,{xs:24,sm:12,md:6,children:e.jsx(q,{style:{borderRadius:"12px",textAlign:"center"},children:e.jsx(Te,{title:"Okunmamış",value:b.unread,prefix:e.jsx(Rt,{style:{color:"#fa8c16"}}),valueStyle:{color:"#fa8c16"}})})}),e.jsx(N,{xs:24,sm:12,md:6,children:e.jsx(q,{style:{borderRadius:"12px",textAlign:"center"},children:e.jsx(Te,{title:"Okunanlar",value:b.read,prefix:e.jsx(ea,{style:{color:"#52c41a"}}),valueStyle:{color:"#52c41a"}})})}),e.jsx(N,{xs:24,sm:12,md:6,children:e.jsx(q,{style:{borderRadius:"12px",textAlign:"center"},children:e.jsx(Te,{title:"Reddedilenler",value:b.rejected,prefix:e.jsx(rr,{style:{color:"#ff4d4f"}}),valueStyle:{color:"#ff4d4f"}})})})]}),e.jsx(q,{style:{borderRadius:"16px",border:"none",boxShadow:"0 4px 20px rgba(0,0,0,0.08)"},bodyStyle:{padding:"24px"},children:e.jsxs(Xe,{activeKey:h,onChange:m,size:"large",style:{marginBottom:0},children:[e.jsx(Zs,{tab:e.jsxs(ce,{children:[e.jsx("span",{children:"Yeni Başvurular"}),b.unread>0&&e.jsx(Ge,{count:b.unread})]}),children:e.jsx(qe,{columns:_,dataSource:C.unreadData,rowKey:"id",pagination:{pageSize:15,showSizeChanger:!0,showQuickJumper:!0,showTotal:(w,g)=>`${g[0]}-${g[1]} / ${w} başvuru`},scroll:{x:1200},size:"middle",style:{marginTop:16}})},"unread"),e.jsx(Zs,{tab:e.jsxs(ce,{children:[e.jsx("span",{children:"Okunanlar"}),b.read>0&&e.jsx(Ge,{count:b.read,style:{backgroundColor:"#52c41a"}})]}),children:e.jsx(qe,{columns:_,dataSource:C.readData,rowKey:"id",pagination:{pageSize:15,showSizeChanger:!0,showQuickJumper:!0,showTotal:(w,g)=>`${g[0]}-${g[1]} / ${w} başvuru`},scroll:{x:1200},size:"middle",style:{marginTop:16}})},"read"),e.jsx(Zs,{tab:e.jsxs(ce,{children:[e.jsx("span",{children:"Reddedilenler"}),b.rejected>0&&e.jsx(Ge,{count:b.rejected,style:{backgroundColor:"#ff4d4f"}})]}),children:e.jsx(qe,{columns:_,dataSource:C.rejectedData,rowKey:"id",pagination:{pageSize:15,showSizeChanger:!0,showQuickJumper:!0,showTotal:(w,g)=>`${g[0]}-${g[1]} / ${w} başvuru`},scroll:{x:1200},size:"middle",style:{marginTop:16}})},"rejected"),e.jsx(Zs,{tab:e.jsxs(ce,{children:[e.jsx("span",{children:"Tüm Başvurular"}),b.total>0&&e.jsx(Ge,{count:b.total,style:{backgroundColor:"#722ed1"}})]}),children:e.jsx(qe,{columns:_,dataSource:C.allData,rowKey:"id",pagination:{pageSize:15,showSizeChanger:!0,showQuickJumper:!0,showTotal:(w,g)=>`${g[0]}-${g[1]} / ${w} başvuru`},scroll:{x:1200},size:"middle",style:{marginTop:16}})},"all")]})})]})},{Title:gy,Text:xy}=ht,yy=()=>{const[t,s]=u.useState([]),[r,n]=u.useState(!0),[d,c]=u.useState(null),h=async()=>{var x;n(!0),c(null);try{const b=await de.get("/backend/site/iletisimtalepleri.php?action=list");if(b.data&&b.data.success){const C=b.data.data.sort((_,w)=>_.isRead===w.isRead?0:_.isRead?1:-1);s(C)}else c(((x=b.data)==null?void 0:x.message)||"İletişim talepleri yüklenemedi")}catch(b){c("İletişim talepleri yüklenirken bir hata oluştu!")}finally{n(!1)}};u.useEffect(()=>{h()},[]);const m=async x=>{try{await de.post("/backend/site/iletisimtalepleri.php?action=okundu",{id:x}),S.success("Talep okundu olarak işaretlendi"),h()}catch(b){S.error("İşlem sırasında bir hata oluştu!")}},y=async x=>{try{await de.post("/backend/site/iletisimtalepleri.php?action=delete",{id:x}),S.success("Talep silindi"),h()}catch(b){S.error("Silme işlemi sırasında bir hata oluştu!")}},p=u.useMemo(()=>{const x=t.length,b=t.filter(_=>!_.isRead||_.isRead===0||_.isRead==="0").length,C=t.filter(_=>_.isRead===1||_.isRead==="1").length;return{total:x,unread:b,read:C}},[t]),j=[{title:"#",dataIndex:"id",key:"id",width:60,sorter:(x,b)=>x.id-b.id},{title:"Ad Soyad",dataIndex:"adsoyad",key:"adsoyad",width:150,sorter:(x,b)=>(x.adsoyad||"").localeCompare(b.adsoyad||"")},{title:"E-posta",dataIndex:"mail",key:"mail",width:200,render:x=>e.jsx("a",{href:`mailto:${x}`,style:{color:"#1890ff"},children:x})},{title:"Mesaj",dataIndex:"mesaj",key:"mesaj",width:400,render:x=>e.jsx("div",{style:{maxWidth:"300px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:x})},{title:"Tarih",dataIndex:"zaman",key:"zaman",width:160,sorter:(x,b)=>(x.zaman||0)-(b.zaman||0),render:x=>x?Fe.unix(Number(x)).format("DD/MM/YYYY HH:mm"):"-"},{title:"Durum",key:"status",width:100,render:(x,b)=>e.jsx(ae,{color:b.isRead?"green":"orange",children:b.isRead?"Okundu":"Yeni"})},{title:"İşlemler",key:"actions",width:140,render:(x,b)=>e.jsxs(ce,{size:"small",children:[e.jsx(I,{type:"primary",size:"small",icon:e.jsx(ea,{}),onClick:()=>m(b.id),disabled:b.isRead,style:{backgroundColor:b.isRead?"#d9d9d9":"#52c41a",borderColor:b.isRead?"#d9d9d9":"#52c41a"},children:b.isRead?"Okundu":"Okundu İşaretle"}),e.jsx(it,{title:"Bu talebi silmek istediğinize emin misiniz?",description:"Bu işlem geri alınamaz.",onConfirm:()=>y(b.id),okText:"Evet, Sil",cancelText:"Vazgeç",okButtonProps:{danger:!0},children:e.jsx(I,{danger:!0,size:"small",icon:e.jsx(Et,{}),children:"Sil"})})]})}];return r?e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"60vh",flexDirection:"column"},children:e.jsx(Ta,{size:"large",tip:"İletişim talepleri yükleniyor..."})}):d?e.jsx("div",{style:{padding:"24px"},children:e.jsx(Zt,{type:"error",message:"Hata",description:d,showIcon:!0,style:{borderRadius:"12px"}})}):e.jsxs("div",{className:"site-contact-requests",style:{padding:"24px",background:"#f5f5f5",minHeight:"100vh"},children:[e.jsx("div",{style:{marginBottom:32,background:"linear-gradient(135deg, #722ed1 0%, #531dab 100%)",borderRadius:"16px",padding:"32px",color:"white",boxShadow:"0 8px 32px rgba(0,0,0,0.1)"},children:e.jsx(ke,{align:"middle",justify:"space-between",children:e.jsx(N,{children:e.jsxs(ce,{align:"center",size:"large",children:[e.jsx("div",{style:{background:"rgba(255,255,255,0.2)",borderRadius:"12px",padding:"12px",backdropFilter:"blur(10px)"},children:e.jsx(Sr,{style:{fontSize:"32px",color:"white"}})}),e.jsxs("div",{children:[e.jsx(gy,{level:2,style:{margin:0,color:"white"},children:"İletişim Talepleri"}),e.jsx(xy,{style:{color:"rgba(255,255,255,0.8)",fontSize:"16px"},children:"Müşteri iletişim talepleri yönetimi"})]})]})})})}),e.jsxs(ke,{gutter:[16,16],style:{marginBottom:32},children:[e.jsx(N,{xs:24,sm:8,children:e.jsx(q,{style:{borderRadius:"12px",textAlign:"center"},children:e.jsx(Te,{title:"Toplam Talep",value:p.total,prefix:e.jsx(ua,{style:{color:"#722ed1"}}),valueStyle:{color:"#722ed1"}})})}),e.jsx(N,{xs:24,sm:8,children:e.jsx(q,{style:{borderRadius:"12px",textAlign:"center"},children:e.jsx(Te,{title:"Yeni Talepler",value:p.unread,prefix:e.jsx(Rt,{style:{color:"#fa8c16"}}),valueStyle:{color:"#fa8c16"}})})}),e.jsx(N,{xs:24,sm:8,children:e.jsx(q,{style:{borderRadius:"12px",textAlign:"center"},children:e.jsx(Te,{title:"Cevaplanmış",value:p.read,prefix:e.jsx(ea,{style:{color:"#52c41a"}}),valueStyle:{color:"#52c41a"}})})})]}),e.jsx(q,{style:{borderRadius:"16px",border:"none",boxShadow:"0 4px 20px rgba(0,0,0,0.08)"},bodyStyle:{padding:"24px"},children:e.jsx(qe,{columns:j,dataSource:t,loading:r,rowKey:"id",pagination:{pageSize:20,showSizeChanger:!0,showQuickJumper:!0,showTotal:(x,b)=>`${b[0]}-${b[1]} / ${x} talep`},scroll:{x:1200},size:"middle",rowClassName:x=>x.isRead?"read-row":"unread-row"})})]})},{Title:by,Text:jy}=ht,vy=()=>{const[t,s]=u.useState([]),[r,n]=u.useState(!0),[d,c]=u.useState(null),h=async()=>{var x;n(!0),c(null);try{const b=await de.get("/backend/site/geriaramatalep.php?action=list");if(b.data&&b.data.success){const C=b.data.data.sort((_,w)=>_.isRead===w.isRead?0:_.isRead?1:-1);s(C)}else c(((x=b.data)==null?void 0:x.message)||"Geri arama talepleri yüklenemedi")}catch(b){c("Geri arama talepleri yüklenirken bir hata oluştu!")}finally{n(!1)}};u.useEffect(()=>{h()},[]);const m=async x=>{try{await de.post("/backend/site/geriaramatalep.php?action=okundu",{id:x}),S.success("Talep okundu olarak işaretlendi"),h()}catch(b){S.error("İşlem sırasında bir hata oluştu!")}},y=async x=>{try{await de.post("/backend/site/geriaramatalep.php?action=delete",{id:x}),S.success("Talep silindi"),h()}catch(b){S.error("Silme işlemi sırasında bir hata oluştu!")}},p=u.useMemo(()=>{const x=t.length,b=t.filter(_=>!_.isRead||_.isRead===0||_.isRead==="0").length,C=t.filter(_=>_.isRead===1||_.isRead==="1").length;return{total:x,unread:b,read:C}},[t]),j=[{title:"#",dataIndex:"id",key:"id",width:60,sorter:(x,b)=>x.id-b.id},{title:"Ad Soyad",dataIndex:"adsoyad",key:"adsoyad",width:150,sorter:(x,b)=>(x.adsoyad||"").localeCompare(b.adsoyad||"")},{title:"Telefon",dataIndex:"telefon",key:"telefon",width:150,render:x=>e.jsx("a",{href:`tel:${x}`,style:{color:"#1890ff"},children:x})},{title:"TikTok",dataIndex:"tiktok",key:"tiktok",width:180,render:x=>x?e.jsxs("a",{href:`https://www.tiktok.com/@${x}`,target:"_blank",rel:"noopener noreferrer",style:{color:"#1890ff"},children:["@",x]}):"-"},{title:"Tarih",dataIndex:"zaman",key:"zaman",width:160,sorter:(x,b)=>(x.zaman||0)-(b.zaman||0),render:x=>x?Fe.unix(Number(x)).format("DD/MM/YYYY HH:mm"):"-"},{title:"Durum",key:"status",width:100,render:(x,b)=>e.jsx(ae,{color:b.isRead?"green":"orange",children:b.isRead?"Arandı":"Bekliyor"})},{title:"İşlemler",key:"actions",width:140,render:(x,b)=>e.jsxs(ce,{size:"small",children:[e.jsx(I,{type:"primary",size:"small",icon:e.jsx(ea,{}),onClick:()=>m(b.id),disabled:b.isRead,style:{backgroundColor:b.isRead?"#d9d9d9":"#52c41a",borderColor:b.isRead?"#d9d9d9":"#52c41a"},children:b.isRead?"Arandı":"Arandı İşaretle"}),e.jsx(it,{title:"Bu talebi silmek istediğinize emin misiniz?",description:"Bu işlem geri alınamaz.",onConfirm:()=>y(b.id),okText:"Evet, Sil",cancelText:"Vazgeç",okButtonProps:{danger:!0},children:e.jsx(I,{danger:!0,size:"small",icon:e.jsx(Et,{}),children:"Sil"})})]})}];return r?e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"60vh",flexDirection:"column"},children:e.jsx(Ta,{size:"large",tip:"Geri arama talepleri yükleniyor..."})}):d?e.jsx("div",{style:{padding:"24px"},children:e.jsx(Zt,{type:"error",message:"Hata",description:d,showIcon:!0,style:{borderRadius:"12px"}})}):e.jsxs("div",{className:"site-callback-requests",style:{padding:"24px",background:"#f5f5f5",minHeight:"100vh"},children:[e.jsx("div",{style:{marginBottom:32,background:"linear-gradient(135deg, #fa8c16 0%, #d46b08 100%)",borderRadius:"16px",padding:"32px",color:"white",boxShadow:"0 8px 32px rgba(0,0,0,0.1)"},children:e.jsx(ke,{align:"middle",justify:"space-between",children:e.jsx(N,{children:e.jsxs(ce,{align:"center",size:"large",children:[e.jsx("div",{style:{background:"rgba(255,255,255,0.2)",borderRadius:"12px",padding:"12px",backdropFilter:"blur(10px)"},children:e.jsx(Gi,{style:{fontSize:"32px",color:"white"}})}),e.jsxs("div",{children:[e.jsx(by,{level:2,style:{margin:0,color:"white"},children:"Geri Arama Talepleri"}),e.jsx(jy,{style:{color:"rgba(255,255,255,0.8)",fontSize:"16px"},children:"Müşteri geri arama talepleri yönetimi"})]})]})})})}),e.jsxs(ke,{gutter:[16,16],style:{marginBottom:32},children:[e.jsx(N,{xs:24,sm:8,children:e.jsx(q,{style:{borderRadius:"12px",textAlign:"center"},children:e.jsx(Te,{title:"Toplam Talep",value:p.total,prefix:e.jsx(ot,{style:{color:"#fa8c16"}}),valueStyle:{color:"#fa8c16"}})})}),e.jsx(N,{xs:24,sm:8,children:e.jsx(q,{style:{borderRadius:"12px",textAlign:"center"},children:e.jsx(Te,{title:"Bekleyen Aramalar",value:p.unread,prefix:e.jsx(Rt,{style:{color:"#ff4d4f"}}),valueStyle:{color:"#ff4d4f"}})})}),e.jsx(N,{xs:24,sm:8,children:e.jsx(q,{style:{borderRadius:"12px",textAlign:"center"},children:e.jsx(Te,{title:"Tamamlanan",value:p.read,prefix:e.jsx(ea,{style:{color:"#52c41a"}}),valueStyle:{color:"#52c41a"}})})})]}),e.jsx(q,{style:{borderRadius:"16px",border:"none",boxShadow:"0 4px 20px rgba(0,0,0,0.08)"},bodyStyle:{padding:"24px"},children:e.jsx(qe,{columns:j,dataSource:t,rowKey:"id",loading:r,pagination:{pageSize:20,showSizeChanger:!0,showQuickJumper:!0,showTotal:(x,b)=>`${b[0]}-${b[1]} / ${x} talep`},scroll:{x:1200},size:"middle",rowClassName:x=>x.isRead?"read-row":"unread-row"})})]})},{Title:ky,Text:wy}=ht,Sy=()=>{const[t,s]=u.useState([]),[r,n]=u.useState(!0),[d,c]=u.useState(null),h=async()=>{var x;n(!0),c(null);try{const b=await de.get("/backend/site/onlinetoplantitalep.php?action=list");if(b.data&&b.data.success){const C=b.data.data.sort((_,w)=>_.isRead===w.isRead?0:_.isRead?1:-1);s(C)}else c(((x=b.data)==null?void 0:x.message)||"Online toplantı talepleri yüklenemedi")}catch(b){c("Online toplantı talepleri yüklenirken bir hata oluştu!")}finally{n(!1)}};u.useEffect(()=>{h()},[]);const m=async x=>{try{await de.post("/backend/site/onlinetoplantitalep.php?action=mark_read",{id:x}),S.success("Talep okundu olarak işaretlendi"),h()}catch(b){S.error("İşlem sırasında bir hata oluştu!")}},y=async x=>{try{await de.post("/backend/site/onlinetoplantitalep.php?action=delete",{id:x}),S.success("Talep silindi"),h()}catch(b){S.error("Silme işlemi sırasında bir hata oluştu!")}},p=u.useMemo(()=>{const x=t.length,b=t.filter(w=>!w.isRead||w.isRead===0||w.isRead==="0").length,C=t.filter(w=>w.isRead===1||w.isRead==="1").length,_=t.filter(w=>w.meeting_date?Fe(w.meeting_date).isAfter(Fe()):!1).length;return{total:x,unread:b,read:C,upcoming:_}},[t]),j=[{title:"#",dataIndex:"id",key:"id",width:60,sorter:(x,b)=>x.id-b.id},{title:"Ad Soyad",dataIndex:"name",key:"name",width:150,sorter:(x,b)=>(x.name||"").localeCompare(b.name||"")},{title:"E-posta",dataIndex:"email",key:"email",width:200,render:x=>e.jsx("a",{href:`mailto:${x}`,style:{color:"#1890ff"},children:x})},{title:"Telefon",dataIndex:"phone",key:"phone",width:150,render:x=>e.jsx("a",{href:`tel:${x}`,style:{color:"#1890ff"},children:x})},{title:"Toplantı Tarihi",dataIndex:"meeting_date",key:"meeting_date",width:160,sorter:(x,b)=>Fe(x.meeting_date||0).unix()-Fe(b.meeting_date||0).unix(),render:x=>{if(!x)return"-";const b=Fe(x),C=b.isAfter(Fe());return e.jsx(ae,{color:C?"blue":"default",children:b.format("DD/MM/YYYY HH:mm")})}},{title:"Platform",dataIndex:"platform",key:"platform",width:120,render:x=>e.jsx(ae,{color:"geekblue",children:x||"Belirtilmemiş"})},{title:"Toplantı Konusu",dataIndex:"meeting_topic",key:"meeting_topic",width:350,render:x=>e.jsx("div",{style:{maxWidth:"300px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:x||"Konu belirtilmemiş"})},{title:"Durum",key:"status",width:100,render:(x,b)=>e.jsx(ae,{color:b.isRead?"green":"orange",children:b.isRead?"Görüldü":"Yeni"})},{title:"İşlemler",key:"actions",width:140,render:(x,b)=>e.jsxs(ce,{size:"small",children:[e.jsx(I,{type:"primary",size:"small",icon:e.jsx(ea,{}),onClick:()=>m(b.id),disabled:b.isRead,style:{backgroundColor:b.isRead?"#d9d9d9":"#52c41a",borderColor:b.isRead?"#d9d9d9":"#52c41a"},children:b.isRead?"Görüldü":"Görüldü İşaretle"}),e.jsx(it,{title:"Bu talebi silmek istediğinize emin misiniz?",description:"Bu işlem geri alınamaz.",onConfirm:()=>y(b.id),okText:"Evet, Sil",cancelText:"Vazgeç",okButtonProps:{danger:!0},children:e.jsx(I,{danger:!0,size:"small",icon:e.jsx(Et,{}),children:"Sil"})})]})}];return r?e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"60vh",flexDirection:"column"},children:e.jsx(Ta,{size:"large",tip:"Online toplantı talepleri yükleniyor..."})}):d?e.jsx("div",{style:{padding:"24px"},children:e.jsx(Zt,{type:"error",message:"Hata",description:d,showIcon:!0,style:{borderRadius:"12px"}})}):e.jsxs("div",{className:"site-meeting-requests",style:{padding:"24px",background:"#f5f5f5",minHeight:"100vh"},children:[e.jsx("div",{style:{marginBottom:32,background:"linear-gradient(135deg, #52c41a 0%, #389e0d 100%)",borderRadius:"16px",padding:"32px",color:"white",boxShadow:"0 8px 32px rgba(0,0,0,0.1)"},children:e.jsx(ke,{align:"middle",justify:"space-between",children:e.jsx(N,{children:e.jsxs(ce,{align:"center",size:"large",children:[e.jsx("div",{style:{background:"rgba(255,255,255,0.2)",borderRadius:"12px",padding:"12px",backdropFilter:"blur(10px)"},children:e.jsx(da,{style:{fontSize:"32px",color:"white"}})}),e.jsxs("div",{children:[e.jsx(ky,{level:2,style:{margin:0,color:"white"},children:"Online Toplantı Talepleri"}),e.jsx(wy,{style:{color:"rgba(255,255,255,0.8)",fontSize:"16px"},children:"Müşteri toplantı talepleri yönetimi"})]})]})})})}),e.jsxs(ke,{gutter:[16,16],style:{marginBottom:32},children:[e.jsx(N,{xs:24,sm:12,md:6,children:e.jsx(q,{style:{borderRadius:"12px",textAlign:"center"},children:e.jsx(Te,{title:"Toplam Talep",value:p.total,prefix:e.jsx(ot,{style:{color:"#52c41a"}}),valueStyle:{color:"#52c41a"}})})}),e.jsx(N,{xs:24,sm:12,md:6,children:e.jsx(q,{style:{borderRadius:"12px",textAlign:"center"},children:e.jsx(Te,{title:"Yeni Talepler",value:p.unread,prefix:e.jsx(Rt,{style:{color:"#fa8c16"}}),valueStyle:{color:"#fa8c16"}})})}),e.jsx(N,{xs:24,sm:12,md:6,children:e.jsx(q,{style:{borderRadius:"12px",textAlign:"center"},children:e.jsx(Te,{title:"Görülenler",value:p.read,prefix:e.jsx(ea,{style:{color:"#1890ff"}}),valueStyle:{color:"#1890ff"}})})}),e.jsx(N,{xs:24,sm:12,md:6,children:e.jsx(q,{style:{borderRadius:"12px",textAlign:"center"},children:e.jsx(Te,{title:"Yaklaşan Toplantılar",value:p.upcoming,prefix:e.jsx(It,{style:{color:"#722ed1"}}),valueStyle:{color:"#722ed1"}})})})]}),e.jsx(q,{style:{borderRadius:"16px",border:"none",boxShadow:"0 4px 20px rgba(0,0,0,0.08)"},bodyStyle:{padding:"24px"},children:e.jsx(qe,{columns:j,dataSource:t,rowKey:"id",loading:r,pagination:{pageSize:20,showSizeChanger:!0,showQuickJumper:!0,showTotal:(x,b)=>`${b[0]}-${b[1]} / ${x} talep`},scroll:{x:1400},size:"middle",rowClassName:x=>x.isRead?"read-row":"unread-row"})})]})};function Bc(t){throw new Error('Could not dynamically require "'+t+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var Li={exports:{}},lr={exports:{}},_y=lr.exports,Eo;function Ty(){return Eo||(Eo=1,function(t,s){(function(r,n){t.exports=n()})(_y,function(){var r;function n(){return r.apply(null,arguments)}function d(a){r=a}function c(a){return a instanceof Array||Object.prototype.toString.call(a)==="[object Array]"}function h(a){return a!=null&&Object.prototype.toString.call(a)==="[object Object]"}function m(a,i){return Object.prototype.hasOwnProperty.call(a,i)}function y(a){if(Object.getOwnPropertyNames)return Object.getOwnPropertyNames(a).length===0;var i;for(i in a)if(m(a,i))return!1;return!0}function p(a){return a===void 0}function j(a){return typeof a=="number"||Object.prototype.toString.call(a)==="[object Number]"}function x(a){return a instanceof Date||Object.prototype.toString.call(a)==="[object Date]"}function b(a,i){var l=[],o,f=a.length;for(o=0;o<f;++o)l.push(i(a[o],o));return l}function C(a,i){for(var l in i)m(i,l)&&(a[l]=i[l]);return m(i,"toString")&&(a.toString=i.toString),m(i,"valueOf")&&(a.valueOf=i.valueOf),a}function _(a,i,l,o){return fl(a,i,l,o,!0).utc()}function w(){return{empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidEra:null,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],era:null,meridiem:null,rfc2822:!1,weekdayMismatch:!1}}function g(a){return a._pf==null&&(a._pf=w()),a._pf}var H;Array.prototype.some?H=Array.prototype.some:H=function(a){var i=Object(this),l=i.length>>>0,o;for(o=0;o<l;o++)if(o in i&&a.call(this,i[o],o,i))return!0;return!1};function U(a){var i=null,l=!1,o=a._d&&!isNaN(a._d.getTime());if(o&&(i=g(a),l=H.call(i.parsedDateParts,function(f){return f!=null}),o=i.overflow<0&&!i.empty&&!i.invalidEra&&!i.invalidMonth&&!i.invalidWeekday&&!i.weekdayMismatch&&!i.nullInput&&!i.invalidFormat&&!i.userInvalidated&&(!i.meridiem||i.meridiem&&l),a._strict&&(o=o&&i.charsLeftOver===0&&i.unusedTokens.length===0&&i.bigHour===void 0)),Object.isFrozen==null||!Object.isFrozen(a))a._isValid=o;else return o;return a._isValid}function T(a){var i=_(NaN);return a!=null?C(g(i),a):g(i).userInvalidated=!0,i}var W=n.momentProperties=[],G=!1;function J(a,i){var l,o,f,v=W.length;if(p(i._isAMomentObject)||(a._isAMomentObject=i._isAMomentObject),p(i._i)||(a._i=i._i),p(i._f)||(a._f=i._f),p(i._l)||(a._l=i._l),p(i._strict)||(a._strict=i._strict),p(i._tzm)||(a._tzm=i._tzm),p(i._isUTC)||(a._isUTC=i._isUTC),p(i._offset)||(a._offset=i._offset),p(i._pf)||(a._pf=g(i)),p(i._locale)||(a._locale=i._locale),v>0)for(l=0;l<v;l++)o=W[l],f=i[o],p(f)||(a[o]=f);return a}function B(a){J(this,a),this._d=new Date(a._d!=null?a._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),G===!1&&(G=!0,n.updateOffset(this),G=!1)}function M(a){return a instanceof B||a!=null&&a._isAMomentObject!=null}function X(a){n.suppressDeprecationWarnings}function oe(a,i){var l=!0;return C(function(){if(n.deprecationHandler!=null&&n.deprecationHandler(null,a),l){var o=[],f,v,E,ne=arguments.length;for(v=0;v<ne;v++){if(f="",typeof arguments[v]=="object"){f+=`
[`+v+"] ";for(E in arguments[0])m(arguments[0],E)&&(f+=E+": "+arguments[0][E]+", ");f=f.slice(0,-2)}else f=arguments[v];o.push(f)}X(a+`
Arguments: `+Array.prototype.slice.call(o).join("")+`
`+new Error().stack),l=!1}return i.apply(this,arguments)},i)}var K={};function ye(a,i){n.deprecationHandler!=null&&n.deprecationHandler(a,i),K[a]||(X(i),K[a]=!0)}n.suppressDeprecationWarnings=!1,n.deprecationHandler=null;function _e(a){return typeof Function!="undefined"&&a instanceof Function||Object.prototype.toString.call(a)==="[object Function]"}function Re(a){var i,l;for(l in a)m(a,l)&&(i=a[l],_e(i)?this[l]=i:this["_"+l]=i);this._config=a,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)}function A(a,i){var l=C({},a),o;for(o in i)m(i,o)&&(h(a[o])&&h(i[o])?(l[o]={},C(l[o],a[o]),C(l[o],i[o])):i[o]!=null?l[o]=i[o]:delete l[o]);for(o in a)m(a,o)&&!m(i,o)&&h(a[o])&&(l[o]=C({},l[o]));return l}function F(a){a!=null&&this.set(a)}var ie;Object.keys?ie=Object.keys:ie=function(a){var i,l=[];for(i in a)m(a,i)&&l.push(i);return l};var je={sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"};function De(a,i,l){var o=this._calendar[a]||this._calendar.sameElse;return _e(o)?o.call(i,l):o}function Y(a,i,l){var o=""+Math.abs(a),f=i-o.length,v=a>=0;return(v?l?"+":"":"-")+Math.pow(10,Math.max(0,f)).toString().substr(1)+o}var pe=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,Oe=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,be={},Ee={};function Z(a,i,l,o){var f=o;typeof o=="string"&&(f=function(){return this[o]()}),a&&(Ee[a]=f),i&&(Ee[i[0]]=function(){return Y(f.apply(this,arguments),i[1],i[2])}),l&&(Ee[l]=function(){return this.localeData().ordinal(f.apply(this,arguments),a)})}function D(a){return a.match(/\[[\s\S]/)?a.replace(/^\[|\]$/g,""):a.replace(/\\/g,"")}function Q(a){var i=a.match(pe),l,o;for(l=0,o=i.length;l<o;l++)Ee[i[l]]?i[l]=Ee[i[l]]:i[l]=D(i[l]);return function(f){var v="",E;for(E=0;E<o;E++)v+=_e(i[E])?i[E].call(f,a):i[E];return v}}function k(a,i){return a.isValid()?(i=ge(i,a.localeData()),be[i]=be[i]||Q(i),be[i](a)):a.localeData().invalidDate()}function ge(a,i){var l=5;function o(f){return i.longDateFormat(f)||f}for(Oe.lastIndex=0;l>=0&&Oe.test(a);)a=a.replace(Oe,o),Oe.lastIndex=0,l-=1;return a}var $={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"};function te(a){var i=this._longDateFormat[a],l=this._longDateFormat[a.toUpperCase()];return i||!l?i:(this._longDateFormat[a]=l.match(pe).map(function(o){return o==="MMMM"||o==="MM"||o==="DD"||o==="dddd"?o.slice(1):o}).join(""),this._longDateFormat[a])}var Ye="Invalid date";function Ve(){return this._invalidDate}var jt="%d",aa=/\d{1,2}/;function Bt(a){return this._ordinal.replace("%d",a)}var sa={future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",w:"a week",ww:"%d weeks",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};function P(a,i,l,o){var f=this._relativeTime[l];return _e(f)?f(a,i,l,o):f.replace(/%d/i,a)}function ue(a,i){var l=this._relativeTime[a>0?"future":"past"];return _e(l)?l(i):l.replace(/%s/i,i)}var xe={D:"date",dates:"date",date:"date",d:"day",days:"day",day:"day",e:"weekday",weekdays:"weekday",weekday:"weekday",E:"isoWeekday",isoweekdays:"isoWeekday",isoweekday:"isoWeekday",DDD:"dayOfYear",dayofyears:"dayOfYear",dayofyear:"dayOfYear",h:"hour",hours:"hour",hour:"hour",ms:"millisecond",milliseconds:"millisecond",millisecond:"millisecond",m:"minute",minutes:"minute",minute:"minute",M:"month",months:"month",month:"month",Q:"quarter",quarters:"quarter",quarter:"quarter",s:"second",seconds:"second",second:"second",gg:"weekYear",weekyears:"weekYear",weekyear:"weekYear",GG:"isoWeekYear",isoweekyears:"isoWeekYear",isoweekyear:"isoWeekYear",w:"week",weeks:"week",week:"week",W:"isoWeek",isoweeks:"isoWeek",isoweek:"isoWeek",y:"year",years:"year",year:"year"};function Me(a){return typeof a=="string"?xe[a]||xe[a.toLowerCase()]:void 0}function R(a){var i={},l,o;for(o in a)m(a,o)&&(l=Me(o),l&&(i[l]=a[o]));return i}var re={date:9,day:11,weekday:11,isoWeekday:11,dayOfYear:4,hour:13,millisecond:16,minute:14,month:8,quarter:7,second:15,weekYear:1,isoWeekYear:1,week:5,isoWeek:5,year:1};function Qe(a){var i=[],l;for(l in a)m(a,l)&&i.push({unit:l,priority:re[l]});return i.sort(function(o,f){return o.priority-f.priority}),i}var Je=/\d/,He=/\d\d/,zs=/\d{3}/,Ja=/\d{4}/,Lt=/[+-]?\d{6}/,We=/\d\d?/,Rs=/\d\d\d\d?/,Es=/\d\d\d\d\d\d?/,Oa=/\d{1,3}/,Za=/\d{1,4}/,za=/[+-]?\d{1,6}/,ra=/\d+/,L=/[+-]?\d+/,ee=/Z|[+-]\d\d:?\d\d/gi,he=/Z|[+-]\d\d(?::?\d\d)?/gi,ve=/[+-]?\d+(\.\d{1,3})?/,rt=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i,Ae=/^[1-9]\d?/,vt=/^([1-9]\d|\d)/,na;na={};function se(a,i,l){na[a]=_e(i)?i:function(o,f){return o&&l?l:i}}function Xa(a,i){return m(na,a)?na[a](i._strict,i._locale):new RegExp(Ra(a))}function Ra(a){return Ot(a.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(i,l,o,f,v){return l||o||f||v}))}function Ot(a){return a.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function zt(a){return a<0?Math.ceil(a)||0:Math.floor(a)}function Ie(a){var i=+a,l=0;return i!==0&&isFinite(i)&&(l=zt(i)),l}var _r={};function $e(a,i){var l,o=i,f;for(typeof a=="string"&&(a=[a]),j(i)&&(o=function(v,E){E[i]=Ie(v)}),f=a.length,l=0;l<f;l++)_r[a[l]]=o}function Qa(a,i){$e(a,function(l,o,f,v){f._w=f._w||{},i(l,f._w,f,v)})}function Fc(a,i,l){i!=null&&m(_r,a)&&_r[a](i,l._a,l,a)}function Ds(a){return a%4===0&&a%100!==0||a%400===0}var ct=0,Ft=1,Nt=2,at=3,Dt=4,Ht=5,pa=6,Hc=7,Wc=8;Z("Y",0,0,function(){var a=this.year();return a<=9999?Y(a,4):"+"+a}),Z(0,["YY",2],0,function(){return this.year()%100}),Z(0,["YYYY",4],0,"year"),Z(0,["YYYYY",5],0,"year"),Z(0,["YYYYYY",6,!0],0,"year"),se("Y",L),se("YY",We,He),se("YYYY",Za,Ja),se("YYYYY",za,Lt),se("YYYYYY",za,Lt),$e(["YYYYY","YYYYYY"],ct),$e("YYYY",function(a,i){i[ct]=a.length===2?n.parseTwoDigitYear(a):Ie(a)}),$e("YY",function(a,i){i[ct]=n.parseTwoDigitYear(a)}),$e("Y",function(a,i){i[ct]=parseInt(a,10)});function es(a){return Ds(a)?366:365}n.parseTwoDigitYear=function(a){return Ie(a)+(Ie(a)>68?1900:2e3)};var Zi=Ea("FullYear",!0);function Uc(){return Ds(this.year())}function Ea(a,i){return function(l){return l!=null?(Xi(this,a,l),n.updateOffset(this,i),this):ts(this,a)}}function ts(a,i){if(!a.isValid())return NaN;var l=a._d,o=a._isUTC;switch(i){case"Milliseconds":return o?l.getUTCMilliseconds():l.getMilliseconds();case"Seconds":return o?l.getUTCSeconds():l.getSeconds();case"Minutes":return o?l.getUTCMinutes():l.getMinutes();case"Hours":return o?l.getUTCHours():l.getHours();case"Date":return o?l.getUTCDate():l.getDate();case"Day":return o?l.getUTCDay():l.getDay();case"Month":return o?l.getUTCMonth():l.getMonth();case"FullYear":return o?l.getUTCFullYear():l.getFullYear();default:return NaN}}function Xi(a,i,l){var o,f,v,E,ne;if(!(!a.isValid()||isNaN(l))){switch(o=a._d,f=a._isUTC,i){case"Milliseconds":return void(f?o.setUTCMilliseconds(l):o.setMilliseconds(l));case"Seconds":return void(f?o.setUTCSeconds(l):o.setSeconds(l));case"Minutes":return void(f?o.setUTCMinutes(l):o.setMinutes(l));case"Hours":return void(f?o.setUTCHours(l):o.setHours(l));case"Date":return void(f?o.setUTCDate(l):o.setDate(l));case"FullYear":break;default:return}v=l,E=a.month(),ne=a.date(),ne=ne===29&&E===1&&!Ds(v)?28:ne,f?o.setUTCFullYear(v,E,ne):o.setFullYear(v,E,ne)}}function Vc(a){return a=Me(a),_e(this[a])?this[a]():this}function Kc(a,i){if(typeof a=="object"){a=R(a);var l=Qe(a),o,f=l.length;for(o=0;o<f;o++)this[l[o].unit](a[l[o].unit])}else if(a=Me(a),_e(this[a]))return this[a](i);return this}function Gc(a,i){return(a%i+i)%i}var et;Array.prototype.indexOf?et=Array.prototype.indexOf:et=function(a){var i;for(i=0;i<this.length;++i)if(this[i]===a)return i;return-1};function Tr(a,i){if(isNaN(a)||isNaN(i))return NaN;var l=Gc(i,12);return a+=(i-l)/12,l===1?Ds(a)?29:28:31-l%7%2}Z("M",["MM",2],"Mo",function(){return this.month()+1}),Z("MMM",0,0,function(a){return this.localeData().monthsShort(this,a)}),Z("MMMM",0,0,function(a){return this.localeData().months(this,a)}),se("M",We,Ae),se("MM",We,He),se("MMM",function(a,i){return i.monthsShortRegex(a)}),se("MMMM",function(a,i){return i.monthsRegex(a)}),$e(["M","MM"],function(a,i){i[Ft]=Ie(a)-1}),$e(["MMM","MMMM"],function(a,i,l,o){var f=l._locale.monthsParse(a,o,l._strict);f!=null?i[Ft]=f:g(l).invalidMonth=a});var qc="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),Qi="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),el=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,Jc=rt,Zc=rt;function Xc(a,i){return a?c(this._months)?this._months[a.month()]:this._months[(this._months.isFormat||el).test(i)?"format":"standalone"][a.month()]:c(this._months)?this._months:this._months.standalone}function Qc(a,i){return a?c(this._monthsShort)?this._monthsShort[a.month()]:this._monthsShort[el.test(i)?"format":"standalone"][a.month()]:c(this._monthsShort)?this._monthsShort:this._monthsShort.standalone}function ed(a,i,l){var o,f,v,E=a.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],o=0;o<12;++o)v=_([2e3,o]),this._shortMonthsParse[o]=this.monthsShort(v,"").toLocaleLowerCase(),this._longMonthsParse[o]=this.months(v,"").toLocaleLowerCase();return l?i==="MMM"?(f=et.call(this._shortMonthsParse,E),f!==-1?f:null):(f=et.call(this._longMonthsParse,E),f!==-1?f:null):i==="MMM"?(f=et.call(this._shortMonthsParse,E),f!==-1?f:(f=et.call(this._longMonthsParse,E),f!==-1?f:null)):(f=et.call(this._longMonthsParse,E),f!==-1?f:(f=et.call(this._shortMonthsParse,E),f!==-1?f:null))}function td(a,i,l){var o,f,v;if(this._monthsParseExact)return ed.call(this,a,i,l);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),o=0;o<12;o++){if(f=_([2e3,o]),l&&!this._longMonthsParse[o]&&(this._longMonthsParse[o]=new RegExp("^"+this.months(f,"").replace(".","")+"$","i"),this._shortMonthsParse[o]=new RegExp("^"+this.monthsShort(f,"").replace(".","")+"$","i")),!l&&!this._monthsParse[o]&&(v="^"+this.months(f,"")+"|^"+this.monthsShort(f,""),this._monthsParse[o]=new RegExp(v.replace(".",""),"i")),l&&i==="MMMM"&&this._longMonthsParse[o].test(a))return o;if(l&&i==="MMM"&&this._shortMonthsParse[o].test(a))return o;if(!l&&this._monthsParse[o].test(a))return o}}function tl(a,i){if(!a.isValid())return a;if(typeof i=="string"){if(/^\d+$/.test(i))i=Ie(i);else if(i=a.localeData().monthsParse(i),!j(i))return a}var l=i,o=a.date();return o=o<29?o:Math.min(o,Tr(a.year(),l)),a._isUTC?a._d.setUTCMonth(l,o):a._d.setMonth(l,o),a}function al(a){return a!=null?(tl(this,a),n.updateOffset(this,!0),this):ts(this,"Month")}function ad(){return Tr(this.year(),this.month())}function sd(a){return this._monthsParseExact?(m(this,"_monthsRegex")||sl.call(this),a?this._monthsShortStrictRegex:this._monthsShortRegex):(m(this,"_monthsShortRegex")||(this._monthsShortRegex=Jc),this._monthsShortStrictRegex&&a?this._monthsShortStrictRegex:this._monthsShortRegex)}function rd(a){return this._monthsParseExact?(m(this,"_monthsRegex")||sl.call(this),a?this._monthsStrictRegex:this._monthsRegex):(m(this,"_monthsRegex")||(this._monthsRegex=Zc),this._monthsStrictRegex&&a?this._monthsStrictRegex:this._monthsRegex)}function sl(){function a(we,Pe){return Pe.length-we.length}var i=[],l=[],o=[],f,v,E,ne;for(f=0;f<12;f++)v=_([2e3,f]),E=Ot(this.monthsShort(v,"")),ne=Ot(this.months(v,"")),i.push(E),l.push(ne),o.push(ne),o.push(E);i.sort(a),l.sort(a),o.sort(a),this._monthsRegex=new RegExp("^("+o.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+l.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+i.join("|")+")","i")}function nd(a,i,l,o,f,v,E){var ne;return a<100&&a>=0?(ne=new Date(a+400,i,l,o,f,v,E),isFinite(ne.getFullYear())&&ne.setFullYear(a)):ne=new Date(a,i,l,o,f,v,E),ne}function as(a){var i,l;return a<100&&a>=0?(l=Array.prototype.slice.call(arguments),l[0]=a+400,i=new Date(Date.UTC.apply(null,l)),isFinite(i.getUTCFullYear())&&i.setUTCFullYear(a)):i=new Date(Date.UTC.apply(null,arguments)),i}function Ms(a,i,l){var o=7+i-l,f=(7+as(a,0,o).getUTCDay()-i)%7;return-f+o-1}function rl(a,i,l,o,f){var v=(7+l-o)%7,E=Ms(a,o,f),ne=1+7*(i-1)+v+E,we,Pe;return ne<=0?(we=a-1,Pe=es(we)+ne):ne>es(a)?(we=a+1,Pe=ne-es(a)):(we=a,Pe=ne),{year:we,dayOfYear:Pe}}function ss(a,i,l){var o=Ms(a.year(),i,l),f=Math.floor((a.dayOfYear()-o-1)/7)+1,v,E;return f<1?(E=a.year()-1,v=f+Wt(E,i,l)):f>Wt(a.year(),i,l)?(v=f-Wt(a.year(),i,l),E=a.year()+1):(E=a.year(),v=f),{week:v,year:E}}function Wt(a,i,l){var o=Ms(a,i,l),f=Ms(a+1,i,l);return(es(a)-o+f)/7}Z("w",["ww",2],"wo","week"),Z("W",["WW",2],"Wo","isoWeek"),se("w",We,Ae),se("ww",We,He),se("W",We,Ae),se("WW",We,He),Qa(["w","ww","W","WW"],function(a,i,l,o){i[o.substr(0,1)]=Ie(a)});function id(a){return ss(a,this._week.dow,this._week.doy).week}var ld={dow:0,doy:6};function od(){return this._week.dow}function cd(){return this._week.doy}function dd(a){var i=this.localeData().week(this);return a==null?i:this.add((a-i)*7,"d")}function ud(a){var i=ss(this,1,4).week;return a==null?i:this.add((a-i)*7,"d")}Z("d",0,"do","day"),Z("dd",0,0,function(a){return this.localeData().weekdaysMin(this,a)}),Z("ddd",0,0,function(a){return this.localeData().weekdaysShort(this,a)}),Z("dddd",0,0,function(a){return this.localeData().weekdays(this,a)}),Z("e",0,0,"weekday"),Z("E",0,0,"isoWeekday"),se("d",We),se("e",We),se("E",We),se("dd",function(a,i){return i.weekdaysMinRegex(a)}),se("ddd",function(a,i){return i.weekdaysShortRegex(a)}),se("dddd",function(a,i){return i.weekdaysRegex(a)}),Qa(["dd","ddd","dddd"],function(a,i,l,o){var f=l._locale.weekdaysParse(a,o,l._strict);f!=null?i.d=f:g(l).invalidWeekday=a}),Qa(["d","e","E"],function(a,i,l,o){i[o]=Ie(a)});function hd(a,i){return typeof a!="string"?a:isNaN(a)?(a=i.weekdaysParse(a),typeof a=="number"?a:null):parseInt(a,10)}function fd(a,i){return typeof a=="string"?i.weekdaysParse(a)%7||7:isNaN(a)?null:a}function Cr(a,i){return a.slice(i,7).concat(a.slice(0,i))}var md="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),nl="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),pd="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),gd=rt,xd=rt,yd=rt;function bd(a,i){var l=c(this._weekdays)?this._weekdays:this._weekdays[a&&a!==!0&&this._weekdays.isFormat.test(i)?"format":"standalone"];return a===!0?Cr(l,this._week.dow):a?l[a.day()]:l}function jd(a){return a===!0?Cr(this._weekdaysShort,this._week.dow):a?this._weekdaysShort[a.day()]:this._weekdaysShort}function vd(a){return a===!0?Cr(this._weekdaysMin,this._week.dow):a?this._weekdaysMin[a.day()]:this._weekdaysMin}function kd(a,i,l){var o,f,v,E=a.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],o=0;o<7;++o)v=_([2e3,1]).day(o),this._minWeekdaysParse[o]=this.weekdaysMin(v,"").toLocaleLowerCase(),this._shortWeekdaysParse[o]=this.weekdaysShort(v,"").toLocaleLowerCase(),this._weekdaysParse[o]=this.weekdays(v,"").toLocaleLowerCase();return l?i==="dddd"?(f=et.call(this._weekdaysParse,E),f!==-1?f:null):i==="ddd"?(f=et.call(this._shortWeekdaysParse,E),f!==-1?f:null):(f=et.call(this._minWeekdaysParse,E),f!==-1?f:null):i==="dddd"?(f=et.call(this._weekdaysParse,E),f!==-1||(f=et.call(this._shortWeekdaysParse,E),f!==-1)?f:(f=et.call(this._minWeekdaysParse,E),f!==-1?f:null)):i==="ddd"?(f=et.call(this._shortWeekdaysParse,E),f!==-1||(f=et.call(this._weekdaysParse,E),f!==-1)?f:(f=et.call(this._minWeekdaysParse,E),f!==-1?f:null)):(f=et.call(this._minWeekdaysParse,E),f!==-1||(f=et.call(this._weekdaysParse,E),f!==-1)?f:(f=et.call(this._shortWeekdaysParse,E),f!==-1?f:null))}function wd(a,i,l){var o,f,v;if(this._weekdaysParseExact)return kd.call(this,a,i,l);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),o=0;o<7;o++){if(f=_([2e3,1]).day(o),l&&!this._fullWeekdaysParse[o]&&(this._fullWeekdaysParse[o]=new RegExp("^"+this.weekdays(f,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[o]=new RegExp("^"+this.weekdaysShort(f,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[o]=new RegExp("^"+this.weekdaysMin(f,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[o]||(v="^"+this.weekdays(f,"")+"|^"+this.weekdaysShort(f,"")+"|^"+this.weekdaysMin(f,""),this._weekdaysParse[o]=new RegExp(v.replace(".",""),"i")),l&&i==="dddd"&&this._fullWeekdaysParse[o].test(a))return o;if(l&&i==="ddd"&&this._shortWeekdaysParse[o].test(a))return o;if(l&&i==="dd"&&this._minWeekdaysParse[o].test(a))return o;if(!l&&this._weekdaysParse[o].test(a))return o}}function Sd(a){if(!this.isValid())return a!=null?this:NaN;var i=ts(this,"Day");return a!=null?(a=hd(a,this.localeData()),this.add(a-i,"d")):i}function _d(a){if(!this.isValid())return a!=null?this:NaN;var i=(this.day()+7-this.localeData()._week.dow)%7;return a==null?i:this.add(a-i,"d")}function Td(a){if(!this.isValid())return a!=null?this:NaN;if(a!=null){var i=fd(a,this.localeData());return this.day(this.day()%7?i:i-7)}else return this.day()||7}function Cd(a){return this._weekdaysParseExact?(m(this,"_weekdaysRegex")||Or.call(this),a?this._weekdaysStrictRegex:this._weekdaysRegex):(m(this,"_weekdaysRegex")||(this._weekdaysRegex=gd),this._weekdaysStrictRegex&&a?this._weekdaysStrictRegex:this._weekdaysRegex)}function Od(a){return this._weekdaysParseExact?(m(this,"_weekdaysRegex")||Or.call(this),a?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(m(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=xd),this._weekdaysShortStrictRegex&&a?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)}function zd(a){return this._weekdaysParseExact?(m(this,"_weekdaysRegex")||Or.call(this),a?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(m(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=yd),this._weekdaysMinStrictRegex&&a?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)}function Or(){function a(ft,qt){return qt.length-ft.length}var i=[],l=[],o=[],f=[],v,E,ne,we,Pe;for(v=0;v<7;v++)E=_([2e3,1]).day(v),ne=Ot(this.weekdaysMin(E,"")),we=Ot(this.weekdaysShort(E,"")),Pe=Ot(this.weekdays(E,"")),i.push(ne),l.push(we),o.push(Pe),f.push(ne),f.push(we),f.push(Pe);i.sort(a),l.sort(a),o.sort(a),f.sort(a),this._weekdaysRegex=new RegExp("^("+f.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+o.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+l.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+i.join("|")+")","i")}function zr(){return this.hours()%12||12}function Rd(){return this.hours()||24}Z("H",["HH",2],0,"hour"),Z("h",["hh",2],0,zr),Z("k",["kk",2],0,Rd),Z("hmm",0,0,function(){return""+zr.apply(this)+Y(this.minutes(),2)}),Z("hmmss",0,0,function(){return""+zr.apply(this)+Y(this.minutes(),2)+Y(this.seconds(),2)}),Z("Hmm",0,0,function(){return""+this.hours()+Y(this.minutes(),2)}),Z("Hmmss",0,0,function(){return""+this.hours()+Y(this.minutes(),2)+Y(this.seconds(),2)});function il(a,i){Z(a,0,0,function(){return this.localeData().meridiem(this.hours(),this.minutes(),i)})}il("a",!0),il("A",!1);function ll(a,i){return i._meridiemParse}se("a",ll),se("A",ll),se("H",We,vt),se("h",We,Ae),se("k",We,Ae),se("HH",We,He),se("hh",We,He),se("kk",We,He),se("hmm",Rs),se("hmmss",Es),se("Hmm",Rs),se("Hmmss",Es),$e(["H","HH"],at),$e(["k","kk"],function(a,i,l){var o=Ie(a);i[at]=o===24?0:o}),$e(["a","A"],function(a,i,l){l._isPm=l._locale.isPM(a),l._meridiem=a}),$e(["h","hh"],function(a,i,l){i[at]=Ie(a),g(l).bigHour=!0}),$e("hmm",function(a,i,l){var o=a.length-2;i[at]=Ie(a.substr(0,o)),i[Dt]=Ie(a.substr(o)),g(l).bigHour=!0}),$e("hmmss",function(a,i,l){var o=a.length-4,f=a.length-2;i[at]=Ie(a.substr(0,o)),i[Dt]=Ie(a.substr(o,2)),i[Ht]=Ie(a.substr(f)),g(l).bigHour=!0}),$e("Hmm",function(a,i,l){var o=a.length-2;i[at]=Ie(a.substr(0,o)),i[Dt]=Ie(a.substr(o))}),$e("Hmmss",function(a,i,l){var o=a.length-4,f=a.length-2;i[at]=Ie(a.substr(0,o)),i[Dt]=Ie(a.substr(o,2)),i[Ht]=Ie(a.substr(f))});function Ed(a){return(a+"").toLowerCase().charAt(0)==="p"}var Dd=/[ap]\.?m?\.?/i,Md=Ea("Hours",!0);function Ad(a,i,l){return a>11?l?"pm":"PM":l?"am":"AM"}var ol={calendar:je,longDateFormat:$,invalidDate:Ye,ordinal:jt,dayOfMonthOrdinalParse:aa,relativeTime:sa,months:qc,monthsShort:Qi,week:ld,weekdays:md,weekdaysMin:pd,weekdaysShort:nl,meridiemParse:Dd},Ke={},rs={},ns;function Id(a,i){var l,o=Math.min(a.length,i.length);for(l=0;l<o;l+=1)if(a[l]!==i[l])return l;return o}function cl(a){return a&&a.toLowerCase().replace("_","-")}function Pd(a){for(var i=0,l,o,f,v;i<a.length;){for(v=cl(a[i]).split("-"),l=v.length,o=cl(a[i+1]),o=o?o.split("-"):null;l>0;){if(f=As(v.slice(0,l).join("-")),f)return f;if(o&&o.length>=l&&Id(v,o)>=l-1)break;l--}i++}return ns}function Ld(a){return!!(a&&a.match("^[^/\\\\]*$"))}function As(a){var i=null,l;if(Ke[a]===void 0&&t&&t.exports&&Ld(a))try{i=ns._abbr,l=Bc,l("./locale/"+a),ia(i)}catch(o){Ke[a]=null}return Ke[a]}function ia(a,i){var l;return a&&(p(i)?l=Ut(a):l=Rr(a,i),l&&(ns=l)),ns._abbr}function Rr(a,i){if(i!==null){var l,o=ol;if(i.abbr=a,Ke[a]!=null)ye("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),o=Ke[a]._config;else if(i.parentLocale!=null)if(Ke[i.parentLocale]!=null)o=Ke[i.parentLocale]._config;else if(l=As(i.parentLocale),l!=null)o=l._config;else return rs[i.parentLocale]||(rs[i.parentLocale]=[]),rs[i.parentLocale].push({name:a,config:i}),null;return Ke[a]=new F(A(o,i)),rs[a]&&rs[a].forEach(function(f){Rr(f.name,f.config)}),ia(a),Ke[a]}else return delete Ke[a],null}function Nd(a,i){if(i!=null){var l,o,f=ol;Ke[a]!=null&&Ke[a].parentLocale!=null?Ke[a].set(A(Ke[a]._config,i)):(o=As(a),o!=null&&(f=o._config),i=A(f,i),o==null&&(i.abbr=a),l=new F(i),l.parentLocale=Ke[a],Ke[a]=l),ia(a)}else Ke[a]!=null&&(Ke[a].parentLocale!=null?(Ke[a]=Ke[a].parentLocale,a===ia()&&ia(a)):Ke[a]!=null&&delete Ke[a]);return Ke[a]}function Ut(a){var i;if(a&&a._locale&&a._locale._abbr&&(a=a._locale._abbr),!a)return ns;if(!c(a)){if(i=As(a),i)return i;a=[a]}return Pd(a)}function Yd(){return ie(Ke)}function Er(a){var i,l=a._a;return l&&g(a).overflow===-2&&(i=l[Ft]<0||l[Ft]>11?Ft:l[Nt]<1||l[Nt]>Tr(l[ct],l[Ft])?Nt:l[at]<0||l[at]>24||l[at]===24&&(l[Dt]!==0||l[Ht]!==0||l[pa]!==0)?at:l[Dt]<0||l[Dt]>59?Dt:l[Ht]<0||l[Ht]>59?Ht:l[pa]<0||l[pa]>999?pa:-1,g(a)._overflowDayOfYear&&(i<ct||i>Nt)&&(i=Nt),g(a)._overflowWeeks&&i===-1&&(i=Hc),g(a)._overflowWeekday&&i===-1&&(i=Wc),g(a).overflow=i),a}var $d=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,Bd=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d|))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,Fd=/Z|[+-]\d\d(?::?\d\d)?/,Is=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/],["YYYYMM",/\d{6}/,!1],["YYYY",/\d{4}/,!1]],Dr=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],Hd=/^\/?Date\((-?\d+)/i,Wd=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/,Ud={UT:0,GMT:0,EDT:-4*60,EST:-5*60,CDT:-5*60,CST:-6*60,MDT:-6*60,MST:-7*60,PDT:-7*60,PST:-8*60};function dl(a){var i,l,o=a._i,f=$d.exec(o)||Bd.exec(o),v,E,ne,we,Pe=Is.length,ft=Dr.length;if(f){for(g(a).iso=!0,i=0,l=Pe;i<l;i++)if(Is[i][1].exec(f[1])){E=Is[i][0],v=Is[i][2]!==!1;break}if(E==null){a._isValid=!1;return}if(f[3]){for(i=0,l=ft;i<l;i++)if(Dr[i][1].exec(f[3])){ne=(f[2]||" ")+Dr[i][0];break}if(ne==null){a._isValid=!1;return}}if(!v&&ne!=null){a._isValid=!1;return}if(f[4])if(Fd.exec(f[4]))we="Z";else{a._isValid=!1;return}a._f=E+(ne||"")+(we||""),Ar(a)}else a._isValid=!1}function Vd(a,i,l,o,f,v){var E=[Kd(a),Qi.indexOf(i),parseInt(l,10),parseInt(o,10),parseInt(f,10)];return v&&E.push(parseInt(v,10)),E}function Kd(a){var i=parseInt(a,10);return i<=49?2e3+i:i<=999?1900+i:i}function Gd(a){return a.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,"")}function qd(a,i,l){if(a){var o=nl.indexOf(a),f=new Date(i[0],i[1],i[2]).getDay();if(o!==f)return g(l).weekdayMismatch=!0,l._isValid=!1,!1}return!0}function Jd(a,i,l){if(a)return Ud[a];if(i)return 0;var o=parseInt(l,10),f=o%100,v=(o-f)/100;return v*60+f}function ul(a){var i=Wd.exec(Gd(a._i)),l;if(i){if(l=Vd(i[4],i[3],i[2],i[5],i[6],i[7]),!qd(i[1],l,a))return;a._a=l,a._tzm=Jd(i[8],i[9],i[10]),a._d=as.apply(null,a._a),a._d.setUTCMinutes(a._d.getUTCMinutes()-a._tzm),g(a).rfc2822=!0}else a._isValid=!1}function Zd(a){var i=Hd.exec(a._i);if(i!==null){a._d=new Date(+i[1]);return}if(dl(a),a._isValid===!1)delete a._isValid;else return;if(ul(a),a._isValid===!1)delete a._isValid;else return;a._strict?a._isValid=!1:n.createFromInputFallback(a)}n.createFromInputFallback=oe("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",function(a){a._d=new Date(a._i+(a._useUTC?" UTC":""))});function Da(a,i,l){return a!=null?a:i!=null?i:l}function Xd(a){var i=new Date(n.now());return a._useUTC?[i.getUTCFullYear(),i.getUTCMonth(),i.getUTCDate()]:[i.getFullYear(),i.getMonth(),i.getDate()]}function Mr(a){var i,l,o=[],f,v,E;if(!a._d){for(f=Xd(a),a._w&&a._a[Nt]==null&&a._a[Ft]==null&&Qd(a),a._dayOfYear!=null&&(E=Da(a._a[ct],f[ct]),(a._dayOfYear>es(E)||a._dayOfYear===0)&&(g(a)._overflowDayOfYear=!0),l=as(E,0,a._dayOfYear),a._a[Ft]=l.getUTCMonth(),a._a[Nt]=l.getUTCDate()),i=0;i<3&&a._a[i]==null;++i)a._a[i]=o[i]=f[i];for(;i<7;i++)a._a[i]=o[i]=a._a[i]==null?i===2?1:0:a._a[i];a._a[at]===24&&a._a[Dt]===0&&a._a[Ht]===0&&a._a[pa]===0&&(a._nextDay=!0,a._a[at]=0),a._d=(a._useUTC?as:nd).apply(null,o),v=a._useUTC?a._d.getUTCDay():a._d.getDay(),a._tzm!=null&&a._d.setUTCMinutes(a._d.getUTCMinutes()-a._tzm),a._nextDay&&(a._a[at]=24),a._w&&typeof a._w.d!="undefined"&&a._w.d!==v&&(g(a).weekdayMismatch=!0)}}function Qd(a){var i,l,o,f,v,E,ne,we,Pe;i=a._w,i.GG!=null||i.W!=null||i.E!=null?(v=1,E=4,l=Da(i.GG,a._a[ct],ss(Ue(),1,4).year),o=Da(i.W,1),f=Da(i.E,1),(f<1||f>7)&&(we=!0)):(v=a._locale._week.dow,E=a._locale._week.doy,Pe=ss(Ue(),v,E),l=Da(i.gg,a._a[ct],Pe.year),o=Da(i.w,Pe.week),i.d!=null?(f=i.d,(f<0||f>6)&&(we=!0)):i.e!=null?(f=i.e+v,(i.e<0||i.e>6)&&(we=!0)):f=v),o<1||o>Wt(l,v,E)?g(a)._overflowWeeks=!0:we!=null?g(a)._overflowWeekday=!0:(ne=rl(l,o,f,v,E),a._a[ct]=ne.year,a._dayOfYear=ne.dayOfYear)}n.ISO_8601=function(){},n.RFC_2822=function(){};function Ar(a){if(a._f===n.ISO_8601){dl(a);return}if(a._f===n.RFC_2822){ul(a);return}a._a=[],g(a).empty=!0;var i=""+a._i,l,o,f,v,E,ne=i.length,we=0,Pe,ft;for(f=ge(a._f,a._locale).match(pe)||[],ft=f.length,l=0;l<ft;l++)v=f[l],o=(i.match(Xa(v,a))||[])[0],o&&(E=i.substr(0,i.indexOf(o)),E.length>0&&g(a).unusedInput.push(E),i=i.slice(i.indexOf(o)+o.length),we+=o.length),Ee[v]?(o?g(a).empty=!1:g(a).unusedTokens.push(v),Fc(v,o,a)):a._strict&&!o&&g(a).unusedTokens.push(v);g(a).charsLeftOver=ne-we,i.length>0&&g(a).unusedInput.push(i),a._a[at]<=12&&g(a).bigHour===!0&&a._a[at]>0&&(g(a).bigHour=void 0),g(a).parsedDateParts=a._a.slice(0),g(a).meridiem=a._meridiem,a._a[at]=eu(a._locale,a._a[at],a._meridiem),Pe=g(a).era,Pe!==null&&(a._a[ct]=a._locale.erasConvertYear(Pe,a._a[ct])),Mr(a),Er(a)}function eu(a,i,l){var o;return l==null?i:a.meridiemHour!=null?a.meridiemHour(i,l):(a.isPM!=null&&(o=a.isPM(l),o&&i<12&&(i+=12),!o&&i===12&&(i=0)),i)}function tu(a){var i,l,o,f,v,E,ne=!1,we=a._f.length;if(we===0){g(a).invalidFormat=!0,a._d=new Date(NaN);return}for(f=0;f<we;f++)v=0,E=!1,i=J({},a),a._useUTC!=null&&(i._useUTC=a._useUTC),i._f=a._f[f],Ar(i),U(i)&&(E=!0),v+=g(i).charsLeftOver,v+=g(i).unusedTokens.length*10,g(i).score=v,ne?v<o&&(o=v,l=i):(o==null||v<o||E)&&(o=v,l=i,E&&(ne=!0));C(a,l||i)}function au(a){if(!a._d){var i=R(a._i),l=i.day===void 0?i.date:i.day;a._a=b([i.year,i.month,l,i.hour,i.minute,i.second,i.millisecond],function(o){return o&&parseInt(o,10)}),Mr(a)}}function su(a){var i=new B(Er(hl(a)));return i._nextDay&&(i.add(1,"d"),i._nextDay=void 0),i}function hl(a){var i=a._i,l=a._f;return a._locale=a._locale||Ut(a._l),i===null||l===void 0&&i===""?T({nullInput:!0}):(typeof i=="string"&&(a._i=i=a._locale.preparse(i)),M(i)?new B(Er(i)):(x(i)?a._d=i:c(l)?tu(a):l?Ar(a):ru(a),U(a)||(a._d=null),a))}function ru(a){var i=a._i;p(i)?a._d=new Date(n.now()):x(i)?a._d=new Date(i.valueOf()):typeof i=="string"?Zd(a):c(i)?(a._a=b(i.slice(0),function(l){return parseInt(l,10)}),Mr(a)):h(i)?au(a):j(i)?a._d=new Date(i):n.createFromInputFallback(a)}function fl(a,i,l,o,f){var v={};return(i===!0||i===!1)&&(o=i,i=void 0),(l===!0||l===!1)&&(o=l,l=void 0),(h(a)&&y(a)||c(a)&&a.length===0)&&(a=void 0),v._isAMomentObject=!0,v._useUTC=v._isUTC=f,v._l=l,v._i=a,v._f=i,v._strict=o,su(v)}function Ue(a,i,l,o){return fl(a,i,l,o,!1)}var nu=oe("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var a=Ue.apply(null,arguments);return this.isValid()&&a.isValid()?a<this?this:a:T()}),iu=oe("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var a=Ue.apply(null,arguments);return this.isValid()&&a.isValid()?a>this?this:a:T()});function ml(a,i){var l,o;if(i.length===1&&c(i[0])&&(i=i[0]),!i.length)return Ue();for(l=i[0],o=1;o<i.length;++o)(!i[o].isValid()||i[o][a](l))&&(l=i[o]);return l}function lu(){var a=[].slice.call(arguments,0);return ml("isBefore",a)}function ou(){var a=[].slice.call(arguments,0);return ml("isAfter",a)}var cu=function(){return Date.now?Date.now():+new Date},is=["year","quarter","month","week","day","hour","minute","second","millisecond"];function du(a){var i,l=!1,o,f=is.length;for(i in a)if(m(a,i)&&!(et.call(is,i)!==-1&&(a[i]==null||!isNaN(a[i]))))return!1;for(o=0;o<f;++o)if(a[is[o]]){if(l)return!1;parseFloat(a[is[o]])!==Ie(a[is[o]])&&(l=!0)}return!0}function uu(){return this._isValid}function hu(){return Mt(NaN)}function Ps(a){var i=R(a),l=i.year||0,o=i.quarter||0,f=i.month||0,v=i.week||i.isoWeek||0,E=i.day||0,ne=i.hour||0,we=i.minute||0,Pe=i.second||0,ft=i.millisecond||0;this._isValid=du(i),this._milliseconds=+ft+Pe*1e3+we*6e4+ne*1e3*60*60,this._days=+E+v*7,this._months=+f+o*3+l*12,this._data={},this._locale=Ut(),this._bubble()}function Ls(a){return a instanceof Ps}function Ir(a){return a<0?Math.round(-1*a)*-1:Math.round(a)}function fu(a,i,l){var o=Math.min(a.length,i.length),f=Math.abs(a.length-i.length),v=0,E;for(E=0;E<o;E++)Ie(a[E])!==Ie(i[E])&&v++;return v+f}function pl(a,i){Z(a,0,0,function(){var l=this.utcOffset(),o="+";return l<0&&(l=-l,o="-"),o+Y(~~(l/60),2)+i+Y(~~l%60,2)})}pl("Z",":"),pl("ZZ",""),se("Z",he),se("ZZ",he),$e(["Z","ZZ"],function(a,i,l){l._useUTC=!0,l._tzm=Pr(he,a)});var mu=/([\+\-]|\d\d)/gi;function Pr(a,i){var l=(i||"").match(a),o,f,v;return l===null?null:(o=l[l.length-1]||[],f=(o+"").match(mu)||["-",0,0],v=+(f[1]*60)+Ie(f[2]),v===0?0:f[0]==="+"?v:-v)}function Lr(a,i){var l,o;return i._isUTC?(l=i.clone(),o=(M(a)||x(a)?a.valueOf():Ue(a).valueOf())-l.valueOf(),l._d.setTime(l._d.valueOf()+o),n.updateOffset(l,!1),l):Ue(a).local()}function Nr(a){return-Math.round(a._d.getTimezoneOffset())}n.updateOffset=function(){};function pu(a,i,l){var o=this._offset||0,f;if(!this.isValid())return a!=null?this:NaN;if(a!=null){if(typeof a=="string"){if(a=Pr(he,a),a===null)return this}else Math.abs(a)<16&&!l&&(a=a*60);return!this._isUTC&&i&&(f=Nr(this)),this._offset=a,this._isUTC=!0,f!=null&&this.add(f,"m"),o!==a&&(!i||this._changeInProgress?bl(this,Mt(a-o,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,n.updateOffset(this,!0),this._changeInProgress=null)),this}else return this._isUTC?o:Nr(this)}function gu(a,i){return a!=null?(typeof a!="string"&&(a=-a),this.utcOffset(a,i),this):-this.utcOffset()}function xu(a){return this.utcOffset(0,a)}function yu(a){return this._isUTC&&(this.utcOffset(0,a),this._isUTC=!1,a&&this.subtract(Nr(this),"m")),this}function bu(){if(this._tzm!=null)this.utcOffset(this._tzm,!1,!0);else if(typeof this._i=="string"){var a=Pr(ee,this._i);a!=null?this.utcOffset(a):this.utcOffset(0,!0)}return this}function ju(a){return this.isValid()?(a=a?Ue(a).utcOffset():0,(this.utcOffset()-a)%60===0):!1}function vu(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()}function ku(){if(!p(this._isDSTShifted))return this._isDSTShifted;var a={},i;return J(a,this),a=hl(a),a._a?(i=a._isUTC?_(a._a):Ue(a._a),this._isDSTShifted=this.isValid()&&fu(a._a,i.toArray())>0):this._isDSTShifted=!1,this._isDSTShifted}function wu(){return this.isValid()?!this._isUTC:!1}function Su(){return this.isValid()?this._isUTC:!1}function gl(){return this.isValid()?this._isUTC&&this._offset===0:!1}var _u=/^(-|\+)?(?:(\d*)[. ])?(\d+):(\d+)(?::(\d+)(\.\d*)?)?$/,Tu=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;function Mt(a,i){var l=a,o=null,f,v,E;return Ls(a)?l={ms:a._milliseconds,d:a._days,M:a._months}:j(a)||!isNaN(+a)?(l={},i?l[i]=+a:l.milliseconds=+a):(o=_u.exec(a))?(f=o[1]==="-"?-1:1,l={y:0,d:Ie(o[Nt])*f,h:Ie(o[at])*f,m:Ie(o[Dt])*f,s:Ie(o[Ht])*f,ms:Ie(Ir(o[pa]*1e3))*f}):(o=Tu.exec(a))?(f=o[1]==="-"?-1:1,l={y:ga(o[2],f),M:ga(o[3],f),w:ga(o[4],f),d:ga(o[5],f),h:ga(o[6],f),m:ga(o[7],f),s:ga(o[8],f)}):l==null?l={}:typeof l=="object"&&("from"in l||"to"in l)&&(E=Cu(Ue(l.from),Ue(l.to)),l={},l.ms=E.milliseconds,l.M=E.months),v=new Ps(l),Ls(a)&&m(a,"_locale")&&(v._locale=a._locale),Ls(a)&&m(a,"_isValid")&&(v._isValid=a._isValid),v}Mt.fn=Ps.prototype,Mt.invalid=hu;function ga(a,i){var l=a&&parseFloat(a.replace(",","."));return(isNaN(l)?0:l)*i}function xl(a,i){var l={};return l.months=i.month()-a.month()+(i.year()-a.year())*12,a.clone().add(l.months,"M").isAfter(i)&&--l.months,l.milliseconds=+i-+a.clone().add(l.months,"M"),l}function Cu(a,i){var l;return a.isValid()&&i.isValid()?(i=Lr(i,a),a.isBefore(i)?l=xl(a,i):(l=xl(i,a),l.milliseconds=-l.milliseconds,l.months=-l.months),l):{milliseconds:0,months:0}}function yl(a,i){return function(l,o){var f,v;return o!==null&&!isNaN(+o)&&(ye(i,"moment()."+i+"(period, number) is deprecated. Please use moment()."+i+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),v=l,l=o,o=v),f=Mt(l,o),bl(this,f,a),this}}function bl(a,i,l,o){var f=i._milliseconds,v=Ir(i._days),E=Ir(i._months);a.isValid()&&(o=o==null?!0:o,E&&tl(a,ts(a,"Month")+E*l),v&&Xi(a,"Date",ts(a,"Date")+v*l),f&&a._d.setTime(a._d.valueOf()+f*l),o&&n.updateOffset(a,v||E))}var Ou=yl(1,"add"),zu=yl(-1,"subtract");function jl(a){return typeof a=="string"||a instanceof String}function Ru(a){return M(a)||x(a)||jl(a)||j(a)||Du(a)||Eu(a)||a===null||a===void 0}function Eu(a){var i=h(a)&&!y(a),l=!1,o=["years","year","y","months","month","M","days","day","d","dates","date","D","hours","hour","h","minutes","minute","m","seconds","second","s","milliseconds","millisecond","ms"],f,v,E=o.length;for(f=0;f<E;f+=1)v=o[f],l=l||m(a,v);return i&&l}function Du(a){var i=c(a),l=!1;return i&&(l=a.filter(function(o){return!j(o)&&jl(a)}).length===0),i&&l}function Mu(a){var i=h(a)&&!y(a),l=!1,o=["sameDay","nextDay","lastDay","nextWeek","lastWeek","sameElse"],f,v;for(f=0;f<o.length;f+=1)v=o[f],l=l||m(a,v);return i&&l}function Au(a,i){var l=a.diff(i,"days",!0);return l<-6?"sameElse":l<-1?"lastWeek":l<0?"lastDay":l<1?"sameDay":l<2?"nextDay":l<7?"nextWeek":"sameElse"}function Iu(a,i){arguments.length===1&&(arguments[0]?Ru(arguments[0])?(a=arguments[0],i=void 0):Mu(arguments[0])&&(i=arguments[0],a=void 0):(a=void 0,i=void 0));var l=a||Ue(),o=Lr(l,this).startOf("day"),f=n.calendarFormat(this,o)||"sameElse",v=i&&(_e(i[f])?i[f].call(this,l):i[f]);return this.format(v||this.localeData().calendar(f,this,Ue(l)))}function Pu(){return new B(this)}function Lu(a,i){var l=M(a)?a:Ue(a);return this.isValid()&&l.isValid()?(i=Me(i)||"millisecond",i==="millisecond"?this.valueOf()>l.valueOf():l.valueOf()<this.clone().startOf(i).valueOf()):!1}function Nu(a,i){var l=M(a)?a:Ue(a);return this.isValid()&&l.isValid()?(i=Me(i)||"millisecond",i==="millisecond"?this.valueOf()<l.valueOf():this.clone().endOf(i).valueOf()<l.valueOf()):!1}function Yu(a,i,l,o){var f=M(a)?a:Ue(a),v=M(i)?i:Ue(i);return this.isValid()&&f.isValid()&&v.isValid()?(o=o||"()",(o[0]==="("?this.isAfter(f,l):!this.isBefore(f,l))&&(o[1]===")"?this.isBefore(v,l):!this.isAfter(v,l))):!1}function $u(a,i){var l=M(a)?a:Ue(a),o;return this.isValid()&&l.isValid()?(i=Me(i)||"millisecond",i==="millisecond"?this.valueOf()===l.valueOf():(o=l.valueOf(),this.clone().startOf(i).valueOf()<=o&&o<=this.clone().endOf(i).valueOf())):!1}function Bu(a,i){return this.isSame(a,i)||this.isAfter(a,i)}function Fu(a,i){return this.isSame(a,i)||this.isBefore(a,i)}function Hu(a,i,l){var o,f,v;if(!this.isValid())return NaN;if(o=Lr(a,this),!o.isValid())return NaN;switch(f=(o.utcOffset()-this.utcOffset())*6e4,i=Me(i),i){case"year":v=Ns(this,o)/12;break;case"month":v=Ns(this,o);break;case"quarter":v=Ns(this,o)/3;break;case"second":v=(this-o)/1e3;break;case"minute":v=(this-o)/6e4;break;case"hour":v=(this-o)/36e5;break;case"day":v=(this-o-f)/864e5;break;case"week":v=(this-o-f)/6048e5;break;default:v=this-o}return l?v:zt(v)}function Ns(a,i){if(a.date()<i.date())return-Ns(i,a);var l=(i.year()-a.year())*12+(i.month()-a.month()),o=a.clone().add(l,"months"),f,v;return i-o<0?(f=a.clone().add(l-1,"months"),v=(i-o)/(o-f)):(f=a.clone().add(l+1,"months"),v=(i-o)/(f-o)),-(l+v)||0}n.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",n.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]";function Wu(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")}function Uu(a){if(!this.isValid())return null;var i=a!==!0,l=i?this.clone().utc():this;return l.year()<0||l.year()>9999?k(l,i?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):_e(Date.prototype.toISOString)?i?this.toDate().toISOString():new Date(this.valueOf()+this.utcOffset()*60*1e3).toISOString().replace("Z",k(l,"Z")):k(l,i?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ")}function Vu(){if(!this.isValid())return"moment.invalid(/* "+this._i+" */)";var a="moment",i="",l,o,f,v;return this.isLocal()||(a=this.utcOffset()===0?"moment.utc":"moment.parseZone",i="Z"),l="["+a+'("]',o=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY",f="-MM-DD[T]HH:mm:ss.SSS",v=i+'[")]',this.format(l+o+f+v)}function Ku(a){a||(a=this.isUtc()?n.defaultFormatUtc:n.defaultFormat);var i=k(this,a);return this.localeData().postformat(i)}function Gu(a,i){return this.isValid()&&(M(a)&&a.isValid()||Ue(a).isValid())?Mt({to:this,from:a}).locale(this.locale()).humanize(!i):this.localeData().invalidDate()}function qu(a){return this.from(Ue(),a)}function Ju(a,i){return this.isValid()&&(M(a)&&a.isValid()||Ue(a).isValid())?Mt({from:this,to:a}).locale(this.locale()).humanize(!i):this.localeData().invalidDate()}function Zu(a){return this.to(Ue(),a)}function vl(a){var i;return a===void 0?this._locale._abbr:(i=Ut(a),i!=null&&(this._locale=i),this)}var kl=oe("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",function(a){return a===void 0?this.localeData():this.locale(a)});function wl(){return this._locale}var Ys=1e3,Ma=60*Ys,$s=60*Ma,Sl=(365*400+97)*24*$s;function Aa(a,i){return(a%i+i)%i}function _l(a,i,l){return a<100&&a>=0?new Date(a+400,i,l)-Sl:new Date(a,i,l).valueOf()}function Tl(a,i,l){return a<100&&a>=0?Date.UTC(a+400,i,l)-Sl:Date.UTC(a,i,l)}function Xu(a){var i,l;if(a=Me(a),a===void 0||a==="millisecond"||!this.isValid())return this;switch(l=this._isUTC?Tl:_l,a){case"year":i=l(this.year(),0,1);break;case"quarter":i=l(this.year(),this.month()-this.month()%3,1);break;case"month":i=l(this.year(),this.month(),1);break;case"week":i=l(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":i=l(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":i=l(this.year(),this.month(),this.date());break;case"hour":i=this._d.valueOf(),i-=Aa(i+(this._isUTC?0:this.utcOffset()*Ma),$s);break;case"minute":i=this._d.valueOf(),i-=Aa(i,Ma);break;case"second":i=this._d.valueOf(),i-=Aa(i,Ys);break}return this._d.setTime(i),n.updateOffset(this,!0),this}function Qu(a){var i,l;if(a=Me(a),a===void 0||a==="millisecond"||!this.isValid())return this;switch(l=this._isUTC?Tl:_l,a){case"year":i=l(this.year()+1,0,1)-1;break;case"quarter":i=l(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":i=l(this.year(),this.month()+1,1)-1;break;case"week":i=l(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":i=l(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":i=l(this.year(),this.month(),this.date()+1)-1;break;case"hour":i=this._d.valueOf(),i+=$s-Aa(i+(this._isUTC?0:this.utcOffset()*Ma),$s)-1;break;case"minute":i=this._d.valueOf(),i+=Ma-Aa(i,Ma)-1;break;case"second":i=this._d.valueOf(),i+=Ys-Aa(i,Ys)-1;break}return this._d.setTime(i),n.updateOffset(this,!0),this}function eh(){return this._d.valueOf()-(this._offset||0)*6e4}function th(){return Math.floor(this.valueOf()/1e3)}function ah(){return new Date(this.valueOf())}function sh(){var a=this;return[a.year(),a.month(),a.date(),a.hour(),a.minute(),a.second(),a.millisecond()]}function rh(){var a=this;return{years:a.year(),months:a.month(),date:a.date(),hours:a.hours(),minutes:a.minutes(),seconds:a.seconds(),milliseconds:a.milliseconds()}}function nh(){return this.isValid()?this.toISOString():null}function ih(){return U(this)}function lh(){return C({},g(this))}function oh(){return g(this).overflow}function ch(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}}Z("N",0,0,"eraAbbr"),Z("NN",0,0,"eraAbbr"),Z("NNN",0,0,"eraAbbr"),Z("NNNN",0,0,"eraName"),Z("NNNNN",0,0,"eraNarrow"),Z("y",["y",1],"yo","eraYear"),Z("y",["yy",2],0,"eraYear"),Z("y",["yyy",3],0,"eraYear"),Z("y",["yyyy",4],0,"eraYear"),se("N",Yr),se("NN",Yr),se("NNN",Yr),se("NNNN",jh),se("NNNNN",vh),$e(["N","NN","NNN","NNNN","NNNNN"],function(a,i,l,o){var f=l._locale.erasParse(a,o,l._strict);f?g(l).era=f:g(l).invalidEra=a}),se("y",ra),se("yy",ra),se("yyy",ra),se("yyyy",ra),se("yo",kh),$e(["y","yy","yyy","yyyy"],ct),$e(["yo"],function(a,i,l,o){var f;l._locale._eraYearOrdinalRegex&&(f=a.match(l._locale._eraYearOrdinalRegex)),l._locale.eraYearOrdinalParse?i[ct]=l._locale.eraYearOrdinalParse(a,f):i[ct]=parseInt(a,10)});function dh(a,i){var l,o,f,v=this._eras||Ut("en")._eras;for(l=0,o=v.length;l<o;++l){switch(typeof v[l].since){case"string":f=n(v[l].since).startOf("day"),v[l].since=f.valueOf();break}switch(typeof v[l].until){case"undefined":v[l].until=1/0;break;case"string":f=n(v[l].until).startOf("day").valueOf(),v[l].until=f.valueOf();break}}return v}function uh(a,i,l){var o,f,v=this.eras(),E,ne,we;for(a=a.toUpperCase(),o=0,f=v.length;o<f;++o)if(E=v[o].name.toUpperCase(),ne=v[o].abbr.toUpperCase(),we=v[o].narrow.toUpperCase(),l)switch(i){case"N":case"NN":case"NNN":if(ne===a)return v[o];break;case"NNNN":if(E===a)return v[o];break;case"NNNNN":if(we===a)return v[o];break}else if([E,ne,we].indexOf(a)>=0)return v[o]}function hh(a,i){var l=a.since<=a.until?1:-1;return i===void 0?n(a.since).year():n(a.since).year()+(i-a.offset)*l}function fh(){var a,i,l,o=this.localeData().eras();for(a=0,i=o.length;a<i;++a)if(l=this.clone().startOf("day").valueOf(),o[a].since<=l&&l<=o[a].until||o[a].until<=l&&l<=o[a].since)return o[a].name;return""}function mh(){var a,i,l,o=this.localeData().eras();for(a=0,i=o.length;a<i;++a)if(l=this.clone().startOf("day").valueOf(),o[a].since<=l&&l<=o[a].until||o[a].until<=l&&l<=o[a].since)return o[a].narrow;return""}function ph(){var a,i,l,o=this.localeData().eras();for(a=0,i=o.length;a<i;++a)if(l=this.clone().startOf("day").valueOf(),o[a].since<=l&&l<=o[a].until||o[a].until<=l&&l<=o[a].since)return o[a].abbr;return""}function gh(){var a,i,l,o,f=this.localeData().eras();for(a=0,i=f.length;a<i;++a)if(l=f[a].since<=f[a].until?1:-1,o=this.clone().startOf("day").valueOf(),f[a].since<=o&&o<=f[a].until||f[a].until<=o&&o<=f[a].since)return(this.year()-n(f[a].since).year())*l+f[a].offset;return this.year()}function xh(a){return m(this,"_erasNameRegex")||$r.call(this),a?this._erasNameRegex:this._erasRegex}function yh(a){return m(this,"_erasAbbrRegex")||$r.call(this),a?this._erasAbbrRegex:this._erasRegex}function bh(a){return m(this,"_erasNarrowRegex")||$r.call(this),a?this._erasNarrowRegex:this._erasRegex}function Yr(a,i){return i.erasAbbrRegex(a)}function jh(a,i){return i.erasNameRegex(a)}function vh(a,i){return i.erasNarrowRegex(a)}function kh(a,i){return i._eraYearOrdinalRegex||ra}function $r(){var a=[],i=[],l=[],o=[],f,v,E,ne,we,Pe=this.eras();for(f=0,v=Pe.length;f<v;++f)E=Ot(Pe[f].name),ne=Ot(Pe[f].abbr),we=Ot(Pe[f].narrow),i.push(E),a.push(ne),l.push(we),o.push(E),o.push(ne),o.push(we);this._erasRegex=new RegExp("^("+o.join("|")+")","i"),this._erasNameRegex=new RegExp("^("+i.join("|")+")","i"),this._erasAbbrRegex=new RegExp("^("+a.join("|")+")","i"),this._erasNarrowRegex=new RegExp("^("+l.join("|")+")","i")}Z(0,["gg",2],0,function(){return this.weekYear()%100}),Z(0,["GG",2],0,function(){return this.isoWeekYear()%100});function Bs(a,i){Z(0,[a,a.length],0,i)}Bs("gggg","weekYear"),Bs("ggggg","weekYear"),Bs("GGGG","isoWeekYear"),Bs("GGGGG","isoWeekYear"),se("G",L),se("g",L),se("GG",We,He),se("gg",We,He),se("GGGG",Za,Ja),se("gggg",Za,Ja),se("GGGGG",za,Lt),se("ggggg",za,Lt),Qa(["gggg","ggggg","GGGG","GGGGG"],function(a,i,l,o){i[o.substr(0,2)]=Ie(a)}),Qa(["gg","GG"],function(a,i,l,o){i[o]=n.parseTwoDigitYear(a)});function wh(a){return Cl.call(this,a,this.week(),this.weekday()+this.localeData()._week.dow,this.localeData()._week.dow,this.localeData()._week.doy)}function Sh(a){return Cl.call(this,a,this.isoWeek(),this.isoWeekday(),1,4)}function _h(){return Wt(this.year(),1,4)}function Th(){return Wt(this.isoWeekYear(),1,4)}function Ch(){var a=this.localeData()._week;return Wt(this.year(),a.dow,a.doy)}function Oh(){var a=this.localeData()._week;return Wt(this.weekYear(),a.dow,a.doy)}function Cl(a,i,l,o,f){var v;return a==null?ss(this,o,f).year:(v=Wt(a,o,f),i>v&&(i=v),zh.call(this,a,i,l,o,f))}function zh(a,i,l,o,f){var v=rl(a,i,l,o,f),E=as(v.year,0,v.dayOfYear);return this.year(E.getUTCFullYear()),this.month(E.getUTCMonth()),this.date(E.getUTCDate()),this}Z("Q",0,"Qo","quarter"),se("Q",Je),$e("Q",function(a,i){i[Ft]=(Ie(a)-1)*3});function Rh(a){return a==null?Math.ceil((this.month()+1)/3):this.month((a-1)*3+this.month()%3)}Z("D",["DD",2],"Do","date"),se("D",We,Ae),se("DD",We,He),se("Do",function(a,i){return a?i._dayOfMonthOrdinalParse||i._ordinalParse:i._dayOfMonthOrdinalParseLenient}),$e(["D","DD"],Nt),$e("Do",function(a,i){i[Nt]=Ie(a.match(We)[0])});var Ol=Ea("Date",!0);Z("DDD",["DDDD",3],"DDDo","dayOfYear"),se("DDD",Oa),se("DDDD",zs),$e(["DDD","DDDD"],function(a,i,l){l._dayOfYear=Ie(a)});function Eh(a){var i=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return a==null?i:this.add(a-i,"d")}Z("m",["mm",2],0,"minute"),se("m",We,vt),se("mm",We,He),$e(["m","mm"],Dt);var Dh=Ea("Minutes",!1);Z("s",["ss",2],0,"second"),se("s",We,vt),se("ss",We,He),$e(["s","ss"],Ht);var Mh=Ea("Seconds",!1);Z("S",0,0,function(){return~~(this.millisecond()/100)}),Z(0,["SS",2],0,function(){return~~(this.millisecond()/10)}),Z(0,["SSS",3],0,"millisecond"),Z(0,["SSSS",4],0,function(){return this.millisecond()*10}),Z(0,["SSSSS",5],0,function(){return this.millisecond()*100}),Z(0,["SSSSSS",6],0,function(){return this.millisecond()*1e3}),Z(0,["SSSSSSS",7],0,function(){return this.millisecond()*1e4}),Z(0,["SSSSSSSS",8],0,function(){return this.millisecond()*1e5}),Z(0,["SSSSSSSSS",9],0,function(){return this.millisecond()*1e6}),se("S",Oa,Je),se("SS",Oa,He),se("SSS",Oa,zs);var la,zl;for(la="SSSS";la.length<=9;la+="S")se(la,ra);function Ah(a,i){i[pa]=Ie(("0."+a)*1e3)}for(la="S";la.length<=9;la+="S")$e(la,Ah);zl=Ea("Milliseconds",!1),Z("z",0,0,"zoneAbbr"),Z("zz",0,0,"zoneName");function Ih(){return this._isUTC?"UTC":""}function Ph(){return this._isUTC?"Coordinated Universal Time":""}var V=B.prototype;V.add=Ou,V.calendar=Iu,V.clone=Pu,V.diff=Hu,V.endOf=Qu,V.format=Ku,V.from=Gu,V.fromNow=qu,V.to=Ju,V.toNow=Zu,V.get=Vc,V.invalidAt=oh,V.isAfter=Lu,V.isBefore=Nu,V.isBetween=Yu,V.isSame=$u,V.isSameOrAfter=Bu,V.isSameOrBefore=Fu,V.isValid=ih,V.lang=kl,V.locale=vl,V.localeData=wl,V.max=iu,V.min=nu,V.parsingFlags=lh,V.set=Kc,V.startOf=Xu,V.subtract=zu,V.toArray=sh,V.toObject=rh,V.toDate=ah,V.toISOString=Uu,V.inspect=Vu,typeof Symbol!="undefined"&&Symbol.for!=null&&(V[Symbol.for("nodejs.util.inspect.custom")]=function(){return"Moment<"+this.format()+">"}),V.toJSON=nh,V.toString=Wu,V.unix=th,V.valueOf=eh,V.creationData=ch,V.eraName=fh,V.eraNarrow=mh,V.eraAbbr=ph,V.eraYear=gh,V.year=Zi,V.isLeapYear=Uc,V.weekYear=wh,V.isoWeekYear=Sh,V.quarter=V.quarters=Rh,V.month=al,V.daysInMonth=ad,V.week=V.weeks=dd,V.isoWeek=V.isoWeeks=ud,V.weeksInYear=Ch,V.weeksInWeekYear=Oh,V.isoWeeksInYear=_h,V.isoWeeksInISOWeekYear=Th,V.date=Ol,V.day=V.days=Sd,V.weekday=_d,V.isoWeekday=Td,V.dayOfYear=Eh,V.hour=V.hours=Md,V.minute=V.minutes=Dh,V.second=V.seconds=Mh,V.millisecond=V.milliseconds=zl,V.utcOffset=pu,V.utc=xu,V.local=yu,V.parseZone=bu,V.hasAlignedHourOffset=ju,V.isDST=vu,V.isLocal=wu,V.isUtcOffset=Su,V.isUtc=gl,V.isUTC=gl,V.zoneAbbr=Ih,V.zoneName=Ph,V.dates=oe("dates accessor is deprecated. Use date instead.",Ol),V.months=oe("months accessor is deprecated. Use month instead",al),V.years=oe("years accessor is deprecated. Use year instead",Zi),V.zone=oe("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",gu),V.isDSTShifted=oe("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",ku);function Lh(a){return Ue(a*1e3)}function Nh(){return Ue.apply(null,arguments).parseZone()}function Rl(a){return a}var Ne=F.prototype;Ne.calendar=De,Ne.longDateFormat=te,Ne.invalidDate=Ve,Ne.ordinal=Bt,Ne.preparse=Rl,Ne.postformat=Rl,Ne.relativeTime=P,Ne.pastFuture=ue,Ne.set=Re,Ne.eras=dh,Ne.erasParse=uh,Ne.erasConvertYear=hh,Ne.erasAbbrRegex=yh,Ne.erasNameRegex=xh,Ne.erasNarrowRegex=bh,Ne.months=Xc,Ne.monthsShort=Qc,Ne.monthsParse=td,Ne.monthsRegex=rd,Ne.monthsShortRegex=sd,Ne.week=id,Ne.firstDayOfYear=cd,Ne.firstDayOfWeek=od,Ne.weekdays=bd,Ne.weekdaysMin=vd,Ne.weekdaysShort=jd,Ne.weekdaysParse=wd,Ne.weekdaysRegex=Cd,Ne.weekdaysShortRegex=Od,Ne.weekdaysMinRegex=zd,Ne.isPM=Ed,Ne.meridiem=Ad;function Fs(a,i,l,o){var f=Ut(),v=_().set(o,i);return f[l](v,a)}function El(a,i,l){if(j(a)&&(i=a,a=void 0),a=a||"",i!=null)return Fs(a,i,l,"month");var o,f=[];for(o=0;o<12;o++)f[o]=Fs(a,o,l,"month");return f}function Br(a,i,l,o){typeof a=="boolean"?(j(i)&&(l=i,i=void 0),i=i||""):(i=a,l=i,a=!1,j(i)&&(l=i,i=void 0),i=i||"");var f=Ut(),v=a?f._week.dow:0,E,ne=[];if(l!=null)return Fs(i,(l+v)%7,o,"day");for(E=0;E<7;E++)ne[E]=Fs(i,(E+v)%7,o,"day");return ne}function Yh(a,i){return El(a,i,"months")}function $h(a,i){return El(a,i,"monthsShort")}function Bh(a,i,l){return Br(a,i,l,"weekdays")}function Fh(a,i,l){return Br(a,i,l,"weekdaysShort")}function Hh(a,i,l){return Br(a,i,l,"weekdaysMin")}ia("en",{eras:[{since:"0001-01-01",until:1/0,offset:1,name:"Anno Domini",narrow:"AD",abbr:"AD"},{since:"0000-12-31",until:-1/0,offset:1,name:"Before Christ",narrow:"BC",abbr:"BC"}],dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(a){var i=a%10,l=Ie(a%100/10)===1?"th":i===1?"st":i===2?"nd":i===3?"rd":"th";return a+l}}),n.lang=oe("moment.lang is deprecated. Use moment.locale instead.",ia),n.langData=oe("moment.langData is deprecated. Use moment.localeData instead.",Ut);var Vt=Math.abs;function Wh(){var a=this._data;return this._milliseconds=Vt(this._milliseconds),this._days=Vt(this._days),this._months=Vt(this._months),a.milliseconds=Vt(a.milliseconds),a.seconds=Vt(a.seconds),a.minutes=Vt(a.minutes),a.hours=Vt(a.hours),a.months=Vt(a.months),a.years=Vt(a.years),this}function Dl(a,i,l,o){var f=Mt(i,l);return a._milliseconds+=o*f._milliseconds,a._days+=o*f._days,a._months+=o*f._months,a._bubble()}function Uh(a,i){return Dl(this,a,i,1)}function Vh(a,i){return Dl(this,a,i,-1)}function Ml(a){return a<0?Math.floor(a):Math.ceil(a)}function Kh(){var a=this._milliseconds,i=this._days,l=this._months,o=this._data,f,v,E,ne,we;return a>=0&&i>=0&&l>=0||a<=0&&i<=0&&l<=0||(a+=Ml(Fr(l)+i)*864e5,i=0,l=0),o.milliseconds=a%1e3,f=zt(a/1e3),o.seconds=f%60,v=zt(f/60),o.minutes=v%60,E=zt(v/60),o.hours=E%24,i+=zt(E/24),we=zt(Al(i)),l+=we,i-=Ml(Fr(we)),ne=zt(l/12),l%=12,o.days=i,o.months=l,o.years=ne,this}function Al(a){return a*4800/146097}function Fr(a){return a*146097/4800}function Gh(a){if(!this.isValid())return NaN;var i,l,o=this._milliseconds;if(a=Me(a),a==="month"||a==="quarter"||a==="year")switch(i=this._days+o/864e5,l=this._months+Al(i),a){case"month":return l;case"quarter":return l/3;case"year":return l/12}else switch(i=this._days+Math.round(Fr(this._months)),a){case"week":return i/7+o/6048e5;case"day":return i+o/864e5;case"hour":return i*24+o/36e5;case"minute":return i*1440+o/6e4;case"second":return i*86400+o/1e3;case"millisecond":return Math.floor(i*864e5)+o;default:throw new Error("Unknown unit "+a)}}function Kt(a){return function(){return this.as(a)}}var Il=Kt("ms"),qh=Kt("s"),Jh=Kt("m"),Zh=Kt("h"),Xh=Kt("d"),Qh=Kt("w"),ef=Kt("M"),tf=Kt("Q"),af=Kt("y"),sf=Il;function rf(){return Mt(this)}function nf(a){return a=Me(a),this.isValid()?this[a+"s"]():NaN}function xa(a){return function(){return this.isValid()?this._data[a]:NaN}}var lf=xa("milliseconds"),of=xa("seconds"),cf=xa("minutes"),df=xa("hours"),uf=xa("days"),hf=xa("months"),ff=xa("years");function mf(){return zt(this.days()/7)}var Gt=Math.round,Ia={ss:44,s:45,m:45,h:22,d:26,w:null,M:11};function pf(a,i,l,o,f){return f.relativeTime(i||1,!!l,a,o)}function gf(a,i,l,o){var f=Mt(a).abs(),v=Gt(f.as("s")),E=Gt(f.as("m")),ne=Gt(f.as("h")),we=Gt(f.as("d")),Pe=Gt(f.as("M")),ft=Gt(f.as("w")),qt=Gt(f.as("y")),oa=v<=l.ss&&["s",v]||v<l.s&&["ss",v]||E<=1&&["m"]||E<l.m&&["mm",E]||ne<=1&&["h"]||ne<l.h&&["hh",ne]||we<=1&&["d"]||we<l.d&&["dd",we];return l.w!=null&&(oa=oa||ft<=1&&["w"]||ft<l.w&&["ww",ft]),oa=oa||Pe<=1&&["M"]||Pe<l.M&&["MM",Pe]||qt<=1&&["y"]||["yy",qt],oa[2]=i,oa[3]=+a>0,oa[4]=o,pf.apply(null,oa)}function xf(a){return a===void 0?Gt:typeof a=="function"?(Gt=a,!0):!1}function yf(a,i){return Ia[a]===void 0?!1:i===void 0?Ia[a]:(Ia[a]=i,a==="s"&&(Ia.ss=i-1),!0)}function bf(a,i){if(!this.isValid())return this.localeData().invalidDate();var l=!1,o=Ia,f,v;return typeof a=="object"&&(i=a,a=!1),typeof a=="boolean"&&(l=a),typeof i=="object"&&(o=Object.assign({},Ia,i),i.s!=null&&i.ss==null&&(o.ss=i.s-1)),f=this.localeData(),v=gf(this,!l,o,f),l&&(v=f.pastFuture(+this,v)),f.postformat(v)}var Hr=Math.abs;function Pa(a){return(a>0)-(a<0)||+a}function Hs(){if(!this.isValid())return this.localeData().invalidDate();var a=Hr(this._milliseconds)/1e3,i=Hr(this._days),l=Hr(this._months),o,f,v,E,ne=this.asSeconds(),we,Pe,ft,qt;return ne?(o=zt(a/60),f=zt(o/60),a%=60,o%=60,v=zt(l/12),l%=12,E=a?a.toFixed(3).replace(/\.?0+$/,""):"",we=ne<0?"-":"",Pe=Pa(this._months)!==Pa(ne)?"-":"",ft=Pa(this._days)!==Pa(ne)?"-":"",qt=Pa(this._milliseconds)!==Pa(ne)?"-":"",we+"P"+(v?Pe+v+"Y":"")+(l?Pe+l+"M":"")+(i?ft+i+"D":"")+(f||o||a?"T":"")+(f?qt+f+"H":"")+(o?qt+o+"M":"")+(a?qt+E+"S":"")):"P0D"}var Le=Ps.prototype;Le.isValid=uu,Le.abs=Wh,Le.add=Uh,Le.subtract=Vh,Le.as=Gh,Le.asMilliseconds=Il,Le.asSeconds=qh,Le.asMinutes=Jh,Le.asHours=Zh,Le.asDays=Xh,Le.asWeeks=Qh,Le.asMonths=ef,Le.asQuarters=tf,Le.asYears=af,Le.valueOf=sf,Le._bubble=Kh,Le.clone=rf,Le.get=nf,Le.milliseconds=lf,Le.seconds=of,Le.minutes=cf,Le.hours=df,Le.days=uf,Le.weeks=mf,Le.months=hf,Le.years=ff,Le.humanize=bf,Le.toISOString=Hs,Le.toString=Hs,Le.toJSON=Hs,Le.locale=vl,Le.localeData=wl,Le.toIsoString=oe("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",Hs),Le.lang=kl,Z("X",0,0,"unix"),Z("x",0,0,"valueOf"),se("x",L),se("X",ve),$e("X",function(a,i,l){l._d=new Date(parseFloat(a)*1e3)}),$e("x",function(a,i,l){l._d=new Date(Ie(a))});return n.version="2.30.1",d(Ue),n.fn=V,n.min=lu,n.max=ou,n.now=cu,n.utc=_,n.unix=Lh,n.months=Yh,n.isDate=x,n.locale=ia,n.invalid=T,n.duration=Mt,n.isMoment=M,n.weekdays=Bh,n.parseZone=Nh,n.localeData=Ut,n.isDuration=Ls,n.monthsShort=$h,n.weekdaysMin=Hh,n.defineLocale=Rr,n.updateLocale=Nd,n.locales=Yd,n.weekdaysShort=Fh,n.normalizeUnits=Me,n.relativeTimeRounding=xf,n.relativeTimeThreshold=yf,n.calendarFormat=Au,n.prototype=V,n.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"},n})}(lr)),lr.exports}var Cy=Li.exports,Do;function Oy(){return Do||(Do=1,function(t,s){(function(r,n){n(typeof Bc=="function"?Ty():r.moment)})(Cy,function(r){var n={1:"'inci",5:"'inci",8:"'inci",70:"'inci",80:"'inci",2:"'nci",7:"'nci",20:"'nci",50:"'nci",3:"'üncü",4:"'üncü",100:"'üncü",6:"'ncı",9:"'uncu",10:"'uncu",30:"'uncu",60:"'ıncı",90:"'ıncı"},d=r.defineLocale("tr",{months:"Ocak_Şubat_Mart_Nisan_Mayıs_Haziran_Temmuz_Ağustos_Eylül_Ekim_Kasım_Aralık".split("_"),monthsShort:"Oca_Şub_Mar_Nis_May_Haz_Tem_Ağu_Eyl_Eki_Kas_Ara".split("_"),weekdays:"Pazar_Pazartesi_Salı_Çarşamba_Perşembe_Cuma_Cumartesi".split("_"),weekdaysShort:"Paz_Pzt_Sal_Çar_Per_Cum_Cmt".split("_"),weekdaysMin:"Pz_Pt_Sa_Ça_Pe_Cu_Ct".split("_"),meridiem:function(c,h,m){return c<12?m?"öö":"ÖÖ":m?"ös":"ÖS"},meridiemParse:/öö|ÖÖ|ös|ÖS/,isPM:function(c){return c==="ös"||c==="ÖS"},longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[bugün saat] LT",nextDay:"[yarın saat] LT",nextWeek:"[gelecek] dddd [saat] LT",lastDay:"[dün] LT",lastWeek:"[geçen] dddd [saat] LT",sameElse:"L"},relativeTime:{future:"%s sonra",past:"%s önce",s:"birkaç saniye",ss:"%d saniye",m:"bir dakika",mm:"%d dakika",h:"bir saat",hh:"%d saat",d:"bir gün",dd:"%d gün",w:"bir hafta",ww:"%d hafta",M:"bir ay",MM:"%d ay",y:"bir yıl",yy:"%d yıl"},ordinal:function(c,h){switch(h){case"d":case"D":case"Do":case"DD":return c;default:if(c===0)return c+"'ıncı";var m=c%10,y=c%100-m,p=c>=100?100:null;return c+(n[m]||n[y]||n[p])}},week:{dow:1,doy:7}});return d})}()),Li.exports}Oy();xt.locale("tr");const{Title:Mo,Text:Ao}=ht,{TabPane:ln}=Xe,{RangePicker:K2}=Ho,{Option:dt}=Ce,Io={uygun:"green",uygun_degil:"red",dm_gonderildi:"blue",dm_gonderilemedi:"orange",beklemede:"purple",baska_ajans:"volcano",coklu_hesap:"magenta",bulunamadi:"grey",desteklenmeyen_bolge:"cyan",diger_nedenler:"gold",manuel_eklendi:"lime"},zy=t=>["Ocak","Şubat","Mart","Nisan","Mayıs","Haziran","Temmuz","Ağustos","Eylül","Ekim","Kasım","Aralık"][t],Ry=t=>{const s=xt(t),r=s.date(),n=s.month(),d=s.year(),c=s.format("HH:mm");return`${r} ${zy(n)} ${d} ${c}`},Ey=()=>{const{updatePublisherDiscoveryStatus:t}=Fi(),[s,r]=u.useState([]),[n,d]=u.useState([]),[c,h]=u.useState(!1),[m,y]=u.useState(10),[p,j]=u.useState([]),[x,b]=u.useState(!1),[C,_]=u.useState(!1),[w,g]=u.useState({running:!1,logOutput:[]}),[H,U]=u.useState("genel"),[T,W]=u.useState("all"),[G,J]=u.useState(null),[B,M]=u.useState(""),[X,oe]=u.useState(!1),[K,ye]=u.useState(!1),[_e,Re]=u.useState(""),[A,F]=u.useState(!1),[ie,je]=u.useState(!1),[De,Y]=u.useState("check_status"),[pe,Oe]=u.useState("1"),[be,Ee]=u.useState({totalPublishers:0,suitablePublishers:0,messageSent:0,averageViewers:0,todayDiscovered:0,statusCounts:{},todayElite:0,todaySuitable:0}),[Z,D]=u.useState([]),[Q,k]=u.useState(10);u.useRef(null);const[ge,$]=u.useState([]),[te,Ye]=u.useState({}),[Ve,jt]=u.useState("60"),[aa,Bt]=u.useState("both"),[sa,P]=u.useState(!1),ue=async(L=!1,ee)=>{L?_(!0):b(!0);try{const he=typeof ee=="string"?ee:T,ve=await fe.get(`${Be.X_SITE_BASE_URL}/publisher-discovery.php`,{params:{status:he!=="all"?he:void 0}}),rt=ve.data&&ve.data.data&&Array.isArray(ve.data.data.publishers)?ve.data.data.publishers:[],Ae=ve.data&&ve.data.data&&ve.data.data.stats?ve.data.data.stats:{totalPublishers:0,suitablePublishers:0,messageSent:0,averageViewers:0,todayDiscovered:0,statusCounts:{}},vt=Ae.todayElite||0,na=Ae.todaySuitable||0;r(rt),Ee({...Ae,todayElite:vt,todaySuitable:na})}catch(he){const ve=Ja();r(ve.publishers),Ee(ve.stats)}finally{L?_(!1):b(!1)}},xe=async()=>{try{const L=await fe.get(`${Be.X_SITE_BASE_URL}/publisher-discovery-status.php`);L.data&&L.data.success?(g(L.data.data),t({running:L.data.data.running,error:!1})):(g({running:!1,startTime:void 0,pid:void 0,logOutput:[],status:void 0}),t({running:!1,error:!1}))}catch(L){t({error:!0})}},Me=async()=>{try{b(!0);const L=await fe.post(`${Be.X_SITE_BASE_URL}/publisher-discovery-control.php`,{action:"start",cycleTime:pe,messageMode:aa});L.data&&L.data.success&&(g({...w,running:!0,startTime:xt().format(),logOutput:[...w.logOutput,"[INFO] Program başlatıldı"],status:"running"}),t({running:!0,error:!1}),S.success("Program başarıyla başlatıldı"))}catch(L){t({error:!0}),S.error("Program başlatılırken bir hata oluştu")}finally{b(!1),xe()}},R=async()=>{try{b(!0);const L=await fe.post(`${Be.X_SITE_BASE_URL}/publisher-discovery-control.php`,{action:"stop"});L.data&&L.data.success&&(g({...w,running:!1,logOutput:[...w.logOutput,"[INFO] Program durduruldu"],status:"stopped"}),t({running:!1,error:!1}),S.success("Program başarıyla durduruldu"))}catch(L){t({error:!0}),S.error("Program durdurulurken bir hata oluştu")}finally{b(!1),xe()}},re=async()=>{var L,ee;if(T&&T!=="all"){try{je(!0);const he=await fe.post(`${Be.X_SITE_BASE_URL}/publisher-discovery-query.php`,{action:De,status:T});he.data&&he.data.success?(De==="send_message"?S.success(`'${T}' statüsündeki tüm kullanıcılara mesaj gönderme başlatıldı`):S.success(`'${T}' statüsündeki tüm kullanıcılar için sorgulama başlatıldı`),setTimeout(()=>{ue(),j([])},2e3)):S.error(((L=he.data)==null?void 0:L.message)||"İşlem başlatılırken bir hata oluştu")}catch(he){S.error("İşlem sırasında bir hata oluştu")}finally{je(!1)}return}if(p.length===0){S.warning("Lütfen en az bir yayıncı seçin");return}try{je(!0);const he=p.map(rt=>({username:rt.username})),ve=await fe.post(`${Be.X_SITE_BASE_URL}/publisher-discovery-query.php`,{usernames:he,action:De});ve.data&&ve.data.success?(De==="send_message"?S.success(`${he.length} kullanıcıya mesaj gönderme işlemi başlatıldı`):S.success(`${he.length} yayıncı için ${De==="check_status"?"durum kontrolü":"mesaj gönderimi"} işlemi başlatıldı`),setTimeout(()=>{ue(),j([])},2e3)):S.error(((ee=ve.data)==null?void 0:ee.message)||"İşlem başlatılırken bir hata oluştu")}catch(he){S.error("İşlem sırasında bir hata oluştu")}finally{je(!1)}},Qe=L=>{Oe(L),w.running&&fe.post(`${Be.X_SITE_BASE_URL}/publisher-discovery-control.php`,{action:"update_cycle_time",cycleTime:L}).then(ee=>{ee.data&&ee.data.success&&S.success("Döngü süresi güncellendi")}).catch(ee=>{S.error("Döngü süresi güncellenirken bir hata oluştu")})};u.useEffect(()=>{fe.get(`${Be.X_SITE_BASE_URL}/publisher-discovery.php?distinct_status=1`).then(L=>{L.data&&L.data.success&&Array.isArray(L.data.statuses)?D(L.data.statuses):D([])})},[]);const Je=s.filter(L=>{if(G&&G[0]&&G[1]){const ee=xt(L.createdAt),he=G[0].startOf("day"),ve=G[1].endOf("day");if(!ee.isBetween(he,ve,void 0,"[]"))return!1}return!(T!=="all"&&L.status.toLowerCase()!==T.toLowerCase()||B&&!L.username.toLowerCase().includes(B.toLowerCase()))}),He=L=>{const ee=p.some(he=>he.id===L.id);j(ee?p.filter(he=>he.id!==L.id):[...p,L])},zs=L=>{j(L?Je:[])},Ja=()=>{const L=["uygun","uygun_degil","dm_gonderildi","dm_gonderilemedi","beklemede","baska_ajans","coklu_hesap","bulunamadi","desteklenmeyen_bolge","diger_nedenler"],ee=Array(50).fill(null).map((ve,rt)=>{const Ae=L[Math.floor(Math.random()*L.length)];return{id:rt+1,username:`tiktok_user_${rt+1}`,viewerCount:Math.floor(Math.random()*1e3)+50,status:Ae,createdAt:xt().subtract(Math.floor(Math.random()*30),"days").format(),messageStatus:Ae==="dm_gonderildi"?"Başarılı":Ae==="dm_gonderilemedi"?"Başarısız":"-",platform:"TikTok",followers:Math.floor(Math.random()*1e5)+1e3,category:["Oyun","Müzik","Dans","Komedi","Yaşam Tarzı"][Math.floor(Math.random()*5)],region:["Türkiye","Azerbaycan","KKTC","Diğer"][Math.floor(Math.random()*4)],lastChecked:xt().subtract(Math.floor(Math.random()*24),"hours").format()}}),he={};return L.forEach(ve=>{he[ve]=ee.filter(rt=>rt.status===ve).length}),{publishers:ee,stats:{totalPublishers:ee.length,suitablePublishers:ee.filter(ve=>ve.status==="uygun").length,messageSent:ee.filter(ve=>ve.status==="dm_gonderildi").length,averageViewers:Math.floor(ee.reduce((ve,rt)=>ve+rt.viewerCount,0)/ee.length),todayDiscovered:ee.filter(ve=>xt(ve.createdAt).isSame(xt(),"day")).length,statusCounts:he}}},Lt=L=>{if(!L)return L;const he={uygun:"Uygun","uygun elite":"Uygun Elite",uygun_degil:"Uygun Değil",dm_gonderildi:"DM Gönderildi",dm_gonderilemedi:"DM Gönderilemedi",beklemede:"Beklemede","başka bir ajansta":"Başka bir ajansta",baska_ajans:"Başka bir ajansta",coklu_hesap:"Çoklu hesap riski","çoklu hesap riski":"Çoklu hesap riski",bulunamadi:"Bulunamadı",desteklenmeyen_bolge:"Desteklenmeyen bölge","desteklenmeyen bölge":"Desteklenmeyen bölge",diger_nedenler:"Diğer nedenler","diğer nedenler":"Diğer nedenler",manuel_eklendi:"Manuel eklendi","manuel eklendi":"Manuel eklendi","takipçi sınırının üstünde":"Takipçi sınırının üstünde"}[L.toLowerCase()];return he||L.split("_").map(ve=>ve.charAt(0).toUpperCase()+ve.slice(1).toLowerCase()).join(" ")},We=[{title:e.jsx(wa,{onChange:L=>zs(L.target.checked),checked:p.length>0&&p.length===Je.length,indeterminate:p.length>0&&p.length<Je.length}),dataIndex:"selected",key:"selected",width:50,render:(L,ee)=>e.jsx(wa,{checked:p.some(he=>he.id===ee.id),onChange:()=>He(ee)})},{title:"Kullanıcı Adı",dataIndex:"username",key:"username",width:150,render:L=>e.jsx("a",{href:`https://tiktok.com/@${L}`,target:"_blank",rel:"noopener noreferrer",children:L})},{title:"İzleyici Sayısı",dataIndex:"viewerCount",key:"viewerCount",width:120,sorter:(L,ee)=>L.viewerCount-ee.viewerCount,render:L=>e.jsxs("span",{children:[e.jsx(Rt,{style:{marginRight:8}}),L]})},{title:"Durum",dataIndex:"status",key:"status",width:130,render:L=>{if(!L||L==="-")return e.jsx("span",{children:"-"});const ee=L.toLowerCase();return ee==="uygun elite"?e.jsx(ae,{style:{background:"#fff7e6",color:"#ad8b00",borderColor:"#ad8b00",fontWeight:700,fontSize:14},children:"Uygun Elite"}):ee==="uygun"?e.jsx(ae,{color:"#52c41a",style:{background:"#f6ffed",color:"#52c41a",borderColor:"#52c41a",fontWeight:600},children:"Uygun"}):ee==="başka bir ajansta"?e.jsx(ae,{color:"#ff4d4f",style:{background:"#fff1f0",color:"#ff4d4f",borderColor:"#ff4d4f",fontWeight:600},children:"Başka bir ajansta"}):ee==="diğer nedenler"?e.jsx(ae,{color:"#faad14",style:{background:"#fffbe6",color:"#faad14",borderColor:"#faad14",fontWeight:600},children:"Diğer nedenler"}):ee==="desteklenmeyen bölge"?e.jsx(ae,{color:"#13c2c2",style:{background:"#e6fffb",color:"#13c2c2",borderColor:"#13c2c2",fontWeight:600},children:"Desteklenmeyen bölge"}):ee==="çoklu hesap riski"?e.jsx(ae,{color:"#2f54eb",style:{background:"#f0f5ff",color:"#2f54eb",borderColor:"#2f54eb",fontWeight:600},children:"Çoklu hesap riski"}):ee==="manuel eklendi"?e.jsx(ae,{color:"#722ed1",style:{background:"#f9f0ff",color:"#722ed1",borderColor:"#722ed1",fontWeight:600},children:"Manuel eklendi"}):ee==="bekleniyor"?e.jsx(ae,{color:"#595959",style:{background:"#fafafa",color:"#595959",borderColor:"#595959",fontWeight:600},children:"Bekleniyor"}):ee==="bulunamadı"?e.jsx(ae,{color:"#ff7875",style:{background:"#fff1f0",color:"#ff7875",borderColor:"#ff7875",fontWeight:600},children:"Bulunamadı"}):ee==="takipçi sınırının üstünde"?e.jsx(ae,{color:"#1890ff",style:{background:"#e6f7ff",color:"#1890ff",borderColor:"#1890ff",fontWeight:600},children:"Takipçi sınırının üstünde"}):e.jsx(ae,{color:Io[ee]||"default",children:Lt(L)})}},{title:"Mesaj Durumu",dataIndex:"messageStatus",key:"messageStatus",width:120,render:L=>L&&L!=="-"?Lt(L):e.jsx("span",{children:"-"})},{title:"Bulunma Tarihi",dataIndex:"createdAt",key:"createdAt",width:150,render:L=>Ry(L)},{title:"Platform",dataIndex:"platform",key:"platform",width:100},{title:e.jsx("div",{style:{textAlign:"center",width:"100%"},children:"İşlemler"}),key:"actions",width:200,align:"center",render:(L,ee)=>e.jsxs(ce,{size:"small",children:[e.jsx(I,{type:"link",icon:e.jsx(ua,{}),size:"small",disabled:ee.status==="dm_gonderildi",onClick:()=>{Re(`Merhaba @${ee.username}, Tuber Ajans olarak TikTok yayınlarınızı takip ediyoruz. Sizinle çalışmak isteriz.`),ye(!0)},children:"Mesaj Gönder"}),e.jsx(I,{type:"link",icon:e.jsx(Rt,{}),size:"small",onClick:()=>window.open(`https://tiktok.com/@${ee.username}`,"_blank"),children:"Profili Gör"})]})}],Rs=async()=>{h(!0);try{const L=await fe.get(`${Be.X_SITE_BASE_URL}/publisher-discovery.php`,{params:{status:"Uygun Elite",today_only:!0}});if(L.data&&L.data.success){let ee=[];Array.isArray(L.data.data)?ee=L.data.data:L.data.data&&Array.isArray(L.data.data.publishers)&&(ee=L.data.data.publishers),ee&&ee.length>0?d(ee):(S.info("Bugün eklenen Uygun Elite kullanıcı bulunamadı"),d([]))}else d([]),S.info("Bugün eklenen Uygun Elite kullanıcı bulunamadı")}catch(L){S.error("Elite kullanıcıları yüklenirken bir hata oluştu"),d([])}finally{h(!1)}},Es=()=>{Rs(),P(!0)};function Oa(){const L=be.todayElite||0,ee=be.todaySuitable||0;return e.jsxs("div",{style:{width:"100%"},children:[e.jsxs(q,{className:"mb-4",style:{width:"100%"},children:[e.jsxs("div",{className:"flex justify-between items-center mb-4 flex-wrap",children:[e.jsxs("div",{className:"w-full md:w-auto mb-4 md:mb-0",children:[e.jsx(Mo,{level:4,className:"mb-0",children:"TikTok Canlı Yayın Veri Toplayıcı"}),e.jsx(Ao,{type:"secondary",children:"Bu program, TikTok'taki canlı yayıncıları otomatik olarak keşfeder ve ajans uygunluğunu değerlendirir"})]}),e.jsxs("div",{style:{display:"flex",alignItems:"center",flexWrap:"wrap",gap:8},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",marginRight:12},children:[e.jsx(Vi,{style:{color:"#13c2c2",marginRight:6,fontSize:18}}),e.jsx("span",{style:{fontWeight:500,marginRight:6},children:"Döngü Süresi"}),e.jsxs(Ce,{value:pe,onChange:Qe,style:{width:110,minWidth:90},className:"cycle-select",size:"middle",children:[e.jsx(dt,{value:"1",children:"1 Dakika"}),e.jsx(dt,{value:"3",children:"3 Dakika"}),e.jsx(dt,{value:"5",children:"5 Dakika"}),e.jsx(dt,{value:"10",children:"10 Dakika"}),e.jsx(dt,{value:"15",children:"15 Dakika"}),e.jsx(dt,{value:"30",children:"30 Dakika"}),e.jsx(dt,{value:"60",children:"1 Saat"}),e.jsx(dt,{value:"180",children:"3 Saat"}),e.jsx(dt,{value:"360",children:"6 Saat"}),e.jsx(dt,{value:"720",children:"12 Saat"})]})]}),e.jsxs("div",{style:{display:"flex",alignItems:"center",marginRight:12},children:[e.jsx("span",{style:{fontWeight:500,marginRight:6},children:"Mesaj Gönderme:"}),e.jsxs(Ce,{value:aa,onChange:Bt,style:{width:220,minWidth:180},size:"middle",children:[e.jsx(dt,{value:"none",children:"Hiç mesaj gönderme"}),e.jsx(dt,{value:"suitable",children:"Sadece uygun olanlara mesaj gönder"}),e.jsx(dt,{value:"elite",children:"Sadece uygun elite olanlara mesaj gönder"}),e.jsx(dt,{value:"both",children:"Hem uygun hem uygun elite olanlara mesaj gönder"})]})]}),e.jsx(I,{type:"primary",icon:e.jsx(Rg,{}),onClick:Me,loading:x&&w.status!=="running",disabled:w.status==="running",style:{backgroundColor:"#52c41a",color:"white",borderColor:"#52c41a",marginBottom:"8px"},children:"Otomasyonu Başlat"}),e.jsx(I,{type:"primary",danger:!0,icon:e.jsx(yo,{}),onClick:R,loading:x&&w.status==="running",disabled:w.status!=="running",style:{marginBottom:"8px"},children:"Otomasyonu Durdur"})]})]}),w.running&&w.startTime&&e.jsx(Zt,{type:"info",message:e.jsxs("span",{children:["Program ",xt(w.startTime).fromNow()," başlatıldı.",w.pid&&` (PID: ${w.pid})`]}),className:"mb-4"}),e.jsxs(ke,{gutter:[16,16],className:"stats-row",children:[e.jsx(N,{xs:24,sm:12,md:8,lg:6,xl:4,children:e.jsx(q,{bordered:!1,className:"stat-card",style:{background:"linear-gradient(135deg, #fffbe6 0%, #ffe58f 100%)",border:"1px solid #FFD700"},onClick:Es,children:e.jsx(Te,{title:e.jsxs("span",{style:{fontWeight:500,fontSize:16},children:[e.jsx("span",{style:{color:"#FFD700",fontSize:18,verticalAlign:"middle",marginRight:4},children:"★"}),"Bugün Bulunan Uygun Elite"]}),value:L,valueStyle:{color:"#FFD700",fontWeight:700}})})}),e.jsx(N,{xs:24,sm:12,md:8,lg:6,xl:4,children:e.jsx(q,{bordered:!1,className:"stat-card",style:{background:"linear-gradient(135deg, #f6ffed 0%, #b7eb8f 100%)",border:"1px solid #52c41a"},children:e.jsx(Te,{title:e.jsx("span",{style:{fontWeight:500,fontSize:16},children:"Bugün Bulunan Uygun"}),value:ee,valueStyle:{color:"#52c41a",fontWeight:700}})})}),e.jsx(N,{xs:24,sm:12,md:8,lg:6,xl:4,children:e.jsx(q,{bordered:!1,className:"stat-card",children:e.jsx(Te,{title:e.jsx("span",{style:{fontWeight:500,fontSize:16},children:"Toplam Kullanıcı"}),value:be.totalPublishers,prefix:e.jsx(ot,{}),valueStyle:{color:"#1890ff"}})})}),e.jsx(N,{xs:24,sm:12,md:8,lg:6,xl:4,children:e.jsx(q,{bordered:!1,className:"stat-card",children:e.jsx(Te,{title:e.jsx("span",{style:{fontWeight:500,fontSize:16},children:"Uygun Kullanıcılar"}),value:be.suitablePublishers,prefix:e.jsx(ea,{}),valueStyle:{color:"#52c41a"}})})}),e.jsx(N,{xs:24,sm:12,md:8,lg:6,xl:4,children:e.jsx(q,{bordered:!1,className:"stat-card",children:e.jsx(Te,{title:e.jsx("span",{style:{fontWeight:500,fontSize:16},children:"Gönderilen Mesajlar"}),value:be.messageSent,prefix:e.jsx(ua,{}),valueStyle:{color:"#722ed1"}})})}),e.jsx(N,{xs:24,sm:12,md:8,lg:6,xl:4,children:e.jsx(q,{bordered:!1,className:"stat-card",children:e.jsx(Te,{title:e.jsx("span",{style:{fontWeight:500,fontSize:16},children:"Ortalama İzleyici"}),value:be.averageViewers,prefix:e.jsx(Rt,{}),valueStyle:{color:"#fa8c16"}})})}),e.jsx(N,{xs:24,sm:12,md:8,lg:6,xl:4,children:e.jsx(q,{bordered:!1,className:"stat-card",children:e.jsx(Te,{title:e.jsx("span",{style:{fontWeight:500,fontSize:16},children:"Bugün Keşfedilen"}),value:be.todayDiscovered,prefix:e.jsx(Tx,{}),valueStyle:{color:"#eb2f96"}})})})]})]}),e.jsx(q,{className:"mb-4",style:{background:"#fff",border:"1px solid #f0f0f0",boxShadow:"0 2px 8px rgba(0,0,0,0.03)"},children:e.jsxs("div",{className:"p-4 rounded",children:[e.jsx("div",{className:"text-lg font-semibold mb-2",children:"Günlük İşlem Özeti"}),e.jsx("div",{className:"overflow-x-auto",children:e.jsx(qe,{columns:[{title:"Tarih",dataIndex:"date",key:"date",render:he=>he?xt(he).format("DD.MM.YYYY"):"-"},{title:"Kullanıcı Sayısı",dataIndex:"userCount",key:"userCount"},{title:"Uygun Kullanıcılar",dataIndex:"suitableUsers",key:"suitableUsers"},{title:"Gönderilen Mesajlar",dataIndex:"sentMessages",key:"sentMessages"},{title:"Ortalama İzleyici",dataIndex:"avgViewer",key:"avgViewer"},{title:"En Yüksek İzleyici Saat Aralığı",dataIndex:"peakHourRange",key:"peakHourRange"}],dataSource:ge.map((he,ve)=>({...he,key:ve})),pagination:!1,size:"small",scroll:{x:"max-content"},className:"responsive-daily-table"})})]})})]})}const Za=()=>{var L,ee,he;if(!Array.isArray(Z)||Z.length===0)return e.jsx(Zt,{message:"İstatistik verisi bulunamadı.",type:"info",showIcon:!0});const ve=["Uygun Elite","Uygun","Diğer nedenler","Bekleniyor","Desteklenmeyen bölge"],rt=[...ve.filter(Ae=>Z.some(vt=>vt.toLowerCase()===Ae.toLowerCase())),...Z.filter(Ae=>!ve.map(vt=>vt.toLowerCase()).includes(Ae.toLowerCase())).sort((Ae,vt)=>Ae.localeCompare(vt,"tr"))];return e.jsx("div",{style:{width:"100%"},children:e.jsxs(q,{style:{width:"100%",background:"transparent",boxShadow:"none",border:"none"},children:[e.jsx(Mo,{level:4,className:"mb-4",children:"İstatistikler"}),e.jsx(ke,{gutter:[16,16],className:"stats-cards-row",children:rt.map((Ae,vt)=>{const na=Lt(Ae),se=be&&be.statusCounts&&typeof be.statusCounts[Ae.toLowerCase()]!="undefined"?be.statusCounts[Ae.toLowerCase()]:0;let Xa={background:vt%2===0?"#f5f5f5":"#ffffff",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.05)",height:"100%"},Ra=Io[Ae.toLowerCase()]||"blue",Ot=na;return Ae.toLowerCase()==="uygun elite"?(Ot=e.jsxs("span",{children:[e.jsx("span",{style:{color:"#FFD700",fontSize:20,verticalAlign:"middle",marginRight:4},children:"★"}),"Uygun Elite"]}),Xa={background:"linear-gradient(135deg, #fffbe6 0%, #ffe58f 100%)",borderRadius:8,boxShadow:"0 2px 8px rgba(255, 215, 0, 0.10)",height:"100%",border:"1px solid #FFD700"},Ra="#FFD700"):Ae.toLowerCase()==="uygun"&&(Xa={background:"linear-gradient(135deg, #f6ffed 0%, #b7eb8f 100%)",borderRadius:8,boxShadow:"0 2px 8px rgba(82, 196, 26, 0.10)",height:"100%",border:"1px solid #52c41a"},Ra="#52c41a"),e.jsx(N,{xs:24,sm:12,md:8,lg:6,children:e.jsx(q,{bordered:!1,style:Xa,className:"stat-status-card",bodyStyle:{padding:"16px",height:"100%"},children:e.jsxs("div",{children:[e.jsx("div",{style:{marginBottom:8},children:e.jsx(ae,{color:Ra,style:Ae.toLowerCase()==="uygun elite"?{background:"#fffbe6",color:"#FFD700",borderColor:"#FFD700",fontWeight:600}:Ae.toLowerCase()==="uygun"?{background:"#f6ffed",color:"#52c41a",borderColor:"#52c41a",fontWeight:600}:{},children:Ot})}),e.jsx(Te,{value:se,valueStyle:{color:Ra,fontWeight:Ae.toLowerCase()==="uygun elite"?700:void 0}}),e.jsx("div",{style:{marginTop:16},children:e.jsx(I,{size:"small",type:"primary",style:{fontSize:"11px",backgroundColor:"#1890ff",color:"white"},onClick:()=>{W(Ae),U("veritabani")},children:"Detay Gör"})})]})})},Ae)})}),e.jsx("div",{style:{width:"100%",marginTop:32},children:e.jsxs(q,{bordered:!1,className:"stat-card son-izleyici-zirveleri-card",style:{background:"linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)",border:"1px solid #1890ff",width:"100%",minHeight:120,display:"flex",flexDirection:"column",justifyContent:"flex-start",padding:0},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:8,padding:"8px 12px 0 12px"},children:[e.jsx("span",{style:{fontWeight:500,fontSize:16},children:"Son İzleyici Zirveleri"}),e.jsx("div",{children:e.jsxs(I.Group,{size:"small",children:[e.jsx(I,{type:Ve==="7"?"primary":"default",style:Ve==="7"?{background:"#fff",color:"#1890ff",borderColor:"#1890ff",fontWeight:600}:{},children:"7 Gün"}),e.jsx(I,{type:Ve==="30"?"primary":"default",style:Ve==="30"?{background:"#fff",color:"#1890ff",borderColor:"#1890ff",fontWeight:600}:{},children:"30 Gün"}),e.jsx(I,{type:Ve==="60"?"primary":"default",style:Ve==="60"?{background:"#fff",color:"#1890ff",borderColor:"#1890ff",fontWeight:600}:{},children:"60 Gün"}),e.jsx(I,{type:Ve==="90"?"primary":"default",style:Ve==="90"?{background:"#fff",color:"#1890ff",borderColor:"#1890ff",fontWeight:600}:{},children:"90 Gün"})]})})]}),te[Ve]?e.jsxs("div",{style:{padding:"0 12px 12px 12px"},children:[e.jsxs("div",{style:{fontWeight:700,fontSize:18,color:"#1890ff",marginBottom:4},children:[te[Ve].range,", ",te[Ve].weekday]}),e.jsxs("div",{style:{fontSize:14,marginBottom:6},children:[e.jsx("b",{children:"Haftanın En Verimli Günü:"})," ",(L=te[Ve].mostEfficientDay)==null?void 0:L.weekday," (Ortalama: ",(ee=te[Ve].mostEfficientDay)==null?void 0:ee.avg,")"]}),e.jsx("div",{style:{fontSize:14,marginBottom:4},children:e.jsx("b",{children:"Haftanın 7 Günü İçin Zirve Saatler:"})}),e.jsx("div",{className:"zirve-kutucuklar-wrapper",style:{display:"flex",flexWrap:"nowrap",gap:10,flexDirection:"row",width:"100%",overflowX:"auto",justifyContent:"flex-start",paddingBottom:4},children:(he=te[Ve].weeklyPeaks)==null?void 0:he.map(Ae=>e.jsxs("div",{className:"zirve-kutucuk",style:{background:"#fff",border:"1px solid #e6f7ff",borderRadius:8,padding:"8px 12px",minWidth:120,maxWidth:160,textAlign:"center",boxShadow:"0 1px 4px rgba(24,144,255,0.06)",flex:"1 1 120px",fontSize:13},children:[e.jsx("div",{style:{fontWeight:600,color:"#1890ff",marginBottom:2},children:Ae.weekday}),e.jsx("div",{style:{fontSize:13},children:Ae.range||"-"}),e.jsxs("div",{style:{fontSize:12,color:"#888",marginTop:2},children:["Ortalama: ",Ae.avg]})]},Ae.weekday))})]}):e.jsx("div",{style:{color:"#999",padding:16},children:"Veri yok"})]})})]})})},za=()=>{const L=["Uygun Elite","Uygun","Diğer nedenler","Bekleniyor","Desteklenmeyen bölge"],ee=[...L.filter(he=>Z.some(ve=>ve.toLowerCase()===he.toLowerCase())),...Z.filter(he=>!L.map(ve=>ve.toLowerCase()).includes(he.toLowerCase())).sort((he,ve)=>he.localeCompare(ve,"tr"))];return e.jsxs("div",{style:{width:"100%"},children:[e.jsx("div",{className:"mb-4",children:e.jsxs("div",{style:{display:"flex",flexWrap:"wrap",gap:8,alignItems:"center",marginBottom:8},children:[e.jsx(Te,{title:e.jsx(Ao,{style:{fontSize:"14px",color:"rgba(0, 0, 0, 0.88)",marginRight:"6px"},children:"Toplam Kullanıcı:"}),value:be.totalPublishers?be.totalPublishers.toLocaleString("tr-TR"):"0",valueStyle:{fontSize:"14px",fontWeight:"bold",color:"#1677ff"},style:{marginRight:16,minWidth:120}}),e.jsx(me,{placeholder:"Kullanıcı adı ara...",prefix:e.jsx(ta,{}),value:B,onChange:he=>M(he.target.value),className:"search-input",style:{width:"100%",maxWidth:250,minWidth:120,marginBottom:0}}),e.jsxs(Ce,{style:{width:"100%",maxWidth:250,minWidth:120,marginBottom:0},value:T,onChange:ra,className:"status-select",dropdownStyle:{minWidth:250},children:[e.jsx(dt,{value:"all",title:"Tümü",children:"Tümü"}),ee.map(he=>e.jsx(dt,{value:he,title:Lt(he),children:Lt(he)},he))]}),e.jsxs("div",{style:{display:"flex",flexWrap:"wrap",gap:8,alignItems:"center"},children:[e.jsx(I,{type:"primary",icon:e.jsx(ox,{}),onClick:()=>ue(),loading:x,style:{backgroundColor:"#1890ff",color:"white",borderColor:"#1890ff",marginBottom:0},children:"Yenile"}),e.jsxs(I,{type:"primary",icon:e.jsx(qi,{}),onClick:()=>{if(p.length===0){S.warning("Lütfen en az bir yayıncı seçin");return}Y("check_status"),re()},loading:ie&&De==="check_status",style:{backgroundColor:"#1890ff",color:"white",borderColor:"#1890ff",marginBottom:0},children:["Tekrar Sorgula ",p.length>0?`(${p.length})`:""]}),e.jsxs(I,{type:"primary",icon:e.jsx(Lc,{}),onClick:()=>{if(p.length===0){S.warning("Lütfen en az bir yayıncı seçin");return}Y("send_message"),re()},loading:ie&&De==="send_message",style:{backgroundColor:"#1890ff",color:"white",borderColor:"#1890ff",marginBottom:0},children:["Mesaj Gönder ",p.length>0?`(${p.length})`:""]}),e.jsx(I,{type:"primary",icon:e.jsx(yo,{}),onClick:()=>{if(p.length===0){S.warning("Lütfen en az bir yayıncı seçin");return}S.info("İşlem durduruldu")},style:{backgroundColor:"#1890ff",color:"white",borderColor:"#1890ff",marginBottom:0},children:"Durdur"})]})]})}),p.length>0&&e.jsx("div",{className:"mb-4",children:e.jsx(Zt,{message:`${p.length} yayıncı seçildi`,type:"info",showIcon:!0})}),e.jsx(qe,{dataSource:Je||[],columns:We,rowKey:"id",loading:C,pagination:{pageSize:Q,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:(he,ve)=>k(ve)},style:{width:"100%"},scroll:{x:"max-content"},className:"responsive-table"}),e.jsxs("div",{className:"flex justify-between mt-4",children:[e.jsx(I,{type:"default",children:"Önceki Sayfa"}),e.jsx(I,{type:"default",children:"Sonraki Sayfa"})]})]})},ra=L=>{const ee=L;W(ee),ue(!1,ee==="all"?void 0:ee)};return u.useEffect(()=>{xe(),ue()},[]),u.useEffect(()=>{fe.get(`${Be.X_SITE_BASE_URL}/publisher-discovery-daily-summary.php`).then(L=>{L.data&&L.data.success&&($(L.data.data),Ye(L.data.peakStats||{}))})},[]),e.jsxs("div",{className:"p-6 px-2 md:px-6",style:{maxWidth:"100%",overflowX:"hidden",paddingTop:12,marginTop:0,minHeight:"100vh",boxSizing:"border-box"},children:[e.jsxs(q,{style:{width:"100%",margin:0,padding:0,boxShadow:"none",border:"none",background:"transparent"},bodyStyle:{padding:0},children:[e.jsxs(Xe,{activeKey:H,onChange:U,tabBarStyle:{marginBottom:0},style:{marginBottom:0},children:[e.jsx(ln,{tab:"Genel"},"genel"),e.jsx(ln,{tab:"İstatistikler"},"istatistikler"),e.jsx(ln,{tab:"Veritabanı"},"veritabani")]}),H==="genel"&&e.jsx(e.Fragment,{children:Oa()}),H==="istatistikler"&&Za(),H==="veritabani"&&za()]}),e.jsx(St,{open:sa,onCancel:()=>P(!1),title:"Bugün Bulunan Uygun Elite Kullanıcılar",footer:null,width:600,children:e.jsx(qe,{loading:c,dataSource:n,columns:[{title:"Kullanıcı Adı",dataIndex:"username",key:"username",sorter:(L,ee)=>L.username.localeCompare(ee.username),render:L=>e.jsx("a",{href:`https://tiktok.com/@${L}`,target:"_blank",rel:"noopener noreferrer",children:L})},{title:"İzleyici",dataIndex:"viewerCount",key:"viewerCount",sorter:(L,ee)=>L.viewerCount-ee.viewerCount,defaultSortOrder:"descend",render:L=>L||0},{title:"Profil",dataIndex:"username",key:"profile",render:L=>e.jsx("a",{href:`https://tiktok.com/@${L}`,target:"_blank",rel:"noopener noreferrer",children:"TikTok"})}],rowKey:"id",pagination:{pageSize:m,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:(L,ee)=>y(ee),showTotal:(L,ee)=>`${ee[0]}-${ee[1]} / ${L} kayıt`},size:"small"})}),e.jsx("style",{children:`
        @media (max-width: 768px) {
          .ant-row {
            flex-direction: column !important;
          }
          .stat-card {
            min-width: 90vw !important;
            margin-bottom: 16px !important;
          }
          .ant-card, .ant-table {
            width: 100% !important;
            min-width: 0 !important;
          }
          .ant-table-content {
            overflow-x: auto !important;
          }
          .ant-tabs-nav {
            flex-wrap: wrap !important;
          }
          .ant-statistic-title {
            font-size: 12px !important;
          }
          .ant-statistic-content {
            font-size: 20px !important;
          }
          .ant-space {
            flex-wrap: wrap !important;
            gap: 8px !important;
            margin-bottom: 10px !important;
          }
          .ant-space-item {
            margin-right: 0 !important;
            margin-bottom: 8px !important;
          }
          .ant-btn {
            padding: 4px 8px !important;
            height: auto !important;
            font-size: 12px !important;
          }
          .ant-input {
            font-size: 12px !important;
          }
          .ant-select {
            width: 100% !important;
            max-width: none !important;
          }
          .ant-picker {
            width: 100% !important;
          }
          .ant-card-body {
            padding: 12px 8px !important;
          }
          .ant-table-thead > tr > th,
          .ant-table-tbody > tr > td {
            padding: 8px 4px !important;
            font-size: 12px !important;
          }
          .ant-table-column-title {
            font-size: 12px !important;
          }
          .ant-table-cell .ant-btn {
            padding: 2px 6px !important;
            font-size: 11px !important;
          }
          .son-izleyici-zirveleri-card {
            padding: 0 !important;
          }
          .zirve-kutucuklar-wrapper {
            flex-wrap: nowrap !important;
            overflow-x: auto !important;
            padding-bottom: 4px !important;
          }
          .zirve-kutucuk {
            min-width: 140px !important;
            max-width: 180px !important;
            font-size: 12px !important;
            padding: 8px 8px !important;
          }
        }

        @media (max-width: 576px) {
          .p-6 {
            padding: 8px !important;
          }
          .ant-card-body {
            padding: 8px 4px !important;
          }
          .ant-tabs-tab {
            padding: 8px 4px !important;
            margin: 0 4px !important;
            font-size: 14px !important;
          }
          .ant-table-thead > tr > th,
          .ant-table-tbody > tr > td {
            padding: 6px 2px !important;
            font-size: 11px !important;
          }
          .ant-statistic-content {
            font-size: 18px !important;
          }
          /* İyileştirilmiş button stilleri */
          .ant-btn {
            padding: 2px 6px !important;
            font-size: 11px !important;
            white-space: nowrap !important;
            text-overflow: ellipsis !important;
            overflow: hidden !important;
            max-width: 100% !important;
          }
          /* Tablo için yatay kaydırma */
          .responsive-table, .responsive-daily-table {
            overflow-x: auto !important;
            width: 100% !important;
            display: block !important;
          }
          /* Stat kartları */
          .ant-col {
            padding: 4px !important;
          }
          .stat-card, .stat-status-card {
            margin-bottom: 8px !important;
          }
          /* Tab menüsü */
          .ant-tabs-nav-list {
            width: 100% !important;
          }
          .ant-tabs-tab {
            flex: 1 !important;
            text-align: center !important;
          }
          /* Filtre alanları */
          .filter-space, .action-space {
            margin-bottom: 8px !important;
          }
          .search-input, .status-select, .date-picker {
            width: 100% !important;
          }
          /* Döngü seçicisi */
          .cycle-select {
            flex: 1 !important;
          }
          /* Başlık alanı */
          .ant-typography {
            font-size: 90% !important;
          }
          h4.ant-typography {
            font-size: 16px !important;
          }
        }
      `})]})},{TabPane:Po}=Xe,{Option:Dy}=Ce,{Title:My}=ht,Ay={"akademi.destek":"blue","site.basvuru":"green","site.iletisim":"cyan","site.geriarama":"orange","site.toplanti":"purple","etsy.tasarim":"magenta","whatsapp.mesaj":"lime"},Lo={"akademi.destek":"Akademi - Destek Talebi","site.basvuru":"Site Yönetimi - Başvuru","site.iletisim":"Site Yönetimi - İletişim Talebi","site.geriarama":"Site Yönetimi - Geri Arama Talebi","site.toplanti":"Site Yönetimi - Toplantı Talebi","etsy.tasarim":"ETSY - Tasarım Onayı","whatsapp.mesaj":"WhatsApp - Mesaj"},Iy=()=>{const t=_a(),{notifications:s,markAsRead:r,markAllAsRead:n,getNotificationRoute:d}=vr(),[c,h]=u.useState("all"),[m,y]=u.useState([]),[p,j]=u.useState(!1),x=()=>{let g=[...s];return c==="unread"&&(g=g.filter(H=>!H.read)),m.length>0&&(g=g.filter(H=>m.includes(H.source))),g},b=g=>{r(g.id);const H=d(g);H!=="/"&&setTimeout(()=>{t(H)},100)},C=()=>{j(!0),setTimeout(()=>{n(),j(!1)},500)},_=x(),w=Array.from(new Set(s.map(g=>g.source)));return e.jsxs("div",{className:"container mx-auto p-4",children:[e.jsx("div",{className:"mb-4 pb-3 border-b",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx(My,{level:4,style:{margin:0},children:"Bildirimler"}),e.jsx("div",{className:"text-gray-500",children:"Tüm bildirimlerinizi görüntüleyin ve yönetin"})]}),e.jsx(ce,{children:e.jsx(I,{icon:e.jsx(j0,{}),onClick:C,disabled:!s.some(g=>!g.read)||p,loading:p,children:"Tümünü Okundu İşaretle"})})]})}),e.jsxs(q,{className:"mt-4",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs(Xe,{activeKey:c,onChange:h,children:[e.jsx(Po,{tab:e.jsxs("span",{children:["Tüm Bildirimler",e.jsx(Ge,{count:s.length,style:{marginLeft:8,backgroundColor:"#1890ff"}})]})},"all"),e.jsx(Po,{tab:e.jsxs("span",{children:["Okunmamış",e.jsx(Ge,{count:s.filter(g=>!g.read).length,style:{marginLeft:8,backgroundColor:"#ff4d4f"}})]})},"unread")]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(Ic,{className:"mr-2"}),e.jsx(Ce,{mode:"multiple",placeholder:"Bildirim kaynağı filtrele",style:{width:300},value:m,onChange:y,maxTagCount:2,children:w.map(g=>e.jsx(Dy,{value:g,children:Lo[g]||g},g))})]})]}),e.jsx(Ta,{spinning:p,children:_.length>0?e.jsx(mt,{itemLayout:"horizontal",dataSource:_,renderItem:g=>e.jsx(mt.Item,{className:`cursor-pointer transition-colors duration-200 ${g.read?"":"bg-blue-50 dark:bg-blue-900/20"}`,onClick:()=>b(g),actions:[e.jsx(ae,{color:Ay[g.source],children:Lo[g.source]||g.source})],children:e.jsx(mt.Item.Meta,{avatar:e.jsx(Ge,{dot:!g.read,offset:[0,0],children:e.jsx(hr,{style:{fontSize:"20px"}})}),title:e.jsx("span",{className:g.read?"":"font-bold",children:g.title}),description:e.jsxs("div",{children:[e.jsx("p",{children:g.message}),e.jsx("small",{className:"text-gray-500",children:g.time})]})})},g.id),pagination:{pageSize:10,total:_.length,showSizeChanger:!1,showTotal:g=>`Toplam ${g} bildirim`}}):e.jsx(or,{description:"Bildirim bulunamadı",image:or.PRESENTED_IMAGE_SIMPLE})})]})]})},{TabPane:No}=Xe,{Title:Py,Text:on}=ht,{TextArea:Xs}=me,Ly=()=>{const[t]=O.useForm(),[s]=O.useForm(),[r,n]=u.useState([]),[d,c]=u.useState(!0),[h,m]=u.useState(!1),[y,p]=u.useState(null),[j,x]=u.useState("add"),[b,C]=u.useState(!1),[_,w]=u.useState(null),g=async()=>{var M;c(!0),w(null);try{const X=await de.get("/backend/site/blog.php?action=list");X.data&&X.data.success?n(X.data.data||[]):w(((M=X.data)==null?void 0:M.message)||"Blog yazıları yüklenemedi")}catch(X){w("Blog yazıları yüklenirken bir hata oluştu!")}finally{c(!1)}};u.useEffect(()=>{g()},[]);const H=u.useMemo(()=>{const M=r.length,X=r.filter(ye=>ye.date?Fe(ye.date).isAfter(Fe().startOf("month")):!1).length,oe=r.filter(ye=>ye.status==="published"||!ye.status).length,K=r.filter(ye=>ye.status==="draft").length;return{total:M,thisMonth:X,published:oe,drafts:K}},[r]),U=async M=>{m(!0);try{!M.slug&&M.title&&(M.slug=M.title.toLowerCase().replace(/[^a-z0-9\s-]/g,"").replace(/\s+/g,"-").trim()),M.date||(M.date=Fe().format("YYYY-MM-DD HH:mm:ss")),await de.post("/backend/site/blog.php?action=add",M),S.success("Blog yazısı başarıyla eklendi"),g(),t.resetFields(),x("list")}catch(X){S.error("Blog yazısı eklenirken bir hata oluştu!")}finally{m(!1)}},T=async M=>{try{await de.post("/backend/site/blog.php?action=delete",{id:M}),S.success("Blog yazısı başarıyla silindi"),g()}catch(X){S.error("Blog yazısı silinirken bir hata oluştu")}},W=M=>{p(M),s.setFieldsValue({...M,date:M.date?Fe(M.date):null}),C(!0)},G=async M=>{m(!0);try{M.date&&Fe.isDayjs(M.date)&&(M.date=M.date.format("YYYY-MM-DD HH:mm:ss")),await de.post("/backend/site/blog.php?action=update",{...M,id:y.id}),S.success("Blog yazısı başarıyla güncellendi"),g(),C(!1),p(null),s.resetFields()}catch(X){S.error("Blog yazısı güncellenirken bir hata oluştu!")}finally{m(!1)}},J=()=>{C(!1),p(null),s.resetFields()},B=[{title:"#",dataIndex:"id",key:"id",width:60,sorter:(M,X)=>M.id-X.id},{title:"Başlık",dataIndex:"title",key:"title",width:250,sorter:(M,X)=>(M.title||"").localeCompare(X.title||""),render:(M,X)=>e.jsxs("div",{children:[e.jsx(on,{strong:!0,style:{display:"block",marginBottom:4},children:M}),X.slug&&e.jsxs(on,{type:"secondary",style:{fontSize:"12px"},children:[e.jsx(sg,{})," ",X.slug]})]})},{title:"Kısa Açıklama",dataIndex:"excerpt",key:"excerpt",width:300,render:M=>e.jsx("div",{style:{maxWidth:"250px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:M||"Açıklama yok"})},{title:"Tarih",dataIndex:"date",key:"date",width:160,sorter:(M,X)=>Fe(M.date||0).unix()-Fe(X.date||0).unix(),render:M=>M?e.jsx(ae,{color:"blue",icon:e.jsx(It,{}),children:Fe(M).format("DD/MM/YYYY")}):"-"},{title:"Durum",dataIndex:"status",key:"status",width:100,render:M=>e.jsx(ae,{color:M==="published"||!M?"green":"orange",children:M==="published"||!M?"Yayında":"Taslak"})},{title:"İşlemler",key:"actions",width:140,render:(M,X)=>e.jsxs(ce,{size:"small",children:[e.jsx(I,{type:"primary",size:"small",icon:e.jsx(yt,{}),onClick:()=>W(X),children:"Düzenle"}),e.jsx(it,{title:"Bu blog yazısını silmek istediğinize emin misiniz?",description:"Bu işlem geri alınamaz.",onConfirm:()=>T(X.id),okText:"Evet, Sil",cancelText:"Vazgeç",okButtonProps:{danger:!0},children:e.jsx(I,{danger:!0,size:"small",icon:e.jsx(Et,{}),children:"Sil"})})]})}];return d?e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"60vh",flexDirection:"column"},children:e.jsx(Ta,{size:"large",tip:"Blog yazıları yükleniyor..."})}):_?e.jsx("div",{style:{padding:"24px"},children:e.jsx(Zt,{type:"error",message:"Hata",description:_,showIcon:!0,style:{borderRadius:"12px"}})}):e.jsxs("div",{className:"site-blog-management",style:{padding:"24px",background:"#f5f5f5",minHeight:"100vh"},children:[e.jsx("div",{style:{marginBottom:32,background:"linear-gradient(135deg, #fa8c16 0%, #d46b08 100%)",borderRadius:"16px",padding:"32px",color:"white",boxShadow:"0 8px 32px rgba(0,0,0,0.1)"},children:e.jsx(ke,{align:"middle",justify:"space-between",children:e.jsx(N,{children:e.jsxs(ce,{align:"center",size:"large",children:[e.jsx("div",{style:{background:"rgba(255,255,255,0.2)",borderRadius:"12px",padding:"12px",backdropFilter:"blur(10px)"},children:e.jsx(Xt,{style:{fontSize:"32px",color:"white"}})}),e.jsxs("div",{children:[e.jsx(Py,{level:2,style:{margin:0,color:"white"},children:"Blog Yönetimi"}),e.jsx(on,{style:{color:"rgba(255,255,255,0.8)",fontSize:"16px"},children:"Blog yazıları oluşturma ve yönetim sistemi"})]})]})})})}),e.jsxs(ke,{gutter:[16,16],style:{marginBottom:32},children:[e.jsx(N,{xs:24,sm:12,md:6,children:e.jsx(q,{style:{borderRadius:"12px",textAlign:"center"},children:e.jsx(Te,{title:"Toplam Blog",value:H.total,prefix:e.jsx(Xt,{style:{color:"#fa8c16"}}),valueStyle:{color:"#fa8c16"}})})}),e.jsx(N,{xs:24,sm:12,md:6,children:e.jsx(q,{style:{borderRadius:"12px",textAlign:"center"},children:e.jsx(Te,{title:"Bu Ay Yazılan",value:H.thisMonth,prefix:e.jsx(It,{style:{color:"#1890ff"}}),valueStyle:{color:"#1890ff"}})})}),e.jsx(N,{xs:24,sm:12,md:6,children:e.jsx(q,{style:{borderRadius:"12px",textAlign:"center"},children:e.jsx(Te,{title:"Yayında",value:H.published,prefix:e.jsx(Rt,{style:{color:"#52c41a"}}),valueStyle:{color:"#52c41a"}})})}),e.jsx(N,{xs:24,sm:12,md:6,children:e.jsx(q,{style:{borderRadius:"12px",textAlign:"center"},children:e.jsx(Te,{title:"Taslaklar",value:H.drafts,prefix:e.jsx(yt,{style:{color:"#722ed1"}}),valueStyle:{color:"#722ed1"}})})})]}),e.jsx(q,{style:{borderRadius:"16px",border:"none",boxShadow:"0 4px 20px rgba(0,0,0,0.08)"},bodyStyle:{padding:"24px"},children:e.jsxs(Xe,{activeKey:j,onChange:x,size:"large",style:{marginBottom:0},children:[e.jsx(No,{tab:e.jsxs(ce,{children:[e.jsx(wt,{}),e.jsx("span",{children:"Yeni Blog Yazısı"})]}),children:e.jsxs(O,{form:t,onFinish:U,layout:"vertical",style:{maxWidth:"800px",margin:"0 auto"},children:[e.jsxs(ke,{gutter:16,children:[e.jsx(N,{xs:24,md:16,children:e.jsx(O.Item,{name:"title",label:"Blog Başlığı",rules:[{required:!0,message:"Blog başlığı gerekli!"}],children:e.jsx(me,{placeholder:"Blog başlığını girin",size:"large"})})}),e.jsx(N,{xs:24,md:8,children:e.jsx(O.Item,{name:"slug",label:"URL Slug",tooltip:"Boş bırakılırsa başlıktan otomatik oluşturulur",children:e.jsx(me,{placeholder:"url-slug",size:"large"})})})]}),e.jsx(O.Item,{name:"excerpt",label:"Kısa Açıklama",tooltip:"SEO ve sosyal medya paylaşımları için önemli",children:e.jsx(Xs,{rows:3,placeholder:"Blog yazısının kısa açıklaması...",showCount:!0,maxLength:200})}),e.jsx(O.Item,{name:"content",label:"Blog İçeriği",rules:[{required:!0,message:"Blog içeriği gerekli!"}],children:e.jsx(Xs,{rows:12,placeholder:"Blog yazısının tam içeriğini buraya yazın...",showCount:!0})}),e.jsxs(ke,{gutter:16,children:[e.jsx(N,{xs:24,md:12,children:e.jsx(O.Item,{name:"thumbnail",label:"Kapak Görseli URL",tooltip:"Blog yazısının kapak görseli",children:e.jsx(me,{placeholder:"https://example.com/image.jpg",size:"large"})})}),e.jsx(N,{xs:24,md:12,children:e.jsx(O.Item,{name:"meta_description",label:"Meta Açıklama",tooltip:"SEO için meta açıklama",children:e.jsx(me,{placeholder:"SEO meta açıklaması",size:"large"})})})]}),e.jsx(O.Item,{style:{textAlign:"center",marginTop:32},children:e.jsxs(ce,{size:"large",children:[e.jsx(I,{type:"primary",htmlType:"submit",loading:h,size:"large",icon:e.jsx(wt,{}),children:"Blog Yazısını Yayınla"}),e.jsx(I,{size:"large",onClick:()=>t.resetFields(),children:"Formu Temizle"})]})})]})},"add"),e.jsx(No,{tab:e.jsxs(ce,{children:[e.jsx(Xt,{}),e.jsx("span",{children:"Blog Yazıları"}),H.total>0&&e.jsx(ae,{color:"blue",children:H.total})]}),children:e.jsx(qe,{columns:B,dataSource:r,rowKey:"id",pagination:{pageSize:15,showSizeChanger:!0,showQuickJumper:!0,showTotal:(M,X)=>`${X[0]}-${X[1]} / ${M} blog yazısı`},scroll:{x:1200},size:"middle",style:{marginTop:16}})},"list")]})}),e.jsx(St,{title:e.jsxs(ce,{children:[e.jsx(yt,{}),e.jsx("span",{children:"Blog Yazısını Düzenle"})]}),open:b,onCancel:J,footer:null,width:900,style:{top:20},destroyOnClose:!0,children:e.jsxs(O,{form:s,onFinish:G,layout:"vertical",style:{marginTop:24},children:[e.jsxs(ke,{gutter:16,children:[e.jsx(N,{xs:24,md:16,children:e.jsx(O.Item,{name:"title",label:"Blog Başlığı",rules:[{required:!0,message:"Blog başlığı gerekli!"}],children:e.jsx(me,{placeholder:"Blog başlığını girin"})})}),e.jsx(N,{xs:24,md:8,children:e.jsx(O.Item,{name:"slug",label:"URL Slug",children:e.jsx(me,{placeholder:"url-slug"})})})]}),e.jsx(O.Item,{name:"excerpt",label:"Kısa Açıklama",children:e.jsx(Xs,{rows:3,placeholder:"Blog yazısının kısa açıklaması..."})}),e.jsx(O.Item,{name:"content",label:"Blog İçeriği",rules:[{required:!0,message:"Blog içeriği gerekli!"}],children:e.jsx(Xs,{rows:10,placeholder:"Blog yazısının tam içeriğini buraya yazın..."})}),e.jsxs(ke,{gutter:16,children:[e.jsx(N,{xs:24,md:12,children:e.jsx(O.Item,{name:"thumbnail",label:"Kapak Görseli URL",children:e.jsx(me,{placeholder:"https://example.com/image.jpg"})})}),e.jsx(N,{xs:24,md:12,children:e.jsx(O.Item,{name:"meta_description",label:"Meta Açıklama",children:e.jsx(me,{placeholder:"SEO meta açıklaması"})})})]}),e.jsx(O.Item,{style:{textAlign:"center",marginTop:24},children:e.jsxs(ce,{size:"large",children:[e.jsx(I,{type:"primary",htmlType:"submit",loading:h,icon:e.jsx(yt,{}),children:"Değişiklikleri Kaydet"}),e.jsx(I,{onClick:J,children:"İptal"})]})})]})})]})},Ny=()=>{const[t,s]=u.useState([]),[r,n]=u.useState(!1),[d,c]=u.useState(!1),[h,m]=u.useState(null),[y]=O.useForm(),p=async()=>{n(!0);try{const w=await de.get("/backend/site/yayincilar_site.php?action=list");s(w.data.data||[])}catch(w){S.error("Yayıncılar yüklenirken bir hata oluştu!")}finally{n(!1)}};u.useEffect(()=>{p()},[]);const j=()=>{m(null),y.resetFields(),c(!0)},x=w=>{m(w),y.setFieldsValue({...w,isFeatured:w.isFeatured==="1"}),c(!0)},b=async w=>{if(window.confirm("Bu yayıncıyı silmek istediğinize emin misiniz?")){n(!0);try{await de.post("/backend/site/yayincilar_site.php?action=delete",{id:w}),S.success("Yayıncı başarıyla silindi!"),p()}catch(g){S.error("Yayıncı silinirken bir hata oluştu!")}finally{n(!1)}}},C=async w=>{n(!0);try{const g={...w,id:h==null?void 0:h.id};h?(await de.post("/backend/site/yayincilar_site.php?action=update",{...g,id:h.id}),S.success("Yayıncı başarıyla güncellendi!")):(await de.post("/backend/site/yayincilar_site.php?action=add",g),S.success("Yayıncı başarıyla eklendi!")),p(),c(!1),y.resetFields(),m(null)}catch(g){S.error("İşlem sırasında bir hata oluştu!")}finally{n(!1)}},_=async()=>{n(!0);try{const w=await de.post("/backend/site/yayincilar_site.php?action=update_all_images");S.success(`${w.data.updated||0} yayıncı resmi güncellendi!`),p()}catch(w){S.error("Resimler güncellenirken bir hata oluştu!")}finally{n(!1)}};return e.jsxs("div",{children:[e.jsx("h2",{children:"Yayıncılarımız"}),e.jsxs("div",{style:{display:"flex",gap:12,marginBottom:24},children:[e.jsx(I,{type:"primary",className:"yayinci-ekle-btn",onClick:j,children:"+ Yayıncı Ekle"}),e.jsx(I,{className:"yayinci-duzenle-btn",onClick:_,children:"Profil Fotoğraflarını Güncelle"})]}),e.jsx(ke,{gutter:[16,16],children:t.map(w=>e.jsx(N,{xs:24,sm:12,md:8,lg:4,children:e.jsx(q,{cover:w.image?e.jsx("img",{alt:w.fullname,src:w.image,style:{height:160,objectFit:"cover"}}):null,actions:[e.jsx(I,{type:"primary",size:"small",className:"yayinci-duzenle-btn",onClick:()=>x(w),children:"Düzenle"}),e.jsx(it,{title:"Silmek istediğinize emin misiniz?",onConfirm:()=>b(w.id),okText:"Evet",cancelText:"Vazgeç",children:e.jsx(I,{danger:!0,size:"small",children:"Sil"})})],children:e.jsx(q.Meta,{title:e.jsxs("span",{children:[w.fullname," ",e.jsxs("span",{style:{color:"#888",fontSize:14},children:["@",w.username]})]}),description:e.jsx(e.Fragment,{children:w.isFeatured==="1"&&e.jsx("div",{style:{color:"#1890ff",fontWeight:500},children:"Öne Çıkan"})})})})},w.id))}),e.jsx(St,{title:h?"Yayıncıyı Düzenle":"Yeni Yayıncı Ekle",open:d,onCancel:()=>c(!1),onOk:()=>y.submit(),okText:h?"Güncelle":"Ekle",cancelText:"Vazgeç",children:e.jsxs(O,{form:y,layout:"vertical",onFinish:C,children:[e.jsxs(O.Item,{name:"username",label:"TikTok Kullanıcı Adı",rules:[{required:!0,message:"Kullanıcı adı giriniz"}],children:[" ",e.jsx(me,{prefix:"@"})," "]}),e.jsxs(O.Item,{name:"fullname",label:"Sitede Görünen Ad",rules:[{required:!0,message:"Ad giriniz"}],children:[" ",e.jsx(me,{})," "]}),e.jsxs(O.Item,{name:"isFeatured",valuePropName:"checked",children:[" ",e.jsx(wa,{children:"Öne Çıkan Yapılsın mı?"})," "]})]})})]})},{Title:Yy,Paragraph:$y}=ht,{TabPane:Qs}=Xe,By=[{title:"Ad",dataIndex:"name",key:"name"},{title:"Telefon",dataIndex:"phone",key:"phone"},{title:"Web",dataIndex:"website",key:"website",render:t=>t?e.jsx("a",{href:`https://${t}`,target:"_blank",rel:"noopener noreferrer",children:t}):"-"},{title:"E-posta",dataIndex:"email",key:"email"},{title:"Kategori",dataIndex:"category",key:"category",render:t=>e.jsx(ae,{color:"blue",children:t})}],Fy={real_estate_agency:"Emlak Ofisi",beauty_salon:"Güzellik Salonu",cafe:"Kafe",restaurant:"Restoran",hotel:"Otel",bar:"Bar",gym:"Spor Salonu",car_dealer:"Oto Galeri",bank:"Banka",pharmacy:"Eczane",point_of_interest:"Diğer"},Ya=t=>Fy[t]||t;function Hy(){const t=localStorage.getItem("bd_categories");if(t)try{const s=JSON.parse(t);if(Array.isArray(s))return s}catch(s){}return["real_estate_agency","beauty_salon","cafe","restaurant","hotel","bar","gym","car_dealer","bank","pharmacy"]}function Wy(t){const s=localStorage.getItem("businessDiscoveryVisibleColumns");if(s)try{const r=JSON.parse(s);if(Array.isArray(r))return r}catch(r){}return t}function Yo(t,s){const[r,n]=u.useState(t);return u.useEffect(()=>{const d=setTimeout(()=>n(t),s);return()=>clearTimeout(d)},[t,s]),r}const Uy=()=>{const[t,s]=u.useState("fetch"),[r,n]=u.useState("local"),[d,c]=u.useState(""),[h,m]=u.useState(""),[y,p]=u.useState(!1),[j,x]=u.useState([]),[b,C]=u.useState([]),[_,w]=u.useState(""),[g,H]=u.useState(!1),U=u.useRef(null),[T,W]=u.useState(10),[G]=O.useForm(),[J,B]=u.useState(localStorage.getItem("bd_defaultCountry")||"local"),[M,X]=u.useState(localStorage.getItem("bd_defaultCategory")||""),[oe,K]=u.useState(Number(localStorage.getItem("bd_defaultPageSize"))||10),[ye,_e]=u.useState(localStorage.getItem("bd_autoRefresh")==="true"),[Re,A]=u.useState(localStorage.getItem("bd_googleApiKey")||""),[F,ie]=u.useState(Hy()),je=Yo(d,400),De=Yo(h,400);u.useEffect(()=>{let R="";je&&De?R=`${De} in ${je}`:je?R=je:De?R=De:R="Istanbul, Turkey";const re=encodeURIComponent(R),Qe=localStorage.getItem("bd_googleApiKey")||"AIzaSyD5mlaD_xPZDJ2g6xV748NCRTZACVcnnHw";w(`https://www.google.com/maps/embed/v1/search?key=${Qe}&q=${re}`)},[je,De]),u.useEffect(()=>(Y(),()=>{U.current&&U.current.cancel("Component unmounted")}),[]);const Y=async()=>{var R;p(!0);try{const re=await de.get("/backend/x-site/business.php");re.data&&re.data.success?(C(re.data.data.map(Qe=>({...Qe,key:Qe.id}))),S.success("Veriler başarıyla yüklendi")):S.error("Veriler alınamadı: "+(((R=re.data)==null?void 0:R.error)||"Bilinmeyen hata"))}catch(re){S.error(`Sunucu hatası: ${re.message||"Bağlantı hatası"}`)}p(!1)},pe=()=>{U.current&&(U.current.cancel("İşlem kullanıcı tarafından durduruldu"),U.current=null),H(!1),p(!1),S.info("Veri çekme işlemi durduruldu"),Y()},[Oe,be]=u.useState(""),Ee=R=>{if(R&&!F.includes(R)){const re=[...F,R];ie(re),localStorage.setItem("bd_categories",JSON.stringify(re))}},Z=async()=>{var R;if(g){pe();return}if(!d&&!Oe){S.warning("Lütfen en az bir arama kriteri girin (konum veya kategori)");return}p(!0),H(!0),x([]),S.loading("İşletmeler aranıyor...",0),U.current=de.CancelToken.source();try{const re=await de.post("/backend/x-site/business.php",{action:"search",query:Ya(Oe),location:d,category_code:Oe},{cancelToken:U.current.token});if(re.data&&re.data.success&&Array.isArray(re.data.places)){let Qe=!0;for(const Je of re.data.places)x(He=>[...He,{...Je,key:Je.place_id,category:Oe}]),Qe&&(p(!1),Qe=!1),await new Promise(He=>setTimeout(He,150));S.success(`${re.data.message}`),Y()}else S.error(`Arama hatası: ${((R=re.data)==null?void 0:R.error)||"Bilinmeyen hata"}`)}catch(re){de.isCancel(re)||S.error(`İşlem hatası: ${re.message||"Bağlantı hatası"}`)}finally{H(!1),p(!1),S.destroy()}},D=()=>e.jsx("div",{style:{width:"100%",height:180,marginTop:16,borderRadius:8,overflow:"hidden",border:"1px solid #eee"},children:e.jsx("iframe",{width:"100%",height:"100%",frameBorder:"0",style:{border:0},src:_||"https://www.google.com/maps/embed/v1/search?key=AIzaSyD5mlaD_xPZDJ2g6xV748NCRTZACVcnnHw&q=Istanbul,Turkey",allowFullScreen:!0,title:"Google Map"})}),Q={background:g?"#ff4d4f":"#1677ff",color:"#fff",border:"none",fontWeight:500,boxShadow:"0 2px 8px rgba(22,119,255,0.08)",transition:"all 0.2s"},k=async R=>{try{await de.post("/backend/x-site/business.php",{action:"delete",id:R}),C(re=>re.filter(Qe=>Qe.id!==R)),S.success("Kayıt silindi")}catch(re){S.error("Silme işlemi başarısız")}},ge=[{title:"Ad",dataIndex:"name",key:"name"},{title:"Adres",dataIndex:"address",key:"address"},{title:"Telefon",dataIndex:"phone",key:"phone"},{title:"Web",dataIndex:"website",key:"website",render:R=>R?e.jsx("a",{href:`https://${R}`,target:"_blank",rel:"noopener noreferrer",children:R}):"-"},{title:"E-posta",dataIndex:"email",key:"email"},{title:"Kategori",dataIndex:"category",key:"category",render:R=>e.jsx(ae,{color:"blue",children:R})},{title:"İşlemler",key:"actions",render:(R,re)=>e.jsx(it,{title:"Bu kaydı silmek istediğinize emin misiniz?",onConfirm:()=>k(re.id),okText:"Evet",cancelText:"Hayır",okButtonProps:{type:"primary",style:{order:1}},cancelButtonProps:{type:"default",style:{order:0}},placement:"left",children:e.jsx(I,{danger:!0,type:"link",children:"Sil"})})}],$=ge.map(R=>R.dataIndex).filter(Boolean),[te,Ye]=u.useState(Wy($));u.useEffect(()=>{localStorage.setItem("businessDiscoveryVisibleColumns",JSON.stringify(te))},[te]);const jt=[...ge.filter(R=>R.dataIndex&&te.includes(R.dataIndex)),ge[ge.length-1]],aa=e.jsx("div",{style:{padding:12,minWidth:180,maxWidth:260,background:"#fff",boxShadow:"0 4px 24px rgba(0,0,0,0.12)",borderRadius:8,maxHeight:350,overflowY:"auto",zIndex:9999},children:e.jsx(wa.Group,{value:te,onChange:R=>Ye(R),style:{display:"flex",flexDirection:"column",gap:12},children:ge.map(R=>e.jsx(wa,{value:R.dataIndex,children:R.title},R.dataIndex))})}),[Bt,sa]=u.useState("Tümü"),P=Array.from(new Set(b.map(R=>R.category).filter(Boolean))),ue=Bt==="Tümü"?b:b.filter(R=>R.category===Bt),xe=R=>{localStorage.setItem("bd_defaultCountry",R.defaultCountry),localStorage.setItem("bd_defaultCategory",R.defaultCategory||""),localStorage.setItem("bd_defaultPageSize",R.defaultPageSize),localStorage.setItem("bd_autoRefresh",R.autoRefresh),R.apiKey&&localStorage.setItem("bd_googleApiKey",R.apiKey),B(R.defaultCountry),X(R.defaultCategory),K(R.defaultPageSize),_e(R.autoRefresh),R.apiKey&&A(R.apiKey),S.success("Ayarlar kaydedildi")},Me=["#0088FE","#00C49F","#FFBB28","#FF8042","#A28FD0","#FF6699","#FFB347","#B6E880","#FF6F61","#6B5B95","#88B04B","#F7CAC9","#92A8D1","#955251","#B565A7","#009B77","#DD4124","#45B8AC","#EFC050","#5B5EA6","#9B2335","#DFCFBE","#55B4B0","#E15D44","#7FCDCD","#BC243C","#C3447A","#98B4D4","#C3B299","#C48F65","#BFD641","#8860D0","#009688","#F7786B","#034F84","#F5A623","#D7263D","#3F681C","#375E97","#FF7F50","#B7B8B6","#6B4226","#D9BF77","#6C4F3D","#FFA07A","#B2BABB","#F1948A","#2471A3","#D4AC0D","#229954","#A569BD","#E67E22","#1ABC9C","#F1C40F","#E74C3C","#34495E","#2ECC71","#3498DB","#9B59B6","#16A085","#27AE60","#2980B9","#8E44AD","#2C3E50","#F39C12","#D35400","#C0392B","#BDC3C7","#7F8C8D","#F0B27A","#D5DBDB","#AAB7B8","#85929E","#A9CCE3","#A3E4D7","#F9E79F","#F5CBA7","#D7BDE2","#FADBD8","#D2B4DE","#A569BD"];return e.jsxs(q,{style:{minHeight:600},children:[e.jsx(Yy,{level:3,style:{marginBottom:0},children:"İşletme Keşfi"}),e.jsx($y,{type:"secondary",style:{marginBottom:24},children:"Google Maps ve diğer kaynaklardan işletme verisi toplayabilir, kategoriye ve konuma göre arama yaparak potansiyel iş ortaklarınızı keşfedebilirsiniz."}),e.jsxs(Xe,{activeKey:t,onChange:s,children:[e.jsxs(Qs,{tab:e.jsxs("span",{children:[e.jsx(T0,{})," Veri Toplama"]}),children:[e.jsxs(ke,{gutter:16,style:{marginBottom:16},children:[e.jsx(N,{children:e.jsxs(Ce,{value:r,onChange:R=>n(R),style:{width:120},children:[e.jsx(Ce.Option,{value:"local",children:"Türkiye"}),e.jsx(Ce.Option,{value:"global",children:"Global"})]})}),e.jsx(N,{children:e.jsx(me,{placeholder:"Konum (örn: İstanbul, Paris, ... )",value:d,onChange:R=>c(R.target.value),style:{width:180}})}),e.jsx(N,{children:e.jsx(Ce,{showSearch:!0,value:Oe,onChange:R=>be(R),style:{width:220},placeholder:"Kategori seçin",dropdownRender:R=>e.jsxs(e.Fragment,{children:[R,e.jsx("div",{style:{display:"flex",alignItems:"center",padding:8},children:e.jsx(me,{style:{flex:1},placeholder:"+ Yeni Kategori Ekle",onPressEnter:re=>{const Qe=re.target.value.trim();Qe&&(Ee(Qe),be(Qe))}})})]}),optionFilterProp:"children",filterOption:(R,re)=>String(re==null?void 0:re.children).toLowerCase().includes(R.toLowerCase()),children:F.map(R=>e.jsx(Ce.Option,{value:R,children:Ya(R)},R))})}),e.jsx(N,{children:e.jsx(I,{type:"primary",danger:g,icon:g?e.jsx(ex,{}):e.jsx(ta,{}),loading:y&&!g,onClick:Z,style:Q,disabled:!Oe,children:g?"Durdur":"Veri Çek"})}),e.jsx(N,{children:e.jsx(I,{icon:e.jsx(qi,{}),loading:y,onClick:Y,children:"Verileri Yenile"})})]}),e.jsx(qe,{columns:By.map(R=>R.dataIndex==="category"?{...R,render:re=>e.jsx(ae,{color:"blue",children:Ya(re)})}:R),dataSource:j,loading:y,pagination:{pageSize:T,showSizeChanger:!0,pageSizeOptions:["5","10","20","50"],onShowSizeChange:(R,re)=>W(re),onChange:(R,re)=>W(re||5)},bordered:!0,size:"middle",style:{background:"#fff",borderRadius:8}}),e.jsx(D,{})]},"fetch"),e.jsxs(Qs,{tab:e.jsxs("span",{children:[e.jsx(vx,{})," Kayıtlar"]}),children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:12},children:[e.jsxs(Ce,{value:Bt,onChange:sa,style:{width:200},placeholder:"Kategori Seç",children:[e.jsx(Ce.Option,{value:"Tümü",children:"Tümü"}),P.map(R=>e.jsx(Ce.Option,{value:R,children:Ya(R)},R))]}),e.jsx(dn,{overlay:aa,trigger:["click"],placement:"bottomRight",children:e.jsx(I,{style:{marginLeft:12},children:"Filtrele"})})]}),e.jsx(qe,{columns:jt,dataSource:ue.map(R=>({...R,key:R.id})),loading:y,pagination:{pageSize:T,showSizeChanger:!0,pageSizeOptions:["5","10","20","50"],onShowSizeChange:(R,re)=>W(re),onChange:(R,re)=>W(re||5)},bordered:!0,size:"middle",style:{background:"#fff",borderRadius:8}})]},"list"),e.jsxs(Qs,{tab:e.jsxs("span",{children:[e.jsx(c0,{})," İstatistikler"]}),children:[e.jsxs(ke,{gutter:16,style:{marginBottom:24},children:[e.jsx(N,{span:6,children:e.jsx(q,{children:e.jsx(Te,{title:"Toplam İşletme Kaydı",value:b.length})})}),e.jsx(N,{span:6,children:e.jsx(q,{children:e.jsx(Te,{title:"Son Çekilen Veri Adedi",value:j.length})})}),e.jsx(N,{span:6,children:e.jsx(q,{children:e.jsx(Te,{title:"En Çok Çekilen Kategori",value:(()=>{var R;const re={};b.forEach(Je=>{Je.category&&(re[Je.category]=(re[Je.category]||0)+1)});const Qe=Object.entries(re).sort((Je,He)=>He[1]-Je[1]);return Ya(((R=Qe[0])==null?void 0:R[0])||"-")})()})})})]}),e.jsx(ke,{gutter:32,children:e.jsx(N,{span:12,children:e.jsx(q,{title:"Kategori Dağılımı",children:e.jsx(Wf,{width:"100%",height:320,children:e.jsxs(Uf,{children:[e.jsx(Vf,{data:Object.entries(b.reduce((R,re)=>(re.category&&(R[re.category]=(R[re.category]||0)+1),R),{})).map(([R,re])=>({type:Ya(R),value:re})),dataKey:"value",nameKey:"type",cx:"50%",cy:"50%",outerRadius:110,label:({name:R,percent:re})=>`${R} ${(re*100).toFixed(1)}%`,children:Object.entries(b.reduce((R,re)=>(re.category&&(R[re.category]=(R[re.category]||0)+1),R),{})).map(([R],re)=>e.jsx(Kf,{fill:Me[re%Me.length]},`cell-cat-${R}`))}),e.jsx(Gf,{formatter:R=>R.toLocaleString("tr-TR")}),e.jsx(qf,{})]})})})})})]},"stats"),e.jsx(Qs,{tab:e.jsxs("span",{children:[e.jsx(_s,{})," Ayarlar"]}),children:e.jsxs(O,{form:G,layout:"vertical",initialValues:{apiKey:Re},onFinish:xe,style:{maxWidth:400,margin:0},children:[e.jsx(O.Item,{label:"Google API Anahtarı",name:"apiKey",children:e.jsx(me.Password,{placeholder:"Google API Key",autoComplete:"off"})}),e.jsx(O.Item,{children:e.jsx(I,{type:"primary",htmlType:"submit",style:{background:"#1677ff",color:"#fff",borderColor:"#1677ff"},children:"Kaydet"})})]})},"settings")]})]})},Vy=u.lazy(()=>tt(()=>import("./Dashboard-B4Obnw4d.js"),__vite__mapDeps([0,1,2,3,4,5,6,7])));u.lazy(()=>tt(()=>import("./Publishers-CTlyjTfy.js"),__vite__mapDeps([8,1,2,3,6,7,4,9,10,11,12,13,14,5])));u.lazy(()=>tt(()=>import("./Influencers-70hmI_5J.js"),__vite__mapDeps([15,1,2,16,10,6,7,4,9,11,13,17,3,5])));const Ky=u.lazy(()=>tt(()=>import("./ContentCreators-X0fNTlV-.js"),__vite__mapDeps([18,1,2,8,3,6,7,4,9,10,11,12,13,14,5,15,16,17]))),Gy=u.lazy(()=>tt(()=>import("./PublisherPerformance-_-pF5EqC.js"),__vite__mapDeps([19,1,2,6,7,4,20,13,3,21,10,22,14,23,5]))),qy=u.lazy(()=>tt(()=>import("./AIAdvisor-tp1PB3a7.js"),__vite__mapDeps([24,1,2,25,10,3,17,14,9,23,6,7,4,5]))),Jy=u.lazy(()=>tt(()=>import("./EventManagement-BtmkU9Rt.js"),__vite__mapDeps([26,1,2,3,4,21,27,6,7,28,29,5,30,31])));u.lazy(()=>tt(()=>import("./AIEventAdvisor-9LfF0ZBE.js"),__vite__mapDeps([28,1,2,4,29,6,7,3,5])));const Zy=u.lazy(()=>tt(()=>import("./PKMatcher-BzjVSg2y.js"),__vite__mapDeps([32,1,2,4,21,27,6,7,30,29,3,5]))),Xy=u.lazy(()=>tt(()=>import("./NotFound-DoxyyHuC.js"),__vite__mapDeps([33,1,2]))),Qy=u.lazy(()=>tt(()=>import("./EventDetail-DHBhnmmb.js"),__vite__mapDeps([34,1,2,3,35,10,27,6,7,4,36,5]))),e2=u.lazy(()=>tt(()=>import("./EventWizard-D44LBqfP.js"),__vite__mapDeps([37,1,2,3,35,10,27,6,7,4,38,5]))),t2=u.lazy(()=>tt(()=>import("./Performance-BinvSQbn.js"),__vite__mapDeps([39,1,2,16,10,6,7,35,4,25,3,9,5]))),a2=u.lazy(()=>tt(()=>import("./TournamentBracket-BjzZw5y1.js"),__vite__mapDeps([40,1,2,3,35,10,27,6,7,4,36,5]))),s2=u.lazy(()=>tt(()=>import("./Unauthorized-Db9tJymV.js"),__vite__mapDeps([41,1,2,4]))),r2=u.lazy(()=>tt(()=>import("./Dashboard-DMWGOHDy.js"),__vite__mapDeps([42,1,2,4,6,7,3,5]))),n2=u.lazy(()=>tt(()=>import("./TasarimOnayi-DEyVG0P1.js"),__vite__mapDeps([43,1,2,4,21,20,6,7,3,5]))),i2=u.lazy(()=>tt(()=>import("./Urunler-oTjyzi3X.js"),__vite__mapDeps([44,1,2,4,6,7,3,5]))),l2=u.lazy(()=>tt(()=>import("./Ayarlar-BIRGft4J.js"),__vite__mapDeps([45,1,2,4,6,7,3,5]))),o2=u.lazy(()=>tt(()=>import("./WhatsAppManager-D7FOKXCv.js"),__vite__mapDeps([46,1,2,6,7,3,4,12,31,22,38,20,11,5,47]))),c2=()=>(u.useEffect(()=>{const s=setTimeout(async()=>{try{if(document.getElementById("elevenlabs-convai-widget"))return;const r=document.createElement("script");r.src="https://elevenlabs.io/convai-widget/index.js",r.async=!0,r.type="text/javascript",r.id="elevenlabs-convai-script",r.crossOrigin="anonymous",r.referrerPolicy="strict-origin-when-cross-origin";const n=new Promise((c,h)=>{r.onload=()=>{c(!0)},r.onerror=m=>{c(!1)},setTimeout(()=>{c(!1)},1e4)});document.head.appendChild(r);const d=await n;setTimeout(()=>{if(!document.getElementById("elevenlabs-convai-widget")){const c=document.createElement("div");if(c.id="elevenlabs-convai-widget",c.innerHTML='<elevenlabs-convai agent-id="agent_01jvtjx9sbf19b7pf7y9nmh7t9"></elevenlabs-convai>',document.body.appendChild(c),!d)try{const h=new CustomEvent("elevenlabs-widget-ready",{detail:{agentId:"agent_01jvtjx9sbf19b7pf7y9nmh7t9"}});window.dispatchEvent(h)}catch(h){}}},1500)}catch(r){const n=document.getElementById("elevenlabs-convai-script");n&&n.remove()}},2e3);return()=>{clearTimeout(s);const r=document.getElementById("elevenlabs-convai-widget"),n=document.getElementById("elevenlabs-convai-script");r&&r.remove(),n&&n.remove()}},[]),null),Ji=()=>e.jsx("div",{className:"flex items-center justify-center h-screen bg-gray-100 dark:bg-gray-900",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"})}),lt=({children:t,requiredRoles:s=[],requiredPermission:r})=>{const{user:n,loading:d}=Ct();if(d)return e.jsx(Ji,{});if(!n)return e.jsx(ba,{to:"/login",replace:!0});if(s.length>0&&!s.includes(n.role))return e.jsx(ba,{to:"/unauthorized",replace:!0});if(r&&n.role!=="admin"){if(!n.permissions)return e.jsx(ba,{to:"/unauthorized",replace:!0});const c=r.split(".");let h=!1;if(c.length===1)h=!!n.permissions[c[0]];else if(c.length===2){const[m,y]=c,p=n.permissions[m];typeof p=="object"&&p!==null&&(h=!!p[y])}if(!h)return e.jsx(ba,{to:"/unauthorized",replace:!0})}return e.jsx(e.Fragment,{children:t})},d2=()=>{const{darkMode:t}=fr(),{loading:s,error:r}=Fi(),{user:n}=Ct();return s?e.jsx(Ji,{}):r?e.jsx("div",{className:"flex items-center justify-center h-screen p-4",children:e.jsxs("p",{className:"text-red-500 text-lg",children:["Veri yükleme hatası: ",r]})}):e.jsx(Ff,{theme:{algorithm:t?Ll.darkAlgorithm:Ll.defaultAlgorithm,token:{colorPrimary:"#1890ff",colorSuccess:"#10B981",colorWarning:"#F59E0B",colorError:"#EF4444",colorInfo:"#3B82F6",borderRadius:8},components:{Card:{colorBgContainer:t?"#1f2937":"#ffffff",colorBorderSecondary:t?"#374151":void 0,colorText:t?"#e5e7eb":void 0},Table:{colorBgContainer:t?"#1f2937":"#ffffff",colorText:t?"#e5e7eb":void 0},Tabs:{colorBgContainer:t?"#1f2937":"#ffffff",colorText:t?"#e5e7eb":void 0},Modal:{colorBgElevated:t?"#1f2937":"#ffffff",colorText:t?"#e5e7eb":void 0,colorPrimary:"#1890ff",colorPrimaryHover:"#40a9ff",colorPrimaryActive:"#096dd9",colorPrimaryText:"#ffffff"},Select:{colorBgContainer:t?"#374151":"#ffffff",colorBgElevated:t?"#1f2937":"#ffffff",colorText:t?"#e5e7eb":void 0,controlItemBgActive:t?"#177ddc":"#e6f7ff"},Input:{colorBgContainer:t?"#374151":"#ffffff",colorText:t?"#e5e7eb":void 0,colorBorder:t?"#4B5563":void 0},Button:{}}},locale:T1,children:e.jsxs(Hf,{children:[e.jsxs(Bo,{children:[e.jsx(Se,{path:"/unauthorized",element:e.jsx(s2,{})}),e.jsxs(Se,{path:"/*",element:e.jsx(Fx,{}),children:[e.jsx(Se,{index:!0,element:e.jsx(Vy,{})}),e.jsx(Se,{path:"notifications",element:e.jsx(lt,{children:e.jsx(Iy,{})})}),e.jsx(Se,{path:"content-creators",element:e.jsx(lt,{requiredPermission:"publishers",children:e.jsx(Ky,{})})}),e.jsx(Se,{path:"publisher-performance",element:e.jsx(lt,{requiredPermission:"tasks",children:e.jsx(Gy,{})})}),e.jsx(Se,{path:"ai-advisor",element:e.jsx(lt,{requiredPermission:"ai_advisor",children:e.jsx(qy,{})})}),e.jsx(Se,{path:"publisher-discovery",element:e.jsx(lt,{requiredPermission:"ai_advisor",children:e.jsx(Ey,{})})}),e.jsx(Se,{path:"events",element:e.jsx(lt,{requiredPermission:"events.view",children:e.jsx(Jy,{})})}),e.jsx(Se,{path:"event/create",element:e.jsx(lt,{requiredPermission:"events.manage",children:e.jsx(e2,{})})}),e.jsx(Se,{path:"event/:id",element:e.jsx(lt,{requiredPermission:"events.view",children:e.jsx(Qy,{})})}),e.jsx(Se,{path:"pk-matcher",element:e.jsx(lt,{requiredPermission:"events.pk_matcher",children:e.jsx(Zy,{})})}),e.jsx(Se,{path:"performance",element:e.jsx(lt,{requiredPermission:"performance",children:e.jsx(t2,{})})}),e.jsx(Se,{path:"tournament",element:e.jsx(lt,{requiredPermission:"tournament",children:e.jsx(a2,{})})}),e.jsx(Se,{path:"users",element:e.jsx(lt,{requiredRoles:["admin"],requiredPermission:"users.manage",children:e.jsx(Kx,{})})}),e.jsxs(Se,{path:"akademi",element:e.jsx(lt,{requiredPermission:"akademi.dashboard",children:e.jsx(u2,{})}),children:[e.jsx(Se,{index:!0,element:e.jsx(ba,{to:"dashboard",replace:!0})}),e.jsx(Se,{path:"dashboard",element:e.jsx(uy,{})}),e.jsx(Se,{path:"duyurular",element:e.jsx(qx,{})}),e.jsx(Se,{path:"egitimler",element:e.jsx(Jx,{})}),e.jsx(Se,{path:"etkinlikler",element:e.jsx(ty,{})}),e.jsx(Se,{path:"destek",element:e.jsx(ny,{})}),e.jsx(Se,{path:"kullanicilar",element:e.jsx(oy,{})}),e.jsx(Se,{path:"yapay-zeka",element:e.jsx(cy,{})})]}),e.jsxs(Se,{path:"site",element:e.jsx(lt,{requiredPermission:"site_yonetimi.anasayfa",children:e.jsx(h2,{})}),children:[e.jsx(Se,{index:!0,element:e.jsx(ba,{to:"anasayfa",replace:!0})}),e.jsx(Se,{path:"anasayfa",element:e.jsx(hy,{})}),e.jsx(Se,{path:"yayincilar",element:e.jsx(Ny,{})}),e.jsx(Se,{path:"basvurular",element:e.jsx(py,{})}),e.jsx(Se,{path:"iletisim-talepleri",element:e.jsx(yy,{})}),e.jsx(Se,{path:"geriarama-talepleri",element:e.jsx(vy,{})}),e.jsx(Se,{path:"toplanti-talepleri",element:e.jsx(Sy,{})}),e.jsx(Se,{path:"toplu-sms",element:e.jsx("div",{children:"Toplu SMS (Yapım aşamasında)"})}),e.jsx(Se,{path:"blog",element:e.jsx(Ly,{})})]}),e.jsxs(Se,{path:"etsy",element:e.jsx(lt,{requiredPermission:"etsy_operasyonu.dashboard",children:e.jsx(f2,{})}),children:[e.jsx(Se,{index:!0,element:e.jsx(ba,{to:"dashboard",replace:!0})}),e.jsx(Se,{path:"dashboard",element:e.jsx(r2,{})}),e.jsx(Se,{path:"tasarim-onaylari",element:e.jsx(n2,{})}),e.jsx(Se,{path:"urunler",element:e.jsx(i2,{})}),e.jsx(Se,{path:"ayarlar",element:e.jsx(l2,{})})]}),e.jsx(Se,{path:"whatsapp",element:e.jsx(lt,{requiredPermission:"whatsapp",children:e.jsx(o2,{})})}),e.jsx(Se,{path:"business-discovery",element:e.jsx(lt,{requiredPermission:"business_discovery",children:e.jsx(Uy,{})})}),e.jsx(Se,{path:"*",element:e.jsx(Xy,{})})]})]}),e.jsx(p1,{position:"top-right",toastOptions:{duration:3e3,style:{borderRadius:"6px",background:t?"#333":"#fff",color:t?"#fff":"#333",padding:"12px 16px",boxShadow:"0 4px 12px rgba(0, 0, 0, 0.1)"},className:"toast-closable"}}),n&&e.jsx(c2,{})]})})},u2=()=>e.jsx(fa,{style:{padding:"0"},children:e.jsx(ws,{})}),h2=()=>e.jsx(fa,{style:{padding:"0"},children:e.jsx(ws,{})}),f2=()=>e.jsx(fa,{style:{padding:"0"},children:e.jsx(ws,{})});function m2(){return e.jsx(b1,{fallback:e.jsx("div",{className:"error-fallback",children:"Bir hata oluştu. Lütfen sayfayı yenileyin."}),children:e.jsx(Jf,{children:e.jsx(g1,{children:e.jsx(O1,{children:e.jsxs(Bo,{children:[e.jsx(Se,{path:"/login",element:e.jsx(Hx,{})}),e.jsx(Se,{path:"*",element:e.jsx(u.Suspense,{fallback:e.jsx(Ji,{}),children:e.jsx(y1,{children:e.jsx(d2,{})})})})]})})})})})}const G2=Object.freeze(Object.defineProperty({__proto__:null,default:m2},Symbol.toStringTag,{value:"Module"}));export{Kn as A,c0 as B,j0 as C,yx as D,Cg as E,rr as F,ux as G,ox as H,le as I,Jn as J,Ct as K,Wa as L,Ic as M,_s as N,Vs as O,Xt as P,hr as Q,qi as R,ua as S,G2 as T,cs as V,Fi as a,ks as b,ea as c,ta as d,i0 as e,Ki as f,ji as g,Pi as h,It as i,fr as j,de as k,ot as l,fe as m,T0 as n,wt as o,yt as p,Et as q,Sr as r,Zr as s,ha as t,vr as u,Lc as v,st as w,nx as x,T1 as y,Vi as z};
