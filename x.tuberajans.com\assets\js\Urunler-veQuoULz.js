import{j as e}from"./reactDnd-uQSTYBkW.js";import{n as i,T as a,$ as r}from"./antd-BfejY-CV.js";import{g as l}from"./App-7woMCAzq.js";import"./vendor-CnpYymF8.js";import"./index-n-wHKc0W.js";import"./utils-CtuI0RRe.js";import"./charts-6B1FLgFz.js";const{Title:s,Paragraph:t}=a,j=()=>e.jsx("div",{className:"p-4",children:e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx(l,{style:{fontSize:24,marginRight:12}}),e.jsx(s,{level:3,style:{margin:0},children:"ETSY Ürünler"})]}),e.jsx(r,{message:"<PERSON><PERSON>yı Geliştirme",description:"Bu sayfa şu anda geliştirme aşamasındadır. ETSY'deki ürü<PERSON>in yönetimi, listelenmesi ve analizi burada yapılacaktır.",type:"info",showIcon:!0}),e.jsx(t,{className:"mt-4",children:"Bu sayfanın tamamlanacak özellikleri:"}),e.jsxs("ul",{className:"list-disc ml-8",children:[e.jsx("li",{children:"Ürün listesi ve arama özellikleri"}),e.jsx("li",{children:"Ürün detayları ve düzenleme"}),e.jsx("li",{children:"Satış istatistikleri"}),e.jsx("li",{children:"Stok takibi"}),e.jsx("li",{children:"Otomatik fiyatlandırma"}),e.jsx("li",{children:"Alıcı yorumları ve değerlendirmeleri"})]})]})});export{j as default};
