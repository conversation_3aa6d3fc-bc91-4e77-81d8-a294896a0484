<?php
/**
 * Etkinlikler API
 * Bu API etkinlikleri listelemek, eklemek, düzenlemek ve silmek için kullanılır
 */

// Hata ayıklama
ini_set('display_errors', 1);
error_reporting(E_ALL);

// CORS ve JSON header'ları
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS, DELETE');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=utf-8');

// OPTIONS istekleri için hızlıca 200 dön
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Ortak config dosyasını dahil et
require_once __DIR__ . '/../config/config.php';

// JSON yanıt fonksiyonu
if (!function_exists('jsonResponse')) {
    function jsonResponse($data, $status = 200) {
        http_response_code($status);
header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}

// Auth kontrolü (checkAuth fonksiyonu - diğer API'ler gibi)
if (!function_exists('checkAuth')) {
    function checkAuth() {
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
        
        if (strpos($authHeader, 'Bearer ') === 0) {
            $token = substr($authHeader, 7);
            if (strlen($token) > 10) { // Basit token kontrolü
                return true;
            }
        }
        return false;
    }
}

// İstek methodunu al
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

// Debug için log ekle
error_log("Events API - İstek methodu: " . $method);
error_log("Events API - Action: " . $action);
error_log("Events API - GET parametreleri: " . json_encode($_GET));

// Veritabanı bağlantısı
try {
    if (!isset($db_akademi)) {
        $db_akademi = new PDO("mysql:host=$servername;dbname=tuberaja_yayinci_akademi;charset=utf8mb4", $db_username, $db_password);
        $db_akademi->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }
} catch (PDOException $e) {
    jsonResponse(['success' => false, 'message' => 'Veritabanı bağlantı hatası: ' . $e->getMessage()], 500);
}

// GET isteği - Etkinlikleri ve Kategorileri Listele
if ($method === 'GET') {
    try {
        // Kategorileri Listele
        if ($action === 'list_categories') {
            error_log("Events API - Kategori listesi istendi");

            // Veritabanından mevcut kategorileri al
            $stmt = $db_akademi->prepare("
                SELECT
                    category,
                    COUNT(*) as event_count,
                    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_count,
                    COUNT(CASE WHEN is_featured = 1 THEN 1 END) as featured_count
                FROM events
                WHERE category IS NOT NULL AND category != ''
                GROUP BY category
                ORDER BY event_count DESC
            ");
            $stmt->execute();
            $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

            error_log("Events API - Veritabanından çekilen kategoriler: " . json_encode($categories));

            // Kategorileri formatla
            $all_categories = [];
            foreach ($categories as $cat) {
                $all_categories[] = [
                    'name' => $cat['category'],
                    'value' => $cat['category'],
                    'event_count' => (int)$cat['event_count'],
                    'active_count' => (int)$cat['active_count'],
                    'featured_count' => (int)$cat['featured_count']
                ];
            }

            error_log("Events API - Gönderilen kategoriler: " . json_encode($all_categories));
            jsonResponse(['success' => true, 'data' => $all_categories]);
        }

        // Test verisi ekleme endpoint'i
        if ($action === 'add_test_data') {
            // Test etkinlikleri ekle
            $testEvents = [
                [
                    'title' => 'Algoritma Güncelleme Webinarı',
                    'description' => 'TikTok algoritmasındaki son güncellemeler hakkında bilgi',
                    'start_date' => '2024-05-24 14:00:00',
                    'end_date' => '2024-05-24 16:00:00',
                    'location' => 'Online',
                    'capacity' => 100,
                    'category' => 'Webinar',
                    'status' => 'active'
                ],
                [
                    'title' => 'İçerik Üretimi Atölyesi',
                    'description' => 'Yaratıcı içerik üretme teknikleri',
                    'start_date' => '2024-05-25 10:00:00',
                    'end_date' => '2024-05-25 17:00:00',
                    'location' => 'İstanbul Ofis',
                    'capacity' => 30,
                    'category' => 'Atölye',
                    'status' => 'active'
                ]
            ];

            foreach ($testEvents as $event) {
                $stmt = $db_akademi->prepare("
                    INSERT INTO events (title, description, start_date, end_date, location, capacity, category, status, created_at, updated_at, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), 1)
                ");
                $stmt->execute([
                    $event['title'],
                    $event['description'],
                    $event['start_date'],
                    $event['end_date'],
                    $event['location'],
                    $event['capacity'],
                    $event['category'],
                    $event['status']
                ]);
            }

            jsonResponse(['success' => true, 'message' => 'Test verileri başarıyla eklendi']);
        }

        // ID parametresi varsa tek bir etkinliği getir
        if (isset($_GET['id'])) {
            $id = intval($_GET['id']);
            $stmt = $db_akademi->prepare("SELECT * FROM events WHERE id = ?");
            $stmt->execute([$id]);
            $event = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($event) {
                jsonResponse(['success' => true, 'data' => $event]);
            } else {
                jsonResponse(['success' => false, 'message' => 'Etkinlik bulunamadı.'], 404);
            }
        }

        // Filtreleme parametreleri
        $whereClause = '';
        $params = [];

        // Tarih filtresi
        if (isset($_GET['upcoming']) && $_GET['upcoming'] === 'true') {
            $whereClause = " WHERE start_date >= NOW()";
        } elseif (isset($_GET['past']) && $_GET['past'] === 'true') {
            $whereClause = " WHERE start_date < NOW()";
        }

        // Kategori filtresi
        if (isset($_GET['category']) && !empty($_GET['category'])) {
            $whereClause = $whereClause ? $whereClause . " AND category = ?" : " WHERE category = ?";
            $params[] = $_GET['category'];
        }

        // Öne çıkan filtresi
        if (isset($_GET['featured']) && $_GET['featured'] === 'true') {
            $whereClause = $whereClause ? $whereClause . " AND is_featured = 1" : " WHERE is_featured = 1";
        }

        // Sıralama
        $orderBy = " ORDER BY start_date ASC";

        // Tüm etkinlikleri getir
        $stmt = $db_akademi->prepare("SELECT * FROM events" . $whereClause . $orderBy);
        $stmt->execute($params);
        $events = $stmt->fetchAll(PDO::FETCH_ASSOC);

        jsonResponse(['success' => true, 'data' => $events]);

    } catch (PDOException $e) {
        error_log("Events API GET hatası: " . $e->getMessage());
        jsonResponse(['success' => false, 'message' => 'Etkinlikler listelenirken bir hata oluştu.'], 500);
    }
}

// POST isteği - Etkinlik ve Kategori İşlemleri
if ($method === 'POST') {
    // POST istekleri için auth gerekli
    if (!checkAuth()) {
        jsonResponse(['success' => false, 'message' => 'Bu işlemi gerçekleştirmek için giriş yapmalısınız.'], 401);
    }

    try {
        // Action'a göre işlem belirle
        switch ($action) {
            case 'add':
        // Etkinlik Ekleme
            $title = $_POST['title'] ?? '';
            $description = $_POST['description'] ?? '';
            $start_date = $_POST['start_date'] ?? '';
            $end_date = $_POST['end_date'] ?? '';
            $location = $_POST['location'] ?? '';
            $capacity = isset($_POST['capacity']) ? intval($_POST['capacity']) : 0;
            $category = $_POST['category'] ?? '';
            $instructor = $_POST['instructor'] ?? '';
            $is_featured = isset($_POST['is_featured']) ? (bool)$_POST['is_featured'] : false;
            $status = $_POST['status'] ?? 'active';

            // Thumbnail yükleme
            $thumbnail = '';
            if (isset($_FILES['thumbnail']) && $_FILES['thumbnail']['error'] === UPLOAD_ERR_OK) {
                $upload_dir = __DIR__ . '/../../uploads/events/';
                if (!file_exists($upload_dir)) {
                    mkdir($upload_dir, 0777, true);
                }

                $file_name = time() . '_' . $_FILES['thumbnail']['name'];
                $upload_path = $upload_dir . $file_name;

                if (move_uploaded_file($_FILES['thumbnail']['tmp_name'], $upload_path)) {
                    $thumbnail = '/uploads/events/' . $file_name;
                }
            }

            // Veritabanına ekle
            $stmt = $db_akademi->prepare("
                INSERT INTO events
                (title, description, start_date, end_date, location, capacity, registered, thumbnail, instructor, category, is_featured, created_by, created_at, updated_at, status)
                    VALUES (?, ?, ?, ?, ?, ?, 0, ?, ?, ?, ?, 1, NOW(), NOW(), ?)
            ");

            $stmt->execute([
                $title,
                $description,
                $start_date,
                $end_date,
                $location,
                $capacity,
                $thumbnail,
                $instructor,
                $category,
                $is_featured ? 1 : 0,
                $status
            ]);

            $event_id = $db_akademi->lastInsertId();
                jsonResponse(['success' => true, 'message' => 'Etkinlik başarıyla eklendi.', 'id' => $event_id]);
                break;

            case 'update':
        // Etkinlik Güncelleme
            $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
            $title = $_POST['title'] ?? '';
            $description = $_POST['description'] ?? '';
            $start_date = $_POST['start_date'] ?? '';
            $end_date = $_POST['end_date'] ?? '';
            $location = $_POST['location'] ?? '';
            $capacity = isset($_POST['capacity']) ? intval($_POST['capacity']) : 0;
            $category = $_POST['category'] ?? '';
            $instructor = $_POST['instructor'] ?? '';
            $is_featured = isset($_POST['is_featured']) ? (bool)$_POST['is_featured'] : false;
            $status = $_POST['status'] ?? 'active';

            // Mevcut etkinliği çek
            $stmt = $db_akademi->prepare("SELECT thumbnail FROM events WHERE id = ?");
            $stmt->execute([$id]);
            $event = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$event) {
                    jsonResponse(['success' => false, 'message' => 'Etkinlik bulunamadı.'], 404);
            }

            // Thumbnail yükleme
            $thumbnail = $event['thumbnail'];
            if (isset($_FILES['thumbnail']) && $_FILES['thumbnail']['error'] === UPLOAD_ERR_OK) {
                $upload_dir = __DIR__ . '/../../uploads/events/';
                if (!file_exists($upload_dir)) {
                    mkdir($upload_dir, 0777, true);
                }

                $file_name = time() . '_' . $_FILES['thumbnail']['name'];
                $upload_path = $upload_dir . $file_name;

                if (move_uploaded_file($_FILES['thumbnail']['tmp_name'], $upload_path)) {
                    // Eski thumbnail'i sil
                    if (!empty($event['thumbnail']) && file_exists(__DIR__ . '/../../' . $event['thumbnail'])) {
                        unlink(__DIR__ . '/../../' . $event['thumbnail']);
                    }

                    $thumbnail = '/uploads/events/' . $file_name;
                }
            }

            // Veritabanını güncelle
            $stmt = $db_akademi->prepare("
                UPDATE events
                SET title = ?, description = ?, start_date = ?, end_date = ?, location = ?,
                    capacity = ?, thumbnail = ?, instructor = ?, category = ?, is_featured = ?,
                    updated_at = NOW(), status = ?
                WHERE id = ?
            ");

            $stmt->execute([
                $title,
                $description,
                $start_date,
                $end_date,
                $location,
                $capacity,
                $thumbnail,
                $instructor,
                $category,
                $is_featured ? 1 : 0,
                $status,
                $id
            ]);

                jsonResponse(['success' => true, 'message' => 'Etkinlik başarıyla güncellendi.']);
                break;

            case 'delete':
        // Etkinlik Silme
            $data = json_decode(file_get_contents('php://input'), true);
            $id = $data['id'] ?? 0;

            // Mevcut etkinliği çek
            $stmt = $db_akademi->prepare("SELECT thumbnail FROM events WHERE id = ?");
            $stmt->execute([$id]);
            $event = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$event) {
                    jsonResponse(['success' => false, 'message' => 'Etkinlik bulunamadı.'], 404);
            }

            // Thumbnail'i sil
            if (!empty($event['thumbnail']) && file_exists(__DIR__ . '/../../' . $event['thumbnail'])) {
                unlink(__DIR__ . '/../../' . $event['thumbnail']);
            }

            // Veritabanından sil
            $stmt = $db_akademi->prepare("DELETE FROM events WHERE id = ?");
            $stmt->execute([$id]);

                jsonResponse(['success' => true, 'message' => 'Etkinlik başarıyla silindi.']);
                break;

            case 'update_category':
        // Kategori Güncelleme - Tüm etkinliklerde kategori ismini değiştir
            $data = json_decode(file_get_contents('php://input'), true);
            $old_category = $data['old_category'] ?? '';
            $new_category = $data['new_category'] ?? '';

                error_log("Events API - Kategori güncelleme: " . $old_category . " -> " . $new_category);

            if (empty($old_category) || empty($new_category)) {
                    jsonResponse(['success' => false, 'message' => 'Eski ve yeni kategori adları gerekli.'], 400);
                }

            // Tüm etkinliklerde eski kategori adını yeni kategori adıyla değiştir
            $stmt = $db_akademi->prepare("UPDATE events SET category = ?, updated_at = NOW() WHERE category = ?");
            $stmt->execute([$new_category, $old_category]);

            $affected_rows = $stmt->rowCount();
                error_log("Events API - Güncellenen etkinlik sayısı: " . $affected_rows);

                jsonResponse([
                'success' => true,
                'message' => "Kategori başarıyla güncellendi. {$affected_rows} etkinlik etkilendi.",
                'affected_rows' => $affected_rows
            ]);
                break;

            case 'delete_category':
        // Kategori Silme - Belirli kategorideki tüm etkinlikleri başka kategoriye taşı
            $data = json_decode(file_get_contents('php://input'), true);
            $category_to_delete = $data['category'] ?? '';
            $move_to_category = $data['move_to'] ?? 'Diğer';

            if (empty($category_to_delete)) {
                    jsonResponse(['success' => false, 'message' => 'Silinecek kategori adı gerekli.'], 400);
            }

            // Bu kategorideki tüm etkinlikleri başka kategoriye taşı
            $stmt = $db_akademi->prepare("UPDATE events SET category = ?, updated_at = NOW() WHERE category = ?");
            $stmt->execute([$move_to_category, $category_to_delete]);

            $affected_rows = $stmt->rowCount();

                jsonResponse([
                'success' => true,
                'message' => "Kategori silindi. {$affected_rows} etkinlik '{$move_to_category}' kategorisine taşındı.",
                'affected_rows' => $affected_rows
            ]);
                break;

            case 'add_category':
                // Yeni Kategori Ekleme - event_categories.php işlevselliği
                $data = json_decode(file_get_contents('php://input'), true);
                if (!$data) {
                    $data = $_POST;
                }

                if (!isset($data['name']) || !isset($data['value'])) {
                    jsonResponse(['success' => false, 'message' => 'Eksik alanlar: name ve value gerekli.'], 400);
                }

                // Kategori değerinin benzersiz olup olmadığını kontrol et
                $stmt = $db_akademi->prepare("SELECT COUNT(*) FROM events WHERE category = ?");
                $stmt->execute([$data['value']]);
                $exists = $stmt->fetchColumn() > 0;

                if ($exists) {
                    jsonResponse(['success' => false, 'message' => 'Bu kategori zaten mevcut.'], 400);
            }

                // Yeni kategori başarıyla eklendi mesajı
                jsonResponse(['success' => true, 'message' => 'Kategori başarıyla eklendi.']);
                break;

            default:
                jsonResponse(['success' => false, 'message' => 'Desteklenmeyen action parametresi: ' . $action], 400);
                break;
        }

    } catch (PDOException $e) {
        error_log("Events API POST hatası: " . $e->getMessage());
        jsonResponse(['success' => false, 'message' => 'Etkinlik işlemi sırasında bir hata oluştu.'], 500);
    } catch (Exception $e) {
        error_log("Events API genel hatası: " . $e->getMessage());
        jsonResponse(['success' => false, 'message' => 'Bir hata oluştu.'], 500);
    }
}

// Desteklenmeyen istek metodu
jsonResponse(['success' => false, 'message' => 'Desteklenmeyen HTTP metodu.'], 405);
