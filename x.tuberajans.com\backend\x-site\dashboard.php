<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// CORS ön kontrol istekleri için
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit;
}

error_log("Dashboard API: Config dosyasını dahil etme denemesi...");
require_once __DIR__ . '/../config/config.php';
error_log("Dashboard API: Config dosyası başarıyla dahil edildi");

$conn = $db;

// İstek parametrelerini al
$type = isset($_GET['type']) ? $_GET['type'] : 'full';
$range = isset($_GET['range']) ? $_GET['range'] : 'weekly';

// Hata yönetimi için try-catch b<PERSON><PERSON><PERSON> kullan
try {
    // Gerekli veri türüne göre işlem yap
    switch ($type) {
        case 'full':
            $response = getAllDashboardData($conn, $range);
            break;
        case 'overview':
            $response = getOverviewData($conn);
            break;
        case 'trends':
            $response = getTrendData($conn, $range);
            break;
        case 'tasks':
            $response = getTasksData($conn);
            break;
        default:
            throw new Exception("Geçersiz veri türü: $type");
    }

    // Başarılı yanıtı döndür
    echo json_encode($response);

} catch (Exception $e) {
    // Hata durumunda HTTP 500 kodu ve hata mesajını döndür
    http_response_code(500);
    echo json_encode([
        'error' => 'Veri alınamadı',
        'details' => $e->getMessage()
    ]);
}

/**
 * Tüm dashboard verilerini toplar ve döndürür
 *
 * @param PDO $conn Veritabanı bağlantısı
 * @param string $range Veri aralığı
 * @return array
 */
function getAllDashboardData($conn, $range) {
    error_log("Dashboard API: getAllDashboardData fonksiyonu çalışıyor");
    // Her bir kategori için veriyi al
    $overviewData = getOverviewData($conn);
    $trendData = getTrendData($conn, $range);
    $topPublishers = getTopPublishers($conn);
    $events = getEvents($conn);
    $matches = getMatches($conn);
    $notifications = getNotifications($conn);
    $categoryDistribution = getCategoryDistribution($conn);
    $taskStatus = getTaskStatusData($conn);
    $siteData = getSiteData($conn);
    $akademiData = getAkademiData($conn);
    $etsyData = getEtsyData($conn);
    $whatsappData = getWhatsappData($conn);
    $publisherDiscoveryData = getPublisherDiscoveryData($conn);

    // Tüm veriyi tek bir yapıda birleştir
    return [
        'overview' => $overviewData,
        'trend_data' => $trendData,
        'top_publishers' => $topPublishers,
        'events' => $events,
        'matches' => $matches,
        'notifications' => $notifications,
        'category_distribution' => $categoryDistribution,
        'task_status' => $taskStatus,
        'site_data' => $siteData,
        'akademi_data' => $akademiData,
        'etsy_data' => $etsyData,
        'whatsapp_data' => $whatsappData,
        'publisher_discovery_data' => $publisherDiscoveryData
    ];
}

/**
 * Dashboard genel bakış verilerini getirir
 *
 * @param PDO $conn Veritabanı bağlantısı
 * @return array
 */
function getOverviewData($conn) {
    error_log("Dashboard API: getOverviewData fonksiyonu çalışıyor");
    try {
        // Toplam yayıncı sayısını al
        error_log("Dashboard API: publisher_info sayısı sorgulanıyor");
        try {
            $stmt = $conn->prepare("SELECT COUNT(*) FROM publisher_info");
            $stmt->execute();
            $totalPublishers = $stmt->fetchColumn();
            error_log("Dashboard API: publisher_info sayısı: $totalPublishers");
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), '42S02') !== false) {
                $totalPublishers = 0;
                error_log("Dashboard API: publisher_info tablosu yok, 0 döndü");
            } else {
                throw $e;
            }
        }

        // Toplam görev sayısını al
        error_log("Dashboard API: weekly_tasks sayısı sorgulanıyor");
        try {
            $stmt = $conn->prepare("SELECT COUNT(*) FROM weekly_tasks");
            $stmt->execute();
            $totalTasks = $stmt->fetchColumn();
            error_log("Dashboard API: weekly_tasks sayısı: $totalTasks");
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), '42S02') !== false) {
                $totalTasks = 0;
                error_log("Dashboard API: weekly_tasks tablosu yok, 0 döndü");
            } else {
                throw $e;
            }
        }

        // Tamamlanan görevlerin sayısını al
        error_log("Dashboard API: weekly_tasks tamamlandı sayısı sorgulanıyor");
        try {
            $stmt = $conn->prepare("SELECT COUNT(*) FROM weekly_tasks WHERE durum = 'tamamlandı'");
            $stmt->execute();
            $completedTasks = $stmt->fetchColumn();
            error_log("Dashboard API: weekly_tasks tamamlandı sayısı: $completedTasks");
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), '42S02') !== false) {
                $completedTasks = 0;
                error_log("Dashboard API: weekly_tasks tablosu yok (tamamlandı), 0 döndü");
            } else {
                throw $e;
            }
        }

        // Bekleyen görevlerin sayısını hesapla
        $pendingTasks = $totalTasks - $completedTasks;
        error_log("Dashboard API: weekly_tasks bekleyen sayısı: $pendingTasks");

        // Toplam takipçi ve elmas sayısını weekly_archive üzerinden al
        error_log("Dashboard API: weekly_archive toplam takipçi ve elmas sorgulanıyor");
        try {
            $stmt = $conn->prepare("SELECT SUM(yeni_takipciler) AS total_followers, SUM(elmaslar) AS total_diamonds FROM weekly_archive");
            $stmt->execute();
            $totals = $stmt->fetch(PDO::FETCH_ASSOC);
            $totalFollowers = $totals['total_followers'] ?: 0;
            $totalDiamonds = $totals['total_diamonds'] ?: 0;
            error_log("Dashboard API: weekly_archive toplam takipçi: $totalFollowers, toplam elmas: $totalDiamonds");
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), '42S02') !== false) {
                $totalFollowers = 0;
                $totalDiamonds = 0;
                error_log("Dashboard API: weekly_archive tablosu yok, 0 döndü");
            } else {
                throw $e;
            }
        }

        // Görev tamamlama oranını hesapla
        $completionRate = $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100) : 0;
        error_log("Dashboard API: Görev tamamlama oranı: $completionRate");

        // Aktif etkinlik sayısını al
        error_log("Dashboard API: events aktif etkinlik sayısı sorgulanıyor");
        try {
            $stmt = $conn->prepare("SELECT COUNT(*) FROM events WHERE status IN ('Yaklaşan', 'Aktif')");
            $stmt->execute();
            $activeEvents = $stmt->fetchColumn();
            error_log("Dashboard API: events aktif etkinlik sayısı: $activeEvents");
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), '42S02') !== false) {
                $activeEvents = 0;
                error_log("Dashboard API: events tablosu yok, 0 döndü");
            } else {
                throw $e;
            }
        }

        // Yaklaşan maç sayısını al
        error_log("Dashboard API: matches beklemede maç sayısı sorgulanıyor");
        try {
            $stmt = $conn->prepare("SELECT COUNT(*) FROM matches WHERE status = 'Beklemede'");
            $stmt->execute();
            $upcomingMatches = $stmt->fetchColumn();
            error_log("Dashboard API: matches beklemede maç sayısı: $upcomingMatches");
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), '42S02') !== false) {
                $upcomingMatches = 0;
                error_log("Dashboard API: matches tablosu yok, 0 döndü");
            } else {
                throw $e;
            }
        }

        return [
            'total_publishers' => (int)$totalPublishers,
            'total_tasks' => (int)$totalTasks,
            'completed_tasks' => (int)$completedTasks,
            'pending_tasks' => (int)$pendingTasks,
            'total_followers' => (int)$totalFollowers,
            'total_diamonds' => (int)$totalDiamonds,
            'completion_rate' => (int)$completionRate,
            'active_events' => (int)$activeEvents,
            'upcoming_matches' => (int)$upcomingMatches
        ];

    } catch (PDOException $e) {
        error_log("Dashboard API: getOverviewData HATA: " . $e->getMessage());
        throw new Exception("Genel bakış verileri alınamadı: " . $e->getMessage());
    }
}

/**
 * Trend verilerini getirir
 *
 * @param PDO $conn Veritabanı bağlantısı
 * @param string $range Veri aralığı
 * @return array
 */
function getTrendData($conn, $range) {
    error_log("Dashboard API: getTrendData fonksiyonu çalışıyor");
    try {
        // Veri aralığına göre SQL sorgusu oluştur
        switch ($range) {
            case 'monthly':
                $groupBy = "DATE_FORMAT(date, '%Y-%m')";
                $dateFormat = '%Y-%m';
                $limit = 12; // Son 12 ay
                break;

            case 'yearly':
                $groupBy = "YEAR(date)";
                $dateFormat = '%Y';
                $limit = 5; // Son 5 yıl
                break;

            case 'weekly':
            default:
                $groupBy = "YEARWEEK(date, 1)";
                $dateFormat = '%Y-%u';
                $limit = 12; // Son 12 hafta
        }

        // Trend verilerini al (sadece aktif yayıncılar)
        $sql = "SELECT wa.hafta_baslangici AS week, SUM(wa.yeni_takipciler) AS followers, SUM(wa.elmaslar) AS diamonds
                FROM weekly_archive wa
                INNER JOIN publisher_info pi ON wa.kullanici_adi = pi.username
                GROUP BY wa.hafta_baslangici
                ORDER BY wa.hafta_baslangici DESC
                LIMIT $limit";

        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Sonuçları istenen formata dönüştür
        $trendData = [];
        foreach ($results as $row) {
            $trendData[] = [
                'week' => $row['week'],
                'followers' => (int)$row['followers'],
                'diamonds' => (int)$row['diamonds']
            ];
        }

        // Tarihe göre artan sırada sırala
        usort($trendData, function($a, $b) {
            return strtotime($a['week']) - strtotime($b['week']);
        });

        return $trendData;

    } catch (PDOException $e) {
        throw new Exception("Trend verileri alınamadı: " . $e->getMessage());
    }
}

/**
 * En popüler yayıncıların verilerini getirir
 *
 * @param PDO $conn Veritabanı bağlantısı
 * @return array
 */
function getTopPublishers($conn) {
    error_log("Dashboard API: getTopPublishers fonksiyonu çalışıyor");
    try {
        $sql = "SELECT wa.kullanici_adi AS username, SUM(wa.yeni_takipciler) AS followers, SUM(wa.elmaslar) AS diamonds
                FROM weekly_archive wa
                INNER JOIN publisher_info pi ON wa.kullanici_adi = pi.username
                GROUP BY wa.kullanici_adi
                ORDER BY diamonds DESC
                LIMIT 10";

        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $topPublishers = [];

        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $topPublishers[] = [
                'username' => $row['username'],
                'followers' => (int)$row['followers'],
                'diamonds' => (int)$row['diamonds']
            ];
        }

        return $topPublishers;

    } catch (PDOException $e) {
        throw new Exception("En iyi yayıncı verileri alınamadı: " . $e->getMessage());
    }
}

/**
 * Etkinlik verilerini getirir
 *
 * @param PDO $conn Veritabanı bağlantısı
 * @return array
 */
function getEvents($conn) {
    error_log("Dashboard API: getEvents fonksiyonu çalışıyor");
    try {
        $sql = "SELECT
                    id,
                    etkinlik_adi as title,
                    etkinlik_tipi as type,
                    DATE_FORMAT(baslangic_tarihi, '%Y-%m-%d') as startDate,
                    DATE_FORMAT(bitis_tarihi, '%Y-%m-%d') as endDate,
                    durum as status
                FROM etkinlikler
                ORDER BY baslangic_tarihi DESC
                LIMIT 10";

        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $events = [];

        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $events[] = [
                'id' => (int)$row['id'],
                'title' => $row['title'],
                'type' => $row['type'],
                'startDate' => $row['startDate'],
                'endDate' => $row['endDate'],
                'status' => $row['status']
            ];
        }

        return $events;

    } catch (PDOException $e) {
        throw new Exception("Etkinlik verileri alınamadı: " . $e->getMessage());
    }
}

/**
 * Maç verilerini getirir
 *
 * @param PDO $conn Veritabanı bağlantısı
 * @return array
 */
function getMatches($conn) {
    error_log("Dashboard API: getMatches fonksiyonu çalışıyor");
    try {
        $sql = "SELECT
                    e.id,
                    (SELECT username FROM publisher_info WHERE id = e.yayinci1_id) as yayinci1,
                    (SELECT username FROM publisher_info WHERE id = e.yayinci2_id) as yayinci2,
                    DATE_FORMAT(e.planlanan_zaman, '%Y-%m-%d %H:%i') as scheduledTime,
                    e.durum as status,
                    e.kazanan_id,
                    (SELECT username FROM publisher_info WHERE id = e.kazanan_id) as winner
                FROM pk_eslesmeleri e
                ORDER BY e.planlanan_zaman DESC
                LIMIT 10";

        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $matches = [];

        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $match = [
                'id' => (int)$row['id'],
                'yayinci1' => $row['yayinci1'],
                'yayinci2' => $row['yayinci2'],
                'scheduledTime' => $row['scheduledTime'],
                'status' => $row['status']
            ];
            // Eğer kazanan varsa ekle
            if (!empty($row['winner'])) {
                $match['winner'] = $row['winner'];
            }
            $matches[] = $match;
        }

        return $matches;

    } catch (PDOException $e) {
        throw new Exception("Maç verileri alınamadı: " . $e->getMessage());
    }
}

/**
 * Bildirim verilerini getirir
 *
 * @param PDO $conn Veritabanı bağlantısı
 * @return array
 */
function getNotifications($conn) {
    error_log("Dashboard API: getNotifications fonksiyonu çalışıyor");
    try {
        $sql = "SELECT
                    id,
                    type,
                    data,
                    is_read,
                    created_at
                FROM notifications
                ORDER BY created_at DESC
                LIMIT 10";

        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $notifications = [];

        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            // data kolonundan message bilgisini JSON olarak çek
            $data = json_decode($row['data'], true);
            $message = isset($data['message']) ? $data['message'] : null;
            $notifications[] = [
                'id' => (int)$row['id'],
                'message' => $message,
                'type' => $row['type'],
                'date' => $row['created_at'],
                'isRead' => (bool)$row['is_read']
            ];
        }

        return $notifications;

    } catch (PDOException $e) {
        throw new Exception("Bildirim verileri alınamadı: " . $e->getMessage());
    }
}

/**
 * Kategori dağılımı verilerini getirir
 *
 * @param PDO $conn Veritabanı bağlantısı
 * @return array
 */
function getCategoryDistribution($conn) {
    error_log("Dashboard API: getCategoryDistribution fonksiyonu çalışıyor");
    try {
        $sql = "SELECT
                    '' as category,
                    COUNT(*) as value
                FROM publisher_info
                GROUP BY category";

        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $categories = [];

        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $categories[] = [
                'name' => $row['category'],
                'value' => (int)$row['value']
            ];
        }

        return $categories;

    } catch (PDOException $e) {
        throw new Exception("Kategori dağılımı verileri alınamadı: " . $e->getMessage());
    }
}

/**
 * Görev durumu verilerini getirir
 *
 * @param PDO $conn Veritabanı bağlantısı
 * @return array
 */
function getTaskStatusData($conn) {
    error_log("Dashboard API: getTaskStatusData fonksiyonu çalışıyor");
    try {
        $sql = "SELECT
                    durum as name,
                    COUNT(*) as value
                FROM weekly_tasks
                GROUP BY durum";

        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $taskStatus = [];

        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            // İngilizce durum adlarını Türkçe'ye çevir
            $statusName = $row['name'];
            if ($statusName === 'tamamlandı') $statusName = 'Tamamlandı';
            else if ($statusName === 'beklemede') $statusName = 'Beklemede';
            else if ($statusName === 'devam_ediyor') $statusName = 'Devam Ediyor';
            else if ($statusName === 'iptal_edildi') $statusName = 'İptal Edildi';

            $taskStatus[] = [
                'name' => $statusName,
                'value' => (int)$row['value']
            ];
        }

        return $taskStatus;

    } catch (PDOException $e) {
        throw new Exception("Görev durumu verileri alınamadı: " . $e->getMessage());
    }
}

/**
 * Site verilerini getirir
 *
 * @param PDO $conn Veritabanı bağlantısı
 * @return array
 */
function getSiteData($conn) {
    error_log("Dashboard API: getSiteData fonksiyonu çalışıyor");
    try {
        // Blog yazılarını say
        $stmt = $conn->prepare("SELECT COUNT(*) FROM blog");
        $stmt->execute();
        $blogPosts = $stmt->fetchColumn() ?: 0;

        // Yeni başvuruları say
        $stmt = $conn->prepare("SELECT COUNT(*) FROM sitebasvurular WHERE isRead = 0");
        $stmt->execute();
        $newApplications = $stmt->fetchColumn() ?: 0;

        // İletişim taleplerini say
        $stmt = $conn->prepare("SELECT COUNT(*) FROM iletisimtalepleri WHERE isRead = 0");
        $stmt->execute();
        $contactRequests = $stmt->fetchColumn() ?: 0;

        // Geri arama taleplerini say
        $stmt = $conn->prepare("SELECT COUNT(*) FROM geriaramatalepleri WHERE isRead = 0");
        $stmt->execute();
        $callbackRequests = $stmt->fetchColumn() ?: 0;

        // Toplantı taleplerini say
        $stmt = $conn->prepare("SELECT COUNT(*) FROM onlinetoplantitalep WHERE isRead = 0");
        $stmt->execute();
        $meetingRequests = $stmt->fetchColumn() ?: 0;

        return [
            'blog_posts' => (int)$blogPosts,
            'new_applications' => (int)$newApplications,
            'contact_requests' => (int)$contactRequests,
            'callback_requests' => (int)$callbackRequests,
            'meeting_requests' => (int)$meetingRequests
        ];

    } catch (PDOException $e) {
        throw new Exception("Site verileri alınamadı: " . $e->getMessage());
    }
}

/**
 * Akademi verilerini getirir
 *
 * @param PDO $conn Veritabanı bağlantısı
 * @return array
 */
function getAkademiData($conn) {
    error_log("Dashboard API: getAkademiData fonksiyonu çalışıyor");
    try {
        global $servername, $db_username, $db_password;
        // Akademi veritabanına bağlan
        $akademiDB = new PDO(
            "mysql:host={$servername};dbname=akademi_db;charset=utf8mb4",
            $db_username,
            $db_password
        );
        $akademiDB->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Toplam öğrenci sayısını al
        $stmt = $akademiDB->prepare("SELECT COUNT(*) FROM students WHERE status = 'active'");
        $stmt->execute();
        $totalStudents = $stmt->fetchColumn() ?: 0;

        // Toplam kurs sayısını al
        $stmt = $akademiDB->prepare("SELECT COUNT(*) FROM courses");
        $stmt->execute();
        $totalCourses = $stmt->fetchColumn() ?: 0;

        // Aktif kurs sayısını al
        $stmt = $akademiDB->prepare("SELECT COUNT(*) FROM courses WHERE status = 'active'");
        $stmt->execute();
        $activeCourses = $stmt->fetchColumn() ?: 0;

        // Destek taleplerini say
        $stmt = $akademiDB->prepare("SELECT COUNT(*) FROM support_tickets WHERE status = 'open'");
        $stmt->execute();
        $supportTickets = $stmt->fetchColumn() ?: 0;

        // Toplam duyuru sayısını al
        $stmt = $akademiDB->prepare("SELECT COUNT(*) FROM announcements");
        $stmt->execute();
        $totalAnnouncements = $stmt->fetchColumn() ?: 0;

        // Yeni duyuruları say (son 7 gün)
        $stmt = $akademiDB->prepare("SELECT COUNT(*) FROM announcements WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
        $stmt->execute();
        $newAnnouncements = $stmt->fetchColumn() ?: 0;

        return [
            'total_students' => (int)$totalStudents,
            'total_courses' => (int)$totalCourses,
            'active_courses' => (int)$activeCourses,
            'support_tickets' => (int)$supportTickets,
            'total_announcements' => (int)$totalAnnouncements,
            'new_announcements' => (int)$newAnnouncements
        ];

    } catch (PDOException $e) {
        // Hata durumunda varsayılan değerleri döndür
        return [
            'total_students' => 0,
            'total_courses' => 0,
            'active_courses' => 0,
            'support_tickets' => 0,
            'total_announcements' => 0,
            'new_announcements' => 0
        ];
    }
}

/**
 * Etsy verilerini getirir
 *
 * @param PDO $conn Veritabanı bağlantısı
 * @return array
 */
function getEtsyData($conn) {
    error_log("Dashboard API: getEtsyData fonksiyonu çalışıyor");
    try {
        // Bekleyen tasarımları say
        $stmt = $conn->prepare("SELECT COUNT(*) FROM etsy_designs WHERE status = 'pending'");
        $stmt->execute();
        $pendingDesigns = $stmt->fetchColumn() ?: 0;

        // Onaylanan tasarımları say
        $stmt = $conn->prepare("SELECT COUNT(*) FROM etsy_designs WHERE status = 'approved'");
        $stmt->execute();
        $approvedDesigns = $stmt->fetchColumn() ?: 0;

        // Aktif ürünleri say
        $stmt = $conn->prepare("SELECT COUNT(*) FROM etsy_products WHERE status = 'active'");
        $stmt->execute();
        $activeProducts = $stmt->fetchColumn() ?: 0;

        // Toplam satış sayısını al
        $stmt = $conn->prepare("SELECT COUNT(*) FROM etsy_sales");
        $stmt->execute();
        $totalSales = $stmt->fetchColumn() ?: 0;

        // Toplam geliri hesapla
        $stmt = $conn->prepare("SELECT SUM(amount) FROM etsy_sales");
        $stmt->execute();
        $totalRevenue = $stmt->fetchColumn() ?: 0;

        return [
            'pending_designs' => (int)$pendingDesigns,
            'approved_designs' => (int)$approvedDesigns,
            'active_products' => (int)$activeProducts,
            'total_sales' => (int)$totalSales,
            'total_revenue' => (float)$totalRevenue
        ];

    } catch (PDOException $e) {
        // Hata durumunda varsayılan değerleri döndür
        return [
            'pending_designs' => 0,
            'approved_designs' => 0,
            'active_products' => 0,
            'total_sales' => 0,
            'total_revenue' => 0
        ];
    }
}

/**
 * WhatsApp verilerini getirir
 *
 * @param PDO $conn Veritabanı bağlantısı
 * @return array
 */
function getWhatsappData($conn) {
    error_log("Dashboard API: getWhatsappData fonksiyonu çalışıyor");
    try {
        // Okunmamış mesajları say
        $stmt = $conn->prepare("SELECT COUNT(*) FROM whatsapp_messages WHERE is_read = 0");
        $stmt->execute();
        $unreadMessages = $stmt->fetchColumn() ?: 0;

        // Aktif sohbetleri say (son 24 saat içinde mesaj alınmış)
        $stmt = $conn->prepare("SELECT COUNT(DISTINCT contact_id) FROM whatsapp_messages
                               WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
        $stmt->execute();
        $activeChats = $stmt->fetchColumn() ?: 0;

        // Son 24 saatteki toplam mesaj sayısı
        $stmt = $conn->prepare("SELECT COUNT(*) FROM whatsapp_messages
                               WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
        $stmt->execute();
        $totalMessages24h = $stmt->fetchColumn() ?: 0;

        return [
            'unread_messages' => (int)$unreadMessages,
            'active_chats' => (int)$activeChats,
            'total_messages_24h' => (int)$totalMessages24h
        ];

    } catch (PDOException $e) {
        // Hata durumunda varsayılan değerleri döndür
        return [
            'unread_messages' => 0,
            'active_chats' => 0,
            'total_messages_24h' => 0
        ];
    }
}

/**
 * Yayıncı keşfi verilerini getirir
 *
 * @param PDO $conn Veritabanı bağlantısı
 * @return array
 */
function getPublisherDiscoveryData($conn) {
    error_log("Dashboard API: getPublisherDiscoveryData fonksiyonu çalışıyor");
    try {
        // Toplam keşfedilen yayıncı sayısı
        $stmt = $conn->prepare("SELECT COUNT(*) FROM discovered_publishers");
        $stmt->execute();
        $totalPublishersDiscovered = $stmt->fetchColumn() ?: 0;

        // Uygun yayıncıların sayısı
        $stmt = $conn->prepare("SELECT COUNT(*) FROM discovered_publishers WHERE status = 'uygun'");
        $stmt->execute();
        $suitablePublishers = $stmt->fetchColumn() ?: 0;

        // Gönderilen mesaj sayısı
        $stmt = $conn->prepare("SELECT COUNT(*) FROM discovered_publishers WHERE status = 'dm_gonderildi'");
        $stmt->execute();
        $messagesSent = $stmt->fetchColumn() ?: 0;

        // Ortalama izleyici sayısı
        $stmt = $conn->prepare("SELECT AVG(viewers) FROM discovered_publishers");
        $stmt->execute();
        $averageViewers = $stmt->fetchColumn() ?: 0;

        // Bugün keşfedilen yayıncı sayısı
        $stmt = $conn->prepare("SELECT COUNT(*) FROM discovered_publishers
                               WHERE DATE(discovered_at) = CURRENT_DATE()");
        $stmt->execute();
        $todayDiscovered = $stmt->fetchColumn() ?: 0;

        // Programın durumunu al
        $stmt = $conn->prepare("SELECT value FROM system_settings WHERE name = 'publisher_discovery_status'");
        $stmt->execute();
        $programStatus = $stmt->fetchColumn() ?: 'stopped';

        // Son keşfedilen yayıncılar
        $stmt = $conn->prepare("SELECT
                                    username,
                                    viewers,
                                    status,
                                    platform,
                                    discovered_at as discoveredAt
                                FROM discovered_publishers
                                ORDER BY discovered_at DESC
                                LIMIT 5");
        $stmt->execute();
        $lastPublishers = [];

        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $lastPublishers[] = [
                'username' => $row['username'],
                'viewers' => (int)$row['viewers'],
                'status' => $row['status'],
                'platform' => $row['platform'],
                'discoveredAt' => $row['discoveredAt']
            ];
        }

        return [
            'total_publishers_discovered' => (int)$totalPublishersDiscovered,
            'suitable_publishers' => (int)$suitablePublishers,
            'messages_sent' => (int)$messagesSent,
            'average_viewers' => (int)$averageViewers,
            'today_discovered' => (int)$todayDiscovered,
            'program_status' => $programStatus,
            'last_publishers' => $lastPublishers
        ];

    } catch (PDOException $e) {
        // Hata durumunda varsayılan değerleri döndür
        return [
            'total_publishers_discovered' => 0,
            'suitable_publishers' => 0,
            'messages_sent' => 0,
            'average_viewers' => 0,
            'today_discovered' => 0,
            'program_status' => 'stopped',
            'last_publishers' => []
        ];
    }
}

/**
 * Görev verileri için ayrıntılı bilgi toplar
 *
 * @param PDO $conn Veritabanı bağlantısı
 * @return array
 */
function getTasksData($conn) {
    error_log("Dashboard API: getTasksData fonksiyonu çalışıyor");
    try {
        // Görev durumlarına göre sayıları al
        $stmt = $conn->prepare("SELECT
                                    durum,
                                    COUNT(*) as count
                                FROM weekly_tasks
                                GROUP BY durum");
        $stmt->execute();
        $taskStatusCounts = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $statusData = [];
        foreach ($taskStatusCounts as $row) {
            $statusData[$row['durum']] = (int)$row['count'];
        }

        // Son görevleri al
        $stmt = $conn->prepare("SELECT
                                    t.id,
                                    t.title,
                                    t.description,
                                    t.durum,
                                    t.due_date,
                                    p.username as publisher
                                FROM weekly_tasks t
                                JOIN publishers p ON t.publisher_id = p.id
                                ORDER BY t.created_at DESC
                                LIMIT 10");
        $stmt->execute();
        $recentTasks = $stmt->fetchAll(PDO::FETCH_ASSOC);

        return [
            'status_counts' => $statusData,
            'recent_tasks' => $recentTasks
        ];

    } catch (PDOException $e) {
        throw new Exception("Görev verileri alınamadı: " . $e->getMessage());
    }
}
?>