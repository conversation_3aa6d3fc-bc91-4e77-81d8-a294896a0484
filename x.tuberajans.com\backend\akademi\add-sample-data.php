<?php
// CORS başlıkları
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
header("Access-Control-Max-Age: 86400");
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(204);
    exit();
}

header('Content-Type: application/json; charset=utf-8');

require_once __DIR__ . '/../config/config.php';

// Akademi veritabanı bağlantısı
$pdo = $db_akademi;

try {
    // Önce mevcut verileri kontrol et
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $userCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

    if ($userCount == 0) {
        // Örnek kullanıcılar ekle
        $users = [
            ['username' => 'ahmet_yayinci', 'email' => '<EMAIL>'],
            ['username' => 'ayse_creator', 'email' => '<EMAIL>'],
            ['username' => 'mehmet_tiktoker', 'email' => '<EMAIL>'],
            ['username' => 'fatma_influencer', 'email' => '<EMAIL>'],
            ['username' => 'ali_content', 'email' => '<EMAIL>']
        ];

        foreach ($users as $user) {
            $stmt = $pdo->prepare("INSERT INTO users (username, email, password, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())");
            $stmt->execute([$user['username'], $user['email'], password_hash('123456', PASSWORD_DEFAULT)]);
        }
        echo "Kullanıcılar eklendi.\n";
    }

    // Duyurular kontrol et ve ekle
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM announcements WHERE status = 'active'");
    $announcementCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

    if ($announcementCount == 0) {
        $announcements = [
            [
                'title' => 'Yeni Eğitim Programı Başlıyor',
                'content' => 'TikTok içerik üretimi konusunda yeni eğitim programımız 15 Ocak\'ta başlıyor. Kayıtlar açık!',
                'category' => 'general'
            ],
            [
                'title' => 'Aylık Performans Raporu',
                'content' => 'Aralık ayı performans raporları yayınlandı. Profilinizden kontrol edebilirsiniz.',
                'category' => 'important'
            ],
            [
                'title' => 'Yeni Özellikler Eklendi',
                'content' => 'Platform\'a yeni analiz araçları ve raporlama özellikleri eklendi.',
                'category' => 'general'
            ]
        ];

        foreach ($announcements as $announcement) {
            $stmt = $pdo->prepare("INSERT INTO announcements (title, content, category, status, created_at, updated_at) VALUES (?, ?, ?, 'active', NOW(), NOW())");
            $stmt->execute([$announcement['title'], $announcement['content'], $announcement['category']]);
        }
        echo "Duyurular eklendi.\n";
    }

    // Kurslar kontrol et ve ekle
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM courses WHERE status = 'active'");
    $courseCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

    if ($courseCount == 0) {
        $courses = [
            [
                'title' => 'TikTok İçerik Üretimi Temelleri',
                'description' => 'TikTok\'ta viral olacak içerikler nasıl üretilir?',
                'category' => 'Başlangıç',
                'content' => 'Bu eğitimde TikTok algoritması, içerik stratejileri ve viral olma teknikleri öğreneceksiniz.'
            ],
            [
                'title' => 'Monetizasyon Stratejileri',
                'description' => 'İçeriklerinizden nasıl para kazanırsınız?',
                'category' => 'Monetizasyon',
                'content' => 'Sponsorluk, affiliate marketing ve diğer gelir modelleri hakkında detaylı bilgi.'
            ],
            [
                'title' => 'İleri Seviye Video Düzenleme',
                'description' => 'Profesyonel video düzenleme teknikleri',
                'category' => 'İleri Seviye',
                'content' => 'CapCut, Adobe Premiere ve diğer araçlarla profesyonel düzenleme.'
            ]
        ];

        foreach ($courses as $course) {
            $stmt = $pdo->prepare("INSERT INTO courses (title, description, content, category, status, created_at, updated_at) VALUES (?, ?, ?, ?, 'active', NOW(), NOW())");
            $stmt->execute([$course['title'], $course['description'], $course['content'], $course['category']]);
        }
        echo "Kurslar eklendi.\n";
    }

    // Etkinlikler kontrol et ve ekle
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM events WHERE status = 'active'");
    $eventCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

    if ($eventCount == 0) {
        $events = [
            [
                'title' => 'Canlı Yayın Eğitimi',
                'description' => 'Etkili canlı yayın teknikleri workshop\'u',
                'start_date' => date('Y-m-d H:i:s', strtotime('+3 days')),
                'end_date' => date('Y-m-d H:i:s', strtotime('+3 days +2 hours'))
            ],
            [
                'title' => 'İçerik Üretimi Maratonu',
                'description' => '24 saatlik içerik üretimi challenge\'ı',
                'start_date' => date('Y-m-d H:i:s', strtotime('+7 days')),
                'end_date' => date('Y-m-d H:i:s', strtotime('+8 days'))
            ]
        ];

        foreach ($events as $event) {
            $stmt = $pdo->prepare("INSERT INTO events (title, description, start_date, end_date, status, created_at, updated_at) VALUES (?, ?, ?, ?, 'active', NOW(), NOW())");
            $stmt->execute([$event['title'], $event['description'], $event['start_date'], $event['end_date']]);
        }
        echo "Etkinlikler eklendi.\n";
    }

    // Destek talepleri kontrol et ve ekle
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM support_tickets");
    $ticketCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

    if ($ticketCount == 0) {
        $tickets = [
            [
                'subject' => 'Video yükleme sorunu',
                'message' => 'Videolarımı yüklerken hata alıyorum',
                'status' => 'open',
                'priority' => 'high'
            ],
            [
                'subject' => 'Hesap doğrulama',
                'message' => 'Hesabımı nasıl doğrulayabilirim?',
                'status' => 'closed',
                'priority' => 'medium'
            ],
            [
                'subject' => 'Ödeme sorunu',
                'message' => 'Ödeme işleminde problem yaşıyorum',
                'status' => 'open',
                'priority' => 'high'
            ]
        ];

        foreach ($tickets as $ticket) {
            $stmt = $pdo->prepare("INSERT INTO support_tickets (subject, message, status, priority, created_at, updated_at) VALUES (?, ?, ?, ?, NOW(), NOW())");
            $stmt->execute([$ticket['subject'], $ticket['message'], $ticket['status'], $ticket['priority']]);
        }
        echo "Destek talepleri eklendi.\n";
    }

    echo "Tüm örnek veriler başarıyla eklendi!";

} catch (PDOException $e) {
    echo "Hata: " . $e->getMessage();
}
?>
