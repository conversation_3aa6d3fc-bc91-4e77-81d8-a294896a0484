#!/bin/bash

# TikTok VDS İşleyicisi Kurulum Script'i

echo "🔧 TikTok VDS İşleyicisi Kurulum Başlatılıyor..."

# Root kontrolü
if [ "$EUID" -ne 0 ]; then
    echo "❌ Bu script root yetki<PERSON><PERSON><PERSON>ırılmalıdır!"
    echo "💡 Kullanım: sudo bash install.sh"
    exit 1
fi

# Sistem güncellemesi
echo "📦 Sistem paketleri güncelleniyor..."
apt update && apt upgrade -y

# Python ve pip kurulumu
echo "🐍 Python ve pip kuruluyor..."
apt install -y python3 python3-pip python3-venv

# Chrome kurulumu
echo "🌐 Google Chrome kuruluyor..."
if ! command -v google-chrome &> /dev/null; then
    wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add -
    echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list
    apt update
    apt install -y google-chrome-stable
fi

# Gerekli sistem paketleri
echo "📚 Sistem bağımlılıkları kuruluyor..."
apt install -y wget unzip curl

# Çalışma klasörünü oluştur
WORK_DIR="/home/<USER>"
echo "📁 Çalışma klasörü oluşturuluyor: $WORK_DIR"
mkdir -p $WORK_DIR
mkdir -p $WORK_DIR/logs

# Dosyaları kopyala
echo "📋 Dosyalar kopyalanıyor..."
cp vds-tiktok-processor.py $WORK_DIR/
cp config.py $WORK_DIR/
cp requirements.txt $WORK_DIR/
cp start.sh $WORK_DIR/
chmod +x $WORK_DIR/start.sh

# Python sanal ortamı oluştur
echo "🏠 Python sanal ortamı oluşturuluyor..."
cd $WORK_DIR
python3 -m venv venv
source venv/bin/activate

# Python paketlerini kur
echo "📦 Python paketleri kuruluyor..."
pip install -r requirements.txt

# ChromeDriver'ı indir
echo "🚗 ChromeDriver indiriliyor..."
CHROME_VERSION=$(google-chrome --version | grep -oP '\d+\.\d+\.\d+')
CHROME_MAJOR_VERSION=${CHROME_VERSION%%.*}

# ChromeDriver'ın son versiyonunu al
CHROMEDRIVER_VERSION=$(curl -s "https://chromedriver.storage.googleapis.com/LATEST_RELEASE_$CHROME_MAJOR_VERSION")
wget -O chromedriver.zip "https://chromedriver.storage.googleapis.com/$CHROMEDRIVER_VERSION/chromedriver_linux64.zip"
unzip chromedriver.zip
chmod +x chromedriver
rm chromedriver.zip

# Systemd servisi oluştur
echo "⚙️ Systemd servisi oluşturuluyor..."
cat > /etc/systemd/system/tiktok-analyzer.service << EOF
[Unit]
Description=TikTok Analyzer VDS Processor
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=$WORK_DIR
Environment=PATH=$WORK_DIR/venv/bin
ExecStart=$WORK_DIR/venv/bin/python $WORK_DIR/vds-tiktok-processor.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

# Systemd'yi yeniden yükle
systemctl daemon-reload
systemctl enable tiktok-analyzer.service

echo ""
echo "✅ Kurulum tamamlandı!"
echo ""
echo "📝 Sonraki adımlar:"
echo "1. $WORK_DIR/config.py dosyasını düzenleyin"
echo "2. Veritabanı bağlantı bilgilerini girin"
echo "3. Servisi başlatın: systemctl start tiktok-analyzer"
echo "4. Durumu kontrol edin: systemctl status tiktok-analyzer"
echo ""
echo "📊 Log dosyası: $WORK_DIR/logs/tiktok_processor.log"
echo "🔧 Konfigürasyon: $WORK_DIR/config.py"
echo ""
