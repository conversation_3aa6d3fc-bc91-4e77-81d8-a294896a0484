import{r as e}from"./vendor-CnpYymF8.js";import{ae as s}from"./antd-gS---Efz.js";import{I as i}from"./App-CRh63wQr.js";function a(){return a=Object.assign?Object.assign.bind():function(t){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(t[o]=r[o])}return t},a.apply(this,arguments)}const c=(t,n)=>e.createElement(i,a({},t,{ref:n,icon:s})),l=e.forwardRef(c);export{l as R};
