<?php
// Publisher Discovery Control API
error_log("Publisher Discovery Control API başlatılıyor - " . date('Y-m-d H:i:s'));

require_once __DIR__ . '/../config/config.php';

// CORS ayarları
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// OPTIONS isteği varsa hızlıca cevap ver
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// JSON yanıt fonksiyonu
if (!function_exists('jsonResponse')) {
    function jsonResponse($data, $status = 200) {
        http_response_code($status);
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
}

// Auth kontrolü
if ($_SERVER['REQUEST_METHOD'] !== 'OPTIONS') {
    if (!checkAuth()) {
        error_log("Publisher Discovery Control API: Auth başarısız");
        jsonResponse(['success' => false, 'error' => 'Unauthorized'], 401);
    }
    error_log("Publisher Discovery Control API: Auth başarılı");
}

// Config dosyası yolunu belirle
$configFile = __DIR__ . '/../automation_config.json';

// Config dosyasını oku
function readConfig($configFile) {
    if (file_exists($configFile)) {
        $content = file_get_contents($configFile);
        $config = json_decode($content, true);
        return $config ?: ['cycleTime' => '1', 'messageMode' => 'both'];
    }
    return ['cycleTime' => '1', 'messageMode' => 'both'];
}

// Config dosyasını yaz
function writeConfig($configFile, $config) {
    $config['lastUpdated'] = date('Y-m-d H:i:s');
    return file_put_contents($configFile, json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
}

// Database auto-detection
$databases = ['tuberaja_yayinci_takip', 'tuberaja_yayinci_akademi', 'tiktok_live_data'];
$automationDatabase = null;

foreach ($databases as $database) {
    try {
        $testQuery = "SELECT 1 FROM $database.automation_commands LIMIT 1";
        $db->query($testQuery);
        $automationDatabase = $database;
        error_log("Publisher Discovery Control API: automation_commands tablosu bulundu: $database");
        break;
    } catch (PDOException $e) {
        continue;
    }
}

if (!$automationDatabase) {
    error_log("Publisher Discovery Control API: automation_commands tablosu bulunamadı");
    jsonResponse(['success' => false, 'error' => 'automation_commands tablosu bulunamadı'], 500);
}

try {
    $requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';
    
    if ($requestMethod === 'POST') {
        $rawInput = file_get_contents('php://input');
        $input = json_decode($rawInput, true);
        
        if (!$input) {
            error_log("Publisher Discovery Control API: JSON decode hatası: " . json_last_error_msg());
            error_log("Publisher Discovery Control API: Raw input: " . $rawInput);
            jsonResponse(['success' => false, 'error' => 'Geçersiz JSON'], 400);
        }
        $action = $input['action'] ?? '';
        
        error_log("Publisher Discovery Control API: Action: $action");
        
        switch ($action) {
            case 'start':
                $cycleTime = $input['cycleTime'] ?? '1';
                $messageMode = $input['messageMode'] ?? 'both';
                
                // Config dosyasını güncelle
                $config = readConfig($configFile);
                $config['cycleTime'] = $cycleTime;
                $config['messageMode'] = $messageMode;
                writeConfig($configFile, $config);
                
                error_log("Publisher Discovery Control API: Otomasyon başlatılıyor - Döngü: $cycleTime dakika, Mesaj Modu: $messageMode");
                
                // automation_commands tablosuna komut ekle
                $params = json_encode([
                    'duration' => $cycleTime,
                    'messageMode' => $messageMode,
                    'headless' => false
                ]);
                
                $stmt = $db->prepare("INSERT INTO $automationDatabase.automation_commands (command, params, status, created_at) VALUES (:command, :params, 'pending', NOW())");
                $stmt->execute([
                    ':command' => 'start',
                    ':params' => $params
                ]);
                
                error_log("Publisher Discovery Control API: Start komutu veritabanına eklendi");
                
                jsonResponse([
                    'success' => true,
                    'message' => 'Otomasyon başlatıldı',
                    'config' => $config
                ]);
                break;
                
            case 'stop':
                error_log("Publisher Discovery Control API: Otomasyon durdurulıyor");
                
                // automation_commands tablosuna stop komutu ekle
                $stmt = $db->prepare("INSERT INTO $automationDatabase.automation_commands (command, params, status, created_at) VALUES (:command, :params, 'pending', NOW())");
                $stmt->execute([
                    ':command' => 'stop',
                    ':params' => '{}'
                ]);
                
                error_log("Publisher Discovery Control API: Stop komutu veritabanına eklendi");
                
                jsonResponse([
                    'success' => true,
                    'message' => 'Otomasyon durduruldu'
                ]);
                break;
                
            case 'update_cycle_time':
                $cycleTime = $input['cycleTime'] ?? '1';
                
                // Config dosyasını güncelle
                $config = readConfig($configFile);
                $config['cycleTime'] = $cycleTime;
                writeConfig($configFile, $config);
                
                error_log("Publisher Discovery Control API: Döngü süresi güncellendi: $cycleTime dakika");
                
                jsonResponse([
                    'success' => true,
                    'message' => 'Döngü süresi güncellendi',
                    'config' => $config
                ]);
                break;
                
            default:
                jsonResponse(['success' => false, 'error' => 'Geçersiz işlem'], 400);
        }
    } else {
        jsonResponse(['success' => false, 'error' => 'Sadece POST istekleri kabul edilir'], 405);
    }

} catch (Exception $e) {
    error_log("Publisher Discovery Control API Hatası: " . $e->getMessage());
    jsonResponse([
        'success' => false,
        'error' => 'Sunucu hatası',
        'message' => $e->getMessage()
    ], 500);
}
?> 