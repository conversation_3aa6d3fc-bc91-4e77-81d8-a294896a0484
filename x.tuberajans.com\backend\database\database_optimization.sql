-- Database Performance Optimization Script
-- Bu script çalıştırılarak veritabanı performansı artırılabilir

-- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tablosu için indeksler
ALTER TABLE yayincilar_site 
ADD INDEX idx_featured_order (isFeatured, featured_order, order_number),
ADD INDEX idx_username (username),
ADD INDEX idx_order (order_number);

-- Users tablosu optimizasyonu
ALTER TABLE users 
ADD INDEX idx_email (email),
ADD INDEX idx_role (role),
ADD INDEX idx_created_at (created_at);

-- User tokens tablosu optimizasyonu  
ALTER TABLE user_tokens 
ADD INDEX idx_token (token),
ADD INDEX idx_expires (expires_at),
ADD INDEX idx_user_id (user_id);

-- TikTok analysis tabloları optimizasyonu
CREATE INDEX idx_tiktok_username ON tiktok_analysis (username);
CREATE INDEX idx_tiktok_created ON tiktok_analysis (created_at);
CREATE INDEX idx_tiktok_status ON tiktok_requests (status);

-- Connection pool ve timeout ayarları
SET GLOBAL max_connections = 200;
SET GLOBAL connect_timeout = 30;
SET GLOBAL wait_timeout = 300;
SET GLOBAL interactive_timeout = 300;
SET GLOBAL net_read_timeout = 60;
SET GLOBAL net_write_timeout = 60;

-- Query cache optimizasyonu (MySQL 5.7 ve öncesi için)
SET GLOBAL query_cache_size = 268435456; -- 256MB
SET GLOBAL query_cache_limit = 1048576;  -- 1MB

-- InnoDB optimizasyonları
SET GLOBAL innodb_buffer_pool_size = 536870912; -- 512MB (sistem RAM'inin %70'i olmalı)
SET GLOBAL innodb_log_file_size = 268435456;    -- 256MB
SET GLOBAL innodb_flush_log_at_trx_commit = 2;  -- Performans için (veri kaybı riski var)

-- Slow query log'u aktif et (yavaş sorguları tespit etmek için)
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2; -- 2 saniyeden uzun sorgular loglanır

-- Tablo analizi ve optimizasyonu
ANALYZE TABLE users, user_tokens, yayincilar_site, tiktok_analysis, tiktok_requests;
OPTIMIZE TABLE users, user_tokens, yayincilar_site, tiktok_analysis, tiktok_requests;

-- Cache tablosu oluştur
CREATE TABLE IF NOT EXISTS api_cache (
    cache_key VARCHAR(255) PRIMARY KEY,
    cache_data LONGTEXT,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_expires (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Request rate limiting tablosu
CREATE TABLE IF NOT EXISTS request_limits (
    ip_address VARCHAR(45) PRIMARY KEY,
    request_count INT DEFAULT 1,
    window_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_window (window_start)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Performance monitoring tablosu
CREATE TABLE IF NOT EXISTS performance_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    endpoint VARCHAR(255),
    execution_time DECIMAL(8,3), -- milisaniye
    memory_usage INT,
    query_count INT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_endpoint (endpoint),
    INDEX idx_timestamp (timestamp),
    INDEX idx_execution_time (execution_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4; 