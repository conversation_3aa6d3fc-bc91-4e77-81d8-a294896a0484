<?php
require_once __DIR__ . '/../config/config.php';

try {
    // Önce mevcut tabloyu kontrol et
    $stmt = $db->prepare("SHOW CREATE TABLE weekly_tasks");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "Mevcut tablo yapısı:\n";
    echo $result['Create Table'] . "\n\n";
    
    // Unique key var mı kontrol et
    $stmt = $db->prepare("SHOW INDEX FROM weekly_tasks WHERE Non_unique = 0");
    $stmt->execute();
    $uniqueIndexes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($uniqueIndexes) || count($uniqueIndexes) <= 1) { // Sadece PRIMARY KEY varsa
        echo "Unique constraint bulunamadı. Ekleniyor...\n";
        
        // Unique constraint ekle: kullanici_adi + hafta_baslangici + gorev_onerisi
        $alterQuery = "ALTER TABLE weekly_tasks 
                      ADD UNIQUE KEY unique_user_week_task (kullanici_adi, hafta_baslangici, gorev_onerisi)";
        
        $stmt = $db->prepare($alterQuery);
        $stmt->execute();
        
        echo "Unique constraint başarıyla eklendi!\n";
    } else {
        echo "Unique constraint zaten mevcut:\n";
        foreach ($uniqueIndexes as $index) {
            echo "- {$index['Key_name']}: {$index['Column_name']}\n";
        }
    }
    
    // Test için bir kayıt ekle
    echo "\nTest kaydı ekleniyor...\n";
    $testQuery = "INSERT INTO weekly_tasks (kullanici_adi, hafta_baslangici, hafta_bitisi, gorev_onerisi, gorev_zorlugu, tamamlandi, puan, durum)
                  VALUES ('test_user', '2024-05-12', '2024-05-19', 'Test Görevi', 'Kolay', 0, 10, 'beklemede')
                  ON DUPLICATE KEY UPDATE
                      gorev_zorlugu = VALUES(gorev_zorlugu),
                      puan = VALUES(puan),
                      durum = 'beklemede'";
    
    $stmt = $db->prepare($testQuery);
    $stmt->execute();
    echo "Test kaydı eklendi/güncellendi.\n";
    
    // Aynı kaydı tekrar ekle (güncelleme olmalı)
    echo "Aynı kayıt tekrar ekleniyor (güncelleme testi)...\n";
    $testQuery2 = "INSERT INTO weekly_tasks (kullanici_adi, hafta_baslangici, hafta_bitisi, gorev_onerisi, gorev_zorlugu, tamamlandi, puan, durum)
                   VALUES ('test_user', '2024-05-12', '2024-05-19', 'Test Görevi', 'Orta', 0, 20, 'beklemede')
                   ON DUPLICATE KEY UPDATE
                       gorev_zorlugu = VALUES(gorev_zorlugu),
                       puan = VALUES(puan),
                       durum = 'beklemede'";
    
    $stmt = $db->prepare($testQuery2);
    $stmt->execute();
    echo "Test kaydı güncellendi.\n";
    
    // Test kaydını kontrol et
    $stmt = $db->prepare("SELECT * FROM weekly_tasks WHERE kullanici_adi = 'test_user' AND hafta_baslangici = '2024-05-12'");
    $stmt->execute();
    $testRecord = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\nTest kaydı sonucu:\n";
    print_r($testRecord);
    
    // Test kaydını sil
    $stmt = $db->prepare("DELETE FROM weekly_tasks WHERE kullanici_adi = 'test_user'");
    $stmt->execute();
    echo "\nTest kaydı silindi.\n";
    
} catch (PDOException $e) {
    echo "Hata: " . $e->getMessage() . "\n";
}
?>
