<?php
require_once __DIR__ . '/../config/config.php';

try {
    // weekly_tasks tablosunun yapısını kontrol et
    $stmt = $db->prepare("DESCRIBE weekly_tasks");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "weekly_tasks tablosu yapısı:\n";
    foreach ($columns as $column) {
        echo "- {$column['Field']}: {$column['Type']} ({$column['Null']}, {$column['Key']}, {$column['Default']})\n";
    }
    
    // Unique key kontrolü
    $stmt = $db->prepare("SHOW INDEX FROM weekly_tasks");
    $stmt->execute();
    $indexes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\nIndexler:\n";
    foreach ($indexes as $index) {
        echo "- {$index['Key_name']}: {$index['Column_name']} (Unique: {$index['Non_unique']})\n";
    }
    
} catch (PDOException $e) {
    echo "Hata: " . $e->getMessage();
}
?>
