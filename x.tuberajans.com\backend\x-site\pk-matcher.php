<?php
// Hata ayıklama
ini_set('display_errors', 1); 
error_reporting(E_ALL);

// Debug: API'nin başladığını bildir
error_log("PK Matcher API başlatılıyor - " . date('Y-m-d H:i:s'));

// Merkezi config dosyasını dahil et
require_once __DIR__ . '/../config/config.php';

// Debug: API'nin buraya kadar çalıştığını bildir
error_log("PK Matcher API: Tüm include'lar tamamlandı - " . date('Y-m-d H:i:s'));

// CORS ayarları
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// OPTIONS isteği varsa hızlıca cevap ver
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// JSON yanıt fonksiyonu
if (!function_exists('jsonResponse')) {
    function jsonResponse($data, $status = 200) {
        http_response_code($status);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}

// GET isteği işle - aktif yayıncıları ve PK durumlarını getir
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {
        // İşlem tipini belirle
        $action = $_GET['action'] ?? '';
        
        // Aktif yayıncıları getir
        if ($action === 'active_publishers') {
            try {
                // Doğru veritabanı ve tablo adını belirt
                $stmt = $db->prepare("SELECT username FROM tuberaja_yayinci_takip.publisher_info WHERE username IS NOT NULL AND username != ''");
                $stmt->execute();
                $rows = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                // Sonuç yoksa, live_data tablosundan da ara
                if (empty($rows)) {
                    try {
                        $stmt = $db->prepare("SELECT DISTINCT username FROM tiktok_live_data.live_data WHERE username IS NOT NULL AND username != ''");
                        $stmt->execute();
                        $rows = $stmt->fetchAll(PDO::FETCH_COLUMN);
                    } catch (Exception $e) {
                        // Varsayılan olarak boş liste döndür
                        error_log("Live data tablosundan veri çekme hatası: " . $e->getMessage());
                        $rows = [];
                    }
                }
                
                jsonResponse([
                    'success' => true,
                    'data' => $rows
                ]);
            } catch (PDOException $e) {
                error_log("PK Matcher API hatası (active_publishers): " . $e->getMessage());
                jsonResponse([
                    'success' => false,
                    'message' => 'Veritabanı hatası: ' . $e->getMessage(),
                    'query' => "SELECT username FROM tuberaja_yayinci_takip.publisher_info WHERE username IS NOT NULL AND username != ''"
                ], 500);
            }
            exit;
        }
        
        // PK eşleşmelerini getir
        if ($action === 'get_matches') {
            // Basit mock veri döndür - gerçek veritabanı fonksiyonu yoksa
            $mockData = [
                [
                    'id' => 1,
                    'publisher1' => '@user1',
                    'publisher2' => '@user2',
                    'scheduledDate' => date('Y-m-d H:i:s', strtotime('+1 day')),
                    'status' => 'scheduled'
                ],
                [
                    'id' => 2,
                    'publisher1' => '@user3',
                    'publisher2' => '@user4',
                    'scheduledDate' => date('Y-m-d H:i:s', strtotime('+2 days')),
                    'status' => 'pending'
                ]
            ];
            
            jsonResponse([
                'success' => true,
                'data' => $mockData
            ]);
            exit;
        }
        
        // Varsayılan yanıt
        jsonResponse([
            'success' => false,
            'message' => 'Geçersiz action parametresi'
        ], 400);
        
    } catch (Exception $e) {
        error_log("PK Matcher API hatası: " . $e->getMessage());
        jsonResponse([
            'success' => false,
            'message' => 'Bir hata oluştu: ' . $e->getMessage()
        ], 500);
    }
}

// POST isteği işle - PK eşleşmesi oluştur
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // POST verisini al
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            jsonResponse([
                'success' => false,
                'message' => 'Geçersiz JSON verisi'
            ], 400);
            exit;
        }
        
        // İşlem tipini belirle
        $action = $_GET['action'] ?? '';
        
        // PK eşleşmesi oluştur
        if ($action === 'create_match') {
            // Gerekli alanları kontrol et
            if (empty($data['publisher1']) || empty($data['publisher2']) || empty($data['scheduledDate'])) {
                jsonResponse([
                    'success' => false,
                    'message' => 'Eksik parametreler: publisher1, publisher2 ve scheduledDate gerekli'
                ], 400);
                exit;
            }
            
            // Başarıyla oluşturuldu mesajı
            jsonResponse([
                'success' => true,
                'message' => 'PK eşleşmesi başarıyla oluşturuldu',
                'data' => [
                    'id' => time(), // Mock ID
                    'publisher1' => $data['publisher1'],
                    'publisher2' => $data['publisher2'],
                    'scheduledDate' => $data['scheduledDate'],
                    'status' => 'scheduled'
                ]
            ]);
            exit;
        }
        
        // Varsayılan yanıt
        jsonResponse([
            'success' => false,
            'message' => 'Geçersiz action parametresi'
        ], 400);
        
    } catch (Exception $e) {
        error_log("PK Matcher API hatası (POST): " . $e->getMessage());
        jsonResponse([
            'success' => false,
            'message' => 'Bir hata oluştu: ' . $e->getMessage()
        ], 500);
    }
}
?> 