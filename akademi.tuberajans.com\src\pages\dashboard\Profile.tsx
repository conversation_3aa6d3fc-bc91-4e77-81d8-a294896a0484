import React, { useState, useEffect, useContext } from 'react';
import { FaChartBar, FaUser } from 'react-icons/fa';
import axios from 'axios';
import { SidebarContext } from '../../contexts/SidebarContext';
import { useTikTok } from '../../contexts/TikTokContext';

const Profile: React.FC = () => {
  // TikTok Context'ten verileri al
  const { tiktokUser, refreshTikTokUser, clearTikTokUser } = useTikTok();

  const [loading, setLoading] = useState(true);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isDisconnecting, setIsDisconnecting] = useState(false);
  const [currentUser, setCurrentUser] = useState<{username: string, name: string} | null>(null);

  // Sidebar context'inden isMobile ve isSidebarOpen değerlerini al
  const { isMobile, isSidebarOpen } = useContext(SidebarContext);

  // Kullanıcı bilgilerini getir
  const fetchUserInfo = async () => {
    setLoading(true);
    try {
      const isDevMode = import.meta.env.VITE_DISABLE_AUTH === 'true' || import.meta.env.DEV;

      if (isDevMode) {
        // localStorage'dan kullanıcı bilgilerini al
        const savedUser = localStorage.getItem('user');
        if (savedUser) {
          const parsedUser = JSON.parse(savedUser);
          setCurrentUser({
            username: parsedUser.username || 'tuberajans',
            name: parsedUser.name || parsedUser.username || 'Tuber Akademi Kullanıcısı'
          });
        } else {
          // Fallback mock data
          setCurrentUser({
            username: 'tuberajans',
            name: 'Tuber Akademi Kullanıcısı'
          });
        }
        setLoading(false);
        return;
      }

      const response = await axios.get('/backend/api/user_info.php');
      if (response.data.success) {
        setCurrentUser({
          username: response.data.user.username || 'kullanici',
          name: response.data.user.name || response.data.user.username || 'Kullanıcı'
        });
      } else {
        console.error('Kullanıcı bilgileri alınamadı:', response.data.message);
        // Fallback user data
        setCurrentUser({
          username: 'kullanici',
          name: 'Akademi Kullanıcısı'
        });
      }
    } catch (err) {
      console.error('Kullanıcı bilgileri yüklenirken hata:', err);
      // Fallback user data
      setCurrentUser({
        username: 'kullanici',
        name: 'Akademi Kullanıcısı'
      });
    } finally {
      setLoading(false);
    }
  };

  // TikTok bağlantısı kurma
  const handleTikTokConnect = async () => {
    setIsConnecting(true);
    try {
      // TikTok OAuth 2.0 parametreleri
      const clientKey = 'awfw8k9nim1e8dmu';
      const redirectUri = encodeURIComponent('https://akademi.tuberajans.com/backend/api/tiktok-callback.php');
      const state = 'profile_' + Math.random().toString(36).substring(2, 15);
      const scope = 'user.info.basic,user.info.profile,user.info.stats,video.list';
      
      // Web tabanlı OAuth akışını kullan
      const url = `https://www.tiktok.com/auth/authorize/` + 
        `?client_key=${clientKey}` +
        `&scope=${scope}` +
        `&response_type=code` +
        `&redirect_uri=${redirectUri}` +
        `&state=${state}`;

      // State'i sakla
      localStorage.setItem('tiktok_oauth_state', state);
      localStorage.setItem('tiktok_redirect_after', 'profile');

      // Yeni sekmede aç
      window.open(url, '_blank');
    } catch (error) {
      console.error('TikTok bağlantısı kurulurken hata:', error);
    } finally {
      setIsConnecting(false);
    }
  };

  // TikTok bağlantısını kaldır
  const handleDisconnectTikTok = async () => {
    setIsDisconnecting(true);
    try {
      const response = await fetch('/backend/api/tiktok_disconnect.php', {
        method: 'POST',
        credentials: 'include'
      });

      const data = await response.json();

      if (data.status === 'success') {
        // TikTok bilgilerini temizle
        clearTikTokUser();
      } else {
        console.error('TikTok bağlantısı kaldırılamadı:', data.message);
      }
    } catch (error) {
      console.error('TikTok bağlantısı kaldırılırken hata:', error);
    } finally {
      setIsDisconnecting(false);
    }
  };

  // Sayı formatı için yardımcı fonksiyon
  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  useEffect(() => {
    fetchUserInfo();
  }, []);

  // TikTok login success kontrolü
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('tiktok_login') === 'success') {
      console.log('Profile: TikTok login success parametresi algılandı');
      window.history.replaceState({}, document.title, window.location.pathname);

      // Sadece success parametresi varsa refresh yap
      console.log('Profile: TikTok kullanıcı bilgileri çekiliyor...');
      refreshTikTokUser().then(() => {
        console.log('Profile: TikTok kullanıcı bilgileri başarıyla yenilendi');
      }).catch((error) => {
        console.error('Profile: TikTok kullanıcı bilgileri yenilenirken hata:', error);
      });
    }
  }, []); // Dependency array'den refreshTikTokUser'ı kaldır

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-[#0f0e15] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF3E71] mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Profil bilgileri yükleniyor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen w-full">
      <div className="container" style={{
        maxWidth: isMobile ? '100%' : (isSidebarOpen ? 'calc(100vw - 280px - 30px)' : 'calc(100vw - 78px - 30px)'),
        overflowX: 'hidden'
      }}>
        {/* Ana İçerik Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Sol Kolon - Profil Bilgileri */}
          <div className="lg:col-span-2 space-y-6">
            {/* Temel Profil Bilgileri */}
            <div className="bg-white dark:bg-[#16151c] rounded-xl shadow-sm p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                  <FaUser className="w-5 h-5 mr-2 text-[#FF3E71]" />
                  Profilim
                </h3>
                {tiktokUser && (
                  <button
                    onClick={handleDisconnectTikTok}
                    disabled={isDisconnecting}
                    className="text-red-600 hover:text-red-700 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {isDisconnecting ? 'Kesiliyor...' : 'TikTok Bağlantısını Kes'}
                  </button>
                )}
              </div>

              {tiktokUser ? (
                /* TikTok Profil Bilgileri */
                <div className="flex items-start justify-between mb-6">
                  <div className="flex items-start">
                    <div className="relative flex-shrink-0">
                      <img
                        src={tiktokUser.avatar_url}
                        alt={tiktokUser.display_name}
                        className="w-16 h-16 rounded-full object-cover"
                      />
                      {tiktokUser.is_verified && (
                        <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center border-2 border-white dark:border-gray-800">
                          <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      )}
                    </div>
                    <div className="ml-4">
                      <h4 className="text-lg font-bold text-gray-900 dark:text-white">{tiktokUser.display_name}</h4>
                      <p className="text-gray-600 dark:text-gray-400 text-sm">@{tiktokUser.username}</p>
                      {tiktokUser.bio_description && (
                        <p className="text-gray-700 dark:text-gray-300 text-sm mt-2 max-w-md leading-relaxed">{tiktokUser.bio_description}</p>
                      )}
                    </div>
                  </div>

                  <div className="flex gap-6 ml-6">
                    <div className="text-center">
                      <div className="text-lg font-bold text-gray-900 dark:text-white">{formatNumber(tiktokUser.follower_count)}</div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">Takipçi</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-gray-900 dark:text-white">{formatNumber(tiktokUser.following_count)}</div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">Takip</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-gray-900 dark:text-white">{formatNumber(tiktokUser.likes_count)}</div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">Beğeni</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-gray-900 dark:text-white">{formatNumber(tiktokUser.video_count)}</div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">Video</div>
                    </div>
                  </div>
                </div>
              ) : (
                /* Normal Kullanıcı Profili */
                <div className="text-center py-8">
                  <div className="w-20 h-20 bg-gradient-to-r from-[#FF3E71] to-[#FF6B85] rounded-full flex items-center justify-center mx-auto mb-4 text-white text-2xl font-bold">
                    {currentUser?.username?.charAt(0).toUpperCase() || 'K'}
                  </div>
                  <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{currentUser?.name || 'Akademi Kullanıcısı'}</h4>
                  <p className="text-gray-600 dark:text-gray-400 text-sm mb-6">@{currentUser?.username || 'kullanici'} • Akademi Üyesi</p>

                  <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-lg p-4 mb-4">
                    <h5 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">🚀 Profilini Güçlendir</h5>
                    <p className="text-xs text-gray-600 dark:text-gray-400 mb-3">TikTok hesabını bağlayarak video analitiklerini görüntüle</p>
                    <button
                      onClick={handleTikTokConnect}
                      disabled={isConnecting}
                      className="bg-[#FF3E71] hover:bg-[#FF2D5C] text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isConnecting ? 'Bağlanıyor...' : 'TikTok ile Bağlan'}
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Sağ Kolon - İstatistikler */}
          <div className="space-y-6">
            <div className="bg-white dark:bg-[#16151c] rounded-xl shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <FaChartBar className="w-5 h-5 mr-2 text-[#FF3E71]" />
                İstatistikler
              </h3>
              <div className="text-center py-8">
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  {tiktokUser ? 'TikTok analitikleri yükleniyor...' : 'TikTok hesabını bağlayarak detaylı analitikleri görüntüle'}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
