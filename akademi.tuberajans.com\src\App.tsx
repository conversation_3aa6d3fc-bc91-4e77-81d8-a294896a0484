// React import'u kaldırıldı
import { Routes, Route, useNavigate } from 'react-router-dom'
import HomePage from './pages/HomePage'
import Dashboard from './pages/dashboard'
import Announcements from './pages/dashboard/Announcements'
import Feed from './pages/dashboard/Feed'
import Courses from './pages/dashboard/Courses'
import Events from './pages/dashboard/Events'
import Requests from './pages/dashboard/Requests'
import Notifications from './pages/dashboard/Notifications'
import Profile from './pages/dashboard/Profile'
import Terms from './pages/Terms'
import Privacy from './pages/Privacy'
import TestPage from './pages/TestPage'
import TestLogin from './pages/TestLogin'
import CourseDetail from './pages/dashboard/CourseDetail'
import { SidebarProvider } from './contexts/SidebarContext'
import { AuthProvider } from './contexts/AuthContext'
import { TikTokProvider } from './contexts/TikTokContext'
import './App.css'

function App() {
  const navigate = useNavigate();

  // Development modunda AuthContext'i devre dışı bırak
  const isDevMode = import.meta.env.VITE_DISABLE_AUTH === 'true';

  const AppContent = () => (
    <TikTokProvider>
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/home" element={<HomePage />} />
        <Route path="/terms" element={<Terms />} />
        <Route path="/privacy" element={<Privacy />} />
        <Route path="/test" element={<TestPage />} />
        <Route path="/testlogin" element={<TestLogin />} />
        <Route path="/dashboard" element={
          <SidebarProvider>
            <Dashboard onLogout={() => navigate('/')} />
          </SidebarProvider>
        }>
        <Route index element={null} /> {/* Ana dashboard sayfası */}
        <Route path="announcements" element={<Announcements />} />
        <Route path="announcements/:id" element={<Announcements />} />
        <Route path="feed" element={<Feed />} />
        <Route path="courses" element={<Courses />} />
        <Route path="courses/:id" element={<CourseDetail />} />
        <Route path="events" element={<Events />} />
        <Route path="requests" element={<Requests />} />
        <Route path="notifications" element={<Notifications />} />
        <Route path="profile" element={<Profile />} />
      </Route>
      </Routes>
    </TikTokProvider>
  );

  return (
    <div className="App">
      {isDevMode ? (
        // Development modunda AuthProvider olmadan çalıştır
        <AppContent />
      ) : (
        // Production modunda AuthProvider ile çalıştır
        <AuthProvider>
          <AppContent />
        </AuthProvider>
      )}
    </div>
  )
}

export default App