<?php
require_once __DIR__ . '/config/config.php';

try {
    // Avatar sütununu ekle
    $sql = "ALTER TABLE users ADD COLUMN avatar VARCHAR(255) NULL AFTER email";
    $db_takip->exec($sql);
    
    echo "✅ Avatar sütunu başarıyla eklendi.\n";
    
    // Avatar klasörü oluştur
    $avatarDir = __DIR__ . '/uploads/avatars';
    if (!file_exists($avatarDir)) {
        mkdir($avatarDir, 0755, true);
        echo "✅ Avatar klasörü oluşturuldu.\n";
    }
    
} catch (PDOException $e) {
    if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
        echo "⚠️  Avatar sütunu zaten mevcut.\n";
    } else {
        echo "❌ Hata: " . $e->getMessage() . "\n";
    }
}
?> 