<?php
/**
 * Akademi API Index
 * 
 * Bu dosya, akademi.tuberajans.com sitesi için API endpoint'lerini listeler.
 */

// CORS başlıkları
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept");
header("Content-Type: application/json; charset=utf-8");

// API bilgilerini döndür
$api_info = [
    'name' => 'Tuber Akademi API',
    'version' => '1.0.0',
    'status' => 'active',
    'endpoints' => [
        [
            'path' => '/api/test.php',
            'method' => 'GET',
            'description' => 'Veritabanı bağlantısını test eder'
        ],
        [
            'path' => '/api/debug.php',
            'method' => 'GET',
            'description' => 'Sunucu ve PHP yapılandırması hakkında bilgi verir'
        ],
        [
            'path' => '/api/login.php',
            'method' => 'POST',
            'description' => 'Kullanıcı girişi yapar'
        ],
        [
            'path' => '/api/create_test_user.php',
            'method' => 'GET',
            'description' => 'Test kullanıcısı oluşturur (sadece geliştirme ortamında)'
        ]
    ],
    'time' => date('Y-m-d H:i:s')
];

// JSON yanıtı döndür
echo json_encode($api_info, JSON_PRETTY_PRINT);
