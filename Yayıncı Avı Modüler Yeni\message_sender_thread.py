import os
import subprocess
import random
import time
import logging
from typing import List, Dict, Optional
from dataclasses import dataclass
from datetime import datetime
from PyQt5.QtCore import QThread, pyqtSignal
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import (
    TimeoutException,
    WebDriverException,
    ElementClickInterceptedException
)
from constants import config_manager
from utils import random_sleep

logger = logging.getLogger('MessageSender')

@dataclass
class User:
    id: int
    username: str
    status: str
    message_log: Optional[str] = None
    timestamp: Optional[datetime] = None
    viewer_count: Optional[int] = None
    link: Optional[str] = None
    sorgu_tarihi: Optional[datetime] = None

class MessageSenderThread(QThread):
    """
    Mesaj gönderme işlemlerini arka planda gerçekleştiren QThread.
    Stop sinyali geldiğinde _is_running bayrağı False yapılır.
    """
    logSignal = pyqtSignal(str)
    finishedSignal = pyqtSignal(str)
    progressSignal = pyqtSignal(int)  # Yüzde ilerleme sinyali

    def __init__(self, db_manager, users: Optional[List[Dict]] = None, parent=None) -> None:
        super().__init__(parent)
        self.db_manager = db_manager
        # Chrome yolları - main.py ile TAMAMEN aynı yolları kullan
        self.driver_path: str = r"C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe"
        self.chrome_profile_path: str = r"C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data"
        self.chrome_binary_path: str = r"C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe"
        self._is_running: bool = False
        self.users: List[User] = [User(**user) for user in (users or [])]
        self.logger = logger
        self.retry_count: int = 3
        self.retry_delay: int = 5
        self.cycle_start_time: Optional[datetime] = None  # Döngü başlangıç zamanı
        
    def run(self) -> None:
        self._is_running = True
        self.log("📩 MessageSender başlatıldı.")
        driver: Optional[webdriver.Chrome] = None

        try:
            driver = self.setup_driver()
            if not self._is_running:
                return

            self.navigate_to_messages(driver)
            if not self._is_running:
                return

            if not self.users:
                self.log("⚠ Gönderilecek kullanıcı yok.")
                return

            total_users = len(self.users)
            for i, user in enumerate(self.users, 1):
                if not self._is_running:
                    self.log("⏹ İşlem kullanıcı isteğiyle durduruldu.")
                    break
                self.process_single_user(driver, user)
                self.progressSignal.emit(int((i / total_users) * 100))

            self.log("📩 Tüm mesajlar başarıyla gönderildi, yeni döngü başlatılıyor...")
            

        except Exception as e:
            self.log(f"⚠ Kritik hata: {e}")
            self.logger.error("Critical error", exc_info=True)
        finally:
            self.cleanup(driver)
            self.finishedSignal.emit("MessageSender tamamlandı.")
            
    def setup_driver(self) -> Optional[webdriver.Chrome]:
        self.close_chrome_instances()
        if not self._is_running:
            return None

        # Chrome binary yolunu kontrol et
        if not self.check_chrome_path():
            return None

        options = Options()
        options.add_argument(f"user-data-dir={self.chrome_profile_path}")
        options.add_argument("profile-directory=Profile 1")
        options.add_argument("--start-maximized")
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_argument("--disable-notifications")
        options.add_argument("--disable-popup-blocking")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option("useAutomationExtension", False)
        options.binary_location = self.chrome_binary_path  # Chrome binary yolunu belirt

        self.log(f"✅ Chrome binary yolu ayarlandı: {self.chrome_binary_path}")
        self.log(f"✅ Chrome profil yolu: {self.chrome_profile_path}, Profil: Profile 1")
        self.log(f"✅ ChromeDriver başlatılıyor: {self.driver_path}")
        service = Service(self.driver_path)
        return webdriver.Chrome(service=service, options=options)

    def close_chrome_instances(self) -> None:
        try:
            os.system("taskkill /im chrome.exe /f")
            self.log("✅ Önceki Chrome işlemleri kapatıldı.")
        except Exception as e:
            self.log(f"⚠ Chrome kapatma hatası: {e}")

    def navigate_to_messages(self, driver: webdriver.Chrome) -> None:
        try:
            time.sleep(5)
            if not self._is_running:
                return
            driver.get("https://live-backstage.tiktok.com/portal/anchor/instant-messages?type=1")
            time.sleep(10)
        except Exception as e:
            self.log(f"Mesaj sayfasına gidilemedi: {e}")
            raise

    def process_single_user(self, driver: webdriver.Chrome, user: User) -> None:
        try:
            # Daha detaylı kontroller
            user_info = self.db_manager.execute_query(
                """
                SELECT 
                    message_log, 
                    timestamp
                FROM live_data 
                WHERE username=%s
                """,
                (user.username,)
            )
            
            # Hiç sonuç dönmediyse veya boş bir liste döndüyse
            if not user_info or len(user_info) == 0:
                self.log(f"[{user.username}] için bilgi bulunamadı, atlanıyor.")
                return
            
            # Kontrol: Daha önce mesaj gönderilmiş mi?
            msg_log = user_info[0].get("message_log") or ""            
            if "Mesaj Gönderildi" in msg_log:
                self.log(f"[{user.username}] Daha önce mesaj gönderilmiş, atlanıyor.")
                return
            
            # Mesaj gönderimi için zaman kontrolü
            timestamp = user_info[0].get("timestamp")
            if not self.is_recent_user(timestamp, self.cycle_start_time):
                self.log(f"[{user.username}] Bu döngüye ait değil, atlanıyor.")
                return
            
            self.log(f"🔍 İşleniyor: {user.username}")

            # Kullanıcı araması
            if not self.search_user(driver, user.username):
                self.update_message_log(user.id, "Kullanıcı adı bulunamadı")
                return
                    
            if not self._is_running:
                return

            # Uygunluk ve uyarı kontrolleri
            if self.check_ineligible_tag(driver, user.id, user.username):
                return
            
            if self.check_post_send_warnings(driver):
                return
            
            # Mesaj gönderimi
            self.send_message(driver, user.id, user.username)
            
        except Exception as e:
            self.log(f"[{user.username}] hata: {e}")
     
    def is_recent_user(self, timestamp: datetime, reference_time: Optional[datetime] = None) -> bool:
        """
        Kullanıcının belirli bir zaman aralığında olup olmadığını kontrol eder
        
        Args:
            timestamp: Kontrol edilecek zaman damgası
            reference_time: Referans olarak kullanılacak başlangıç zamanı (döngü başlangıç zamanı)
        """
        if not timestamp:
            return False
        
        # Eğer reference_time verilmişse onu kullan, yoksa şu anki zamandan 1 saat öncesini kullan
        if reference_time:
            return timestamp >= reference_time
        else:
            # Geriye dönük uyumluluk için eski davranışı koruyalım
            from datetime import timedelta
            current_time = datetime.now()
            time_threshold = current_time - timedelta(hours=1)
            return timestamp >= time_threshold
    
    def check_dm_history(self, driver: webdriver.Chrome) -> bool:
        try:
            container = WebDriverWait(driver, 5).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "div.im-message-item-layout-message-content-content"))
            )
            my_messages = container.find_elements(By.CSS_SELECTOR, "div.im-message-type-text-right")
            return bool(my_messages)
        except TimeoutException:
            return False
        except Exception as e:
            self.log(f"DM geçmişi kontrolü hata: {e}")
            return False

    def search_user(self, driver: webdriver.Chrome, username: str) -> bool:
        if not self._is_running:
            return False
        try:
            input_box = WebDriverWait(driver, 10).until(
                EC.visibility_of_element_located((By.CSS_SELECTOR, "input[placeholder='İçerik üreticisi kullanıcı adı']"))
            )
            input_box.click()
            time.sleep(1)
            actions = ActionChains(driver)
            actions.move_to_element(input_box).click().perform()
            time.sleep(0.5)
            actions.key_down(Keys.CONTROL).send_keys('a').key_up(Keys.CONTROL).send_keys(Keys.DELETE).perform()
            time.sleep(0.5)
            actions.send_keys(username).pause(1).send_keys(Keys.ENTER).perform()
            result_element = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CLASS_NAME, "nameBox--BEINF"))
            )
            driver.execute_script("arguments[0].scrollIntoView(true);", result_element)
            actions.move_to_element(result_element).pause(1).click().perform()
            return True
        except TimeoutException:
            self.log(f"[{username}] Arama popup'taki sonuç bulunamadı.")
            return False
        except Exception as e:
            self.log(f"[{username}] arama sonucu tıklanamadı: {e}")
            return False

    def check_ineligible_tag(self, driver: webdriver.Chrome, user_id: int, username: str) -> bool:
        if not self._is_running:
            return True
        try:
            tag_elem = WebDriverWait(driver, 10).until(
                EC.visibility_of_element_located((By.CSS_SELECTOR, "div[aria-label='Tag: Uygun değil']"))
            )
            if tag_elem:
                self.log(f"[{username}] Artık uygun değil.")
                self.update_status(user_id, "Uygun değil")
                return True
        except TimeoutException:
            pass
        return False

    def check_post_send_warnings(self, driver: webdriver.Chrome) -> bool:
        """
        Mesaj gönderildikten sonra, 2 saniyelik bekleme sonrasında
        ilgili uyarı mesajlarının DOM'da yer alıp almadığını kontrol eder.
        Eğer uyarı varsa, True döner.
        """
        warning_messages = [
            "Bu kullanıcının gizlilik ayarları nedeniyle mesaj gönderilemez",
            "Bu kullanıcı sizi takip etmediği için mesaj gönderilemez",
            "Alıcının ayarları nedeniyle mesaj gönderilemedi",
            "Sadece arkadaşlar birbirine mesaj gönderebilir",
            "İletişime geçmeye çalıştığınız hesap askıya alındı"
        ]
        # 2 saniye bekleyerek uyarıların DOM'a yansımasını sağla
        for warning_text in warning_messages:
            try:
                warning = WebDriverWait(driver, 5).until(
                    EC.visibility_of_element_located(
                        (By.XPATH, f"//span[contains(text(), '{warning_text}')]")
                    )
                )
                if warning:
                    self.log(f"Post-send: '{warning_text}' uyarısı bulundu.")
                    return True
            except TimeoutException:
                continue
        return False
        
    def send_message(self, driver: webdriver.Chrome, user_id: int, username: str) -> None:
        if not self._is_running:
            return
        try:
            message_box = WebDriverWait(driver, 7).until(
                EC.visibility_of_element_located((By.CSS_SELECTOR,
                    "textarea[placeholder='Bu mesaj, içerik üreticisi tarafından görüntülenecek. Lütfen Topluluk Kurallarına uyun.']"
                ))
            )
            actions = ActionChains(driver)
            actions.move_to_element(message_box).click().perform()
            
            # Rastgele bekleme süreleri kullanarak yazı yazma
            actions.send_keys("Her pazartesi").pause(random.uniform(1, 2)).perform()
            actions.send_keys(Keys.ARROW_DOWN).pause(random.uniform(0.5, 1)).perform()
            actions.send_keys(Keys.ENTER).pause(random.uniform(1, 2)).perform()
            
            send_button = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "button[data-id='backstage_IM_send_btn']"))
            )
            send_button.click()
            
            # Mesaj gönderildikten sonra 2 saniye bekle
            time.sleep(3)
            
            # Gönderim sonrası uyarı kontrolü
            if self.check_post_send_warnings(driver):
                self.update_message_log(user_id, "Mesaj gönderilemedi")
                self.log(f"[{username}] mesaj gönderildi fakat post-send uyarısı bulundu, mesaj gönderilemedi olarak güncellendi.")
                return

            # Uyarı yoksa, mesajın gönderildiğini kabul et ve veritabanını güncelle
            self.update_message_log(user_id, "Mesaj Gönderildi")
            self.log(f"[{username}] adlı kullanıcıya mesaj gönderildi.")
        except (TimeoutException, WebDriverException, ElementClickInterceptedException) as e:
            self.log(f"[{username}] Mesaj gönderme hatası: {e}")
            self.update_message_log(user_id, "Mesaj gönderilemedi")
          
    def update_message_log(self, user_id: int, message: str) -> None:
        try:
            self.db_manager.execute_query(
                "UPDATE live_data SET message_log=%s WHERE id=%s",
                (message, user_id)
            )
            self.log(f"Veritabanı güncellemesi: ID {user_id} için '{message}' olarak güncellendi.")
        except Exception as e:
            self.log(f"Veritabanı güncelleme hatası: {e}")
            self.logger.error("Database update error", exc_info=True)
        
    def cleanup(self, driver: Optional[webdriver.Chrome]) -> None:
        if driver:
            try:
                driver.quit()
                self.log("Chrome driver kapatıldı.")
            except Exception as e:
                self.log(f"Driver kapatma hatası: {e}")
                self.logger.error("Driver cleanup error", exc_info=True)
        self._is_running = False

    def stop(self) -> None:
        self._is_running = False
        self.log("⏹ MessageSender durdurma sinyali alındı.")

    def log(self, msg: str) -> None:
        if msg is None:
            self.logger.error("🚨 HATA: log() fonksiyonuna None değeri gönderildi!")
            return
        self.logger.info(msg)
        self.logSignal.emit(msg)

    def check_chrome_path(self) -> bool:
        """Chrome binary yolunun geçerli olup olmadığını kontrol eder"""
        try:
            if not os.path.exists(self.chrome_binary_path):
                self.log(f"❌ Chrome yolu bulunamadı: {self.chrome_binary_path}")
                self.log("Chrome'un doğru yolda kurulu olduğundan emin olun")
                return False
                
            if not os.path.exists(self.chrome_profile_path):
                self.log(f"❌ Chrome profil yolu bulunamadı: {self.chrome_profile_path}")
                self.log("Chrome profil klasörünün doğru yolda olduğundan emin olun")
                return False
                
            self.log("✅ Chrome yolu doğrulandı")
            return True
        except Exception as e:
            self.log(f"❌ Chrome yolu kontrol hatası: {e}")
            return False
