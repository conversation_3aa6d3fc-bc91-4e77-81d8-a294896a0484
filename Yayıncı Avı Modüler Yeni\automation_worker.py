import time
import json
import pymysql
import logging
from main import MainApp
from dotenv import load_dotenv
import os
import traceback
import sys

# .env dosyasını yükle
load_dotenv()

DB_CONFIG = {
    "host": os.getenv("DB_HOST", os.getenv("MYSQL_HOST", "localhost")),
    "user": os.getenv("DB_USER", os.getenv("MYSQL_USER", "root")),
    "password": os.getenv("DB_PASSWORD", os.getenv("MYSQL_PASSWORD", "")),
    "database": os.getenv("DB_NAME", os.getenv("MYSQL_DATABASE", "tiktok_live_data")),
    "charset": "utf8mb4",
    "cursorclass": pymysql.cursors.DictCursor
}

# Loglama ayarları
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.FileHandler("automation_worker.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def get_db_connection():
    try:
        return pymysql.connect(**DB_CONFIG)
    except Exception as e:
        logger.error(f"Veritabanı bağlantı hatası: {e}")
        raise

def update_status(status, last_error=None):
    try:
        with get_db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    "UPDATE automation_status SET last_heartbeat=NOW(), status=%s, last_error=%s WHERE id=1",
                    (status, last_error)
                )
            conn.commit()
    except Exception as e:
        logger.error(f"Durum güncelleme hatası: {e}")

def process_command(cmd, main_app):
    try:
        command = cmd['command']
        params = cmd.get('params')
        params_dict = json.loads(params) if params else {}

        if command == 'start':
            duration = params_dict.get('duration', 5)  # Varsayılan 5 dakika
            headless = params_dict.get('headless', False)  # Headless mod desteği
            message_mode = params_dict.get('messageMode', 'both')
            
            if not main_app or not main_app.cycle_running:
                logger.info(f"Otomasyon başlatılıyor. Süre: {duration} dk, Headless: {headless}, MessageMode: {message_mode}")
                main_app = MainApp()
                main_app.set_duration(duration)
                main_app.set_headless(headless)
                main_app.set_message_mode(message_mode)
                main_app.start_automation()
                update_status('running')
            else:
                logger.info("Otomasyon zaten çalışıyor.")
                
        elif command == 'stop':
            if main_app and main_app.cycle_running:
                logger.info("Otomasyon durduruluyor.")
                main_app.stop_automation()
                update_status('stopped')
            else:
                logger.info("Otomasyon zaten durdurulmuş.")
        
        # YENİ: check_status komutu desteği
        elif command == 'check_status':
            usernames = []
            if params_dict.get('usernames'):
                usernames = [u['username'] if isinstance(u, dict) and 'username' in u else str(u) for u in params_dict['usernames']]
            if usernames:
                logger.info(f"{len(usernames)} kullanıcı için durum sorgulama başlatılıyor: {usernames}")
                try:
                    from status_checker import StatusCheckerThread
                    from database_manager import DatabaseManager

                    db_manager = DatabaseManager()
                    checker = StatusCheckerThread(db_manager, publishers=[{"username": u} for u in usernames])
                    checker.run()  # QThread olmadan doğrudan çalıştırıyoruz
                    logger.info("Durum sorgulama tamamlandı.")
                except Exception as e:
                    logger.error(f"StatusChecker çalıştırılırken hata: {e}")
            else:
                logger.warning("Kullanıcı listesi boş, durum sorgulama atlandı.")
        # YENİ: send_message komutu desteği
        elif command == 'send_message':
            usernames = []
            if params_dict.get('usernames'):
                usernames = [u['username'] if isinstance(u, dict) and 'username' in u else str(u) for u in params_dict['usernames']]
            if usernames:
                logger.info(f"{len(usernames)} kullanıcıya mesaj gönderme başlatılıyor: {usernames}")
                try:
                    from message_sender_thread import MessageSenderThread
                    from database_manager import DatabaseManager

                    db_manager = DatabaseManager()
                    sender = MessageSenderThread(db_manager, publishers=[{"username": u} for u in usernames])
                    sender.run()  # QThread olmadan doğrudan çalıştırıyoruz
                    logger.info("Mesaj gönderme tamamlandı.")
                except Exception as e:
                    logger.error(f"MessageSender çalıştırılırken hata: {e}")
            else:
                logger.warning("Kullanıcı listesi boş, mesaj gönderme atlandı.")
        
        return main_app
        
    except Exception as e:
        logger.error(f"Komut işleme hatası: {e}")
        logger.error(traceback.format_exc())
        update_status('error', str(e))
        return main_app

def main_loop():
    main_app = None
    consecutive_errors = 0
    max_consecutive_errors = 5
    
    while True:
        try:
            with get_db_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT * FROM automation_commands 
                        WHERE status='pending' 
                        ORDER BY created_at ASC 
                        LIMIT 1
                    """)
                    cmd = cursor.fetchone()
                    
                    if cmd:
                        logger.info(f"Yeni komut bulundu: {cmd['command']}")
                        main_app = process_command(cmd, main_app)
                        
                        # Komut işlendi olarak işaretle
                        cursor.execute("""
                            UPDATE automation_commands 
                            SET status='done', 
                                processed_at=NOW() 
                            WHERE id=%s
                        """, (cmd['id'],))
                        conn.commit()
                        
                        # Başarılı işlem sonrası hata sayacını sıfırla
                        consecutive_errors = 0
            
            # Heartbeat güncelle
            if main_app is None:
                update_status('idle')
            elif main_app.cycle_running:
                update_status('running')
            else:
                update_status('idle')
            
        except Exception as e:
            consecutive_errors += 1
            logger.error(f"Döngü hatası: {e}")
            logger.error(traceback.format_exc())
            update_status('error', str(e))
            
            # Çok fazla ardışık hata varsa bekleme süresini artır
            if consecutive_errors >= max_consecutive_errors:
                logger.warning(f"Çok fazla ardışık hata ({consecutive_errors}). 30 saniye bekleniyor...")
                time.sleep(30)
                consecutive_errors = 0
            else:
                time.sleep(2)  # Normal bekleme süresi
        else:
            time.sleep(2)  # Normal bekleme süresi

if __name__ == "__main__":
    try:
        logger.info("Otomasyon worker başlatılıyor...")
        logger.info(f"Çalışma dizini: {os.getcwd()}")
        logger.info(f"Python versiyonu: {sys.version}")
        main_loop()
    except KeyboardInterrupt:
        logger.info("Program kullanıcı tarafından durduruldu.")
    except Exception as e:
        logger.error(f"Kritik hata: {e}")
        logger.error(traceback.format_exc())
    finally:
        logger.info("Program sonlandırılıyor...") 