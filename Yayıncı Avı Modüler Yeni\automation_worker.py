import time
import json
import pymysql
import logging
from main import MainApp
from dotenv import load_dotenv
import os
import traceback
import sys
import threading

# .env dosyasını yükle
load_dotenv()

DB_CONFIG = {
    "host": os.getenv("DB_HOST", os.getenv("MYSQL_HOST", "localhost")),
    "user": os.getenv("DB_USER", os.getenv("MYSQL_USER", "root")),
    "password": os.getenv("DB_PASSWORD", os.getenv("MYSQL_PASSWORD", "")),
    "database": os.getenv("DB_NAME", os.getenv("MYSQL_DATABASE", "tiktok_live_data")),
    "charset": "utf8mb4",
    "cursorclass": pymysql.cursors.DictCursor
}

# Loglama ayarları
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s - %(message)s",
    handlers=[
        logging.FileHandler("automation_worker.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Global MainApp instance ve lock
main_app_lock = threading.Lock()
global_main_app = None

def get_db_connection():
    try:
        return pymysql.connect(**DB_CONFIG)
    except Exception as e:
        logger.error(f"Veritabanı bağlantı hatası: {e}")
        raise

def update_status(status, last_error=None):
    try:
        with get_db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    "UPDATE automation_status SET last_heartbeat=NOW(), status=%s, last_error=%s WHERE id=1",
                    (status, last_error)
                )
            conn.commit()
    except Exception as e:
        logger.error(f"Durum güncelleme hatası: {e}")

def cleanup_main_app():
    """MainApp instance'ını güvenli şekilde temizler"""
    global global_main_app
    try:
        if global_main_app:
            logger.info("🧹 Önceki MainApp instance temizleniyor...")
            if hasattr(global_main_app, 'cycle_running') and global_main_app.cycle_running:
                global_main_app.stop_automation()
                # Durdurma işleminin tamamlanması için bekle
                time.sleep(3)
            global_main_app = None
            logger.info("✅ MainApp instance temizlendi")
    except Exception as e:
        logger.error(f"MainApp temizleme hatası: {e}")
        global_main_app = None

def process_command(cmd):
    global global_main_app

    try:
        with main_app_lock:  # Thread-safe işlem
            command = cmd['command']
            params = cmd.get('params')
            params_dict = json.loads(params) if params else {}

            if command == 'start':
                duration = params_dict.get('duration', 1)  # Varsayılan 1 dakika
                headless = params_dict.get('headless', False)  # Headless mod desteği
                message_mode = params_dict.get('messageMode', 'both')

                # Önceki instance'ı kontrol et ve temizle
                if global_main_app and global_main_app.cycle_running:
                    logger.info("⚠️ Otomasyon zaten çalışıyor, yeni başlatma isteği reddediliyor.")
                    return global_main_app

                # Önceki instance'ı temizle
                cleanup_main_app()

                logger.info(f"🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:761)")
                logger.info(f"1️⃣ Scraper başlatılıyor (süre: {duration} dakika) (main.py:296)")

                # Yeni instance oluştur
                global_main_app = MainApp()
                global_main_app.set_duration(duration)
                global_main_app.set_headless(headless)
                global_main_app.set_message_mode(message_mode)

                success = global_main_app.start_automation()
                if success:
                    update_status('running')
                    logger.info(f"✅ Komut başarıyla işlendi: start (ID: {cmd['id']}) (automation_worker.py:585)")
                else:
                    logger.error("❌ Otomasyon başlatılamadı")
                    cleanup_main_app()
                    update_status('error', 'Otomasyon başlatılamadı')

            elif command == 'stop':
                if global_main_app and global_main_app.cycle_running:
                    logger.info("⏹️ Otomasyon durduruluyor.")
                    global_main_app.stop_automation()
                    cleanup_main_app()
                    update_status('stopped')
                else:
                    logger.info("ℹ️ Otomasyon zaten durdurulmuş.")

            # YENİ: check_status komutu desteği - SADECE STANDALONE ÇALIŞMA
            elif command == 'check_status':
                usernames = []
                if params_dict.get('usernames'):
                    usernames = [u['username'] if isinstance(u, dict) and 'username' in u else str(u) for u in params_dict['usernames']]
                if usernames:
                    logger.info(f"🔍 {len(usernames)} kullanıcı için durum sorgulama başlatılıyor: {usernames}")
                    try:
                        from status_checker import StatusCheckerThread
                        from database_manager import DatabaseManager

                        db_manager = DatabaseManager()
                        checker = StatusCheckerThread(db_manager, publishers=[{"username": u} for u in usernames])
                        checker.run()  # QThread olmadan doğrudan çalıştırıyoruz
                        logger.info("✅ Durum sorgulama tamamlandı.")
                    except Exception as e:
                        logger.error(f"❌ StatusChecker çalıştırılırken hata: {e}")
                else:
                    logger.warning("⚠️ Kullanıcı listesi boş, durum sorgulama atlandı.")

            # YENİ: send_message komutu desteği - SADECE STANDALONE ÇALIŞMA
            elif command == 'send_message':
                usernames = []
                if params_dict.get('usernames'):
                    usernames = [u['username'] if isinstance(u, dict) and 'username' in u else str(u) for u in params_dict['usernames']]
                if usernames:
                    logger.info(f"📩 {len(usernames)} kullanıcıya mesaj gönderme başlatılıyor: {usernames}")
                    try:
                        from message_sender_thread import MessageSenderThread
                        from database_manager import DatabaseManager

                        db_manager = DatabaseManager()
                        sender = MessageSenderThread(db_manager, users=[{"username": u} for u in usernames])
                        sender.run()  # QThread olmadan doğrudan çalıştırıyoruz
                        logger.info("✅ Mesaj gönderme tamamlandı.")
                    except Exception as e:
                        logger.error(f"❌ MessageSender çalıştırılırken hata: {e}")
                else:
                    logger.warning("⚠️ Kullanıcı listesi boş, mesaj gönderme atlandı.")

            return global_main_app

    except Exception as e:
        logger.error(f"❌ Komut işleme hatası: {e}")
        logger.error(traceback.format_exc())
        update_status('error', str(e))
        return global_main_app

def ensure_database_schema():
    """Veritabanı şemasının güncel olduğundan emin olur"""
    try:
        with get_db_connection() as conn:
            with conn.cursor() as cursor:
                # params alanının LONGTEXT olduğundan emin ol
                cursor.execute("""
                    ALTER TABLE automation_commands
                    MODIFY COLUMN params LONGTEXT
                """)
                logger.info("✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502)")
    except Exception:
        # Hata varsa da devam et, muhtemelen zaten doğru tipte
        pass

def main_loop():
    global global_main_app
    consecutive_errors = 0
    max_consecutive_errors = 5

    # Veritabanı şemasını kontrol et
    ensure_database_schema()

    logger.info("✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:647)")

    while True:
        try:
            with get_db_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT * FROM automation_commands
                        WHERE status='pending'
                        ORDER BY created_at ASC
                        LIMIT 1
                    """)
                    cmd = cursor.fetchone()

                    if cmd:
                        logger.info(f"📨 Yeni komut bulundu: {cmd['command']} (ID: {cmd['id']}) (automation_worker.py:570)")
                        process_command(cmd)

                        # Komut işlendi olarak işaretle
                        cursor.execute("""
                            UPDATE automation_commands
                            SET status='done',
                                processed_at=NOW()
                            WHERE id=%s
                        """, (cmd['id'],))
                        conn.commit()

                        # Başarılı işlem sonrası hata sayacını sıfırla
                        consecutive_errors = 0

            # Heartbeat güncelle
            if global_main_app is None:
                update_status('idle')
            elif hasattr(global_main_app, 'cycle_running') and global_main_app.cycle_running:
                update_status('running')
            else:
                update_status('idle')

        except Exception as e:
            consecutive_errors += 1
            logger.error(f"❌ Döngü hatası: {e}")
            logger.error(traceback.format_exc())
            update_status('error', str(e))

            # Çok fazla ardışık hata varsa bekleme süresini artır
            if consecutive_errors >= max_consecutive_errors:
                logger.warning(f"⚠️ Çok fazla ardışık hata ({consecutive_errors}). 30 saniye bekleniyor...")
                time.sleep(30)
                consecutive_errors = 0
            else:
                time.sleep(2)  # Normal bekleme süresi
        else:
            time.sleep(2)  # Normal bekleme süresi

if __name__ == "__main__":
    try:
        logger.info("🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:761)")
        logger.info(f"📁 Çalışma dizini: {os.getcwd()}")
        logger.info(f"🐍 Python versiyonu: {sys.version}")
        main_loop()
    except KeyboardInterrupt:
        logger.info("⏹️ Program kullanıcı tarafından durduruldu.")
        cleanup_main_app()
    except Exception as e:
        logger.error(f"❌ Kritik hata: {e}")
        logger.error(traceback.format_exc())
        cleanup_main_app()
    finally:
        logger.info("🔚 Program sonlandırılıyor...")