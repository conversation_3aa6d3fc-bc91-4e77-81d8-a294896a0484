import React, { useState, useEffect, useContext } from 'react';
import { motion } from 'framer-motion';
import { FaGraduationCap, FaSearch } from 'react-icons/fa';
import { BsCalendar3 } from 'react-icons/bs';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { SidebarContext } from '../../contexts/SidebarContext';

const Courses: React.FC = () => {
  const navigate = useNavigate();
  const [activeCategory, setActiveCategory] = useState<string>('Tüm Kategoriler');
  const [searchQuery, setSearchQuery] = useState<string>('');

  // Sidebar context'inden isMobile değerini al
  const { isMobile } = useContext(SidebarContext);
  // Kurs tipi tanımı - tek bir courses tablosu kullanıyoruz
  interface Course {
    id: number;
    title: string;
    description: string;
    content?: string;
    category: string;
    image: string;
    icon: string;
    featured: boolean;
    created_by?: number;
    created_at?: string;
    updated_at?: string;
    status?: string;
  }

  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Dinamik kategoriler
  const [categories, setCategories] = useState<string[]>(['Tüm Kategoriler']);

  // Eğitimleri API'den çek
  const fetchCourses = async () => {
    setLoading(true);
    setError(null);

    try {
      console.log('API çağrısı yapılıyor...');

      // Development modunda mock veri kullan
      const isDevMode = import.meta.env.VITE_DISABLE_AUTH === 'true' || import.meta.env.DEV;

      if (isDevMode) {
        console.log('Development mode: Mock eğitim verileri kullanılıyor');

        const mockCourses: Course[] = [
          {
            id: 1,
            title: 'TikTok İçerik Üretimi Temelleri',
            description: 'TikTok\'ta viral olacak içerikler nasıl üretilir? Bu eğitimde temel teknikleri, trend analizi ve içerik stratejilerini öğreneceksiniz.',
            content: 'Bu eğitimde TikTok algoritması, hashtag kullanımı, video düzenleme teknikleri ve daha fazlasını öğreneceksiniz.',
            category: 'Başlangıç',
            image: '',
            icon: '🎬',
            featured: true,
            created_at: '2025-01-05T12:00:00Z',
            status: 'active'
          },
          {
            id: 2,
            title: 'Monetizasyon Stratejileri',
            description: 'TikTok hesabınızdan nasıl para kazanırsınız? Creator Fund, marka iş birlikleri ve diğer gelir kaynaklarını keşfedin.',
            content: 'Bu eğitimde TikTok Creator Fund başvurusu, sponsorluk anlaşmaları, affiliate marketing ve daha fazlasını öğreneceksiniz.',
            category: 'Monetizasyon',
            image: '',
            icon: '💰',
            featured: false,
            created_at: '2025-01-03T14:30:00Z',
            status: 'active'
          },
          {
            id: 3,
            title: 'Algoritma Optimizasyonu',
            description: 'TikTok algoritmasını anlayın ve içeriklerinizin daha fazla kişiye ulaşmasını sağlayın.',
            content: 'Bu eğitimde algoritma çalışma prensipleri, engagement artırma teknikleri ve viral olma stratejilerini öğreneceksiniz.',
            category: 'İleri Seviye',
            image: '',
            icon: '📈',
            featured: true,
            created_at: '2025-01-01T09:15:00Z',
            status: 'active'
          },
          {
            id: 4,
            title: 'Video Düzenleme Teknikleri',
            description: 'Profesyonel video düzenleme teknikleri ile içeriklerinizi bir üst seviyeye taşıyın.',
            content: 'Bu eğitimde CapCut, InShot ve diğer popüler video düzenleme uygulamalarını kullanmayı öğreneceksiniz.',
            category: 'Genel',
            image: '',
            icon: '✂️',
            featured: false,
            created_at: '2024-12-28T16:45:00Z',
            status: 'active'
          }
        ];

        setCourses(mockCourses);

        // Kategorileri dinamik olarak oluştur
        const uniqueCategories = ['Tüm Kategoriler', ...Array.from(new Set(mockCourses.map(course => course.category)))];
        setCategories(uniqueCategories);

        setLoading(false);
        return;
      }

      // Production modunda gerçek API çağrısı
      const response = await axios.get('/backend/api/courses.php?status=active');
      console.log('API yanıtı:', response.data);

      if (response.data.success && response.data.data) {
        // API'den gelen verileri formatla
        const formattedCourses = response.data.data.map((course: any) => {
          return {
            id: course.id,
            title: course.title,
            description: course.description,
            content: course.content,
            category: getCategoryName(course.category),
            image: course.image || '',
            icon: course.icon || getIconForCategory(course.category),
            featured: course.featured === 1 || course.featured === true,
            created_by: course.created_by,
            created_at: course.created_at,
            updated_at: course.updated_at,
            status: course.status
          };
        });

        setCourses(formattedCourses);

        // Kategorileri çıkar
        const categorySet = new Set<string>(formattedCourses.map((course: Course) => course.category));
        const uniqueCategories: string[] = ['Tüm Kategoriler', ...Array.from(categorySet)];
        setCategories(uniqueCategories);
      } else if (response.data.data && response.data.data.length === 0) {
        // Veri var ama boş dizi ise
        console.log('API boş dizi döndürdü, örnek veriler gösteriliyor');
        setMockCourses();
      } else {
        console.log('API başarılı yanıt vermedi, örnek veriler gösteriliyor');
        setError('Eğitimler alınamadı: ' + (response.data.message || 'Bilinmeyen hata'));
        // Hata durumunda örnek veriler göster
        setMockCourses();
      }
    } catch (err: any) {
      console.error('Eğitimler alınırken hata oluştu:', err);
      console.log('Hata detayları:', err.message, err.response?.status, err.response?.data);
      setError('Eğitimler alınamadı. Lütfen daha sonra tekrar deneyin.');
      // Hata durumunda örnek veriler göster
      setMockCourses();
    } finally {
      setLoading(false);
    }
  };

  // Örnek veriler (API bağlantısı başarısız olursa)
  const setMockCourses = () => {
    const mockCourses: Course[] = [
      {
        id: 1,
        title: 'TikTok Algoritması Temelleri',
        description: 'TikTok algoritmasının temel çalışma prensipleri ve önemi hakkında detaylı bilgi.',
        content: '<h2>TikTok Algoritması Temelleri</h2><p>TikTok algoritmasının temel çalışma prensipleri ve önemi hakkında detaylı bilgi.</p>',
        category: 'Genel',
        image: 'course1.jpg',
        icon: 'chart-line',
        featured: true,
        created_by: 1,
        created_at: '2023-05-01 12:00:00',
        updated_at: '2023-05-01 12:00:00',
        status: 'active'
      },
      {
        id: 2,
        title: 'İçerik Keşif Sayfası',
        description: 'TikTok Keşfet sayfasının yapısı ve önemi.',
        content: '<h2>İçerik Keşif Sayfası</h2><p>TikTok Keşfet sayfasının yapısı ve önemi.</p>',
        category: 'Başlangıç',
        image: 'course2.jpg',
        icon: 'search',
        featured: false,
        created_by: 1,
        created_at: '2023-05-15 12:00:00',
        updated_at: '2023-05-15 12:00:00',
        status: 'active'
      },
      {
        id: 3,
        title: 'Algoritma Güncellemeleri',
        description: 'Son algoritma değişiklikleri ve etkileri.',
        content: '<h2>Algoritma Güncellemeleri</h2><p>Son algoritma değişiklikleri ve etkileri.</p>',
        category: 'İleri Seviye',
        image: 'course3.jpg',
        icon: 'bolt',
        featured: false,
        created_by: 1,
        created_at: '2023-06-10 12:00:00',
        updated_at: '2023-06-10 12:00:00',
        status: 'active'
      }
    ];

    setCourses(mockCourses);

    // Kategorileri çıkar
    const categorySet = new Set<string>(mockCourses.map(course => course.category));
    const uniqueCategories: string[] = ['Tüm Kategoriler', ...Array.from(categorySet)];
    setCategories(uniqueCategories);
  };

  // Kategori kodunu okunabilir isme dönüştür
  const getCategoryName = (categoryCode: string) => {
    const categoryMapping: { [key: string]: string } = {
      'general': 'Genel',
      'video-editing': 'Video Editörlüğü',
      'content-creation': 'İçerik Üretimi',
      'social-media': 'Sosyal Medya',
      'marketing': 'Pazarlama',
      'technical': 'Teknik',
      'creativity': 'Yaratıcılık',
      'business': 'İş Geliştirme',
      // Eski kategori isimleri için de dönüşüm
      'algoritma': 'Genel',
      'teknik': 'Teknik',
      'içerik': 'İçerik Üretimi',
      'pazarlama': 'Pazarlama',
      'işbirliği': 'İş Geliştirme',
      'topluluk': 'Sosyal Medya',
      'planlama': 'İş Geliştirme',
      'canlı yayın': 'Teknik',
      // Eğer kategori zaten güncellenmiş ise
      'Genel': 'Genel',
      'Video Editörlüğü': 'Video Editörlüğü',
      'İçerik Üretimi': 'İçerik Üretimi',
      'Sosyal Medya': 'Sosyal Medya',
      'Pazarlama': 'Pazarlama',
      'Teknik': 'Teknik',
      'Yaratıcılık': 'Yaratıcılık',
      'İş Geliştirme': 'İş Geliştirme'
    };

    return categoryMapping[categoryCode] || categoryCode?.charAt(0).toUpperCase() + categoryCode?.slice(1) || 'Genel';
  };

  // Kategori için ikon belirle
  const getIconForCategory = (categoryCode: string) => {
    switch (categoryCode) {
      case 'general': return '🔑';
      case 'beginner': return '👋';
      case 'intermediate': return '🚀';
      case 'advanced': return '⚡';
      // Eski kategori isimleri için de ikonlar ekleyelim
      case 'algoritma': return '🔑';
      case 'tiktok': return '🔑';
      case 'etki': return '👋';
      case 'içerik': return '👋';
      case 'sosyal': return '🚀';
      case 'topluluk': return '🚀';
      case 'marka': return '⚡';
      case 'canlı yayın': return '⚡';
      default: return '📚';
    }
  };

  // Sayfa yüklendiğinde eğitimleri çek
  useEffect(() => {
    fetchCourses();
  }, []);

  // Eğitim detayına git
  const handleCourseClick = (courseId: number) => {
    // React Router ile yönlendirme yap
    console.log(`Eğitim ID: ${courseId} tıklandı`);
    navigate(`/dashboard/courses/${courseId}`);
  };

  // Filtreleme fonksiyonu
  const filteredCourses = courses.filter(course => {
    // Kategori filtreleme
    const categoryMatch = activeCategory === 'Tüm Kategoriler' || course.category === activeCategory;

    // Arama filtreleme
    const searchMatch = searchQuery === '' ||
      course.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      course.description.toLowerCase().includes(searchQuery.toLowerCase());

    return categoryMatch && searchMatch;
  });

  return (
    <div className="min-h-screen w-full">
      <div className="container" style={{
        maxWidth: isMobile ? '100vw' : 'none',
        overflowX: 'hidden'
      }}>
        <div className="flex flex-col gap-6">
        {loading ? (
          <div className="text-center py-6 sm:py-10 bg-white dark:bg-[#16151c] rounded-lg shadow-sm px-2">
            <div className="w-12 h-12 sm:w-16 sm:h-16 mx-auto bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-300 rounded-full flex items-center justify-center mb-2 sm:mb-4 animate-pulse">
              <FaGraduationCap className="text-base sm:text-xl" />
            </div>
            <h3 className="text-sm sm:text-lg font-medium text-gray-900 dark:text-white">Yükleniyor...</h3>
            <p className="mt-0.5 sm:mt-1 text-xs sm:text-sm text-gray-500 dark:text-gray-400">Bekleyin.</p>
          </div>
        ) : error ? (
          <div className="text-center py-6 sm:py-10 bg-white dark:bg-[#16151c] rounded-lg shadow-sm px-2">
            <div className="w-12 h-12 sm:w-16 sm:h-16 mx-auto bg-red-100 dark:bg-red-900/30 text-red-500 dark:text-red-300 rounded-full flex items-center justify-center mb-2 sm:mb-4">
              <FaGraduationCap className="text-base sm:text-xl" />
            </div>
            <h3 className="text-sm sm:text-lg font-medium text-gray-900 dark:text-white">Hata</h3>
            <p className="mt-0.5 sm:mt-1 text-xs sm:text-sm text-gray-500 dark:text-gray-400">{error}</p>
            <button
              onClick={fetchCourses}
              className="mt-2 sm:mt-4 px-3 py-1.5 sm:px-4 sm:py-2 bg-[#FF3E71] text-white rounded-full text-xs sm:text-sm"
            >
              Tekrar Dene
            </button>
          </div>
        ) : (
          <>
            {/* Kategori Filtreleme ve Arama */}
            <div className="bg-white dark:bg-[#16151c] rounded-lg shadow-sm py-1.5 sm:py-2.5 px-1.5 sm:px-3 mb-1 overflow-hidden">
              <div className="flex flex-col lg:flex-row justify-between items-center mb-0 gap-2">
                <div className="category-scroll" style={{ width: '100%', maxWidth: '100%', overflow: 'auto', paddingLeft: '4px' }}>
                  <div style={{ display: 'flex', flexWrap: 'nowrap', width: 'max-content', paddingLeft: '0', gap: '4px' }}>
                    {categories.map((category) => (
                      <button
                        key={category}
                        onClick={() => setActiveCategory(category)}
                        className={`whitespace-nowrap px-1.5 sm:px-2 py-0.5 text-xs font-medium rounded-full transition-all duration-200 ${
                          activeCategory === category
                            ? 'bg-[#FF3E71] text-white'
                            : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                        }`}
                        style={{ flexShrink: 0, display: 'inline-block' }}
                      >
                        {category}
                      </button>
                    ))}
                  </div>
                </div>
                <div className="w-full lg:w-auto my-1 lg:my-0">
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Ara..."
                      className="w-full lg:w-48 px-2 sm:px-3 py-1 pr-8 sm:pr-10 rounded-lg border border-gray-200 dark:border-gray-700 focus:ring-2 focus:ring-[#FF3E71] dark:bg-gray-800 dark:text-white text-xs sm:text-sm"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                    <button className="absolute right-0 top-0 h-full px-2 sm:px-3 text-[#FF3E71]">
                      <FaSearch className="text-xs sm:text-sm" />
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Eğitim Kartları */}
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-4 xl:grid-cols-4 gap-2 sm:gap-3 md:gap-4">
              {filteredCourses.map((course, index) => (
                <motion.div
                  key={course.id}
                  className="bg-white dark:bg-[#16151c] rounded-lg shadow-md overflow-hidden flex flex-col cursor-pointer hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-transparent hover:border-[#FF3E71]/20"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  onClick={() => handleCourseClick(course.id)}
                >
                  <div className="relative">
                    <div className="h-32 sm:h-36 relative overflow-hidden rounded-t-lg">
                      {/* Arkaplan resmi veya gradient */}
                      {/* Etkinlikler sayfasındaki gibi gradient arkaplan */}
                      <div className="absolute inset-0 bg-gradient-to-br from-[#FF3E71]/90 to-[#FF5F87]/90 brightness-90"></div>
                      {course.image && (
                        <img
                          src={course.image}
                          alt={course.title}
                          className="absolute inset-0 w-full h-full object-cover object-center brightness-75"
                          onError={(e) => {
                            // Görsel yüklenemezse gradient arka plan zaten gösterilecek
                            e.currentTarget.style.display = 'none';
                          }}
                        />
                      )}

                      {/* Dekoratif desen */}
                      <div className="absolute inset-0 opacity-10">
                        <div className="absolute inset-0" style={{
                          backgroundImage: 'radial-gradient(circle, rgba(255,255,255,0.8) 1px, transparent 1px)',
                          backgroundSize: '15px 15px'
                        }}></div>
                      </div>

                      {/* Parlama efekti */}
                      <div className="absolute -top-10 sm:-top-20 -right-10 sm:-right-20 w-20 sm:w-40 h-20 sm:h-40 rounded-full bg-white opacity-20 blur-2xl"></div>
                      <div className="absolute -bottom-5 sm:-bottom-10 -left-5 sm:-left-10 w-16 sm:w-32 h-16 sm:h-32 rounded-full bg-white opacity-10 blur-xl"></div>

                      {/* Başlık ortada - Etkinlikler sayfasındaki gibi */}
                      <div className="absolute inset-0 flex flex-col justify-center items-center p-2 sm:p-4 text-center">
                        <h2 className="text-sm sm:text-base font-bold text-white drop-shadow-lg line-clamp-2">
                          {course.title}
                        </h2>
                      </div>

                      {/* Kategori etiketi */}
                      <div className="absolute top-1 right-1 bg-black/30 backdrop-blur-sm text-white text-[11px] px-1.5 py-0.5 rounded-md border border-white/30 shadow-lg">
                        {course.category}
                      </div>

                      {/* Öne Çıkan badgesi */}
                      {course.featured && (
                        <div className="absolute top-1 left-1 bg-gradient-to-r from-yellow-400 to-amber-500 text-white text-[11px] px-1.5 py-0.5 rounded-md border border-yellow-300/50 shadow-lg font-semibold">
                          ⭐ Öne Çıkan
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="p-2">
                    <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2 mb-1">{course.description}</p>

                    <div className="mt-1 flex justify-between items-center">
                      <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                        <BsCalendar3 className="mr-1 text-[#FF3E71] text-xs" />
                        <span>{new Date(course.created_at || Date.now()).toLocaleDateString('tr-TR')}</span>
                      </div>

                      <button className="inline-flex items-center px-2 py-1 border border-transparent rounded-full shadow-sm text-xs font-medium text-white bg-gradient-to-r from-[#FF3E71] to-[#FF5F87] hover:from-[#FF5F87] hover:to-[#FF3E71] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF3E71] transition-all duration-300">
                        Görüntüle
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {filteredCourses.length === 0 && !loading && !error && (
              <div className="text-center py-6 sm:py-10 bg-white dark:bg-[#16151c] rounded-lg shadow-sm px-2">
                <div className="w-12 h-12 sm:w-16 sm:h-16 mx-auto bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-300 rounded-full flex items-center justify-center mb-2 sm:mb-4">
                  <FaGraduationCap className="text-base sm:text-xl" />
                </div>
                <h3 className="text-sm sm:text-lg font-medium text-gray-900 dark:text-white">Eğitim Yok</h3>
                <p className="mt-0.5 sm:mt-1 text-xs sm:text-sm text-gray-500 dark:text-gray-400">Bu kategoride eğitim yok.</p>
              </div>
            )}
          </>
        )}
        </div>
      </div>
    </div>
  );
};

export default Courses;