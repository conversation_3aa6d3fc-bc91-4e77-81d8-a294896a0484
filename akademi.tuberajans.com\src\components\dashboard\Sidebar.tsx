import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import {
  HomeIcon,
  MegaphoneIcon,
  AcademicCapIcon,
  CalendarIcon,
  ClipboardDocumentListIcon,
  ArrowRightStartOnRectangleIcon,
  XMarkIcon,
  RssIcon,
  UserCircleIcon
} from '@heroicons/react/24/outline';
import { FaHome, FaBook, FaCalendarAlt, FaChartBar, FaUsers, FaCog, FaSignOutAlt } from 'react-icons/fa';

export interface SidebarProps {
  onLogout: () => void;
  isMobile: boolean;
  isOpen: boolean;
  onClose: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  onLogout,
  isMobile,
  isOpen,
  onClose
}) => {
  // Development mode kontrolü
  const isDevMode = import.meta.env.VITE_DISABLE_AUTH === 'true' || import.meta.env.DEV;

  // AuthContext'i sadece production modunda kullan
  let logout: any = null;
  if (!isDevMode) {
    try {
      const authContext = useAuth();
      logout = authContext.logout;
    } catch (error) {
      console.error('Auth context error in Sidebar:', error);
      // Fallback: basit logout fonksiyonu
      logout = () => {
        localStorage.removeItem('user');
        localStorage.removeItem('token');
        return Promise.resolve();
      };
    }
  } else {
    // Development modunda basit logout fonksiyonu
    logout = () => {
      localStorage.removeItem('user');
      localStorage.removeItem('token');
      return Promise.resolve();
    };
  }

  // Mobilde sidebar açıkken body scroll'unu engelle
  useEffect(() => {
    if (isMobile && isOpen) {
      document.body.style.overflow = 'hidden';
      document.body.style.height = '100%';
    } else {
      document.body.style.overflow = '';
      document.body.style.height = '';
    }

    // Cleanup function
    return () => {
      document.body.style.overflow = '';
      document.body.style.height = '';
    };
  }, [isMobile, isOpen]);

  const menuItems = [
    { icon: FaHome, text: 'Ana Sayfa', path: '/dashboard' },
    { icon: FaBook, text: 'Eğitimler', path: '/dashboard/courses' },
    { icon: FaCalendarAlt, text: 'Takvim', path: '/dashboard/calendar' },
    { icon: FaChartBar, text: 'İstatistikler', path: '/dashboard/stats' },
    { icon: FaUsers, text: 'Topluluk', path: '/dashboard/community' },
    { icon: FaCog, text: 'Ayarlar', path: '/dashboard/settings' },
  ];

  // Sabit genişlik değerleri
  const expandedWidth = isMobile ? '280px' : '240px';
  const collapsedWidth = '78px';

  const sidebarVariants = {
    open: {
      width: expandedWidth,
      transition: {
        type: "spring",
        stiffness: 200,
        damping: 30,
        mass: 0.8
      }
    },
    closed: {
      width: collapsedWidth,
      transition: {
        type: "spring",
        stiffness: 200,
        damping: 30,
        mass: 0.8
      }
    }
  };

  // Menü öğeleri için ekstra stabilite
  const menuItemStyle = {
    height: '44px', // Tüm menü öğeleri için sabit yükseklik
    display: 'flex',
    alignItems: 'center',
    overflow: 'hidden' // Taşma engellemek için
  };

  const currentPath = window.location.pathname;

  const sidebarClasses = `
    fixed inset-y-0 left-0 z-30 w-64 bg-white shadow-lg transform 
    ${isOpen ? 'translate-x-0' : '-translate-x-full'}
    ${!isMobile ? 'translate-x-0' : ''}
    transition-transform duration-300 ease-in-out
  `;

  return (
    <>
      {/* Mobil Overlay */}
      {isMobile && isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-20"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <aside className={sidebarClasses}>
        {/* Logo */}
        <div className="h-16 flex items-center justify-center border-b">
          <img src="/logo.png" alt="Logo" className="h-8" />
        </div>

        {/* Menu Items */}
        <nav className="mt-6">
          {menuItems.map((item) => (
            <Link
              key={item.path}
              to={item.path}
              className="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100"
            >
              <item.icon className="w-5 h-5 mr-3" />
              <span>{item.text}</span>
            </Link>
          ))}
        </nav>

        {/* Logout Button */}
        <button
          onClick={onLogout}
          className="absolute bottom-0 w-full flex items-center px-6 py-4 text-gray-700 hover:bg-gray-100"
        >
          <FaSignOutAlt className="w-5 h-5 mr-3" />
          <span>Çıkış Yap</span>
        </button>
      </aside>
    </>
  );
};

export default Sidebar;