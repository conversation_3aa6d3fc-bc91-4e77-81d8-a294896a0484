<?php
require_once __DIR__ . '/../config/config.php';
header('Content-Type: application/json');

// CORS headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$endpoint = $_GET['endpoint'] ?? '';

// Session başlat
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Kullanıcı oturum kontrolü - hem normal login hem de TikTok login'i kontrol et
$user = null;
$is_agency_publisher = false;

if (isset($_SESSION['user'])) {
    $user = $_SESSION['user'];
    $is_agency_publisher = $user['is_agency_publisher'] ?? false;
} elseif (isset($_SESSION['tiktok_user'])) {
    $user = $_SESSION['tiktok_user'];
    $is_agency_publisher = $user['is_agency_publisher'] ?? false;
} else {
    echo json_encode([
        'status' => 'error',
        'message' => 'Giriş yapılmamış',
        'code' => 'NOT_LOGGED_IN',
        'has_access' => false
    ]);
    exit;
}

// Debug: Kullanıcı bilgilerini logla
error_log("User data: " . json_encode($user));

// TikTok username'i farklı alanlardan kontrol et
$tiktok_username = null;
if (!empty($user['tiktok_username'])) {
    $tiktok_username = $user['tiktok_username'];
} elseif (!empty($user['username'])) {
    $tiktok_username = $user['username'];
} elseif (!empty($user['unique_id'])) {
    $tiktok_username = $user['unique_id'];
}

error_log("TikTok username to check: " . ($tiktok_username ?? 'NULL'));

// Eğer TikTok username varsa ajans kontrolü yap
if (!empty($tiktok_username)) {
    try {
        // Önce veritabanında hangi kullanıcılar var kontrol et
        $debug_stmt = $db->prepare("
            SELECT username
            FROM tuberaja_yayinci_takip.publisher_info
            WHERE username IS NOT NULL AND username != ''
            LIMIT 10
        ");
        $debug_stmt->execute();
        $sample_users = $debug_stmt->fetchAll(PDO::FETCH_COLUMN);
        error_log("Sample usernames in DB: " . json_encode($sample_users));

        // Ajans kontrolü yap
        $stmt = $db->prepare("
            SELECT COUNT(*), username
            FROM tuberaja_yayinci_takip.publisher_info
            WHERE username = ? AND username IS NOT NULL AND username != ''
        ");
        $stmt->execute([$tiktok_username]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $is_agency_publisher = $result['COUNT(*)'] > 0;

        error_log("Agency check query result: " . json_encode($result));
        error_log("Agency check for '" . $tiktok_username . "': " . ($is_agency_publisher ? 'true' : 'false'));

        // Session'daki bilgiyi güncelle
        if (isset($_SESSION['user'])) {
            $_SESSION['user']['is_agency_publisher'] = $is_agency_publisher;
        }
        if (isset($_SESSION['tiktok_user'])) {
            $_SESSION['tiktok_user']['is_agency_publisher'] = $is_agency_publisher;
        }

    } catch (Exception $e) {
        error_log("Agency check error: " . $e->getMessage());
        $is_agency_publisher = false;
    }
} else {
    error_log("No TikTok username found for agency check");
}

// Endpoint'e göre yetki kontrolü
$permissions = [
    'courses' => $is_agency_publisher,
    'events' => $is_agency_publisher,
    'announcements' => $is_agency_publisher,
    'feed' => $is_agency_publisher,
    'requests' => $is_agency_publisher,
    'notifications' => $is_agency_publisher,
    'profile' => true, // Herkes kendi profilini görebilir
    'dashboard' => true // Herkes dashboard'a erişebilir ama içerik farklı
];

$has_access = $permissions[$endpoint] ?? false;

// Mesajlar
$messages = [
    'courses' => [
        'allowed' => 'Eğitimlere erişim izniniz var.',
        'denied' => 'Eğitimler sadece Tuber Ajans yayıncıları için erişilebilir. Ajansımıza katılmak için iletişime geçebilirsiniz.'
    ],
    'events' => [
        'allowed' => 'Etkinliklere erişim izniniz var.',
        'denied' => 'Etkinlikler sadece Tuber Ajans yayıncıları için erişilebilir. Ajansımıza katılmak için iletişime geçebilirsiniz.'
    ],
    'announcements' => [
        'allowed' => 'Duyurulara erişim izniniz var.',
        'denied' => 'Duyurular sadece Tuber Ajans yayıncıları için erişilebilir. Ajansımıza katılmak için iletişime geçebilirsiniz.'
    ],
    'feed' => [
        'allowed' => 'Feed\'e erişim izniniz var.',
        'denied' => 'Feed sadece Tuber Ajans yayıncıları için erişilebilir. Ajansımıza katılmak için iletişime geçebilirsiniz.'
    ],
    'requests' => [
        'allowed' => 'Taleplere erişim izniniz var.',
        'denied' => 'Talepler sadece Tuber Ajans yayıncıları için erişilebilir. Ajansımıza katılmak için iletişime geçebilirsiniz.'
    ],
    'notifications' => [
        'allowed' => 'Bildirimlere erişim izniniz var.',
        'denied' => 'Bildirimler sadece Tuber Ajans yayıncıları için erişilebilir. Ajansımıza katılmak için iletişime geçebilirsiniz.'
    ]
];

$message = $has_access ? 
    ($messages[$endpoint]['allowed'] ?? 'Erişim izniniz var.') : 
    ($messages[$endpoint]['denied'] ?? 'Bu içeriğe erişim izniniz bulunmamaktadır.');

echo json_encode([
    'status' => 'success',
    'has_access' => $has_access,
    'is_agency_publisher' => $is_agency_publisher,
    'message' => $message,
    'user_info' => [
        'display_name' => $user['display_name'] ?? $user['name'] ?? '',
        'username' => $user['username'] ?? $user['tiktok_username'] ?? '',
        'is_verified' => $user['is_verified'] ?? false
    ],
    'contact_info' => !$has_access ? [
        'message' => 'Tuber Ajans ailesine katılmak ister misiniz?',
        'email' => '<EMAIL>',
        'phone' => '+90 XXX XXX XX XX',
        'website' => 'https://tuberajans.com'
    ] : null
]);
?>
