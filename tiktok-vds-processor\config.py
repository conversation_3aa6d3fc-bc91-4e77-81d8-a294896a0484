# TikTok VDS İşleyicisi - Konfigürasyon Dosyası

# Veritabanı bağlantı bilgileri
DB_CONFIG = {
    'host': '**************',  # X-site ile aynı sunucu
    'database': 'social_media_analytics',
    'user': 'root',                   # Veritabanı kullanıcı adı
    'password': 'Bebek845396!', # VDS'deki MySQL root şifresi
    'charset': 'utf8mb4',
    'port': 3306
}

# ChromeDriver yolu (VDS'deki tam yol)
# CHROME_DRIVER_PATH = '/home/<USER>/chromedriver'  # Linux için
CHROME_DRIVER_PATH = r'C:\chromedriver.exe'  # Windows için

# Chrome profil ayarları (Yayıncı Avı ile aynı profil)
CHROME_BINARY_PATH = r"C:\Users\<USER>\Downloads\chrome-win64\chrome-win64\chrome.exe"
CHROME_PROFILE_PATH = r"C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data"
CHROME_PROFILE_DIRECTORY = "Profile 1"

# VDS adı (benzersiz olmalı)
VDS_NAME = 'VDS-TikTok-01'

# Log dosyası yolu
LOG_FILE = r'C:\Users\<USER>\Desktop\tiktok-vds-processor\logs\tiktok_processor.log'

# İşlem ayarları
MAX_VIDEOS_PER_PROFILE = 10  # Her profil için maksimum video sayısı
REQUEST_TIMEOUT = 300       # İstek timeout süresi (saniye)
POLL_INTERVAL = 10          # Veritabanı kontrol aralığı (saniye)
RETRY_INTERVAL = 30         # Hata durumunda bekleme süresi (saniye)

# Chrome ayarları (Yayıncı Avı ile aynı ayarlar)
CHROME_OPTIONS = [
    '--disable-gpu',
    '--disable-notifications',
    '--disable-popup-blocking',
    '--start-maximized',
    '--lang=tr-TR',
    '--disable-blink-features=AutomationControlled',
    '--no-sandbox',
    '--disable-dev-shm-usage'
]

# Chrome prefs
CHROME_PREFS = {
    'profile.default_content_setting_values.notifications': 2,
    'profile.default_content_settings.popups': 0,
    'profile.managed_default_content_settings.images': 2,
    'profile.default_content_setting_values.media_stream': 2
}
