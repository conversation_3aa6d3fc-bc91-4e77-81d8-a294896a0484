<?php
// Hata ayıklama
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/basvurular_errors.log');

error_log("Başvurular API başladı - " . date('Y-m-d H:i:s'));

// CORS başlıkları
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE');
header('Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization');
header('Content-Type: application/json; charset=utf-8');

// OPTIONS istekleri için
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(204);
    exit;
}

try {
    error_log("Config dosyasını dahil etme başladı");
    require_once __DIR__ . '/../config/config.php';
    error_log("Config dahil edildi");

    // Veritabanı bağlantısı
    $pdo = $db_takip;

    // İstek türüne göre işlem yap
    $action = isset($_GET['action']) ? $_GET['action'] : 'list';
    error_log("İstek türü: $action");

    // Başvuruların sayısını getir
    if ($action === 'count') {
        try {
            error_log("Sayım işlemi başladı");
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM sitebasvurular WHERE isRead = 0");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $count = $result['count'] ?? 0;

            echo json_encode(['success' => true, 'count' => (int)$count]);
            error_log("Sayım başarılı: $count");
        } catch (PDOException $e) {
            error_log("Sayım hatası: " . $e->getMessage());
            echo json_encode(['success' => false, 'message' => 'Başvuru sayısı alınamadı', 'error' => $e->getMessage()]);
        }
        exit;
    }

    // Tüm başvuruları listele
    else if ($action === 'list') {
        try {
            error_log("Listeleme başladı");
            $stmt = $pdo->query("SELECT * FROM sitebasvurular ORDER BY created_at DESC");
            $data = $stmt->fetchAll(PDO::FETCH_ASSOC);

            echo json_encode(['success' => true, 'data' => $data]);
            error_log("Listeleme başarılı, kayıt sayısı: " . count($data));
        } catch (PDOException $e) {
            error_log("Listeleme hatası: " . $e->getMessage());
            echo json_encode(['success' => false, 'message' => 'Başvurular alınamadı', 'error' => $e->getMessage()]);
        }
        exit;
    }

    // Başvuru durumunu güncelle
    else if ($action === 'update_status') {
        try {
            error_log("Durum güncelleme başladı");
            $data = json_decode(file_get_contents('php://input'), true);
            $id = isset($data['id']) ? intval($data['id']) : 0;
            $approve = isset($data['approve']) ? (int)$data['approve'] : null;
            $isReject = isset($data['isReject']) ? (int)$data['isReject'] : null;
            $isRead = isset($data['isRead']) ? (int)$data['isRead'] : null;

            if ($id <= 0 || ($approve === null && $isReject === null && $isRead === null)) {
                echo json_encode(['success' => false, 'message' => 'Eksik parametreler']);
                error_log("Durum güncelleme hatası: Eksik parametreler (id: $id, approve: $approve, isReject: $isReject, isRead: $isRead)");
                exit;
            }
            $setParts = [];
            $params = [];
            if ($approve !== null) {
                $setParts[] = 'approve = ?';
                $params[] = $approve;
            }
            if ($isReject !== null) {
                $setParts[] = 'isReject = ?';
                $params[] = $isReject;
            }
            if ($isRead !== null) {
                $setParts[] = 'isRead = ?';
                $params[] = $isRead;
            }
            $params[] = $id;
            $stmt = $pdo->prepare("UPDATE sitebasvurular SET ".implode(', ', $setParts)." WHERE id = ?");
            $stmt->execute($params);

            echo json_encode(['success' => true]);
            error_log("Durum güncelleme başarılı: ID $id, approve: $approve, isReject: $isReject, isRead: $isRead");
        } catch (PDOException $e) {
            error_log("Durum güncelleme hatası: " . $e->getMessage());
            echo json_encode(['success' => false, 'message' => 'Durum güncellenemedi', 'error' => $e->getMessage()]);
        }
        exit;
    }

    // Yeni başvuru ekle (Ana siteden gelen başvurular için)
    else if ($action === 'add') {
        try {
            error_log("Yeni başvuru ekleme başladı");
            $data = json_decode(file_get_contents('php://input'), true);

            // Gerekli alanları kontrol et
            $name = $data['name'] ?? '';
            $surname = $data['surname'] ?? '';
            $phone = $data['phone'] ?? '';
            $mail = $data['mail'] ?? '';
            $tiktok_username = $data['tiktok_username'] ?? '';
            $instagram_username = $data['instagram_username'] ?? '';
            $yayin_suresi = $data['yayin_suresi'] ?? '';
            $follower_range = $data['follower_range'] ?? '';
            $deneyim = $data['deneyim'] ?? '';
            $approve = $data['approve'] ?? 0;
            $approveTic = $data['approveTic'] ?? 0;
            $ip = $data['ip'] ?? '';
            $unixts = $data['unixts'] ?? time();
            $isRead = $data['isRead'] ?? 0;
            $isAcademy = $data['isAcademy'] ?? 0;
            $isFinal = $data['isFinal'] ?? 0;
            $isReject = $data['isReject'] ?? 0;
            $smsRed = $data['smsRed'] ?? 0;

            if (empty($name) || empty($phone) || empty($tiktok_username)) {
                echo json_encode(['success' => false, 'message' => 'Ad, telefon ve TikTok kullanıcı adı zorunludur']);
                error_log("Başvuru ekleme hatası: Eksik zorunlu alanlar");
                exit;
            }

            // Veritabanına ekle
            $stmt = $pdo->prepare("
                INSERT INTO sitebasvurular
                (name, surname, phone, mail, tiktok_username, instagram_username, yayin_suresi,
                 follower_range, deneyim, approve, approveTic, ip, unixts, isRead, isAcademy,
                 isFinal, isReject, smsRed, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");

            $stmt->execute([
                $name, $surname, $phone, $mail, $tiktok_username, $instagram_username,
                $yayin_suresi, $follower_range, $deneyim, $approve, $approveTic, $ip,
                $unixts, $isRead, $isAcademy, $isFinal, $isReject, $smsRed
            ]);

            $newId = $pdo->lastInsertId();

            echo json_encode(['success' => true, 'id' => $newId, 'message' => 'Başvuru başarıyla eklendi']);
            error_log("Başvuru başarıyla eklendi: ID $newId, Ad: $name, TikTok: $tiktok_username");

        } catch (PDOException $e) {
            error_log("Başvuru ekleme hatası: " . $e->getMessage());
            echo json_encode(['success' => false, 'message' => 'Başvuru eklenemedi', 'error' => $e->getMessage()]);
        }
        exit;
    }

    // Başvuru sil
    else if ($action === 'delete') {
        try {
            error_log("Silme işlemi başladı");
            $data = json_decode(file_get_contents('php://input'), true);
            $id = isset($data['id']) ? intval($data['id']) : 0;

            if ($id <= 0) {
                echo json_encode(['success' => false, 'message' => 'Geçersiz ID']);
                error_log("Silme hatası: Geçersiz ID: $id");
                exit;
            }

            $stmt = $pdo->prepare("DELETE FROM sitebasvurular WHERE id = ?");
            $stmt->execute([$id]);

            echo json_encode(['success' => true]);
            error_log("Silme başarılı: ID $id");
        } catch (PDOException $e) {
            error_log("Silme hatası: " . $e->getMessage());
            echo json_encode(['success' => false, 'message' => 'Başvuru silinemedi', 'error' => $e->getMessage()]);
        }
        exit;
    }

    // Geçersiz istek
    echo json_encode(['success' => false, 'message' => 'Geçersiz istek türü: ' . $action]);
    error_log("Geçersiz istek türü: $action");

} catch (Exception $e) {
    error_log("Genel hata: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'İşlem sırasında hata oluştu', 'error' => $e->getMessage()]);
}