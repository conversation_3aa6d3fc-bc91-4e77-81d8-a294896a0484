import{j as o}from"./reactDnd-CIvPAkL_.js";import{r as n}from"./vendor-CnpYymF8.js";import T from"./Publishers-CqqF4Vjo.js";import N from"./Influencers-BgoiuH6_.js";import{A as l,S as p}from"./index-CVO3aNyS.js";import{j as k,k as f,b as w,l as z}from"./App-DhIV03Gw.js";import{_ as A,$ as m,o as B,k as h}from"./antd-gS---Efz.js";import"./utils-CtuI0RRe.js";import"./x-BlF-lTk7.js";import"./createLucideIcon-DxVmGoQf.js";import"./EllipsisOutlined-PshnPLca.js";import"./DownloadOutlined-dOuUZbfI.js";import"./UploadOutlined-C87fzLVr.js";import"./plus-DjpIx7VF.js";import"./api-Rn_0IlsQ.js";import"./trash-2-C8zHBfrV.js";import"./charts-CXWFy-zF.js";const V=()=>{const[u,g]=n.useState("1"),[x,i]=n.useState(0),[c,s]=n.useState(0),[e,b]=n.useState(!1),S=n.useRef(null),d=k(),a=d?d.darkMode:!1;n.useEffect(()=>{const t=()=>{b(window.innerWidth<=768)};return t(),window.addEventListener("resize",t),()=>{window.removeEventListener("resize",t)}},[]);const C=async()=>{try{const t=await f.get(`${l.X_SITE_BASE_URL}${l.ENDPOINTS.PUBLISHERS}`,{headers:{Authorization:`Bearer ${localStorage.getItem(p.TOKEN_KEY)}`,"Content-Type":"application/json"}});t&&t.data&&Array.isArray(t.data.data)?i(t.data.data.length):i(0);const r=await f.get(`${l.X_SITE_BASE_URL}${l.ENDPOINTS.INFLUENCERS}?countOnly=1`,{headers:{Authorization:`Bearer ${localStorage.getItem(p.TOKEN_KEY)}`,"Content-Type":"application/json"}});r&&r.data&&typeof r.data.count=="number"?s(r.data.count):r&&r.data&&Array.isArray(r.data.data)?s(r.data.data.length):s(0)}catch(t){i(0),s(0)}};n.useEffect(()=>{C()},[]);const y=t=>{g(t)},I=[{key:"1",label:o.jsxs(h,{size:e?2:8,className:"tab-label",children:[o.jsx(w,{}),o.jsx("span",{style:{marginLeft:e?"2px":"8px",color:a?"#e6e6e6":"inherit"},children:"Yayıncılar"}),o.jsxs("span",{style:{fontSize:e?"10px":"12px",color:"#52c41a",fontWeight:"normal",opacity:.8},children:["(",x,")"]})]}),children:o.jsx(T,{hideHeader:!0,isMobileView:e,onDataChange:t=>i(t),darkTheme:a})},{key:"2",label:o.jsxs(h,{size:e?2:8,className:"tab-label",children:[o.jsx(z,{}),o.jsx("span",{style:{marginLeft:e?"2px":"8px",color:a?"#e6e6e6":"inherit"},children:"Influencerlar"}),o.jsxs("span",{style:{fontSize:e?"10px":"12px",color:"#1890ff",fontWeight:"normal",opacity:.8},children:["(",c,")"]})]}),children:o.jsx(N,{hideHeader:!0,isMobileView:e,onDataChange:t=>s(t),initialCount:c})}],E={padding:0,margin:0,marginTop:-85,marginLeft:e?0:-16,marginRight:e?0:-48,width:e?"100%":"100vw",maxWidth:"100%",overflowX:"hidden",backgroundColor:a?"#141414":"#fff"},j={margin:0,padding:e?"0px 8px 0 8px":"0px 24px 0 24px",backgroundColor:a?"#141414":"#fff",borderBottom:`1px solid ${a?"#303030":"#e8e8e8"}`};return o.jsx("div",{className:`content-creators-page ${a?"dark-theme":""}`,style:E,ref:S,children:o.jsx(A,{theme:{algorithm:a?[m.darkAlgorithm]:[m.defaultAlgorithm],components:{Tabs:{titleFontSize:e?14:16,cardGutter:e?2:8,horizontalItemPadding:e?"4px 8px":"8px 16px"},Table:{headerBg:a?"#1f1f1f":e?"#f5f5f5":"#f0f0f0",headerSplitColor:a?"#303030":"#e8e8e8",colorBgContainer:a?"#141414":"#ffffff",colorBorderSecondary:a?"#303030":"#e8e8e8",paddingXS:e?4:8,paddingSM:e?6:12,paddingMD:e?8:16,fontSize:e?12:14,colorTextHeading:a?"rgba(255, 255, 255, 0.85)":"rgba(0, 0, 0, 0.85)"},Button:{paddingInline:e?8:16,paddingBlock:e?2:4,fontSizeSM:e?12:14}}},children:o.jsx(B,{activeKey:u,onChange:y,items:I,className:`custom-tabs ${e?"mobile-tabs":""} ${a?"dark-theme-tabs":""}`,tabBarStyle:j,tabBarGutter:e?10:30,size:e?"small":"large",moreIcon:o.jsx("span",{className:"more-tabs-icon",children:"Daha"}),centered:e})})})};export{V as default};
