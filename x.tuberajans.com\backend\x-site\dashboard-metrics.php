<?php
/**
 * Dashboard Metrics API - Frontend ile Uyumlu Gerçek Veritabanı Versiyonu
 * Frontend DataContext'in beklediği exact format ile çalışır
 */

// Hızlı ayarlar
set_time_limit(8); // 8 saniye max
ini_set('memory_limit', '64M');

// Headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// OPTIONS isteği için hızlı çıkış
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

$startTime = microtime(true);

// Cache ayarları
$range = isset($_GET['range']) && is_numeric($_GET['range']) ? intval($_GET['range']) : 7;
$cacheKey = "dashboard_real_v2_$range";
$cacheFile = sys_get_temp_dir() . "/cache_$cacheKey.json";

// Cache kontrolü (90 saniye)
if (file_exists($cacheFile) && filemtime($cacheFile) > time() - 90) {
    $cached = json_decode(file_get_contents($cacheFile), true);
    if ($cached && is_array($cached)) {
        $cached['cached'] = true;
        $cached['response_time'] = round((microtime(true) - $startTime) * 1000, 1) . 'ms';
        echo json_encode($cached);
        exit();
    }
}

// Veritabanı bağlantı ayarları
$servername = "**************";
$db_username = "root";
$db_password = "Bebek845396!";

try {
    // Ana veritabanı bağlantısı (takip sistemi)
    $pdo_takip = new PDO("mysql:host=$servername;dbname=tuberaja_yayinci_takip;charset=utf8mb4", 
                   $db_username, $db_password, [
                       PDO::ATTR_TIMEOUT => 3,
                       PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                       PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
                   ]);
    
    // TikTok Live Data bağlantısı
    $pdo_tiktok = new PDO("mysql:host=$servername;dbname=tiktok_live_data;charset=utf8mb4", 
                   $db_username, $db_password, [
                       PDO::ATTR_TIMEOUT => 3,
                       PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                       PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
                   ]);
    
    // Test connections
    $pdo_takip->query("SELECT 1")->fetch();
    $pdo_tiktok->query("SELECT 1")->fetch();
    
    // METRICS SECTION - Frontend'in exact beklediği format
    $metrics = [];
    
    // 1. Toplam yayıncı sayısı - publisher_info tablosundan
    try {
        $stmt = $pdo_takip->query("SELECT COUNT(*) FROM publisher_info WHERE username IS NOT NULL AND username != ''");
        $totalPublishers = (int)$stmt->fetchColumn();
        $metrics['total_publishers'] = $totalPublishers;
    } catch (Exception $e) {
        $metrics['total_publishers'] = 0;
    }
    
    // 2. Weekly Tasks - toplam ve tamamlanan görevler
    try {
        $stmt = $pdo_takip->query("SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN durum = 'tamamlandı' THEN 1 ELSE 0 END) as completed
            FROM weekly_tasks");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $metrics['total_tasks'] = (int)$result['total'];
        $metrics['completed_tasks'] = (int)$result['completed'];
        $metrics['task_completion'] = $result['total'] > 0 ? round(($result['completed'] / $result['total']) * 100, 1) : 0;
    } catch (Exception $e) {
        $metrics['total_tasks'] = 0;
        $metrics['completed_tasks'] = 0;
        $metrics['task_completion'] = 0;
    }
    
    // 3. Toplam takipçi ve elmas - weekly_archive tablosundan
    try {
        $stmt = $pdo_takip->query("SELECT 
            SUM(yeni_takipciler) as total_followers,
            SUM(elmaslar) as total_diamonds,
            SUM(yayin_suresi) as total_streaming_time
            FROM weekly_archive");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $metrics['total_followers'] = (int)($result['total_followers'] ?? 0);
        $metrics['total_diamonds'] = (int)($result['total_diamonds'] ?? 0);
        $metrics['total_streaming_time'] = (int)($result['total_streaming_time'] ?? 0);
    } catch (Exception $e) {
        $metrics['total_followers'] = 0;
        $metrics['total_diamonds'] = 0;
        $metrics['total_streaming_time'] = 0;
    }
    
    // 4. Yayıncı Keşfi - TikTok Live Data'dan bugün keşfedilenler
    $publisherDiscoveryData = [
        'today_discovered' => 0,
        'total_discovered' => 0,
        'suitable_count' => 0,
        'program_status' => 'stopped'
    ];
    
    try {
        // Bugün keşfedilen yayıncılar
        $stmt = $pdo_tiktok->query("SELECT COUNT(*) FROM live_data WHERE DATE(timestamp) = CURDATE()");
        $todayDiscovered = (int)$stmt->fetchColumn();
        
        // Toplam keşfedilen
        $stmt = $pdo_tiktok->query("SELECT COUNT(*) FROM live_data");
        $totalDiscovered = (int)$stmt->fetchColumn();
        
        // Uygun olanlar (status = 'Uygun' veya 'Uygun Elite')
        $stmt = $pdo_tiktok->query("SELECT COUNT(*) FROM live_data WHERE status IN ('Uygun', 'Uygun Elite')");
        $suitableCount = (int)$stmt->fetchColumn();
        
        // Program durumunu kontrol et (automation_status tablosundan)
        try {
            $stmt = $pdo_tiktok->query("SELECT status FROM automation_status WHERE id = 1 LIMIT 1");
            $programStatus = $stmt->fetchColumn();
            $programStatus = $programStatus === 'running' ? 'running' : 'stopped';
        } catch (Exception $e) {
            $programStatus = 'stopped';
        }
        
        $publisherDiscoveryData = [
            'today_discovered' => $todayDiscovered,
            'total_discovered' => $totalDiscovered,
            'suitable_count' => $suitableCount,
            'program_status' => $programStatus
        ];
        
    } catch (Exception $e) {
        // TikTok veritabanı erişim hatası - varsayılan değerler kullan
        error_log("TikTok Live Data access error: " . $e->getMessage());
    }
    
    // 5. Trend Data - Son haftalık performans (weekly_archive'dan)
    $trendData = [];
    try {
        $stmt = $pdo_takip->query("SELECT 
            DATE(hafta_baslangici) as week_start,
            COUNT(DISTINCT kullanici_adi) as active_publishers,
            SUM(yeni_takipciler) as total_followers,
            SUM(elmaslar) as total_diamonds,
            SUM(yayin_suresi) as total_streaming_time,
            SUM(canli_yayin_gunu) as streaming_days
          FROM weekly_archive
            WHERE hafta_baslangici >= DATE_SUB(NOW(), INTERVAL 8 WEEK)
            GROUP BY DATE(hafta_baslangici)
            ORDER BY hafta_baslangici ASC");
        
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $trendData[] = [
                'week_start' => $row['week_start'],
                'total_followers' => (int)$row['total_followers'],
                'total_diamonds' => (int)$row['total_diamonds'],
                'active_publishers' => (int)$row['active_publishers'],
                'streaming_time' => (int)$row['total_streaming_time'],
                'streaming_days' => (int)$row['streaming_days']
            ];
        }
    } catch (Exception $e) {
        // Hata durumunda boş array
        $trendData = [];
    }
    
    // 6. Top Publishers - En iyi performans gösteren yayıncılar
    $topPublishers = [];
    try {
        $stmt = $pdo_takip->query("SELECT 
            w.kullanici_adi as username,
            p.isim_soyisim as name,
            SUM(w.yeni_takipciler) as total_followers,
            SUM(w.elmaslar) as total_diamonds,
            SUM(w.yayin_suresi) as streaming_time,
            AVG(CASE WHEN w.durum = 'tamamlandı' THEN 100 ELSE 0 END) as completion_rate
            FROM weekly_archive w
            LEFT JOIN publisher_info p ON w.kullanici_adi = p.username
            WHERE w.hafta_baslangici >= DATE_SUB(NOW(), INTERVAL 4 WEEK)
            GROUP BY w.kullanici_adi
            HAVING total_diamonds > 0
            ORDER BY total_diamonds DESC
            LIMIT 10");
        
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $topPublishers[] = [
                'id' => $row['username'],
                'username' => $row['username'],
                'name' => $row['name'] ?? $row['username'],
                'growth' => (int)$row['total_followers'],
                'diamonds' => (int)$row['total_diamonds'],
                'streaming_time' => (int)$row['streaming_time'],
                'completion_rate' => round($row['completion_rate'], 1)
            ];
        }
    } catch (Exception $e) {
        $topPublishers = [];
    }
    
    // 7. Category Distribution - İçerik türü dağılımı (mock data - gerçek kategori tablosu yoksa)
    $categoryDistribution = [
        ['name' => 'Gaming', 'value' => 35],
        ['name' => 'Lifestyle', 'value' => 25],
        ['name' => 'Entertainment', 'value' => 20],
        ['name' => 'Music', 'value' => 15],
        ['name' => 'Other', 'value' => 5]
    ];
    
    // 8. Recent Activity - Son aktiviteler
    $recentActivity = [];
    try {
        // Son tamamlanan görevler
        $stmt = $pdo_takip->query("SELECT 
            kullanici_adi, 
            gorev_onerisi,
            updated_at 
            FROM weekly_tasks 
            WHERE durum = 'tamamlandı' 
            ORDER BY updated_at DESC 
            LIMIT 5");
        
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $timeAgo = time() - strtotime($row['updated_at']);
            $recentActivity[] = [
                'text' => $row['kullanici_adi'] . " görevini tamamladı: " . $row['gorev_onerisi'],
                'time' => $timeAgo < 3600 ? floor($timeAgo/60) . ' dk önce' : floor($timeAgo/3600) . ' saat önce',
                'type' => 'task_completed'
            ];
        }
        
        // Son kayıt olan yayıncılar
        $stmt = $pdo_takip->query("SELECT 
            username, 
            isim_soyisim,
            kayit_tarihi 
            FROM publisher_info 
            ORDER BY kayit_tarihi DESC 
            LIMIT 3");
        
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $timeAgo = time() - strtotime($row['kayit_tarihi']);
            $recentActivity[] = [
                'text' => "Yeni yayıncı: " . ($row['isim_soyisim'] ?? $row['username']),
                'time' => $timeAgo < 86400 ? floor($timeAgo/3600) . ' saat önce' : floor($timeAgo/86400) . ' gün önce',
                'type' => 'new_publisher'
            ];
        }
        
    } catch (Exception $e) {
        $recentActivity = [
            ['text' => 'Sistem çalışıyor', 'time' => 'Şimdi', 'type' => 'system'],
            ['text' => 'Veritabanı bağlantısı aktif', 'time' => '1 dk önce', 'type' => 'system']
        ];
    }
    
    // FRONTEND'İN EXACT BEKLEDİĞİ FORMAT
    $response = [
        'success' => true,
        'cached' => false,
        
        // Metrics object - DataContext'in beklediği format
        'metrics' => [
            'total_publishers' => $metrics['total_publishers'],
            'total_tasks' => $metrics['total_tasks'], 
            'completed_tasks' => $metrics['completed_tasks'],
            'total_followers' => $metrics['total_followers'],
            'total_diamonds' => $metrics['total_diamonds'],
            'task_completion' => $metrics['task_completion'],
            
            // Trend data array
            'trend_data' => $trendData,
            
            // Top publishers array
            'top_publishers' => $topPublishers,
            
            // Category distribution
            'category_distribution' => $categoryDistribution,
            
            // Publisher discovery data
            'publisher_discovery_data' => $publisherDiscoveryData,
            
            // Additional metrics
            'current_month_revenue' => 0, // TODO: Gelir tablosu eklendikçe
            'previous_month_revenue' => 0,
            'target_revenue' => 0,
            'target_growth' => 0,
        ],
        
        // Recent activity
        'recent_activity' => array_slice($recentActivity, 0, 8),
        
        // Meta information
        'timestamp' => time(),
        'source' => 'database',
        'database_sources' => [
            'publisher_count' => 'tuberaja_yayinci_takip.publisher_info',
            'tasks' => 'tuberaja_yayinci_takip.weekly_tasks',
            'performance' => 'tuberaja_yayinci_takip.weekly_archive',
            'discovery' => 'tiktok_live_data.live_data'
        ],
        'response_time' => round((microtime(true) - $startTime) * 1000, 1) . 'ms'
    ];
    
    // Cache'e kaydet (90 saniye)
    file_put_contents($cacheFile, json_encode($response));
    
    echo json_encode($response);
    
} catch (Exception $e) {
    error_log("Dashboard metrics database error: " . $e->getMessage());
    
    // Hata durumunda fallback response - frontend'in çalışması için
    $fallbackResponse = [
        'success' => false,
        'cached' => false,
        'error' => 'Database connection failed',
        'metrics' => [
            'total_publishers' => 0,
            'total_tasks' => 0,
            'completed_tasks' => 0,
            'total_followers' => 0,
            'total_diamonds' => 0,
            'task_completion' => 0,
            'trend_data' => [],
            'top_publishers' => [],
            'category_distribution' => [],
            'publisher_discovery_data' => [
                'today_discovered' => 0,
                'program_status' => 'error'
            ]
        ],
        'recent_activity' => [
            ['text' => 'Veritabanı bağlantı hatası', 'time' => 'Şimdi', 'type' => 'error']
        ],
        'timestamp' => time(),
        'source' => 'fallback',
        'response_time' => round((microtime(true) - $startTime) * 1000, 1) . 'ms'
    ];
    
    http_response_code(200); // Frontend'in çalışması için 200 döndür
    echo json_encode($fallbackResponse);
}
?> 