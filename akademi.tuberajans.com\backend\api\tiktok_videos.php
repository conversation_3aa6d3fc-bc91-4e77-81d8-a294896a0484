<?php
require_once __DIR__ . '/../config/config.php';
header('Content-Type: application/json');

// CORS headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// TikTok kullanıcısının oturum kontrolü
if (!isset($_SESSION['tiktok_user']) || !isset($_SESSION['tiktok_user']['access_token'])) {
    echo json_encode([
        'status' => 'error',
        'message' => 'TikTok ile giriş yapılma<PERSON>ı<PERSON>',
        'code' => 'NOT_LOGGED_IN'
    ]);
    exit;
}

$access_token = $_SESSION['tiktok_user']['access_token'];

try {
    // TikTok Video List API'den kullanıcının videolarını çek
    $video_list_url = 'https://open.tiktokapis.com/v2/video/list/?fields=id,title,video_description,duration,cover_image_url,embed_link,like_count,comment_count,share_count,view_count,create_time';
    
    $ch = curl_init($video_list_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $access_token,
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
        'max_count' => 20 // Son 20 videoyu getir
    ]));
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);

    if ($response === false) {
        throw new Exception('cURL hatası: ' . $curl_error);
    }

    if ($http_code !== 200) {
        throw new Exception('TikTok API hatası: HTTP ' . $http_code . ' - ' . $response);
    }

    $video_data = json_decode($response, true);

    if (isset($video_data['data']['videos']) && is_array($video_data['data']['videos'])) {
        $videos = $video_data['data']['videos'];
        
        // Video verilerini formatla
        $formatted_videos = [];
        foreach ($videos as $video) {
            $formatted_videos[] = [
                'id' => $video['id'] ?? '',
                'title' => $video['title'] ?? '',
                'description' => $video['video_description'] ?? '',
                'duration' => $video['duration'] ?? 0,
                'cover_image_url' => $video['cover_image_url'] ?? '',
                'embed_link' => $video['embed_link'] ?? '',
                'like_count' => $video['like_count'] ?? 0,
                'comment_count' => $video['comment_count'] ?? 0,
                'share_count' => $video['share_count'] ?? 0,
                'view_count' => $video['view_count'] ?? 0,
                'create_time' => $video['create_time'] ?? '',
                'formatted_date' => isset($video['create_time']) ? date('d.m.Y H:i', $video['create_time']) : '',
                'engagement_rate' => 0 // Hesaplanacak
            ];
        }

        // Engagement rate hesapla
        foreach ($formatted_videos as &$video) {
            if ($video['view_count'] > 0) {
                $total_engagement = $video['like_count'] + $video['comment_count'] + $video['share_count'];
                $video['engagement_rate'] = round(($total_engagement / $video['view_count']) * 100, 2);
            }
        }

        // Toplam istatistikleri hesapla
        $total_stats = [
            'total_videos' => count($formatted_videos),
            'total_views' => array_sum(array_column($formatted_videos, 'view_count')),
            'total_likes' => array_sum(array_column($formatted_videos, 'like_count')),
            'total_comments' => array_sum(array_column($formatted_videos, 'comment_count')),
            'total_shares' => array_sum(array_column($formatted_videos, 'share_count')),
            'average_views' => count($formatted_videos) > 0 ? round(array_sum(array_column($formatted_videos, 'view_count')) / count($formatted_videos)) : 0,
            'average_likes' => count($formatted_videos) > 0 ? round(array_sum(array_column($formatted_videos, 'like_count')) / count($formatted_videos)) : 0,
            'average_engagement_rate' => count($formatted_videos) > 0 ? round(array_sum(array_column($formatted_videos, 'engagement_rate')) / count($formatted_videos), 2) : 0
        ];

        echo json_encode([
            'status' => 'success',
            'videos' => $formatted_videos,
            'stats' => $total_stats,
            'user_info' => [
                'display_name' => $_SESSION['tiktok_user']['display_name'] ?? '',
                'username' => $_SESSION['tiktok_user']['username'] ?? '',
                'follower_count' => $_SESSION['tiktok_user']['follower_count'] ?? 0,
                'following_count' => $_SESSION['tiktok_user']['following_count'] ?? 0,
                'likes_count' => $_SESSION['tiktok_user']['likes_count'] ?? 0,
                'video_count' => $_SESSION['tiktok_user']['video_count'] ?? 0
            ]
        ]);

    } else {
        // Video bulunamadı veya hata
        echo json_encode([
            'status' => 'success',
            'videos' => [],
            'stats' => [
                'total_videos' => 0,
                'total_views' => 0,
                'total_likes' => 0,
                'total_comments' => 0,
                'total_shares' => 0,
                'average_views' => 0,
                'average_likes' => 0,
                'average_engagement_rate' => 0
            ],
            'user_info' => [
                'display_name' => $_SESSION['tiktok_user']['display_name'] ?? '',
                'username' => $_SESSION['tiktok_user']['username'] ?? '',
                'follower_count' => $_SESSION['tiktok_user']['follower_count'] ?? 0,
                'following_count' => $_SESSION['tiktok_user']['following_count'] ?? 0,
                'likes_count' => $_SESSION['tiktok_user']['likes_count'] ?? 0,
                'video_count' => $_SESSION['tiktok_user']['video_count'] ?? 0
            ],
            'message' => 'Video bulunamadı veya erişim izni yok',
            'raw_response' => $video_data
        ]);
    }

} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => 'Video listesi alınırken hata oluştu: ' . $e->getMessage(),
        'code' => 'API_ERROR'
    ]);
}
?>
