2025-06-16 13:06:12,813 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:761) (automation_worker.py:251)
2025-06-16 13:06:12,821 [INFO] __main__ - 📁 Çalışma dizini: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:252)
2025-06-16 13:06:12,823 [INFO] __main__ - 🐍 Python versiyonu: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)] (automation_worker.py:253)
2025-06-16 13:23:35,016 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 13:23:35,017 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı A<PERSON><PERSON> (automation_worker.py:253)
2025-06-16 13:23:35,028 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 13:23:35,043 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 13:23:35,044 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 13:23:45,183 [INFO] __main__ - 📨 Komut: start (ID: 351) (automation_worker.py:214)
2025-06-16 13:23:45,284 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 13:23:48,568 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 13:23:48,568 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 13:23:48,568 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 13:23:48,571 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 13:23:48,572 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 13:23:48,572 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 13:23:48,572 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:234)
2025-06-16 13:23:48,573 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome işlemleri temizleniyor... (scraper_thread.py:291)
2025-06-16 13:23:48,576 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 351) (automation_worker.py:150)
2025-06-16 13:23:51,084 [INFO] scraper_thread - [SCRAPER] ✅ Chrome işlemleri temizlendi (scraper_thread.py:291)
2025-06-16 13:25:24,686 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 13:25:24,686 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 13:25:24,702 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 13:25:24,702 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 13:25:24,702 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 13:25:32,748 [INFO] __main__ - 📨 Komut: start (ID: 353) (automation_worker.py:214)
2025-06-16 13:25:32,748 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 13:25:35,905 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 13:25:35,905 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 13:25:35,905 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 13:25:35,905 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 13:25:35,905 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 13:25:35,905 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 13:25:35,905 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:234)
2025-06-16 13:25:35,905 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome işlemleri temizleniyor... (scraper_thread.py:291)
2025-06-16 13:25:35,905 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 353) (automation_worker.py:150)
2025-06-16 13:25:38,077 [INFO] scraper_thread - [SCRAPER] ✅ Chrome işlemleri temizlendi (scraper_thread.py:291)
2025-06-16 13:25:40,060 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:291)
2025-06-16 13:25:51,686 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:291)
2025-06-16 13:25:55,515 [INFO] scraper_thread - [SCRAPER] tarotunsesinden | 342 (scraper_thread.py:291)
2025-06-16 13:26:10,811 [INFO] scraper_thread - [SCRAPER] reberr2101 | 0 (scraper_thread.py:291)
2025-06-16 13:26:14,828 [INFO] scraper_thread - [SCRAPER] reelsye4 | 6 (scraper_thread.py:291)
2025-06-16 13:26:18,780 [INFO] scraper_thread - [SCRAPER] sekofitness | 287 (scraper_thread.py:291)
2025-06-16 13:26:27,405 [INFO] scraper_thread - [SCRAPER] zazaokeypubg | 28 (scraper_thread.py:291)
2025-06-16 13:26:34,702 [INFO] scraper_thread - [SCRAPER] bbcberkan | 91 (scraper_thread.py:291)
2025-06-16 13:26:39,342 [INFO] scraper_thread - [SCRAPER] thisisdusuncetozu | 50 (scraper_thread.py:291)
2025-06-16 13:26:44,796 [INFO] scraper_thread - [SCRAPER] eros.pm4 | 16 (scraper_thread.py:291)
2025-06-16 13:26:49,436 [INFO] scraper_thread - [SCRAPER] yelizzz1907 | 93 (scraper_thread.py:291)
2025-06-16 13:26:53,421 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 9 benzersiz kullanıcı bulundu (scraper_thread.py:291)
2025-06-16 13:26:56,905 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome driver kapatıldı (scraper_thread.py:291)
2025-06-16 13:26:57,045 [INFO] scraper_thread - [SCRAPER] 🔄 Tüm Chrome işlemleri kapatıldı (scraper_thread.py:291)
2025-06-16 13:27:02,498 [INFO] main - 🏁 scraper tamamlandı (main.py:113)
2025-06-16 13:27:02,498 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 13:27:05,655 [INFO] main - ✅ Chrome kapatıldı (main.py:73)
2025-06-16 13:27:08,670 [INFO] main - 2️⃣ Status Checker başlatılıyor (8 kullanıcı) (main.py:179)
2025-06-16 13:29:55,702 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 13:29:55,702 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 13:29:55,702 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:148)
2025-06-16 13:29:55,702 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 13:29:55,873 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 13:31:46,090 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 13:31:46,090 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 13:31:46,103 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 13:31:46,117 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 13:31:46,118 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 13:32:23,405 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 13:32:23,405 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 13:32:23,420 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 13:32:23,436 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 13:32:23,436 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 13:32:28,952 [INFO] __main__ - 📨 Komut: start (ID: 354) (automation_worker.py:214)
2025-06-16 13:32:28,952 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 13:32:29,456 [INFO] __main__ - 📨 Komut: start (ID: 354) (automation_worker.py:214)
2025-06-16 13:32:29,457 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 13:32:32,217 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 13:32:32,217 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 13:32:32,217 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 13:32:32,217 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 13:32:32,217 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 13:32:32,217 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 13:32:32,217 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:234)
2025-06-16 13:32:32,217 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome işlemleri temizleniyor... (scraper_thread.py:333)
2025-06-16 13:32:32,217 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 354) (automation_worker.py:150)
2025-06-16 13:32:33,092 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 13:32:33,092 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 13:32:33,092 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 13:32:33,092 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 13:32:33,092 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 13:32:33,092 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 13:32:33,092 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:234)
2025-06-16 13:32:33,092 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome işlemleri temizleniyor... (scraper_thread.py:333)
2025-06-16 13:32:33,092 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 354) (automation_worker.py:150)
2025-06-16 13:32:34,420 [INFO] scraper_thread - [SCRAPER] ✅ Chrome işlemleri temizlendi (scraper_thread.py:333)
2025-06-16 13:32:35,358 [INFO] scraper_thread - [SCRAPER] ✅ Chrome işlemleri temizlendi (scraper_thread.py:333)
2025-06-16 13:32:39,525 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:333)
2025-06-16 13:32:39,526 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:333)
2025-06-16 13:32:40,251 [ERROR] scraper_thread - [SCRAPER] ❌ Chrome başlatma hatası: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
Stacktrace:
	GetHandleVerifier [0x0x7ff6555dfea5+79173]
	GetHandleVerifier [0x0x7ff6555dff00+79264]
	(No symbol) [0x0x7ff655399e5a]
	(No symbol) [0x0x7ff6553d7b85]
	(No symbol) [0x0x7ff6553d2b1d]
	(No symbol) [0x0x7ff6554267de]
	(No symbol) [0x0x7ff655425f70]
	(No symbol) [0x0x7ff655418743]
	(No symbol) [0x0x7ff6553e14c1]
	(No symbol) [0x0x7ff6553e2253]
	GetHandleVerifier [0x0x7ff6558aa2dd+3004797]
	GetHandleVerifier [0x0x7ff6558a472d+2981325]
	GetHandleVerifier [0x0x7ff6558c3380+3107360]
	GetHandleVerifier [0x0x7ff6555faa2e+188622]
	GetHandleVerifier [0x0x7ff6556022bf+219487]
	GetHandleVerifier [0x0x7ff6555e8df4+115860]
	GetHandleVerifier [0x0x7ff6555e8fa9+116297]
	GetHandleVerifier [0x0x7ff6555cf558+11256]
	BaseThreadInitThunk [0x0x7ff80e937374+20]
	RtlUserThreadStart [0x0x7ff81081cc91+33]
 (scraper_thread.py:337)
2025-06-16 13:32:45,077 [ERROR] scraper_thread - [SCRAPER] Chrome başlatılamadı! İşlem sonlandırılıyor. (scraper_thread.py:337)
2025-06-16 13:32:47,468 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:333)
2025-06-16 13:32:51,350 [INFO] main - 🏁 scraper tamamlandı (main.py:113)
2025-06-16 13:32:51,368 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 13:32:51,874 [WARNING] urllib3.connectionpool - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', ConnectionResetError(10054, 'Varolan bir bağlantı uzaktaki bir ana bilgisayar tarafından zorla kapatıldı', None, 10054, None))': /session/bf10fbdf79a4464ebc28d34b3699c70f/url (connectionpool.py:827)
2025-06-16 13:32:54,811 [INFO] main - ✅ Chrome kapatıldı (main.py:73)
2025-06-16 13:32:56,055 [WARNING] urllib3.connectionpool - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000020382643910>: Failed to establish a new connection: [WinError 10061] Hedef makine etkin olarak reddettiğinden bağlantı kurulamadı')': /session/bf10fbdf79a4464ebc28d34b3699c70f/url (connectionpool.py:827)
2025-06-16 13:32:58,273 [INFO] main - 🔄 Kullanıcı bulunamadı, scraper tekrar başlatılıyor (main.py:182)
2025-06-16 13:33:00,140 [WARNING] urllib3.connectionpool - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000203826435E0>: Failed to establish a new connection: [WinError 10061] Hedef makine etkin olarak reddettiğinden bağlantı kurulamadı')': /session/bf10fbdf79a4464ebc28d34b3699c70f/url (connectionpool.py:827)
2025-06-16 13:33:08,474 [WARNING] urllib3.connectionpool - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000203826435B0>: Failed to establish a new connection: [WinError 10061] Hedef makine etkin olarak reddettiğinden bağlantı kurulamadı')': /session/bf10fbdf79a4464ebc28d34b3699c70f/actions (connectionpool.py:827)
2025-06-16 13:33:12,176 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 13:33:12,177 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 13:33:12,177 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:148)
2025-06-16 13:33:12,178 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 13:33:12,486 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 13:33:12,564 [WARNING] urllib3.connectionpool - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000203826423E0>: Failed to establish a new connection: [WinError 10061] Hedef makine etkin olarak reddettiğinden bağlantı kurulamadı')': /session/bf10fbdf79a4464ebc28d34b3699c70f/actions (connectionpool.py:827)
2025-06-16 13:33:26,030 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 13:33:26,030 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 13:33:26,030 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:148)
2025-06-16 13:33:26,030 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 13:33:26,248 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 13:33:28,233 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 13:33:28,233 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 13:33:28,248 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 13:33:28,264 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 13:33:28,264 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 13:33:38,311 [INFO] __main__ - 📨 Komut: start (ID: 355) (automation_worker.py:214)
2025-06-16 13:33:38,998 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 13:33:42,170 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 13:33:42,170 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 13:33:42,170 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 13:33:42,170 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 13:33:42,170 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 13:33:42,170 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 13:33:42,170 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:234)
2025-06-16 13:33:42,170 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome işlemleri temizleniyor... (scraper_thread.py:333)
2025-06-16 13:33:42,170 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 355) (automation_worker.py:150)
2025-06-16 13:33:44,436 [INFO] scraper_thread - [SCRAPER] ✅ Chrome işlemleri temizlendi (scraper_thread.py:333)
2025-06-16 13:33:47,376 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:333)
2025-06-16 13:33:47,377 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:333)
2025-06-16 13:33:54,467 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:333)
2025-06-16 13:34:00,415 [INFO] scraper_thread - [SCRAPER] tarotunsesinden | 438 (scraper_thread.py:333)
2025-06-16 13:34:09,295 [INFO] scraper_thread - [SCRAPER] aboofatos | 14 (scraper_thread.py:333)
2025-06-16 13:34:13,904 [INFO] scraper_thread - [SCRAPER] erkangk01 | 149 (scraper_thread.py:333)
2025-06-16 13:34:19,702 [INFO] scraper_thread - [SCRAPER] tuncay.ahi65 | 252 (scraper_thread.py:333)
2025-06-16 13:34:25,324 [INFO] scraper_thread - [SCRAPER] ebocuk | 11 (scraper_thread.py:333)
2025-06-16 13:34:30,780 [INFO] scraper_thread - [SCRAPER] taylancelik00 | 7 (scraper_thread.py:333)
2025-06-16 13:34:35,349 [INFO] scraper_thread - [SCRAPER] havvanrerel | 31 (scraper_thread.py:333)
2025-06-16 13:34:39,280 [INFO] scraper_thread - [SCRAPER] vedatbatmaz8 | 0 (scraper_thread.py:333)
2025-06-16 13:34:44,410 [INFO] scraper_thread - [SCRAPER] halilik34 | 0 (scraper_thread.py:333)
2025-06-16 13:34:48,584 [INFO] scraper_thread - [SCRAPER] nurcanyazarr | 6 (scraper_thread.py:333)
2025-06-16 13:34:53,064 [INFO] scraper_thread - [SCRAPER] hozan.cetin73 | 34 (scraper_thread.py:333)
2025-06-16 13:34:58,986 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 11 benzersiz kullanıcı bulundu (scraper_thread.py:333)
2025-06-16 13:35:01,577 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome driver kapatıldı (scraper_thread.py:333)
2025-06-16 13:35:01,717 [INFO] scraper_thread - [SCRAPER] 🔄 Tüm Chrome işlemleri kapatıldı (scraper_thread.py:333)
2025-06-16 13:35:07,045 [INFO] main - 🏁 scraper tamamlandı (main.py:113)
2025-06-16 13:35:07,045 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 13:35:10,202 [INFO] main - ✅ Chrome kapatıldı (main.py:73)
2025-06-16 13:35:13,244 [INFO] main - 2️⃣ Status Checker başlatılıyor (9 kullanıcı) (main.py:179)
2025-06-16 13:37:54,052 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 13:37:54,052 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 13:37:54,065 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 13:37:54,110 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 13:37:54,112 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 13:37:58,201 [INFO] __main__ - 📨 Komut: start (ID: 356) (automation_worker.py:214)
2025-06-16 13:37:58,305 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 13:37:58,830 [INFO] __main__ - 📨 Komut: start (ID: 356) (automation_worker.py:214)
2025-06-16 13:37:58,831 [INFO] __main__ - ⚠️ Otomasyon zaten çalışıyor (automation_worker.py:119)
2025-06-16 13:38:01,134 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 13:38:01,479 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 13:38:01,803 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 13:38:01,821 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 13:38:01,900 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 13:38:02,028 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 13:38:02,238 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 13:38:02,304 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 13:38:02,352 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 13:38:02,352 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:289)
2025-06-16 13:38:02,448 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 356) (automation_worker.py:150)
2025-06-16 13:38:02,506 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome işlemleri temizleniyor... (scraper_thread.py:333)
2025-06-16 13:38:05,118 [INFO] scraper_thread - [SCRAPER] ✅ Chrome işlemleri temizlendi (scraper_thread.py:333)
2025-06-16 13:38:10,231 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:333)
2025-06-16 13:38:10,232 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:333)
2025-06-16 13:38:32,049 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:333)
2025-06-16 13:38:36,623 [INFO] scraper_thread - [SCRAPER] tarotunsesinden | 536 (scraper_thread.py:333)
2025-06-16 13:38:46,341 [INFO] scraper_thread - [SCRAPER] sekofitness | 264 (scraper_thread.py:333)
2025-06-16 13:38:51,412 [INFO] scraper_thread - [SCRAPER] ozgenizresmii | 31 (scraper_thread.py:333)
2025-06-16 13:38:55,633 [INFO] scraper_thread - [SCRAPER] selimay051 | 5 (scraper_thread.py:333)
2025-06-16 13:39:00,361 [INFO] scraper_thread - [SCRAPER] eceshaw | 78 (scraper_thread.py:333)
2025-06-16 13:39:06,148 [INFO] scraper_thread - [SCRAPER] alierenkoyuncu | 1 (scraper_thread.py:333)
2025-06-16 13:39:11,418 [INFO] scraper_thread - [SCRAPER] ibrahim1.234 | 1 (scraper_thread.py:333)
2025-06-16 13:39:15,647 [INFO] scraper_thread - [SCRAPER] f4mariaf4 | 10 (scraper_thread.py:333)
2025-06-16 13:39:19,998 [INFO] scraper_thread - [SCRAPER] karaca4816 | 59 (scraper_thread.py:333)
2025-06-16 13:39:25,207 [INFO] scraper_thread - [SCRAPER] simaybnimkarim | 25 (scraper_thread.py:333)
2025-06-16 13:39:30,202 [INFO] scraper_thread - [SCRAPER] pmsekerci | 26 (scraper_thread.py:333)
2025-06-16 13:39:34,438 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 11 benzersiz kullanıcı bulundu (scraper_thread.py:333)
2025-06-16 13:39:37,358 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome driver kapatıldı (scraper_thread.py:333)
2025-06-16 13:39:37,569 [INFO] scraper_thread - [SCRAPER] 🔄 Tüm Chrome işlemleri kapatıldı (scraper_thread.py:333)
2025-06-16 13:39:42,235 [INFO] main - 🏁 scraper tamamlandı (main.py:118)
2025-06-16 13:39:42,235 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 13:39:45,421 [INFO] main - ✅ Chrome kapatıldı (main.py:73)
2025-06-16 13:39:48,438 [INFO] main - ⚠️ Status Checker geçici olarak devre dışı - 9 kullanıcı 'Uygun' olarak işaretleniyor (main.py:217)
2025-06-16 13:39:48,452 [INFO] main - ✅ 9 kullanıcı 'Uygun' olarak güncellendi (main.py:228)
2025-06-16 13:39:48,488 [INFO] main - 3️⃣ Message Sender başlatılıyor (6 kullanıcı) (main.py:259)
2025-06-16 13:42:56,106 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 13:42:56,107 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 13:42:56,108 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:184)
2025-06-16 13:42:56,108 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 13:42:56,848 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 13:43:16,827 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 13:43:16,827 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 13:43:16,827 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 13:43:16,842 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 13:43:16,842 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 13:45:11,545 [INFO] __main__ - 📨 Komut: start (ID: 357) (automation_worker.py:214)
2025-06-16 13:45:12,686 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 13:45:15,889 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 13:45:15,889 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 13:45:15,890 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 13:45:15,890 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 13:45:15,890 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 13:45:15,890 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 13:45:15,890 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:270)
2025-06-16 13:45:15,890 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome işlemleri temizleniyor... (scraper_thread.py:333)
2025-06-16 13:45:15,890 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 357) (automation_worker.py:150)
2025-06-16 13:45:18,061 [INFO] scraper_thread - [SCRAPER] ✅ Chrome işlemleri temizlendi (scraper_thread.py:333)
2025-06-16 13:45:20,454 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:333)
2025-06-16 13:45:20,455 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:333)
2025-06-16 13:45:28,221 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:333)
2025-06-16 13:45:33,737 [INFO] scraper_thread - [SCRAPER] tarotunsesinden | 390 (scraper_thread.py:333)
2025-06-16 13:45:51,253 [INFO] scraper_thread - [SCRAPER] tunahanerdemm | 90 (scraper_thread.py:333)
2025-06-16 13:46:00,363 [INFO] scraper_thread - [SCRAPER] yarensahinq | 19 (scraper_thread.py:333)
2025-06-16 13:46:04,297 [INFO] scraper_thread - [SCRAPER] selimay051 | 6 (scraper_thread.py:333)
2025-06-16 13:46:08,020 [INFO] scraper_thread - [SCRAPER] hozan.cetin73 | 30 (scraper_thread.py:333)
2025-06-16 13:46:13,653 [INFO] scraper_thread - [SCRAPER] blackdiamondorjj | 11 (scraper_thread.py:333)
2025-06-16 13:46:17,738 [INFO] scraper_thread - [SCRAPER] eceshaw | 60 (scraper_thread.py:333)
2025-06-16 13:46:21,977 [INFO] scraper_thread - [SCRAPER] fuatustaa | 19 (scraper_thread.py:333)
2025-06-16 13:46:27,429 [INFO] scraper_thread - [SCRAPER] usering4333800.0 | 41 (scraper_thread.py:333)
2025-06-16 13:46:33,065 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 9 benzersiz kullanıcı bulundu (scraper_thread.py:333)
2025-06-16 13:46:35,827 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome driver kapatıldı (scraper_thread.py:333)
2025-06-16 13:46:35,967 [INFO] scraper_thread - [SCRAPER] 🔄 Tüm Chrome işlemleri kapatıldı (scraper_thread.py:333)
2025-06-16 13:46:41,483 [INFO] main - 🏁 scraper tamamlandı (main.py:118)
2025-06-16 13:46:41,483 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 13:46:44,639 [INFO] main - ✅ Chrome kapatıldı (main.py:73)
2025-06-16 13:46:47,655 [INFO] main - 2️⃣ Status Checker başlatılıyor (8 kullanıcı) (main.py:215)
2025-06-16 13:58:33,010 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 13:58:33,011 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 13:58:33,012 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:184)
2025-06-16 13:58:33,012 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 13:58:36,192 [INFO] main - ✅ Chrome kapatıldı (main.py:73)
2025-06-16 13:59:48,651 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 13:59:48,652 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 13:59:48,664 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 13:59:48,678 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 13:59:48,678 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 14:00:23,881 [INFO] __main__ - 📨 Komut: start (ID: 358) (automation_worker.py:214)
2025-06-16 14:00:23,986 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 14:00:27,252 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 14:00:27,253 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 14:00:27,253 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 14:00:27,256 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 14:00:27,256 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 14:00:27,257 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 14:00:27,257 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:270)
2025-06-16 14:00:27,258 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome işlemleri temizleniyor... (scraper_thread.py:333)
2025-06-16 14:00:27,262 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 358) (automation_worker.py:150)
2025-06-16 14:00:29,491 [INFO] scraper_thread - [SCRAPER] ✅ Chrome işlemleri temizlendi (scraper_thread.py:333)
2025-06-16 14:00:34,736 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:333)
2025-06-16 14:00:34,737 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:333)
2025-06-16 14:00:37,726 [ERROR] scraper_thread - [SCRAPER] Scraper döngüsünde hata: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: Unable to receive message from renderer
  (Session info: chrome=137.0.7151.68)
Stacktrace:
	GetHandleVerifier [0x0x7ff6555dfea5+79173]
	GetHandleVerifier [0x0x7ff6555dff00+79264]
	(No symbol) [0x0x7ff655399e5a]
	(No symbol) [0x0x7ff6553872dc]
	(No symbol) [0x0x7ff655386fca]
	(No symbol) [0x0x7ff655384b9f]
	(No symbol) [0x0x7ff6553855ff]
	(No symbol) [0x0x7ff6553942ae]
	(No symbol) [0x0x7ff6553aa671]
	(No symbol) [0x0x7ff6553b17ba]
	(No symbol) [0x0x7ff655385d9d]
	(No symbol) [0x0x7ff6553a9e61]
	(No symbol) [0x0x7ff655441384]
	(No symbol) [0x0x7ff655418743]
	(No symbol) [0x0x7ff6553e14c1]
	(No symbol) [0x0x7ff6553e2253]
	GetHandleVerifier [0x0x7ff6558aa2dd+3004797]
	GetHandleVerifier [0x0x7ff6558a472d+2981325]
	GetHandleVerifier [0x0x7ff6558c3380+3107360]
	GetHandleVerifier [0x0x7ff6555faa2e+188622]
	GetHandleVerifier [0x0x7ff6556022bf+219487]
	GetHandleVerifier [0x0x7ff6555e8df4+115860]
	GetHandleVerifier [0x0x7ff6555e8fa9+116297]
	GetHandleVerifier [0x0x7ff6555cf558+11256]
	BaseThreadInitThunk [0x0x7ff80e937374+20]
	RtlUserThreadStart [0x0x7ff81081cc91+33]
 (scraper_thread.py:337)
2025-06-16 14:00:37,731 [WARNING] scraper_thread - [SCRAPER] Chrome session kaybedildi, yeniden başlatma deneniyor... (scraper_thread.py:335)
2025-06-16 14:00:59,308 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:333)
2025-06-16 14:00:59,363 [INFO] scraper_thread - [SCRAPER] Chrome yeniden başlatıldı (scraper_thread.py:333)
2025-06-16 14:01:08,918 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 14:01:09,008 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 14:01:09,051 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 14:01:09,077 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 14:01:09,165 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 14:01:25,280 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 0 benzersiz kullanıcı bulundu (scraper_thread.py:333)
2025-06-16 14:01:28,077 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome driver kapatıldı (scraper_thread.py:333)
2025-06-16 14:01:28,243 [INFO] scraper_thread - [SCRAPER] 🔄 Tüm Chrome işlemleri kapatıldı (scraper_thread.py:333)
2025-06-16 14:01:33,533 [INFO] main - 🏁 scraper tamamlandı (main.py:118)
2025-06-16 14:01:33,533 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 14:01:36,708 [INFO] main - ✅ Chrome kapatıldı (main.py:73)
2025-06-16 14:01:39,717 [INFO] main - 🔄 Kullanıcı bulunamadı, scraper tekrar başlatılıyor (main.py:218)
2025-06-16 14:02:05,929 [INFO] __main__ - 📨 Komut: start (ID: 359) (automation_worker.py:214)
2025-06-16 14:02:05,983 [INFO] __main__ - ⚠️ Otomasyon zaten çalışıyor (automation_worker.py:119)
2025-06-16 14:02:23,829 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 14:02:23,830 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 14:02:23,840 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 14:02:23,861 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 14:02:23,861 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 14:04:03,130 [INFO] __main__ - ✅ Import'lar başarılı (test_status_checker.py:25)
2025-06-16 14:04:03,131 [INFO] __main__ - ✅ DatabaseManager oluşturuldu (test_status_checker.py:29)
2025-06-16 14:04:03,131 [INFO] __main__ - 🔧 StatusCheckerThread oluşturuluyor... (2 kullanıcı) (test_status_checker.py:37)
2025-06-16 14:04:03,132 [INFO] __main__ - ✅ StatusCheckerThread oluşturuldu (test_status_checker.py:46)
2025-06-16 14:04:03,132 [INFO] __main__ - 🚀 StatusCheckerThread başlatılıyor... (test_status_checker.py:47)
2025-06-16 14:04:03,132 [INFO] __main__ - ✅ StatusCheckerThread başlatıldı (test_status_checker.py:51)
2025-06-16 14:04:03,132 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread başlatıldı (status_checker.py:85)
2025-06-16 14:04:03,133 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 2 (status_checker.py:85)
2025-06-16 14:04:03,133 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome işlemleri temizleniyor... (status_checker.py:85)
2025-06-16 14:04:08,202 [INFO] __main__ - ✅ Test tamamlandı (test_status_checker.py:57)
2025-06-16 14:04:20,231 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 14:04:20,233 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 14:04:20,245 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 14:04:20,267 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 14:04:20,268 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 14:04:52,099 [INFO] __main__ - 📨 Komut: start (ID: 360) (automation_worker.py:214)
2025-06-16 14:04:52,100 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 14:04:52,473 [INFO] __main__ - 📨 Komut: start (ID: 360) (automation_worker.py:214)
2025-06-16 14:04:52,475 [INFO] __main__ - ⚠️ Otomasyon zaten çalışıyor (automation_worker.py:119)
2025-06-16 14:04:55,283 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 14:04:55,283 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 14:04:55,283 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 14:04:55,286 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 14:04:55,287 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 14:04:55,287 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 14:04:55,287 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:270)
2025-06-16 14:04:55,288 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome işlemleri temizleniyor... (scraper_thread.py:333)
2025-06-16 14:04:55,291 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 360) (automation_worker.py:150)
2025-06-16 14:04:57,465 [INFO] scraper_thread - [SCRAPER] ✅ Chrome işlemleri temizlendi (scraper_thread.py:333)
2025-06-16 14:05:03,851 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:333)
2025-06-16 14:05:03,853 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:333)
2025-06-16 14:05:17,925 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:333)
2025-06-16 14:05:22,809 [INFO] scraper_thread - [SCRAPER] tarotunsesinden | 422 (scraper_thread.py:333)
2025-06-16 14:05:32,558 [INFO] scraper_thread - [SCRAPER] nazzlnurr | 64 (scraper_thread.py:333)
2025-06-16 14:05:37,398 [INFO] scraper_thread - [SCRAPER] elinags1905 | 14 (scraper_thread.py:333)
2025-06-16 14:05:43,423 [INFO] scraper_thread - [SCRAPER] clcgunes_ | 31 (scraper_thread.py:333)
2025-06-16 14:05:48,467 [INFO] scraper_thread - [SCRAPER] __mehmet___7 | 224 (scraper_thread.py:333)
2025-06-16 14:05:54,838 [INFO] scraper_thread - [SCRAPER] baharkupsi_ | 20 (scraper_thread.py:333)
2025-06-16 14:06:00,667 [INFO] scraper_thread - [SCRAPER] halimeyy14 | 7 (scraper_thread.py:333)
2025-06-16 14:06:23,858 [INFO] main - ✅ Otomasyon durduruldu (main.py:196)
2025-06-16 14:06:25,860 [INFO] __main__ - ✅ Otomasyon temizlendi (automation_worker.py:97)
2025-06-16 14:06:38,546 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 14:06:41,874 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 14:06:41,949 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 14:07:00,702 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 14:07:00,702 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 14:07:00,717 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 14:07:00,733 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 14:07:00,733 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 14:07:12,811 [INFO] __main__ - 📨 Komut: start (ID: 361) (automation_worker.py:214)
2025-06-16 14:07:14,561 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 14:07:17,717 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 14:07:17,717 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 14:07:17,717 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 14:07:17,717 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 14:07:17,717 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 14:07:17,717 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 14:07:17,717 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:270)
2025-06-16 14:07:17,717 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome işlemleri temizleniyor... (scraper_thread.py:333)
2025-06-16 14:07:17,717 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 361) (automation_worker.py:150)
2025-06-16 14:07:19,896 [INFO] scraper_thread - [SCRAPER] ✅ Chrome işlemleri temizlendi (scraper_thread.py:333)
2025-06-16 14:07:46,962 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:333)
2025-06-16 14:07:46,963 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:333)
2025-06-16 14:07:54,655 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:333)
2025-06-16 14:07:58,994 [INFO] scraper_thread - [SCRAPER] tarotunsesinden | 390 (scraper_thread.py:333)
2025-06-16 14:08:07,873 [INFO] scraper_thread - [SCRAPER] sinem6534 | 306 (scraper_thread.py:333)
2025-06-16 14:08:13,756 [INFO] scraper_thread - [SCRAPER] tuncay.ahi65 | 342 (scraper_thread.py:333)
2025-06-16 14:08:17,730 [INFO] scraper_thread - [SCRAPER] veliyldrm_42 | 7 (scraper_thread.py:333)
2025-06-16 14:08:22,892 [INFO] scraper_thread - [SCRAPER] halilik34 | 0 (scraper_thread.py:333)
2025-06-16 14:08:27,185 [INFO] scraper_thread - [SCRAPER] a_mer_.99 | 6 (scraper_thread.py:333)
2025-06-16 14:08:32,574 [INFO] scraper_thread - [SCRAPER] cerkeshostes | 9 (scraper_thread.py:333)
2025-06-16 14:08:36,801 [INFO] scraper_thread - [SCRAPER] ertanisbilirmusic | 11 (scraper_thread.py:333)
2025-06-16 14:08:40,959 [INFO] scraper_thread - [SCRAPER] vedatbatmaz8 | 1 (scraper_thread.py:333)
2025-06-16 14:08:45,731 [INFO] scraper_thread - [SCRAPER] emirhan.turhan6 | 0 (scraper_thread.py:333)
2025-06-16 14:08:49,890 [INFO] scraper_thread - [SCRAPER] f4mariaf4 | 7 (scraper_thread.py:333)
2025-06-16 14:08:55,272 [INFO] scraper_thread - [SCRAPER] emirhannq1 | 1 (scraper_thread.py:333)
2025-06-16 14:08:55,274 [INFO] scraper_thread - [SCRAPER] Süre doldu (1.0 dk) (scraper_thread.py:333)
2025-06-16 14:08:55,274 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 12 benzersiz kullanıcı bulundu (scraper_thread.py:333)
2025-06-16 14:08:57,873 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome driver kapatıldı (scraper_thread.py:333)
2025-06-16 14:08:58,014 [INFO] scraper_thread - [SCRAPER] 🔄 Tüm Chrome işlemleri kapatıldı (scraper_thread.py:333)
2025-06-16 14:09:02,545 [INFO] main - 🏁 scraper tamamlandı (main.py:118)
2025-06-16 14:09:02,545 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 14:09:05,702 [INFO] main - ✅ Chrome kapatıldı (main.py:73)
2025-06-16 14:09:08,717 [INFO] main - 2️⃣ Status Checker başlatılıyor (10 kullanıcı) (main.py:215)
2025-06-16 14:13:02,056 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 14:13:02,059 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 14:13:02,108 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 14:13:02,122 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 14:13:02,122 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 14:13:02,346 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 14:13:02,346 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 14:13:02,347 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:184)
2025-06-16 14:13:02,347 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 14:13:02,543 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
