<?php
require_once 'config.php';
require_once 'functions.php';

// Veritabanı bağlantısı
try {
    $db = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        array(
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        )
    );
} catch (PDOException $e) {
    die("Veritabanı bağlantı hatası: " . $e->getMessage());
}

// Erkan Ünlü'nün yetkilerini güncelle
$permissions = array(
    'dashboard' => true,
    'publishers' => true,
    'tasks' => true,
    'events' => array(
        'view' => true,
        'manage' => true,
        'ai_advisor' => true,
        'pk_matcher' => true
    ),
    'ai_advisor' => true,
    'performance' => true,
    'tournament' => true,
    'users' => array(
        'view' => true,
        'manage' => true
    ),
    'akademi' => array(
        'view' => true,
        'manage' => true,
        'dashboard' => true,
        'duyurular' => true,
        'egitimler' => true,
        'destek' => true,
        'kullanicilar' => true,
        'ai' => true,
        'ayarlar' => true
    ),
    'site_yonetimi' => array(
        'view' => true,
        'manage' => true,
        'anasayfa' => true,
        'yayincilar' => true,
        'basvurular' => true,
        'iletisim' => true,
        'geriarama' => true,
        'toplanti' => true,
        'sms' => true,
        'blog' => true,
        'ayarlar' => true
    ),
    'etsy_operasyonu' => array(
        'view' => true,
        'manage' => true,
        'dashboard' => true,
        'tasarim' => true,
        'urunler' => true,
        'ayarlar' => true
    ),
    'whatsapp' => array(
        'view' => true,
        'manage' => true
    ),
    'yayinci_kesfi' => array(
        'view' => true,
        'manage' => true
    )
);

try {
    // Kullanıcıyı bul
    $stmt = $db->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $user = $stmt->fetch();

    if (!$user) {
        die("Kullanıcı bulunamadı");
    }

    // Yetkileri güncelle
    $stmt = $db->prepare("UPDATE users SET permissions = ? WHERE id = ?");
    $stmt->execute([json_encode($permissions), $user['id']]);

    echo "Yetkiler başarıyla güncellendi";
} catch (PDOException $e) {
    die("Yetki güncelleme hatası: " . $e->getMessage());
} 