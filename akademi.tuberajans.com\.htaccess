AddHandler application/x-httpd-php .php

<IfModule mod_mime.c>
    AddType application/x-httpd-php .php
</IfModule>

<IfModule mod_headers.c>
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Methods "GET, POST, OPTIONS, PUT, DELETE"
    Header set Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization"
</IfModule>

# Enable rewriting
RewriteEngine On

# Base directory
RewriteBase /

# Redirect old API paths to new paths
RewriteRule ^api_data\.php$ /backend/api/api_data.php [L]
RewriteRule ^api_support\.php$ /backend/api/api_support.php [L]

# Handle React Router paths
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.html [QSA,L]
