<?php
// AI tablolarını sıfırla ve yeniden oluştur
require_once __DIR__ . '/../config/config.php';

try {
    // Mevcut AI tablolarını sil
    $db_akademi->exec("DROP TABLE IF EXISTS ai_conversations");
    $db_akademi->exec("DROP TABLE IF EXISTS ai_training_documents");
    $db_akademi->exec("DROP TABLE IF EXISTS ai_settings");
    $db_akademi->exec("DROP TABLE IF EXISTS ai_models");

    echo "Eski tablolar silindi.\n";

    // AI Modelleri tablosu
    $db_akademi->exec("CREATE TABLE ai_models (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        provider VARCHAR(50) NOT NULL,
        model_id VARCHAR(100) NOT NULL,
        display_name VARCHAR(150) NOT NULL,
        description TEXT,
        max_tokens INT DEFAULT 4000,
        cost_per_1k_tokens DECIMAL(10,6) DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        is_default BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");

    // AI Ayarları tablosu
    $db_akademi->exec("CREATE TABLE ai_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) NOT NULL UNIQUE,
        setting_value TEXT,
        description TEXT,
        is_encrypted BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");

    // AI Konuşmaları tablosu
    $db_akademi->exec("CREATE TABLE ai_conversations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        session_id VARCHAR(100),
        model_id INT,
        prompt TEXT NOT NULL,
        response TEXT,
        tokens_used INT DEFAULT 0,
        response_time DECIMAL(5,3) DEFAULT 0,
        status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
        error_message TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (model_id) REFERENCES ai_models(id) ON DELETE SET NULL
    )");

    // AI Eğitim Dokümanları tablosu
    $db_akademi->exec("CREATE TABLE ai_training_documents (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        category VARCHAR(100),
        content LONGTEXT,
        file_path VARCHAR(500),
        file_type VARCHAR(50),
        file_size INT,
        status ENUM('active', 'inactive', 'processing') DEFAULT 'active',
        uploaded_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");

    echo "Yeni tablolar oluşturuldu.\n";

    // Varsayılan AI modellerini ekle (sadece GPT-4 Turbo aktif)
    $models = [
        ['GPT-4 Turbo', 'openai', 'gpt-4-turbo-preview', 'GPT-4 Turbo', 'En gelişmiş OpenAI modeli, yüksek performans', 128000, 0.01, 1, 1],
        ['GPT-4', 'openai', 'gpt-4', 'GPT-4', 'OpenAI GPT-4 modeli, güvenilir performans', 8192, 0.03, 1, 0],
        ['GPT-3.5 Turbo', 'openai', 'gpt-3.5-turbo', 'GPT-3.5 Turbo', 'Hızlı ve ekonomik OpenAI modeli', 16385, 0.001, 1, 0],
        ['Claude 3 Sonnet', 'anthropic', 'claude-3-sonnet-20240229', 'Claude 3 Sonnet', 'Anthropic Claude 3 Sonnet, dengeli performans', 200000, 0.003, 1, 0],
        ['Claude 3 Haiku', 'anthropic', 'claude-3-haiku-20240307', 'Claude 3 Haiku', 'Hızlı ve ekonomik Anthropic modeli', 200000, 0.00025, 1, 0],
        ['Gemini Pro', 'google', 'gemini-pro', 'Gemini Pro', 'Google Gemini Pro modeli', 32768, 0.0005, 1, 0]
    ];

    $stmt = $db_akademi->prepare("INSERT INTO ai_models (name, provider, model_id, display_name, description, max_tokens, cost_per_1k_tokens, is_active, is_default) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");

    foreach ($models as $model) {
        $stmt->execute($model);
    }

    echo "AI modelleri eklendi.\n";

    // Varsayılan ayarları ekle
    $settings = [
        ['openai_api_key', '', 'OpenAI API anahtarı', 1],
        ['anthropic_api_key', '', 'Anthropic API anahtarı', 1],
        ['google_api_key', '', 'Google AI API anahtarı', 1],
        ['default_model_id', '1', 'Varsayılan AI modeli ID', 0],
        ['max_conversation_history', '10', 'Maksimum konuşma geçmişi sayısı', 0],
        ['system_prompt', 'Sen Tuber Akademi\'nin AI asistanısın. Kullanıcılara TikTok içerik üretimi, canlı yayın stratejileri ve sosyal medya pazarlama konularında yardım ediyorsun. Türkçe yanıt ver ve profesyonel bir dil kullan.', 'Sistem prompt metni', 0]
    ];

    $stmt = $db_akademi->prepare("INSERT INTO ai_settings (setting_key, setting_value, description, is_encrypted) VALUES (?, ?, ?, ?)");

    foreach ($settings as $setting) {
        $stmt->execute($setting);
    }

    echo "AI ayarları eklendi.\n";
    echo "AI tabloları başarıyla sıfırlandı ve yeniden oluşturuldu!\n";

} catch (Exception $e) {
    echo "Hata: " . $e->getMessage() . "\n";
}
?>
