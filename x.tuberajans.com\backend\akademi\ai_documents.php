<?php
header('Content-Type: application/json; charset=utf-8');
require_once __DIR__ . '/../config/config.php';
$pdo = $db_akademi;
$method = $_SERVER['REQUEST_METHOD'];
if ($method === 'GET') {
    $stmt = $pdo->query("SELECT * FROM ai_training_documents ORDER BY created_at DESC");
    $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo json_encode(['success' => true, 'data' => $data]);
    exit;
}
if ($method === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    $stmt = $pdo->prepare("INSERT INTO ai_training_documents (title, content, category, status, created_at) VALUES (?, ?, ?, ?, NOW())");
    $stmt->execute([
        $input['title'],
        $input['content'],
        $input['category'],
        $input['status'] ?? 'active'
    ]);
    echo json_encode(['success' => true]);
    exit;
}
if ($method === 'DELETE') {
    $id = $_GET['id'] ?? 0;
    $stmt = $pdo->prepare("DELETE FROM ai_training_documents WHERE id = ?");
    $stmt->execute([$id]);
    echo json_encode(['success' => true]);
    exit;
}
echo json_encode(['success' => false, 'message' => 'Geçersiz istek']); 