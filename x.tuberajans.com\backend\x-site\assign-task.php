<?php
require_once __DIR__ . '/../config/config.php';

// POST isteği kontrolü
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendJsonResponse(['error' => 'Geçersiz istek metodu'], 405);
    exit;
}

// JSON verisini al
$data = json_decode(file_get_contents('php://input'), true);

if (!isset($data['assignments']) || !is_array($data['assignments'])) {
    sendJsonResponse(['error' => 'Geçerli görev atamaları gerekli'], 400);
    exit;
}

try {
    // Toplu görev ataması yap
    $stmt = $pdo->prepare("
        INSERT INTO task_assignments 
        (gorev_id, kullanici_adi, hafta_baslangici, hafta_bitisi, durumu, atanma_tarihi, tamamlanma_tarihi)
        VALUES 
        (:gorev_id, :kullanici_adi, :hafta_baslangici, :hafta_bitisi, :durumu, :atanma_tarihi, :tamamlanma_tarihi)
    ");

    $successfulAssignments = [];
    $failedAssignments = [];

    foreach ($data['assignments'] as $assignment) {
        try {
            $stmt->execute([
                'gorev_id' => $assignment['gorev_id'],
                'kullanici_adi' => $assignment['kullanici_adi'],
                'hafta_baslangici' => $assignment['hafta_baslangici'],
                'hafta_bitisi' => $assignment['hafta_bitisi'],
                'durumu' => $assignment['durumu'] ?? 'açık',
                'atanma_tarihi' => $assignment['atanma_tarihi'] ?? date('Y-m-d'),
                'tamamlanma_tarihi' => $assignment['tamamlanma_tarihi'] ?? null
            ]);
            $successfulAssignments[] = $assignment;
        } catch (PDOException $e) {
            $failedAssignments[] = [
                'assignment' => $assignment,
                'error' => $e->getMessage()
            ];
        }
    }

    sendJsonResponse([
        'message' => 'Görev atamaları tamamlandı',
        'successful' => $successfulAssignments,
        'failed' => $failedAssignments
    ]);

} catch (PDOException $e) {
    error_log('Görev atama hatası: ' . $e->getMessage());
    sendJsonResponse(['error' => 'Görevler atanırken bir hata oluştu'], 500);
} 