<?php
// Update Followers API
ini_set('display_errors', 0);
error_reporting(E_ALL);
require_once __DIR__ . '/../config/config.php';
jsonResponse(['status' => 'init'], 200); // ensure JSON functions loaded

// For long-running operations
set_time_limit(0);

/**
 * TikTok'tan tam takipçi sayısını çeken gelişmiş fonksiyon
 */
function getTikTokExactFollowerCount($username) {
    $username = ltrim($username, '@');
    $url = "https://www.tiktok.com/@" . $username;
    
    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        CURLOPT_TIMEOUT => 15,
        CURLOPT_SSL_VERIFYHOST => 0,
        CURLOPT_SSL_VERIFYPEER => 0,
        CURLOPT_HTTPHEADER => [
            'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language: en-US,en;q=0.9',
            'Cache-Control: no-cache',
            'Pragma: no-cache'
        ]
    ]);
    
    $html = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    
    if ($httpCode == 200) {
        // __UNIVERSAL_DATA_FOR_REHYDRATION__ scriptini bul
        if (preg_match('/<script id="__UNIVERSAL_DATA_FOR_REHYDRATION__"[^>]*>(.*?)<\/script>/s', $html, $universalMatch)) {
            $universalData = $universalMatch[1];
            // JSON verisini decode et ve doğru path'i takip et
            $jsonData = json_decode($universalData, true);
            
            if ($jsonData) {
                // webapp.user-detail içindeki userInfo'ya eriş
                $userInfo = $jsonData['__DEFAULT_SCOPE__']['webapp.user-detail']['userInfo'] ?? null;
                if ($userInfo && isset($userInfo['stats']['followerCount'])) {
                    $exactCount = (int)$userInfo['stats']['followerCount'];
                    return [
                        'success' => true,
                        'exact_count' => $exactCount,
                        'pattern_used' => 'UNIVERSAL_DATA_JSON_PARSE'
                    ];
                }
            }
        }
        
        // Yedek yöntem: direkt regex ile followerCount bul
        if (preg_match('/"followerCount":(\d+)/', $html, $countMatch)) {
            $exactCount = (int)$countMatch[1];
            return [
                'success' => true,
                'exact_count' => $exactCount,
                'pattern_used' => 'DIRECT_REGEX'
            ];
        }
    }
    
    return [
        'success' => false,
        'error' => 'HTTP ' . $httpCode . ' - Takipçi sayısı bulunamadı'
    ];
}

try {
    // Fetch all influencer usernames
    $stmt = $db->query("SELECT id, username FROM influencer_info");
    $list = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $updated = 0;
    $failed = 0;
    $results = [];
    
    foreach ($list as $row) {
        $result = getTikTokExactFollowerCount($row['username']);
        
        if ($result['success']) {
            $exactCount = $result['exact_count'];
            $uStmt = $db->prepare("UPDATE influencer_info SET followers = ? WHERE id = ?");
            $uStmt->execute([$exactCount, $row['id']]);
            $updated++;
            
            $results[] = [
                'id' => $row['id'],
                'username' => $row['username'],
                'followers' => $exactCount,
                'status' => 'success',
                'pattern_used' => $result['pattern_used']
            ];
        } else {
            $failed++;
            
            $results[] = [
                'id' => $row['id'],
                'username' => $row['username'],
                'status' => 'error',
                'error' => $result['error']
            ];
        }
        
        // Rate limiting (her istek arası 0.75 saniye)
        usleep(750000);
    }
    
    jsonResponse([
        'success' => true, 
        'updatedCount' => $updated, 
        'failedCount' => $failed,
        'results' => $results
    ]);
} catch (Exception $e) {
    error_log("Update Followers API error: " . $e->getMessage());
    jsonResponse(['success' => false, 'error' => $e->getMessage()], 500);
}
?> 