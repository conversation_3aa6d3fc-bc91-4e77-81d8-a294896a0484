import React, { useState } from 'react';
import { Input, <PERSON><PERSON>, Card, message as antMessage, Table, Tooltip, Typography, Modal, Radio, Switch } from 'antd';
import { DeleteOutlined, SendOutlined, ImportOutlined, ClearOutlined, RobotOutlined } from '@ant-design/icons';
import { TikTokOutlined } from '../components/icons/TikTokOutlined';
import axios from 'axios';
import { API_BASE_URL } from '../config';

const { TextArea } = Input;
const { Text } = Typography;

interface UserData {
  key: string;
  username: string;
  userId?: string;
  status: 'pending' | 'success' | 'error';
  error?: string;
  messageStatus?: 'pending' | 'sent' | 'error';
}

export const TikTok: React.FC = () => {
  const [username, setUsername] = useState('');
  const [messageText, setMessageText] = useState('');
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState<UserData[]>([]);
  const [isImportModalVisible, setIsImportModalVisible] = useState(false);
  const [bulkUsernames, setBulkUsernames] = useState('');
  const [importType, setImportType] = useState<'newline' | 'comma'>('newline');
  const [autoSendMessage, setAutoSendMessage] = useState(false);
  const [messageDelay, setMessageDelay] = useState(2000); // 2 saniye varsayılan bekleme

  const addUser = () => {
    if (!username) {
      antMessage.error('Lütfen bir kullanıcı adı girin');
      return;
    }

    const cleanUsername = username.replace('@', '').trim();
    
    // Kullanıcı zaten ekli mi kontrol et
    if (users.some(user => user.username === cleanUsername)) {
      antMessage.error('Bu kullanıcı zaten eklenmiş');
      return;
    }

    setUsers(prev => [...prev, {
      key: Date.now().toString(),
      username: cleanUsername,
      status: 'pending'
    }]);
    setUsername('');
  };

  const handleBulkImport = () => {
    if (!bulkUsernames.trim()) {
      antMessage.error('Lütfen kullanıcı adlarını girin');
      return;
    }

    // Kullanıcı adlarını ayır
    const usernameList = importType === 'newline' 
      ? bulkUsernames.split('\n') 
      : bulkUsernames.split(',');

    // Temizle ve geçerli kullanıcı adlarını al
    const cleanUsernames = usernameList
      .map(username => username.trim().replace('@', ''))
      .filter(username => username !== '');

    // Tekrar eden kullanıcıları filtrele
    const uniqueUsernames = [...new Set(cleanUsernames)];

    // Mevcut listede olmayan kullanıcıları ekle
    const newUsers = uniqueUsernames
      .filter(username => !users.some(user => user.username === username))
      .map(username => ({
        key: Date.now().toString() + Math.random(),
        username,
        status: 'pending' as const
      }));

    if (newUsers.length === 0) {
      antMessage.warning('Eklenecek yeni kullanıcı bulunamadı');
      return;
    }

    setUsers(prev => [...prev, ...newUsers]);
    antMessage.success(`${newUsers.length} kullanıcı başarıyla eklendi`);
    setIsImportModalVisible(false);
    setBulkUsernames('');
  };

  const removeUser = (key: string) => {
    setUsers(prev => prev.filter(user => user.key !== key));
  };

  const clearAllUsers = () => {
    Modal.confirm({
      title: 'Tüm Kullanıcıları Sil',
      content: 'Tüm kullanıcıları silmek istediğinizden emin misiniz?',
      okText: 'Evet',
      cancelText: 'Hayır',
      onOk: () => {
        setUsers([]);
        antMessage.success('Tüm kullanıcılar silindi');
      }
    });
  };

  const sendMessageWithPuppeteer = async (userId: string, message: string) => {
    try {
      // Business Suite API URL'i
      const url = `https://www.tiktok.com/business-suite/messages?from=homepage&lang=tr-TR&u=${userId}`;
      
      // Yeni sekmede aç (manuel kontrol için)
      window.open(url, '_blank');

      try {
        // API isteği için gerekli headers
        const headers = {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Origin': 'https://www.tiktok.com',
          'Referer': url
        };

        // API endpoint'i
        const apiUrl = 'https://www.tiktok.com/api/business-messaging/api/send';

        // API isteği için payload
        const payload = {
          message_type: 'text',
          content: {
            text: message
          },
          recipient_id: userId,
          business_id: userId, // Business ID gerekebilir
          conversation_type: 'one_on_one'
        };

        // API isteği gönder
        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: headers,
          body: JSON.stringify(payload),
          credentials: 'include' // Cookie'leri gönder
        });

        if (response.ok) {
          console.log('Mesaj başarıyla gönderildi!');
          return true;
        } else {
          // API yanıtını kontrol et
          const errorData = await response.json();
          console.error('API Hatası:', errorData);
          
          // Kullanıcıya bilgi ver
          antMessage.error('Mesaj otomatik gönderilemedi. Lütfen açılan sekmeden manuel olarak gönderiniz.');
          return false;
        }
      } catch (error) {
        console.error('API Hatası:', error);
        // Kullanıcıya bilgi ver
        antMessage.error('Mesaj otomatik gönderilemedi. Lütfen açılan sekmeden manuel olarak gönderiniz.');
        return false;
      }
    } catch (error) {
      console.error('Mesaj gönderme hatası:', error);
      return false;
    }
  };

  const handleStartClick = async () => {
    if (users.length === 0) {
      antMessage.error('Lütfen en az bir kullanıcı ekleyin');
      return;
    }

    if (!messageText.trim()) {
      antMessage.error('Lütfen gönderilecek mesajı yazın');
      return;
    }

    setLoading(true);
    const updatedUsers = [...users];

    for (let i = 0; i < updatedUsers.length; i++) {
      const user = updatedUsers[i];
      try {
        // Kullanıcı ID'sini API'den al
        const response = await axios.post(`${API_BASE_URL}/api/tiktok_user.php`, {
          username: user.username
        });

        const userId = response.data.userId;
        updatedUsers[i] = {
          ...user,
          userId,
          status: 'success'
        };

        if (autoSendMessage) {
          // Her kullanıcı için yeni deneme
          let retryCount = 0;
          let messageSent = false;

          while (retryCount < 3 && !messageSent) {
            messageSent = await sendMessageWithPuppeteer(userId, messageText);
            if (!messageSent) {
              retryCount++;
              // Başarısız olursa 3 saniye bekle ve tekrar dene
              await new Promise(resolve => setTimeout(resolve, 3000));
            }
          }

          updatedUsers[i].messageStatus = messageSent ? 'sent' : 'error';
          
          if (messageSent) {
            antMessage.success(`${user.username} için mesaj gönderildi`);
          } else {
            antMessage.error(`${user.username} için mesaj gönderilemedi`);
          }
          
          // Bir sonraki kullanıcı için bekle
          await new Promise(resolve => setTimeout(resolve, messageDelay));
        } else {
          // Sadece mesajlaşma sayfasını yeni sekmede aç
          const url = `https://www.tiktok.com/business-suite/messages?from=homepage&lang=tr-TR&u=${userId}`;
          window.open(url, '_blank');
          antMessage.success(`${user.username} için mesajlaşma sayfası açıldı`);
        }
      } catch (error) {
        updatedUsers[i] = {
          ...user,
          status: 'error',
          error: 'Kullanıcı bulunamadı'
        };
        antMessage.error(`${user.username} için hata oluştu`);
      }
    }

    setUsers(updatedUsers);
    setLoading(false);
  };

  const columns = [
    {
      title: 'Kullanıcı Adı',
      dataIndex: 'username',
      key: 'username',
      render: (text: string) => <Text>@{text}</Text>
    },
    {
      title: 'Durum',
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: UserData) => (
        <div>
          <Text type={
            status === 'success' ? 'success' :
            status === 'error' ? 'danger' :
            undefined
          }>
            {status === 'success' ? 'Başarılı' :
             status === 'error' ? 'Hata' :
             'Bekliyor'}
          </Text>
          {record.messageStatus && (
            <Text type={
              record.messageStatus === 'sent' ? 'success' :
              record.messageStatus === 'error' ? 'danger' :
              undefined
            } className="ml-2">
              {record.messageStatus === 'sent' ? '(Mesaj Gönderildi)' :
               record.messageStatus === 'error' ? '(Mesaj Hatası)' :
               '(Mesaj Bekliyor)'}
            </Text>
          )}
        </div>
      )
    },
    {
      title: 'İşlem',
      key: 'action',
      render: (_: any, record: UserData) => (
        <Tooltip title="Kullanıcıyı Kaldır">
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => removeUser(record.key)}
          />
        </Tooltip>
      )
    }
  ];

  return (
    <div className="p-6">
      <Card 
        title={
          <div className="flex items-center gap-2">
            <TikTokOutlined />
            <span>TikTok Toplu Mesaj</span>
          </div>
        }
        className="max-w-4xl mx-auto"
      >
        <div className="space-y-6">
          <div className="flex gap-2">
            <Input
              placeholder="Kullanıcı adını girin (örn: @username)"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              onPressEnter={addUser}
              prefix={<TikTokOutlined className="text-gray-400" />}
            />
            <Button type="primary" onClick={addUser}>
              Ekle
            </Button>
            <Button 
              icon={<ImportOutlined />} 
              onClick={() => setIsImportModalVisible(true)}
              title="Toplu Kullanıcı Ekle"
            >
              Toplu Ekle
            </Button>
            <Button 
              type="text"
              danger
              icon={<ClearOutlined />} 
              onClick={clearAllUsers}
              disabled={users.length === 0}
              title="Tüm Kullanıcıları Sil"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Gönderilecek Mesaj
            </label>
            <TextArea
              rows={4}
              value={messageText}
              onChange={(e) => setMessageText(e.target.value)}
              placeholder="Gönderilecek mesajı yazın..."
            />
          </div>

          <div className="flex items-center gap-4 border-t pt-4">
            <Switch
              checked={autoSendMessage}
              onChange={setAutoSendMessage}
              checkedChildren={<RobotOutlined />}
              unCheckedChildren={<RobotOutlined />}
            />
            <Text>Mesajları Otomatik Gönder</Text>
            {autoSendMessage && (
              <div className="flex items-center gap-2">
                <Text type="secondary">Bekleme Süresi:</Text>
                <Input
                  type="number"
                  min={1000}
                  max={10000}
                  step={500}
                  value={messageDelay}
                  onChange={(e) => setMessageDelay(Number(e.target.value))}
                  style={{ width: 100 }}
                  addonAfter="ms"
                />
              </div>
            )}
          </div>

          <Table
            columns={columns}
            dataSource={users}
            pagination={false}
            className="mb-4"
          />

          <Button
            type="primary"
            icon={<SendOutlined />}
            onClick={handleStartClick}
            loading={loading}
            disabled={users.length === 0 || !messageText.trim()}
            className="w-full"
          >
            {autoSendMessage ? 'Mesajları Otomatik Gönder' : 'Mesajlaşma Sayfalarını Aç'} ({users.length} Kullanıcı)
          </Button>
        </div>
      </Card>

      <Modal
        title="Toplu Kullanıcı Ekleme"
        open={isImportModalVisible}
        onOk={handleBulkImport}
        onCancel={() => {
          setIsImportModalVisible(false);
          setBulkUsernames('');
        }}
        okText="Ekle"
        cancelText="İptal"
      >
        <div className="space-y-4">
          <Radio.Group 
            value={importType} 
            onChange={e => setImportType(e.target.value)}
            className="mb-4"
          >
            <Radio.Button value="newline">Her Satıra Bir Kullanıcı</Radio.Button>
            <Radio.Button value="comma">Virgülle Ayırarak</Radio.Button>
          </Radio.Group>

          <TextArea
            rows={10}
            value={bulkUsernames}
            onChange={(e) => setBulkUsernames(e.target.value)}
            placeholder={
              importType === 'newline' 
                ? "@username1\n@username2\n@username3" 
                : "@username1, @username2, @username3"
            }
          />
          
          <Text type="secondary">
            Not: @ işareti opsiyoneldir. Tekrar eden kullanıcılar otomatik olarak filtrelenecektir.
          </Text>
        </div>
      </Modal>
    </div>
  );
}; 