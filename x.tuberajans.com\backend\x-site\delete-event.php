<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/auth.php';

header('Content-Type: application/json; charset=utf-8');

// Yetkilendirme kontrolü
$userId = requireAuthToken();
if (!$userId) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

// ID kontrolü
$eventId = $_GET['id'] ?? null;
if (!$eventId) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Etkinlik ID gereklidir']);
    exit;
}

// DELETE metodunu destekleme
$method = $_SERVER['REQUEST_METHOD'];
if ($method !== 'DELETE') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method Not Allowed']);
    exit;
}

try {
    // Veritabanı işlemlerini transaction içinde yap
    $db->beginTransaction();
    
    // İlişkili PK eşleşmelerini sil
    $stmt = $db->prepare("DELETE FROM pk_eslesmeleri WHERE etkinlik_id = ?");
    $result1 = $stmt->execute([$eventId]);
    
    // Etkinliği sil
    $stmt = $db->prepare("DELETE FROM etkinlikler WHERE id = ?");
    $result2 = $stmt->execute([$eventId]);
    
    // Tüm silme işlemleri başarılıysa işlemi onayla
    if ($result1 && $result2) {
        $db->commit();
        echo json_encode(['success' => true, 'message' => 'Etkinlik başarıyla silindi']);
    } else {
        // Hata varsa işlemi geri al
        $db->rollBack();
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Etkinlik silinemedi']);
    }
} catch (PDOException $e) {
    // Hata durumunda işlemi geri al
    if ($db->inTransaction()) {
        $db->rollBack();
    }
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Veritabanı hatası: ' . $e->getMessage()]);
} catch (Exception $e) {
    // Genel hata durumunda işlemi geri al
    if ($db->inTransaction()) {
        $db->rollBack();
    }
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Beklenmeyen hata: ' . $e->getMessage()]);
}

exit; 