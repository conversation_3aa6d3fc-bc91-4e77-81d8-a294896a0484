<?php
/**
 * Weekly Tasks API - Haftalık Görevleri Getir
 * Belirli kullanıcı ve hafta için atanan görevleri döndürür
 */

// Headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// OPTIONS kontrolü
if (($_SERVER['REQUEST_METHOD'] ?? '') === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Config dosyalarını dahil et
require_once __DIR__ . '/../config/config.php';

// Hata logları
error_log("Weekly Tasks API çağrıldı - " . date('Y-m-d H:i:s'));

// JSON yanıt fonksiyonu
if (!function_exists('jsonResponse')) {
    function jsonResponse($data, $status = 200) {
        http_response_code($status);
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
}

try {
    $requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';
    
    if ($requestMethod === 'GET') {
    // Auth kontrolü
        if (!checkAuth()) {
            error_log("Weekly Tasks API: Auth başarısız");
            jsonResponse(['success' => false, 'error' => 'Geçersiz token'], 401);
        }
        error_log("Weekly Tasks API: Auth başarılı");

        // Parametreleri al
        $kullanici_adi = $_GET['kullanici_adi'] ?? '';
        $start_date = $_GET['start_date'] ?? '';
        $end_date = $_GET['end_date'] ?? '';

        if (empty($kullanici_adi)) {
            jsonResponse(['success' => false, 'error' => 'Kullanıcı adı gerekli'], 400);
        }

        if (empty($start_date) || empty($end_date)) {
            jsonResponse(['success' => false, 'error' => 'Başlangıç ve bitiş tarihi gerekli'], 400);
        }
            
        error_log("Weekly Tasks API: Kullanıcı: $kullanici_adi, Tarih: $start_date - $end_date");
            
        // Önce performans verilerini al
        $performanceQuery = "
            SELECT 
                canli_yayin_gunu,
                yayin_suresi,
                elmaslar,
                yeni_takipciler,
                aboneler,
                maclar
            FROM weekly_archive 
            WHERE kullanici_adi = ? 
                AND hafta_baslangici >= ? 
                AND hafta_baslangici < ?
            LIMIT 1
        ";
        
        $performanceStmt = $db->prepare($performanceQuery);
        $performanceStmt->execute([$kullanici_adi, $start_date, $end_date]);
        $performance = $performanceStmt->fetch(PDO::FETCH_ASSOC);

        // Veritabanından haftalık görevleri getir
        $query = "
            SELECT 
                id,
                kullanici_adi,
                hafta_baslangici,
                hafta_bitisi,
                gorev_onerisi,
                gorev_zorlugu,
                tamamlandi,
                puan,
                durum
            FROM weekly_tasks 
            WHERE kullanici_adi = ? 
                AND hafta_baslangici >= ? 
                AND hafta_baslangici < ?
            ORDER BY id DESC
        ";
            
            $stmt = $db->prepare($query);
        $stmt->execute([$kullanici_adi, $start_date, $end_date]);
            $tasks = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        // Performans kontrolü fonksiyonu
        function checkTaskCompletion($taskDescription, $performance) {
            if (!$performance) return false;
            
            $description = strtolower($taskDescription);
            
            // Canlı yayın günü kontrolü
            if (preg_match('/(\d+)\s*gün.*yayın/i', $taskDescription, $matches)) {
                $requiredDays = (int)$matches[1];
                return ($performance['canli_yayin_gunu'] ?? 0) >= $requiredDays;
                }
                
            // Yayın süresi kontrolü (saat)
            if (preg_match('/(\d+)\s*saat.*yayın/i', $taskDescription, $matches)) {
                $requiredHours = (int)$matches[1];
                $actualHours = ($performance['yayin_suresi'] ?? 0) / 3600;
                return $actualHours >= $requiredHours;
            }
            
            // Elmas kontrolü
            if (preg_match('/(\d+)\s*elmas/i', $taskDescription, $matches)) {
                $requiredDiamonds = (int)$matches[1];
                return ($performance['elmaslar'] ?? 0) >= $requiredDiamonds;
            }
            
            // Takipçi kontrolü
            if (preg_match('/(\d+)\s*takip[çc]i/i', $taskDescription, $matches)) {
                $requiredFollowers = (int)$matches[1];
                return ($performance['yeni_takipciler'] ?? 0) >= $requiredFollowers;
            }
            
            // PK/Maç kontrolü
            if (preg_match('/(\d+)\s*(pk|m[üu]cadele|ma[çc])/i', $taskDescription, $matches)) {
                $requiredMatches = (int)$matches[1];
                return ($performance['maclar'] ?? 0) >= $requiredMatches;
            }
            
            // Abone kontrolü
            if (preg_match('/(\d+)\s*abone/i', $taskDescription, $matches)) {
                $requiredSubscribers = (int)$matches[1];
                return ($performance['aboneler'] ?? 0) >= $requiredSubscribers;
            }
            
            // Video paylaş kontrolü (şimdilik manuel kontrol gerekiyor)
            if (preg_match('/(\d+)\s*video.*payla[şs]/i', $taskDescription, $matches)) {
                // Video sayısını weekly_archive'dan alamayız, manuel kontrol gerekir
                return false;
            }
            
            return false;
                }
                
        // Veri işleme
        $processedTasks = [];
        foreach ($tasks as $task) {
            // Performans verilerine göre tamamlanma durumunu kontrol et
            $autoCompleted = checkTaskCompletion($task['gorev_onerisi'] ?? '', $performance);
            
            // Manuel tamamlandi durumu veya otomatik kontrol sonucu
            $task['tamamlandi'] = (bool)($task['tamamlandi'] ?? 0) || $autoCompleted;
            $task['auto_completed'] = $autoCompleted; // Otomatik tamamlanma bilgisi
            
            // Otomatik tamamlanmışsa durumu güncelle
            if ($autoCompleted && ($task['durum'] ?? 'Beklemede') === 'Beklemede') {
                $task['durum'] = 'Tamamlandı';
            }
            
            // Numeric alanları integer'a çevir
            $task['id'] = (int)($task['id'] ?? 0);
            $task['puan'] = (int)($task['puan'] ?? 0);
            
            // Null değerleri düzenle
            $task['gorev_onerisi'] = $task['gorev_onerisi'] ?? '';
            $task['gorev_zorlugu'] = $task['gorev_zorlugu'] ?? 'Orta';
            $task['durum'] = $task['durum'] ?? 'Beklemede';

            $processedTasks[] = $task;
        }
            
        error_log("Weekly Tasks API: " . count($processedTasks) . " görev bulundu");
        
        // Debug: Performans ve görev detayları
        if ($performance) {
            error_log("Performans Verileri: " . json_encode($performance));
        } else {
            error_log("Bu hafta için performans verisi bulunamadı");
        }
            
            jsonResponse([
                'success' => true,
            'data' => $processedTasks,
            'count' => count($processedTasks),
            'kullanici_adi' => $kullanici_adi,
            'start_date' => $start_date,
            'end_date' => $end_date,
            'performance' => $performance,
            'debug_info' => [
                'performance_found' => (bool)$performance,
                'auto_check_enabled' => true
            ]
        ]);

    } else {
        jsonResponse(['success' => false, 'error' => 'Desteklenmeyen HTTP metodu: ' . $requestMethod], 405);
    }
            
        } catch (PDOException $e) {
    error_log("Weekly Tasks API DB Hatası: " . $e->getMessage());
    jsonResponse([
        'success' => false,
        'error' => 'Veritabanı hatası',
        'message' => 'Görevler alınırken hata oluştu'
    ], 500);
} catch (Exception $e) {
    error_log("Weekly Tasks API Genel Hatası: " . $e->getMessage());
        jsonResponse([
            'success' => false,
            'error' => 'Sunucu hatası',
            'message' => 'Geçici bir hata oluştu, lütfen tekrar deneyin'
        ], 500);
}
?>