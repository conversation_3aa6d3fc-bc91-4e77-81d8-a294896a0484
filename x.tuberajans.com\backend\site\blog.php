<?php
header('Content-Type: application/json; charset=utf-8');
require_once __DIR__ . '/../config/config.php';

$pdo = $db_takip;
$action = isset($_GET['action']) ? $_GET['action'] : 'list';

if ($action === 'list') {
    try {
        $stmt = $pdo->query('SELECT * FROM blog ORDER BY date DESC');
        $data = array_map(function($row) {
            return [
                'id' => $row['ID'],
                'title' => $row['title'],
                'slug' => $row['slug'],
                'excerpt' => $row['excerpt'],
                'content' => $row['content'],
                'date' => $row['date'],
                'thumbnail' => $row['thumbnail'],
                'meta_description' => $row['meta_description'],
                'meta_keywords' => $row['meta_keywords']
            ];
        }, $stmt->fetchAll());
        echo json_encode(['success' => true, 'data' => $data]);
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'message' => 'Veriler alınamadı', 'error' => $e->getMessage()]);
    }
    exit;
}

if ($action === 'add') {
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        echo json_encode(['success' => false, 'message' => 'Geçersiz veri']);
        exit;
    }
    try {
        $stmt = $pdo->prepare('INSERT INTO blog (title, slug, excerpt, content, date, thumbnail, meta_description, meta_keywords) VALUES (?, ?, ?, ?, ?, ?, ?, ?)');
        $stmt->execute([
            $input['title'],
            $input['slug'],
            $input['excerpt'],
            $input['content'],
            $input['date'],
            $input['thumbnail'],
            $input['meta_description'],
            $input['meta_keywords']
        ]);
        echo json_encode(['success' => true, 'message' => 'Blog eklendi']);
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'message' => 'Blog eklenemedi', 'error' => $e->getMessage()]);
    }
    exit;
}

if ($action === 'update') {
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input || !isset($input['id'])) {
        echo json_encode(['success' => false, 'message' => 'Geçersiz veri']);
        exit;
    }
    try {
        $stmt = $pdo->prepare('UPDATE blog SET title=?, slug=?, excerpt=?, content=?, date=?, thumbnail=?, meta_description=?, meta_keywords=? WHERE ID=?');
        $stmt->execute([
            $input['title'],
            $input['slug'],
            $input['excerpt'],
            $input['content'],
            $input['date'],
            $input['thumbnail'],
            $input['meta_description'],
            $input['meta_keywords'],
            $input['id']
        ]);
        echo json_encode(['success' => true, 'message' => 'Blog güncellendi']);
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'message' => 'Blog güncellenemedi', 'error' => $e->getMessage()]);
    }
    exit;
}

if ($action === 'delete') {
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    if ($id > 0) {
        try {
            $stmt = $pdo->prepare('DELETE FROM blog WHERE ID=?');
            $stmt->execute([$id]);
            echo json_encode(['success' => true, 'message' => 'Blog silindi']);
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'message' => 'Blog silinemedi', 'error' => $e->getMessage()]);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Geçersiz ID']);
    }
    exit;
}

echo json_encode(['success' => false, 'message' => 'Geçersiz action']);
exit; 