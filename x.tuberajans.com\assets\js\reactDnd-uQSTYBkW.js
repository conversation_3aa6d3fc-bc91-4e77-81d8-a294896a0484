import{e as Ue,r as p,g as qe}from"./vendor-CnpYymF8.js";import{a as re}from"./antd-BfejY-CV.js";var j={exports:{}},C={};var ne;function Fe(){if(ne)return C;ne=1;var t=Ue(),e=Symbol.for("react.element"),r=Symbol.for("react.fragment"),n=Object.prototype.hasOwnProperty,i=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,o={key:!0,ref:!0,__self:!0,__source:!0};function s(a,c,l){var f,v={},h=null,d=null;l!==void 0&&(h=""+l),c.key!==void 0&&(h=""+c.key),c.ref!==void 0&&(d=c.ref);for(f in c)n.call(c,f)&&!o.hasOwnProperty(f)&&(v[f]=c[f]);if(a&&a.defaultProps)for(f in c=a.defaultProps,c)v[f]===void 0&&(v[f]=c[f]);return{$$typeof:e,type:a,key:h,ref:d,props:v,_owner:i.current}}return C.Fragment=r,C.jsx=s,C.jsxs=s,C}var ie;function $e(){return ie||(ie=1,j.exports=Fe()),j.exports}var Ge=$e();function y(t){return"Minified Redux error #"+t+"; visit https://redux.js.org/Errors?code="+t+" for the full message or use the non-minified dev environment for full errors. "}var oe=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}(),se=function(){return Math.random().toString(36).substring(7).split("").join(".")},ae={INIT:"@@redux/INIT"+se(),REPLACE:"@@redux/REPLACE"+se()};function Be(t){if(typeof t!="object"||t===null)return!1;for(var e=t;Object.getPrototypeOf(e)!==null;)e=Object.getPrototypeOf(e);return Object.getPrototypeOf(t)===e}function ye(t,e,r){var n;if(typeof e=="function"&&typeof r=="function"||typeof r=="function"&&typeof arguments[3]=="function")throw new Error(y(0));if(typeof e=="function"&&typeof r=="undefined"&&(r=e,e=void 0),typeof r!="undefined"){if(typeof r!="function")throw new Error(y(1));return r(ye)(t,e)}if(typeof t!="function")throw new Error(y(2));var i=t,o=e,s=[],a=s,c=!1;function l(){a===s&&(a=s.slice())}function f(){if(c)throw new Error(y(3));return o}function v(g){if(typeof g!="function")throw new Error(y(4));if(c)throw new Error(y(5));var m=!0;return l(),a.push(g),function(){if(m){if(c)throw new Error(y(6));m=!1,l();var O=a.indexOf(g);a.splice(O,1),s=null}}}function h(g){if(!Be(g))throw new Error(y(7));if(typeof g.type=="undefined")throw new Error(y(8));if(c)throw new Error(y(9));try{c=!0,o=i(o,g)}finally{c=!1}for(var m=s=a,S=0;S<m.length;S++){var O=m[S];O()}return g}function d(g){if(typeof g!="function")throw new Error(y(10));i=g,h({type:ae.REPLACE})}function D(){var g,m=v;return g={subscribe:function(O){if(typeof O!="object"||O===null)throw new Error(y(11));function I(){O.next&&O.next(f())}I();var H=m(I);return{unsubscribe:H}}},g[oe]=function(){return this},g}return h({type:ae.INIT}),n={dispatch:h,subscribe:v,getState:f,replaceReducer:d},n[oe]=D,n}function ce(t,e){return function(){return e(t.apply(this,arguments))}}function Kr(t,e){if(typeof t=="function")return ce(t,e);if(typeof t!="object"||t===null)throw new Error(y(16));var r={};for(var n in t){var i=t[n];typeof i=="function"&&(r[n]=ce(i,e))}return r}function Ve(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];return e.length===0?function(n){return n}:e.length===1?e[0]:e.reduce(function(n,i){return function(){return n(i.apply(void 0,arguments))}})}function en(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];return function(n){return function(){var i=n.apply(void 0,arguments),o=function(){throw new Error(y(15))},s={getState:i.getState,dispatch:function(){return o.apply(void 0,arguments)}},a=e.map(function(c){return c(s)});return o=Ve.apply(void 0,a)(i.dispatch),re(re({},i),{},{dispatch:o})}}}const Oe=p.createContext({dragDropManager:void 0});function u(t,e,...r){if(Xe()&&e===void 0)throw new Error("invariant requires an error message argument");if(!t){let n;if(e===void 0)n=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{let i=0;n=new Error(e.replace(/%s/g,function(){return r[i++]})),n.name="Invariant Violation"}throw n.framesToPop=1,n}}function Xe(){return typeof process!="undefined"&&!0}function Ye(t,e,r){return e.split(".").reduce((n,i)=>n&&n[i]?n[i]:r||null,t)}function We(t,e){return t.filter(r=>r!==e)}function Te(t){return typeof t=="object"}function ze(t,e){const r=new Map,n=o=>{r.set(o,r.has(o)?r.get(o)+1:1)};t.forEach(n),e.forEach(n);const i=[];return r.forEach((o,s)=>{o===1&&i.push(s)}),i}function Je(t,e){return t.filter(r=>e.indexOf(r)>-1)}const z="dnd-core/INIT_COORDS",x="dnd-core/BEGIN_DRAG",J="dnd-core/PUBLISH_DRAG_SOURCE",M="dnd-core/HOVER",_="dnd-core/DROP",L="dnd-core/END_DRAG";function ue(t,e){return{type:z,payload:{sourceClientOffset:e||null,clientOffset:t||null}}}const Qe={type:z,payload:{clientOffset:null,sourceClientOffset:null}};function Ze(t){return function(r=[],n={publishSource:!0}){const{publishSource:i=!0,clientOffset:o,getSourceClientOffset:s}=n,a=t.getMonitor(),c=t.getRegistry();t.dispatch(ue(o)),Ke(r,a,c);const l=rt(r,a);if(l==null){t.dispatch(Qe);return}let f=null;if(o){if(!s)throw new Error("getSourceClientOffset must be defined");et(s),f=s(l)}t.dispatch(ue(o,f));const h=c.getSource(l).beginDrag(a,l);if(h==null)return;tt(h),c.pinSource(l);const d=c.getSourceType(l);return{type:x,payload:{itemType:d,item:h,sourceId:l,clientOffset:o||null,sourceClientOffset:f||null,isSourcePublic:!!i}}}}function Ke(t,e,r){u(!e.isDragging(),"Cannot call beginDrag while dragging."),t.forEach(function(n){u(r.getSource(n),"Expected sourceIds to be registered.")})}function et(t){u(typeof t=="function","When clientOffset is provided, getSourceClientOffset must be a function.")}function tt(t){u(Te(t),"Item must be an object.")}function rt(t,e){let r=null;for(let n=t.length-1;n>=0;n--)if(e.canDragSource(t[n])){r=t[n];break}return r}function nt(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function it(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{},n=Object.keys(r);typeof Object.getOwnPropertySymbols=="function"&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(i){return Object.getOwnPropertyDescriptor(r,i).enumerable}))),n.forEach(function(i){nt(t,i,r[i])})}return t}function ot(t){return function(r={}){const n=t.getMonitor(),i=t.getRegistry();st(n),ut(n).forEach((s,a)=>{const c=at(s,a,i,n),l={type:_,payload:{dropResult:it({},r,c)}};t.dispatch(l)})}}function st(t){u(t.isDragging(),"Cannot call drop while not dragging."),u(!t.didDrop(),"Cannot call drop twice during one drag operation.")}function at(t,e,r,n){const i=r.getTarget(t);let o=i?i.drop(n,t):void 0;return ct(o),typeof o=="undefined"&&(o=e===0?{}:n.getDropResult()),o}function ct(t){u(typeof t=="undefined"||Te(t),"Drop result must either be an object or undefined.")}function ut(t){const e=t.getTargetIds().filter(t.canDropOnTarget,t);return e.reverse(),e}function dt(t){return function(){const r=t.getMonitor(),n=t.getRegistry();lt(r);const i=r.getSourceId();return i!=null&&(n.getSource(i,!0).endDrag(r,i),n.unpinSource()),{type:L}}}function lt(t){u(t.isDragging(),"Cannot call endDrag while not dragging.")}function B(t,e){return e===null?t===null:Array.isArray(t)?t.some(r=>r===e):t===e}function ft(t){return function(r,{clientOffset:n}={}){gt(r);const i=r.slice(0),o=t.getMonitor(),s=t.getRegistry(),a=o.getItemType();return pt(i,s,a),ht(i,o,s),vt(i,o,s),{type:M,payload:{targetIds:i,clientOffset:n||null}}}}function gt(t){u(Array.isArray(t),"Expected targetIds to be an array.")}function ht(t,e,r){u(e.isDragging(),"Cannot call hover while not dragging."),u(!e.didDrop(),"Cannot call hover after drop.");for(let n=0;n<t.length;n++){const i=t[n];u(t.lastIndexOf(i)===n,"Expected targetIds to be unique in the passed array.");const o=r.getTarget(i);u(o,"Expected targetIds to be registered.")}}function pt(t,e,r){for(let n=t.length-1;n>=0;n--){const i=t[n],o=e.getTargetType(i);B(o,r)||t.splice(n,1)}}function vt(t,e,r){t.forEach(function(n){r.getTarget(n).hover(e,n)})}function mt(t){return function(){if(t.getMonitor().isDragging())return{type:J}}}function Dt(t){return{beginDrag:Ze(t),publishDragSource:mt(t),hover:ft(t),drop:ot(t),endDrag:dt(t)}}class St{receiveBackend(e){this.backend=e}getMonitor(){return this.monitor}getBackend(){return this.backend}getRegistry(){return this.monitor.registry}getActions(){const e=this,{dispatch:r}=this.store;function n(o){return(...s)=>{const a=o.apply(e,s);typeof a!="undefined"&&r(a)}}const i=Dt(this);return Object.keys(i).reduce((o,s)=>{const a=i[s];return o[s]=n(a),o},{})}dispatch(e){this.store.dispatch(e)}constructor(e,r){this.isSetUp=!1,this.handleRefCountChange=()=>{const n=this.store.getState().refCount>0;this.backend&&(n&&!this.isSetUp?(this.backend.setup(),this.isSetUp=!0):!n&&this.isSetUp&&(this.backend.teardown(),this.isSetUp=!1))},this.store=e,this.monitor=r,e.subscribe(this.handleRefCountChange)}}function yt(t,e){return{x:t.x+e.x,y:t.y+e.y}}function Ie(t,e){return{x:t.x-e.x,y:t.y-e.y}}function Ot(t){const{clientOffset:e,initialClientOffset:r,initialSourceClientOffset:n}=t;return!e||!r||!n?null:Ie(yt(e,n),r)}function Tt(t){const{clientOffset:e,initialClientOffset:r}=t;return!e||!r?null:Ie(e,r)}const P=[],Q=[];P.__IS_NONE__=!0;Q.__IS_ALL__=!0;function It(t,e){return t===P?!1:t===Q||typeof e=="undefined"?!0:Je(e,t).length>0}class Et{subscribeToStateChange(e,r={}){const{handlerIds:n}=r;u(typeof e=="function","listener must be a function."),u(typeof n=="undefined"||Array.isArray(n),"handlerIds, when specified, must be an array of strings.");let i=this.store.getState().stateId;const o=()=>{const s=this.store.getState(),a=s.stateId;try{a===i||a===i+1&&!It(s.dirtyHandlerIds,n)||e()}finally{i=a}};return this.store.subscribe(o)}subscribeToOffsetChange(e){u(typeof e=="function","listener must be a function.");let r=this.store.getState().dragOffset;const n=()=>{const i=this.store.getState().dragOffset;i!==r&&(r=i,e())};return this.store.subscribe(n)}canDragSource(e){if(!e)return!1;const r=this.registry.getSource(e);return u(r,`Expected to find a valid source. sourceId=${e}`),this.isDragging()?!1:r.canDrag(this,e)}canDropOnTarget(e){if(!e)return!1;const r=this.registry.getTarget(e);if(u(r,`Expected to find a valid target. targetId=${e}`),!this.isDragging()||this.didDrop())return!1;const n=this.registry.getTargetType(e),i=this.getItemType();return B(n,i)&&r.canDrop(this,e)}isDragging(){return!!this.getItemType()}isDraggingSource(e){if(!e)return!1;const r=this.registry.getSource(e,!0);if(u(r,`Expected to find a valid source. sourceId=${e}`),!this.isDragging()||!this.isSourcePublic())return!1;const n=this.registry.getSourceType(e),i=this.getItemType();return n!==i?!1:r.isDragging(this,e)}isOverTarget(e,r={shallow:!1}){if(!e)return!1;const{shallow:n}=r;if(!this.isDragging())return!1;const i=this.registry.getTargetType(e),o=this.getItemType();if(o&&!B(i,o))return!1;const s=this.getTargetIds();if(!s.length)return!1;const a=s.indexOf(e);return n?a===s.length-1:a>-1}getItemType(){return this.store.getState().dragOperation.itemType}getItem(){return this.store.getState().dragOperation.item}getSourceId(){return this.store.getState().dragOperation.sourceId}getTargetIds(){return this.store.getState().dragOperation.targetIds}getDropResult(){return this.store.getState().dragOperation.dropResult}didDrop(){return this.store.getState().dragOperation.didDrop}isSourcePublic(){return!!this.store.getState().dragOperation.isSourcePublic}getInitialClientOffset(){return this.store.getState().dragOffset.initialClientOffset}getInitialSourceClientOffset(){return this.store.getState().dragOffset.initialSourceClientOffset}getClientOffset(){return this.store.getState().dragOffset.clientOffset}getSourceClientOffset(){return Ot(this.store.getState().dragOffset)}getDifferenceFromInitialOffset(){return Tt(this.store.getState().dragOffset)}constructor(e,r){this.store=e,this.registry=r}}const de=typeof global!="undefined"?global:self,Ee=de.MutationObserver||de.WebKitMutationObserver;function be(t){return function(){const r=setTimeout(i,0),n=setInterval(i,50);function i(){clearTimeout(r),clearInterval(n),t()}}}function bt(t){let e=1;const r=new Ee(t),n=document.createTextNode("");return r.observe(n,{characterData:!0}),function(){e=-e,n.data=e}}const wt=typeof Ee=="function"?bt:be;class Ct{enqueueTask(e){const{queue:r,requestFlush:n}=this;r.length||(n(),this.flushing=!0),r[r.length]=e}constructor(){this.queue=[],this.pendingErrors=[],this.flushing=!1,this.index=0,this.capacity=1024,this.flush=()=>{const{queue:e}=this;for(;this.index<e.length;){const r=this.index;if(this.index++,e[r].call(),this.index>this.capacity){for(let n=0,i=e.length-this.index;n<i;n++)e[n]=e[n+this.index];e.length-=this.index,this.index=0}}e.length=0,this.index=0,this.flushing=!1},this.registerPendingError=e=>{this.pendingErrors.push(e),this.requestErrorThrow()},this.requestFlush=wt(this.flush),this.requestErrorThrow=be(()=>{if(this.pendingErrors.length)throw this.pendingErrors.shift()})}}class Pt{call(){try{this.task&&this.task()}catch(e){this.onError(e)}finally{this.task=null,this.release(this)}}constructor(e,r){this.onError=e,this.release=r,this.task=null}}class Rt{create(e){const r=this.freeTasks,n=r.length?r.pop():new Pt(this.onError,i=>r[r.length]=i);return n.task=e,n}constructor(e){this.onError=e,this.freeTasks=[]}}const we=new Ct,Nt=new Rt(we.registerPendingError);function xt(t){we.enqueueTask(Nt.create(t))}const Z="dnd-core/ADD_SOURCE",K="dnd-core/ADD_TARGET",ee="dnd-core/REMOVE_SOURCE",A="dnd-core/REMOVE_TARGET";function Mt(t){return{type:Z,payload:{sourceId:t}}}function _t(t){return{type:K,payload:{targetId:t}}}function Lt(t){return{type:ee,payload:{sourceId:t}}}function At(t){return{type:A,payload:{targetId:t}}}function Ht(t){u(typeof t.canDrag=="function","Expected canDrag to be a function."),u(typeof t.beginDrag=="function","Expected beginDrag to be a function."),u(typeof t.endDrag=="function","Expected endDrag to be a function.")}function jt(t){u(typeof t.canDrop=="function","Expected canDrop to be a function."),u(typeof t.hover=="function","Expected hover to be a function."),u(typeof t.drop=="function","Expected beginDrag to be a function.")}function V(t,e){if(e&&Array.isArray(t)){t.forEach(r=>V(r,!1));return}u(typeof t=="string"||typeof t=="symbol",e?"Type can only be a string, a symbol, or an array of either.":"Type can only be a string or a symbol.")}var T;(function(t){t.SOURCE="SOURCE",t.TARGET="TARGET"})(T||(T={}));let kt=0;function Ut(){return kt++}function qt(t){const e=Ut().toString();switch(t){case T.SOURCE:return`S${e}`;case T.TARGET:return`T${e}`;default:throw new Error(`Unknown Handler Role: ${t}`)}}function le(t){switch(t[0]){case"S":return T.SOURCE;case"T":return T.TARGET;default:throw new Error(`Cannot parse handler ID: ${t}`)}}function fe(t,e){const r=t.entries();let n=!1;do{const{done:i,value:[,o]}=r.next();if(o===e)return!0;n=!!i}while(!n);return!1}class Ft{addSource(e,r){V(e),Ht(r);const n=this.addHandler(T.SOURCE,e,r);return this.store.dispatch(Mt(n)),n}addTarget(e,r){V(e,!0),jt(r);const n=this.addHandler(T.TARGET,e,r);return this.store.dispatch(_t(n)),n}containsHandler(e){return fe(this.dragSources,e)||fe(this.dropTargets,e)}getSource(e,r=!1){return u(this.isSourceId(e),"Expected a valid source ID."),r&&e===this.pinnedSourceId?this.pinnedSource:this.dragSources.get(e)}getTarget(e){return u(this.isTargetId(e),"Expected a valid target ID."),this.dropTargets.get(e)}getSourceType(e){return u(this.isSourceId(e),"Expected a valid source ID."),this.types.get(e)}getTargetType(e){return u(this.isTargetId(e),"Expected a valid target ID."),this.types.get(e)}isSourceId(e){return le(e)===T.SOURCE}isTargetId(e){return le(e)===T.TARGET}removeSource(e){u(this.getSource(e),"Expected an existing source."),this.store.dispatch(Lt(e)),xt(()=>{this.dragSources.delete(e),this.types.delete(e)})}removeTarget(e){u(this.getTarget(e),"Expected an existing target."),this.store.dispatch(At(e)),this.dropTargets.delete(e),this.types.delete(e)}pinSource(e){const r=this.getSource(e);u(r,"Expected an existing source."),this.pinnedSourceId=e,this.pinnedSource=r}unpinSource(){u(this.pinnedSource,"No source is pinned at the time."),this.pinnedSourceId=null,this.pinnedSource=null}addHandler(e,r,n){const i=qt(e);return this.types.set(i,r),e===T.SOURCE?this.dragSources.set(i,n):e===T.TARGET&&this.dropTargets.set(i,n),i}constructor(e){this.types=new Map,this.dragSources=new Map,this.dropTargets=new Map,this.pinnedSourceId=null,this.pinnedSource=null,this.store=e}}const $t=(t,e)=>t===e;function Gt(t,e){return!t&&!e?!0:!t||!e?!1:t.x===e.x&&t.y===e.y}function Bt(t,e,r=$t){if(t.length!==e.length)return!1;for(let n=0;n<t.length;++n)if(!r(t[n],e[n]))return!1;return!0}function Vt(t=P,e){switch(e.type){case M:break;case Z:case K:case A:case ee:return P;case x:case J:case L:case _:default:return Q}const{targetIds:r=[],prevTargetIds:n=[]}=e.payload,i=ze(r,n);if(!(i.length>0||!Bt(r,n)))return P;const s=n[n.length-1],a=r[r.length-1];return s!==a&&(s&&i.push(s),a&&i.push(a)),i}function Xt(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Yt(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{},n=Object.keys(r);typeof Object.getOwnPropertySymbols=="function"&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(i){return Object.getOwnPropertyDescriptor(r,i).enumerable}))),n.forEach(function(i){Xt(t,i,r[i])})}return t}const ge={initialSourceClientOffset:null,initialClientOffset:null,clientOffset:null};function Wt(t=ge,e){const{payload:r}=e;switch(e.type){case z:case x:return{initialSourceClientOffset:r.sourceClientOffset,initialClientOffset:r.clientOffset,clientOffset:r.clientOffset};case M:return Gt(t.clientOffset,r.clientOffset)?t:Yt({},t,{clientOffset:r.clientOffset});case L:case _:return ge;default:return t}}function zt(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function b(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{},n=Object.keys(r);typeof Object.getOwnPropertySymbols=="function"&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(i){return Object.getOwnPropertyDescriptor(r,i).enumerable}))),n.forEach(function(i){zt(t,i,r[i])})}return t}const Jt={itemType:null,item:null,sourceId:null,targetIds:[],dropResult:null,didDrop:!1,isSourcePublic:null};function Qt(t=Jt,e){const{payload:r}=e;switch(e.type){case x:return b({},t,{itemType:r.itemType,item:r.item,sourceId:r.sourceId,isSourcePublic:r.isSourcePublic,dropResult:null,didDrop:!1});case J:return b({},t,{isSourcePublic:!0});case M:return b({},t,{targetIds:r.targetIds});case A:return t.targetIds.indexOf(r.targetId)===-1?t:b({},t,{targetIds:We(t.targetIds,r.targetId)});case _:return b({},t,{dropResult:r.dropResult,didDrop:!0,targetIds:[]});case L:return b({},t,{itemType:null,item:null,sourceId:null,dropResult:null,didDrop:!1,isSourcePublic:null,targetIds:[]});default:return t}}function Zt(t=0,e){switch(e.type){case Z:case K:return t+1;case ee:case A:return t-1;default:return t}}function Kt(t=0){return t+1}function er(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tr(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{},n=Object.keys(r);typeof Object.getOwnPropertySymbols=="function"&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(i){return Object.getOwnPropertyDescriptor(r,i).enumerable}))),n.forEach(function(i){er(t,i,r[i])})}return t}function rr(t={},e){return{dirtyHandlerIds:Vt(t.dirtyHandlerIds,{type:e.type,payload:tr({},e.payload,{prevTargetIds:Ye(t,"dragOperation.targetIds",[])})}),dragOffset:Wt(t.dragOffset,e),refCount:Zt(t.refCount,e),dragOperation:Qt(t.dragOperation,e),stateId:Kt(t.stateId)}}function nr(t,e=void 0,r={},n=!1){const i=ir(n),o=new Et(i,new Ft(i)),s=new St(i,o),a=t(s,e,r);return s.receiveBackend(a),s}function ir(t){const e=typeof window!="undefined"&&window.__REDUX_DEVTOOLS_EXTENSION__;return ye(rr,t&&e&&e({name:"dnd-core",instanceId:"dnd-core"}))}function or(t,e){if(t==null)return{};var r=sr(t,e),n,i;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(i=0;i<o.length;i++)n=o[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function sr(t,e){if(t==null)return{};var r={},n=Object.keys(t),i,o;for(o=0;o<n.length;o++)i=n[o],!(e.indexOf(i)>=0)&&(r[i]=t[i]);return r}let he=0;const N=Symbol.for("__REACT_DND_CONTEXT_INSTANCE__");var tn=p.memo(function(e){var{children:r}=e,n=or(e,["children"]);const[i,o]=ar(n);return p.useEffect(()=>{if(o){const s=Ce();return++he,()=>{--he===0&&(s[N]=null)}}},[]),Ge.jsx(Oe.Provider,{value:i,children:r})});function ar(t){if("manager"in t)return[{dragDropManager:t.manager},!1];const e=cr(t.backend,t.context,t.options,t.debugMode),r=!t.context;return[e,r]}function cr(t,e=Ce(),r,n){const i=e;return i[N]||(i[N]={dragDropManager:nr(t,e,r,n)}),i[N]}function Ce(){return typeof global!="undefined"?global:window}var k,pe;function ur(){return pe||(pe=1,k=function t(e,r){if(e===r)return!0;if(e&&r&&typeof e=="object"&&typeof r=="object"){if(e.constructor!==r.constructor)return!1;var n,i,o;if(Array.isArray(e)){if(n=e.length,n!=r.length)return!1;for(i=n;i--!==0;)if(!t(e[i],r[i]))return!1;return!0}if(e.constructor===RegExp)return e.source===r.source&&e.flags===r.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===r.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===r.toString();if(o=Object.keys(e),n=o.length,n!==Object.keys(r).length)return!1;for(i=n;i--!==0;)if(!Object.prototype.hasOwnProperty.call(r,o[i]))return!1;for(i=n;i--!==0;){var s=o[i];if(!t(e[s],r[s]))return!1}return!0}return e!==e&&r!==r}),k}var dr=ur();const lr=qe(dr),E=typeof window!="undefined"?p.useLayoutEffect:p.useEffect;function fr(t,e,r){const[n,i]=p.useState(()=>e(t)),o=p.useCallback(()=>{const s=e(t);lr(n,s)||(i(s),r&&r())},[n,t,r]);return E(o),[n,o]}function gr(t,e,r){const[n,i]=fr(t,e,r);return E(function(){const s=t.getHandlerId();if(s!=null)return t.subscribeToStateChange(i,{handlerIds:[s]})},[t,i]),n}function Pe(t,e,r){return gr(e,t||(()=>({})),()=>r.reconnect())}function Re(t,e){const r=[];return typeof t!="function"&&r.push(t),p.useMemo(()=>typeof t=="function"?t():t,r)}function hr(t){return p.useMemo(()=>t.hooks.dragSource(),[t])}function pr(t){return p.useMemo(()=>t.hooks.dragPreview(),[t])}let U=!1,q=!1;class vr{receiveHandlerId(e){this.sourceId=e}getHandlerId(){return this.sourceId}canDrag(){u(!U,"You may not call monitor.canDrag() inside your canDrag() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return U=!0,this.internalMonitor.canDragSource(this.sourceId)}finally{U=!1}}isDragging(){if(!this.sourceId)return!1;u(!q,"You may not call monitor.isDragging() inside your isDragging() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return q=!0,this.internalMonitor.isDraggingSource(this.sourceId)}finally{q=!1}}subscribeToStateChange(e,r){return this.internalMonitor.subscribeToStateChange(e,r)}isDraggingSource(e){return this.internalMonitor.isDraggingSource(e)}isOverTarget(e,r){return this.internalMonitor.isOverTarget(e,r)}getTargetIds(){return this.internalMonitor.getTargetIds()}isSourcePublic(){return this.internalMonitor.isSourcePublic()}getSourceId(){return this.internalMonitor.getSourceId()}subscribeToOffsetChange(e){return this.internalMonitor.subscribeToOffsetChange(e)}canDragSource(e){return this.internalMonitor.canDragSource(e)}canDropOnTarget(e){return this.internalMonitor.canDropOnTarget(e)}getItemType(){return this.internalMonitor.getItemType()}getItem(){return this.internalMonitor.getItem()}getDropResult(){return this.internalMonitor.getDropResult()}didDrop(){return this.internalMonitor.didDrop()}getInitialClientOffset(){return this.internalMonitor.getInitialClientOffset()}getInitialSourceClientOffset(){return this.internalMonitor.getInitialSourceClientOffset()}getSourceClientOffset(){return this.internalMonitor.getSourceClientOffset()}getClientOffset(){return this.internalMonitor.getClientOffset()}getDifferenceFromInitialOffset(){return this.internalMonitor.getDifferenceFromInitialOffset()}constructor(e){this.sourceId=null,this.internalMonitor=e.getMonitor()}}let F=!1;class mr{receiveHandlerId(e){this.targetId=e}getHandlerId(){return this.targetId}subscribeToStateChange(e,r){return this.internalMonitor.subscribeToStateChange(e,r)}canDrop(){if(!this.targetId)return!1;u(!F,"You may not call monitor.canDrop() inside your canDrop() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target-monitor");try{return F=!0,this.internalMonitor.canDropOnTarget(this.targetId)}finally{F=!1}}isOver(e){return this.targetId?this.internalMonitor.isOverTarget(this.targetId,e):!1}getItemType(){return this.internalMonitor.getItemType()}getItem(){return this.internalMonitor.getItem()}getDropResult(){return this.internalMonitor.getDropResult()}didDrop(){return this.internalMonitor.didDrop()}getInitialClientOffset(){return this.internalMonitor.getInitialClientOffset()}getInitialSourceClientOffset(){return this.internalMonitor.getInitialSourceClientOffset()}getSourceClientOffset(){return this.internalMonitor.getSourceClientOffset()}getClientOffset(){return this.internalMonitor.getClientOffset()}getDifferenceFromInitialOffset(){return this.internalMonitor.getDifferenceFromInitialOffset()}constructor(e){this.targetId=null,this.internalMonitor=e.getMonitor()}}function Dr(t,e,r){const n=r.getRegistry(),i=n.addTarget(t,e);return[i,()=>n.removeTarget(i)]}function Sr(t,e,r){const n=r.getRegistry(),i=n.addSource(t,e);return[i,()=>n.removeSource(i)]}function X(t,e,r,n){let i;if(i!==void 0)return!!i;if(t===e)return!0;if(typeof t!="object"||!t||typeof e!="object"||!e)return!1;const o=Object.keys(t),s=Object.keys(e);if(o.length!==s.length)return!1;const a=Object.prototype.hasOwnProperty.bind(e);for(let c=0;c<o.length;c++){const l=o[c];if(!a(l))return!1;const f=t[l],v=e[l];if(i=void 0,i===!1||i===void 0&&f!==v)return!1}return!0}function Y(t){return t!==null&&typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}function yr(t){if(typeof t.type=="string")return;const e=t.type.displayName||t.type.name||"the component";throw new Error(`Only native element nodes can now be passed to React DnD connectors.You can either wrap ${e} into a <div>, or turn it into a drag source or a drop target itself.`)}function Or(t){return(e=null,r=null)=>{if(!p.isValidElement(e)){const o=e;return t(o,r),o}const n=e;return yr(n),Tr(n,r?o=>t(o,r):t)}}function Ne(t){const e={};return Object.keys(t).forEach(r=>{const n=t[r];if(r.endsWith("Ref"))e[r]=t[r];else{const i=Or(n);e[r]=()=>i}}),e}function ve(t,e){typeof t=="function"?t(e):t.current=e}function Tr(t,e){const r=t.ref;return u(typeof r!="string","Cannot connect React DnD to an element with an existing string ref. Please convert it to use a callback ref instead, or wrap it into a <span> or <div>. Read more: https://reactjs.org/docs/refs-and-the-dom.html#callback-refs"),r?p.cloneElement(t,{ref:n=>{ve(r,n),ve(e,n)}}):p.cloneElement(t,{ref:e})}class Ir{receiveHandlerId(e){this.handlerId!==e&&(this.handlerId=e,this.reconnect())}get connectTarget(){return this.dragSource}get dragSourceOptions(){return this.dragSourceOptionsInternal}set dragSourceOptions(e){this.dragSourceOptionsInternal=e}get dragPreviewOptions(){return this.dragPreviewOptionsInternal}set dragPreviewOptions(e){this.dragPreviewOptionsInternal=e}reconnect(){const e=this.reconnectDragSource();this.reconnectDragPreview(e)}reconnectDragSource(){const e=this.dragSource,r=this.didHandlerIdChange()||this.didConnectedDragSourceChange()||this.didDragSourceOptionsChange();return r&&this.disconnectDragSource(),this.handlerId?e?(r&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragSource=e,this.lastConnectedDragSourceOptions=this.dragSourceOptions,this.dragSourceUnsubscribe=this.backend.connectDragSource(this.handlerId,e,this.dragSourceOptions)),r):(this.lastConnectedDragSource=e,r):r}reconnectDragPreview(e=!1){const r=this.dragPreview,n=e||this.didHandlerIdChange()||this.didConnectedDragPreviewChange()||this.didDragPreviewOptionsChange();if(n&&this.disconnectDragPreview(),!!this.handlerId){if(!r){this.lastConnectedDragPreview=r;return}n&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragPreview=r,this.lastConnectedDragPreviewOptions=this.dragPreviewOptions,this.dragPreviewUnsubscribe=this.backend.connectDragPreview(this.handlerId,r,this.dragPreviewOptions))}}didHandlerIdChange(){return this.lastConnectedHandlerId!==this.handlerId}didConnectedDragSourceChange(){return this.lastConnectedDragSource!==this.dragSource}didConnectedDragPreviewChange(){return this.lastConnectedDragPreview!==this.dragPreview}didDragSourceOptionsChange(){return!X(this.lastConnectedDragSourceOptions,this.dragSourceOptions)}didDragPreviewOptionsChange(){return!X(this.lastConnectedDragPreviewOptions,this.dragPreviewOptions)}disconnectDragSource(){this.dragSourceUnsubscribe&&(this.dragSourceUnsubscribe(),this.dragSourceUnsubscribe=void 0)}disconnectDragPreview(){this.dragPreviewUnsubscribe&&(this.dragPreviewUnsubscribe(),this.dragPreviewUnsubscribe=void 0,this.dragPreviewNode=null,this.dragPreviewRef=null)}get dragSource(){return this.dragSourceNode||this.dragSourceRef&&this.dragSourceRef.current}get dragPreview(){return this.dragPreviewNode||this.dragPreviewRef&&this.dragPreviewRef.current}clearDragSource(){this.dragSourceNode=null,this.dragSourceRef=null}clearDragPreview(){this.dragPreviewNode=null,this.dragPreviewRef=null}constructor(e){this.hooks=Ne({dragSource:(r,n)=>{this.clearDragSource(),this.dragSourceOptions=n||null,Y(r)?this.dragSourceRef=r:this.dragSourceNode=r,this.reconnectDragSource()},dragPreview:(r,n)=>{this.clearDragPreview(),this.dragPreviewOptions=n||null,Y(r)?this.dragPreviewRef=r:this.dragPreviewNode=r,this.reconnectDragPreview()}}),this.handlerId=null,this.dragSourceRef=null,this.dragSourceOptionsInternal=null,this.dragPreviewRef=null,this.dragPreviewOptionsInternal=null,this.lastConnectedHandlerId=null,this.lastConnectedDragSource=null,this.lastConnectedDragSourceOptions=null,this.lastConnectedDragPreview=null,this.lastConnectedDragPreviewOptions=null,this.backend=e}}class Er{get connectTarget(){return this.dropTarget}reconnect(){const e=this.didHandlerIdChange()||this.didDropTargetChange()||this.didOptionsChange();e&&this.disconnectDropTarget();const r=this.dropTarget;if(this.handlerId){if(!r){this.lastConnectedDropTarget=r;return}e&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDropTarget=r,this.lastConnectedDropTargetOptions=this.dropTargetOptions,this.unsubscribeDropTarget=this.backend.connectDropTarget(this.handlerId,r,this.dropTargetOptions))}}receiveHandlerId(e){e!==this.handlerId&&(this.handlerId=e,this.reconnect())}get dropTargetOptions(){return this.dropTargetOptionsInternal}set dropTargetOptions(e){this.dropTargetOptionsInternal=e}didHandlerIdChange(){return this.lastConnectedHandlerId!==this.handlerId}didDropTargetChange(){return this.lastConnectedDropTarget!==this.dropTarget}didOptionsChange(){return!X(this.lastConnectedDropTargetOptions,this.dropTargetOptions)}disconnectDropTarget(){this.unsubscribeDropTarget&&(this.unsubscribeDropTarget(),this.unsubscribeDropTarget=void 0)}get dropTarget(){return this.dropTargetNode||this.dropTargetRef&&this.dropTargetRef.current}clearDropTarget(){this.dropTargetRef=null,this.dropTargetNode=null}constructor(e){this.hooks=Ne({dropTarget:(r,n)=>{this.clearDropTarget(),this.dropTargetOptions=n,Y(r)?this.dropTargetRef=r:this.dropTargetNode=r,this.reconnect()}}),this.handlerId=null,this.dropTargetRef=null,this.dropTargetOptionsInternal=null,this.lastConnectedHandlerId=null,this.lastConnectedDropTarget=null,this.lastConnectedDropTargetOptions=null,this.backend=e}}function w(){const{dragDropManager:t}=p.useContext(Oe);return u(t!=null,"Expected drag drop context"),t}function br(t,e){const r=w(),n=p.useMemo(()=>new Ir(r.getBackend()),[r]);return E(()=>(n.dragSourceOptions=t||null,n.reconnect(),()=>n.disconnectDragSource()),[n,t]),E(()=>(n.dragPreviewOptions=e||null,n.reconnect(),()=>n.disconnectDragPreview()),[n,e]),n}function wr(){const t=w();return p.useMemo(()=>new vr(t),[t])}class Cr{beginDrag(){const e=this.spec,r=this.monitor;let n=null;return typeof e.item=="object"?n=e.item:typeof e.item=="function"?n=e.item(r):n={},n!=null?n:null}canDrag(){const e=this.spec,r=this.monitor;return typeof e.canDrag=="boolean"?e.canDrag:typeof e.canDrag=="function"?e.canDrag(r):!0}isDragging(e,r){const n=this.spec,i=this.monitor,{isDragging:o}=n;return o?o(i):r===e.getSourceId()}endDrag(){const e=this.spec,r=this.monitor,n=this.connector,{end:i}=e;i&&i(r.getItem(),r),n.reconnect()}constructor(e,r,n){this.spec=e,this.monitor=r,this.connector=n}}function Pr(t,e,r){const n=p.useMemo(()=>new Cr(t,e,r),[e,r]);return p.useEffect(()=>{n.spec=t},[t]),n}function Rr(t){return p.useMemo(()=>{const e=t.type;return u(e!=null,"spec.type must be defined"),e},[t])}function Nr(t,e,r){const n=w(),i=Pr(t,e,r),o=Rr(t);E(function(){if(o!=null){const[a,c]=Sr(o,i,n);return e.receiveHandlerId(a),r.receiveHandlerId(a),c}},[n,e,r,i,o])}function rn(t,e){const r=Re(t);u(!r.begin,"useDrag::spec.begin was deprecated in v14. Replace spec.begin() with spec.item(). (see more here - https://react-dnd.github.io/react-dnd/docs/api/use-drag)");const n=wr(),i=br(r.options,r.previewOptions);return Nr(r,n,i),[Pe(r.collect,n,i),hr(i),pr(i)]}function xr(t){return p.useMemo(()=>t.hooks.dropTarget(),[t])}function Mr(t){const e=w(),r=p.useMemo(()=>new Er(e.getBackend()),[e]);return E(()=>(r.dropTargetOptions=t||null,r.reconnect(),()=>r.disconnectDropTarget()),[t]),r}function _r(){const t=w();return p.useMemo(()=>new mr(t),[t])}function Lr(t){const{accept:e}=t;return p.useMemo(()=>(u(t.accept!=null,"accept must be defined"),Array.isArray(e)?e:[e]),[e])}class Ar{canDrop(){const e=this.spec,r=this.monitor;return e.canDrop?e.canDrop(r.getItem(),r):!0}hover(){const e=this.spec,r=this.monitor;e.hover&&e.hover(r.getItem(),r)}drop(){const e=this.spec,r=this.monitor;if(e.drop)return e.drop(r.getItem(),r)}constructor(e,r){this.spec=e,this.monitor=r}}function Hr(t,e){const r=p.useMemo(()=>new Ar(t,e),[e]);return p.useEffect(()=>{r.spec=t},[t]),r}function jr(t,e,r){const n=w(),i=Hr(t,e),o=Lr(t);E(function(){const[a,c]=Dr(o,i,n);return e.receiveHandlerId(a),r.receiveHandlerId(a),c},[n,e,i,r,o.map(s=>s.toString()).join("|")])}function nn(t,e){const r=Re(t),n=_r(),i=Mr(r.options);return jr(r,n,i),[Pe(r.collect,n,i),xr(i)]}function xe(t){let e=null;return()=>(e==null&&(e=t()),e)}function kr(t,e){return t.filter(r=>r!==e)}function Ur(t,e){const r=new Set,n=o=>r.add(o);t.forEach(n),e.forEach(n);const i=[];return r.forEach(o=>i.push(o)),i}class qr{enter(e){const r=this.entered.length,n=i=>this.isNodeInDocument(i)&&(!i.contains||i.contains(e));return this.entered=Ur(this.entered.filter(n),[e]),r===0&&this.entered.length>0}leave(e){const r=this.entered.length;return this.entered=kr(this.entered.filter(this.isNodeInDocument),e),r>0&&this.entered.length===0}reset(){this.entered=[]}constructor(e){this.entered=[],this.isNodeInDocument=e}}class Fr{initializeExposedProperties(){Object.keys(this.config.exposeProperties).forEach(e=>{Object.defineProperty(this.item,e,{configurable:!0,enumerable:!0,get(){return null}})})}loadDataTransfer(e){if(e){const r={};Object.keys(this.config.exposeProperties).forEach(n=>{const i=this.config.exposeProperties[n];i!=null&&(r[n]={value:i(e,this.config.matchesTypes),configurable:!0,enumerable:!0})}),Object.defineProperties(this.item,r)}}canDrag(){return!0}beginDrag(){return this.item}isDragging(e,r){return r===e.getSourceId()}endDrag(){}constructor(e){this.config=e,this.item={},this.initializeExposedProperties()}}const Me="__NATIVE_FILE__",_e="__NATIVE_URL__",Le="__NATIVE_TEXT__",Ae="__NATIVE_HTML__",me=Object.freeze(Object.defineProperty({__proto__:null,FILE:Me,HTML:Ae,TEXT:Le,URL:_e},Symbol.toStringTag,{value:"Module"}));function $(t,e,r){const n=e.reduce((i,o)=>i||t.getData(o),"");return n!=null?n:r}const W={[Me]:{exposeProperties:{files:t=>Array.prototype.slice.call(t.files),items:t=>t.items,dataTransfer:t=>t},matchesTypes:["Files"]},[Ae]:{exposeProperties:{html:(t,e)=>$(t,e,""),dataTransfer:t=>t},matchesTypes:["Html","text/html"]},[_e]:{exposeProperties:{urls:(t,e)=>$(t,e,"").split(`
`),dataTransfer:t=>t},matchesTypes:["Url","text/uri-list"]},[Le]:{exposeProperties:{text:(t,e)=>$(t,e,""),dataTransfer:t=>t},matchesTypes:["Text","text/plain"]}};function $r(t,e){const r=W[t];if(!r)throw new Error(`native type ${t} has no configuration`);const n=new Fr(r);return n.loadDataTransfer(e),n}function G(t){if(!t)return null;const e=Array.prototype.slice.call(t.types||[]);return Object.keys(W).filter(r=>{const n=W[r];return n!=null&&n.matchesTypes?n.matchesTypes.some(i=>e.indexOf(i)>-1):!1})[0]||null}const Gr=xe(()=>/firefox/i.test(navigator.userAgent)),He=xe(()=>!!window.safari);class De{interpolate(e){const{xs:r,ys:n,c1s:i,c2s:o,c3s:s}=this;let a=r.length-1;if(e===r[a])return n[a];let c=0,l=s.length-1,f;for(;c<=l;){f=Math.floor(.5*(c+l));const d=r[f];if(d<e)c=f+1;else if(d>e)l=f-1;else return n[f]}a=Math.max(0,l);const v=e-r[a],h=v*v;return n[a]+i[a]*v+o[a]*h+s[a]*v*h}constructor(e,r){const{length:n}=e,i=[];for(let d=0;d<n;d++)i.push(d);i.sort((d,D)=>e[d]<e[D]?-1:1);const o=[],s=[];let a,c;for(let d=0;d<n-1;d++)a=e[d+1]-e[d],c=r[d+1]-r[d],o.push(a),s.push(c/a);const l=[s[0]];for(let d=0;d<o.length-1;d++){const D=s[d],g=s[d+1];if(D*g<=0)l.push(0);else{a=o[d];const m=o[d+1],S=a+m;l.push(3*S/((S+m)/D+(S+a)/g))}}l.push(s[s.length-1]);const f=[],v=[];let h;for(let d=0;d<l.length-1;d++){h=s[d];const D=l[d],g=1/o[d],m=D+l[d+1]-h-h;f.push((h-D-m)*g),v.push(m*g*g)}this.xs=e,this.ys=r,this.c1s=l,this.c2s=f,this.c3s=v}}const Br=1;function je(t){const e=t.nodeType===Br?t:t.parentElement;if(!e)return null;const{top:r,left:n}=e.getBoundingClientRect();return{x:n,y:r}}function R(t){return{x:t.clientX,y:t.clientY}}function Vr(t){var e;return t.nodeName==="IMG"&&(Gr()||!(!((e=document.documentElement)===null||e===void 0)&&e.contains(t)))}function Xr(t,e,r,n){let i=t?e.width:r,o=t?e.height:n;return He()&&t&&(o/=window.devicePixelRatio,i/=window.devicePixelRatio),{dragPreviewWidth:i,dragPreviewHeight:o}}function Yr(t,e,r,n,i){const o=Vr(e),a=je(o?t:e),c={x:r.x-a.x,y:r.y-a.y},{offsetWidth:l,offsetHeight:f}=t,{anchorX:v,anchorY:h}=n,{dragPreviewWidth:d,dragPreviewHeight:D}=Xr(o,e,l,f),g=()=>{let te=new De([0,.5,1],[c.y,c.y/f*D,c.y+D-f]).interpolate(h);return He()&&o&&(te+=(window.devicePixelRatio-1)*D),te},m=()=>new De([0,.5,1],[c.x,c.x/l*d,c.x+d-l]).interpolate(v),{offsetX:S,offsetY:O}=i,I=S===0||S,H=O===0||O;return{x:I?S:m(),y:H?O:g()}}class Wr{get window(){if(this.globalContext)return this.globalContext;if(typeof window!="undefined")return window}get document(){var e;return!((e=this.globalContext)===null||e===void 0)&&e.document?this.globalContext.document:this.window?this.window.document:void 0}get rootElement(){var e;return((e=this.optionsArgs)===null||e===void 0?void 0:e.rootElement)||this.window}constructor(e,r){this.ownerDocument=null,this.globalContext=e,this.optionsArgs=r}}function zr(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Se(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{},n=Object.keys(r);typeof Object.getOwnPropertySymbols=="function"&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(i){return Object.getOwnPropertyDescriptor(r,i).enumerable}))),n.forEach(function(i){zr(t,i,r[i])})}return t}class Jr{profile(){var e,r;return{sourcePreviewNodes:this.sourcePreviewNodes.size,sourcePreviewNodeOptions:this.sourcePreviewNodeOptions.size,sourceNodeOptions:this.sourceNodeOptions.size,sourceNodes:this.sourceNodes.size,dragStartSourceIds:((e=this.dragStartSourceIds)===null||e===void 0?void 0:e.length)||0,dropTargetIds:this.dropTargetIds.length,dragEnterTargetIds:this.dragEnterTargetIds.length,dragOverTargetIds:((r=this.dragOverTargetIds)===null||r===void 0?void 0:r.length)||0}}get window(){return this.options.window}get document(){return this.options.document}get rootElement(){return this.options.rootElement}setup(){const e=this.rootElement;if(e!==void 0){if(e.__isReactDndBackendSetUp)throw new Error("Cannot have two HTML5 backends at the same time.");e.__isReactDndBackendSetUp=!0,this.addEventListeners(e)}}teardown(){const e=this.rootElement;if(e!==void 0&&(e.__isReactDndBackendSetUp=!1,this.removeEventListeners(this.rootElement),this.clearCurrentDragSourceNode(),this.asyncEndDragFrameId)){var r;(r=this.window)===null||r===void 0||r.cancelAnimationFrame(this.asyncEndDragFrameId)}}connectDragPreview(e,r,n){return this.sourcePreviewNodeOptions.set(e,n),this.sourcePreviewNodes.set(e,r),()=>{this.sourcePreviewNodes.delete(e),this.sourcePreviewNodeOptions.delete(e)}}connectDragSource(e,r,n){this.sourceNodes.set(e,r),this.sourceNodeOptions.set(e,n);const i=s=>this.handleDragStart(s,e),o=s=>this.handleSelectStart(s);return r.setAttribute("draggable","true"),r.addEventListener("dragstart",i),r.addEventListener("selectstart",o),()=>{this.sourceNodes.delete(e),this.sourceNodeOptions.delete(e),r.removeEventListener("dragstart",i),r.removeEventListener("selectstart",o),r.setAttribute("draggable","false")}}connectDropTarget(e,r){const n=s=>this.handleDragEnter(s,e),i=s=>this.handleDragOver(s,e),o=s=>this.handleDrop(s,e);return r.addEventListener("dragenter",n),r.addEventListener("dragover",i),r.addEventListener("drop",o),()=>{r.removeEventListener("dragenter",n),r.removeEventListener("dragover",i),r.removeEventListener("drop",o)}}addEventListeners(e){e.addEventListener&&(e.addEventListener("dragstart",this.handleTopDragStart),e.addEventListener("dragstart",this.handleTopDragStartCapture,!0),e.addEventListener("dragend",this.handleTopDragEndCapture,!0),e.addEventListener("dragenter",this.handleTopDragEnter),e.addEventListener("dragenter",this.handleTopDragEnterCapture,!0),e.addEventListener("dragleave",this.handleTopDragLeaveCapture,!0),e.addEventListener("dragover",this.handleTopDragOver),e.addEventListener("dragover",this.handleTopDragOverCapture,!0),e.addEventListener("drop",this.handleTopDrop),e.addEventListener("drop",this.handleTopDropCapture,!0))}removeEventListeners(e){e.removeEventListener&&(e.removeEventListener("dragstart",this.handleTopDragStart),e.removeEventListener("dragstart",this.handleTopDragStartCapture,!0),e.removeEventListener("dragend",this.handleTopDragEndCapture,!0),e.removeEventListener("dragenter",this.handleTopDragEnter),e.removeEventListener("dragenter",this.handleTopDragEnterCapture,!0),e.removeEventListener("dragleave",this.handleTopDragLeaveCapture,!0),e.removeEventListener("dragover",this.handleTopDragOver),e.removeEventListener("dragover",this.handleTopDragOverCapture,!0),e.removeEventListener("drop",this.handleTopDrop),e.removeEventListener("drop",this.handleTopDropCapture,!0))}getCurrentSourceNodeOptions(){const e=this.monitor.getSourceId(),r=this.sourceNodeOptions.get(e);return Se({dropEffect:this.altKeyPressed?"copy":"move"},r||{})}getCurrentDropEffect(){return this.isDraggingNativeItem()?"copy":this.getCurrentSourceNodeOptions().dropEffect}getCurrentSourcePreviewNodeOptions(){const e=this.monitor.getSourceId(),r=this.sourcePreviewNodeOptions.get(e);return Se({anchorX:.5,anchorY:.5,captureDraggingState:!1},r||{})}isDraggingNativeItem(){const e=this.monitor.getItemType();return Object.keys(me).some(r=>me[r]===e)}beginDragNativeItem(e,r){this.clearCurrentDragSourceNode(),this.currentNativeSource=$r(e,r),this.currentNativeHandle=this.registry.addSource(e,this.currentNativeSource),this.actions.beginDrag([this.currentNativeHandle])}setCurrentDragSourceNode(e){this.clearCurrentDragSourceNode(),this.currentDragSourceNode=e;const r=1e3;this.mouseMoveTimeoutTimer=setTimeout(()=>{var n;return(n=this.rootElement)===null||n===void 0?void 0:n.addEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0)},r)}clearCurrentDragSourceNode(){if(this.currentDragSourceNode){if(this.currentDragSourceNode=null,this.rootElement){var e;(e=this.window)===null||e===void 0||e.clearTimeout(this.mouseMoveTimeoutTimer||void 0),this.rootElement.removeEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0)}return this.mouseMoveTimeoutTimer=null,!0}return!1}handleDragStart(e,r){e.defaultPrevented||(this.dragStartSourceIds||(this.dragStartSourceIds=[]),this.dragStartSourceIds.unshift(r))}handleDragEnter(e,r){this.dragEnterTargetIds.unshift(r)}handleDragOver(e,r){this.dragOverTargetIds===null&&(this.dragOverTargetIds=[]),this.dragOverTargetIds.unshift(r)}handleDrop(e,r){this.dropTargetIds.unshift(r)}constructor(e,r,n){this.sourcePreviewNodes=new Map,this.sourcePreviewNodeOptions=new Map,this.sourceNodes=new Map,this.sourceNodeOptions=new Map,this.dragStartSourceIds=null,this.dropTargetIds=[],this.dragEnterTargetIds=[],this.currentNativeSource=null,this.currentNativeHandle=null,this.currentDragSourceNode=null,this.altKeyPressed=!1,this.mouseMoveTimeoutTimer=null,this.asyncEndDragFrameId=null,this.dragOverTargetIds=null,this.lastClientOffset=null,this.hoverRafId=null,this.getSourceClientOffset=i=>{const o=this.sourceNodes.get(i);return o&&je(o)||null},this.endDragNativeItem=()=>{this.isDraggingNativeItem()&&(this.actions.endDrag(),this.currentNativeHandle&&this.registry.removeSource(this.currentNativeHandle),this.currentNativeHandle=null,this.currentNativeSource=null)},this.isNodeInDocument=i=>!!(i&&this.document&&this.document.body&&this.document.body.contains(i)),this.endDragIfSourceWasRemovedFromDOM=()=>{const i=this.currentDragSourceNode;i==null||this.isNodeInDocument(i)||(this.clearCurrentDragSourceNode()&&this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover())},this.scheduleHover=i=>{this.hoverRafId===null&&typeof requestAnimationFrame!="undefined"&&(this.hoverRafId=requestAnimationFrame(()=>{this.monitor.isDragging()&&this.actions.hover(i||[],{clientOffset:this.lastClientOffset}),this.hoverRafId=null}))},this.cancelHover=()=>{this.hoverRafId!==null&&typeof cancelAnimationFrame!="undefined"&&(cancelAnimationFrame(this.hoverRafId),this.hoverRafId=null)},this.handleTopDragStartCapture=()=>{this.clearCurrentDragSourceNode(),this.dragStartSourceIds=[]},this.handleTopDragStart=i=>{if(i.defaultPrevented)return;const{dragStartSourceIds:o}=this;this.dragStartSourceIds=null;const s=R(i);this.monitor.isDragging()&&(this.actions.endDrag(),this.cancelHover()),this.actions.beginDrag(o||[],{publishSource:!1,getSourceClientOffset:this.getSourceClientOffset,clientOffset:s});const{dataTransfer:a}=i,c=G(a);if(this.monitor.isDragging()){if(a&&typeof a.setDragImage=="function"){const f=this.monitor.getSourceId(),v=this.sourceNodes.get(f),h=this.sourcePreviewNodes.get(f)||v;if(h){const{anchorX:d,anchorY:D,offsetX:g,offsetY:m}=this.getCurrentSourcePreviewNodeOptions(),I=Yr(v,h,s,{anchorX:d,anchorY:D},{offsetX:g,offsetY:m});a.setDragImage(h,I.x,I.y)}}try{a==null||a.setData("application/json",{})}catch(f){}this.setCurrentDragSourceNode(i.target);const{captureDraggingState:l}=this.getCurrentSourcePreviewNodeOptions();l?this.actions.publishDragSource():setTimeout(()=>this.actions.publishDragSource(),0)}else if(c)this.beginDragNativeItem(c);else{if(a&&!a.types&&(i.target&&!i.target.hasAttribute||!i.target.hasAttribute("draggable")))return;i.preventDefault()}},this.handleTopDragEndCapture=()=>{this.clearCurrentDragSourceNode()&&this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover()},this.handleTopDragEnterCapture=i=>{if(this.dragEnterTargetIds=[],this.isDraggingNativeItem()){var o;(o=this.currentNativeSource)===null||o===void 0||o.loadDataTransfer(i.dataTransfer)}if(!this.enterLeaveCounter.enter(i.target)||this.monitor.isDragging())return;const{dataTransfer:a}=i,c=G(a);c&&this.beginDragNativeItem(c,a)},this.handleTopDragEnter=i=>{const{dragEnterTargetIds:o}=this;if(this.dragEnterTargetIds=[],!this.monitor.isDragging())return;this.altKeyPressed=i.altKey,o.length>0&&this.actions.hover(o,{clientOffset:R(i)}),o.some(a=>this.monitor.canDropOnTarget(a))&&(i.preventDefault(),i.dataTransfer&&(i.dataTransfer.dropEffect=this.getCurrentDropEffect()))},this.handleTopDragOverCapture=i=>{if(this.dragOverTargetIds=[],this.isDraggingNativeItem()){var o;(o=this.currentNativeSource)===null||o===void 0||o.loadDataTransfer(i.dataTransfer)}},this.handleTopDragOver=i=>{const{dragOverTargetIds:o}=this;if(this.dragOverTargetIds=[],!this.monitor.isDragging()){i.preventDefault(),i.dataTransfer&&(i.dataTransfer.dropEffect="none");return}this.altKeyPressed=i.altKey,this.lastClientOffset=R(i),this.scheduleHover(o),(o||[]).some(a=>this.monitor.canDropOnTarget(a))?(i.preventDefault(),i.dataTransfer&&(i.dataTransfer.dropEffect=this.getCurrentDropEffect())):this.isDraggingNativeItem()?i.preventDefault():(i.preventDefault(),i.dataTransfer&&(i.dataTransfer.dropEffect="none"))},this.handleTopDragLeaveCapture=i=>{this.isDraggingNativeItem()&&i.preventDefault(),this.enterLeaveCounter.leave(i.target)&&(this.isDraggingNativeItem()&&setTimeout(()=>this.endDragNativeItem(),0),this.cancelHover())},this.handleTopDropCapture=i=>{if(this.dropTargetIds=[],this.isDraggingNativeItem()){var o;i.preventDefault(),(o=this.currentNativeSource)===null||o===void 0||o.loadDataTransfer(i.dataTransfer)}else G(i.dataTransfer)&&i.preventDefault();this.enterLeaveCounter.reset()},this.handleTopDrop=i=>{const{dropTargetIds:o}=this;this.dropTargetIds=[],this.actions.hover(o,{clientOffset:R(i)}),this.actions.drop({dropEffect:this.getCurrentDropEffect()}),this.isDraggingNativeItem()?this.endDragNativeItem():this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover()},this.handleSelectStart=i=>{const o=i.target;typeof o.dragDrop=="function"&&(o.tagName==="INPUT"||o.tagName==="SELECT"||o.tagName==="TEXTAREA"||o.isContentEditable||(i.preventDefault(),o.dragDrop()))},this.options=new Wr(r,n),this.actions=e.getActions(),this.monitor=e.getMonitor(),this.registry=e.getRegistry(),this.enterLeaveCounter=new qr(this.isNodeInDocument)}}const on=function(e,r,n){return new Jr(e,r,n)};export{tn as D,on as H,Ve as a,Kr as b,ye as c,en as d,nn as e,Ge as j,rn as u};
