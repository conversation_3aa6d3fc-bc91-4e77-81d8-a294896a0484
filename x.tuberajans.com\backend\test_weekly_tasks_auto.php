<?php
// Test weekly tasks API with auto completion

$url = "http://localhost/x-site/weekly_tasks.php?kullanici_adi=61.rahab.61&start_date=2025-05-19&end_date=2025-05-26";

$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => [
            'Authorization: Bearer test-token',
            'Content-Type: application/json'
        ]
    ]
]);

$response = file_get_contents($url, false, $context);

if ($response === false) {
    echo "Error: Could not fetch data\n";
    exit(1);
}

echo "Raw Response:\n";
echo $response . "\n\n";

$data = json_decode($response, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    echo "JSON Error: " . json_last_error_msg() . "\n";
    exit(1);
}

echo "Parsed Response:\n";
print_r($data);

if (isset($data['data'])) {
    echo "\n=== GÖREVLER ===\n";
    foreach ($data['data'] as $task) {
        echo "Görev: " . $task['gorev_onerisi'] . "\n";
        echo "Zorluk: " . $task['gorev_zorlugu'] . "\n";
        echo "Durum: " . $task['durum'] . "\n";
        echo "Tamamlandı: " . ($task['tamamlandi'] ? 'Evet' : 'Hayır') . "\n";
        echo "Otomatik Tamamlandı: " . (isset($task['auto_completed']) && $task['auto_completed'] ? 'Evet' : 'Hayır') . "\n";
        echo "Puan: " . $task['puan'] . "\n";
        echo "---\n";
    }
}

if (isset($data['performance'])) {
    echo "\n=== PERFORMANS VERİLERİ ===\n";
    print_r($data['performance']);
}
?> 