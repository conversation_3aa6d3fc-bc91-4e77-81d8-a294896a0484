import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from './AuthContext';

interface TikTokUser {
  open_id: string;
  union_id: string;
  username: string;
  display_name: string;
  avatar_url: string;
  bio_description: string;
  is_verified: boolean;
  follower_count: number;
  following_count: number;
  likes_count: number;
  video_count: number;
}

interface TikTokContextType {
  tiktokUser: TikTokUser | null;
  loading: boolean;
  error: string | null;
  refreshTikTokUser: () => Promise<void>;
  clearTikTokUser: () => void;
}

const TikTokContext = createContext<TikTokContextType | undefined>(undefined);

export const useTikTok = () => {
  const context = useContext(TikTokContext);
  if (!context) {
    throw new Error('useTikTok must be used within a TikTokProvider');
  }
  return context;
};

export const TikTokProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [tiktokUser, setTiktokUser] = useState<TikTokUser | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated } = useAuth();

  // TikTok kullanıcı bilgilerini temizle
  const clearTikTokUser = () => {
    setTiktokUser(null);
    localStorage.removeItem('tiktokUser');
  };

  const refreshTikTokUser = async () => {
    if (!isAuthenticated) {
      clearTikTokUser();
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Önce session kontrolü yap
      const sessionResponse = await fetch('/backend/api/tiktok_user.php?action=check_session', {
        credentials: 'include'
      });

      const sessionData = await sessionResponse.json();

      // Eğer TikTok bağlantısı yoksa, bilgileri temizle ve çık
      if (!sessionData.has_tiktok_session) {
        clearTikTokUser();
        setLoading(false);
        return;
      }

      // TikTok bağlantısı varsa bilgileri getir
      const userResponse = await fetch('/backend/api/tiktok_user.php', {
        credentials: 'include'
      });

      const userData = await userResponse.json();

      if (userData.status === 'success' && userData.tiktok_user) {
        setTiktokUser(userData.tiktok_user);
        localStorage.setItem('tiktokUser', JSON.stringify(userData.tiktok_user));
      } else {
        clearTikTokUser();
      }
    } catch (error) {
      console.error('TikTok user refresh error:', error);
      clearTikTokUser();
      setError('TikTok bilgileri alınamadı');
    } finally {
      setLoading(false);
    }
  };

  // Sayfa yüklendiğinde ve kullanıcı değiştiğinde TikTok bilgilerini kontrol et
  useEffect(() => {
    if (isAuthenticated) {
      // Önbellekten TikTok bilgilerini yükle
      const cachedTikTokUser = localStorage.getItem('tiktokUser');
      if (cachedTikTokUser) {
        try {
          setTiktokUser(JSON.parse(cachedTikTokUser));
        } catch (error) {
          console.error('Cached TikTok user parse error:', error);
          localStorage.removeItem('tiktokUser');
        }
      }
      
      // TikTok bilgilerini yenile (sadece bağlantı varsa)
      refreshTikTokUser();
    } else {
      clearTikTokUser();
    }
  }, [isAuthenticated]);

  const value = {
    tiktokUser,
    loading,
    error,
    refreshTikTokUser,
    clearTikTokUser
  };

  return (
    <TikTokContext.Provider value={value}>
      {children}
    </TikTokContext.Provider>
  );
};
