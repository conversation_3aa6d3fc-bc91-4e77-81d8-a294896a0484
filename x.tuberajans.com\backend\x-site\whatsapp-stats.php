<?php
header('Content-Type: application/json; charset=utf-8');

// İstek türünü belirle
$action = $_GET['action'] ?? 'summary';

// Okunmamış mesaj sayısını al
if ($action === 'unread_count') {
    $inLog = __DIR__ . '/whatsapp_incoming_log.txt';
    $readLog = __DIR__ . '/whatsapp_read_messages.txt';

    // Okunan mesajların ID'lerini al
    $readIds = [];
    if (file_exists($readLog)) {
        $readLines = file($readLog);
        foreach ($readLines as $line) {
            $readIds[] = trim($line);
        }
    }

    // Gelen mesajları sayma
    $unreadCount = 0;
    if (file_exists($inLog)) {
        $inLines = file($inLog);
        foreach ($inLines as $line) {
            $json = trim(substr($line, 27)); // <PERSON><PERSON>h kısmını atla
            $data = json_decode($json, true);

            if ($data && isset($data['entry'][0]['changes'][0]['value']['messages'][0])) {
                $msgId = $data['entry'][0]['changes'][0]['value']['messages'][0]['id'] ?? '';

                // Eğer mesaj ID'si okunanlar listesinde yoksa sayacı artır
                if (!empty($msgId) && !in_array($msgId, $readIds)) {
                    $unreadCount++;
                }
            }
        }
    }

    echo json_encode([
        'success' => true,
        'count' => $unreadCount
    ]);
    exit;
}

// Varsayılan istatistikler
if ($action === 'summary') {
    $inLog = __DIR__ . '/whatsapp_incoming_log.txt';
    $outLog = __DIR__ . '/whatsapp_outgoing_log.txt';

    // Gelen ve giden mesajlar
    $inMessages = file_exists($inLog) ? file($inLog) : [];
    $outMessages = file_exists($outLog) ? file($outLog) : [];

    // Son 24 saat için mesajları sayma
    $last24h = 0;
    $now = time();
    $oneDayAgo = $now - (24 * 60 * 60);

    // Gelen mesajları kontrol et
    foreach ($inMessages as $line) {
        $dateStr = substr($line, 0, 26);
        $messageDate = strtotime($dateStr);
        if ($messageDate >= $oneDayAgo) {
            $last24h++;
        }
    }

    // Giden mesajları kontrol et
    foreach ($outMessages as $line) {
        $dateStr = substr($line, 0, 26);
        $messageDate = strtotime($dateStr);
        if ($messageDate >= $oneDayAgo) {
            $last24h++;
        }
    }

    // Aktif sohbetleri hesapla (farklı telefon numaralarından son 7 günde gelen mesajlar)
    $sevenDaysAgo = $now - (7 * 24 * 60 * 60);
    $activeChats = 0;
    $uniquePhones = [];

    foreach ($inMessages as $line) {
        $dateStr = substr($line, 0, 26);
        $messageDate = strtotime($dateStr);
        $json = trim(substr($line, 27));
        $data = json_decode($json, true);

        if ($messageDate >= $sevenDaysAgo && $data && isset($data['entry'][0]['changes'][0]['value']['messages'][0]['from'])) {
            $from = $data['entry'][0]['changes'][0]['value']['messages'][0]['from'];
            $uniquePhones[$from] = true;
        }
    }

    $activeChats = count($uniquePhones);

    // İstatistikleri döndür
    echo json_encode([
        'success' => true,
        'totalMessages' => count($inMessages) + count($outMessages),
        'messagesLast24h' => $last24h,
        'activeChats' => $activeChats,
        'deliveryRate' => 98 // Sabit bir oran (örnek amaçlı)
    ]);
    exit;
}

// Okunmamış mesaj sayacını sıfırla
if ($action === 'reset_unread_count') {
    $inLog = __DIR__ . '/whatsapp_incoming_log.txt';
    $readLog = __DIR__ . '/whatsapp_read_messages.txt';

    // Tüm gelen mesajları okundu olarak işaretle
    if (file_exists($inLog)) {
        $inLines = file($inLog);
        $readIds = [];

        // Mevcut okunan mesajları al
        if (file_exists($readLog)) {
            $existingReadLines = file($readLog);
            foreach ($existingReadLines as $line) {
                $readIds[] = trim($line);
            }
        }

        // Tüm gelen mesajların ID'lerini al ve okunanlar listesine ekle
        foreach ($inLines as $line) {
            $json = trim(substr($line, 27)); // Tarih kısmını atla
            $data = json_decode($json, true);

            if ($data && isset($data['entry'][0]['changes'][0]['value']['messages'][0])) {
                $msgId = $data['entry'][0]['changes'][0]['value']['messages'][0]['id'] ?? '';

                // Eğer mesaj ID'si okunanlar listesinde yoksa ekle
                if (!empty($msgId) && !in_array($msgId, $readIds)) {
                    $readIds[] = $msgId;
                }
            }
        }

        // Okunan mesajları dosyaya yaz
        file_put_contents($readLog, implode("\n", $readIds) . "\n");
    }

    echo json_encode([
        'success' => true,
        'message' => 'Okunmamış mesaj sayacı sıfırlandı'
    ]);
    exit;
}

// Geçersiz eylem
echo json_encode([
    'success' => false,
    'error' => 'Invalid action'
]);