import{j as e}from"./reactDnd-CIvPAkL_.js";import{l as i,T as a,Y as r}from"./antd-gS---Efz.js";import{g as l}from"./App-CRh63wQr.js";import"./vendor-CnpYymF8.js";import"./index-CA4FAjHu.js";import"./utils-CtuI0RRe.js";import"./charts-CXWFy-zF.js";const{Title:s,Paragraph:t}=a,j=()=>e.jsx("div",{className:"p-4",children:e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx(l,{style:{fontSize:24,marginRight:12}}),e.jsx(s,{level:3,style:{margin:0},children:"ETSY Ürünler"})]}),e.jsx(r,{message:"<PERSON><PERSON>yı Geliştirme",description:"Bu sayfa şu anda geliştirme aşamasındadır. ETSY'deki ürünlerin yönetimi, listelenmesi ve analizi burada yapılacaktır.",type:"info",showIcon:!0}),e.jsx(t,{className:"mt-4",children:"Bu sayfanın tamamlanacak özellikleri:"}),e.jsxs("ul",{className:"list-disc ml-8",children:[e.jsx("li",{children:"Ürün listesi ve arama özellikleri"}),e.jsx("li",{children:"Ürün detayları ve düzenleme"}),e.jsx("li",{children:"Satış istatistikleri"}),e.jsx("li",{children:"Stok takibi"}),e.jsx("li",{children:"Otomatik fiyatlandırma"}),e.jsx("li",{children:"Alıcı yorumları ve değerlendirmeleri"})]})]})});export{j as default};
