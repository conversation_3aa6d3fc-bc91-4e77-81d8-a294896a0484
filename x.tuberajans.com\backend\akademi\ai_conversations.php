<?php
header('Content-Type: application/json; charset=utf-8');
require_once __DIR__ . '/../config/config.php';
$pdo = $db_akademi;
$method = $_SERVER['REQUEST_METHOD'];
if ($method === 'GET') {
    $where = '';
    $params = [];
    if (isset($_GET['user_id'])) {
        $where = 'WHERE user_id = ?';
        $params[] = $_GET['user_id'];
    }
    $stmt = $pdo->prepare("SELECT * FROM ai_conversations $where ORDER BY created_at DESC LIMIT 100");
    $stmt->execute($params);
    $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo json_encode(['success' => true, 'data' => $data]);
    exit;
}
echo json_encode(['success' => false, 'message' => 'Geçersiz istek']); 