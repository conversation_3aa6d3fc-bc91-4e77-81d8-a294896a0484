<?php
ini_set('display_errors', 1);
error_reporting(E_ALL);
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/auth.php';

header('Content-Type: application/json; charset=utf-8');

$userId = requireAuthToken();
if (!$userId) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);
if (!$input) {
    echo json_encode(['success' => false, 'error' => 'Geçersiz veri']);
    exit;
}

// Gerekli alanlar
$etkinlik_adi = $input['etkinlik_adi'] ?? null;
$etkinlik_tipi = $input['etkinlik_tipi'] ?? 'pk_turnuva';
$baslangic_tarihi = $input['baslangic_tarihi'] ?? null;
$bitis_tarihi = $input['bitis_tarihi'] ?? null;
$kayit_uygunlugu = $input['kayit_uygunlugu'] ?? 'herkese_acik';
$puan_sistemi = $input['puan_sistemi'] ?? 'elmas';
$durum = $input['durum'] ?? 'aktif';
$aciklama = $input['aciklama'] ?? null;

if (!$etkinlik_adi || !$baslangic_tarihi || !$bitis_tarihi) {
    echo json_encode(['success' => false, 'error' => 'Eksik alanlar']);
    exit;
}

// Etkinliği ekle
$stmt = $db->prepare("INSERT INTO etkinlikler (etkinlik_adi, etkinlik_tipi, baslangic_tarihi, bitis_tarihi, kayit_uygunlugu, puan_sistemi, durum, aciklama) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
$result = $stmt->execute([
    $etkinlik_adi,
    $etkinlik_tipi,
    $baslangic_tarihi,
    $bitis_tarihi,
    $kayit_uygunlugu,
    $puan_sistemi,
    $durum,
    $aciklama
]);

if (!$result) {
    echo json_encode(['success' => false, 'error' => 'Etkinlik eklenemedi']);
    exit;
}

$etkinlik_id = $db->lastInsertId();

// PK eşleşmeleri varsa ekle
if (isset($input['eslesme_bilgileri']) && is_array($input['eslesme_bilgileri'])) {
    foreach ($input['eslesme_bilgileri'] as $eslesme) {
        $yayinci1_username = $eslesme['yayinci1_username'] ?? null;
        $yayinci2_username = $eslesme['yayinci2_username'] ?? null;
        $planlanan_zaman = $eslesme['mac_zamani'] ?? $baslangic_tarihi;
        if ($yayinci1_username && $yayinci2_username) {
            $stmt2 = $db->prepare("INSERT INTO pk_eslesmeleri (etkinlik_id, yayinci1_username, yayinci2_username, planlanan_zaman, round, durum) VALUES (?, ?, ?, ?, 1, 'bekleniyor')");
            $stmt2->execute([$etkinlik_id, $yayinci1_username, $yayinci2_username, $planlanan_zaman]);
        }
    }
}

echo json_encode(['success' => true, 'etkinlik_id' => $etkinlik_id]);
exit; 