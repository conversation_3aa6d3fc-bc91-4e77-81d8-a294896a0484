<?php
require_once __DIR__ . '/../config/config.php';
header('Content-Type: application/json');

$code = $_GET['code'] ?? '';
error_log('TikTok Token API - Code: ' . $code);
error_log('TikTok Token API - GET params: ' . json_encode($_GET));

if (!$code) {
    error_log('TikTok Token API - Code parametresi eksik');
    http_response_code(400);
    echo json_encode(['status' => 'error', 'message' => 'Code parametresi eksik']);
    exit;
}

$redirect_uri = 'https://akademi.tuberajans.com/backend/api/tiktok-callback.php';

// TikTok Access Token almak için POST isteği (cURL ile)
$url = 'https://open.tiktokapis.com/v2/oauth/token/';
$data = [
    'client_key' => $tiktok_client_key,
    'client_secret' => $tiktok_client_secret,
    'code' => urldecode($code),
    'grant_type' => 'authorization_code',
    'redirect_uri' => $redirect_uri
];

$ch = curl_init($url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 15);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    "Content-Type: application/x-www-form-urlencoded"
]);
$result = curl_exec($ch);
$curlError = curl_error($ch);
$curlInfo = curl_getinfo($ch);
curl_close($ch);

error_log('TikTok Token API - cURL Result: ' . ($result ?: 'false'));
error_log('TikTok Token API - cURL Error: ' . $curlError);
error_log('TikTok Token API - cURL Info: ' . json_encode($curlInfo));

if ($result === false) {
    error_log('TikTok Token API - cURL başarısız');
    http_response_code(502);
    echo json_encode([
        'status' => 'error',
        'message' => 'TikTok API bağlantı hatası (cURL)',
        'curl_error' => $curlError,
        'curl_info' => $curlInfo
    ]);
    exit;
}

// TikTok'tan gelen yanıtı işle
$data = json_decode($result, true);
error_log('TikTok Token API - Response: ' . $result);
error_log('TikTok Token API - Parsed Data: ' . json_encode($data));

if (isset($data['access_token'])) {
    error_log('TikTok Token API - Access token başarıyla alındı');
    http_response_code(200);
    echo json_encode($data);
} else {
    $tiktok_error_code = $data['error'] ?? ($data['code'] ?? null);
    $tiktok_error_message = $data['error_description'] ?? ($data['message'] ?? null);
    error_log('TikTok Token API - Access token alınamadı: ' . $tiktok_error_code . ' - ' . $tiktok_error_message);
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => 'TikTok access token alınamadı',
        'tiktok_response' => $data,
        'tiktok_error_code' => $tiktok_error_code,
        'tiktok_error_message' => $tiktok_error_message,
        'curl_info' => $curlInfo
    ]);
}