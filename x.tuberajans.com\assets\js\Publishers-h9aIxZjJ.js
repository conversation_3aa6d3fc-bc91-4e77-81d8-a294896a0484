import{j as D}from"./reactDnd-uQSTYBkW.js";import{r as xe,o as Ua}from"./vendor-CnpYymF8.js";import{h as At}from"./utils-CtuI0RRe.js";import{m as ds,j as ms,d as ps,n as gs,o as Wa,p as vs,q as _s,r as ja,V as De,l as Ha,h as Ts,s as Es}from"./App-BJoNc_gH.js";import{S as jr,A as Cr}from"./index-gEPTqZB-.js";import{o as ve,$ as Ga,k as ke,X as ws,Z as Ss,l as ys,M as Gt,s as As,t as Zr,I as rr,O as Fs,u as pn,p as Va,U as Cs,m as ks,y as Os,x as Oe,A as za}from"./antd-BfejY-CV.js";import{X as Is}from"./x-BlF-lTk7.js";import{c as Ds}from"./createLucideIcon-DxVmGoQf.js";import{R as Ns}from"./EllipsisOutlined-ZMwirapM.js";import{R as Ya}from"./UploadOutlined-DFFE-vGi.js";import{P as Rs}from"./plus-DjpIx7VF.js";import"./charts-6B1FLgFz.js";const Ps=[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]],Ls=Ds("mail",Ps);var An={};An.version="0.18.5";var Pi=1252,Ms=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],Li=function(e){Ms.indexOf(e)!=-1&&(Pi=e)};function Bs(){Li(1252)}var Zt=function(e){Li(e)};function bs(){Zt(1200),Bs()}var gn=function(t){return String.fromCharCode(t)},$a=function(t){return String.fromCharCode(t)},Xa,et="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function Qt(e){for(var t="",r=0,n=0,a=0,i=0,s=0,f=0,o=0,l=0;l<e.length;)r=e.charCodeAt(l++),i=r>>2,n=e.charCodeAt(l++),s=(r&3)<<4|n>>4,a=e.charCodeAt(l++),f=(n&15)<<2|a>>6,o=a&63,isNaN(n)?f=o=64:isNaN(a)&&(o=64),t+=et.charAt(i)+et.charAt(s)+et.charAt(f)+et.charAt(o);return t}function zr(e){var t="",r=0,n=0,a=0,i=0,s=0,f=0,o=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var l=0;l<e.length;)i=et.indexOf(e.charAt(l++)),s=et.indexOf(e.charAt(l++)),r=i<<2|s>>4,t+=String.fromCharCode(r),f=et.indexOf(e.charAt(l++)),n=(s&15)<<4|f>>2,f!==64&&(t+=String.fromCharCode(n)),o=et.indexOf(e.charAt(l++)),a=(f&3)<<6|o,o!==64&&(t+=String.fromCharCode(a));return t}var Ee=function(){return typeof Buffer!="undefined"&&typeof process!="undefined"&&typeof process.versions!="undefined"&&!!process.versions.node}(),$r=function(){if(typeof Buffer!="undefined"){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch(t){e=!0}return e?function(t,r){return r?new Buffer(t,r):new Buffer(t)}:Buffer.from.bind(Buffer)}return function(){}}();function ut(e){return Ee?Buffer.alloc?Buffer.alloc(e):new Buffer(e):typeof Uint8Array!="undefined"?new Uint8Array(e):new Array(e)}function Ka(e){return Ee?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):typeof Uint8Array!="undefined"?new Uint8Array(e):new Array(e)}var Dr=function(t){return Ee?$r(t,"binary"):t.split("").map(function(r){return r.charCodeAt(0)&255})};function Un(e){if(typeof ArrayBuffer=="undefined")return Dr(e);for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),n=0;n!=e.length;++n)r[n]=e.charCodeAt(n)&255;return t}function sn(e){if(Array.isArray(e))return e.map(function(n){return String.fromCharCode(n)}).join("");for(var t=[],r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function Us(e){if(typeof Uint8Array=="undefined")throw new Error("Unsupported");return new Uint8Array(e)}var Ke=Ee?function(e){return Buffer.concat(e.map(function(t){return Buffer.isBuffer(t)?t:$r(t)}))}:function(e){if(typeof Uint8Array!="undefined"){var t=0,r=0;for(t=0;t<e.length;++t)r+=e[t].length;var n=new Uint8Array(r),a=0;for(t=0,r=0;t<e.length;r+=a,++t)if(a=e[t].length,e[t]instanceof Uint8Array)n.set(e[t],r);else{if(typeof e[t]=="string")throw"wtf";n.set(new Uint8Array(e[t]),r)}return n}return[].concat.apply([],e.map(function(i){return Array.isArray(i)?i:[].slice.call(i)}))};function Ws(e){for(var t=[],r=0,n=e.length+250,a=ut(e.length+255),i=0;i<e.length;++i){var s=e.charCodeAt(i);if(s<128)a[r++]=s;else if(s<2048)a[r++]=192|s>>6&31,a[r++]=128|s&63;else if(s>=55296&&s<57344){s=(s&1023)+64;var f=e.charCodeAt(++i)&1023;a[r++]=240|s>>8&7,a[r++]=128|s>>2&63,a[r++]=128|f>>6&15|(s&3)<<4,a[r++]=128|f&63}else a[r++]=224|s>>12&15,a[r++]=128|s>>6&63,a[r++]=128|s&63;r>n&&(t.push(a.slice(0,r)),r=0,a=ut(65535),n=65530)}return t.push(a.slice(0,r)),Ke(t)}var Yt=/\u0000/g,vn=/[\u0001-\u0006]/g;function Dt(e){for(var t="",r=e.length-1;r>=0;)t+=e.charAt(r--);return t}function Nr(e,t){var r=""+e;return r.length>=t?r:Pe("0",t-r.length)+r}function va(e,t){var r=""+e;return r.length>=t?r:Pe(" ",t-r.length)+r}function Fn(e,t){var r=""+e;return r.length>=t?r:r+Pe(" ",t-r.length)}function js(e,t){var r=""+Math.round(e);return r.length>=t?r:Pe("0",t-r.length)+r}function Hs(e,t){var r=""+e;return r.length>=t?r:Pe("0",t-r.length)+r}var Ja=Math.pow(2,32);function Ft(e,t){if(e>Ja||e<-Ja)return js(e,t);var r=Math.round(e);return Hs(r,t)}function Cn(e,t){return t=t||0,e.length>=7+t&&(e.charCodeAt(t)|32)===103&&(e.charCodeAt(t+1)|32)===101&&(e.charCodeAt(t+2)|32)===110&&(e.charCodeAt(t+3)|32)===101&&(e.charCodeAt(t+4)|32)===114&&(e.charCodeAt(t+5)|32)===97&&(e.charCodeAt(t+6)|32)===108}var qa=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],aa=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];function Gs(e){return e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',e}var Le={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},Za={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},Vs={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function kn(e,t,r){for(var n=e<0?-1:1,a=e*n,i=0,s=1,f=0,o=1,l=0,c=0,m=Math.floor(a);l<t&&(m=Math.floor(a),f=m*s+i,c=m*l+o,!(a-m<5e-8));)a=1/(a-m),i=s,s=f,o=l,l=c;if(c>t&&(l>t?(c=o,f=i):(c=l,f=s)),!r)return[0,n*f,c];var d=Math.floor(n*f/c);return[d,n*f-d*c,c]}function _n(e,t,r){if(e>2958465||e<0)return null;var n=e|0,a=Math.floor(86400*(e-n)),i=0,s=[],f={D:n,T:a,u:86400*(e-n)-a,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(f.u)<1e-6&&(f.u=0),t&&t.date1904&&(n+=1462),f.u>.9999&&(f.u=0,++a==86400&&(f.T=a=0,++n,++f.D)),n===60)s=r?[1317,10,29]:[1900,2,29],i=3;else if(n===0)s=r?[1317,8,29]:[1900,1,0],i=6;else{n>60&&--n;var o=new Date(1900,0,1);o.setDate(o.getDate()+n-1),s=[o.getFullYear(),o.getMonth()+1,o.getDate()],i=o.getDay(),n<60&&(i=(i+6)%7),r&&(i=qs(o,s))}return f.y=s[0],f.m=s[1],f.d=s[2],f.S=a%60,a=Math.floor(a/60),f.M=a%60,a=Math.floor(a/60),f.H=a,f.q=i,f}var Mi=new Date(1899,11,31,0,0,0),zs=Mi.getTime(),Ys=new Date(1900,2,1,0,0,0);function Bi(e,t){var r=e.getTime();return t?r-=1461*24*60*60*1e3:e>=Ys&&(r+=24*60*60*1e3),(r-(zs+(e.getTimezoneOffset()-Mi.getTimezoneOffset())*6e4))/(24*60*60*1e3)}function _a(e){return e.indexOf(".")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function $s(e){return e.indexOf("E")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2")}function Xs(e){var t=e<0?12:11,r=_a(e.toFixed(12));return r.length<=t||(r=e.toPrecision(10),r.length<=t)?r:e.toExponential(5)}function Ks(e){var t=_a(e.toFixed(11));return t.length>(e<0?12:11)||t==="0"||t==="-0"?e.toPrecision(6):t}function Js(e){var t=Math.floor(Math.log(Math.abs(e))*Math.LOG10E),r;return t>=-4&&t<=-1?r=e.toPrecision(10+t):Math.abs(t)<=9?r=Xs(e):t===10?r=e.toFixed(10).substr(0,12):r=Ks(e),_a($s(r.toUpperCase()))}function da(e,t){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(e|0)===e?e.toString(10):Js(e);case"undefined":return"";case"object":if(e==null)return"";if(e instanceof Date)return tt(14,Bi(e,t&&t.date1904),t)}throw new Error("unsupported value in General format: "+e)}function qs(e,t){t[0]-=581;var r=e.getDay();return e<60&&(r=(r+6)%7),r}function Zs(e,t,r,n){var a="",i=0,s=0,f=r.y,o,l=0;switch(e){case 98:f=r.y+543;case 121:switch(t.length){case 1:case 2:o=f%100,l=2;break;default:o=f%1e4,l=4;break}break;case 109:switch(t.length){case 1:case 2:o=r.m,l=t.length;break;case 3:return aa[r.m-1][1];case 5:return aa[r.m-1][0];default:return aa[r.m-1][2]}break;case 100:switch(t.length){case 1:case 2:o=r.d,l=t.length;break;case 3:return qa[r.q][0];default:return qa[r.q][1]}break;case 104:switch(t.length){case 1:case 2:o=1+(r.H+11)%12,l=t.length;break;default:throw"bad hour format: "+t}break;case 72:switch(t.length){case 1:case 2:o=r.H,l=t.length;break;default:throw"bad hour format: "+t}break;case 77:switch(t.length){case 1:case 2:o=r.M,l=t.length;break;default:throw"bad minute format: "+t}break;case 115:if(t!="s"&&t!="ss"&&t!=".0"&&t!=".00"&&t!=".000")throw"bad second format: "+t;return r.u===0&&(t=="s"||t=="ss")?Nr(r.S,t.length):(n>=2?s=n===3?1e3:100:s=n===1?10:1,i=Math.round(s*(r.S+r.u)),i>=60*s&&(i=0),t==="s"?i===0?"0":""+i/s:(a=Nr(i,2+n),t==="ss"?a.substr(0,2):"."+a.substr(2,t.length-1)));case 90:switch(t){case"[h]":case"[hh]":o=r.D*24+r.H;break;case"[m]":case"[mm]":o=(r.D*24+r.H)*60+r.M;break;case"[s]":case"[ss]":o=((r.D*24+r.H)*60+r.M)*60+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+t}l=t.length===3?1:2;break;case 101:o=f,l=1;break}var c=l>0?Nr(o,l):"";return c}function rt(e){var t=3;if(e.length<=t)return e;for(var r=e.length%t,n=e.substr(0,r);r!=e.length;r+=t)n+=(n.length>0?",":"")+e.substr(r,t);return n}var bi=/%/g;function Qs(e,t,r){var n=t.replace(bi,""),a=t.length-n.length;return Hr(e,n,r*Math.pow(10,2*a))+Pe("%",a)}function ef(e,t,r){for(var n=t.length-1;t.charCodeAt(n-1)===44;)--n;return Hr(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}function Ui(e,t){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(t==0)return"0.0E+0";if(t<0)return"-"+Ui(e,-t);var a=e.indexOf(".");a===-1&&(a=e.indexOf("E"));var i=Math.floor(Math.log(t)*Math.LOG10E)%a;if(i<0&&(i+=a),r=(t/Math.pow(10,i)).toPrecision(n+1+(a+i)%a),r.indexOf("e")===-1){var s=Math.floor(Math.log(t)*Math.LOG10E);for(r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(s-r.length+i):r+="E+"+(s-i);r.substr(0,2)==="0.";)r=r.charAt(0)+r.substr(2,a)+"."+r.substr(2+a),r=r.replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(f,o,l,c){return o+l+c.substr(0,(a+i)%a)+"."+c.substr(i)+"E"})}else r=t.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}var Wi=/# (\?+)( ?)\/( ?)(\d+)/;function rf(e,t,r){var n=parseInt(e[4],10),a=Math.round(t*n),i=Math.floor(a/n),s=a-i*n,f=n;return r+(i===0?"":""+i)+" "+(s===0?Pe(" ",e[1].length+1+e[4].length):va(s,e[1].length)+e[2]+"/"+e[3]+Nr(f,e[4].length))}function tf(e,t,r){return r+(t===0?"":""+t)+Pe(" ",e[1].length+2+e[4].length)}var ji=/^#*0*\.([0#]+)/,Hi=/\).*[0#]/,Gi=/\(###\) ###\\?-####/;function sr(e){for(var t="",r,n=0;n!=e.length;++n)switch(r=e.charCodeAt(n)){case 35:break;case 63:t+=" ";break;case 48:t+="0";break;default:t+=String.fromCharCode(r)}return t}function Qa(e,t){var r=Math.pow(10,t);return""+Math.round(e*r)/r}function ei(e,t){var r=e-Math.floor(e),n=Math.pow(10,t);return t<(""+Math.round(r*n)).length?0:Math.round(r*n)}function nf(e,t){return t<(""+Math.round((e-Math.floor(e))*Math.pow(10,t))).length?1:0}function af(e){return e<2147483647&&e>-2147483648?""+(e>=0?e|0:e-1|0):""+Math.floor(e)}function wr(e,t,r){if(e.charCodeAt(0)===40&&!t.match(Hi)){var n=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?wr("n",n,r):"("+wr("n",n,-r)+")"}if(t.charCodeAt(t.length-1)===44)return ef(e,t,r);if(t.indexOf("%")!==-1)return Qs(e,t,r);if(t.indexOf("E")!==-1)return Ui(t,r);if(t.charCodeAt(0)===36)return"$"+wr(e,t.substr(t.charAt(1)==" "?2:1),r);var a,i,s,f,o=Math.abs(r),l=r<0?"-":"";if(t.match(/^00+$/))return l+Ft(o,t.length);if(t.match(/^[#?]+$/))return a=Ft(r,0),a==="0"&&(a=""),a.length>t.length?a:sr(t.substr(0,t.length-a.length))+a;if(i=t.match(Wi))return rf(i,o,l);if(t.match(/^#+0+$/))return l+Ft(o,t.length-t.indexOf("0"));if(i=t.match(ji))return a=Qa(r,i[1].length).replace(/^([^\.]+)$/,"$1."+sr(i[1])).replace(/\.$/,"."+sr(i[1])).replace(/\.(\d*)$/,function(v,u){return"."+u+Pe("0",sr(i[1]).length-u.length)}),t.indexOf("0.")!==-1?a:a.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),i=t.match(/^(0*)\.(#*)$/))return l+Qa(o,i[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".");if(i=t.match(/^#{1,3},##0(\.?)$/))return l+rt(Ft(o,0));if(i=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+wr(e,t,-r):rt(""+(Math.floor(r)+nf(r,i[1].length)))+"."+Nr(ei(r,i[1].length),i[1].length);if(i=t.match(/^#,#*,#0/))return wr(e,t.replace(/^#,#*,/,""),r);if(i=t.match(/^([0#]+)(\\?-([0#]+))+$/))return a=Dt(wr(e,t.replace(/[\\-]/g,""),r)),s=0,Dt(Dt(t.replace(/\\/g,"")).replace(/[0#]/g,function(v){return s<a.length?a.charAt(s++):v==="0"?"0":""}));if(t.match(Gi))return a=wr(e,"##########",r),"("+a.substr(0,3)+") "+a.substr(3,3)+"-"+a.substr(6);var c="";if(i=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(i[4].length,7),f=kn(o,Math.pow(10,s)-1,!1),a=""+l,c=Hr("n",i[1],f[1]),c.charAt(c.length-1)==" "&&(c=c.substr(0,c.length-1)+"0"),a+=c+i[2]+"/"+i[3],c=Fn(f[2],s),c.length<i[4].length&&(c=sr(i[4].substr(i[4].length-c.length))+c),a+=c,a;if(i=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(Math.max(i[1].length,i[4].length),7),f=kn(o,Math.pow(10,s)-1,!0),l+(f[0]||(f[1]?"":"0"))+" "+(f[1]?va(f[1],s)+i[2]+"/"+i[3]+Fn(f[2],s):Pe(" ",2*s+1+i[2].length+i[3].length));if(i=t.match(/^[#0?]+$/))return a=Ft(r,0),t.length<=a.length?a:sr(t.substr(0,t.length-a.length))+a;if(i=t.match(/^([#0?]+)\.([#0]+)$/)){a=""+r.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1"),s=a.indexOf(".");var m=t.indexOf(".")-s,d=t.length-a.length-m;return sr(t.substr(0,m)+a+t.substr(t.length-d))}if(i=t.match(/^00,000\.([#0]*0)$/))return s=ei(r,i[1].length),r<0?"-"+wr(e,t,-r):rt(af(r)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(v){return"00,"+(v.length<3?Nr(0,3-v.length):"")+v})+"."+Nr(s,i[1].length);switch(t){case"###,##0.00":return wr(e,"#,##0.00",r);case"###,###":case"##,###":case"#,###":var x=rt(Ft(o,0));return x!=="0"?l+x:"";case"###,###.00":return wr(e,"###,##0.00",r).replace(/^0\./,".");case"#,###.00":return wr(e,"#,##0.00",r).replace(/^0\./,".")}throw new Error("unsupported format |"+t+"|")}function sf(e,t,r){for(var n=t.length-1;t.charCodeAt(n-1)===44;)--n;return Hr(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}function ff(e,t,r){var n=t.replace(bi,""),a=t.length-n.length;return Hr(e,n,r*Math.pow(10,2*a))+Pe("%",a)}function Vi(e,t){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(t==0)return"0.0E+0";if(t<0)return"-"+Vi(e,-t);var a=e.indexOf(".");a===-1&&(a=e.indexOf("E"));var i=Math.floor(Math.log(t)*Math.LOG10E)%a;if(i<0&&(i+=a),r=(t/Math.pow(10,i)).toPrecision(n+1+(a+i)%a),!r.match(/[Ee]/)){var s=Math.floor(Math.log(t)*Math.LOG10E);r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(s-r.length+i):r+="E+"+(s-i),r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(f,o,l,c){return o+l+c.substr(0,(a+i)%a)+"."+c.substr(i)+"E"})}else r=t.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}function Lr(e,t,r){if(e.charCodeAt(0)===40&&!t.match(Hi)){var n=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?Lr("n",n,r):"("+Lr("n",n,-r)+")"}if(t.charCodeAt(t.length-1)===44)return sf(e,t,r);if(t.indexOf("%")!==-1)return ff(e,t,r);if(t.indexOf("E")!==-1)return Vi(t,r);if(t.charCodeAt(0)===36)return"$"+Lr(e,t.substr(t.charAt(1)==" "?2:1),r);var a,i,s,f,o=Math.abs(r),l=r<0?"-":"";if(t.match(/^00+$/))return l+Nr(o,t.length);if(t.match(/^[#?]+$/))return a=""+r,r===0&&(a=""),a.length>t.length?a:sr(t.substr(0,t.length-a.length))+a;if(i=t.match(Wi))return tf(i,o,l);if(t.match(/^#+0+$/))return l+Nr(o,t.length-t.indexOf("0"));if(i=t.match(ji))return a=(""+r).replace(/^([^\.]+)$/,"$1."+sr(i[1])).replace(/\.$/,"."+sr(i[1])),a=a.replace(/\.(\d*)$/,function(v,u){return"."+u+Pe("0",sr(i[1]).length-u.length)}),t.indexOf("0.")!==-1?a:a.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),i=t.match(/^(0*)\.(#*)$/))return l+(""+o).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".");if(i=t.match(/^#{1,3},##0(\.?)$/))return l+rt(""+o);if(i=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+Lr(e,t,-r):rt(""+r)+"."+Pe("0",i[1].length);if(i=t.match(/^#,#*,#0/))return Lr(e,t.replace(/^#,#*,/,""),r);if(i=t.match(/^([0#]+)(\\?-([0#]+))+$/))return a=Dt(Lr(e,t.replace(/[\\-]/g,""),r)),s=0,Dt(Dt(t.replace(/\\/g,"")).replace(/[0#]/g,function(v){return s<a.length?a.charAt(s++):v==="0"?"0":""}));if(t.match(Gi))return a=Lr(e,"##########",r),"("+a.substr(0,3)+") "+a.substr(3,3)+"-"+a.substr(6);var c="";if(i=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(i[4].length,7),f=kn(o,Math.pow(10,s)-1,!1),a=""+l,c=Hr("n",i[1],f[1]),c.charAt(c.length-1)==" "&&(c=c.substr(0,c.length-1)+"0"),a+=c+i[2]+"/"+i[3],c=Fn(f[2],s),c.length<i[4].length&&(c=sr(i[4].substr(i[4].length-c.length))+c),a+=c,a;if(i=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(Math.max(i[1].length,i[4].length),7),f=kn(o,Math.pow(10,s)-1,!0),l+(f[0]||(f[1]?"":"0"))+" "+(f[1]?va(f[1],s)+i[2]+"/"+i[3]+Fn(f[2],s):Pe(" ",2*s+1+i[2].length+i[3].length));if(i=t.match(/^[#0?]+$/))return a=""+r,t.length<=a.length?a:sr(t.substr(0,t.length-a.length))+a;if(i=t.match(/^([#0]+)\.([#0]+)$/)){a=""+r.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1"),s=a.indexOf(".");var m=t.indexOf(".")-s,d=t.length-a.length-m;return sr(t.substr(0,m)+a+t.substr(t.length-d))}if(i=t.match(/^00,000\.([#0]*0)$/))return r<0?"-"+Lr(e,t,-r):rt(""+r).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(v){return"00,"+(v.length<3?Nr(0,3-v.length):"")+v})+"."+Nr(0,i[1].length);switch(t){case"###,###":case"##,###":case"#,###":var x=rt(""+o);return x!=="0"?l+x:"";default:if(t.match(/\.[0#?]*$/))return Lr(e,t.slice(0,t.lastIndexOf(".")),r)+sr(t.slice(t.lastIndexOf(".")))}throw new Error("unsupported format |"+t+"|")}function Hr(e,t,r){return(r|0)===r?Lr(e,t,r):wr(e,t,r)}function lf(e){for(var t=[],r=!1,n=0,a=0;n<e.length;++n)switch(e.charCodeAt(n)){case 34:r=!r;break;case 95:case 42:case 92:++n;break;case 59:t[t.length]=e.substr(a,n-a),a=n+1}if(t[t.length]=e.substr(a),r===!0)throw new Error("Format |"+e+"| unterminated string ");return t}var zi=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function Yi(e){for(var t=0,r="",n="";t<e.length;)switch(r=e.charAt(t)){case"G":Cn(e,t)&&(t+=6),t++;break;case'"':for(;e.charCodeAt(++t)!==34&&t<e.length;);++t;break;case"\\":t+=2;break;case"_":t+=2;break;case"@":++t;break;case"B":case"b":if(e.charAt(t+1)==="1"||e.charAt(t+1)==="2")return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if(e.substr(t,3).toUpperCase()==="A/P"||e.substr(t,5).toUpperCase()==="AM/PM"||e.substr(t,5).toUpperCase()==="上午/下午")return!0;++t;break;case"[":for(n=r;e.charAt(t++)!=="]"&&t<e.length;)n+=e.charAt(t);if(n.match(zi))return!0;break;case".":case"0":case"#":for(;t<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++t))>-1||r=="\\"&&e.charAt(t+1)=="-"&&"0#".indexOf(e.charAt(t+2))>-1););break;case"?":for(;e.charAt(++t)===r;);break;case"*":++t,(e.charAt(t)==" "||e.charAt(t)=="*")&&++t;break;case"(":case")":++t;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;t<e.length&&"0123456789".indexOf(e.charAt(++t))>-1;);break;case" ":++t;break;default:++t;break}return!1}function of(e,t,r,n){for(var a=[],i="",s=0,f="",o="t",l,c,m,d="H";s<e.length;)switch(f=e.charAt(s)){case"G":if(!Cn(e,s))throw new Error("unrecognized character "+f+" in "+e);a[a.length]={t:"G",v:"General"},s+=7;break;case'"':for(i="";(m=e.charCodeAt(++s))!==34&&s<e.length;)i+=String.fromCharCode(m);a[a.length]={t:"t",v:i},++s;break;case"\\":var x=e.charAt(++s),v=x==="("||x===")"?x:"t";a[a.length]={t:v,v:x},++s;break;case"_":a[a.length]={t:"t",v:" "},s+=2;break;case"@":a[a.length]={t:"T",v:t},++s;break;case"B":case"b":if(e.charAt(s+1)==="1"||e.charAt(s+1)==="2"){if(l==null&&(l=_n(t,r,e.charAt(s+1)==="2"),l==null))return"";a[a.length]={t:"X",v:e.substr(s,2)},o=f,s+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":f=f.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(t<0||l==null&&(l=_n(t,r),l==null))return"";for(i=f;++s<e.length&&e.charAt(s).toLowerCase()===f;)i+=f;f==="m"&&o.toLowerCase()==="h"&&(f="M"),f==="h"&&(f=d),a[a.length]={t:f,v:i},o=f;break;case"A":case"a":case"上":var u={t:f,v:f};if(l==null&&(l=_n(t,r)),e.substr(s,3).toUpperCase()==="A/P"?(l!=null&&(u.v=l.H>=12?"P":"A"),u.t="T",d="h",s+=3):e.substr(s,5).toUpperCase()==="AM/PM"?(l!=null&&(u.v=l.H>=12?"PM":"AM"),u.t="T",s+=5,d="h"):e.substr(s,5).toUpperCase()==="上午/下午"?(l!=null&&(u.v=l.H>=12?"下午":"上午"),u.t="T",s+=5,d="h"):(u.t="t",++s),l==null&&u.t==="T")return"";a[a.length]=u,o=f;break;case"[":for(i=f;e.charAt(s++)!=="]"&&s<e.length;)i+=e.charAt(s);if(i.slice(-1)!=="]")throw'unterminated "[" block: |'+i+"|";if(i.match(zi)){if(l==null&&(l=_n(t,r),l==null))return"";a[a.length]={t:"Z",v:i.toLowerCase()},o=i.charAt(1)}else i.indexOf("$")>-1&&(i=(i.match(/\$([^-\[\]]*)/)||[])[1]||"$",Yi(e)||(a[a.length]={t:"t",v:i}));break;case".":if(l!=null){for(i=f;++s<e.length&&(f=e.charAt(s))==="0";)i+=f;a[a.length]={t:"s",v:i};break}case"0":case"#":for(i=f;++s<e.length&&"0#?.,E+-%".indexOf(f=e.charAt(s))>-1;)i+=f;a[a.length]={t:"n",v:i};break;case"?":for(i=f;e.charAt(++s)===f;)i+=f;a[a.length]={t:f,v:i},o=f;break;case"*":++s,(e.charAt(s)==" "||e.charAt(s)=="*")&&++s;break;case"(":case")":a[a.length]={t:n===1?"t":f,v:f},++s;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(i=f;s<e.length&&"0123456789".indexOf(e.charAt(++s))>-1;)i+=e.charAt(s);a[a.length]={t:"D",v:i};break;case" ":a[a.length]={t:f,v:f},++s;break;case"$":a[a.length]={t:"t",v:"$"},++s;break;default:if(",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(f)===-1)throw new Error("unrecognized character "+f+" in "+e);a[a.length]={t:"t",v:f},++s;break}var _=0,R=0,P;for(s=a.length-1,o="t";s>=0;--s)switch(a[s].t){case"h":case"H":a[s].t=d,o="h",_<1&&(_=1);break;case"s":(P=a[s].v.match(/\.0+$/))&&(R=Math.max(R,P[0].length-1)),_<3&&(_=3);case"d":case"y":case"M":case"e":o=a[s].t;break;case"m":o==="s"&&(a[s].t="M",_<2&&(_=2));break;case"X":break;case"Z":_<1&&a[s].v.match(/[Hh]/)&&(_=1),_<2&&a[s].v.match(/[Mm]/)&&(_=2),_<3&&a[s].v.match(/[Ss]/)&&(_=3)}switch(_){case 0:break;case 1:l.u>=.5&&(l.u=0,++l.S),l.S>=60&&(l.S=0,++l.M),l.M>=60&&(l.M=0,++l.H);break;case 2:l.u>=.5&&(l.u=0,++l.S),l.S>=60&&(l.S=0,++l.M);break}var O="",j;for(s=0;s<a.length;++s)switch(a[s].t){case"t":case"T":case" ":case"D":break;case"X":a[s].v="",a[s].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":a[s].v=Zs(a[s].t.charCodeAt(0),a[s].v,l,R),a[s].t="t";break;case"n":case"?":for(j=s+1;a[j]!=null&&((f=a[j].t)==="?"||f==="D"||(f===" "||f==="t")&&a[j+1]!=null&&(a[j+1].t==="?"||a[j+1].t==="t"&&a[j+1].v==="/")||a[s].t==="("&&(f===" "||f==="n"||f===")")||f==="t"&&(a[j].v==="/"||a[j].v===" "&&a[j+1]!=null&&a[j+1].t=="?"));)a[s].v+=a[j].v,a[j]={v:"",t:";"},++j;O+=a[s].v,s=j-1;break;case"G":a[s].t="t",a[s].v=da(t,r);break}var Q="",ne,k;if(O.length>0){O.charCodeAt(0)==40?(ne=t<0&&O.charCodeAt(0)===45?-t:t,k=Hr("n",O,ne)):(ne=t<0&&n>1?-t:t,k=Hr("n",O,ne),ne<0&&a[0]&&a[0].t=="t"&&(k=k.substr(1),a[0].v="-"+a[0].v)),j=k.length-1;var z=a.length;for(s=0;s<a.length;++s)if(a[s]!=null&&a[s].t!="t"&&a[s].v.indexOf(".")>-1){z=s;break}var W=a.length;if(z===a.length&&k.indexOf("E")===-1){for(s=a.length-1;s>=0;--s)a[s]==null||"n?".indexOf(a[s].t)===-1||(j>=a[s].v.length-1?(j-=a[s].v.length,a[s].v=k.substr(j+1,a[s].v.length)):j<0?a[s].v="":(a[s].v=k.substr(0,j+1),j=-1),a[s].t="t",W=s);j>=0&&W<a.length&&(a[W].v=k.substr(0,j+1)+a[W].v)}else if(z!==a.length&&k.indexOf("E")===-1){for(j=k.indexOf(".")-1,s=z;s>=0;--s)if(!(a[s]==null||"n?".indexOf(a[s].t)===-1)){for(c=a[s].v.indexOf(".")>-1&&s===z?a[s].v.indexOf(".")-1:a[s].v.length-1,Q=a[s].v.substr(c+1);c>=0;--c)j>=0&&(a[s].v.charAt(c)==="0"||a[s].v.charAt(c)==="#")&&(Q=k.charAt(j--)+Q);a[s].v=Q,a[s].t="t",W=s}for(j>=0&&W<a.length&&(a[W].v=k.substr(0,j+1)+a[W].v),j=k.indexOf(".")+1,s=z;s<a.length;++s)if(!(a[s]==null||"n?(".indexOf(a[s].t)===-1&&s!==z)){for(c=a[s].v.indexOf(".")>-1&&s===z?a[s].v.indexOf(".")+1:0,Q=a[s].v.substr(0,c);c<a[s].v.length;++c)j<k.length&&(Q+=k.charAt(j++));a[s].v=Q,a[s].t="t",W=s}}}for(s=0;s<a.length;++s)a[s]!=null&&"n?".indexOf(a[s].t)>-1&&(ne=n>1&&t<0&&s>0&&a[s-1].v==="-"?-t:t,a[s].v=Hr(a[s].t,a[s].v,ne),a[s].t="t");var X="";for(s=0;s!==a.length;++s)a[s]!=null&&(X+=a[s].v);return X}var ri=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function ti(e,t){if(t==null)return!1;var r=parseFloat(t[2]);switch(t[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0;break}return!1}function cf(e,t){var r=lf(e),n=r.length,a=r[n-1].indexOf("@");if(n<4&&a>-1&&--n,r.length>4)throw new Error("cannot find right format for |"+r.join("|")+"|");if(typeof t!="number")return[4,r.length===4||a>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=a>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=a>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=a>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"];break}var i=t>0?r[0]:t<0?r[1]:r[2];if(r[0].indexOf("[")===-1&&r[1].indexOf("[")===-1)return[n,i];if(r[0].match(/\[[=<>]/)!=null||r[1].match(/\[[=<>]/)!=null){var s=r[0].match(ri),f=r[1].match(ri);return ti(t,s)?[n,r[0]]:ti(t,f)?[n,r[1]]:[n,r[s!=null&&f!=null?2:1]]}return[n,i]}function tt(e,t,r){r==null&&(r={});var n="";switch(typeof e){case"string":e=="m/d/yy"&&r.dateNF?n=r.dateNF:n=e;break;case"number":e==14&&r.dateNF?n=r.dateNF:n=(r.table!=null?r.table:Le)[e],n==null&&(n=r.table&&r.table[Za[e]]||Le[Za[e]]),n==null&&(n=Vs[e]||"General");break}if(Cn(n,0))return da(t,r);t instanceof Date&&(t=Bi(t,r.date1904));var a=cf(n,t);if(Cn(a[1]))return da(t,r);if(t===!0)t="TRUE";else if(t===!1)t="FALSE";else if(t===""||t==null)return"";return of(a[1],t,r,a[0])}function $i(e,t){if(typeof t!="number"){t=+t||-1;for(var r=0;r<392;++r){if(Le[r]==null){t<0&&(t=r);continue}if(Le[r]==e){t=r;break}}t<0&&(t=391)}return Le[t]=e,t}function Wn(e){for(var t=0;t!=392;++t)e[t]!==void 0&&$i(e[t],t)}function jn(){Le=Gs()}var Xi=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g;function hf(e){var t=typeof e=="number"?Le[e]:e;return t=t.replace(Xi,"(\\d+)"),new RegExp("^"+t+"$")}function uf(e,t,r){var n=-1,a=-1,i=-1,s=-1,f=-1,o=-1;(t.match(Xi)||[]).forEach(function(m,d){var x=parseInt(r[d+1],10);switch(m.toLowerCase().charAt(0)){case"y":n=x;break;case"d":i=x;break;case"h":s=x;break;case"s":o=x;break;case"m":s>=0?f=x:a=x;break}}),o>=0&&f==-1&&a>=0&&(f=a,a=-1);var l=(""+(n>=0?n:new Date().getFullYear())).slice(-4)+"-"+("00"+(a>=1?a:1)).slice(-2)+"-"+("00"+(i>=1?i:1)).slice(-2);l.length==7&&(l="0"+l),l.length==8&&(l="20"+l);var c=("00"+(s>=0?s:0)).slice(-2)+":"+("00"+(f>=0?f:0)).slice(-2)+":"+("00"+(o>=0?o:0)).slice(-2);return s==-1&&f==-1&&o==-1?l:n==-1&&a==-1&&i==-1?c:l+"T"+c}var xf=function(){var e={};e.version="1.2.0";function t(){for(var k=0,z=new Array(256),W=0;W!=256;++W)k=W,k=k&1?-306674912^k>>>1:k>>>1,k=k&1?-306674912^k>>>1:k>>>1,k=k&1?-306674912^k>>>1:k>>>1,k=k&1?-306674912^k>>>1:k>>>1,k=k&1?-306674912^k>>>1:k>>>1,k=k&1?-306674912^k>>>1:k>>>1,k=k&1?-306674912^k>>>1:k>>>1,k=k&1?-306674912^k>>>1:k>>>1,z[W]=k;return typeof Int32Array!="undefined"?new Int32Array(z):z}var r=t();function n(k){var z=0,W=0,X=0,J=typeof Int32Array!="undefined"?new Int32Array(4096):new Array(4096);for(X=0;X!=256;++X)J[X]=k[X];for(X=0;X!=256;++X)for(W=k[X],z=256+X;z<4096;z+=256)W=J[z]=W>>>8^k[W&255];var Z=[];for(X=1;X!=16;++X)Z[X-1]=typeof Int32Array!="undefined"?J.subarray(X*256,X*256+256):J.slice(X*256,X*256+256);return Z}var a=n(r),i=a[0],s=a[1],f=a[2],o=a[3],l=a[4],c=a[5],m=a[6],d=a[7],x=a[8],v=a[9],u=a[10],_=a[11],R=a[12],P=a[13],O=a[14];function j(k,z){for(var W=z^-1,X=0,J=k.length;X<J;)W=W>>>8^r[(W^k.charCodeAt(X++))&255];return~W}function Q(k,z){for(var W=z^-1,X=k.length-15,J=0;J<X;)W=O[k[J++]^W&255]^P[k[J++]^W>>8&255]^R[k[J++]^W>>16&255]^_[k[J++]^W>>>24]^u[k[J++]]^v[k[J++]]^x[k[J++]]^d[k[J++]]^m[k[J++]]^c[k[J++]]^l[k[J++]]^o[k[J++]]^f[k[J++]]^s[k[J++]]^i[k[J++]]^r[k[J++]];for(X+=15;J<X;)W=W>>>8^r[(W^k[J++])&255];return~W}function ne(k,z){for(var W=z^-1,X=0,J=k.length,Z=0,ae=0;X<J;)Z=k.charCodeAt(X++),Z<128?W=W>>>8^r[(W^Z)&255]:Z<2048?(W=W>>>8^r[(W^(192|Z>>6&31))&255],W=W>>>8^r[(W^(128|Z&63))&255]):Z>=55296&&Z<57344?(Z=(Z&1023)+64,ae=k.charCodeAt(X++)&1023,W=W>>>8^r[(W^(240|Z>>8&7))&255],W=W>>>8^r[(W^(128|Z>>2&63))&255],W=W>>>8^r[(W^(128|ae>>6&15|(Z&3)<<4))&255],W=W>>>8^r[(W^(128|ae&63))&255]):(W=W>>>8^r[(W^(224|Z>>12&15))&255],W=W>>>8^r[(W^(128|Z>>6&63))&255],W=W>>>8^r[(W^(128|Z&63))&255]);return~W}return e.table=r,e.bstr=j,e.buf=Q,e.str=ne,e}(),Ce=function(){var t={};t.version="1.2.1";function r(h,T){for(var p=h.split("/"),g=T.split("/"),E=0,w=0,M=Math.min(p.length,g.length);E<M;++E){if(w=p[E].length-g[E].length)return w;if(p[E]!=g[E])return p[E]<g[E]?-1:1}return p.length-g.length}function n(h){if(h.charAt(h.length-1)=="/")return h.slice(0,-1).indexOf("/")===-1?h:n(h.slice(0,-1));var T=h.lastIndexOf("/");return T===-1?h:h.slice(0,T+1)}function a(h){if(h.charAt(h.length-1)=="/")return a(h.slice(0,-1));var T=h.lastIndexOf("/");return T===-1?h:h.slice(T+1)}function i(h,T){typeof T=="string"&&(T=new Date(T));var p=T.getHours();p=p<<6|T.getMinutes(),p=p<<5|T.getSeconds()>>>1,h.write_shift(2,p);var g=T.getFullYear()-1980;g=g<<4|T.getMonth()+1,g=g<<5|T.getDate(),h.write_shift(2,g)}function s(h){var T=h.read_shift(2)&65535,p=h.read_shift(2)&65535,g=new Date,E=p&31;p>>>=5;var w=p&15;p>>>=4,g.setMilliseconds(0),g.setFullYear(p+1980),g.setMonth(w-1),g.setDate(E);var M=T&31;T>>>=5;var G=T&63;return T>>>=6,g.setHours(T),g.setMinutes(G),g.setSeconds(M<<1),g}function f(h){dr(h,0);for(var T={},p=0;h.l<=h.length-4;){var g=h.read_shift(2),E=h.read_shift(2),w=h.l+E,M={};switch(g){case 21589:p=h.read_shift(1),p&1&&(M.mtime=h.read_shift(4)),E>5&&(p&2&&(M.atime=h.read_shift(4)),p&4&&(M.ctime=h.read_shift(4))),M.mtime&&(M.mt=new Date(M.mtime*1e3));break}h.l=w,T[g]=M}return T}var o;function l(){return o||(o={})}function c(h,T){if(h[0]==80&&h[1]==75)return br(h,T);if((h[0]|32)==109&&(h[1]|32)==105)return qn(h,T);if(h.length<512)throw new Error("CFB file size "+h.length+" < 512");var p=3,g=512,E=0,w=0,M=0,G=0,N=0,B=[],U=h.slice(0,512);dr(U,0);var q=m(U);switch(p=q[0],p){case 3:g=512;break;case 4:g=4096;break;case 0:if(q[1]==0)return br(h,T);default:throw new Error("Major Version: Expected 3 or 4 saw "+p)}g!==512&&(U=h.slice(0,g),dr(U,28));var S=h.slice(0,g);d(U,p);var A=U.read_shift(4,"i");if(p===3&&A!==0)throw new Error("# Directory Sectors: Expected 0 saw "+A);U.l+=4,M=U.read_shift(4,"i"),U.l+=4,U.chk("00100000","Mini Stream Cutoff Size: "),G=U.read_shift(4,"i"),E=U.read_shift(4,"i"),N=U.read_shift(4,"i"),w=U.read_shift(4,"i");for(var I=-1,L=0;L<109&&(I=U.read_shift(4,"i"),!(I<0));++L)B[L]=I;var V=x(h,g);_(N,w,V,g,B);var te=P(V,M,B,g);te[M].name="!Directory",E>0&&G!==ae&&(te[G].name="!MiniFAT"),te[B[0]].name="!FAT",te.fat_addrs=B,te.ssz=g;var oe={},he=[],Fe=[],er=[];O(M,te,V,he,E,oe,Fe,G),v(Fe,er,he),he.shift();var Er={FileIndex:Fe,FullPaths:er};return T&&T.raw&&(Er.raw={header:S,sectors:V}),Er}function m(h){if(h[h.l]==80&&h[h.l+1]==75)return[0,0];h.chk(pe,"Header Signature: "),h.l+=16;var T=h.read_shift(2,"u");return[h.read_shift(2,"u"),T]}function d(h,T){var p=9;switch(h.l+=2,p=h.read_shift(2)){case 9:if(T!=3)throw new Error("Sector Shift: Expected 9 saw "+p);break;case 12:if(T!=4)throw new Error("Sector Shift: Expected 12 saw "+p);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+p)}h.chk("0600","Mini Sector Shift: "),h.chk("000000000000","Reserved: ")}function x(h,T){for(var p=Math.ceil(h.length/T)-1,g=[],E=1;E<p;++E)g[E-1]=h.slice(E*T,(E+1)*T);return g[p-1]=h.slice(p*T),g}function v(h,T,p){for(var g=0,E=0,w=0,M=0,G=0,N=p.length,B=[],U=[];g<N;++g)B[g]=U[g]=g,T[g]=p[g];for(;G<U.length;++G)g=U[G],E=h[g].L,w=h[g].R,M=h[g].C,B[g]===g&&(E!==-1&&B[E]!==E&&(B[g]=B[E]),w!==-1&&B[w]!==w&&(B[g]=B[w])),M!==-1&&(B[M]=g),E!==-1&&g!=B[g]&&(B[E]=B[g],U.lastIndexOf(E)<G&&U.push(E)),w!==-1&&g!=B[g]&&(B[w]=B[g],U.lastIndexOf(w)<G&&U.push(w));for(g=1;g<N;++g)B[g]===g&&(w!==-1&&B[w]!==w?B[g]=B[w]:E!==-1&&B[E]!==E&&(B[g]=B[E]));for(g=1;g<N;++g)if(h[g].type!==0){if(G=g,G!=B[G])do G=B[G],T[g]=T[G]+"/"+T[g];while(G!==0&&B[G]!==-1&&G!=B[G]);B[g]=-1}for(T[0]+="/",g=1;g<N;++g)h[g].type!==2&&(T[g]+="/")}function u(h,T,p){for(var g=h.start,E=h.size,w=[],M=g;p&&E>0&&M>=0;)w.push(T.slice(M*Z,M*Z+Z)),E-=Z,M=ct(p,M*4);return w.length===0?H(0):Ke(w).slice(0,h.size)}function _(h,T,p,g,E){var w=ae;if(h===ae){if(T!==0)throw new Error("DIFAT chain shorter than expected")}else if(h!==-1){var M=p[h],G=(g>>>2)-1;if(!M)return;for(var N=0;N<G&&(w=ct(M,N*4))!==ae;++N)E.push(w);_(ct(M,g-4),T-1,p,g,E)}}function R(h,T,p,g,E){var w=[],M=[];E||(E=[]);var G=g-1,N=0,B=0;for(N=T;N>=0;){E[N]=!0,w[w.length]=N,M.push(h[N]);var U=p[Math.floor(N*4/g)];if(B=N*4&G,g<4+B)throw new Error("FAT boundary crossed: "+N+" 4 "+g);if(!h[U])break;N=ct(h[U],B)}return{nodes:w,data:ci([M])}}function P(h,T,p,g){var E=h.length,w=[],M=[],G=[],N=[],B=g-1,U=0,q=0,S=0,A=0;for(U=0;U<E;++U)if(G=[],S=U+T,S>=E&&(S-=E),!M[S]){N=[];var I=[];for(q=S;q>=0;){I[q]=!0,M[q]=!0,G[G.length]=q,N.push(h[q]);var L=p[Math.floor(q*4/g)];if(A=q*4&B,g<4+A)throw new Error("FAT boundary crossed: "+q+" 4 "+g);if(!h[L]||(q=ct(h[L],A),I[q]))break}w[S]={nodes:G,data:ci([N])}}return w}function O(h,T,p,g,E,w,M,G){for(var N=0,B=g.length?2:0,U=T[h].data,q=0,S=0,A;q<U.length;q+=128){var I=U.slice(q,q+128);dr(I,64),S=I.read_shift(2),A=ya(I,0,S-B),g.push(A);var L={name:A,type:I.read_shift(1),color:I.read_shift(1),L:I.read_shift(4,"i"),R:I.read_shift(4,"i"),C:I.read_shift(4,"i"),clsid:I.read_shift(16),state:I.read_shift(4,"i"),start:0,size:0},V=I.read_shift(2)+I.read_shift(2)+I.read_shift(2)+I.read_shift(2);V!==0&&(L.ct=j(I,I.l-8));var te=I.read_shift(2)+I.read_shift(2)+I.read_shift(2)+I.read_shift(2);te!==0&&(L.mt=j(I,I.l-8)),L.start=I.read_shift(4,"i"),L.size=I.read_shift(4,"i"),L.size<0&&L.start<0&&(L.size=L.type=0,L.start=ae,L.name=""),L.type===5?(N=L.start,E>0&&N!==ae&&(T[N].name="!StreamData")):L.size>=4096?(L.storage="fat",T[L.start]===void 0&&(T[L.start]=R(p,L.start,T.fat_addrs,T.ssz)),T[L.start].name=L.name,L.content=T[L.start].data.slice(0,L.size)):(L.storage="minifat",L.size<0?L.size=0:N!==ae&&L.start!==ae&&T[N]&&(L.content=u(L,T[N].data,(T[G]||{}).data))),L.content&&dr(L.content,0),w[A]=L,M.push(L)}}function j(h,T){return new Date((pr(h,T+4)/1e7*Math.pow(2,32)+pr(h,T)/1e7-11644473600)*1e3)}function Q(h,T){return l(),c(o.readFileSync(h),T)}function ne(h,T){var p=T&&T.type;switch(p||Ee&&Buffer.isBuffer(h)&&(p="buffer"),p||"base64"){case"file":return Q(h,T);case"base64":return c(Dr(zr(h)),T);case"binary":return c(Dr(h),T)}return c(h,T)}function k(h,T){var p=T||{},g=p.root||"Root Entry";if(h.FullPaths||(h.FullPaths=[]),h.FileIndex||(h.FileIndex=[]),h.FullPaths.length!==h.FileIndex.length)throw new Error("inconsistent CFB structure");h.FullPaths.length===0&&(h.FullPaths[0]=g+"/",h.FileIndex[0]={name:g,type:5}),p.CLSID&&(h.FileIndex[0].clsid=p.CLSID),z(h)}function z(h){var T="Sh33tJ5";if(!Ce.find(h,"/"+T)){var p=H(4);p[0]=55,p[1]=p[3]=50,p[2]=54,h.FileIndex.push({name:T,type:2,content:p,size:4,L:69,R:69,C:69}),h.FullPaths.push(h.FullPaths[0]+T),W(h)}}function W(h,T){k(h);for(var p=!1,g=!1,E=h.FullPaths.length-1;E>=0;--E){var w=h.FileIndex[E];switch(w.type){case 0:g?p=!0:(h.FileIndex.pop(),h.FullPaths.pop());break;case 1:case 2:case 5:g=!0,isNaN(w.R*w.L*w.C)&&(p=!0),w.R>-1&&w.L>-1&&w.R==w.L&&(p=!0);break;default:p=!0;break}}if(!(!p&&!T)){var M=new Date(1987,1,19),G=0,N=Object.create?Object.create(null):{},B=[];for(E=0;E<h.FullPaths.length;++E)N[h.FullPaths[E]]=!0,h.FileIndex[E].type!==0&&B.push([h.FullPaths[E],h.FileIndex[E]]);for(E=0;E<B.length;++E){var U=n(B[E][0]);g=N[U],g||(B.push([U,{name:a(U).replace("/",""),type:1,clsid:Me,ct:M,mt:M,content:null}]),N[U]=!0)}for(B.sort(function(A,I){return r(A[0],I[0])}),h.FullPaths=[],h.FileIndex=[],E=0;E<B.length;++E)h.FullPaths[E]=B[E][0],h.FileIndex[E]=B[E][1];for(E=0;E<B.length;++E){var q=h.FileIndex[E],S=h.FullPaths[E];if(q.name=a(S).replace("/",""),q.L=q.R=q.C=-(q.color=1),q.size=q.content?q.content.length:0,q.start=0,q.clsid=q.clsid||Me,E===0)q.C=B.length>1?1:-1,q.size=0,q.type=5;else if(S.slice(-1)=="/"){for(G=E+1;G<B.length&&n(h.FullPaths[G])!=S;++G);for(q.C=G>=B.length?-1:G,G=E+1;G<B.length&&n(h.FullPaths[G])!=n(S);++G);q.R=G>=B.length?-1:G,q.type=1}else n(h.FullPaths[E+1]||"")==n(S)&&(q.R=E+1),q.type=2}}}function X(h,T){var p=T||{};if(p.fileType=="mad")return Zn(h,p);switch(W(h),p.fileType){case"zip":return qr(h,p)}var g=function(A){for(var I=0,L=0,V=0;V<A.FileIndex.length;++V){var te=A.FileIndex[V];if(te.content){var oe=te.content.length;oe>0&&(oe<4096?I+=oe+63>>6:L+=oe+511>>9)}}for(var he=A.FullPaths.length+3>>2,Fe=I+7>>3,er=I+127>>7,Er=Fe+L+he+er,ot=Er+127>>7,na=ot<=109?0:Math.ceil((ot-109)/127);Er+ot+na+127>>7>ot;)na=++ot<=109?0:Math.ceil((ot-109)/127);var Wr=[1,na,ot,er,he,L,I,0];return A.FileIndex[0].size=I<<6,Wr[7]=(A.FileIndex[0].start=Wr[0]+Wr[1]+Wr[2]+Wr[3]+Wr[4]+Wr[5])+(Wr[6]+7>>3),Wr}(h),E=H(g[7]<<9),w=0,M=0;{for(w=0;w<8;++w)E.write_shift(1,ue[w]);for(w=0;w<8;++w)E.write_shift(2,0);for(E.write_shift(2,62),E.write_shift(2,3),E.write_shift(2,65534),E.write_shift(2,9),E.write_shift(2,6),w=0;w<3;++w)E.write_shift(2,0);for(E.write_shift(4,0),E.write_shift(4,g[2]),E.write_shift(4,g[0]+g[1]+g[2]+g[3]-1),E.write_shift(4,0),E.write_shift(4,4096),E.write_shift(4,g[3]?g[0]+g[1]+g[2]-1:ae),E.write_shift(4,g[3]),E.write_shift(-4,g[1]?g[0]-1:ae),E.write_shift(4,g[1]),w=0;w<109;++w)E.write_shift(-4,w<g[2]?g[1]+w:-1)}if(g[1])for(M=0;M<g[1];++M){for(;w<236+M*127;++w)E.write_shift(-4,w<g[2]?g[1]+w:-1);E.write_shift(-4,M===g[1]-1?ae:M+1)}var G=function(A){for(M+=A;w<M-1;++w)E.write_shift(-4,w+1);A&&(++w,E.write_shift(-4,ae))};for(M=w=0,M+=g[1];w<M;++w)E.write_shift(-4,Ne.DIFSECT);for(M+=g[2];w<M;++w)E.write_shift(-4,Ne.FATSECT);G(g[3]),G(g[4]);for(var N=0,B=0,U=h.FileIndex[0];N<h.FileIndex.length;++N)U=h.FileIndex[N],U.content&&(B=U.content.length,!(B<4096)&&(U.start=M,G(B+511>>9)));for(G(g[6]+7>>3);E.l&511;)E.write_shift(-4,Ne.ENDOFCHAIN);for(M=w=0,N=0;N<h.FileIndex.length;++N)U=h.FileIndex[N],U.content&&(B=U.content.length,!(!B||B>=4096)&&(U.start=M,G(B+63>>6)));for(;E.l&511;)E.write_shift(-4,Ne.ENDOFCHAIN);for(w=0;w<g[4]<<2;++w){var q=h.FullPaths[w];if(!q||q.length===0){for(N=0;N<17;++N)E.write_shift(4,0);for(N=0;N<3;++N)E.write_shift(4,-1);for(N=0;N<12;++N)E.write_shift(4,0);continue}U=h.FileIndex[w],w===0&&(U.start=U.size?U.start-1:ae);var S=w===0&&p.root||U.name;if(B=2*(S.length+1),E.write_shift(64,S,"utf16le"),E.write_shift(2,B),E.write_shift(1,U.type),E.write_shift(1,U.color),E.write_shift(-4,U.L),E.write_shift(-4,U.R),E.write_shift(-4,U.C),U.clsid)E.write_shift(16,U.clsid,"hex");else for(N=0;N<4;++N)E.write_shift(4,0);E.write_shift(4,U.state||0),E.write_shift(4,0),E.write_shift(4,0),E.write_shift(4,0),E.write_shift(4,0),E.write_shift(4,U.start),E.write_shift(4,U.size),E.write_shift(4,0)}for(w=1;w<h.FileIndex.length;++w)if(U=h.FileIndex[w],U.size>=4096)if(E.l=U.start+1<<9,Ee&&Buffer.isBuffer(U.content))U.content.copy(E,E.l,0,U.size),E.l+=U.size+511&-512;else{for(N=0;N<U.size;++N)E.write_shift(1,U.content[N]);for(;N&511;++N)E.write_shift(1,0)}for(w=1;w<h.FileIndex.length;++w)if(U=h.FileIndex[w],U.size>0&&U.size<4096)if(Ee&&Buffer.isBuffer(U.content))U.content.copy(E,E.l,0,U.size),E.l+=U.size+63&-64;else{for(N=0;N<U.size;++N)E.write_shift(1,U.content[N]);for(;N&63;++N)E.write_shift(1,0)}if(Ee)E.l=E.length;else for(;E.l<E.length;)E.write_shift(1,0);return E}function J(h,T){var p=h.FullPaths.map(function(N){return N.toUpperCase()}),g=p.map(function(N){var B=N.split("/");return B[B.length-(N.slice(-1)=="/"?2:1)]}),E=!1;T.charCodeAt(0)===47?(E=!0,T=p[0].slice(0,-1)+T):E=T.indexOf("/")!==-1;var w=T.toUpperCase(),M=E===!0?p.indexOf(w):g.indexOf(w);if(M!==-1)return h.FileIndex[M];var G=!w.match(vn);for(w=w.replace(Yt,""),G&&(w=w.replace(vn,"!")),M=0;M<p.length;++M)if((G?p[M].replace(vn,"!"):p[M]).replace(Yt,"")==w||(G?g[M].replace(vn,"!"):g[M]).replace(Yt,"")==w)return h.FileIndex[M];return null}var Z=64,ae=-2,pe="d0cf11e0a1b11ae1",ue=[208,207,17,224,161,177,26,225],Me="00000000000000000000000000000000",Ne={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:ae,FREESECT:-1,HEADER_SIGNATURE:pe,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:Me,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function ur(h,T,p){l();var g=X(h,p);o.writeFileSync(T,g)}function Be(h){for(var T=new Array(h.length),p=0;p<h.length;++p)T[p]=String.fromCharCode(h[p]);return T.join("")}function ze(h,T){var p=X(h,T);switch(T&&T.type||"buffer"){case"file":return l(),o.writeFileSync(T.filename,p),p;case"binary":return typeof p=="string"?p:Be(p);case"base64":return Qt(typeof p=="string"?p:Be(p));case"buffer":if(Ee)return Buffer.isBuffer(p)?p:$r(p);case"array":return typeof p=="string"?Dr(p):p}return p}var Ye;function y(h){try{var T=h.InflateRaw,p=new T;if(p._processChunk(new Uint8Array([3,0]),p._finishFlushFlag),p.bytesRead)Ye=h;else throw new Error("zlib does not expose bytesRead")}catch(g){}}function b(h,T){if(!Ye)return jt(h,T);var p=Ye.InflateRaw,g=new p,E=g._processChunk(h.slice(h.l),g._finishFlushFlag);return h.l+=g.bytesRead,E}function C(h){return Ye?Ye.deflateRawSync(h):dn(h)}var F=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],K=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],le=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];function ce(h){var T=(h<<1|h<<11)&139536|(h<<5|h<<15)&558144;return(T>>16|T>>8|T)&255}for(var fe=typeof Uint8Array!="undefined",ie=fe?new Uint8Array(256):[],ge=0;ge<256;++ge)ie[ge]=ce(ge);function de(h,T){var p=ie[h&255];return T<=8?p>>>8-T:(p=p<<8|ie[h>>8&255],T<=16?p>>>16-T:(p=p<<8|ie[h>>16&255],p>>>24-T))}function Qe(h,T){var p=T&7,g=T>>>3;return(h[g]|(p<=6?0:h[g+1]<<8))>>>p&3}function Te(h,T){var p=T&7,g=T>>>3;return(h[g]|(p<=5?0:h[g+1]<<8))>>>p&7}function Pr(h,T){var p=T&7,g=T>>>3;return(h[g]|(p<=4?0:h[g+1]<<8))>>>p&15}function Re(h,T){var p=T&7,g=T>>>3;return(h[g]|(p<=3?0:h[g+1]<<8))>>>p&31}function se(h,T){var p=T&7,g=T>>>3;return(h[g]|(p<=1?0:h[g+1]<<8))>>>p&127}function ar(h,T,p){var g=T&7,E=T>>>3,w=(1<<p)-1,M=h[E]>>>g;return p<8-g||(M|=h[E+1]<<8-g,p<16-g)||(M|=h[E+2]<<16-g,p<24-g)||(M|=h[E+3]<<24-g),M&w}function Ar(h,T,p){var g=T&7,E=T>>>3;return g<=5?h[E]|=(p&7)<<g:(h[E]|=p<<g&255,h[E+1]=(p&7)>>8-g),T+3}function Fr(h,T,p){var g=T&7,E=T>>>3;return p=(p&1)<<g,h[E]|=p,T+1}function Xr(h,T,p){var g=T&7,E=T>>>3;return p<<=g,h[E]|=p&255,p>>>=8,h[E+1]=p,T+8}function un(h,T,p){var g=T&7,E=T>>>3;return p<<=g,h[E]|=p&255,p>>>=8,h[E+1]=p&255,h[E+2]=p>>>8,T+16}function Tt(h,T){var p=h.length,g=2*p>T?2*p:T+5,E=0;if(p>=T)return h;if(Ee){var w=Ka(g);if(h.copy)h.copy(w);else for(;E<h.length;++E)w[E]=h[E];return w}else if(fe){var M=new Uint8Array(g);if(M.set)M.set(h);else for(;E<p;++E)M[E]=h[E];return M}return h.length=g,h}function vr(h){for(var T=new Array(h),p=0;p<h;++p)T[p]=0;return T}function Et(h,T,p){var g=1,E=0,w=0,M=0,G=0,N=h.length,B=fe?new Uint16Array(32):vr(32);for(w=0;w<32;++w)B[w]=0;for(w=N;w<p;++w)h[w]=0;N=h.length;var U=fe?new Uint16Array(N):vr(N);for(w=0;w<N;++w)B[E=h[w]]++,g<E&&(g=E),U[w]=0;for(B[0]=0,w=1;w<=g;++w)B[w+16]=G=G+B[w-1]<<1;for(w=0;w<N;++w)G=h[w],G!=0&&(U[w]=B[G+16]++);var q=0;for(w=0;w<N;++w)if(q=h[w],q!=0)for(G=de(U[w],g)>>g-q,M=(1<<g+4-q)-1;M>=0;--M)T[G|M<<q]=q&15|w<<4;return g}var Kr=fe?new Uint16Array(512):vr(512),Wt=fe?new Uint16Array(32):vr(32);if(!fe){for(var _r=0;_r<512;++_r)Kr[_r]=0;for(_r=0;_r<32;++_r)Wt[_r]=0}(function(){for(var h=[],T=0;T<32;T++)h.push(5);Et(h,Wt,32);var p=[];for(T=0;T<=143;T++)p.push(8);for(;T<=255;T++)p.push(9);for(;T<=279;T++)p.push(7);for(;T<=287;T++)p.push(8);Et(p,Kr,288)})();var xn=function(){for(var T=fe?new Uint8Array(32768):[],p=0,g=0;p<le.length-1;++p)for(;g<le[p+1];++g)T[g]=p;for(;g<32768;++g)T[g]=29;var E=fe?new Uint8Array(259):[];for(p=0,g=0;p<K.length-1;++p)for(;g<K[p+1];++g)E[g]=p;function w(G,N){for(var B=0;B<G.length;){var U=Math.min(65535,G.length-B),q=B+U==G.length;for(N.write_shift(1,+q),N.write_shift(2,U),N.write_shift(2,~U&65535);U-- >0;)N[N.l++]=G[B++]}return N.l}function M(G,N){for(var B=0,U=0,q=fe?new Uint16Array(32768):[];U<G.length;){var S=Math.min(65535,G.length-U);if(S<10){for(B=Ar(N,B,+(U+S==G.length)),B&7&&(B+=8-(B&7)),N.l=B/8|0,N.write_shift(2,S),N.write_shift(2,~S&65535);S-- >0;)N[N.l++]=G[U++];B=N.l*8;continue}B=Ar(N,B,+(U+S==G.length)+2);for(var A=0;S-- >0;){var I=G[U];A=(A<<5^I)&32767;var L=-1,V=0;if((L=q[A])&&(L|=U&-32768,L>U&&(L-=32768),L<U))for(;G[L+V]==G[U+V]&&V<250;)++V;if(V>2){I=E[V],I<=22?B=Xr(N,B,ie[I+1]>>1)-1:(Xr(N,B,3),B+=5,Xr(N,B,ie[I-23]>>5),B+=3);var te=I<8?0:I-4>>2;te>0&&(un(N,B,V-K[I]),B+=te),I=T[U-L],B=Xr(N,B,ie[I]>>3),B-=3;var oe=I<4?0:I-2>>1;oe>0&&(un(N,B,U-L-le[I]),B+=oe);for(var he=0;he<V;++he)q[A]=U&32767,A=(A<<5^G[U])&32767,++U;S-=V-1}else I<=143?I=I+48:B=Fr(N,B,1),B=Xr(N,B,ie[I]),q[A]=U&32767,++U}B=Xr(N,B,0)-1}return N.l=(B+7)/8|0,N.l}return function(N,B){return N.length<8?w(N,B):M(N,B)}}();function dn(h){var T=H(50+Math.floor(h.length*1.1)),p=xn(h,T);return T.slice(0,p)}var Jr=fe?new Uint16Array(32768):vr(32768),wt=fe?new Uint16Array(32768):vr(32768),st=fe?new Uint16Array(128):vr(128),mn=1,St=1;function ft(h,T){var p=Re(h,T)+257;T+=5;var g=Re(h,T)+1;T+=5;var E=Pr(h,T)+4;T+=4;for(var w=0,M=fe?new Uint8Array(19):vr(19),G=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],N=1,B=fe?new Uint8Array(8):vr(8),U=fe?new Uint8Array(8):vr(8),q=M.length,S=0;S<E;++S)M[F[S]]=w=Te(h,T),N<w&&(N=w),B[w]++,T+=3;var A=0;for(B[0]=0,S=1;S<=N;++S)U[S]=A=A+B[S-1]<<1;for(S=0;S<q;++S)(A=M[S])!=0&&(G[S]=U[A]++);var I=0;for(S=0;S<q;++S)if(I=M[S],I!=0){A=ie[G[S]]>>8-I;for(var L=(1<<7-I)-1;L>=0;--L)st[A|L<<I]=I&7|S<<3}var V=[];for(N=1;V.length<p+g;)switch(A=st[se(h,T)],T+=A&7,A>>>=3){case 16:for(w=3+Qe(h,T),T+=2,A=V[V.length-1];w-- >0;)V.push(A);break;case 17:for(w=3+Te(h,T),T+=3;w-- >0;)V.push(0);break;case 18:for(w=11+se(h,T),T+=7;w-- >0;)V.push(0);break;default:V.push(A),N<A&&(N=A);break}var te=V.slice(0,p),oe=V.slice(p);for(S=p;S<286;++S)te[S]=0;for(S=g;S<30;++S)oe[S]=0;return mn=Et(te,Jr,286),St=Et(oe,wt,30),T}function yt(h,T){if(h[0]==3&&!(h[1]&3))return[ut(T),2];for(var p=0,g=0,E=Ka(T||1<<18),w=0,M=E.length>>>0,G=0,N=0;(g&1)==0;){if(g=Te(h,p),p+=3,g>>>1)g>>1==1?(G=9,N=5):(p=ft(h,p),G=mn,N=St);else{p&7&&(p+=8-(p&7));var B=h[p>>>3]|h[(p>>>3)+1]<<8;if(p+=32,B>0)for(!T&&M<w+B&&(E=Tt(E,w+B),M=E.length);B-- >0;)E[w++]=h[p>>>3],p+=8;continue}for(;;){!T&&M<w+32767&&(E=Tt(E,w+32767),M=E.length);var U=ar(h,p,G),q=g>>>1==1?Kr[U]:Jr[U];if(p+=q&15,q>>>=4,(q>>>8&255)===0)E[w++]=q;else{if(q==256)break;q-=257;var S=q<8?0:q-4>>2;S>5&&(S=0);var A=w+K[q];S>0&&(A+=ar(h,p,S),p+=S),U=ar(h,p,N),q=g>>>1==1?Wt[U]:wt[U],p+=q&15,q>>>=4;var I=q<4?0:q-2>>1,L=le[q];for(I>0&&(L+=ar(h,p,I),p+=I),!T&&M<A&&(E=Tt(E,A+100),M=E.length);w<A;)E[w]=E[w-L],++w}}}return T?[E,p+7>>>3]:[E.slice(0,w),p+7>>>3]}function jt(h,T){var p=h.slice(h.l||0),g=yt(p,T);return h.l+=g[1],g[0]}function Tr(h,T){if(!h)throw new Error(T)}function br(h,T){var p=h;dr(p,0);var g=[],E=[],w={FileIndex:g,FullPaths:E};k(w,{root:T.root});for(var M=p.length-4;(p[M]!=80||p[M+1]!=75||p[M+2]!=5||p[M+3]!=6)&&M>=0;)--M;p.l=M+4,p.l+=4;var G=p.read_shift(2);p.l+=6;var N=p.read_shift(4);for(p.l=N,M=0;M<G;++M){p.l+=20;var B=p.read_shift(4),U=p.read_shift(4),q=p.read_shift(2),S=p.read_shift(2),A=p.read_shift(2);p.l+=8;var I=p.read_shift(4),L=f(p.slice(p.l+q,p.l+q+S));p.l+=q+S+A;var V=p.l;p.l=I+4,Ur(p,B,U,w,L),p.l=V}return w}function Ur(h,T,p,g,E){h.l+=2;var w=h.read_shift(2),M=h.read_shift(2),G=s(h);if(w&8257)throw new Error("Unsupported ZIP encryption");for(var N=h.read_shift(4),B=h.read_shift(4),U=h.read_shift(4),q=h.read_shift(2),S=h.read_shift(2),A="",I=0;I<q;++I)A+=String.fromCharCode(h[h.l++]);if(S){var L=f(h.slice(h.l,h.l+S));(L[21589]||{}).mt&&(G=L[21589].mt),((E||{})[21589]||{}).mt&&(G=E[21589].mt)}h.l+=S;var V=h.slice(h.l,h.l+B);switch(M){case 8:V=b(h,U);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+M)}var te=!1;w&8&&(N=h.read_shift(4),N==134695760&&(N=h.read_shift(4),te=!0),B=h.read_shift(4),U=h.read_shift(4)),B!=T&&Tr(te,"Bad compressed size: "+T+" != "+B),U!=p&&Tr(te,"Bad uncompressed size: "+p+" != "+U),Ht(g,A,V,{unsafe:!0,mt:G})}function qr(h,T){var p=T||{},g=[],E=[],w=H(1),M=p.compression?8:0,G=0,N=0,B=0,U=0,q=0,S=h.FullPaths[0],A=S,I=h.FileIndex[0],L=[],V=0;for(N=1;N<h.FullPaths.length;++N)if(A=h.FullPaths[N].slice(S.length),I=h.FileIndex[N],!(!I.size||!I.content||A=="Sh33tJ5")){var te=U,oe=H(A.length);for(B=0;B<A.length;++B)oe.write_shift(1,A.charCodeAt(B)&127);oe=oe.slice(0,oe.l),L[q]=xf.buf(I.content,0);var he=I.content;M==8&&(he=C(he)),w=H(30),w.write_shift(4,67324752),w.write_shift(2,20),w.write_shift(2,G),w.write_shift(2,M),I.mt?i(w,I.mt):w.write_shift(4,0),w.write_shift(-4,L[q]),w.write_shift(4,he.length),w.write_shift(4,I.content.length),w.write_shift(2,oe.length),w.write_shift(2,0),U+=w.length,g.push(w),U+=oe.length,g.push(oe),U+=he.length,g.push(he),w=H(46),w.write_shift(4,33639248),w.write_shift(2,0),w.write_shift(2,20),w.write_shift(2,G),w.write_shift(2,M),w.write_shift(4,0),w.write_shift(-4,L[q]),w.write_shift(4,he.length),w.write_shift(4,I.content.length),w.write_shift(2,oe.length),w.write_shift(2,0),w.write_shift(2,0),w.write_shift(2,0),w.write_shift(2,0),w.write_shift(4,0),w.write_shift(4,te),V+=w.l,E.push(w),V+=oe.length,E.push(oe),++q}return w=H(22),w.write_shift(4,101010256),w.write_shift(2,0),w.write_shift(2,0),w.write_shift(2,q),w.write_shift(2,q),w.write_shift(4,V),w.write_shift(4,U),w.write_shift(2,0),Ke([Ke(g),Ke(E),w])}var ir={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function $n(h,T){if(h.ctype)return h.ctype;var p=h.name||"",g=p.match(/\.([^\.]+)$/);return g&&ir[g[1]]||T&&(g=(p=T).match(/[\.\\]([^\.\\])+$/),g&&ir[g[1]])?ir[g[1]]:"application/octet-stream"}function lt(h){for(var T=Qt(h),p=[],g=0;g<T.length;g+=76)p.push(T.slice(g,g+76));return p.join(`\r
`)+`\r
`}function Xn(h){var T=h.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,function(B){var U=B.charCodeAt(0).toString(16).toUpperCase();return"="+(U.length==1?"0"+U:U)});T=T.replace(/ $/mg,"=20").replace(/\t$/mg,"=09"),T.charAt(0)==`
`&&(T="=0D"+T.slice(1)),T=T.replace(/\r(?!\n)/mg,"=0D").replace(/\n\n/mg,`
=0A`).replace(/([^\r\n])\n/mg,"$1=0A");for(var p=[],g=T.split(`\r
`),E=0;E<g.length;++E){var w=g[E];if(w.length==0){p.push("");continue}for(var M=0;M<w.length;){var G=76,N=w.slice(M,M+G);N.charAt(G-1)=="="?G--:N.charAt(G-2)=="="?G-=2:N.charAt(G-3)=="="&&(G-=3),N=w.slice(M,M+G),M+=G,M<w.length&&(N+="="),p.push(N)}}return p.join(`\r
`)}function Kn(h){for(var T=[],p=0;p<h.length;++p){for(var g=h[p];p<=h.length&&g.charAt(g.length-1)=="=";)g=g.slice(0,g.length-1)+h[++p];T.push(g)}for(var E=0;E<T.length;++E)T[E]=T[E].replace(/[=][0-9A-Fa-f]{2}/g,function(w){return String.fromCharCode(parseInt(w.slice(1),16))});return Dr(T.join(`\r
`))}function Jn(h,T,p){for(var g="",E="",w="",M,G=0;G<10;++G){var N=T[G];if(!N||N.match(/^\s*$/))break;var B=N.match(/^(.*?):\s*([^\s].*)$/);if(B)switch(B[1].toLowerCase()){case"content-location":g=B[2].trim();break;case"content-type":w=B[2].trim();break;case"content-transfer-encoding":E=B[2].trim();break}}switch(++G,E.toLowerCase()){case"base64":M=Dr(zr(T.slice(G).join("")));break;case"quoted-printable":M=Kn(T.slice(G));break;default:throw new Error("Unsupported Content-Transfer-Encoding "+E)}var U=Ht(h,g.slice(p.length),M,{unsafe:!0});w&&(U.ctype=w)}function qn(h,T){if(Be(h.slice(0,13)).toLowerCase()!="mime-version:")throw new Error("Unsupported MAD header");var p=T&&T.root||"",g=(Ee&&Buffer.isBuffer(h)?h.toString("binary"):Be(h)).split(`\r
`),E=0,w="";for(E=0;E<g.length;++E)if(w=g[E],!!/^Content-Location:/i.test(w)&&(w=w.slice(w.indexOf("file")),p||(p=w.slice(0,w.lastIndexOf("/")+1)),w.slice(0,p.length)!=p))for(;p.length>0&&(p=p.slice(0,p.length-1),p=p.slice(0,p.lastIndexOf("/")+1),w.slice(0,p.length)!=p););var M=(g[1]||"").match(/boundary="(.*?)"/);if(!M)throw new Error("MAD cannot find boundary");var G="--"+(M[1]||""),N=[],B=[],U={FileIndex:N,FullPaths:B};k(U);var q,S=0;for(E=0;E<g.length;++E){var A=g[E];A!==G&&A!==G+"--"||(S++&&Jn(U,g.slice(q,E),p),q=E)}return U}function Zn(h,T){var p=T||{},g=p.boundary||"SheetJS";g="------="+g;for(var E=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+g.slice(2)+'"',"","",""],w=h.FullPaths[0],M=w,G=h.FileIndex[0],N=1;N<h.FullPaths.length;++N)if(M=h.FullPaths[N].slice(w.length),G=h.FileIndex[N],!(!G.size||!G.content||M=="Sh33tJ5")){M=M.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,function(V){return"_x"+V.charCodeAt(0).toString(16)+"_"}).replace(/[\u0080-\uFFFF]/g,function(V){return"_u"+V.charCodeAt(0).toString(16)+"_"});for(var B=G.content,U=Ee&&Buffer.isBuffer(B)?B.toString("binary"):Be(B),q=0,S=Math.min(1024,U.length),A=0,I=0;I<=S;++I)(A=U.charCodeAt(I))>=32&&A<128&&++q;var L=q>=S*4/5;E.push(g),E.push("Content-Location: "+(p.root||"file:///C:/SheetJS/")+M),E.push("Content-Transfer-Encoding: "+(L?"quoted-printable":"base64")),E.push("Content-Type: "+$n(G,M)),E.push(""),E.push(L?Xn(U):lt(U))}return E.push(g+`--\r
`),E.join(`\r
`)}function Qn(h){var T={};return k(T,h),T}function Ht(h,T,p,g){var E=g&&g.unsafe;E||k(h);var w=!E&&Ce.find(h,T);if(!w){var M=h.FullPaths[0];T.slice(0,M.length)==M?M=T:(M.slice(-1)!="/"&&(M+="/"),M=(M+T).replace("//","/")),w={name:a(T),type:2},h.FileIndex.push(w),h.FullPaths.push(M),E||Ce.utils.cfb_gc(h)}return w.content=p,w.size=p?p.length:0,g&&(g.CLSID&&(w.clsid=g.CLSID),g.mt&&(w.mt=g.mt),g.ct&&(w.ct=g.ct)),w}function ea(h,T){k(h);var p=Ce.find(h,T);if(p){for(var g=0;g<h.FileIndex.length;++g)if(h.FileIndex[g]==p)return h.FileIndex.splice(g,1),h.FullPaths.splice(g,1),!0}return!1}function ra(h,T,p){k(h);var g=Ce.find(h,T);if(g){for(var E=0;E<h.FileIndex.length;++E)if(h.FileIndex[E]==g)return h.FileIndex[E].name=a(p),h.FullPaths[E]=p,!0}return!1}function ta(h){W(h,!0)}return t.find=J,t.read=ne,t.parse=c,t.write=ze,t.writeFile=ur,t.utils={cfb_new:Qn,cfb_add:Ht,cfb_del:ea,cfb_mov:ra,cfb_gc:ta,ReadShift:Xt,CheckField:u0,prep_blob:dr,bconcat:Ke,use_zlib:y,_deflateRaw:dn,_inflateRaw:jt,consts:Ne},t}();function df(e){return typeof e=="string"?Un(e):Array.isArray(e)?Us(e):e}function fn(e,t,r){if(typeof Deno!="undefined"){if(r&&typeof t=="string")switch(r){case"utf8":t=new TextEncoder(r).encode(t);break;case"binary":t=Un(t);break;default:throw new Error("Unsupported encoding "+r)}return Deno.writeFileSync(e,t)}var n=r=="utf8"?rn(t):t;if(typeof IE_SaveFile!="undefined")return IE_SaveFile(n,e);if(typeof Blob!="undefined"){var a=new Blob([df(n)],{type:"application/octet-stream"});if(typeof navigator!="undefined"&&navigator.msSaveBlob)return navigator.msSaveBlob(a,e);if(typeof saveAs!="undefined")return saveAs(a,e);if(typeof URL!="undefined"&&typeof document!="undefined"&&document.createElement&&URL.createObjectURL){var i=URL.createObjectURL(a);if(typeof chrome=="object"&&typeof(chrome.downloads||{}).download=="function")return URL.revokeObjectURL&&typeof setTimeout!="undefined"&&setTimeout(function(){URL.revokeObjectURL(i)},6e4),chrome.downloads.download({url:i,filename:e,saveAs:!0});var s=document.createElement("a");if(s.download!=null)return s.download=e,s.href=i,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL&&typeof setTimeout!="undefined"&&setTimeout(function(){URL.revokeObjectURL(i)},6e4),i}}if(typeof $!="undefined"&&typeof File!="undefined"&&typeof Folder!="undefined")try{var f=File(e);return f.open("w"),f.encoding="binary",Array.isArray(t)&&(t=sn(t)),f.write(t),f.close(),t}catch(o){if(!o.message||!o.message.match(/onstruct/))throw o}throw new Error("cannot save file "+e)}function Ze(e){for(var t=Object.keys(e),r=[],n=0;n<t.length;++n)Object.prototype.hasOwnProperty.call(e,t[n])&&r.push(t[n]);return r}function ni(e,t){for(var r=[],n=Ze(e),a=0;a!==n.length;++a)r[e[n[a]][t]]==null&&(r[e[n[a]][t]]=n[a]);return r}function Ta(e){for(var t=[],r=Ze(e),n=0;n!==r.length;++n)t[e[r[n]]]=r[n];return t}function Hn(e){for(var t=[],r=Ze(e),n=0;n!==r.length;++n)t[e[r[n]]]=parseInt(r[n],10);return t}function mf(e){for(var t=[],r=Ze(e),n=0;n!==r.length;++n)t[e[r[n]]]==null&&(t[e[r[n]]]=[]),t[e[r[n]]].push(r[n]);return t}var On=new Date(1899,11,30,0,0,0);function cr(e,t){var r=e.getTime(),n=On.getTime()+(e.getTimezoneOffset()-On.getTimezoneOffset())*6e4;return(r-n)/(24*60*60*1e3)}var Ki=new Date,pf=On.getTime()+(Ki.getTimezoneOffset()-On.getTimezoneOffset())*6e4,ai=Ki.getTimezoneOffset();function Ji(e){var t=new Date;return t.setTime(e*24*60*60*1e3+pf),t.getTimezoneOffset()!==ai&&t.setTime(t.getTime()+(t.getTimezoneOffset()-ai)*6e4),t}var ii=new Date("2017-02-19T19:06:09.000Z"),qi=isNaN(ii.getFullYear())?new Date("2/19/17"):ii,gf=qi.getFullYear()==2017;function lr(e,t){var r=new Date(e);if(gf)return t>0?r.setTime(r.getTime()+r.getTimezoneOffset()*60*1e3):t<0&&r.setTime(r.getTime()-r.getTimezoneOffset()*60*1e3),r;if(e instanceof Date)return e;if(qi.getFullYear()==1917&&!isNaN(r.getFullYear())){var n=r.getFullYear();return e.indexOf(""+n)>-1||r.setFullYear(r.getFullYear()+100),r}var a=e.match(/\d+/g)||["2017","2","19","0","0","0"],i=new Date(+a[0],+a[1]-1,+a[2],+a[3]||0,+a[4]||0,+a[5]||0);return e.indexOf("Z")>-1&&(i=new Date(i.getTime()-i.getTimezoneOffset()*60*1e3)),i}function Gn(e,t){if(Ee&&Buffer.isBuffer(e))return e.toString("binary");if(typeof TextDecoder!="undefined")try{var r={"€":"","‚":"",ƒ:"","„":"","…":"","†":"","‡":"","ˆ":"","‰":"",Š:"","‹":"",Œ:"",Ž:"","‘":"","’":"","“":"","”":"","•":"","–":"","—":"","˜":"","™":"",š:"","›":"",œ:"",ž:"",Ÿ:""};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,function(i){return r[i]||i})}catch(i){}for(var n=[],a=0;a!=e.length;++a)n.push(String.fromCharCode(e[a]));return n.join("")}function hr(e){if(typeof JSON!="undefined"&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if(typeof e!="object"||e==null)return e;if(e instanceof Date)return new Date(e.getTime());var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=hr(e[r]));return t}function Pe(e,t){for(var r="";r.length<t;)r+=e;return r}function Gr(e){var t=Number(e);if(!isNaN(t))return isFinite(t)?t:NaN;if(!/\d/.test(e))return t;var r=1,n=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,function(){return r*=100,""});return!isNaN(t=Number(n))||(n=n.replace(/[(](.*)[)]/,function(a,i){return r=-r,i}),!isNaN(t=Number(n)))?t/r:t}var vf=["january","february","march","april","may","june","july","august","september","october","november","december"];function en(e){var t=new Date(e),r=new Date(NaN),n=t.getYear(),a=t.getMonth(),i=t.getDate();if(isNaN(i))return r;var s=e.toLowerCase();if(s.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if(s=s.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,""),s.length>3&&vf.indexOf(s)==-1)return r}else if(s.match(/[a-z]/))return r;return n<0||n>8099?r:(a>0||i>1)&&n!=101?t:e.match(/[^-0-9:,\/\\]/)?r:t}function me(e,t,r){if(e.FullPaths){if(typeof r=="string"){var n;return Ee?n=$r(r):n=Ws(r),Ce.utils.cfb_add(e,t,n)}Ce.utils.cfb_add(e,t,r)}else e.file(t,r)}function Ea(){return Ce.utils.cfb_new()}var Ue=`<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r
`,_f={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},wa=Ta(_f),Sa=/[&<>'"]/g,Tf=/[\u0000-\u0008\u000b-\u001f]/g;function ye(e){var t=e+"";return t.replace(Sa,function(r){return wa[r]}).replace(Tf,function(r){return"_x"+("000"+r.charCodeAt(0).toString(16)).slice(-4)+"_"})}function si(e){return ye(e).replace(/ /g,"_x0020_")}var Zi=/[\u0000-\u001f]/g;function Ef(e){var t=e+"";return t.replace(Sa,function(r){return wa[r]}).replace(/\n/g,"<br/>").replace(Zi,function(r){return"&#x"+("000"+r.charCodeAt(0).toString(16)).slice(-4)+";"})}function wf(e){var t=e+"";return t.replace(Sa,function(r){return wa[r]}).replace(Zi,function(r){return"&#x"+r.charCodeAt(0).toString(16).toUpperCase()+";"})}function Sf(e){return e.replace(/(\r\n|[\r\n])/g,"&#10;")}function yf(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function ia(e){for(var t="",r=0,n=0,a=0,i=0,s=0,f=0;r<e.length;){if(n=e.charCodeAt(r++),n<128){t+=String.fromCharCode(n);continue}if(a=e.charCodeAt(r++),n>191&&n<224){s=(n&31)<<6,s|=a&63,t+=String.fromCharCode(s);continue}if(i=e.charCodeAt(r++),n<240){t+=String.fromCharCode((n&15)<<12|(a&63)<<6|i&63);continue}s=e.charCodeAt(r++),f=((n&7)<<18|(a&63)<<12|(i&63)<<6|s&63)-65536,t+=String.fromCharCode(55296+(f>>>10&1023)),t+=String.fromCharCode(56320+(f&1023))}return t}function fi(e){var t=ut(2*e.length),r,n,a=1,i=0,s=0,f;for(n=0;n<e.length;n+=a)a=1,(f=e.charCodeAt(n))<128?r=f:f<224?(r=(f&31)*64+(e.charCodeAt(n+1)&63),a=2):f<240?(r=(f&15)*4096+(e.charCodeAt(n+1)&63)*64+(e.charCodeAt(n+2)&63),a=3):(a=4,r=(f&7)*262144+(e.charCodeAt(n+1)&63)*4096+(e.charCodeAt(n+2)&63)*64+(e.charCodeAt(n+3)&63),r-=65536,s=55296+(r>>>10&1023),r=56320+(r&1023)),s!==0&&(t[i++]=s&255,t[i++]=s>>>8,s=0),t[i++]=r%256,t[i++]=r>>>8;return t.slice(0,i).toString("ucs2")}function li(e){return $r(e,"binary").toString("utf8")}var Tn="foo bar bazâð£",$t=Ee&&(li(Tn)==ia(Tn)&&li||fi(Tn)==ia(Tn)&&fi)||ia,rn=Ee?function(e){return $r(e,"utf8").toString("binary")}:function(e){for(var t=[],r=0,n=0,a=0;r<e.length;)switch(n=e.charCodeAt(r++),!0){case n<128:t.push(String.fromCharCode(n));break;case n<2048:t.push(String.fromCharCode(192+(n>>6))),t.push(String.fromCharCode(128+(n&63)));break;case(n>=55296&&n<57344):n-=55296,a=e.charCodeAt(r++)-56320+(n<<10),t.push(String.fromCharCode(240+(a>>18&7))),t.push(String.fromCharCode(144+(a>>12&63))),t.push(String.fromCharCode(128+(a>>6&63))),t.push(String.fromCharCode(128+(a&63)));break;default:t.push(String.fromCharCode(224+(n>>12))),t.push(String.fromCharCode(128+(n>>6&63))),t.push(String.fromCharCode(128+(n&63)))}return t.join("")},Af=function(){var e=[["nbsp"," "],["middot","·"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map(function(t){return[new RegExp("&"+t[0]+";","ig"),t[1]]});return function(r){for(var n=r.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,`
`).replace(/<[^>]*>/g,""),a=0;a<e.length;++a)n=n.replace(e[a][0],e[a][1]);return n}}(),Qi=/(^\s|\s$|\n)/;function Je(e,t){return"<"+e+(t.match(Qi)?' xml:space="preserve"':"")+">"+t+"</"+e+">"}function tn(e){return Ze(e).map(function(t){return" "+t+'="'+e[t]+'"'}).join("")}function ee(e,t,r){return"<"+e+(r!=null?tn(r):"")+(t!=null?(t.match(Qi)?' xml:space="preserve"':"")+">"+t+"</"+e:"/")+">"}function ma(e,t){try{return e.toISOString().replace(/\.\d*/,"")}catch(r){if(t)throw r}return""}function Ff(e,t){switch(typeof e){case"string":var r=ee("vt:lpwstr",ye(e));return r=r.replace(/&quot;/g,"_x0022_"),r;case"number":return ee((e|0)==e?"vt:i4":"vt:r8",ye(String(e)));case"boolean":return ee("vt:bool",e?"true":"false")}if(e instanceof Date)return ee("vt:filetime",ma(e));throw new Error("Unable to serialize "+e)}var He={CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/metadata/core-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",CT:"http://schemas.openxmlformats.org/package/2006/content-types",RELS:"http://schemas.openxmlformats.org/package/2006/relationships",TCMNT:"http://schemas.microsoft.com/office/spreadsheetml/2018/threadedcomments",dc:"http://purl.org/dc/elements/1.1/",dcterms:"http://purl.org/dc/terms/",dcmitype:"http://purl.org/dc/dcmitype/",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",vt:"http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",xsi:"http://www.w3.org/2001/XMLSchema-instance",xsd:"http://www.w3.org/2001/XMLSchema"},Mt=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"],mr={o:"urn:schemas-microsoft-com:office:office",x:"urn:schemas-microsoft-com:office:excel",ss:"urn:schemas-microsoft-com:office:spreadsheet",dt:"uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",mv:"http://macVmlSchemaUri",v:"urn:schemas-microsoft-com:vml",html:"http://www.w3.org/TR/REC-html40"};function Cf(e,t){for(var r=1-2*(e[t+7]>>>7),n=((e[t+7]&127)<<4)+(e[t+6]>>>4&15),a=e[t+6]&15,i=5;i>=0;--i)a=a*256+e[t+i];return n==2047?a==0?r*(1/0):NaN:(n==0?n=-1022:(n-=1023,a+=Math.pow(2,52)),r*Math.pow(2,n-52)*a)}function kf(e,t,r){var n=(t<0||1/t==-1/0?1:0)<<7,a=0,i=0,s=n?-t:t;isFinite(s)?s==0?a=i=0:(a=Math.floor(Math.log(s)/Math.LN2),i=s*Math.pow(2,52-a),a<=-1023&&(!isFinite(i)||i<Math.pow(2,52))?a=-1022:(i-=Math.pow(2,52),a+=1023)):(a=2047,i=isNaN(t)?26985:0);for(var f=0;f<=5;++f,i/=256)e[r+f]=i&255;e[r+6]=(a&15)<<4|i&15,e[r+7]=a>>4|n}var oi=function(e){for(var t=[],r=10240,n=0;n<e[0].length;++n)if(e[0][n])for(var a=0,i=e[0][n].length;a<i;a+=r)t.push.apply(t,e[0][n].slice(a,a+r));return t},ci=Ee?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map(function(t){return Buffer.isBuffer(t)?t:$r(t)})):oi(e)}:oi,hi=function(e,t,r){for(var n=[],a=t;a<r;a+=2)n.push(String.fromCharCode(zt(e,a)));return n.join("").replace(Yt,"")},ya=Ee?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf16le",t,r).replace(Yt,""):hi(e,t,r)}:hi,ui=function(e,t,r){for(var n=[],a=t;a<t+r;++a)n.push(("0"+e[a].toString(16)).slice(-2));return n.join("")},e0=Ee?function(e,t,r){return Buffer.isBuffer(e)?e.toString("hex",t,t+r):ui(e,t,r)}:ui,xi=function(e,t,r){for(var n=[],a=t;a<r;a++)n.push(String.fromCharCode(Ot(e,a)));return n.join("")},ln=Ee?function(t,r,n){return Buffer.isBuffer(t)?t.toString("utf8",r,n):xi(t,r,n)}:xi,r0=function(e,t){var r=pr(e,t);return r>0?ln(e,t+4,t+4+r-1):""},t0=r0,n0=function(e,t){var r=pr(e,t);return r>0?ln(e,t+4,t+4+r-1):""},a0=n0,i0=function(e,t){var r=2*pr(e,t);return r>0?ln(e,t+4,t+4+r-1):""},s0=i0,f0=function(t,r){var n=pr(t,r);return n>0?ya(t,r+4,r+4+n):""},l0=f0,o0=function(e,t){var r=pr(e,t);return r>0?ln(e,t+4,t+4+r):""},c0=o0,h0=function(e,t){return Cf(e,t)},In=h0,Aa=function(t){return Array.isArray(t)||typeof Uint8Array!="undefined"&&t instanceof Uint8Array};Ee&&(t0=function(t,r){if(!Buffer.isBuffer(t))return r0(t,r);var n=t.readUInt32LE(r);return n>0?t.toString("utf8",r+4,r+4+n-1):""},a0=function(t,r){if(!Buffer.isBuffer(t))return n0(t,r);var n=t.readUInt32LE(r);return n>0?t.toString("utf8",r+4,r+4+n-1):""},s0=function(t,r){if(!Buffer.isBuffer(t))return i0(t,r);var n=2*t.readUInt32LE(r);return t.toString("utf16le",r+4,r+4+n-1)},l0=function(t,r){if(!Buffer.isBuffer(t))return f0(t,r);var n=t.readUInt32LE(r);return t.toString("utf16le",r+4,r+4+n)},c0=function(t,r){if(!Buffer.isBuffer(t))return o0(t,r);var n=t.readUInt32LE(r);return t.toString("utf8",r+4,r+4+n)},In=function(t,r){return Buffer.isBuffer(t)?t.readDoubleLE(r):h0(t,r)},Aa=function(t){return Buffer.isBuffer(t)||Array.isArray(t)||typeof Uint8Array!="undefined"&&t instanceof Uint8Array});var Ot=function(e,t){return e[t]},zt=function(e,t){return e[t+1]*256+e[t]},Of=function(e,t){var r=e[t+1]*256+e[t];return r<32768?r:(65535-r+1)*-1},pr=function(e,t){return e[t+3]*(1<<24)+(e[t+2]<<16)+(e[t+1]<<8)+e[t]},ct=function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]},If=function(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]};function Xt(e,t){var r="",n,a,i=[],s,f,o,l;switch(t){case"dbcs":if(l=this.l,Ee&&Buffer.isBuffer(this))r=this.slice(this.l,this.l+2*e).toString("utf16le");else for(o=0;o<e;++o)r+=String.fromCharCode(zt(this,l)),l+=2;e*=2;break;case"utf8":r=ln(this,this.l,this.l+e);break;case"utf16le":e*=2,r=ya(this,this.l,this.l+e);break;case"wstr":return Xt.call(this,e,"dbcs");case"lpstr-ansi":r=t0(this,this.l),e=4+pr(this,this.l);break;case"lpstr-cp":r=a0(this,this.l),e=4+pr(this,this.l);break;case"lpwstr":r=s0(this,this.l),e=4+2*pr(this,this.l);break;case"lpp4":e=4+pr(this,this.l),r=l0(this,this.l),e&2&&(e+=2);break;case"8lpp4":e=4+pr(this,this.l),r=c0(this,this.l),e&3&&(e+=4-(e&3));break;case"cstr":for(e=0,r="";(s=Ot(this,this.l+e++))!==0;)i.push(gn(s));r=i.join("");break;case"_wstr":for(e=0,r="";(s=zt(this,this.l+e))!==0;)i.push(gn(s)),e+=2;e+=2,r=i.join("");break;case"dbcs-cont":for(r="",l=this.l,o=0;o<e;++o){if(this.lens&&this.lens.indexOf(l)!==-1)return s=Ot(this,l),this.l=l+1,f=Xt.call(this,e-o,s?"dbcs-cont":"sbcs-cont"),i.join("")+f;i.push(gn(zt(this,l))),l+=2}r=i.join(""),e*=2;break;case"cpstr":case"sbcs-cont":for(r="",l=this.l,o=0;o!=e;++o){if(this.lens&&this.lens.indexOf(l)!==-1)return s=Ot(this,l),this.l=l+1,f=Xt.call(this,e-o,s?"dbcs-cont":"sbcs-cont"),i.join("")+f;i.push(gn(Ot(this,l))),l+=1}r=i.join("");break;default:switch(e){case 1:return n=Ot(this,this.l),this.l++,n;case 2:return n=(t==="i"?Of:zt)(this,this.l),this.l+=2,n;case 4:case-4:return t==="i"||(this[this.l+3]&128)===0?(n=(e>0?ct:If)(this,this.l),this.l+=4,n):(a=pr(this,this.l),this.l+=4,a);case 8:case-8:if(t==="f")return e==8?a=In(this,this.l):a=In([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,a;e=8;case 16:r=e0(this,this.l,e);break}}return this.l+=e,r}var Df=function(e,t,r){e[r]=t&255,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24&255},Nf=function(e,t,r){e[r]=t&255,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255},Rf=function(e,t,r){e[r]=t&255,e[r+1]=t>>>8&255};function Pf(e,t,r){var n=0,a=0;if(r==="dbcs"){for(a=0;a!=t.length;++a)Rf(this,t.charCodeAt(a),this.l+2*a);n=2*t.length}else if(r==="sbcs"){for(t=t.replace(/[^\x00-\x7F]/g,"_"),a=0;a!=t.length;++a)this[this.l+a]=t.charCodeAt(a)&255;n=t.length}else if(r==="hex"){for(;a<e;++a)this[this.l++]=parseInt(t.slice(2*a,2*a+2),16)||0;return this}else if(r==="utf16le"){var i=Math.min(this.l+e,this.length);for(a=0;a<Math.min(t.length,e);++a){var s=t.charCodeAt(a);this[this.l++]=s&255,this[this.l++]=s>>8}for(;this.l<i;)this[this.l++]=0;return this}else switch(e){case 1:n=1,this[this.l]=t&255;break;case 2:n=2,this[this.l]=t&255,t>>>=8,this[this.l+1]=t&255;break;case 3:n=3,this[this.l]=t&255,t>>>=8,this[this.l+1]=t&255,t>>>=8,this[this.l+2]=t&255;break;case 4:n=4,Df(this,t,this.l);break;case 8:if(n=8,r==="f"){kf(this,t,this.l);break}case 16:break;case-4:n=4,Nf(this,t,this.l);break}return this.l+=n,this}function u0(e,t){var r=e0(this,this.l,e.length>>1);if(r!==e)throw new Error(t+"Expected "+e+" saw "+r);this.l+=e.length>>1}function dr(e,t){e.l=t,e.read_shift=Xt,e.chk=u0,e.write_shift=Pf}function Br(e,t){e.l+=t}function H(e){var t=ut(e);return dr(t,0),t}function or(){var e=[],t=Ee?256:2048,r=function(l){var c=H(l);return dr(c,0),c},n=r(t),a=function(){n&&(n.length>n.l&&(n=n.slice(0,n.l),n.l=n.length),n.length>0&&e.push(n),n=null)},i=function(l){return n&&l<n.length-n.l?n:(a(),n=r(Math.max(l+1,t)))},s=function(){return a(),Ke(e)},f=function(l){a(),n=l,n.l==null&&(n.l=n.length),i(t)};return{next:i,push:f,end:s,_bufs:e}}function Y(e,t,r,n){var a=+t,i;if(!isNaN(a)){n||(n=k1[a].p||(r||[]).length||0),i=1+(a>=128?1:0)+1,n>=128&&++i,n>=16384&&++i,n>=2097152&&++i;var s=e.next(i);a<=127?s.write_shift(1,a):(s.write_shift(1,(a&127)+128),s.write_shift(1,a>>7));for(var f=0;f!=4;++f)if(n>=128)s.write_shift(1,(n&127)+128),n>>=7;else{s.write_shift(1,n);break}n>0&&Aa(r)&&e.push(r)}}function Kt(e,t,r){var n=hr(e);if(t.s?(n.cRel&&(n.c+=t.s.c),n.rRel&&(n.r+=t.s.r)):(n.cRel&&(n.c+=t.c),n.rRel&&(n.r+=t.r)),!r||r.biff<12){for(;n.c>=256;)n.c-=256;for(;n.r>=65536;)n.r-=65536}return n}function di(e,t,r){var n=hr(e);return n.s=Kt(n.s,t.s,r),n.e=Kt(n.e,t.s,r),n}function Jt(e,t){if(e.cRel&&e.c<0)for(e=hr(e);e.c<0;)e.c+=t>8?16384:256;if(e.rRel&&e.r<0)for(e=hr(e);e.r<0;)e.r+=t>8?1048576:t>5?65536:16384;var r=Ae(e);return!e.cRel&&e.cRel!=null&&(r=Bf(r)),!e.rRel&&e.rRel!=null&&(r=Lf(r)),r}function sa(e,t){return e.s.r==0&&!e.s.rRel&&e.e.r==(t.biff>=12?1048575:t.biff>=8?65536:16384)&&!e.e.rRel?(e.s.cRel?"":"$")+tr(e.s.c)+":"+(e.e.cRel?"":"$")+tr(e.e.c):e.s.c==0&&!e.s.cRel&&e.e.c==(t.biff>=12?16383:255)&&!e.e.cRel?(e.s.rRel?"":"$")+qe(e.s.r)+":"+(e.e.rRel?"":"$")+qe(e.e.r):Jt(e.s,t.biff)+":"+Jt(e.e,t.biff)}function Fa(e){return parseInt(Mf(e),10)-1}function qe(e){return""+(e+1)}function Lf(e){return e.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")}function Mf(e){return e.replace(/\$(\d+)$/,"$1")}function Ca(e){for(var t=bf(e),r=0,n=0;n!==t.length;++n)r=26*r+t.charCodeAt(n)-64;return r-1}function tr(e){if(e<0)throw new Error("invalid column "+e);var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode((e-1)%26+65)+t;return t}function Bf(e){return e.replace(/^([A-Z])/,"$$$1")}function bf(e){return e.replace(/^\$([A-Z])/,"$1")}function Uf(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")}function Ge(e){for(var t=0,r=0,n=0;n<e.length;++n){var a=e.charCodeAt(n);a>=48&&a<=57?t=10*t+(a-48):a>=65&&a<=90&&(r=26*r+(a-64))}return{c:r-1,r:t-1}}function Ae(e){for(var t=e.c+1,r="";t;t=(t-1)/26|0)r=String.fromCharCode((t-1)%26+65)+r;return r+(e.r+1)}function gr(e){var t=e.indexOf(":");return t==-1?{s:Ge(e),e:Ge(e)}:{s:Ge(e.slice(0,t)),e:Ge(e.slice(t+1))}}function je(e,t){return typeof t=="undefined"||typeof t=="number"?je(e.s,e.e):(typeof e!="string"&&(e=Ae(e)),typeof t!="string"&&(t=Ae(t)),e==t?e:e+":"+t)}function Ie(e){var t={s:{c:0,r:0},e:{c:0,r:0}},r=0,n=0,a=0,i=e.length;for(r=0;n<i&&!((a=e.charCodeAt(n)-64)<1||a>26);++n)r=26*r+a;for(t.s.c=--r,r=0;n<i&&!((a=e.charCodeAt(n)-48)<0||a>9);++n)r=10*r+a;if(t.s.r=--r,n===i||a!=10)return t.e.c=t.s.c,t.e.r=t.s.r,t;for(++n,r=0;n!=i&&!((a=e.charCodeAt(n)-64)<1||a>26);++n)r=26*r+a;for(t.e.c=--r,r=0;n!=i&&!((a=e.charCodeAt(n)-48)<0||a>9);++n)r=10*r+a;return t.e.r=--r,t}function mi(e,t){var r=e.t=="d"&&t instanceof Date;if(e.z!=null)try{return e.w=tt(e.z,r?cr(t):t)}catch(n){}try{return e.w=tt((e.XF||{}).numFmtId||(r?14:0),r?cr(t):t)}catch(n){return""+t}}function Yr(e,t,r){return e==null||e.t==null||e.t=="z"?"":e.w!==void 0?e.w:(e.t=="d"&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),e.t=="e"?on[e.v]||e.v:t==null?mi(e,e.v):mi(e,t))}function mt(e,t){var r=t&&t.sheet?t.sheet:"Sheet1",n={};return n[r]=e,{SheetNames:[r],Sheets:n}}function x0(e,t,r){var n=r||{},a=e?Array.isArray(e):n.dense,i=e||(a?[]:{}),s=0,f=0;if(i&&n.origin!=null){if(typeof n.origin=="number")s=n.origin;else{var o=typeof n.origin=="string"?Ge(n.origin):n.origin;s=o.r,f=o.c}i["!ref"]||(i["!ref"]="A1:A1")}var l={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(i["!ref"]){var c=Ie(i["!ref"]);l.s.c=c.s.c,l.s.r=c.s.r,l.e.c=Math.max(l.e.c,c.e.c),l.e.r=Math.max(l.e.r,c.e.r),s==-1&&(l.e.r=s=c.e.r+1)}for(var m=0;m!=t.length;++m)if(t[m]){if(!Array.isArray(t[m]))throw new Error("aoa_to_sheet expects an array of arrays");for(var d=0;d!=t[m].length;++d)if(typeof t[m][d]!="undefined"){var x={v:t[m][d]},v=s+m,u=f+d;if(l.s.r>v&&(l.s.r=v),l.s.c>u&&(l.s.c=u),l.e.r<v&&(l.e.r=v),l.e.c<u&&(l.e.c=u),t[m][d]&&typeof t[m][d]=="object"&&!Array.isArray(t[m][d])&&!(t[m][d]instanceof Date))x=t[m][d];else if(Array.isArray(x.v)&&(x.f=t[m][d][1],x.v=x.v[0]),x.v===null)if(x.f)x.t="n";else if(n.nullError)x.t="e",x.v=0;else if(n.sheetStubs)x.t="z";else continue;else typeof x.v=="number"?x.t="n":typeof x.v=="boolean"?x.t="b":x.v instanceof Date?(x.z=n.dateNF||Le[14],n.cellDates?(x.t="d",x.w=tt(x.z,cr(x.v))):(x.t="n",x.v=cr(x.v),x.w=tt(x.z,x.v))):x.t="s";if(a)i[v]||(i[v]=[]),i[v][u]&&i[v][u].z&&(x.z=i[v][u].z),i[v][u]=x;else{var _=Ae({c:u,r:v});i[_]&&i[_].z&&(x.z=i[_].z),i[_]=x}}}return l.s.c<1e7&&(i["!ref"]=je(l)),i}function Bt(e,t){return x0(null,e,t)}function Wf(e){return e.read_shift(4,"i")}function Rr(e,t){return t||(t=H(4)),t.write_shift(4,e),t}function nr(e){var t=e.read_shift(4);return t===0?"":e.read_shift(t,"dbcs")}function Ve(e,t){var r=!1;return t==null&&(r=!0,t=H(4+2*e.length)),t.write_shift(4,e.length),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function jf(e){return{ich:e.read_shift(2),ifnt:e.read_shift(2)}}function Hf(e,t){return t||(t=H(4)),t.write_shift(2,0),t.write_shift(2,0),t}function ka(e,t){var r=e.l,n=e.read_shift(1),a=nr(e),i=[],s={t:a,h:a};if((n&1)!==0){for(var f=e.read_shift(4),o=0;o!=f;++o)i.push(jf(e));s.r=i}else s.r=[{ich:0,ifnt:0}];return e.l=r+t,s}function Gf(e,t){var r=!1;return t==null&&(r=!0,t=H(15+4*e.t.length)),t.write_shift(1,0),Ve(e.t,t),r?t.slice(0,t.l):t}var Vf=ka;function zf(e,t){var r=!1;return t==null&&(r=!0,t=H(23+4*e.t.length)),t.write_shift(1,1),Ve(e.t,t),t.write_shift(4,1),Hf({},t),r?t.slice(0,t.l):t}function yr(e){var t=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:t,iStyleRef:r}}function pt(e,t){return t==null&&(t=H(8)),t.write_shift(-4,e.c),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function gt(e){var t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:t}}function vt(e,t){return t==null&&(t=H(4)),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}var Yf=nr,d0=Ve;function Oa(e){var t=e.read_shift(4);return t===0||t===4294967295?"":e.read_shift(t,"dbcs")}function Dn(e,t){var r=!1;return t==null&&(r=!0,t=H(127)),t.write_shift(4,e.length>0?e.length:4294967295),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}var $f=nr,pa=Oa,Ia=Dn;function m0(e){var t=e.slice(e.l,e.l+4),r=t[0]&1,n=t[0]&2;e.l+=4;var a=n===0?In([0,0,0,0,t[0]&252,t[1],t[2],t[3]],0):ct(t,0)>>2;return r?a/100:a}function p0(e,t){t==null&&(t=H(4));var r=0,n=0,a=e*100;if(e==(e|0)&&e>=-536870912&&e<1<<29?n=1:a==(a|0)&&a>=-536870912&&a<1<<29&&(n=1,r=1),n)t.write_shift(-4,((r?a:e)<<2)+(r+2));else throw new Error("unsupported RkNumber "+e)}function g0(e){var t={s:{},e:{}};return t.s.r=e.read_shift(4),t.e.r=e.read_shift(4),t.s.c=e.read_shift(4),t.e.c=e.read_shift(4),t}function Xf(e,t){return t||(t=H(16)),t.write_shift(4,e.s.r),t.write_shift(4,e.e.r),t.write_shift(4,e.s.c),t.write_shift(4,e.e.c),t}var _t=g0,bt=Xf;function Ut(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function xt(e,t){return(t||H(8)).write_shift(8,e,"f")}function Kf(e){var t={},r=e.read_shift(1),n=r>>>1,a=e.read_shift(1),i=e.read_shift(2,"i"),s=e.read_shift(1),f=e.read_shift(1),o=e.read_shift(1);switch(e.l++,n){case 0:t.auto=1;break;case 1:t.index=a;var l=al[a];l&&(t.rgb=Fi(l));break;case 2:t.rgb=Fi([s,f,o]);break;case 3:t.theme=a;break}return i!=0&&(t.tint=i>0?i/32767:i/32768),t}function Nn(e,t){if(t||(t=H(8)),!e||e.auto)return t.write_shift(4,0),t.write_shift(4,0),t;e.index!=null?(t.write_shift(1,2),t.write_shift(1,e.index)):e.theme!=null?(t.write_shift(1,6),t.write_shift(1,e.theme)):(t.write_shift(1,5),t.write_shift(1,0));var r=e.tint||0;if(r>0?r*=32767:r<0&&(r*=32768),t.write_shift(2,r),!e.rgb||e.theme!=null)t.write_shift(2,0),t.write_shift(1,0),t.write_shift(1,0);else{var n=e.rgb||"FFFFFF";typeof n=="number"&&(n=("000000"+n.toString(16)).slice(-6)),t.write_shift(1,parseInt(n.slice(0,2),16)),t.write_shift(1,parseInt(n.slice(2,4),16)),t.write_shift(1,parseInt(n.slice(4,6),16)),t.write_shift(1,255)}return t}function Jf(e){var t=e.read_shift(1);e.l++;var r={fBold:t&1,fItalic:t&2,fUnderline:t&4,fStrikeout:t&8,fOutline:t&16,fShadow:t&32,fCondense:t&64,fExtend:t&128};return r}function qf(e,t){t||(t=H(2));var r=(e.italic?2:0)|(e.strike?8:0)|(e.outline?16:0)|(e.shadow?32:0)|(e.condense?64:0)|(e.extend?128:0);return t.write_shift(1,r),t.write_shift(1,0),t}var v0=2,xr=3,En=11,Rn=19,wn=64,Zf=65,Qf=71,el=4108,rl=4126,Xe=80,pi={1:{n:"CodePage",t:v0},2:{n:"Category",t:Xe},3:{n:"PresentationFormat",t:Xe},4:{n:"ByteCount",t:xr},5:{n:"LineCount",t:xr},6:{n:"ParagraphCount",t:xr},7:{n:"SlideCount",t:xr},8:{n:"NoteCount",t:xr},9:{n:"HiddenCount",t:xr},10:{n:"MultimediaClipCount",t:xr},11:{n:"ScaleCrop",t:En},12:{n:"HeadingPairs",t:el},13:{n:"TitlesOfParts",t:rl},14:{n:"Manager",t:Xe},15:{n:"Company",t:Xe},16:{n:"LinksUpToDate",t:En},17:{n:"CharacterCount",t:xr},19:{n:"SharedDoc",t:En},22:{n:"HyperlinksChanged",t:En},23:{n:"AppVersion",t:xr,p:"version"},24:{n:"DigSig",t:Zf},26:{n:"ContentType",t:Xe},27:{n:"ContentStatus",t:Xe},28:{n:"Language",t:Xe},29:{n:"Version",t:Xe},255:{},2147483648:{n:"Locale",t:Rn},2147483651:{n:"Behavior",t:Rn},1919054434:{}},gi={1:{n:"CodePage",t:v0},2:{n:"Title",t:Xe},3:{n:"Subject",t:Xe},4:{n:"Author",t:Xe},5:{n:"Keywords",t:Xe},6:{n:"Comments",t:Xe},7:{n:"Template",t:Xe},8:{n:"LastAuthor",t:Xe},9:{n:"RevNumber",t:Xe},10:{n:"EditTime",t:wn},11:{n:"LastPrinted",t:wn},12:{n:"CreatedDate",t:wn},13:{n:"ModifiedDate",t:wn},14:{n:"PageCount",t:xr},15:{n:"WordCount",t:xr},16:{n:"CharCount",t:xr},17:{n:"Thumbnail",t:Qf},18:{n:"Application",t:Xe},19:{n:"DocSecurity",t:xr},255:{},2147483648:{n:"Locale",t:Rn},2147483651:{n:"Behavior",t:Rn},1919054434:{}};function tl(e){return e.map(function(t){return[t>>16&255,t>>8&255,t&255]})}var nl=tl([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]),al=hr(nl),on={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},il={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},Sn={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},metadata:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml",xlsb:"application/vnd.ms-excel.sheetMetadata"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};function _0(){return{workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""}}function T0(e,t){var r=mf(il),n=[],a;n[n.length]=Ue,n[n.length]=ee("Types",null,{xmlns:He.CT,"xmlns:xsd":He.xsd,"xmlns:xsi":He.xsi}),n=n.concat([["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels","application/vnd.openxmlformats-package.relationships+xml"]].map(function(o){return ee("Default",null,{Extension:o[0],ContentType:o[1]})}));var i=function(o){e[o]&&e[o].length>0&&(a=e[o][0],n[n.length]=ee("Override",null,{PartName:(a[0]=="/"?"":"/")+a,ContentType:Sn[o][t.bookType]||Sn[o].xlsx}))},s=function(o){(e[o]||[]).forEach(function(l){n[n.length]=ee("Override",null,{PartName:(l[0]=="/"?"":"/")+l,ContentType:Sn[o][t.bookType]||Sn[o].xlsx})})},f=function(o){(e[o]||[]).forEach(function(l){n[n.length]=ee("Override",null,{PartName:(l[0]=="/"?"":"/")+l,ContentType:r[o][0]})})};return i("workbooks"),s("sheets"),s("charts"),f("themes"),["strs","styles"].forEach(i),["coreprops","extprops","custprops"].forEach(f),f("vba"),f("comments"),f("threadedcomments"),f("drawings"),s("metadata"),f("people"),n.length>2&&(n[n.length]="</Types>",n[1]=n[1].replace("/>",">")),n.join("")}var _e={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function E0(e){var t=e.lastIndexOf("/");return e.slice(0,t+1)+"_rels/"+e.slice(t+1)+".rels"}function Nt(e){var t=[Ue,ee("Relationships",null,{xmlns:He.RELS})];return Ze(e["!id"]).forEach(function(r){t[t.length]=ee("Relationship",null,e["!id"][r])}),t.length>2&&(t[t.length]="</Relationships>",t[1]=t[1].replace("/>",">")),t.join("")}function Se(e,t,r,n,a,i){if(a||(a={}),e["!id"]||(e["!id"]={}),e["!idx"]||(e["!idx"]=1),t<0)for(t=e["!idx"];e["!id"]["rId"+t];++t);if(e["!idx"]=t+1,a.Id="rId"+t,a.Type=n,a.Target=r,[_e.HLINK,_e.XPATH,_e.XMISS].indexOf(a.Type)>-1&&(a.TargetMode="External"),e["!id"][a.Id])throw new Error("Cannot rewrite rId "+t);return e["!id"][a.Id]=a,e[("/"+a.Target).replace("//","/")]=a,t}function sl(e){var t=[Ue];t.push(`<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">
`),t.push(`  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>
`);for(var r=0;r<e.length;++r)t.push('  <manifest:file-entry manifest:full-path="'+e[r][0]+'" manifest:media-type="'+e[r][1]+`"/>
`);return t.push("</manifest:manifest>"),t.join("")}function vi(e,t,r){return['  <rdf:Description rdf:about="'+e+`">
`,'    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(r||"odf")+"#"+t+`"/>
`,`  </rdf:Description>
`].join("")}function fl(e,t){return['  <rdf:Description rdf:about="'+e+`">
`,'    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+t+`"/>
`,`  </rdf:Description>
`].join("")}function ll(e){var t=[Ue];t.push(`<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
`);for(var r=0;r!=e.length;++r)t.push(vi(e[r][0],e[r][1])),t.push(fl("",e[r][0]));return t.push(vi("","Document","pkg")),t.push("</rdf:RDF>"),t.join("")}function w0(){return'<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>SheetJS '+An.version+"</meta:generator></office:meta></office:document-meta>"}var ht=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]];function fa(e,t,r,n,a){a[e]!=null||t==null||t===""||(a[e]=t,t=ye(t),n[n.length]=r?ee(e,t,r):Je(e,t))}function S0(e,t){var r=t||{},n=[Ue,ee("cp:coreProperties",null,{"xmlns:cp":He.CORE_PROPS,"xmlns:dc":He.dc,"xmlns:dcterms":He.dcterms,"xmlns:dcmitype":He.dcmitype,"xmlns:xsi":He.xsi})],a={};if(!e&&!r.Props)return n.join("");e&&(e.CreatedDate!=null&&fa("dcterms:created",typeof e.CreatedDate=="string"?e.CreatedDate:ma(e.CreatedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,a),e.ModifiedDate!=null&&fa("dcterms:modified",typeof e.ModifiedDate=="string"?e.ModifiedDate:ma(e.ModifiedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,a));for(var i=0;i!=ht.length;++i){var s=ht[i],f=r.Props&&r.Props[s[1]]!=null?r.Props[s[1]]:e?e[s[1]]:null;f===!0?f="1":f===!1?f="0":typeof f=="number"&&(f=String(f)),f!=null&&fa(s[0],f,null,n,a)}return n.length>2&&(n[n.length]="</cp:coreProperties>",n[1]=n[1].replace("/>",">")),n.join("")}var Rt=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]],y0=["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"];function A0(e){var t=[],r=ee;return e||(e={}),e.Application="SheetJS",t[t.length]=Ue,t[t.length]=ee("Properties",null,{xmlns:He.EXT_PROPS,"xmlns:vt":He.vt}),Rt.forEach(function(n){if(e[n[1]]!==void 0){var a;switch(n[2]){case"string":a=ye(String(e[n[1]]));break;case"bool":a=e[n[1]]?"true":"false";break}a!==void 0&&(t[t.length]=r(n[0],a))}}),t[t.length]=r("HeadingPairs",r("vt:vector",r("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+r("vt:variant",r("vt:i4",String(e.Worksheets))),{size:2,baseType:"variant"})),t[t.length]=r("TitlesOfParts",r("vt:vector",e.SheetNames.map(function(n){return"<vt:lpstr>"+ye(n)+"</vt:lpstr>"}).join(""),{size:e.Worksheets,baseType:"lpstr"})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}function F0(e){var t=[Ue,ee("Properties",null,{xmlns:He.CUST_PROPS,"xmlns:vt":He.vt})];if(!e)return t.join("");var r=1;return Ze(e).forEach(function(a){++r,t[t.length]=ee("property",Ff(e[a]),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:r,name:ye(a)})}),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var _i={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"};function ol(e,t){var r=[];return Ze(_i).map(function(n){for(var a=0;a<ht.length;++a)if(ht[a][1]==n)return ht[a];for(a=0;a<Rt.length;++a)if(Rt[a][1]==n)return Rt[a];throw n}).forEach(function(n){if(e[n[1]]!=null){var a=t&&t.Props&&t.Props[n[1]]!=null?t.Props[n[1]]:e[n[1]];switch(n[2]){case"date":a=new Date(a).toISOString().replace(/\.\d*Z/,"Z");break}typeof a=="number"?a=String(a):a===!0||a===!1?a=a?"1":"0":a instanceof Date&&(a=new Date(a).toISOString().replace(/\.\d*Z/,"")),r.push(Je(_i[n[1]]||n[1],a))}}),ee("DocumentProperties",r.join(""),{xmlns:mr.o})}function cl(e,t){var r=["Worksheets","SheetNames"],n="CustomDocumentProperties",a=[];return e&&Ze(e).forEach(function(i){if(Object.prototype.hasOwnProperty.call(e,i)){for(var s=0;s<ht.length;++s)if(i==ht[s][1])return;for(s=0;s<Rt.length;++s)if(i==Rt[s][1])return;for(s=0;s<r.length;++s)if(i==r[s])return;var f=e[i],o="string";typeof f=="number"?(o="float",f=String(f)):f===!0||f===!1?(o="boolean",f=f?"1":"0"):f=String(f),a.push(ee(si(i),f,{"dt:dt":o}))}}),t&&Ze(t).forEach(function(i){if(Object.prototype.hasOwnProperty.call(t,i)&&!(e&&Object.prototype.hasOwnProperty.call(e,i))){var s=t[i],f="string";typeof s=="number"?(f="float",s=String(s)):s===!0||s===!1?(f="boolean",s=s?"1":"0"):s instanceof Date?(f="dateTime.tz",s=s.toISOString()):s=String(s),a.push(ee(si(i),s,{"dt:dt":f}))}}),"<"+n+' xmlns="'+mr.o+'">'+a.join("")+"</"+n+">"}function hl(e){var t=typeof e=="string"?new Date(Date.parse(e)):e,r=t.getTime()/1e3+11644473600,n=r%Math.pow(2,32),a=(r-n)/Math.pow(2,32);n*=1e7,a*=1e7;var i=n/Math.pow(2,32)|0;i>0&&(n=n%Math.pow(2,32),a+=i);var s=H(8);return s.write_shift(4,n),s.write_shift(4,a),s}function Ti(e,t){var r=H(4),n=H(4);switch(r.write_shift(4,e==80?31:e),e){case 3:n.write_shift(-4,t);break;case 5:n=H(8),n.write_shift(8,t,"f");break;case 11:n.write_shift(4,t?1:0);break;case 64:n=hl(t);break;case 31:case 80:for(n=H(4+2*(t.length+1)+(t.length%2?0:2)),n.write_shift(4,t.length+1),n.write_shift(0,t,"dbcs");n.l!=n.length;)n.write_shift(1,0);break;default:throw new Error("TypedPropertyValue unrecognized type "+e+" "+t)}return Ke([r,n])}var C0=["CodePage","Thumbnail","_PID_LINKBASE","_PID_HLINKS","SystemIdentifier","FMTID"];function ul(e){switch(typeof e){case"boolean":return 11;case"number":return(e|0)==e?3:5;case"string":return 31;case"object":if(e instanceof Date)return 64;break}return-1}function Ei(e,t,r){var n=H(8),a=[],i=[],s=8,f=0,o=H(8),l=H(8);if(o.write_shift(4,2),o.write_shift(4,1200),l.write_shift(4,1),i.push(o),a.push(l),s+=8+o.length,!t){l=H(8),l.write_shift(4,0),a.unshift(l);var c=[H(4)];for(c[0].write_shift(4,e.length),f=0;f<e.length;++f){var m=e[f][0];for(o=H(8+2*(m.length+1)+(m.length%2?0:2)),o.write_shift(4,f+2),o.write_shift(4,m.length+1),o.write_shift(0,m,"dbcs");o.l!=o.length;)o.write_shift(1,0);c.push(o)}o=Ke(c),i.unshift(o),s+=8+o.length}for(f=0;f<e.length;++f)if(!(t&&!t[e[f][0]])&&!(C0.indexOf(e[f][0])>-1||y0.indexOf(e[f][0])>-1)&&e[f][1]!=null){var d=e[f][1],x=0;if(t){x=+t[e[f][0]];var v=r[x];if(v.p=="version"&&typeof d=="string"){var u=d.split(".");d=(+u[0]<<16)+(+u[1]||0)}o=Ti(v.t,d)}else{var _=ul(d);_==-1&&(_=31,d=String(d)),o=Ti(_,d)}i.push(o),l=H(8),l.write_shift(4,t?x:2+f),a.push(l),s+=8+o.length}var R=8*(i.length+1);for(f=0;f<i.length;++f)a[f].write_shift(4,R),R+=i[f].length;return n.write_shift(4,s),n.write_shift(4,i.length),Ke([n].concat(a).concat(i))}function wi(e,t,r,n,a,i){var s=H(a?68:48),f=[s];s.write_shift(2,65534),s.write_shift(2,0),s.write_shift(4,842412599),s.write_shift(16,Ce.utils.consts.HEADER_CLSID,"hex"),s.write_shift(4,a?2:1),s.write_shift(16,t,"hex"),s.write_shift(4,a?68:48);var o=Ei(e,r,n);if(f.push(o),a){var l=Ei(a,null,null);s.write_shift(16,i,"hex"),s.write_shift(4,68+o.length),f.push(l)}return Ke(f)}function xl(e,t){t||(t=H(e));for(var r=0;r<e;++r)t.write_shift(1,0);return t}function dl(e,t){return e.read_shift(t)===1}function fr(e,t){return t||(t=H(2)),t.write_shift(2,+!!e),t}function k0(e){return e.read_shift(2,"u")}function Sr(e,t){return t||(t=H(2)),t.write_shift(2,e),t}function O0(e,t,r){return r||(r=H(2)),r.write_shift(1,t=="e"?+e:+!!e),r.write_shift(1,t=="e"?1:0),r}function I0(e,t,r){var n=e.read_shift(r&&r.biff>=12?2:1),a="sbcs-cont";if(r&&r.biff>=8,!r||r.biff==8){var i=e.read_shift(1);i&&(a="dbcs-cont")}else r.biff==12&&(a="wstr");r.biff>=2&&r.biff<=5&&(a="cpstr");var s=n?e.read_shift(n,a):"";return s}function ml(e){var t=e.t||"",r=H(3);r.write_shift(2,t.length),r.write_shift(1,1);var n=H(2*t.length);n.write_shift(2*t.length,t,"utf16le");var a=[r,n];return Ke(a)}function pl(e,t,r){var n;if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(t,"cpstr");if(r.biff>=12)return e.read_shift(t,"dbcs-cont")}var a=e.read_shift(1);return a===0?n=e.read_shift(t,"sbcs-cont"):n=e.read_shift(t,"dbcs-cont"),n}function gl(e,t,r){var n=e.read_shift(r&&r.biff==2?1:2);return n===0?(e.l++,""):pl(e,n,r)}function vl(e,t,r){if(r.biff>5)return gl(e,t,r);var n=e.read_shift(1);return n===0?(e.l++,""):e.read_shift(n,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function D0(e,t,r){return r||(r=H(3+2*e.length)),r.write_shift(2,e.length),r.write_shift(1,1),r.write_shift(31,e,"utf16le"),r}function Si(e,t){t||(t=H(6+e.length*2)),t.write_shift(4,1+e.length);for(var r=0;r<e.length;++r)t.write_shift(2,e.charCodeAt(r));return t.write_shift(2,0),t}function _l(e){var t=H(512),r=0,n=e.Target;n.slice(0,7)=="file://"&&(n=n.slice(7));var a=n.indexOf("#"),i=a>-1?31:23;switch(n.charAt(0)){case"#":i=28;break;case".":i&=-3;break}t.write_shift(4,2),t.write_shift(4,i);var s=[8,6815827,6619237,4849780,83];for(r=0;r<s.length;++r)t.write_shift(4,s[r]);if(i==28)n=n.slice(1),Si(n,t);else if(i&2){for(s="e0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),r=0;r<s.length;++r)t.write_shift(1,parseInt(s[r],16));var f=a>-1?n.slice(0,a):n;for(t.write_shift(4,2*(f.length+1)),r=0;r<f.length;++r)t.write_shift(2,f.charCodeAt(r));t.write_shift(2,0),i&8&&Si(a>-1?n.slice(a+1):"",t)}else{for(s="03 03 00 00 00 00 00 00 c0 00 00 00 00 00 00 46".split(" "),r=0;r<s.length;++r)t.write_shift(1,parseInt(s[r],16));for(var o=0;n.slice(o*3,o*3+3)=="../"||n.slice(o*3,o*3+3)=="..\\";)++o;for(t.write_shift(2,o),t.write_shift(4,n.length-3*o+1),r=0;r<n.length-3*o;++r)t.write_shift(1,n.charCodeAt(r+3*o)&255);for(t.write_shift(1,0),t.write_shift(2,65535),t.write_shift(2,57005),r=0;r<6;++r)t.write_shift(4,0)}return t.slice(0,t.l)}function dt(e,t,r,n){return n||(n=H(6)),n.write_shift(2,e),n.write_shift(2,t),n.write_shift(2,r||0),n}function Tl(e,t,r){var n=r.biff>8?4:2,a=e.read_shift(n),i=e.read_shift(n,"i"),s=e.read_shift(n,"i");return[a,i,s]}function El(e){var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(2),a=e.read_shift(2);return{s:{c:n,r:t},e:{c:a,r}}}function N0(e,t){return t||(t=H(8)),t.write_shift(2,e.s.r),t.write_shift(2,e.e.r),t.write_shift(2,e.s.c),t.write_shift(2,e.e.c),t}function Da(e,t,r){var n=1536,a=16;switch(r.bookType){case"biff8":break;case"biff5":n=1280,a=8;break;case"biff4":n=4,a=6;break;case"biff3":n=3,a=6;break;case"biff2":n=2,a=4;break;case"xla":break;default:throw new Error("unsupported BIFF version")}var i=H(a);return i.write_shift(2,n),i.write_shift(2,t),a>4&&i.write_shift(2,29282),a>6&&i.write_shift(2,1997),a>8&&(i.write_shift(2,49161),i.write_shift(2,1),i.write_shift(2,1798),i.write_shift(2,0)),i}function wl(e,t){var r=!t||t.biff==8,n=H(r?112:54);for(n.write_shift(t.biff==8?2:1,7),r&&n.write_shift(1,0),n.write_shift(4,859007059),n.write_shift(4,5458548|(r?0:536870912));n.l<n.length;)n.write_shift(1,r?0:32);return n}function Sl(e,t){var r=!t||t.biff>=8?2:1,n=H(8+r*e.name.length);n.write_shift(4,e.pos),n.write_shift(1,e.hs||0),n.write_shift(1,e.dt),n.write_shift(1,e.name.length),t.biff>=8&&n.write_shift(1,1),n.write_shift(r*e.name.length,e.name,t.biff<8?"sbcs":"utf16le");var a=n.slice(0,n.l);return a.l=n.l,a}function yl(e,t){var r=H(8);r.write_shift(4,e.Count),r.write_shift(4,e.Unique);for(var n=[],a=0;a<e.length;++a)n[a]=ml(e[a]);var i=Ke([r].concat(n));return i.parts=[r.length].concat(n.map(function(s){return s.length})),i}function Al(){var e=H(18);return e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,29280),e.write_shift(2,17600),e.write_shift(2,56),e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,1),e.write_shift(2,500),e}function Fl(e){var t=H(18),r=1718;return e&&e.RTL&&(r|=64),t.write_shift(2,r),t.write_shift(4,0),t.write_shift(4,64),t.write_shift(4,0),t.write_shift(4,0),t}function Cl(e,t){var r=e.name||"Arial",n=t&&t.biff==5,a=n?15+r.length:16+2*r.length,i=H(a);return i.write_shift(2,e.sz*20),i.write_shift(4,0),i.write_shift(2,400),i.write_shift(4,0),i.write_shift(2,0),i.write_shift(1,r.length),n||i.write_shift(1,1),i.write_shift((n?1:2)*r.length,r,n?"sbcs":"utf16le"),i}function kl(e,t,r,n){var a=H(10);return dt(e,t,n,a),a.write_shift(4,r),a}function Ol(e,t,r,n,a){var i=!a||a.biff==8,s=H(8+ +i+(1+i)*r.length);return dt(e,t,n,s),s.write_shift(2,r.length),i&&s.write_shift(1,1),s.write_shift((1+i)*r.length,r,i?"utf16le":"sbcs"),s}function Il(e,t,r,n){var a=r&&r.biff==5;n||(n=H(a?3+t.length:5+2*t.length)),n.write_shift(2,e),n.write_shift(a?1:2,t.length),a||n.write_shift(1,1),n.write_shift((a?1:2)*t.length,t,a?"sbcs":"utf16le");var i=n.length>n.l?n.slice(0,n.l):n;return i.l==null&&(i.l=i.length),i}function Dl(e,t){var r=t.biff==8||!t.biff?4:2,n=H(2*r+6);return n.write_shift(r,e.s.r),n.write_shift(r,e.e.r+1),n.write_shift(2,e.s.c),n.write_shift(2,e.e.c+1),n.write_shift(2,0),n}function yi(e,t,r,n){var a=r&&r.biff==5;n||(n=H(a?16:20)),n.write_shift(2,0),e.style?(n.write_shift(2,e.numFmtId||0),n.write_shift(2,65524)):(n.write_shift(2,e.numFmtId||0),n.write_shift(2,t<<4));var i=0;return e.numFmtId>0&&a&&(i|=1024),n.write_shift(4,i),n.write_shift(4,0),a||n.write_shift(4,0),n.write_shift(2,0),n}function Nl(e){var t=H(8);return t.write_shift(4,0),t.write_shift(2,0),t.write_shift(2,0),t}function Rl(e,t,r,n,a,i){var s=H(8);return dt(e,t,n,s),O0(r,i,s),s}function Pl(e,t,r,n){var a=H(14);return dt(e,t,n,a),xt(r,a),a}function Ll(e,t,r){if(r.biff<8)return Ml(e,t,r);for(var n=[],a=e.l+t,i=e.read_shift(r.biff>8?4:2);i--!==0;)n.push(Tl(e,r.biff>8?12:6,r));if(e.l!=a)throw new Error("Bad ExternSheet: "+e.l+" != "+a);return n}function Ml(e,t,r){e[e.l+1]==3&&e[e.l]++;var n=I0(e,t,r);return n.charCodeAt(0)==3?n.slice(1):n}function Bl(e){var t=H(2+e.length*8);t.write_shift(2,e.length);for(var r=0;r<e.length;++r)N0(e[r],t);return t}function bl(e){var t=H(24),r=Ge(e[0]);t.write_shift(2,r.r),t.write_shift(2,r.r),t.write_shift(2,r.c),t.write_shift(2,r.c);for(var n="d0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),a=0;a<16;++a)t.write_shift(1,parseInt(n[a],16));return Ke([t,_l(e[1])])}function Ul(e){var t=e[1].Tooltip,r=H(10+2*(t.length+1));r.write_shift(2,2048);var n=Ge(e[0]);r.write_shift(2,n.r),r.write_shift(2,n.r),r.write_shift(2,n.c),r.write_shift(2,n.c);for(var a=0;a<t.length;++a)r.write_shift(2,t.charCodeAt(a));return r.write_shift(2,0),r}function Wl(e){return e||(e=H(4)),e.write_shift(2,1),e.write_shift(2,1),e}function jl(e,t,r){if(!r.cellStyles)return Br(e,t);var n=r&&r.biff>=12?4:2,a=e.read_shift(n),i=e.read_shift(n),s=e.read_shift(n),f=e.read_shift(n),o=e.read_shift(2);n==2&&(e.l+=2);var l={s:a,e:i,w:s,ixfe:f,flags:o};return(r.biff>=5||!r.biff)&&(l.level=o>>8&7),l}function Hl(e,t){var r=H(12);r.write_shift(2,t),r.write_shift(2,t),r.write_shift(2,e.width*256),r.write_shift(2,0);var n=0;return e.hidden&&(n|=1),r.write_shift(1,n),n=e.level||0,r.write_shift(1,n),r.write_shift(2,0),r}function Gl(e){for(var t=H(2*e),r=0;r<e;++r)t.write_shift(2,r+1);return t}function Vl(e,t,r){var n=H(15);return hn(n,e,t),n.write_shift(8,r,"f"),n}function zl(e,t,r){var n=H(9);return hn(n,e,t),n.write_shift(2,r),n}var Yl=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},t=Ta({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function r(f,o){var l=[],c=ut(1);switch(o.type){case"base64":c=Dr(zr(f));break;case"binary":c=Dr(f);break;case"buffer":case"array":c=f;break}dr(c,0);var m=c.read_shift(1),d=!!(m&136),x=!1,v=!1;switch(m){case 2:break;case 3:break;case 48:x=!0,d=!0;break;case 49:x=!0,d=!0;break;case 131:break;case 139:break;case 140:v=!0;break;case 245:break;default:throw new Error("DBF Unsupported Version: "+m.toString(16))}var u=0,_=521;m==2&&(u=c.read_shift(2)),c.l+=3,m!=2&&(u=c.read_shift(4)),u>1048576&&(u=1e6),m!=2&&(_=c.read_shift(2));var R=c.read_shift(2),P=o.codepage||1252;m!=2&&(c.l+=16,c.read_shift(1),c[c.l]!==0&&(P=e[c[c.l]]),c.l+=1,c.l+=2),v&&(c.l+=36);for(var O=[],j={},Q=Math.min(c.length,m==2?521:_-10-(x?264:0)),ne=v?32:11;c.l<Q&&c[c.l]!=13;)switch(j={},j.name=Xa.utils.decode(P,c.slice(c.l,c.l+ne)).replace(/[\u0000\r\n].*$/g,""),c.l+=ne,j.type=String.fromCharCode(c.read_shift(1)),m!=2&&!v&&(j.offset=c.read_shift(4)),j.len=c.read_shift(1),m==2&&(j.offset=c.read_shift(2)),j.dec=c.read_shift(1),j.name.length&&O.push(j),m!=2&&(c.l+=v?13:14),j.type){case"B":(!x||j.len!=8)&&o.WTF;break;case"G":case"P":o.WTF;break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw new Error("Unknown Field Type: "+j.type)}if(c[c.l]!==13&&(c.l=_-1),c.read_shift(1)!==13)throw new Error("DBF Terminator not found "+c.l+" "+c[c.l]);c.l=_;var k=0,z=0;for(l[0]=[],z=0;z!=O.length;++z)l[0][z]=O[z].name;for(;u-- >0;){if(c[c.l]===42){c.l+=R;continue}for(++c.l,l[++k]=[],z=0,z=0;z!=O.length;++z){var W=c.slice(c.l,c.l+O[z].len);c.l+=O[z].len,dr(W,0);var X=Xa.utils.decode(P,W);switch(O[z].type){case"C":X.trim().length&&(l[k][z]=X.replace(/\s+$/,""));break;case"D":X.length===8?l[k][z]=new Date(+X.slice(0,4),+X.slice(4,6)-1,+X.slice(6,8)):l[k][z]=X;break;case"F":l[k][z]=parseFloat(X.trim());break;case"+":case"I":l[k][z]=v?W.read_shift(-4,"i")^2147483648:W.read_shift(4,"i");break;case"L":switch(X.trim().toUpperCase()){case"Y":case"T":l[k][z]=!0;break;case"N":case"F":l[k][z]=!1;break;case"":case"?":break;default:throw new Error("DBF Unrecognized L:|"+X+"|")}break;case"M":if(!d)throw new Error("DBF Unexpected MEMO for type "+m.toString(16));l[k][z]="##MEMO##"+(v?parseInt(X.trim(),10):W.read_shift(4));break;case"N":X=X.replace(/\u0000/g,"").trim(),X&&X!="."&&(l[k][z]=+X||0);break;case"@":l[k][z]=new Date(W.read_shift(-8,"f")-621356832e5);break;case"T":l[k][z]=new Date((W.read_shift(4)-2440588)*864e5+W.read_shift(4));break;case"Y":l[k][z]=W.read_shift(4,"i")/1e4+W.read_shift(4,"i")/1e4*Math.pow(2,32);break;case"O":l[k][z]=-W.read_shift(-8,"f");break;case"B":if(x&&O[z].len==8){l[k][z]=W.read_shift(8,"f");break}case"G":case"P":W.l+=O[z].len;break;case"0":if(O[z].name==="_NullFlags")break;default:throw new Error("DBF Unsupported data type "+O[z].type)}}}if(m!=2&&c.l<c.length&&c[c.l++]!=26)throw new Error("DBF EOF Marker missing "+(c.l-1)+" of "+c.length+" "+c[c.l-1].toString(16));return o&&o.sheetRows&&(l=l.slice(0,o.sheetRows)),o.DBF=O,l}function n(f,o){var l=o||{};l.dateNF||(l.dateNF="yyyymmdd");var c=Bt(r(f,l),l);return c["!cols"]=l.DBF.map(function(m){return{wch:m.len,DBF:m}}),delete l.DBF,c}function a(f,o){try{return mt(n(f,o),o)}catch(l){if(o&&o.WTF)throw l}return{SheetNames:[],Sheets:{}}}var i={B:8,C:250,L:1,D:8,"?":0,"":0};function s(f,o){var l=o||{};if(+l.codepage>=0&&Zt(+l.codepage),l.type=="string")throw new Error("Cannot write DBF to JS string");var c=or(),m=bn(f,{header:1,raw:!0,cellDates:!0}),d=m[0],x=m.slice(1),v=f["!cols"]||[],u=0,_=0,R=0,P=1;for(u=0;u<d.length;++u){if(((v[u]||{}).DBF||{}).name){d[u]=v[u].DBF.name,++R;continue}if(d[u]!=null){if(++R,typeof d[u]=="number"&&(d[u]=d[u].toString(10)),typeof d[u]!="string")throw new Error("DBF Invalid column name "+d[u]+" |"+typeof d[u]+"|");if(d.indexOf(d[u])!==u){for(_=0;_<1024;++_)if(d.indexOf(d[u]+"_"+_)==-1){d[u]+="_"+_;break}}}}var O=Ie(f["!ref"]),j=[],Q=[],ne=[];for(u=0;u<=O.e.c-O.s.c;++u){var k="",z="",W=0,X=[];for(_=0;_<x.length;++_)x[_][u]!=null&&X.push(x[_][u]);if(X.length==0||d[u]==null){j[u]="?";continue}for(_=0;_<X.length;++_){switch(typeof X[_]){case"number":z="B";break;case"string":z="C";break;case"boolean":z="L";break;case"object":z=X[_]instanceof Date?"D":"C";break;default:z="C"}W=Math.max(W,String(X[_]).length),k=k&&k!=z?"C":z}W>250&&(W=250),z=((v[u]||{}).DBF||{}).type,z=="C"&&v[u].DBF.len>W&&(W=v[u].DBF.len),k=="B"&&z=="N"&&(k="N",ne[u]=v[u].DBF.dec,W=v[u].DBF.len),Q[u]=k=="C"||z=="N"?W:i[k]||0,P+=Q[u],j[u]=k}var J=c.next(32);for(J.write_shift(4,318902576),J.write_shift(4,x.length),J.write_shift(2,296+32*R),J.write_shift(2,P),u=0;u<4;++u)J.write_shift(4,0);for(J.write_shift(4,0|(+t[Pi]||3)<<8),u=0,_=0;u<d.length;++u)if(d[u]!=null){var Z=c.next(32),ae=(d[u].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);Z.write_shift(1,ae,"sbcs"),Z.write_shift(1,j[u]=="?"?"C":j[u],"sbcs"),Z.write_shift(4,_),Z.write_shift(1,Q[u]||i[j[u]]||0),Z.write_shift(1,ne[u]||0),Z.write_shift(1,2),Z.write_shift(4,0),Z.write_shift(1,0),Z.write_shift(4,0),Z.write_shift(4,0),_+=Q[u]||i[j[u]]||0}var pe=c.next(264);for(pe.write_shift(4,13),u=0;u<65;++u)pe.write_shift(4,0);for(u=0;u<x.length;++u){var ue=c.next(P);for(ue.write_shift(1,0),_=0;_<d.length;++_)if(d[_]!=null)switch(j[_]){case"L":ue.write_shift(1,x[u][_]==null?63:x[u][_]?84:70);break;case"B":ue.write_shift(8,x[u][_]||0,"f");break;case"N":var Me="0";for(typeof x[u][_]=="number"&&(Me=x[u][_].toFixed(ne[_]||0)),R=0;R<Q[_]-Me.length;++R)ue.write_shift(1,32);ue.write_shift(1,Me,"sbcs");break;case"D":x[u][_]?(ue.write_shift(4,("0000"+x[u][_].getFullYear()).slice(-4),"sbcs"),ue.write_shift(2,("00"+(x[u][_].getMonth()+1)).slice(-2),"sbcs"),ue.write_shift(2,("00"+x[u][_].getDate()).slice(-2),"sbcs")):ue.write_shift(8,"00000000","sbcs");break;case"C":var Ne=String(x[u][_]!=null?x[u][_]:"").slice(0,Q[_]);for(ue.write_shift(1,Ne,"sbcs"),R=0;R<Q[_]-Ne.length;++R)ue.write_shift(1,32);break}}return c.next(1).write_shift(1,26),c.end()}return{to_workbook:a,to_sheet:n,from_sheet:s}}(),$l=function(){var e={AA:"À",BA:"Á",CA:"Â",DA:195,HA:"Ä",JA:197,AE:"È",BE:"É",CE:"Ê",HE:"Ë",AI:"Ì",BI:"Í",CI:"Î",HI:"Ï",AO:"Ò",BO:"Ó",CO:"Ô",DO:213,HO:"Ö",AU:"Ù",BU:"Ú",CU:"Û",HU:"Ü",Aa:"à",Ba:"á",Ca:"â",Da:227,Ha:"ä",Ja:229,Ae:"è",Be:"é",Ce:"ê",He:"ë",Ai:"ì",Bi:"í",Ci:"î",Hi:"ï",Ao:"ò",Bo:"ó",Co:"ô",Do:245,Ho:"ö",Au:"ù",Bu:"ú",Cu:"û",Hu:"ü",KC:"Ç",Kc:"ç",q:"æ",z:"œ",a:"Æ",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},t=new RegExp("\x1BN("+Ze(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),r=function(d,x){var v=e[x];return typeof v=="number"?$a(v):v},n=function(d,x,v){var u=x.charCodeAt(0)-32<<4|v.charCodeAt(0)-48;return u==59?d:$a(u)};e["|"]=254;function a(d,x){switch(x.type){case"base64":return i(zr(d),x);case"binary":return i(d,x);case"buffer":return i(Ee&&Buffer.isBuffer(d)?d.toString("binary"):sn(d),x);case"array":return i(Gn(d),x)}throw new Error("Unrecognized type "+x.type)}function i(d,x){var v=d.split(/[\n\r]+/),u=-1,_=-1,R=0,P=0,O=[],j=[],Q=null,ne={},k=[],z=[],W=[],X=0,J;for(+x.codepage>=0&&Zt(+x.codepage);R!==v.length;++R){X=0;var Z=v[R].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,n).replace(t,r),ae=Z.replace(/;;/g,"\0").split(";").map(function(F){return F.replace(/\u0000/g,";")}),pe=ae[0],ue;if(Z.length>0)switch(pe){case"ID":break;case"E":break;case"B":break;case"O":break;case"W":break;case"P":ae[1].charAt(0)=="P"&&j.push(Z.slice(3).replace(/;;/g,";"));break;case"C":var Me=!1,Ne=!1,ur=!1,Be=!1,ze=-1,Ye=-1;for(P=1;P<ae.length;++P)switch(ae[P].charAt(0)){case"A":break;case"X":_=parseInt(ae[P].slice(1))-1,Ne=!0;break;case"Y":for(u=parseInt(ae[P].slice(1))-1,Ne||(_=0),J=O.length;J<=u;++J)O[J]=[];break;case"K":ue=ae[P].slice(1),ue.charAt(0)==='"'?ue=ue.slice(1,ue.length-1):ue==="TRUE"?ue=!0:ue==="FALSE"?ue=!1:isNaN(Gr(ue))?isNaN(en(ue).getDate())||(ue=lr(ue)):(ue=Gr(ue),Q!==null&&Yi(Q)&&(ue=Ji(ue))),Me=!0;break;case"E":Be=!0;var y=Yo(ae[P].slice(1),{r:u,c:_});O[u][_]=[O[u][_],y];break;case"S":ur=!0,O[u][_]=[O[u][_],"S5S"];break;case"G":break;case"R":ze=parseInt(ae[P].slice(1))-1;break;case"C":Ye=parseInt(ae[P].slice(1))-1;break;default:if(x&&x.WTF)throw new Error("SYLK bad record "+Z)}if(Me&&(O[u][_]&&O[u][_].length==2?O[u][_][0]=ue:O[u][_]=ue,Q=null),ur){if(Be)throw new Error("SYLK shared formula cannot have own formula");var b=ze>-1&&O[ze][Ye];if(!b||!b[1])throw new Error("SYLK shared formula cannot find base");O[u][_][1]=$o(b[1],{r:u-ze,c:_-Ye})}break;case"F":var C=0;for(P=1;P<ae.length;++P)switch(ae[P].charAt(0)){case"X":_=parseInt(ae[P].slice(1))-1,++C;break;case"Y":for(u=parseInt(ae[P].slice(1))-1,J=O.length;J<=u;++J)O[J]=[];break;case"M":X=parseInt(ae[P].slice(1))/20;break;case"F":break;case"G":break;case"P":Q=j[parseInt(ae[P].slice(1))];break;case"S":break;case"D":break;case"N":break;case"W":for(W=ae[P].slice(1).split(" "),J=parseInt(W[0],10);J<=parseInt(W[1],10);++J)X=parseInt(W[2],10),z[J-1]=X===0?{hidden:!0}:{wch:X},Na(z[J-1]);break;case"C":_=parseInt(ae[P].slice(1))-1,z[_]||(z[_]={});break;case"R":u=parseInt(ae[P].slice(1))-1,k[u]||(k[u]={}),X>0?(k[u].hpt=X,k[u].hpx=B0(X)):X===0&&(k[u].hidden=!0);break;default:if(x&&x.WTF)throw new Error("SYLK bad record "+Z)}C<1&&(Q=null);break;default:if(x&&x.WTF)throw new Error("SYLK bad record "+Z)}}return k.length>0&&(ne["!rows"]=k),z.length>0&&(ne["!cols"]=z),x&&x.sheetRows&&(O=O.slice(0,x.sheetRows)),[O,ne]}function s(d,x){var v=a(d,x),u=v[0],_=v[1],R=Bt(u,x);return Ze(_).forEach(function(P){R[P]=_[P]}),R}function f(d,x){return mt(s(d,x),x)}function o(d,x,v,u){var _="C;Y"+(v+1)+";X"+(u+1)+";K";switch(d.t){case"n":_+=d.v||0,d.f&&!d.F&&(_+=";E"+Pa(d.f,{r:v,c:u}));break;case"b":_+=d.v?"TRUE":"FALSE";break;case"e":_+=d.w||d.v;break;case"d":_+='"'+(d.w||d.v)+'"';break;case"s":_+='"'+d.v.replace(/"/g,"").replace(/;/g,";;")+'"';break}return _}function l(d,x){x.forEach(function(v,u){var _="F;W"+(u+1)+" "+(u+1)+" ";v.hidden?_+="0":(typeof v.width=="number"&&!v.wpx&&(v.wpx=Pn(v.width)),typeof v.wpx=="number"&&!v.wch&&(v.wch=Ln(v.wpx)),typeof v.wch=="number"&&(_+=Math.round(v.wch))),_.charAt(_.length-1)!=" "&&d.push(_)})}function c(d,x){x.forEach(function(v,u){var _="F;";v.hidden?_+="M0;":v.hpt?_+="M"+20*v.hpt+";":v.hpx&&(_+="M"+20*Mn(v.hpx)+";"),_.length>2&&d.push(_+"R"+(u+1))})}function m(d,x){var v=["ID;PWXL;N;E"],u=[],_=Ie(d["!ref"]),R,P=Array.isArray(d),O=`\r
`;v.push("P;PGeneral"),v.push("F;P0;DG0G8;M255"),d["!cols"]&&l(v,d["!cols"]),d["!rows"]&&c(v,d["!rows"]),v.push("B;Y"+(_.e.r-_.s.r+1)+";X"+(_.e.c-_.s.c+1)+";D"+[_.s.c,_.s.r,_.e.c,_.e.r].join(" "));for(var j=_.s.r;j<=_.e.r;++j)for(var Q=_.s.c;Q<=_.e.c;++Q){var ne=Ae({r:j,c:Q});R=P?(d[j]||[])[Q]:d[ne],!(!R||R.v==null&&(!R.f||R.F))&&u.push(o(R,d,j,Q))}return v.join(O)+O+u.join(O)+O+"E"+O}return{to_workbook:f,to_sheet:s,from_sheet:m}}(),Xl=function(){function e(i,s){switch(s.type){case"base64":return t(zr(i),s);case"binary":return t(i,s);case"buffer":return t(Ee&&Buffer.isBuffer(i)?i.toString("binary"):sn(i),s);case"array":return t(Gn(i),s)}throw new Error("Unrecognized type "+s.type)}function t(i,s){for(var f=i.split(`
`),o=-1,l=-1,c=0,m=[];c!==f.length;++c){if(f[c].trim()==="BOT"){m[++o]=[],l=0;continue}if(!(o<0)){var d=f[c].trim().split(","),x=d[0],v=d[1];++c;for(var u=f[c]||"";(u.match(/["]/g)||[]).length&1&&c<f.length-1;)u+=`
`+f[++c];switch(u=u.trim(),+x){case-1:if(u==="BOT"){m[++o]=[],l=0;continue}else if(u!=="EOD")throw new Error("Unrecognized DIF special command "+u);break;case 0:u==="TRUE"?m[o][l]=!0:u==="FALSE"?m[o][l]=!1:isNaN(Gr(v))?isNaN(en(v).getDate())?m[o][l]=v:m[o][l]=lr(v):m[o][l]=Gr(v),++l;break;case 1:u=u.slice(1,u.length-1),u=u.replace(/""/g,'"'),u&&u.match(/^=".*"$/)&&(u=u.slice(2,-1)),m[o][l++]=u!==""?u:null;break}if(u==="EOD")break}}return s&&s.sheetRows&&(m=m.slice(0,s.sheetRows)),m}function r(i,s){return Bt(e(i,s),s)}function n(i,s){return mt(r(i,s),s)}var a=function(){var i=function(o,l,c,m,d){o.push(l),o.push(c+","+m),o.push('"'+d.replace(/"/g,'""')+'"')},s=function(o,l,c,m){o.push(l+","+c),o.push(l==1?'"'+m.replace(/"/g,'""')+'"':m)};return function(o){var l=[],c=Ie(o["!ref"]),m,d=Array.isArray(o);i(l,"TABLE",0,1,"sheetjs"),i(l,"VECTORS",0,c.e.r-c.s.r+1,""),i(l,"TUPLES",0,c.e.c-c.s.c+1,""),i(l,"DATA",0,0,"");for(var x=c.s.r;x<=c.e.r;++x){s(l,-1,0,"BOT");for(var v=c.s.c;v<=c.e.c;++v){var u=Ae({r:x,c:v});if(m=d?(o[x]||[])[v]:o[u],!m){s(l,1,0,"");continue}switch(m.t){case"n":var _=m.w;!_&&m.v!=null&&(_=m.v),_==null?m.f&&!m.F?s(l,1,0,"="+m.f):s(l,1,0,""):s(l,0,_,"V");break;case"b":s(l,0,m.v?1:0,m.v?"TRUE":"FALSE");break;case"s":s(l,1,0,isNaN(m.v)?m.v:'="'+m.v+'"');break;case"d":m.w||(m.w=tt(m.z||Le[14],cr(lr(m.v)))),s(l,0,m.w,"V");break;default:s(l,1,0,"")}}}s(l,-1,0,"EOD");var R=`\r
`,P=l.join(R);return P}}();return{to_workbook:n,to_sheet:r,from_sheet:a}}(),R0=function(){function e(m){return m.replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,`
`)}function t(m){return m.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function r(m,d){for(var x=m.split(`
`),v=-1,u=-1,_=0,R=[];_!==x.length;++_){var P=x[_].trim().split(":");if(P[0]==="cell"){var O=Ge(P[1]);if(R.length<=O.r)for(v=R.length;v<=O.r;++v)R[v]||(R[v]=[]);switch(v=O.r,u=O.c,P[2]){case"t":R[v][u]=e(P[3]);break;case"v":R[v][u]=+P[3];break;case"vtf":var j=P[P.length-1];case"vtc":switch(P[3]){case"nl":R[v][u]=!!+P[4];break;default:R[v][u]=+P[4];break}P[2]=="vtf"&&(R[v][u]=[R[v][u],j])}}}return d&&d.sheetRows&&(R=R.slice(0,d.sheetRows)),R}function n(m,d){return Bt(r(m,d),d)}function a(m,d){return mt(n(m,d),d)}var i=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join(`
`),s=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join(`
`)+`
`,f=["# SocialCalc Spreadsheet Control Save","part:sheet"].join(`
`),o="--SocialCalcSpreadsheetControlSave--";function l(m){if(!m||!m["!ref"])return"";for(var d=[],x=[],v,u="",_=gr(m["!ref"]),R=Array.isArray(m),P=_.s.r;P<=_.e.r;++P)for(var O=_.s.c;O<=_.e.c;++O)if(u=Ae({r:P,c:O}),v=R?(m[P]||[])[O]:m[u],!(!v||v.v==null||v.t==="z")){switch(x=["cell",u,"t"],v.t){case"s":case"str":x.push(t(v.v));break;case"n":v.f?(x[2]="vtf",x[3]="n",x[4]=v.v,x[5]=t(v.f)):(x[2]="v",x[3]=v.v);break;case"b":x[2]="vt"+(v.f?"f":"c"),x[3]="nl",x[4]=v.v?"1":"0",x[5]=t(v.f||(v.v?"TRUE":"FALSE"));break;case"d":var j=cr(lr(v.v));x[2]="vtc",x[3]="nd",x[4]=""+j,x[5]=v.w||tt(v.z||Le[14],j);break;case"e":continue}d.push(x.join(":"))}return d.push("sheet:c:"+(_.e.c-_.s.c+1)+":r:"+(_.e.r-_.s.r+1)+":tvf:1"),d.push("valueformat:1:text-wiki"),d.join(`
`)}function c(m){return[i,s,f,s,l(m),o].join(`
`)}return{to_workbook:a,to_sheet:n,from_sheet:c}}(),Kl=function(){function e(c,m,d,x,v){v.raw?m[d][x]=c:c===""||(c==="TRUE"?m[d][x]=!0:c==="FALSE"?m[d][x]=!1:isNaN(Gr(c))?isNaN(en(c).getDate())?m[d][x]=c:m[d][x]=lr(c):m[d][x]=Gr(c))}function t(c,m){var d=m||{},x=[];if(!c||c.length===0)return x;for(var v=c.split(/[\r\n]/),u=v.length-1;u>=0&&v[u].length===0;)--u;for(var _=10,R=0,P=0;P<=u;++P)R=v[P].indexOf(" "),R==-1?R=v[P].length:R++,_=Math.max(_,R);for(P=0;P<=u;++P){x[P]=[];var O=0;for(e(v[P].slice(0,_).trim(),x,P,O,d),O=1;O<=(v[P].length-_)/10+1;++O)e(v[P].slice(_+(O-1)*10,_+O*10).trim(),x,P,O,d)}return d.sheetRows&&(x=x.slice(0,d.sheetRows)),x}var r={44:",",9:"	",59:";",124:"|"},n={44:3,9:2,59:1,124:0};function a(c){for(var m={},d=!1,x=0,v=0;x<c.length;++x)(v=c.charCodeAt(x))==34?d=!d:!d&&v in r&&(m[v]=(m[v]||0)+1);v=[];for(x in m)Object.prototype.hasOwnProperty.call(m,x)&&v.push([m[x],x]);if(!v.length){m=n;for(x in m)Object.prototype.hasOwnProperty.call(m,x)&&v.push([m[x],x])}return v.sort(function(u,_){return u[0]-_[0]||n[u[1]]-n[_[1]]}),r[v.pop()[1]]||44}function i(c,m){var d=m||{},x="",v=d.dense?[]:{},u={s:{c:0,r:0},e:{c:0,r:0}};c.slice(0,4)=="sep="?c.charCodeAt(5)==13&&c.charCodeAt(6)==10?(x=c.charAt(4),c=c.slice(7)):c.charCodeAt(5)==13||c.charCodeAt(5)==10?(x=c.charAt(4),c=c.slice(6)):x=a(c.slice(0,1024)):d&&d.FS?x=d.FS:x=a(c.slice(0,1024));var _=0,R=0,P=0,O=0,j=0,Q=x.charCodeAt(0),ne=!1,k=0,z=c.charCodeAt(0);c=c.replace(/\r\n/mg,`
`);var W=d.dateNF!=null?hf(d.dateNF):null;function X(){var J=c.slice(O,j),Z={};if(J.charAt(0)=='"'&&J.charAt(J.length-1)=='"'&&(J=J.slice(1,-1).replace(/""/g,'"')),J.length===0)Z.t="z";else if(d.raw)Z.t="s",Z.v=J;else if(J.trim().length===0)Z.t="s",Z.v=J;else if(J.charCodeAt(0)==61)J.charCodeAt(1)==34&&J.charCodeAt(J.length-1)==34?(Z.t="s",Z.v=J.slice(2,-1).replace(/""/g,'"')):Xo(J)?(Z.t="n",Z.f=J.slice(1)):(Z.t="s",Z.v=J);else if(J=="TRUE")Z.t="b",Z.v=!0;else if(J=="FALSE")Z.t="b",Z.v=!1;else if(!isNaN(P=Gr(J)))Z.t="n",d.cellText!==!1&&(Z.w=J),Z.v=P;else if(!isNaN(en(J).getDate())||W&&J.match(W)){Z.z=d.dateNF||Le[14];var ae=0;W&&J.match(W)&&(J=uf(J,d.dateNF,J.match(W)||[]),ae=1),d.cellDates?(Z.t="d",Z.v=lr(J,ae)):(Z.t="n",Z.v=cr(lr(J,ae))),d.cellText!==!1&&(Z.w=tt(Z.z,Z.v instanceof Date?cr(Z.v):Z.v)),d.cellNF||delete Z.z}else Z.t="s",Z.v=J;if(Z.t=="z"||(d.dense?(v[_]||(v[_]=[]),v[_][R]=Z):v[Ae({c:R,r:_})]=Z),O=j+1,z=c.charCodeAt(O),u.e.c<R&&(u.e.c=R),u.e.r<_&&(u.e.r=_),k==Q)++R;else if(R=0,++_,d.sheetRows&&d.sheetRows<=_)return!0}e:for(;j<c.length;++j)switch(k=c.charCodeAt(j)){case 34:z===34&&(ne=!ne);break;case Q:case 10:case 13:if(!ne&&X())break e;break}return j-O>0&&X(),v["!ref"]=je(u),v}function s(c,m){return!(m&&m.PRN)||m.FS||c.slice(0,4)=="sep="||c.indexOf("	")>=0||c.indexOf(",")>=0||c.indexOf(";")>=0?i(c,m):Bt(t(c,m),m)}function f(c,m){var d="",x=m.type=="string"?[0,0,0,0]:lx(c,m);switch(m.type){case"base64":d=zr(c);break;case"binary":d=c;break;case"buffer":m.codepage==65001?d=c.toString("utf8"):(m.codepage,d=Ee&&Buffer.isBuffer(c)?c.toString("binary"):sn(c));break;case"array":d=Gn(c);break;case"string":d=c;break;default:throw new Error("Unrecognized type "+m.type)}return x[0]==239&&x[1]==187&&x[2]==191?d=$t(d.slice(3)):m.type!="string"&&m.type!="buffer"&&m.codepage==65001?d=$t(d):m.type=="binary",d.slice(0,19)=="socialcalc:version:"?R0.to_sheet(m.type=="string"?d:$t(d),m):s(d,m)}function o(c,m){return mt(f(c,m),m)}function l(c){for(var m=[],d=Ie(c["!ref"]),x,v=Array.isArray(c),u=d.s.r;u<=d.e.r;++u){for(var _=[],R=d.s.c;R<=d.e.c;++R){var P=Ae({r:u,c:R});if(x=v?(c[u]||[])[R]:c[P],!x||x.v==null){_.push("          ");continue}for(var O=(x.w||(Yr(x),x.w)||"").slice(0,10);O.length<10;)O+=" ";_.push(O+(R===0?" ":""))}m.push(_.join(""))}return m.join(`
`)}return{to_workbook:o,to_sheet:f,from_sheet:l}}(),Ai=function(){function e(y,b,C){if(y){dr(y,y.l||0);for(var F=C.Enum||ze;y.l<y.length;){var K=y.read_shift(2),le=F[K]||F[65535],ce=y.read_shift(2),fe=y.l+ce,ie=le.f&&le.f(y,ce,C);if(y.l=fe,b(ie,le,K))return}}}function t(y,b){switch(b.type){case"base64":return r(Dr(zr(y)),b);case"binary":return r(Dr(y),b);case"buffer":case"array":return r(y,b)}throw"Unsupported type "+b.type}function r(y,b){if(!y)return y;var C=b||{},F=C.dense?[]:{},K="Sheet1",le="",ce=0,fe={},ie=[],ge=[],de={s:{r:0,c:0},e:{r:0,c:0}},Qe=C.sheetRows||0;if(y[2]==0&&(y[3]==8||y[3]==9)&&y.length>=16&&y[14]==5&&y[15]===108)throw new Error("Unsupported Works 3 for Mac file");if(y[2]==2)C.Enum=ze,e(y,function(se,ar,Ar){switch(Ar){case 0:C.vers=se,se>=4096&&(C.qpro=!0);break;case 6:de=se;break;case 204:se&&(le=se);break;case 222:le=se;break;case 15:case 51:C.qpro||(se[1].v=se[1].v.slice(1));case 13:case 14:case 16:Ar==14&&(se[2]&112)==112&&(se[2]&15)>1&&(se[2]&15)<15&&(se[1].z=C.dateNF||Le[14],C.cellDates&&(se[1].t="d",se[1].v=Ji(se[1].v))),C.qpro&&se[3]>ce&&(F["!ref"]=je(de),fe[K]=F,ie.push(K),F=C.dense?[]:{},de={s:{r:0,c:0},e:{r:0,c:0}},ce=se[3],K=le||"Sheet"+(ce+1),le="");var Fr=C.dense?(F[se[0].r]||[])[se[0].c]:F[Ae(se[0])];if(Fr){Fr.t=se[1].t,Fr.v=se[1].v,se[1].z!=null&&(Fr.z=se[1].z),se[1].f!=null&&(Fr.f=se[1].f);break}C.dense?(F[se[0].r]||(F[se[0].r]=[]),F[se[0].r][se[0].c]=se[1]):F[Ae(se[0])]=se[1];break}},C);else if(y[2]==26||y[2]==14)C.Enum=Ye,y[2]==14&&(C.qpro=!0,y.l=0),e(y,function(se,ar,Ar){switch(Ar){case 204:K=se;break;case 22:se[1].v=se[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(se[3]>ce&&(F["!ref"]=je(de),fe[K]=F,ie.push(K),F=C.dense?[]:{},de={s:{r:0,c:0},e:{r:0,c:0}},ce=se[3],K="Sheet"+(ce+1)),Qe>0&&se[0].r>=Qe)break;C.dense?(F[se[0].r]||(F[se[0].r]=[]),F[se[0].r][se[0].c]=se[1]):F[Ae(se[0])]=se[1],de.e.c<se[0].c&&(de.e.c=se[0].c),de.e.r<se[0].r&&(de.e.r=se[0].r);break;case 27:se[14e3]&&(ge[se[14e3][0]]=se[14e3][1]);break;case 1537:ge[se[0]]=se[1],se[0]==ce&&(K=se[1]);break}},C);else throw new Error("Unrecognized LOTUS BOF "+y[2]);if(F["!ref"]=je(de),fe[le||K]=F,ie.push(le||K),!ge.length)return{SheetNames:ie,Sheets:fe};for(var Te={},Pr=[],Re=0;Re<ge.length;++Re)fe[ie[Re]]?(Pr.push(ge[Re]||ie[Re]),Te[ge[Re]]=fe[ge[Re]]||fe[ie[Re]]):(Pr.push(ge[Re]),Te[ge[Re]]={"!ref":"A1"});return{SheetNames:Pr,Sheets:Te}}function n(y,b){var C=b||{};if(+C.codepage>=0&&Zt(+C.codepage),C.type=="string")throw new Error("Cannot write WK1 to JS string");var F=or(),K=Ie(y["!ref"]),le=Array.isArray(y),ce=[];re(F,0,i(1030)),re(F,6,o(K));for(var fe=Math.min(K.e.r,8191),ie=K.s.r;ie<=fe;++ie)for(var ge=qe(ie),de=K.s.c;de<=K.e.c;++de){ie===K.s.r&&(ce[de]=tr(de));var Qe=ce[de]+ge,Te=le?(y[ie]||[])[de]:y[Qe];if(!(!Te||Te.t=="z"))if(Te.t=="n")(Te.v|0)==Te.v&&Te.v>=-32768&&Te.v<=32767?re(F,13,x(ie,de,Te.v)):re(F,14,u(ie,de,Te.v));else{var Pr=Yr(Te);re(F,15,m(ie,de,Pr.slice(0,239)))}}return re(F,1),F.end()}function a(y,b){var C=b||{};if(+C.codepage>=0&&Zt(+C.codepage),C.type=="string")throw new Error("Cannot write WK3 to JS string");var F=or();re(F,0,s(y));for(var K=0,le=0;K<y.SheetNames.length;++K)(y.Sheets[y.SheetNames[K]]||{})["!ref"]&&re(F,27,Be(y.SheetNames[K],le++));var ce=0;for(K=0;K<y.SheetNames.length;++K){var fe=y.Sheets[y.SheetNames[K]];if(!(!fe||!fe["!ref"])){for(var ie=Ie(fe["!ref"]),ge=Array.isArray(fe),de=[],Qe=Math.min(ie.e.r,8191),Te=ie.s.r;Te<=Qe;++Te)for(var Pr=qe(Te),Re=ie.s.c;Re<=ie.e.c;++Re){Te===ie.s.r&&(de[Re]=tr(Re));var se=de[Re]+Pr,ar=ge?(fe[Te]||[])[Re]:fe[se];if(!(!ar||ar.t=="z"))if(ar.t=="n")re(F,23,X(Te,Re,ce,ar.v));else{var Ar=Yr(ar);re(F,22,k(Te,Re,ce,Ar.slice(0,239)))}}++ce}}return re(F,1),F.end()}function i(y){var b=H(2);return b.write_shift(2,y),b}function s(y){var b=H(26);b.write_shift(2,4096),b.write_shift(2,4),b.write_shift(4,0);for(var C=0,F=0,K=0,le=0;le<y.SheetNames.length;++le){var ce=y.SheetNames[le],fe=y.Sheets[ce];if(!(!fe||!fe["!ref"])){++K;var ie=gr(fe["!ref"]);C<ie.e.r&&(C=ie.e.r),F<ie.e.c&&(F=ie.e.c)}}return C>8191&&(C=8191),b.write_shift(2,C),b.write_shift(1,K),b.write_shift(1,F),b.write_shift(2,0),b.write_shift(2,0),b.write_shift(1,1),b.write_shift(1,2),b.write_shift(4,0),b.write_shift(4,0),b}function f(y,b,C){var F={s:{c:0,r:0},e:{c:0,r:0}};return b==8&&C.qpro?(F.s.c=y.read_shift(1),y.l++,F.s.r=y.read_shift(2),F.e.c=y.read_shift(1),y.l++,F.e.r=y.read_shift(2),F):(F.s.c=y.read_shift(2),F.s.r=y.read_shift(2),b==12&&C.qpro&&(y.l+=2),F.e.c=y.read_shift(2),F.e.r=y.read_shift(2),b==12&&C.qpro&&(y.l+=2),F.s.c==65535&&(F.s.c=F.e.c=F.s.r=F.e.r=0),F)}function o(y){var b=H(8);return b.write_shift(2,y.s.c),b.write_shift(2,y.s.r),b.write_shift(2,y.e.c),b.write_shift(2,y.e.r),b}function l(y,b,C){var F=[{c:0,r:0},{t:"n",v:0},0,0];return C.qpro&&C.vers!=20768?(F[0].c=y.read_shift(1),F[3]=y.read_shift(1),F[0].r=y.read_shift(2),y.l+=2):(F[2]=y.read_shift(1),F[0].c=y.read_shift(2),F[0].r=y.read_shift(2)),F}function c(y,b,C){var F=y.l+b,K=l(y,b,C);if(K[1].t="s",C.vers==20768){y.l++;var le=y.read_shift(1);return K[1].v=y.read_shift(le,"utf8"),K}return C.qpro&&y.l++,K[1].v=y.read_shift(F-y.l,"cstr"),K}function m(y,b,C){var F=H(7+C.length);F.write_shift(1,255),F.write_shift(2,b),F.write_shift(2,y),F.write_shift(1,39);for(var K=0;K<F.length;++K){var le=C.charCodeAt(K);F.write_shift(1,le>=128?95:le)}return F.write_shift(1,0),F}function d(y,b,C){var F=l(y,b,C);return F[1].v=y.read_shift(2,"i"),F}function x(y,b,C){var F=H(7);return F.write_shift(1,255),F.write_shift(2,b),F.write_shift(2,y),F.write_shift(2,C,"i"),F}function v(y,b,C){var F=l(y,b,C);return F[1].v=y.read_shift(8,"f"),F}function u(y,b,C){var F=H(13);return F.write_shift(1,255),F.write_shift(2,b),F.write_shift(2,y),F.write_shift(8,C,"f"),F}function _(y,b,C){var F=y.l+b,K=l(y,b,C);if(K[1].v=y.read_shift(8,"f"),C.qpro)y.l=F;else{var le=y.read_shift(2);j(y.slice(y.l,y.l+le),K),y.l+=le}return K}function R(y,b,C){var F=b&32768;return b&=-32769,b=(F?y:0)+(b>=8192?b-16384:b),(F?"":"$")+(C?tr(b):qe(b))}var P={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},O=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function j(y,b){dr(y,0);for(var C=[],F=0,K="",le="",ce="",fe="";y.l<y.length;){var ie=y[y.l++];switch(ie){case 0:C.push(y.read_shift(8,"f"));break;case 1:le=R(b[0].c,y.read_shift(2),!0),K=R(b[0].r,y.read_shift(2),!1),C.push(le+K);break;case 2:{var ge=R(b[0].c,y.read_shift(2),!0),de=R(b[0].r,y.read_shift(2),!1);le=R(b[0].c,y.read_shift(2),!0),K=R(b[0].r,y.read_shift(2),!1),C.push(ge+de+":"+le+K)}break;case 3:if(y.l<y.length)return;break;case 4:C.push("("+C.pop()+")");break;case 5:C.push(y.read_shift(2));break;case 6:{for(var Qe="";ie=y[y.l++];)Qe+=String.fromCharCode(ie);C.push('"'+Qe.replace(/"/g,'""')+'"')}break;case 8:C.push("-"+C.pop());break;case 23:C.push("+"+C.pop());break;case 22:C.push("NOT("+C.pop()+")");break;case 20:case 21:fe=C.pop(),ce=C.pop(),C.push(["AND","OR"][ie-20]+"("+ce+","+fe+")");break;default:if(ie<32&&O[ie])fe=C.pop(),ce=C.pop(),C.push(ce+O[ie]+fe);else if(P[ie]){if(F=P[ie][1],F==69&&(F=y[y.l++]),F>C.length)return;var Te=C.slice(-F);C.length-=F,C.push(P[ie][0]+"("+Te.join(",")+")")}else return ie<=7||ie<=24||ie<=30||ie<=115,void 0}}C.length==1&&(b[1].f=""+C[0])}function Q(y){var b=[{c:0,r:0},{t:"n",v:0},0];return b[0].r=y.read_shift(2),b[3]=y[y.l++],b[0].c=y[y.l++],b}function ne(y,b){var C=Q(y);return C[1].t="s",C[1].v=y.read_shift(b-4,"cstr"),C}function k(y,b,C,F){var K=H(6+F.length);K.write_shift(2,y),K.write_shift(1,C),K.write_shift(1,b),K.write_shift(1,39);for(var le=0;le<F.length;++le){var ce=F.charCodeAt(le);K.write_shift(1,ce>=128?95:ce)}return K.write_shift(1,0),K}function z(y,b){var C=Q(y);C[1].v=y.read_shift(2);var F=C[1].v>>1;if(C[1].v&1)switch(F&7){case 0:F=(F>>3)*5e3;break;case 1:F=(F>>3)*500;break;case 2:F=(F>>3)/20;break;case 3:F=(F>>3)/200;break;case 4:F=(F>>3)/2e3;break;case 5:F=(F>>3)/2e4;break;case 6:F=(F>>3)/16;break;case 7:F=(F>>3)/64;break}return C[1].v=F,C}function W(y,b){var C=Q(y),F=y.read_shift(4),K=y.read_shift(4),le=y.read_shift(2);if(le==65535)return F===0&&K===3221225472?(C[1].t="e",C[1].v=15):F===0&&K===3489660928?(C[1].t="e",C[1].v=42):C[1].v=0,C;var ce=le&32768;return le=(le&32767)-16446,C[1].v=(1-ce*2)*(K*Math.pow(2,le+32)+F*Math.pow(2,le)),C}function X(y,b,C,F){var K=H(14);if(K.write_shift(2,y),K.write_shift(1,C),K.write_shift(1,b),F==0)return K.write_shift(4,0),K.write_shift(4,0),K.write_shift(2,65535),K;var le=0,ce=0,fe=0,ie=0;return F<0&&(le=1,F=-F),ce=Math.log2(F)|0,F/=Math.pow(2,ce-31),ie=F>>>0,(ie&2147483648)==0&&(F/=2,++ce,ie=F>>>0),F-=ie,ie|=2147483648,ie>>>=0,F*=Math.pow(2,32),fe=F>>>0,K.write_shift(4,fe),K.write_shift(4,ie),ce+=16383+(le?32768:0),K.write_shift(2,ce),K}function J(y,b){var C=W(y);return y.l+=b-14,C}function Z(y,b){var C=Q(y),F=y.read_shift(4);return C[1].v=F>>6,C}function ae(y,b){var C=Q(y),F=y.read_shift(8,"f");return C[1].v=F,C}function pe(y,b){var C=ae(y);return y.l+=b-10,C}function ue(y,b){return y[y.l+b-1]==0?y.read_shift(b,"cstr"):""}function Me(y,b){var C=y[y.l++];C>b-1&&(C=b-1);for(var F="";F.length<C;)F+=String.fromCharCode(y[y.l++]);return F}function Ne(y,b,C){if(!(!C.qpro||b<21)){var F=y.read_shift(1);y.l+=17,y.l+=1,y.l+=2;var K=y.read_shift(b-21,"cstr");return[F,K]}}function ur(y,b){for(var C={},F=y.l+b;y.l<F;){var K=y.read_shift(2);if(K==14e3){for(C[K]=[0,""],C[K][0]=y.read_shift(2);y[y.l];)C[K][1]+=String.fromCharCode(y[y.l]),y.l++;y.l++}}return C}function Be(y,b){var C=H(5+y.length);C.write_shift(2,14e3),C.write_shift(2,b);for(var F=0;F<y.length;++F){var K=y.charCodeAt(F);C[C.l++]=K>127?95:K}return C[C.l++]=0,C}var ze={0:{n:"BOF",f:k0},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:d},14:{n:"NUMBER",f:v},15:{n:"LABEL",f:c},16:{n:"FORMULA",f:_},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:c},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:ue},222:{n:"SHEETNAMELP",f:Me},65535:{n:""}},Ye={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:ne},23:{n:"NUMBER17",f:W},24:{n:"NUMBER18",f:z},25:{n:"FORMULA19",f:J},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:ur},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:Z},38:{n:"??"},39:{n:"NUMBER27",f:ae},40:{n:"FORMULA28",f:pe},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:ue},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:Ne},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:n,book_to_wk3:a,to_workbook:t}}(),Jl=/^\s|\s$|[\t\n\r]/;function P0(e,t){if(!t.bookSST)return"";var r=[Ue];r[r.length]=ee("sst",null,{xmlns:Mt[0],count:e.Count,uniqueCount:e.Unique});for(var n=0;n!=e.length;++n)if(e[n]!=null){var a=e[n],i="<si>";a.r?i+=a.r:(i+="<t",a.t||(a.t=""),a.t.match(Jl)&&(i+=' xml:space="preserve"'),i+=">"+ye(a.t)+"</t>"),i+="</si>",r[r.length]=i}return r.length>2&&(r[r.length]="</sst>",r[1]=r[1].replace("/>",">")),r.join("")}function ql(e){return[e.read_shift(4),e.read_shift(4)]}function Zl(e,t){return t||(t=H(8)),t.write_shift(4,e.Count),t.write_shift(4,e.Unique),t}var Ql=Gf;function eo(e){var t=or();Y(t,159,Zl(e));for(var r=0;r<e.length;++r)Y(t,19,Ql(e[r]));return Y(t,160),t.end()}function ro(e){for(var t=[],r=e.split(""),n=0;n<r.length;++n)t[n]=r[n].charCodeAt(0);return t}function L0(e){var t=0,r,n=ro(e),a=n.length+1,i,s,f,o,l;for(r=ut(a),r[0]=n.length,i=1;i!=a;++i)r[i]=n[i-1];for(i=a-1;i>=0;--i)s=r[i],f=(t&16384)===0?0:1,o=t<<1&32767,l=f|o,t=l^s;return t^52811}var to=function(){function e(a,i){switch(i.type){case"base64":return t(zr(a),i);case"binary":return t(a,i);case"buffer":return t(Ee&&Buffer.isBuffer(a)?a.toString("binary"):sn(a),i);case"array":return t(Gn(a),i)}throw new Error("Unrecognized type "+i.type)}function t(a,i){var s=i||{},f=s.dense?[]:{},o=a.match(/\\trowd.*?\\row\b/g);if(!o.length)throw new Error("RTF missing table");var l={s:{c:0,r:0},e:{c:0,r:o.length-1}};return o.forEach(function(c,m){Array.isArray(f)&&(f[m]=[]);for(var d=/\\\w+\b/g,x=0,v,u=-1;v=d.exec(c);){switch(v[0]){case"\\cell":var _=c.slice(x,d.lastIndex-v[0].length);if(_[0]==" "&&(_=_.slice(1)),++u,_.length){var R={v:_,t:"s"};Array.isArray(f)?f[m][u]=R:f[Ae({r:m,c:u})]=R}break}x=d.lastIndex}u>l.e.c&&(l.e.c=u)}),f["!ref"]=je(l),f}function r(a,i){return mt(e(a,i),i)}function n(a){for(var i=["{\\rtf1\\ansi"],s=Ie(a["!ref"]),f,o=Array.isArray(a),l=s.s.r;l<=s.e.r;++l){i.push("\\trowd\\trautofit1");for(var c=s.s.c;c<=s.e.c;++c)i.push("\\cellx"+(c+1));for(i.push("\\pard\\intbl"),c=s.s.c;c<=s.e.c;++c){var m=Ae({r:l,c});f=o?(a[l]||[])[c]:a[m],!(!f||f.v==null&&(!f.f||f.F))&&(i.push(" "+(f.w||(Yr(f),f.w))),i.push("\\cell"))}i.push("\\pard\\intbl\\row")}return i.join("")+"}"}return{to_workbook:r,to_sheet:e,from_sheet:n}}();function Fi(e){for(var t=0,r=1;t!=3;++t)r=r*256+(e[t]>255?255:e[t]<0?0:e[t]);return r.toString(16).toUpperCase().slice(1)}var no=6,Vr=no;function Pn(e){return Math.floor((e+Math.round(128/Vr)/256)*Vr)}function Ln(e){return Math.floor((e-5)/Vr*100+.5)/100}function ga(e){return Math.round((e*Vr+5)/Vr*256)/256}function Na(e){e.width?(e.wpx=Pn(e.width),e.wch=Ln(e.wpx),e.MDW=Vr):e.wpx?(e.wch=Ln(e.wpx),e.width=ga(e.wch),e.MDW=Vr):typeof e.wch=="number"&&(e.width=ga(e.wch),e.wpx=Pn(e.width),e.MDW=Vr),e.customWidth&&delete e.customWidth}var ao=96,M0=ao;function Mn(e){return e*96/M0}function B0(e){return e*M0/96}function io(e){var t=["<numFmts>"];return[[5,8],[23,26],[41,44],[50,392]].forEach(function(r){for(var n=r[0];n<=r[1];++n)e[n]!=null&&(t[t.length]=ee("numFmt",null,{numFmtId:n,formatCode:ye(e[n])}))}),t.length===1?"":(t[t.length]="</numFmts>",t[0]=ee("numFmts",null,{count:t.length-2}).replace("/>",">"),t.join(""))}function so(e){var t=[];return t[t.length]=ee("cellXfs",null),e.forEach(function(r){t[t.length]=ee("xf",null,r)}),t[t.length]="</cellXfs>",t.length===2?"":(t[0]=ee("cellXfs",null,{count:t.length-2}).replace("/>",">"),t.join(""))}function b0(e,t){var r=[Ue,ee("styleSheet",null,{xmlns:Mt[0],"xmlns:vt":He.vt})],n;return e.SSF&&(n=io(e.SSF))!=null&&(r[r.length]=n),r[r.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>',r[r.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>',r[r.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>',r[r.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>',(n=so(t.cellXfs))&&(r[r.length]=n),r[r.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>',r[r.length]='<dxfs count="0"/>',r[r.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>',r.length>2&&(r[r.length]="</styleSheet>",r[1]=r[1].replace("/>",">")),r.join("")}function fo(e,t){var r=e.read_shift(2),n=nr(e);return[r,n]}function lo(e,t,r){r||(r=H(6+4*t.length)),r.write_shift(2,e),Ve(t,r);var n=r.length>r.l?r.slice(0,r.l):r;return r.l==null&&(r.l=r.length),n}function oo(e,t,r){var n={};n.sz=e.read_shift(2)/20;var a=Jf(e);a.fItalic&&(n.italic=1),a.fCondense&&(n.condense=1),a.fExtend&&(n.extend=1),a.fShadow&&(n.shadow=1),a.fOutline&&(n.outline=1),a.fStrikeout&&(n.strike=1);var i=e.read_shift(2);switch(i===700&&(n.bold=1),e.read_shift(2)){case 1:n.vertAlign="superscript";break;case 2:n.vertAlign="subscript";break}var s=e.read_shift(1);s!=0&&(n.underline=s);var f=e.read_shift(1);f>0&&(n.family=f);var o=e.read_shift(1);switch(o>0&&(n.charset=o),e.l++,n.color=Kf(e),e.read_shift(1)){case 1:n.scheme="major";break;case 2:n.scheme="minor";break}return n.name=nr(e),n}function co(e,t){t||(t=H(25+4*32)),t.write_shift(2,e.sz*20),qf(e,t),t.write_shift(2,e.bold?700:400);var r=0;e.vertAlign=="superscript"?r=1:e.vertAlign=="subscript"&&(r=2),t.write_shift(2,r),t.write_shift(1,e.underline||0),t.write_shift(1,e.family||0),t.write_shift(1,e.charset||0),t.write_shift(1,0),Nn(e.color,t);var n=0;return n=2,t.write_shift(1,n),Ve(e.name,t),t.length>t.l?t.slice(0,t.l):t}var ho=["none","solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"],la,uo=Br;function Ci(e,t){t||(t=H(4*3+8*7+16*1)),la||(la=Ta(ho));var r=la[e.patternType];r==null&&(r=40),t.write_shift(4,r);var n=0;if(r!=40)for(Nn({auto:1},t),Nn({auto:1},t);n<12;++n)t.write_shift(4,0);else{for(;n<4;++n)t.write_shift(4,0);for(;n<12;++n)t.write_shift(4,0)}return t.length>t.l?t.slice(0,t.l):t}function xo(e,t){var r=e.l+t,n=e.read_shift(2),a=e.read_shift(2);return e.l=r,{ixfe:n,numFmtId:a}}function U0(e,t,r){r||(r=H(16)),r.write_shift(2,t||0),r.write_shift(2,e.numFmtId||0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(1,0),r.write_shift(1,0);var n=0;return r.write_shift(1,n),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r}function Vt(e,t){return t||(t=H(10)),t.write_shift(1,0),t.write_shift(1,0),t.write_shift(4,0),t.write_shift(4,0),t}var mo=Br;function po(e,t){return t||(t=H(51)),t.write_shift(1,0),Vt(null,t),Vt(null,t),Vt(null,t),Vt(null,t),Vt(null,t),t.length>t.l?t.slice(0,t.l):t}function go(e,t){return t||(t=H(12+4*10)),t.write_shift(4,e.xfId),t.write_shift(2,1),t.write_shift(1,0),t.write_shift(1,0),Dn(e.name||"",t),t.length>t.l?t.slice(0,t.l):t}function vo(e,t,r){var n=H(2052);return n.write_shift(4,e),Dn(t,n),Dn(r,n),n.length>n.l?n.slice(0,n.l):n}function _o(e,t){if(t){var r=0;[[5,8],[23,26],[41,44],[50,392]].forEach(function(n){for(var a=n[0];a<=n[1];++a)t[a]!=null&&++r}),r!=0&&(Y(e,615,Rr(r)),[[5,8],[23,26],[41,44],[50,392]].forEach(function(n){for(var a=n[0];a<=n[1];++a)t[a]!=null&&Y(e,44,lo(a,t[a]))}),Y(e,616))}}function To(e){var t=1;Y(e,611,Rr(t)),Y(e,43,co({sz:12,color:{theme:1},name:"Calibri",family:2})),Y(e,612)}function Eo(e){var t=2;Y(e,603,Rr(t)),Y(e,45,Ci({patternType:"none"})),Y(e,45,Ci({patternType:"gray125"})),Y(e,604)}function wo(e){var t=1;Y(e,613,Rr(t)),Y(e,46,po()),Y(e,614)}function So(e){var t=1;Y(e,626,Rr(t)),Y(e,47,U0({numFmtId:0},65535)),Y(e,627)}function yo(e,t){Y(e,617,Rr(t.length)),t.forEach(function(r){Y(e,47,U0(r,0))}),Y(e,618)}function Ao(e){var t=1;Y(e,619,Rr(t)),Y(e,48,go({xfId:0,name:"Normal"})),Y(e,620)}function Fo(e){var t=0;Y(e,505,Rr(t)),Y(e,506)}function Co(e){var t=0;Y(e,508,vo(t,"TableStyleMedium9","PivotStyleMedium4")),Y(e,509)}function ko(e,t){var r=or();return Y(r,278),_o(r,e.SSF),To(r),Eo(r),wo(r),So(r),yo(r,t.cellXfs),Ao(r),Fo(r),Co(r),Y(r,279),r.end()}function W0(e,t){if(t&&t.themeXLSX)return t.themeXLSX;if(e&&typeof e.raw=="string")return e.raw;var r=[Ue];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}function Oo(e,t){return{flags:e.read_shift(4),version:e.read_shift(4),name:nr(e)}}function Io(e){var t=H(12+2*e.name.length);return t.write_shift(4,e.flags),t.write_shift(4,e.version),Ve(e.name,t),t.slice(0,t.l)}function Do(e){for(var t=[],r=e.read_shift(4);r-- >0;)t.push([e.read_shift(4),e.read_shift(4)]);return t}function No(e){var t=H(4+8*e.length);t.write_shift(4,e.length);for(var r=0;r<e.length;++r)t.write_shift(4,e[r][0]),t.write_shift(4,e[r][1]);return t}function Ro(e,t){var r=H(8+2*t.length);return r.write_shift(4,e),Ve(t,r),r.slice(0,r.l)}function Po(e){return e.l+=4,e.read_shift(4)!=0}function Lo(e,t){var r=H(8);return r.write_shift(4,e),r.write_shift(4,1),r}function Mo(){var e=or();return Y(e,332),Y(e,334,Rr(1)),Y(e,335,Io({name:"XLDAPR",version:12e4,flags:3496657072})),Y(e,336),Y(e,339,Ro(1,"XLDAPR")),Y(e,52),Y(e,35,Rr(514)),Y(e,4096,Rr(0)),Y(e,4097,Sr(1)),Y(e,36),Y(e,53),Y(e,340),Y(e,337,Lo(1)),Y(e,51,No([[1,0]])),Y(e,338),Y(e,333),e.end()}function j0(){var e=[Ue];return e.push(`<metadata xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:xlrd="http://schemas.microsoft.com/office/spreadsheetml/2017/richdata" xmlns:xda="http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray">
  <metadataTypes count="1">
    <metadataType name="XLDAPR" minSupportedVersion="120000" copy="1" pasteAll="1" pasteValues="1" merge="1" splitFirst="1" rowColShift="1" clearFormats="1" clearComments="1" assign="1" coerce="1" cellMeta="1"/>
  </metadataTypes>
  <futureMetadata name="XLDAPR" count="1">
    <bk>
      <extLst>
        <ext uri="{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}">
          <xda:dynamicArrayProperties fDynamic="1" fCollapsed="0"/>
        </ext>
      </extLst>
    </bk>
  </futureMetadata>
  <cellMetadata count="1">
    <bk>
      <rc t="1" v="0"/>
    </bk>
  </cellMetadata>
</metadata>`),e.join("")}function Bo(e){var t={};t.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),t.r=Ae(r);var n=e.read_shift(1);return n&2&&(t.l="1"),n&8&&(t.a="1"),t}var It=1024;function H0(e,t){for(var r=[21600,21600],n=["m0,0l0",r[1],r[0],r[1],r[0],"0xe"].join(","),a=[ee("xml",null,{"xmlns:v":mr.v,"xmlns:o":mr.o,"xmlns:x":mr.x,"xmlns:mv":mr.mv}).replace(/\/>/,">"),ee("o:shapelayout",ee("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"}),ee("v:shapetype",[ee("v:stroke",null,{joinstyle:"miter"}),ee("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202","o:spt":202,coordsize:r.join(","),path:n})];It<e*1e3;)It+=1e3;return t.forEach(function(i){var s=Ge(i[0]),f={color2:"#BEFF82",type:"gradient"};f.type=="gradient"&&(f.angle="-180");var o=f.type=="gradient"?ee("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null,l=ee("v:fill",o,f),c={on:"t",obscured:"t"};++It,a=a.concat(["<v:shape"+tn({id:"_x0000_s"+It,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(i[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",l,ee("v:shadow",null,c),ee("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",Je("x:Anchor",[s.c+1,0,s.r+1,0,s.c+3,20,s.r+5,20].join(",")),Je("x:AutoFill","False"),Je("x:Row",String(s.r)),Je("x:Column",String(s.c)),i[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"])}),a.push("</xml>"),a.join("")}function G0(e){var t=[Ue,ee("comments",null,{xmlns:Mt[0]})],r=[];return t.push("<authors>"),e.forEach(function(n){n[1].forEach(function(a){var i=ye(a.a);r.indexOf(i)==-1&&(r.push(i),t.push("<author>"+i+"</author>")),a.T&&a.ID&&r.indexOf("tc="+a.ID)==-1&&(r.push("tc="+a.ID),t.push("<author>tc="+a.ID+"</author>"))})}),r.length==0&&(r.push("SheetJ5"),t.push("<author>SheetJ5</author>")),t.push("</authors>"),t.push("<commentList>"),e.forEach(function(n){var a=0,i=[];if(n[1][0]&&n[1][0].T&&n[1][0].ID?a=r.indexOf("tc="+n[1][0].ID):n[1].forEach(function(o){o.a&&(a=r.indexOf(ye(o.a))),i.push(o.t||"")}),t.push('<comment ref="'+n[0]+'" authorId="'+a+'"><text>'),i.length<=1)t.push(Je("t",ye(i[0]||"")));else{for(var s=`Comment:
    `+i[0]+`
`,f=1;f<i.length;++f)s+=`Reply:
    `+i[f]+`
`;t.push(Je("t",ye(s)))}t.push("</text></comment>")}),t.push("</commentList>"),t.length>2&&(t[t.length]="</comments>",t[1]=t[1].replace("/>",">")),t.join("")}function bo(e,t,r){var n=[Ue,ee("ThreadedComments",null,{xmlns:He.TCMNT}).replace(/[\/]>/,">")];return e.forEach(function(a){var i="";(a[1]||[]).forEach(function(s,f){if(!s.T){delete s.ID;return}s.a&&t.indexOf(s.a)==-1&&t.push(s.a);var o={ref:a[0],id:"{54EE7951-7262-4200-6969-"+("000000000000"+r.tcid++).slice(-12)+"}"};f==0?i=o.id:o.parentId=i,s.ID=o.id,s.a&&(o.personId="{54EE7950-7262-4200-6969-"+("000000000000"+t.indexOf(s.a)).slice(-12)+"}"),n.push(ee("threadedComment",Je("text",s.t||""),o))})}),n.push("</ThreadedComments>"),n.join("")}function Uo(e){var t=[Ue,ee("personList",null,{xmlns:He.TCMNT,"xmlns:x":Mt[0]}).replace(/[\/]>/,">")];return e.forEach(function(r,n){t.push(ee("person",null,{displayName:r,id:"{54EE7950-7262-4200-6969-"+("000000000000"+n).slice(-12)+"}",userId:r,providerId:"None"}))}),t.push("</personList>"),t.join("")}function Wo(e){var t={};t.iauthor=e.read_shift(4);var r=_t(e);return t.rfx=r.s,t.ref=Ae(r.s),e.l+=16,t}function jo(e,t){return t==null&&(t=H(36)),t.write_shift(4,e[1].iauthor),bt(e[0],t),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t}var Ho=nr;function Go(e){return Ve(e.slice(0,54))}function Vo(e){var t=or(),r=[];return Y(t,628),Y(t,630),e.forEach(function(n){n[1].forEach(function(a){r.indexOf(a.a)>-1||(r.push(a.a.slice(0,54)),Y(t,632,Go(a.a)))})}),Y(t,631),Y(t,633),e.forEach(function(n){n[1].forEach(function(a){a.iauthor=r.indexOf(a.a);var i={s:Ge(n[0]),e:Ge(n[0])};Y(t,635,jo([i,a])),a.t&&a.t.length>0&&Y(t,637,zf(a)),Y(t,636),delete a.iauthor})}),Y(t,634),Y(t,629),t.end()}function zo(e,t){t.FullPaths.forEach(function(r,n){if(n!=0){var a=r.replace(/[^\/]*[\/]/,"/_VBA_PROJECT_CUR/");a.slice(-1)!=="/"&&Ce.utils.cfb_add(e,a,t.FileIndex[n].content)}})}var V0=["xlsb","xlsm","xlam","biff8","xla"],Yo=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,t={r:0,c:0};function r(n,a,i,s){var f=!1,o=!1;i.length==0?o=!0:i.charAt(0)=="["&&(o=!0,i=i.slice(1,-1)),s.length==0?f=!0:s.charAt(0)=="["&&(f=!0,s=s.slice(1,-1));var l=i.length>0?parseInt(i,10)|0:0,c=s.length>0?parseInt(s,10)|0:0;return f?c+=t.c:--c,o?l+=t.r:--l,a+(f?"":"$")+tr(c)+(o?"":"$")+qe(l)}return function(a,i){return t=i,a.replace(e,r)}}(),Ra=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,Pa=function(){return function(t,r){return t.replace(Ra,function(n,a,i,s,f,o){var l=Ca(s)-(i?0:r.c),c=Fa(o)-(f?0:r.r),m=c==0?"":f?c+1:"["+c+"]",d=l==0?"":i?l+1:"["+l+"]";return a+"R"+m+"C"+d})}}();function $o(e,t){return e.replace(Ra,function(r,n,a,i,s,f){return n+(a=="$"?a+i:tr(Ca(i)+t.c))+(s=="$"?s+f:qe(Fa(f)+t.r))})}function Xo(e){return e.length!=1}function be(e){e.l+=1}function nt(e,t){var r=e.read_shift(2);return[r&16383,r>>14&1,r>>15&1]}function z0(e,t,r){var n=2;if(r){if(r.biff>=2&&r.biff<=5)return Y0(e);r.biff==12&&(n=4)}var a=e.read_shift(n),i=e.read_shift(n),s=nt(e),f=nt(e);return{s:{r:a,c:s[0],cRel:s[1],rRel:s[2]},e:{r:i,c:f[0],cRel:f[1],rRel:f[2]}}}function Y0(e){var t=nt(e),r=nt(e),n=e.read_shift(1),a=e.read_shift(1);return{s:{r:t[0],c:n,cRel:t[1],rRel:t[2]},e:{r:r[0],c:a,cRel:r[1],rRel:r[2]}}}function Ko(e,t,r){if(r.biff<8)return Y0(e);var n=e.read_shift(r.biff==12?4:2),a=e.read_shift(r.biff==12?4:2),i=nt(e),s=nt(e);return{s:{r:n,c:i[0],cRel:i[1],rRel:i[2]},e:{r:a,c:s[0],cRel:s[1],rRel:s[2]}}}function $0(e,t,r){if(r&&r.biff>=2&&r.biff<=5)return Jo(e);var n=e.read_shift(r&&r.biff==12?4:2),a=nt(e);return{r:n,c:a[0],cRel:a[1],rRel:a[2]}}function Jo(e){var t=nt(e),r=e.read_shift(1);return{r:t[0],c:r,cRel:t[1],rRel:t[2]}}function qo(e){var t=e.read_shift(2),r=e.read_shift(2);return{r:t,c:r&255,fQuoted:!!(r&16384),cRel:r>>15,rRel:r>>15}}function Zo(e,t,r){var n=r&&r.biff?r.biff:8;if(n>=2&&n<=5)return Qo(e);var a=e.read_shift(n>=12?4:2),i=e.read_shift(2),s=(i&16384)>>14,f=(i&32768)>>15;if(i&=16383,f==1)for(;a>524287;)a-=1048576;if(s==1)for(;i>8191;)i=i-16384;return{r:a,c:i,cRel:s,rRel:f}}function Qo(e){var t=e.read_shift(2),r=e.read_shift(1),n=(t&32768)>>15,a=(t&16384)>>14;return t&=16383,n==1&&t>=8192&&(t=t-16384),a==1&&r>=128&&(r=r-256),{r:t,c:r,cRel:a,rRel:n}}function ec(e,t,r){var n=(e[e.l++]&96)>>5,a=z0(e,r.biff>=2&&r.biff<=5?6:8,r);return[n,a]}function rc(e,t,r){var n=(e[e.l++]&96)>>5,a=e.read_shift(2,"i"),i=8;if(r)switch(r.biff){case 5:e.l+=12,i=6;break;case 12:i=12;break}var s=z0(e,i,r);return[n,a,s]}function tc(e,t,r){var n=(e[e.l++]&96)>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[n]}function nc(e,t,r){var n=(e[e.l++]&96)>>5,a=e.read_shift(2),i=8;if(r)switch(r.biff){case 5:e.l+=12,i=6;break;case 12:i=12;break}return e.l+=i,[n,a]}function ac(e,t,r){var n=(e[e.l++]&96)>>5,a=Ko(e,t-1,r);return[n,a]}function ic(e,t,r){var n=(e[e.l++]&96)>>5;return e.l+=r.biff==2?6:r.biff==12?14:7,[n]}function ki(e){var t=e[e.l+1]&1,r=1;return e.l+=4,[t,r]}function sc(e,t,r){e.l+=2;for(var n=e.read_shift(r&&r.biff==2?1:2),a=[],i=0;i<=n;++i)a.push(e.read_shift(r&&r.biff==2?1:2));return a}function fc(e,t,r){var n=e[e.l+1]&255?1:0;return e.l+=2,[n,e.read_shift(r&&r.biff==2?1:2)]}function lc(e,t,r){var n=e[e.l+1]&255?1:0;return e.l+=2,[n,e.read_shift(r&&r.biff==2?1:2)]}function oc(e){var t=e[e.l+1]&255?1:0;return e.l+=2,[t,e.read_shift(2)]}function cc(e,t,r){var n=e[e.l+1]&255?1:0;return e.l+=r&&r.biff==2?3:4,[n]}function X0(e){var t=e.read_shift(1),r=e.read_shift(1);return[t,r]}function hc(e){return e.read_shift(2),X0(e)}function uc(e){return e.read_shift(2),X0(e)}function xc(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var a=$0(e,0,r);return[n,a]}function dc(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var a=Zo(e,0,r);return[n,a]}function mc(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var a=e.read_shift(2);r&&r.biff==5&&(e.l+=12);var i=$0(e,0,r);return[n,a,i]}function pc(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var a=e.read_shift(r&&r.biff<=3?1:2);return[ph[a],q0[a],n]}function gc(e,t,r){var n=e[e.l++],a=e.read_shift(1),i=r&&r.biff<=3?[n==88?-1:0,e.read_shift(1)]:vc(e);return[a,(i[0]===0?q0:mh)[i[1]]]}function vc(e){return[e[e.l+1]>>7,e.read_shift(2)&32767]}function _c(e,t,r){e.l+=r&&r.biff==2?3:4}function Tc(e,t,r){if(e.l++,r&&r.biff==12)return[e.read_shift(4,"i"),0];var n=e.read_shift(2),a=e.read_shift(r&&r.biff==2?1:2);return[n,a]}function Ec(e){return e.l++,on[e.read_shift(1)]}function wc(e){return e.l++,e.read_shift(2)}function Sc(e){return e.l++,e.read_shift(1)!==0}function yc(e){return e.l++,Ut(e)}function Ac(e,t,r){return e.l++,I0(e,t-1,r)}function Fc(e,t){var r=[e.read_shift(1)];if(t==12)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2;break}switch(r[0]){case 4:r[1]=dl(e,1)?"TRUE":"FALSE",t!=12&&(e.l+=7);break;case 37:case 16:r[1]=on[e[e.l]],e.l+=t==12?4:8;break;case 0:e.l+=8;break;case 1:r[1]=Ut(e);break;case 2:r[1]=vl(e,0,{biff:t>0&&t<8?2:t});break;default:throw new Error("Bad SerAr: "+r[0])}return r}function Cc(e,t,r){for(var n=e.read_shift(r.biff==12?4:2),a=[],i=0;i!=n;++i)a.push((r.biff==12?_t:El)(e));return a}function kc(e,t,r){var n=0,a=0;r.biff==12?(n=e.read_shift(4),a=e.read_shift(4)):(a=1+e.read_shift(1),n=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--n,--a==0&&(a=256));for(var i=0,s=[];i!=n&&(s[i]=[]);++i)for(var f=0;f!=a;++f)s[i][f]=Fc(e,r.biff);return s}function Oc(e,t,r){var n=e.read_shift(1)>>>5&3,a=!r||r.biff>=8?4:2,i=e.read_shift(a);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12;break}return[n,0,i]}function Ic(e,t,r){if(r.biff==5)return Dc(e);var n=e.read_shift(1)>>>5&3,a=e.read_shift(2),i=e.read_shift(4);return[n,a,i]}function Dc(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2,"i");e.l+=8;var n=e.read_shift(2);return e.l+=12,[t,r,n]}function Nc(e,t,r){var n=e.read_shift(1)>>>5&3;e.l+=r&&r.biff==2?3:4;var a=e.read_shift(r&&r.biff==2?1:2);return[n,a]}function Rc(e,t,r){var n=e.read_shift(1)>>>5&3,a=e.read_shift(r&&r.biff==2?1:2);return[n,a]}function Pc(e,t,r){var n=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,r.biff==12&&(e.l+=2),[n]}function Lc(e,t,r){var n=(e[e.l++]&96)>>5,a=e.read_shift(2),i=4;if(r)switch(r.biff){case 5:i=15;break;case 12:i=6;break}return e.l+=i,[n,a]}var Mc=Br,Bc=Br,bc=Br;function cn(e,t,r){return e.l+=2,[qo(e)]}function La(e){return e.l+=6,[]}var Uc=cn,Wc=La,jc=La,Hc=cn;function K0(e){return e.l+=2,[k0(e),e.read_shift(2)&1]}var Gc=cn,Vc=K0,zc=La,Yc=cn,$c=cn,Xc=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"];function Kc(e){e.l+=2;var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(4),a=e.read_shift(2),i=e.read_shift(2),s=Xc[r>>2&31];return{ixti:t,coltype:r&3,rt:s,idx:n,c:a,C:i}}function Jc(e){return e.l+=2,[e.read_shift(4)]}function qc(e,t,r){return e.l+=5,e.l+=2,e.l+=r.biff==2?1:4,["PTGSHEET"]}function Zc(e,t,r){return e.l+=r.biff==2?4:5,["PTGENDSHEET"]}function Qc(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function eh(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function rh(e){return e.l+=4,[0,0]}var Oi={1:{n:"PtgExp",f:Tc},2:{n:"PtgTbl",f:bc},3:{n:"PtgAdd",f:be},4:{n:"PtgSub",f:be},5:{n:"PtgMul",f:be},6:{n:"PtgDiv",f:be},7:{n:"PtgPower",f:be},8:{n:"PtgConcat",f:be},9:{n:"PtgLt",f:be},10:{n:"PtgLe",f:be},11:{n:"PtgEq",f:be},12:{n:"PtgGe",f:be},13:{n:"PtgGt",f:be},14:{n:"PtgNe",f:be},15:{n:"PtgIsect",f:be},16:{n:"PtgUnion",f:be},17:{n:"PtgRange",f:be},18:{n:"PtgUplus",f:be},19:{n:"PtgUminus",f:be},20:{n:"PtgPercent",f:be},21:{n:"PtgParen",f:be},22:{n:"PtgMissArg",f:be},23:{n:"PtgStr",f:Ac},26:{n:"PtgSheet",f:qc},27:{n:"PtgEndSheet",f:Zc},28:{n:"PtgErr",f:Ec},29:{n:"PtgBool",f:Sc},30:{n:"PtgInt",f:wc},31:{n:"PtgNum",f:yc},32:{n:"PtgArray",f:ic},33:{n:"PtgFunc",f:pc},34:{n:"PtgFuncVar",f:gc},35:{n:"PtgName",f:Oc},36:{n:"PtgRef",f:xc},37:{n:"PtgArea",f:ec},38:{n:"PtgMemArea",f:Nc},39:{n:"PtgMemErr",f:Mc},40:{n:"PtgMemNoMem",f:Bc},41:{n:"PtgMemFunc",f:Rc},42:{n:"PtgRefErr",f:Pc},43:{n:"PtgAreaErr",f:tc},44:{n:"PtgRefN",f:dc},45:{n:"PtgAreaN",f:ac},46:{n:"PtgMemAreaN",f:Qc},47:{n:"PtgMemNoMemN",f:eh},57:{n:"PtgNameX",f:Ic},58:{n:"PtgRef3d",f:mc},59:{n:"PtgArea3d",f:rc},60:{n:"PtgRefErr3d",f:Lc},61:{n:"PtgAreaErr3d",f:nc},255:{}},th={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},nh={1:{n:"PtgElfLel",f:K0},2:{n:"PtgElfRw",f:Yc},3:{n:"PtgElfCol",f:Uc},6:{n:"PtgElfRwV",f:$c},7:{n:"PtgElfColV",f:Hc},10:{n:"PtgElfRadical",f:Gc},11:{n:"PtgElfRadicalS",f:zc},13:{n:"PtgElfColS",f:Wc},15:{n:"PtgElfColSV",f:jc},16:{n:"PtgElfRadicalLel",f:Vc},25:{n:"PtgList",f:Kc},29:{n:"PtgSxName",f:Jc},255:{}},ah={0:{n:"PtgAttrNoop",f:rh},1:{n:"PtgAttrSemi",f:cc},2:{n:"PtgAttrIf",f:lc},4:{n:"PtgAttrChoose",f:sc},8:{n:"PtgAttrGoto",f:fc},16:{n:"PtgAttrSum",f:_c},32:{n:"PtgAttrBaxcel",f:ki},33:{n:"PtgAttrBaxcel",f:ki},64:{n:"PtgAttrSpace",f:hc},65:{n:"PtgAttrSpaceSemi",f:uc},128:{n:"PtgAttrIfError",f:oc},255:{}};function ih(e,t,r,n){if(n.biff<8)return Br(e,t);for(var a=e.l+t,i=[],s=0;s!==r.length;++s)switch(r[s][0]){case"PtgArray":r[s][1]=kc(e,0,n),i.push(r[s][1]);break;case"PtgMemArea":r[s][2]=Cc(e,r[s][1],n),i.push(r[s][2]);break;case"PtgExp":n&&n.biff==12&&(r[s][1][1]=e.read_shift(4),i.push(r[s][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[s][0]}return t=a-e.l,t!==0&&i.push(Br(e,t)),i}function sh(e,t,r){for(var n=e.l+t,a,i,s=[];n!=e.l;)t=n-e.l,i=e[e.l],a=Oi[i]||Oi[th[i]],(i===24||i===25)&&(a=(i===24?nh:ah)[e[e.l+1]]),!a||!a.f?Br(e,t):s.push([a.n,a.f(e,t,r)]);return s}function fh(e){for(var t=[],r=0;r<e.length;++r){for(var n=e[r],a=[],i=0;i<n.length;++i){var s=n[i];if(s)switch(s[0]){case 2:a.push('"'+s[1].replace(/"/g,'""')+'"');break;default:a.push(s[1])}else a.push("")}t.push(a.join(","))}return t.join(";")}var lh={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function oh(e,t){if(!e&&!(t&&t.biff<=5&&t.biff>=2))throw new Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}function J0(e,t,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[t]))return e.SheetNames[t];if(!e.XTI)return"SH33TJSERR6";var n=e.XTI[t];if(r.biff<8)return t>1e4&&(t-=65536),t<0&&(t=-t),t==0?"":e.XTI[t-1];if(!n)return"SH33TJSERR1";var a="";if(r.biff>8)switch(e[n[0]][0]){case 357:return a=n[1]==-1?"#REF":e.SheetNames[n[1]],n[1]==n[2]?a:a+":"+e.SheetNames[n[2]];case 358:return r.SID!=null?e.SheetNames[r.SID]:"SH33TJSSAME"+e[n[0]][0];case 355:default:return"SH33TJSSRC"+e[n[0]][0]}switch(e[n[0]][0][0]){case 1025:return a=n[1]==-1?"#REF":e.SheetNames[n[1]]||"SH33TJSERR3",n[1]==n[2]?a:a+":"+e.SheetNames[n[2]];case 14849:return e[n[0]].slice(1).map(function(i){return i.Name}).join(";;");default:return e[n[0]][0][3]?(a=n[1]==-1?"#REF":e[n[0]][0][3][n[1]]||"SH33TJSERR4",n[1]==n[2]?a:a+":"+e[n[0]][0][3][n[2]]):"SH33TJSERR2"}}function Ii(e,t,r){var n=J0(e,t,r);return n=="#REF"?n:oh(n,r)}function Lt(e,t,r,n,a){var i=a&&a.biff||8,s={s:{c:0,r:0}},f=[],o,l,c,m=0,d=0,x,v="";if(!e[0]||!e[0][0])return"";for(var u=-1,_="",R=0,P=e[0].length;R<P;++R){var O=e[0][R];switch(O[0]){case"PtgUminus":f.push("-"+f.pop());break;case"PtgUplus":f.push("+"+f.pop());break;case"PtgPercent":f.push(f.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(o=f.pop(),l=f.pop(),u>=0){switch(e[0][u][1][0]){case 0:_=Pe(" ",e[0][u][1][1]);break;case 1:_=Pe("\r",e[0][u][1][1]);break;default:if(_="",a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][u][1][0])}l=l+_,u=-1}f.push(l+lh[O[0]]+o);break;case"PtgIsect":o=f.pop(),l=f.pop(),f.push(l+" "+o);break;case"PtgUnion":o=f.pop(),l=f.pop(),f.push(l+","+o);break;case"PtgRange":o=f.pop(),l=f.pop(),f.push(l+":"+o);break;case"PtgAttrChoose":break;case"PtgAttrGoto":break;case"PtgAttrIf":break;case"PtgAttrIfError":break;case"PtgRef":c=Kt(O[1][1],s,a),f.push(Jt(c,i));break;case"PtgRefN":c=r?Kt(O[1][1],r,a):O[1][1],f.push(Jt(c,i));break;case"PtgRef3d":m=O[1][1],c=Kt(O[1][2],s,a),v=Ii(n,m,a),f.push(v+"!"+Jt(c,i));break;case"PtgFunc":case"PtgFuncVar":var j=O[1][0],Q=O[1][1];j||(j=0),j&=127;var ne=j==0?[]:f.slice(-j);f.length-=j,Q==="User"&&(Q=ne.shift()),f.push(Q+"("+ne.join(",")+")");break;case"PtgBool":f.push(O[1]?"TRUE":"FALSE");break;case"PtgInt":f.push(O[1]);break;case"PtgNum":f.push(String(O[1]));break;case"PtgStr":f.push('"'+O[1].replace(/"/g,'""')+'"');break;case"PtgErr":f.push(O[1]);break;case"PtgAreaN":x=di(O[1][1],r?{s:r}:s,a),f.push(sa(x,a));break;case"PtgArea":x=di(O[1][1],s,a),f.push(sa(x,a));break;case"PtgArea3d":m=O[1][1],x=O[1][2],v=Ii(n,m,a),f.push(v+"!"+sa(x,a));break;case"PtgAttrSum":f.push("SUM("+f.pop()+")");break;case"PtgAttrBaxcel":case"PtgAttrSemi":break;case"PtgName":d=O[1][2];var k=(n.names||[])[d-1]||(n[0]||[])[d],z=k?k.Name:"SH33TJSNAME"+String(d);z&&z.slice(0,6)=="_xlfn."&&!a.xlfn&&(z=z.slice(6)),f.push(z);break;case"PtgNameX":var W=O[1][1];d=O[1][2];var X;if(a.biff<=5)W<0&&(W=-W),n[W]&&(X=n[W][d]);else{var J="";if(((n[W]||[])[0]||[])[0]==14849||(((n[W]||[])[0]||[])[0]==1025?n[W][d]&&n[W][d].itab>0&&(J=n.SheetNames[n[W][d].itab-1]+"!"):J=n.SheetNames[d-1]+"!"),n[W]&&n[W][d])J+=n[W][d].Name;else if(n[0]&&n[0][d])J+=n[0][d].Name;else{var Z=(J0(n,W,a)||"").split(";;");Z[d-1]?J=Z[d-1]:J+="SH33TJSERRX"}f.push(J);break}X||(X={Name:"SH33TJSERRY"}),f.push(X.Name);break;case"PtgParen":var ae="(",pe=")";if(u>=0){switch(_="",e[0][u][1][0]){case 2:ae=Pe(" ",e[0][u][1][1])+ae;break;case 3:ae=Pe("\r",e[0][u][1][1])+ae;break;case 4:pe=Pe(" ",e[0][u][1][1])+pe;break;case 5:pe=Pe("\r",e[0][u][1][1])+pe;break;default:if(a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][u][1][0])}u=-1}f.push(ae+f.pop()+pe);break;case"PtgRefErr":f.push("#REF!");break;case"PtgRefErr3d":f.push("#REF!");break;case"PtgExp":c={c:O[1][1],r:O[1][0]};var ue={c:r.c,r:r.r};if(n.sharedf[Ae(c)]){var Me=n.sharedf[Ae(c)];f.push(Lt(Me,s,ue,n,a))}else{var Ne=!1;for(o=0;o!=n.arrayf.length;++o)if(l=n.arrayf[o],!(c.c<l[0].s.c||c.c>l[0].e.c)&&!(c.r<l[0].s.r||c.r>l[0].e.r)){f.push(Lt(l[1],s,ue,n,a)),Ne=!0;break}Ne||f.push(O[1])}break;case"PtgArray":f.push("{"+fh(O[1])+"}");break;case"PtgMemArea":break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":u=R;break;case"PtgTbl":break;case"PtgMemErr":break;case"PtgMissArg":f.push("");break;case"PtgAreaErr":f.push("#REF!");break;case"PtgAreaErr3d":f.push("#REF!");break;case"PtgList":f.push("Table"+O[1].idx+"[#"+O[1].rt+"]");break;case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":break;case"PtgMemFunc":break;case"PtgMemNoMem":break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw new Error("Unsupported ELFs");case"PtgSxName":throw new Error("Unrecognized Formula Token: "+String(O));default:throw new Error("Unrecognized Formula Token: "+String(O))}var ur=["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"];if(a.biff!=3&&u>=0&&ur.indexOf(e[0][R][0])==-1){O=e[0][u];var Be=!0;switch(O[1][0]){case 4:Be=!1;case 0:_=Pe(" ",O[1][1]);break;case 5:Be=!1;case 1:_=Pe("\r",O[1][1]);break;default:if(_="",a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+O[1][0])}f.push((Be?_:"")+f.pop()+(Be?"":_)),u=-1}}if(f.length>1&&a.WTF)throw new Error("bad formula stack");return f[0]}function ch(e){if(e==null){var t=H(8);return t.write_shift(1,3),t.write_shift(1,0),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(2,65535),t}else if(typeof e=="number")return xt(e);return xt(0)}function hh(e,t,r,n,a){var i=dt(t,r,a),s=ch(e.v),f=H(6),o=33;f.write_shift(2,o),f.write_shift(4,0);for(var l=H(e.bf.length),c=0;c<e.bf.length;++c)l[c]=e.bf[c];var m=Ke([i,s,f,l]);return m}function Vn(e,t,r){var n=e.read_shift(4),a=sh(e,n,r),i=e.read_shift(4),s=i>0?ih(e,i,a,r):null;return[a,s]}var uh=Vn,zn=Vn,xh=Vn,dh=Vn,mh={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},q0={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},ph={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function gh(e){var t="of:="+e.replace(Ra,"$1[.$2$3$4$5]").replace(/\]:\[/g,":");return t.replace(/;/g,"|").replace(/,/g,";")}function vh(e){return e.replace(/\./,"!")}var qt=typeof Map!="undefined";function Ma(e,t,r){var n=0,a=e.length;if(r){if(qt?r.has(t):Object.prototype.hasOwnProperty.call(r,t)){for(var i=qt?r.get(t):r[t];n<i.length;++n)if(e[i[n]].t===t)return e.Count++,i[n]}}else for(;n<a;++n)if(e[n].t===t)return e.Count++,n;return e[a]={t},e.Count++,e.Unique++,r&&(qt?(r.has(t)||r.set(t,[]),r.get(t).push(a)):(Object.prototype.hasOwnProperty.call(r,t)||(r[t]=[]),r[t].push(a))),a}function Yn(e,t){var r={min:e+1,max:e+1},n=-1;return t.MDW&&(Vr=t.MDW),t.width!=null?r.customWidth=1:t.wpx!=null?n=Ln(t.wpx):t.wch!=null&&(n=t.wch),n>-1?(r.width=ga(n),r.customWidth=1):t.width!=null&&(r.width=t.width),t.hidden&&(r.hidden=!0),t.level!=null&&(r.outlineLevel=r.level=t.level),r}function Z0(e,t){if(e){var r=[.7,.7,.75,.75,.3,.3];e.left==null&&(e.left=r[0]),e.right==null&&(e.right=r[1]),e.top==null&&(e.top=r[2]),e.bottom==null&&(e.bottom=r[3]),e.header==null&&(e.header=r[4]),e.footer==null&&(e.footer=r[5])}}function it(e,t,r){var n=r.revssf[t.z!=null?t.z:"General"],a=60,i=e.length;if(n==null&&r.ssf){for(;a<392;++a)if(r.ssf[a]==null){$i(t.z,a),r.ssf[a]=t.z,r.revssf[t.z]=n=a;break}}for(a=0;a!=i;++a)if(e[a].numFmtId===n)return a;return e[i]={numFmtId:n,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1},i}function _h(e,t,r){if(e&&e["!ref"]){var n=Ie(e["!ref"]);if(n.e.c<n.s.c||n.e.r<n.s.r)throw new Error("Bad range ("+r+"): "+e["!ref"])}}function Th(e){if(e.length===0)return"";for(var t='<mergeCells count="'+e.length+'">',r=0;r!=e.length;++r)t+='<mergeCell ref="'+je(e[r])+'"/>';return t+"</mergeCells>"}function Eh(e,t,r,n,a){var i=!1,s={},f=null;if(n.bookType!=="xlsx"&&t.vbaraw){var o=t.SheetNames[r];try{t.Workbook&&(o=t.Workbook.Sheets[r].CodeName||o)}catch(c){}i=!0,s.codeName=rn(ye(o))}if(e&&e["!outline"]){var l={summaryBelow:1,summaryRight:1};e["!outline"].above&&(l.summaryBelow=0),e["!outline"].left&&(l.summaryRight=0),f=(f||"")+ee("outlinePr",null,l)}!i&&!f||(a[a.length]=ee("sheetPr",f,s))}var wh=["objects","scenarios","selectLockedCells","selectUnlockedCells"],Sh=["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"];function yh(e){var t={sheet:1};return wh.forEach(function(r){e[r]!=null&&e[r]&&(t[r]="1")}),Sh.forEach(function(r){e[r]!=null&&!e[r]&&(t[r]="0")}),e.password&&(t.password=L0(e.password).toString(16).toUpperCase()),ee("sheetProtection",null,t)}function Ah(e){return Z0(e),ee("pageMargins",null,e)}function Fh(e,t){for(var r=["<cols>"],n,a=0;a!=t.length;++a)(n=t[a])&&(r[r.length]=ee("col",null,Yn(a,n)));return r[r.length]="</cols>",r.join("")}function Ch(e,t,r,n){var a=typeof e.ref=="string"?e.ref:je(e.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var i=r.Workbook.Names,s=gr(a);s.s.r==s.e.r&&(s.e.r=gr(t["!ref"]).e.r,a=je(s));for(var f=0;f<i.length;++f){var o=i[f];if(o.Name=="_xlnm._FilterDatabase"&&o.Sheet==n){o.Ref="'"+r.SheetNames[n]+"'!"+a;break}}return f==i.length&&i.push({Name:"_xlnm._FilterDatabase",Sheet:n,Ref:"'"+r.SheetNames[n]+"'!"+a}),ee("autoFilter",null,{ref:a})}function kh(e,t,r,n){var a={workbookViewId:"0"};return(((n||{}).Workbook||{}).Views||[])[0]&&(a.rightToLeft=n.Workbook.Views[0].RTL?"1":"0"),ee("sheetViews",ee("sheetView",null,a),{})}function Oh(e,t,r,n){if(e.c&&r["!comments"].push([t,e.c]),e.v===void 0&&typeof e.f!="string"||e.t==="z"&&!e.f)return"";var a="",i=e.t,s=e.v;if(e.t!=="z")switch(e.t){case"b":a=e.v?"1":"0";break;case"n":a=""+e.v;break;case"e":a=on[e.v];break;case"d":n&&n.cellDates?a=lr(e.v,-1).toISOString():(e=hr(e),e.t="n",a=""+(e.v=cr(lr(e.v)))),typeof e.z=="undefined"&&(e.z=Le[14]);break;default:a=e.v;break}var f=Je("v",ye(a)),o={r:t},l=it(n.cellXfs,e,n);switch(l!==0&&(o.s=l),e.t){case"n":break;case"d":o.t="d";break;case"b":o.t="b";break;case"e":o.t="e";break;case"z":break;default:if(e.v==null){delete e.t;break}if(e.v.length>32767)throw new Error("Text length must not exceed 32767 characters");if(n&&n.bookSST){f=Je("v",""+Ma(n.Strings,e.v,n.revStrings)),o.t="s";break}o.t="str";break}if(e.t!=i&&(e.t=i,e.v=s),typeof e.f=="string"&&e.f){var c=e.F&&e.F.slice(0,t.length)==t?{t:"array",ref:e.F}:null;f=ee("f",ye(e.f),c)+(e.v!=null?f:"")}return e.l&&r["!links"].push([t,e.l]),e.D&&(o.cm=1),ee("c",f,o)}function Ih(e,t,r,n){var a=[],i=[],s=Ie(e["!ref"]),f="",o,l="",c=[],m=0,d=0,x=e["!rows"],v=Array.isArray(e),u={r:l},_,R=-1;for(d=s.s.c;d<=s.e.c;++d)c[d]=tr(d);for(m=s.s.r;m<=s.e.r;++m){for(i=[],l=qe(m),d=s.s.c;d<=s.e.c;++d){o=c[d]+l;var P=v?(e[m]||[])[d]:e[o];P!==void 0&&(f=Oh(P,o,e,t))!=null&&i.push(f)}(i.length>0||x&&x[m])&&(u={r:l},x&&x[m]&&(_=x[m],_.hidden&&(u.hidden=1),R=-1,_.hpx?R=Mn(_.hpx):_.hpt&&(R=_.hpt),R>-1&&(u.ht=R,u.customHeight=1),_.level&&(u.outlineLevel=_.level)),a[a.length]=ee("row",i.join(""),u))}if(x)for(;m<x.length;++m)x&&x[m]&&(u={r:m+1},_=x[m],_.hidden&&(u.hidden=1),R=-1,_.hpx?R=Mn(_.hpx):_.hpt&&(R=_.hpt),R>-1&&(u.ht=R,u.customHeight=1),_.level&&(u.outlineLevel=_.level),a[a.length]=ee("row","",u));return a.join("")}function Q0(e,t,r,n){var a=[Ue,ee("worksheet",null,{xmlns:Mt[0],"xmlns:r":He.r})],i=r.SheetNames[e],s=0,f="",o=r.Sheets[i];o==null&&(o={});var l=o["!ref"]||"A1",c=Ie(l);if(c.e.c>16383||c.e.r>1048575){if(t.WTF)throw new Error("Range "+l+" exceeds format limit A1:XFD1048576");c.e.c=Math.min(c.e.c,16383),c.e.r=Math.min(c.e.c,1048575),l=je(c)}n||(n={}),o["!comments"]=[];var m=[];Eh(o,r,e,t,a),a[a.length]=ee("dimension",null,{ref:l}),a[a.length]=kh(o,t,e,r),t.sheetFormat&&(a[a.length]=ee("sheetFormatPr",null,{defaultRowHeight:t.sheetFormat.defaultRowHeight||"16",baseColWidth:t.sheetFormat.baseColWidth||"10",outlineLevelRow:t.sheetFormat.outlineLevelRow||"7"})),o["!cols"]!=null&&o["!cols"].length>0&&(a[a.length]=Fh(o,o["!cols"])),a[s=a.length]="<sheetData/>",o["!links"]=[],o["!ref"]!=null&&(f=Ih(o,t),f.length>0&&(a[a.length]=f)),a.length>s+1&&(a[a.length]="</sheetData>",a[s]=a[s].replace("/>",">")),o["!protect"]&&(a[a.length]=yh(o["!protect"])),o["!autofilter"]!=null&&(a[a.length]=Ch(o["!autofilter"],o,r,e)),o["!merges"]!=null&&o["!merges"].length>0&&(a[a.length]=Th(o["!merges"]));var d=-1,x,v=-1;return o["!links"].length>0&&(a[a.length]="<hyperlinks>",o["!links"].forEach(function(u){u[1].Target&&(x={ref:u[0]},u[1].Target.charAt(0)!="#"&&(v=Se(n,-1,ye(u[1].Target).replace(/#.*$/,""),_e.HLINK),x["r:id"]="rId"+v),(d=u[1].Target.indexOf("#"))>-1&&(x.location=ye(u[1].Target.slice(d+1))),u[1].Tooltip&&(x.tooltip=ye(u[1].Tooltip)),a[a.length]=ee("hyperlink",null,x))}),a[a.length]="</hyperlinks>"),delete o["!links"],o["!margins"]!=null&&(a[a.length]=Ah(o["!margins"])),(!t||t.ignoreEC||t.ignoreEC==null)&&(a[a.length]=Je("ignoredErrors",ee("ignoredError",null,{numberStoredAsText:1,sqref:l}))),m.length>0&&(v=Se(n,-1,"../drawings/drawing"+(e+1)+".xml",_e.DRAW),a[a.length]=ee("drawing",null,{"r:id":"rId"+v}),o["!drawing"]=m),o["!comments"].length>0&&(v=Se(n,-1,"../drawings/vmlDrawing"+(e+1)+".vml",_e.VML),a[a.length]=ee("legacyDrawing",null,{"r:id":"rId"+v}),o["!legacy"]=v),a.length>1&&(a[a.length]="</worksheet>",a[1]=a[1].replace("/>",">")),a.join("")}function Dh(e,t){var r={},n=e.l+t;r.r=e.read_shift(4),e.l+=4;var a=e.read_shift(2);e.l+=1;var i=e.read_shift(1);return e.l=n,i&7&&(r.level=i&7),i&16&&(r.hidden=!0),i&32&&(r.hpt=a/20),r}function Nh(e,t,r){var n=H(145),a=(r["!rows"]||[])[e]||{};n.write_shift(4,e),n.write_shift(4,0);var i=320;a.hpx?i=Mn(a.hpx)*20:a.hpt&&(i=a.hpt*20),n.write_shift(2,i),n.write_shift(1,0);var s=0;a.level&&(s|=a.level),a.hidden&&(s|=16),(a.hpx||a.hpt)&&(s|=32),n.write_shift(1,s),n.write_shift(1,0);var f=0,o=n.l;n.l+=4;for(var l={r:e,c:0},c=0;c<16;++c)if(!(t.s.c>c+1<<10||t.e.c<c<<10)){for(var m=-1,d=-1,x=c<<10;x<c+1<<10;++x){l.c=x;var v=Array.isArray(r)?(r[l.r]||[])[l.c]:r[Ae(l)];v&&(m<0&&(m=x),d=x)}m<0||(++f,n.write_shift(4,m),n.write_shift(4,d))}var u=n.l;return n.l=o,n.write_shift(4,f),n.l=u,n.length>n.l?n.slice(0,n.l):n}function Rh(e,t,r,n){var a=Nh(n,r,t);(a.length>17||(t["!rows"]||[])[n])&&Y(e,0,a)}var Ph=_t,Lh=bt;function Mh(){}function Bh(e,t){var r={},n=e[e.l];return++e.l,r.above=!(n&64),r.left=!(n&128),e.l+=18,r.name=Yf(e),r}function bh(e,t,r){r==null&&(r=H(84+4*e.length));var n=192;t&&(t.above&&(n&=-65),t.left&&(n&=-129)),r.write_shift(1,n);for(var a=1;a<3;++a)r.write_shift(1,0);return Nn({auto:1},r),r.write_shift(-4,-1),r.write_shift(-4,-1),d0(e,r),r.slice(0,r.l)}function Uh(e){var t=yr(e);return[t]}function Wh(e,t,r){return r==null&&(r=H(8)),pt(t,r)}function jh(e){var t=gt(e);return[t]}function Hh(e,t,r){return r==null&&(r=H(4)),vt(t,r)}function Gh(e){var t=yr(e),r=e.read_shift(1);return[t,r,"b"]}function Vh(e,t,r){return r==null&&(r=H(9)),pt(t,r),r.write_shift(1,e.v?1:0),r}function zh(e){var t=gt(e),r=e.read_shift(1);return[t,r,"b"]}function Yh(e,t,r){return r==null&&(r=H(5)),vt(t,r),r.write_shift(1,e.v?1:0),r}function $h(e){var t=yr(e),r=e.read_shift(1);return[t,r,"e"]}function Xh(e,t,r){return r==null&&(r=H(9)),pt(t,r),r.write_shift(1,e.v),r}function Kh(e){var t=gt(e),r=e.read_shift(1);return[t,r,"e"]}function Jh(e,t,r){return r==null&&(r=H(8)),vt(t,r),r.write_shift(1,e.v),r.write_shift(2,0),r.write_shift(1,0),r}function qh(e){var t=yr(e),r=e.read_shift(4);return[t,r,"s"]}function Zh(e,t,r){return r==null&&(r=H(12)),pt(t,r),r.write_shift(4,t.v),r}function Qh(e){var t=gt(e),r=e.read_shift(4);return[t,r,"s"]}function eu(e,t,r){return r==null&&(r=H(8)),vt(t,r),r.write_shift(4,t.v),r}function ru(e){var t=yr(e),r=Ut(e);return[t,r,"n"]}function tu(e,t,r){return r==null&&(r=H(16)),pt(t,r),xt(e.v,r),r}function nu(e){var t=gt(e),r=Ut(e);return[t,r,"n"]}function au(e,t,r){return r==null&&(r=H(12)),vt(t,r),xt(e.v,r),r}function iu(e){var t=yr(e),r=m0(e);return[t,r,"n"]}function su(e,t,r){return r==null&&(r=H(12)),pt(t,r),p0(e.v,r),r}function fu(e){var t=gt(e),r=m0(e);return[t,r,"n"]}function lu(e,t,r){return r==null&&(r=H(8)),vt(t,r),p0(e.v,r),r}function ou(e){var t=yr(e),r=ka(e);return[t,r,"is"]}function cu(e){var t=yr(e),r=nr(e);return[t,r,"str"]}function hu(e,t,r){return r==null&&(r=H(12+4*e.v.length)),pt(t,r),Ve(e.v,r),r.length>r.l?r.slice(0,r.l):r}function uu(e){var t=gt(e),r=nr(e);return[t,r,"str"]}function xu(e,t,r){return r==null&&(r=H(8+4*e.v.length)),vt(t,r),Ve(e.v,r),r.length>r.l?r.slice(0,r.l):r}function du(e,t,r){var n=e.l+t,a=yr(e);a.r=r["!row"];var i=e.read_shift(1),s=[a,i,"b"];if(r.cellFormula){e.l+=2;var f=zn(e,n-e.l,r);s[3]=Lt(f,null,a,r.supbooks,r)}else e.l=n;return s}function mu(e,t,r){var n=e.l+t,a=yr(e);a.r=r["!row"];var i=e.read_shift(1),s=[a,i,"e"];if(r.cellFormula){e.l+=2;var f=zn(e,n-e.l,r);s[3]=Lt(f,null,a,r.supbooks,r)}else e.l=n;return s}function pu(e,t,r){var n=e.l+t,a=yr(e);a.r=r["!row"];var i=Ut(e),s=[a,i,"n"];if(r.cellFormula){e.l+=2;var f=zn(e,n-e.l,r);s[3]=Lt(f,null,a,r.supbooks,r)}else e.l=n;return s}function gu(e,t,r){var n=e.l+t,a=yr(e);a.r=r["!row"];var i=nr(e),s=[a,i,"str"];if(r.cellFormula){e.l+=2;var f=zn(e,n-e.l,r);s[3]=Lt(f,null,a,r.supbooks,r)}else e.l=n;return s}var vu=_t,_u=bt;function Tu(e,t){return t==null&&(t=H(4)),t.write_shift(4,e),t}function Eu(e,t){var r=e.l+t,n=_t(e),a=Oa(e),i=nr(e),s=nr(e),f=nr(e);e.l=r;var o={rfx:n,relId:a,loc:i,display:f};return s&&(o.Tooltip=s),o}function wu(e,t){var r=H(50+4*(e[1].Target.length+(e[1].Tooltip||"").length));bt({s:Ge(e[0]),e:Ge(e[0])},r),Ia("rId"+t,r);var n=e[1].Target.indexOf("#"),a=n==-1?"":e[1].Target.slice(n+1);return Ve(a||"",r),Ve(e[1].Tooltip||"",r),Ve("",r),r.slice(0,r.l)}function Su(){}function yu(e,t,r){var n=e.l+t,a=g0(e),i=e.read_shift(1),s=[a];if(s[2]=i,r.cellFormula){var f=uh(e,n-e.l,r);s[1]=f}else e.l=n;return s}function Au(e,t,r){var n=e.l+t,a=_t(e),i=[a];if(r.cellFormula){var s=dh(e,n-e.l,r);i[1]=s,e.l=n}else e.l=n;return i}function Fu(e,t,r){r==null&&(r=H(18));var n=Yn(e,t);r.write_shift(-4,e),r.write_shift(-4,e),r.write_shift(4,(n.width||10)*256),r.write_shift(4,0);var a=0;return t.hidden&&(a|=1),typeof n.width=="number"&&(a|=2),t.level&&(a|=t.level<<8),r.write_shift(2,a),r}var es=["left","right","top","bottom","header","footer"];function Cu(e){var t={};return es.forEach(function(r){t[r]=Ut(e)}),t}function ku(e,t){return t==null&&(t=H(6*8)),Z0(e),es.forEach(function(r){xt(e[r],t)}),t}function Ou(e){var t=e.read_shift(2);return e.l+=28,{RTL:t&32}}function Iu(e,t,r){r==null&&(r=H(30));var n=924;return(((t||{}).Views||[])[0]||{}).RTL&&(n|=32),r.write_shift(2,n),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(2,0),r.write_shift(2,100),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(4,0),r}function Du(e){var t=H(24);return t.write_shift(4,4),t.write_shift(4,1),bt(e,t),t}function Nu(e,t){return t==null&&(t=H(16*4+2)),t.write_shift(2,e.password?L0(e.password):0),t.write_shift(4,1),[["objects",!1],["scenarios",!1],["formatCells",!0],["formatColumns",!0],["formatRows",!0],["insertColumns",!0],["insertRows",!0],["insertHyperlinks",!0],["deleteColumns",!0],["deleteRows",!0],["selectLockedCells",!1],["sort",!0],["autoFilter",!0],["pivotTables",!0],["selectUnlockedCells",!1]].forEach(function(r){r[1]?t.write_shift(4,e[r[0]]!=null&&!e[r[0]]?1:0):t.write_shift(4,e[r[0]]!=null&&e[r[0]]?0:1)}),t}function Ru(){}function Pu(){}function Lu(e,t,r,n,a,i,s){if(t.v===void 0)return!1;var f="";switch(t.t){case"b":f=t.v?"1":"0";break;case"d":t=hr(t),t.z=t.z||Le[14],t.v=cr(lr(t.v)),t.t="n";break;case"n":case"e":f=""+t.v;break;default:f=t.v;break}var o={r,c:n};switch(o.s=it(a.cellXfs,t,a),t.l&&i["!links"].push([Ae(o),t.l]),t.c&&i["!comments"].push([Ae(o),t.c]),t.t){case"s":case"str":return a.bookSST?(f=Ma(a.Strings,t.v,a.revStrings),o.t="s",o.v=f,s?Y(e,18,eu(t,o)):Y(e,7,Zh(t,o))):(o.t="str",s?Y(e,17,xu(t,o)):Y(e,6,hu(t,o))),!0;case"n":return t.v==(t.v|0)&&t.v>-1e3&&t.v<1e3?s?Y(e,13,lu(t,o)):Y(e,2,su(t,o)):s?Y(e,16,au(t,o)):Y(e,5,tu(t,o)),!0;case"b":return o.t="b",s?Y(e,15,Yh(t,o)):Y(e,4,Vh(t,o)),!0;case"e":return o.t="e",s?Y(e,14,Jh(t,o)):Y(e,3,Xh(t,o)),!0}return s?Y(e,12,Hh(t,o)):Y(e,1,Wh(t,o)),!0}function Mu(e,t,r,n){var a=Ie(t["!ref"]||"A1"),i,s="",f=[];Y(e,145);var o=Array.isArray(t),l=a.e.r;t["!rows"]&&(l=Math.max(a.e.r,t["!rows"].length-1));for(var c=a.s.r;c<=l;++c){s=qe(c),Rh(e,t,a,c);var m=!1;if(c<=a.e.r)for(var d=a.s.c;d<=a.e.c;++d){c===a.s.r&&(f[d]=tr(d)),i=f[d]+s;var x=o?(t[c]||[])[d]:t[i];if(!x){m=!1;continue}m=Lu(e,x,c,d,n,t,m)}}Y(e,146)}function Bu(e,t){!t||!t["!merges"]||(Y(e,177,Tu(t["!merges"].length)),t["!merges"].forEach(function(r){Y(e,176,_u(r))}),Y(e,178))}function bu(e,t){!t||!t["!cols"]||(Y(e,390),t["!cols"].forEach(function(r,n){r&&Y(e,60,Fu(n,r))}),Y(e,391))}function Uu(e,t){!t||!t["!ref"]||(Y(e,648),Y(e,649,Du(Ie(t["!ref"]))),Y(e,650))}function Wu(e,t,r){t["!links"].forEach(function(n){if(n[1].Target){var a=Se(r,-1,n[1].Target.replace(/#.*$/,""),_e.HLINK);Y(e,494,wu(n,a))}}),delete t["!links"]}function ju(e,t,r,n){if(t["!comments"].length>0){var a=Se(n,-1,"../drawings/vmlDrawing"+(r+1)+".vml",_e.VML);Y(e,551,Ia("rId"+a)),t["!legacy"]=a}}function Hu(e,t,r,n){if(t["!autofilter"]){var a=t["!autofilter"],i=typeof a.ref=="string"?a.ref:je(a.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var s=r.Workbook.Names,f=gr(i);f.s.r==f.e.r&&(f.e.r=gr(t["!ref"]).e.r,i=je(f));for(var o=0;o<s.length;++o){var l=s[o];if(l.Name=="_xlnm._FilterDatabase"&&l.Sheet==n){l.Ref="'"+r.SheetNames[n]+"'!"+i;break}}o==s.length&&s.push({Name:"_xlnm._FilterDatabase",Sheet:n,Ref:"'"+r.SheetNames[n]+"'!"+i}),Y(e,161,bt(Ie(i))),Y(e,162)}}function Gu(e,t,r){Y(e,133),Y(e,137,Iu(t,r)),Y(e,138),Y(e,134)}function Vu(e,t){t["!protect"]&&Y(e,535,Nu(t["!protect"]))}function zu(e,t,r,n){var a=or(),i=r.SheetNames[e],s=r.Sheets[i]||{},f=i;try{r&&r.Workbook&&(f=r.Workbook.Sheets[e].CodeName||f)}catch(l){}var o=Ie(s["!ref"]||"A1");if(o.e.c>16383||o.e.r>1048575){if(t.WTF)throw new Error("Range "+(s["!ref"]||"A1")+" exceeds format limit A1:XFD1048576");o.e.c=Math.min(o.e.c,16383),o.e.r=Math.min(o.e.c,1048575)}return s["!links"]=[],s["!comments"]=[],Y(a,129),(r.vbaraw||s["!outline"])&&Y(a,147,bh(f,s["!outline"])),Y(a,148,Lh(o)),Gu(a,s,r.Workbook),bu(a,s),Mu(a,s,e,t),Vu(a,s),Hu(a,s,r,e),Bu(a,s),Wu(a,s,n),s["!margins"]&&Y(a,476,ku(s["!margins"])),(!t||t.ignoreEC||t.ignoreEC==null)&&Uu(a,s),ju(a,s,e,n),Y(a,130),a.end()}function Yu(e,t){e.l+=10;var r=nr(e);return{name:r}}var $u=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]];function Xu(e){return!e.Workbook||!e.Workbook.WBProps?"false":yf(e.Workbook.WBProps.date1904)?"true":"false"}var Ku="][*?/\\".split("");function rs(e,t){if(e.length>31)throw new Error("Sheet names cannot exceed 31 chars");var r=!0;return Ku.forEach(function(n){if(e.indexOf(n)!=-1)throw new Error("Sheet name cannot contain : \\ / ? * [ ]")}),r}function Ju(e,t,r){e.forEach(function(n,a){rs(n);for(var i=0;i<a;++i)if(n==e[i])throw new Error("Duplicate Sheet Name: "+n);if(r){var s=t&&t[a]&&t[a].CodeName||n;if(s.charCodeAt(0)==95&&s.length>22)throw new Error("Bad Code Name: Worksheet"+s)}})}function qu(e){if(!e||!e.SheetNames||!e.Sheets)throw new Error("Invalid Workbook");if(!e.SheetNames.length)throw new Error("Workbook is empty");var t=e.Workbook&&e.Workbook.Sheets||[];Ju(e.SheetNames,t,!!e.vbaraw);for(var r=0;r<e.SheetNames.length;++r)_h(e.Sheets[e.SheetNames[r]],e.SheetNames[r],r)}function ts(e){var t=[Ue];t[t.length]=ee("workbook",null,{xmlns:Mt[0],"xmlns:r":He.r});var r=e.Workbook&&(e.Workbook.Names||[]).length>0,n={codeName:"ThisWorkbook"};e.Workbook&&e.Workbook.WBProps&&($u.forEach(function(f){e.Workbook.WBProps[f[0]]!=null&&e.Workbook.WBProps[f[0]]!=f[1]&&(n[f[0]]=e.Workbook.WBProps[f[0]])}),e.Workbook.WBProps.CodeName&&(n.codeName=e.Workbook.WBProps.CodeName,delete n.CodeName)),t[t.length]=ee("workbookPr",null,n);var a=e.Workbook&&e.Workbook.Sheets||[],i=0;if(a&&a[0]&&a[0].Hidden){for(t[t.length]="<bookViews>",i=0;i!=e.SheetNames.length&&!(!a[i]||!a[i].Hidden);++i);i==e.SheetNames.length&&(i=0),t[t.length]='<workbookView firstSheet="'+i+'" activeTab="'+i+'"/>',t[t.length]="</bookViews>"}for(t[t.length]="<sheets>",i=0;i!=e.SheetNames.length;++i){var s={name:ye(e.SheetNames[i].slice(0,31))};if(s.sheetId=""+(i+1),s["r:id"]="rId"+(i+1),a[i])switch(a[i].Hidden){case 1:s.state="hidden";break;case 2:s.state="veryHidden";break}t[t.length]=ee("sheet",null,s)}return t[t.length]="</sheets>",r&&(t[t.length]="<definedNames>",e.Workbook&&e.Workbook.Names&&e.Workbook.Names.forEach(function(f){var o={name:f.Name};f.Comment&&(o.comment=f.Comment),f.Sheet!=null&&(o.localSheetId=""+f.Sheet),f.Hidden&&(o.hidden="1"),f.Ref&&(t[t.length]=ee("definedName",ye(f.Ref),o))}),t[t.length]="</definedNames>"),t.length>2&&(t[t.length]="</workbook>",t[1]=t[1].replace("/>",">")),t.join("")}function Zu(e,t){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=pa(e),r.name=nr(e),r}function Qu(e,t){return t||(t=H(127)),t.write_shift(4,e.Hidden),t.write_shift(4,e.iTabID),Ia(e.strRelID,t),Ve(e.name.slice(0,31),t),t.length>t.l?t.slice(0,t.l):t}function e1(e,t){var r={},n=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var a=t>8?nr(e):"";return a.length>0&&(r.CodeName=a),r.autoCompressPictures=!!(n&65536),r.backupFile=!!(n&64),r.checkCompatibility=!!(n&4096),r.date1904=!!(n&1),r.filterPrivacy=!!(n&8),r.hidePivotFieldList=!!(n&1024),r.promptedSolutions=!!(n&16),r.publishItems=!!(n&2048),r.refreshAllConnections=!!(n&262144),r.saveExternalLinkValues=!!(n&128),r.showBorderUnselectedTables=!!(n&4),r.showInkAnnotation=!!(n&32),r.showObjects=["all","placeholders","none"][n>>13&3],r.showPivotChartFilter=!!(n&32768),r.updateLinks=["userSet","never","always"][n>>8&3],r}function r1(e,t){t||(t=H(72));var r=0;return e&&e.filterPrivacy&&(r|=8),t.write_shift(4,r),t.write_shift(4,0),d0(e&&e.CodeName||"ThisWorkbook",t),t.slice(0,t.l)}function t1(e,t,r){var n=e.l+t;e.l+=4,e.l+=1;var a=e.read_shift(4),i=$f(e),s=xh(e,0,r),f=Oa(e);e.l=n;var o={Name:i,Ptg:s};return a<268435455&&(o.Sheet=a),f&&(o.Comment=f),o}function n1(e,t){Y(e,143);for(var r=0;r!=t.SheetNames.length;++r){var n=t.Workbook&&t.Workbook.Sheets&&t.Workbook.Sheets[r]&&t.Workbook.Sheets[r].Hidden||0,a={Hidden:n,iTabID:r+1,strRelID:"rId"+(r+1),name:t.SheetNames[r]};Y(e,156,Qu(a))}Y(e,144)}function a1(e,t){t||(t=H(127));for(var r=0;r!=4;++r)t.write_shift(4,0);return Ve("SheetJS",t),Ve(An.version,t),Ve(An.version,t),Ve("7262",t),t.length>t.l?t.slice(0,t.l):t}function i1(e,t){t||(t=H(29)),t.write_shift(-4,0),t.write_shift(-4,460),t.write_shift(4,28800),t.write_shift(4,17600),t.write_shift(4,500),t.write_shift(4,e),t.write_shift(4,e);var r=120;return t.write_shift(1,r),t.length>t.l?t.slice(0,t.l):t}function s1(e,t){if(!(!t.Workbook||!t.Workbook.Sheets)){for(var r=t.Workbook.Sheets,n=0,a=-1,i=-1;n<r.length;++n)!r[n]||!r[n].Hidden&&a==-1?a=n:r[n].Hidden==1&&i==-1&&(i=n);i>a||(Y(e,135),Y(e,158,i1(a)),Y(e,136))}}function f1(e,t){var r=or();return Y(r,131),Y(r,128,a1()),Y(r,153,r1(e.Workbook&&e.Workbook.WBProps||null)),s1(r,e),n1(r,e),Y(r,132),r.end()}function l1(e,t,r){return(t.slice(-4)===".bin"?f1:ts)(e)}function o1(e,t,r,n,a){return(t.slice(-4)===".bin"?zu:Q0)(e,r,n,a)}function c1(e,t,r){return(t.slice(-4)===".bin"?ko:b0)(e,r)}function h1(e,t,r){return(t.slice(-4)===".bin"?eo:P0)(e,r)}function u1(e,t,r){return(t.slice(-4)===".bin"?Vo:G0)(e)}function x1(e){return(e.slice(-4)===".bin"?Mo:j0)()}function d1(e,t){var r=[];return e.Props&&r.push(ol(e.Props,t)),e.Custprops&&r.push(cl(e.Props,e.Custprops)),r.join("")}function m1(){return""}function p1(e,t){var r=['<Style ss:ID="Default" ss:Name="Normal"><NumberFormat/></Style>'];return t.cellXfs.forEach(function(n,a){var i=[];i.push(ee("NumberFormat",null,{"ss:Format":ye(Le[n.numFmtId])}));var s={"ss:ID":"s"+(21+a)};r.push(ee("Style",i.join(""),s))}),ee("Styles",r.join(""))}function ns(e){return ee("NamedRange",null,{"ss:Name":e.Name,"ss:RefersTo":"="+Pa(e.Ref,{r:0,c:0})})}function g1(e){if(!((e||{}).Workbook||{}).Names)return"";for(var t=e.Workbook.Names,r=[],n=0;n<t.length;++n){var a=t[n];a.Sheet==null&&(a.Name.match(/^_xlfn\./)||r.push(ns(a)))}return ee("Names",r.join(""))}function v1(e,t,r,n){if(!e||!((n||{}).Workbook||{}).Names)return"";for(var a=n.Workbook.Names,i=[],s=0;s<a.length;++s){var f=a[s];f.Sheet==r&&(f.Name.match(/^_xlfn\./)||i.push(ns(f)))}return i.join("")}function _1(e,t,r,n){if(!e)return"";var a=[];if(e["!margins"]&&(a.push("<PageSetup>"),e["!margins"].header&&a.push(ee("Header",null,{"x:Margin":e["!margins"].header})),e["!margins"].footer&&a.push(ee("Footer",null,{"x:Margin":e["!margins"].footer})),a.push(ee("PageMargins",null,{"x:Bottom":e["!margins"].bottom||"0.75","x:Left":e["!margins"].left||"0.7","x:Right":e["!margins"].right||"0.7","x:Top":e["!margins"].top||"0.75"})),a.push("</PageSetup>")),n&&n.Workbook&&n.Workbook.Sheets&&n.Workbook.Sheets[r])if(n.Workbook.Sheets[r].Hidden)a.push(ee("Visible",n.Workbook.Sheets[r].Hidden==1?"SheetHidden":"SheetVeryHidden",{}));else{for(var i=0;i<r&&!(n.Workbook.Sheets[i]&&!n.Workbook.Sheets[i].Hidden);++i);i==r&&a.push("<Selected/>")}return((((n||{}).Workbook||{}).Views||[])[0]||{}).RTL&&a.push("<DisplayRightToLeft/>"),e["!protect"]&&(a.push(Je("ProtectContents","True")),e["!protect"].objects&&a.push(Je("ProtectObjects","True")),e["!protect"].scenarios&&a.push(Je("ProtectScenarios","True")),e["!protect"].selectLockedCells!=null&&!e["!protect"].selectLockedCells?a.push(Je("EnableSelection","NoSelection")):e["!protect"].selectUnlockedCells!=null&&!e["!protect"].selectUnlockedCells&&a.push(Je("EnableSelection","UnlockedCells")),[["formatCells","AllowFormatCells"],["formatColumns","AllowSizeCols"],["formatRows","AllowSizeRows"],["insertColumns","AllowInsertCols"],["insertRows","AllowInsertRows"],["insertHyperlinks","AllowInsertHyperlinks"],["deleteColumns","AllowDeleteCols"],["deleteRows","AllowDeleteRows"],["sort","AllowSort"],["autoFilter","AllowFilter"],["pivotTables","AllowUsePivotTables"]].forEach(function(s){e["!protect"][s[0]]&&a.push("<"+s[1]+"/>")})),a.length==0?"":ee("WorksheetOptions",a.join(""),{xmlns:mr.x})}function T1(e){return e.map(function(t){var r=Sf(t.t||""),n=ee("ss:Data",r,{xmlns:"http://www.w3.org/TR/REC-html40"});return ee("Comment",n,{"ss:Author":t.a})}).join("")}function E1(e,t,r,n,a,i,s){if(!e||e.v==null&&e.f==null)return"";var f={};if(e.f&&(f["ss:Formula"]="="+ye(Pa(e.f,s))),e.F&&e.F.slice(0,t.length)==t){var o=Ge(e.F.slice(t.length+1));f["ss:ArrayRange"]="RC:R"+(o.r==s.r?"":"["+(o.r-s.r)+"]")+"C"+(o.c==s.c?"":"["+(o.c-s.c)+"]")}if(e.l&&e.l.Target&&(f["ss:HRef"]=ye(e.l.Target),e.l.Tooltip&&(f["x:HRefScreenTip"]=ye(e.l.Tooltip))),r["!merges"])for(var l=r["!merges"],c=0;c!=l.length;++c)l[c].s.c!=s.c||l[c].s.r!=s.r||(l[c].e.c>l[c].s.c&&(f["ss:MergeAcross"]=l[c].e.c-l[c].s.c),l[c].e.r>l[c].s.r&&(f["ss:MergeDown"]=l[c].e.r-l[c].s.r));var m="",d="";switch(e.t){case"z":if(!n.sheetStubs)return"";break;case"n":m="Number",d=String(e.v);break;case"b":m="Boolean",d=e.v?"1":"0";break;case"e":m="Error",d=on[e.v];break;case"d":m="DateTime",d=new Date(e.v).toISOString(),e.z==null&&(e.z=e.z||Le[14]);break;case"s":m="String",d=wf(e.v||"");break}var x=it(n.cellXfs,e,n);f["ss:StyleID"]="s"+(21+x),f["ss:Index"]=s.c+1;var v=e.v!=null?d:"",u=e.t=="z"?"":'<Data ss:Type="'+m+'">'+v+"</Data>";return(e.c||[]).length>0&&(u+=T1(e.c)),ee("Cell",u,f)}function w1(e,t){var r='<Row ss:Index="'+(e+1)+'"';return t&&(t.hpt&&!t.hpx&&(t.hpx=B0(t.hpt)),t.hpx&&(r+=' ss:AutoFitHeight="0" ss:Height="'+t.hpx+'"'),t.hidden&&(r+=' ss:Hidden="1"')),r+">"}function S1(e,t,r,n){if(!e["!ref"])return"";var a=Ie(e["!ref"]),i=e["!merges"]||[],s=0,f=[];e["!cols"]&&e["!cols"].forEach(function(_,R){Na(_);var P=!!_.width,O=Yn(R,_),j={"ss:Index":R+1};P&&(j["ss:Width"]=Pn(O.width)),_.hidden&&(j["ss:Hidden"]="1"),f.push(ee("Column",null,j))});for(var o=Array.isArray(e),l=a.s.r;l<=a.e.r;++l){for(var c=[w1(l,(e["!rows"]||[])[l])],m=a.s.c;m<=a.e.c;++m){var d=!1;for(s=0;s!=i.length;++s)if(!(i[s].s.c>m)&&!(i[s].s.r>l)&&!(i[s].e.c<m)&&!(i[s].e.r<l)){(i[s].s.c!=m||i[s].s.r!=l)&&(d=!0);break}if(!d){var x={r:l,c:m},v=Ae(x),u=o?(e[l]||[])[m]:e[v];c.push(E1(u,v,e,t,r,n,x))}}c.push("</Row>"),c.length>2&&f.push(c.join(""))}return f.join("")}function y1(e,t,r){var n=[],a=r.SheetNames[e],i=r.Sheets[a],s=i?v1(i,t,e,r):"";return s.length>0&&n.push("<Names>"+s+"</Names>"),s=i?S1(i,t,e,r):"",s.length>0&&n.push("<Table>"+s+"</Table>"),n.push(_1(i,t,e,r)),n.join("")}function A1(e,t){t||(t={}),e.SSF||(e.SSF=hr(Le)),e.SSF&&(jn(),Wn(e.SSF),t.revssf=Hn(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF,t.cellXfs=[],it(t.cellXfs,{},{revssf:{General:0}}));var r=[];r.push(d1(e,t)),r.push(m1()),r.push(""),r.push("");for(var n=0;n<e.SheetNames.length;++n)r.push(ee("Worksheet",y1(n,t,e),{"ss:Name":ye(e.SheetNames[n])}));return r[2]=p1(e,t),r[3]=g1(e),Ue+ee("Workbook",r.join(""),{xmlns:mr.ss,"xmlns:o":mr.o,"xmlns:x":mr.x,"xmlns:ss":mr.ss,"xmlns:dt":mr.dt,"xmlns:html":mr.html})}var oa={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae",UDI:"05d5cdd59c2e1b10939708002b2cf9ae"};function F1(e,t){var r=[],n=[],a=[],i=0,s,f=ni(pi,"n"),o=ni(gi,"n");if(e.Props)for(s=Ze(e.Props),i=0;i<s.length;++i)(Object.prototype.hasOwnProperty.call(f,s[i])?r:Object.prototype.hasOwnProperty.call(o,s[i])?n:a).push([s[i],e.Props[s[i]]]);if(e.Custprops)for(s=Ze(e.Custprops),i=0;i<s.length;++i)Object.prototype.hasOwnProperty.call(e.Props||{},s[i])||(Object.prototype.hasOwnProperty.call(f,s[i])?r:Object.prototype.hasOwnProperty.call(o,s[i])?n:a).push([s[i],e.Custprops[s[i]]]);var l=[];for(i=0;i<a.length;++i)C0.indexOf(a[i][0])>-1||y0.indexOf(a[i][0])>-1||a[i][1]!=null&&l.push(a[i]);n.length&&Ce.utils.cfb_add(t,"/SummaryInformation",wi(n,oa.SI,o,gi)),(r.length||l.length)&&Ce.utils.cfb_add(t,"/DocumentSummaryInformation",wi(r,oa.DSI,f,pi,l.length?l:null,oa.UDI))}function C1(e,t){var r=t||{},n=Ce.utils.cfb_new({root:"R"}),a="/Workbook";switch(r.bookType||"xls"){case"xls":r.bookType="biff8";case"xla":r.bookType||(r.bookType="xla");case"biff8":a="/Workbook",r.biff=8;break;case"biff5":a="/Book",r.biff=5;break;default:throw new Error("invalid type "+r.bookType+" for XLS CFB")}return Ce.utils.cfb_add(n,a,as(e,r)),r.biff==8&&(e.Props||e.Custprops)&&F1(e,n),r.biff==8&&e.vbaraw&&zo(n,Ce.read(e.vbaraw,{type:typeof e.vbaraw=="string"?"binary":"buffer"})),n}var k1={0:{f:Dh},1:{f:Uh},2:{f:iu},3:{f:$h},4:{f:Gh},5:{f:ru},6:{f:cu},7:{f:qh},8:{f:gu},9:{f:pu},10:{f:du},11:{f:mu},12:{f:jh},13:{f:fu},14:{f:Kh},15:{f:zh},16:{f:nu},17:{f:uu},18:{f:Qh},19:{f:ka},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:t1},40:{},42:{},43:{f:oo},44:{f:fo},45:{f:uo},46:{f:mo},47:{f:xo},48:{},49:{f:Wf},50:{},51:{f:Do},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:jl},62:{f:ou},63:{f:Bo},64:{f:Ru},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:Br,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:Ou},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:Bh},148:{f:Ph,p:16},151:{f:Su},152:{},153:{f:e1},154:{},155:{},156:{f:Zu},157:{},158:{},159:{T:1,f:ql},160:{T:-1},161:{T:1,f:_t},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:vu},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:Oo},336:{T:-1},337:{f:Po,T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:pa},357:{},358:{},359:{},360:{T:1},361:{},362:{f:Ll},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:yu},427:{f:Au},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:Cu},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:Mh},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:Eu},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:pa},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:Ho},633:{T:1},634:{T:-1},635:{T:1,f:Wo},636:{T:-1},637:{f:Vf},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:Yu},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:Pu},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}};function re(e,t,r,n){var a=t;if(!isNaN(a)){var i=n||(r||[]).length||0,s=e.next(4);s.write_shift(2,a),s.write_shift(2,i),i>0&&Aa(r)&&e.push(r)}}function O1(e,t,r,n){var a=(r||[]).length||0;if(a<=8224)return re(e,t,r,a);var i=t;if(!isNaN(i)){for(var s=r.parts||[],f=0,o=0,l=0;l+(s[f]||8224)<=8224;)l+=s[f]||8224,f++;var c=e.next(4);for(c.write_shift(2,i),c.write_shift(2,l),e.push(r.slice(o,o+l)),o+=l;o<a;){for(c=e.next(4),c.write_shift(2,60),l=0;l+(s[f]||8224)<=8224;)l+=s[f]||8224,f++;c.write_shift(2,l),e.push(r.slice(o,o+l)),o+=l}}}function hn(e,t,r){return e||(e=H(7)),e.write_shift(2,t),e.write_shift(2,r),e.write_shift(2,0),e.write_shift(1,0),e}function I1(e,t,r,n){var a=H(9);return hn(a,e,t),O0(r,n||"b",a),a}function D1(e,t,r){var n=H(8+2*r.length);return hn(n,e,t),n.write_shift(1,r.length),n.write_shift(r.length,r,"sbcs"),n.l<n.length?n.slice(0,n.l):n}function N1(e,t,r,n){if(t.v!=null)switch(t.t){case"d":case"n":var a=t.t=="d"?cr(lr(t.v)):t.v;a==(a|0)&&a>=0&&a<65536?re(e,2,zl(r,n,a)):re(e,3,Vl(r,n,a));return;case"b":case"e":re(e,5,I1(r,n,t.v,t.t));return;case"s":case"str":re(e,4,D1(r,n,(t.v||"").slice(0,255)));return}re(e,1,hn(null,r,n))}function R1(e,t,r,n){var a=Array.isArray(t),i=Ie(t["!ref"]||"A1"),s,f="",o=[];if(i.e.c>255||i.e.r>16383){if(n.WTF)throw new Error("Range "+(t["!ref"]||"A1")+" exceeds format limit A1:IV16384");i.e.c=Math.min(i.e.c,255),i.e.r=Math.min(i.e.c,16383),s=je(i)}for(var l=i.s.r;l<=i.e.r;++l){f=qe(l);for(var c=i.s.c;c<=i.e.c;++c){l===i.s.r&&(o[c]=tr(c)),s=o[c]+f;var m=a?(t[l]||[])[c]:t[s];m&&N1(e,m,l,c)}}}function P1(e,t){for(var r=t||{},n=or(),a=0,i=0;i<e.SheetNames.length;++i)e.SheetNames[i]==r.sheet&&(a=i);if(a==0&&r.sheet&&e.SheetNames[0]!=r.sheet)throw new Error("Sheet not found: "+r.sheet);return re(n,r.biff==4?1033:r.biff==3?521:9,Da(e,16,r)),R1(n,e.Sheets[e.SheetNames[a]],a,r),re(n,10),n.end()}function L1(e,t,r){re(e,49,Cl({sz:12,name:"Arial"},r))}function M1(e,t,r){t&&[[5,8],[23,26],[41,44],[50,392]].forEach(function(n){for(var a=n[0];a<=n[1];++a)t[a]!=null&&re(e,1054,Il(a,t[a],r))})}function B1(e,t){var r=H(19);r.write_shift(4,2151),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,1),r.write_shift(4,0),re(e,2151,r),r=H(39),r.write_shift(4,2152),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,0),r.write_shift(4,0),r.write_shift(2,1),r.write_shift(4,4),r.write_shift(2,0),N0(Ie(t["!ref"]||"A1"),r),r.write_shift(4,4),re(e,2152,r)}function b1(e,t){for(var r=0;r<16;++r)re(e,224,yi({numFmtId:0,style:!0},0,t));t.cellXfs.forEach(function(n){re(e,224,yi(n,0,t))})}function U1(e,t){for(var r=0;r<t["!links"].length;++r){var n=t["!links"][r];re(e,440,bl(n)),n[1].Tooltip&&re(e,2048,Ul(n))}delete t["!links"]}function W1(e,t){if(t){var r=0;t.forEach(function(n,a){++r<=256&&n&&re(e,125,Hl(Yn(a,n),a))})}}function j1(e,t,r,n,a){var i=16+it(a.cellXfs,t,a);if(t.v==null&&!t.bf){re(e,513,dt(r,n,i));return}if(t.bf)re(e,6,hh(t,r,n,a,i));else switch(t.t){case"d":case"n":var s=t.t=="d"?cr(lr(t.v)):t.v;re(e,515,Pl(r,n,s,i));break;case"b":case"e":re(e,517,Rl(r,n,t.v,i,a,t.t));break;case"s":case"str":if(a.bookSST){var f=Ma(a.Strings,t.v,a.revStrings);re(e,253,kl(r,n,f,i))}else re(e,516,Ol(r,n,(t.v||"").slice(0,255),i,a));break;default:re(e,513,dt(r,n,i))}}function H1(e,t,r){var n=or(),a=r.SheetNames[e],i=r.Sheets[a]||{},s=(r||{}).Workbook||{},f=(s.Sheets||[])[e]||{},o=Array.isArray(i),l=t.biff==8,c,m="",d=[],x=Ie(i["!ref"]||"A1"),v=l?65536:16384;if(x.e.c>255||x.e.r>=v){if(t.WTF)throw new Error("Range "+(i["!ref"]||"A1")+" exceeds format limit A1:IV16384");x.e.c=Math.min(x.e.c,255),x.e.r=Math.min(x.e.c,v-1)}re(n,2057,Da(r,16,t)),re(n,13,Sr(1)),re(n,12,Sr(100)),re(n,15,fr(!0)),re(n,17,fr(!1)),re(n,16,xt(.001)),re(n,95,fr(!0)),re(n,42,fr(!1)),re(n,43,fr(!1)),re(n,130,Sr(1)),re(n,128,Nl()),re(n,131,fr(!1)),re(n,132,fr(!1)),l&&W1(n,i["!cols"]),re(n,512,Dl(x,t)),l&&(i["!links"]=[]);for(var u=x.s.r;u<=x.e.r;++u){m=qe(u);for(var _=x.s.c;_<=x.e.c;++_){u===x.s.r&&(d[_]=tr(_)),c=d[_]+m;var R=o?(i[u]||[])[_]:i[c];R&&(j1(n,R,u,_,t),l&&R.l&&i["!links"].push([c,R.l]))}}var P=f.CodeName||f.name||a;return l&&re(n,574,Fl((s.Views||[])[0])),l&&(i["!merges"]||[]).length&&re(n,229,Bl(i["!merges"])),l&&U1(n,i),re(n,442,D0(P)),l&&B1(n,i),re(n,10),n.end()}function G1(e,t,r){var n=or(),a=(e||{}).Workbook||{},i=a.Sheets||[],s=a.WBProps||{},f=r.biff==8,o=r.biff==5;if(re(n,2057,Da(e,5,r)),r.bookType=="xla"&&re(n,135),re(n,225,f?Sr(1200):null),re(n,193,xl(2)),o&&re(n,191),o&&re(n,192),re(n,226),re(n,92,wl("SheetJS",r)),re(n,66,Sr(f?1200:1252)),f&&re(n,353,Sr(0)),f&&re(n,448),re(n,317,Gl(e.SheetNames.length)),f&&e.vbaraw&&re(n,211),f&&e.vbaraw){var l=s.CodeName||"ThisWorkbook";re(n,442,D0(l))}re(n,156,Sr(17)),re(n,25,fr(!1)),re(n,18,fr(!1)),re(n,19,Sr(0)),f&&re(n,431,fr(!1)),f&&re(n,444,Sr(0)),re(n,61,Al()),re(n,64,fr(!1)),re(n,141,Sr(0)),re(n,34,fr(Xu(e)=="true")),re(n,14,fr(!0)),f&&re(n,439,fr(!1)),re(n,218,Sr(0)),L1(n,e,r),M1(n,e.SSF,r),b1(n,r),f&&re(n,352,fr(!1));var c=n.end(),m=or();f&&re(m,140,Wl()),f&&r.Strings&&O1(m,252,yl(r.Strings)),re(m,10);var d=m.end(),x=or(),v=0,u=0;for(u=0;u<e.SheetNames.length;++u)v+=(f?12:11)+(f?2:1)*e.SheetNames[u].length;var _=c.length+v+d.length;for(u=0;u<e.SheetNames.length;++u){var R=i[u]||{};re(x,133,Sl({pos:_,hs:R.Hidden||0,dt:0,name:e.SheetNames[u]},r)),_+=t[u].length}var P=x.end();if(v!=P.length)throw new Error("BS8 "+v+" != "+P.length);var O=[];return c.length&&O.push(c),P.length&&O.push(P),d.length&&O.push(d),Ke(O)}function V1(e,t){var r=t||{},n=[];e&&!e.SSF&&(e.SSF=hr(Le)),e&&e.SSF&&(jn(),Wn(e.SSF),r.revssf=Hn(e.SSF),r.revssf[e.SSF[65535]]=0,r.ssf=e.SSF),r.Strings=[],r.Strings.Count=0,r.Strings.Unique=0,Ba(r),r.cellXfs=[],it(r.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});for(var a=0;a<e.SheetNames.length;++a)n[n.length]=H1(a,r,e);return n.unshift(G1(e,n,r)),Ke(n)}function as(e,t){for(var r=0;r<=e.SheetNames.length;++r){var n=e.Sheets[e.SheetNames[r]];if(!(!n||!n["!ref"])){var a=gr(n["!ref"]);a.e.c>255}}var i=t||{};switch(i.biff||2){case 8:case 5:return V1(e,t);case 4:case 3:case 2:return P1(e,t)}throw new Error("invalid type "+i.bookType+" for BIFF")}function z1(e,t,r,n){for(var a=e["!merges"]||[],i=[],s=t.s.c;s<=t.e.c;++s){for(var f=0,o=0,l=0;l<a.length;++l)if(!(a[l].s.r>r||a[l].s.c>s)&&!(a[l].e.r<r||a[l].e.c<s)){if(a[l].s.r<r||a[l].s.c<s){f=-1;break}f=a[l].e.r-a[l].s.r+1,o=a[l].e.c-a[l].s.c+1;break}if(!(f<0)){var c=Ae({r,c:s}),m=n.dense?(e[r]||[])[s]:e[c],d=m&&m.v!=null&&(m.h||Ef(m.w||(Yr(m),m.w)||""))||"",x={};f>1&&(x.rowspan=f),o>1&&(x.colspan=o),n.editable?d='<span contenteditable="true">'+d+"</span>":m&&(x["data-t"]=m&&m.t||"z",m.v!=null&&(x["data-v"]=m.v),m.z!=null&&(x["data-z"]=m.z),m.l&&(m.l.Target||"#").charAt(0)!="#"&&(d='<a href="'+m.l.Target+'">'+d+"</a>")),x.id=(n.id||"sjs")+"-"+c,i.push(ee("td",d,x))}}var v="<tr>";return v+i.join("")+"</tr>"}var Y1='<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',$1="</body></html>";function X1(e,t,r){var n=[];return n.join("")+"<table"+(r&&r.id?' id="'+r.id+'"':"")+">"}function is(e,t){var r=t||{},n=r.header!=null?r.header:Y1,a=r.footer!=null?r.footer:$1,i=[n],s=gr(e["!ref"]);r.dense=Array.isArray(e),i.push(X1(e,s,r));for(var f=s.s.r;f<=s.e.r;++f)i.push(z1(e,s,f,r));return i.push("</table>"+a),i.join("")}function ss(e,t,r){var n=r||{},a=0,i=0;if(n.origin!=null)if(typeof n.origin=="number")a=n.origin;else{var s=typeof n.origin=="string"?Ge(n.origin):n.origin;a=s.r,i=s.c}var f=t.getElementsByTagName("tr"),o=Math.min(n.sheetRows||1e7,f.length),l={s:{r:0,c:0},e:{r:a,c:i}};if(e["!ref"]){var c=gr(e["!ref"]);l.s.r=Math.min(l.s.r,c.s.r),l.s.c=Math.min(l.s.c,c.s.c),l.e.r=Math.max(l.e.r,c.e.r),l.e.c=Math.max(l.e.c,c.e.c),a==-1&&(l.e.r=a=c.e.r+1)}var m=[],d=0,x=e["!rows"]||(e["!rows"]=[]),v=0,u=0,_=0,R=0,P=0,O=0;for(e["!cols"]||(e["!cols"]=[]);v<f.length&&u<o;++v){var j=f[v];if(Di(j)){if(n.display)continue;x[u]={hidden:!0}}var Q=j.children;for(_=R=0;_<Q.length;++_){var ne=Q[_];if(!(n.display&&Di(ne))){var k=ne.hasAttribute("data-v")?ne.getAttribute("data-v"):ne.hasAttribute("v")?ne.getAttribute("v"):Af(ne.innerHTML),z=ne.getAttribute("data-z")||ne.getAttribute("z");for(d=0;d<m.length;++d){var W=m[d];W.s.c==R+i&&W.s.r<u+a&&u+a<=W.e.r&&(R=W.e.c+1-i,d=-1)}O=+ne.getAttribute("colspan")||1,((P=+ne.getAttribute("rowspan")||1)>1||O>1)&&m.push({s:{r:u+a,c:R+i},e:{r:u+a+(P||1)-1,c:R+i+(O||1)-1}});var X={t:"s",v:k},J=ne.getAttribute("data-t")||ne.getAttribute("t")||"";k!=null&&(k.length==0?X.t=J||"z":n.raw||k.trim().length==0||J=="s"||(k==="TRUE"?X={t:"b",v:!0}:k==="FALSE"?X={t:"b",v:!1}:isNaN(Gr(k))?isNaN(en(k).getDate())||(X={t:"d",v:lr(k)},n.cellDates||(X={t:"n",v:cr(X.v)}),X.z=n.dateNF||Le[14]):X={t:"n",v:Gr(k)})),X.z===void 0&&z!=null&&(X.z=z);var Z="",ae=ne.getElementsByTagName("A");if(ae&&ae.length)for(var pe=0;pe<ae.length&&!(ae[pe].hasAttribute("href")&&(Z=ae[pe].getAttribute("href"),Z.charAt(0)!="#"));++pe);Z&&Z.charAt(0)!="#"&&(X.l={Target:Z}),n.dense?(e[u+a]||(e[u+a]=[]),e[u+a][R+i]=X):e[Ae({c:R+i,r:u+a})]=X,l.e.c<R+i&&(l.e.c=R+i),R+=O}}++u}return m.length&&(e["!merges"]=(e["!merges"]||[]).concat(m)),l.e.r=Math.max(l.e.r,u-1+a),e["!ref"]=je(l),u>=o&&(e["!fullref"]=je((l.e.r=f.length-v+u-1+a,l))),e}function fs(e,t){var r=t||{},n=r.dense?[]:{};return ss(n,e,t)}function K1(e,t){return mt(fs(e,t),t)}function Di(e){var t="",r=J1(e);return r&&(t=r(e).getPropertyValue("display")),t||(t=e.style&&e.style.display),t==="none"}function J1(e){return e.ownerDocument.defaultView&&typeof e.ownerDocument.defaultView.getComputedStyle=="function"?e.ownerDocument.defaultView.getComputedStyle:typeof getComputedStyle=="function"?getComputedStyle:null}var q1=function(){var e=["<office:master-styles>",'<style:master-page style:name="mp1" style:page-layout-name="mp1">',"<style:header/>",'<style:header-left style:display="false"/>',"<style:footer/>",'<style:footer-left style:display="false"/>',"</style:master-page>","</office:master-styles>"].join(""),t="<office:document-styles "+tn({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+">"+e+"</office:document-styles>";return function(){return Ue+t}}(),Ni=function(){var e=function(i){return ye(i).replace(/  +/g,function(s){return'<text:s text:c="'+s.length+'"/>'}).replace(/\t/g,"<text:tab/>").replace(/\n/g,"</text:p><text:p>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>")},t=`          <table:table-cell />
`,r=`          <table:covered-table-cell/>
`,n=function(i,s,f){var o=[];o.push('      <table:table table:name="'+ye(s.SheetNames[f])+`" table:style-name="ta1">
`);var l=0,c=0,m=gr(i["!ref"]||"A1"),d=i["!merges"]||[],x=0,v=Array.isArray(i);if(i["!cols"])for(c=0;c<=m.e.c;++c)o.push("        <table:table-column"+(i["!cols"][c]?' table:style-name="co'+i["!cols"][c].ods+'"':"")+`></table:table-column>
`);var u="",_=i["!rows"]||[];for(l=0;l<m.s.r;++l)u=_[l]?' table:style-name="ro'+_[l].ods+'"':"",o.push("        <table:table-row"+u+`></table:table-row>
`);for(;l<=m.e.r;++l){for(u=_[l]?' table:style-name="ro'+_[l].ods+'"':"",o.push("        <table:table-row"+u+`>
`),c=0;c<m.s.c;++c)o.push(t);for(;c<=m.e.c;++c){var R=!1,P={},O="";for(x=0;x!=d.length;++x)if(!(d[x].s.c>c)&&!(d[x].s.r>l)&&!(d[x].e.c<c)&&!(d[x].e.r<l)){(d[x].s.c!=c||d[x].s.r!=l)&&(R=!0),P["table:number-columns-spanned"]=d[x].e.c-d[x].s.c+1,P["table:number-rows-spanned"]=d[x].e.r-d[x].s.r+1;break}if(R){o.push(r);continue}var j=Ae({r:l,c}),Q=v?(i[l]||[])[c]:i[j];if(Q&&Q.f&&(P["table:formula"]=ye(gh(Q.f)),Q.F&&Q.F.slice(0,j.length)==j)){var ne=gr(Q.F);P["table:number-matrix-columns-spanned"]=ne.e.c-ne.s.c+1,P["table:number-matrix-rows-spanned"]=ne.e.r-ne.s.r+1}if(!Q){o.push(t);continue}switch(Q.t){case"b":O=Q.v?"TRUE":"FALSE",P["office:value-type"]="boolean",P["office:boolean-value"]=Q.v?"true":"false";break;case"n":O=Q.w||String(Q.v||0),P["office:value-type"]="float",P["office:value"]=Q.v||0;break;case"s":case"str":O=Q.v==null?"":Q.v,P["office:value-type"]="string";break;case"d":O=Q.w||lr(Q.v).toISOString(),P["office:value-type"]="date",P["office:date-value"]=lr(Q.v).toISOString(),P["table:style-name"]="ce1";break;default:o.push(t);continue}var k=e(O);if(Q.l&&Q.l.Target){var z=Q.l.Target;z=z.charAt(0)=="#"?"#"+vh(z.slice(1)):z,z.charAt(0)!="#"&&!z.match(/^\w+:/)&&(z="../"+z),k=ee("text:a",k,{"xlink:href":z.replace(/&/g,"&amp;")})}o.push("          "+ee("table:table-cell",ee("text:p",k,{}),P)+`
`)}o.push(`        </table:table-row>
`)}return o.push(`      </table:table>
`),o.join("")},a=function(i,s){i.push(` <office:automatic-styles>
`),i.push(`  <number:date-style style:name="N37" number:automatic-order="true">
`),i.push(`   <number:month number:style="long"/>
`),i.push(`   <number:text>/</number:text>
`),i.push(`   <number:day number:style="long"/>
`),i.push(`   <number:text>/</number:text>
`),i.push(`   <number:year/>
`),i.push(`  </number:date-style>
`);var f=0;s.SheetNames.map(function(l){return s.Sheets[l]}).forEach(function(l){if(l&&l["!cols"]){for(var c=0;c<l["!cols"].length;++c)if(l["!cols"][c]){var m=l["!cols"][c];if(m.width==null&&m.wpx==null&&m.wch==null)continue;Na(m),m.ods=f;var d=l["!cols"][c].wpx+"px";i.push('  <style:style style:name="co'+f+`" style:family="table-column">
`),i.push('   <style:table-column-properties fo:break-before="auto" style:column-width="'+d+`"/>
`),i.push(`  </style:style>
`),++f}}});var o=0;s.SheetNames.map(function(l){return s.Sheets[l]}).forEach(function(l){if(l&&l["!rows"]){for(var c=0;c<l["!rows"].length;++c)if(l["!rows"][c]){l["!rows"][c].ods=o;var m=l["!rows"][c].hpx+"px";i.push('  <style:style style:name="ro'+o+`" style:family="table-row">
`),i.push('   <style:table-row-properties fo:break-before="auto" style:row-height="'+m+`"/>
`),i.push(`  </style:style>
`),++o}}}),i.push(`  <style:style style:name="ta1" style:family="table" style:master-page-name="mp1">
`),i.push(`   <style:table-properties table:display="true" style:writing-mode="lr-tb"/>
`),i.push(`  </style:style>
`),i.push(`  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>
`),i.push(` </office:automatic-styles>
`)};return function(s,f){var o=[Ue],l=tn({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"}),c=tn({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});f.bookType=="fods"?(o.push("<office:document"+l+c+`>
`),o.push(w0().replace(/office:document-meta/g,"office:meta"))):o.push("<office:document-content"+l+`>
`),a(o,s),o.push(`  <office:body>
`),o.push(`    <office:spreadsheet>
`);for(var m=0;m!=s.SheetNames.length;++m)o.push(n(s.Sheets[s.SheetNames[m]],s,m));return o.push(`    </office:spreadsheet>
`),o.push(`  </office:body>
`),f.bookType=="fods"?o.push("</office:document>"):o.push("</office:document-content>"),o.join("")}}();function ls(e,t){if(t.bookType=="fods")return Ni(e,t);var r=Ea(),n="",a=[],i=[];return n="mimetype",me(r,n,"application/vnd.oasis.opendocument.spreadsheet"),n="content.xml",me(r,n,Ni(e,t)),a.push([n,"text/xml"]),i.push([n,"ContentFile"]),n="styles.xml",me(r,n,q1(e,t)),a.push([n,"text/xml"]),i.push([n,"StylesFile"]),n="meta.xml",me(r,n,Ue+w0()),a.push([n,"text/xml"]),i.push([n,"MetadataFile"]),n="manifest.rdf",me(r,n,ll(i)),a.push([n,"application/rdf+xml"]),n="META-INF/manifest.xml",me(r,n,sl(a)),r}function Bn(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function Z1(e){return typeof TextEncoder!="undefined"?new TextEncoder().encode(e):Dr(rn(e))}function Q1(e,t){e:for(var r=0;r<=e.length-t.length;++r){for(var n=0;n<t.length;++n)if(e[r+n]!=t[n])continue e;return!0}return!1}function at(e){var t=e.reduce(function(a,i){return a+i.length},0),r=new Uint8Array(t),n=0;return e.forEach(function(a){r.set(a,n),n+=a.length}),r}function ex(e,t,r){var n=Math.floor(r==0?0:Math.LOG10E*Math.log(Math.abs(r)))+6176-20,a=r/Math.pow(10,n-6176);e[t+15]|=n>>7,e[t+14]|=(n&127)<<1;for(var i=0;a>=1;++i,a/=256)e[t+i]=a&255;e[t+15]|=r>=0?0:128}function nn(e,t){var r=t?t[0]:0,n=e[r]&127;e:if(e[r++]>=128&&(n|=(e[r]&127)<<7,e[r++]<128||(n|=(e[r]&127)<<14,e[r++]<128)||(n|=(e[r]&127)<<21,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,28),++r,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,35),++r,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,42),++r,e[r++]<128)))break e;return t&&(t[0]=r),n}function we(e){var t=new Uint8Array(7);t[0]=e&127;var r=1;e:if(e>127){if(t[r-1]|=128,t[r]=e>>7&127,++r,e<=16383||(t[r-1]|=128,t[r]=e>>14&127,++r,e<=2097151)||(t[r-1]|=128,t[r]=e>>21&127,++r,e<=268435455)||(t[r-1]|=128,t[r]=e/256>>>21&127,++r,e<=34359738367)||(t[r-1]|=128,t[r]=e/65536>>>21&127,++r,e<=4398046511103))break e;t[r-1]|=128,t[r]=e/16777216>>>21&127,++r}return t.slice(0,r)}function Pt(e){var t=0,r=e[t]&127;e:if(e[t++]>=128){if(r|=(e[t]&127)<<7,e[t++]<128||(r|=(e[t]&127)<<14,e[t++]<128)||(r|=(e[t]&127)<<21,e[t++]<128))break e;r|=(e[t]&127)<<28}return r}function We(e){for(var t=[],r=[0];r[0]<e.length;){var n=r[0],a=nn(e,r),i=a&7;a=Math.floor(a/8);var s=0,f;if(a==0)break;switch(i){case 0:{for(var o=r[0];e[r[0]++]>=128;);f=e.slice(o,r[0])}break;case 5:s=4,f=e.slice(r[0],r[0]+s),r[0]+=s;break;case 1:s=8,f=e.slice(r[0],r[0]+s),r[0]+=s;break;case 2:s=nn(e,r),f=e.slice(r[0],r[0]+s),r[0]+=s;break;case 3:case 4:default:throw new Error("PB Type ".concat(i," for Field ").concat(a," at offset ").concat(n))}var l={data:f,type:i};t[a]==null?t[a]=[l]:t[a].push(l)}return t}function $e(e){var t=[];return e.forEach(function(r,n){r.forEach(function(a){a.data&&(t.push(we(n*8+a.type)),a.type==2&&t.push(we(a.data.length)),t.push(a.data))})}),at(t)}function kr(e){for(var t,r=[],n=[0];n[0]<e.length;){var a=nn(e,n),i=We(e.slice(n[0],n[0]+a));n[0]+=a;var s={id:Pt(i[1][0].data),messages:[]};i[2].forEach(function(f){var o=We(f.data),l=Pt(o[3][0].data);s.messages.push({meta:o,data:e.slice(n[0],n[0]+l)}),n[0]+=l}),(t=i[3])!=null&&t[0]&&(s.merge=Pt(i[3][0].data)>>>0>0),r.push(s)}return r}function Ct(e){var t=[];return e.forEach(function(r){var n=[];n[1]=[{data:we(r.id),type:0}],n[2]=[],r.merge!=null&&(n[3]=[{data:we(+!!r.merge),type:0}]);var a=[];r.messages.forEach(function(s){a.push(s.data),s.meta[3]=[{type:0,data:we(s.data.length)}],n[2].push({data:$e(s.meta),type:2})});var i=$e(n);t.push(we(i.length)),t.push(i),a.forEach(function(s){return t.push(s)})}),at(t)}function rx(e,t){if(e!=0)throw new Error("Unexpected Snappy chunk type ".concat(e));for(var r=[0],n=nn(t,r),a=[];r[0]<t.length;){var i=t[r[0]]&3;if(i==0){var s=t[r[0]++]>>2;if(s<60)++s;else{var f=s-59;s=t[r[0]],f>1&&(s|=t[r[0]+1]<<8),f>2&&(s|=t[r[0]+2]<<16),f>3&&(s|=t[r[0]+3]<<24),s>>>=0,s++,r[0]+=f}a.push(t.slice(r[0],r[0]+s)),r[0]+=s;continue}else{var o=0,l=0;if(i==1?(l=(t[r[0]]>>2&7)+4,o=(t[r[0]++]&224)<<3,o|=t[r[0]++]):(l=(t[r[0]++]>>2)+1,i==2?(o=t[r[0]]|t[r[0]+1]<<8,r[0]+=2):(o=(t[r[0]]|t[r[0]+1]<<8|t[r[0]+2]<<16|t[r[0]+3]<<24)>>>0,r[0]+=4)),a=[at(a)],o==0)throw new Error("Invalid offset 0");if(o>a[0].length)throw new Error("Invalid offset beyond length");if(l>=o)for(a.push(a[0].slice(-o)),l-=o;l>=a[a.length-1].length;)a.push(a[a.length-1]),l-=a[a.length-1].length;a.push(a[0].slice(-o,-o+l))}}var c=at(a);if(c.length!=n)throw new Error("Unexpected length: ".concat(c.length," != ").concat(n));return c}function Or(e){for(var t=[],r=0;r<e.length;){var n=e[r++],a=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,t.push(rx(n,e.slice(r,r+a))),r+=a}if(r!==e.length)throw new Error("data is not a valid framed stream!");return at(t)}function kt(e){for(var t=[],r=0;r<e.length;){var n=Math.min(e.length-r,268435455),a=new Uint8Array(4);t.push(a);var i=we(n),s=i.length;t.push(i),n<=60?(s++,t.push(new Uint8Array([n-1<<2]))):n<=256?(s+=2,t.push(new Uint8Array([240,n-1&255]))):n<=65536?(s+=3,t.push(new Uint8Array([244,n-1&255,n-1>>8&255]))):n<=16777216?(s+=4,t.push(new Uint8Array([248,n-1&255,n-1>>8&255,n-1>>16&255]))):n<=4294967296&&(s+=5,t.push(new Uint8Array([252,n-1&255,n-1>>8&255,n-1>>16&255,n-1>>>24&255]))),t.push(e.slice(r,r+n)),s+=n,a[0]=0,a[1]=s&255,a[2]=s>>8&255,a[3]=s>>16&255,r+=n}return at(t)}function ca(e,t){var r=new Uint8Array(32),n=Bn(r),a=12,i=0;switch(r[0]=5,e.t){case"n":r[1]=2,ex(r,a,e.v),i|=1,a+=16;break;case"b":r[1]=6,n.setFloat64(a,e.v?1:0,!0),i|=2,a+=8;break;case"s":if(t.indexOf(e.v)==-1)throw new Error("Value ".concat(e.v," missing from SST!"));r[1]=3,n.setUint32(a,t.indexOf(e.v),!0),i|=8,a+=4;break;default:throw"unsupported cell type "+e.t}return n.setUint32(8,i,!0),r.slice(0,a)}function ha(e,t){var r=new Uint8Array(32),n=Bn(r),a=12,i=0;switch(r[0]=3,e.t){case"n":r[2]=2,n.setFloat64(a,e.v,!0),i|=32,a+=8;break;case"b":r[2]=6,n.setFloat64(a,e.v?1:0,!0),i|=32,a+=8;break;case"s":if(t.indexOf(e.v)==-1)throw new Error("Value ".concat(e.v," missing from SST!"));r[2]=3,n.setUint32(a,t.indexOf(e.v),!0),i|=16,a+=4;break;default:throw"unsupported cell type "+e.t}return n.setUint32(4,i,!0),r.slice(0,a)}function Qr(e){var t=We(e);return nn(t[1][0].data)}function tx(e,t,r){var n,a,i,s;if(!((n=e[6])!=null&&n[0])||!((a=e[7])!=null&&a[0]))throw"Mutation only works on post-BNC storages!";var f=((s=(i=e[8])==null?void 0:i[0])==null?void 0:s.data)&&Pt(e[8][0].data)>0||!1;if(f)throw"Math only works with normal offsets";for(var o=0,l=Bn(e[7][0].data),c=0,m=[],d=Bn(e[4][0].data),x=0,v=[],u=0;u<t.length;++u){if(t[u]==null){l.setUint16(u*2,65535,!0),d.setUint16(u*2,65535);continue}l.setUint16(u*2,c,!0),d.setUint16(u*2,x,!0);var _,R;switch(typeof t[u]){case"string":_=ca({t:"s",v:t[u]},r),R=ha({t:"s",v:t[u]},r);break;case"number":_=ca({t:"n",v:t[u]},r),R=ha({t:"n",v:t[u]},r);break;case"boolean":_=ca({t:"b",v:t[u]},r),R=ha({t:"b",v:t[u]},r);break;default:throw new Error("Unsupported value "+t[u])}m.push(_),c+=_.length,v.push(R),x+=R.length,++o}for(e[2][0].data=we(o);u<e[7][0].data.length/2;++u)l.setUint16(u*2,65535,!0),d.setUint16(u*2,65535,!0);return e[6][0].data=at(m),e[3][0].data=at(v),o}function nx(e,t){if(!t||!t.numbers)throw new Error("Must pass a `numbers` option -- check the README");var r=e.Sheets[e.SheetNames[0]];e.SheetNames.length>1;var n=gr(r["!ref"]);n.s.r=n.s.c=0;var a=!1;n.e.c>9&&(a=!0,n.e.c=9),n.e.r>49&&(a=!0,n.e.r=49);var i=bn(r,{range:n,header:1}),s=["~Sh33tJ5~"];i.forEach(function(b){return b.forEach(function(C){typeof C=="string"&&s.push(C)})});var f={},o=[],l=Ce.read(t.numbers,{type:"base64"});l.FileIndex.map(function(b,C){return[b,l.FullPaths[C]]}).forEach(function(b){var C=b[0],F=b[1];if(C.type==2&&C.name.match(/\.iwa/)){var K=C.content,le=Or(K),ce=kr(le);ce.forEach(function(fe){o.push(fe.id),f[fe.id]={deps:[],location:F,type:Pt(fe.messages[0].meta[1][0].data)}})}}),o.sort(function(b,C){return b-C});var c=o.filter(function(b){return b>1}).map(function(b){return[b,we(b)]});l.FileIndex.map(function(b,C){return[b,l.FullPaths[C]]}).forEach(function(b){var C=b[0];if(b[1],!!C.name.match(/\.iwa/)){var F=kr(Or(C.content));F.forEach(function(K){K.messages.forEach(function(le){c.forEach(function(ce){K.messages.some(function(fe){return Pt(fe.meta[1][0].data)!=11006&&Q1(fe.data,ce[1])})&&f[ce[0]].deps.push(K.id)})})})}});for(var m=Ce.find(l,f[1].location),d=kr(Or(m.content)),x,v=0;v<d.length;++v){var u=d[v];u.id==1&&(x=u)}var _=Qr(We(x.messages[0].data)[1][0].data);for(m=Ce.find(l,f[_].location),d=kr(Or(m.content)),v=0;v<d.length;++v)u=d[v],u.id==_&&(x=u);for(_=Qr(We(x.messages[0].data)[2][0].data),m=Ce.find(l,f[_].location),d=kr(Or(m.content)),v=0;v<d.length;++v)u=d[v],u.id==_&&(x=u);for(_=Qr(We(x.messages[0].data)[2][0].data),m=Ce.find(l,f[_].location),d=kr(Or(m.content)),v=0;v<d.length;++v)u=d[v],u.id==_&&(x=u);var R=We(x.messages[0].data);{R[6][0].data=we(n.e.r+1),R[7][0].data=we(n.e.c+1);var P=Qr(R[46][0].data),O=Ce.find(l,f[P].location),j=kr(Or(O.content));{for(var Q=0;Q<j.length&&j[Q].id!=P;++Q);if(j[Q].id!=P)throw"Bad ColumnRowUIDMapArchive";var ne=We(j[Q].messages[0].data);ne[1]=[],ne[2]=[],ne[3]=[];for(var k=0;k<=n.e.c;++k){var z=[];z[1]=z[2]=[{type:0,data:we(k+420690)}],ne[1].push({type:2,data:$e(z)}),ne[2].push({type:0,data:we(k)}),ne[3].push({type:0,data:we(k)})}ne[4]=[],ne[5]=[],ne[6]=[];for(var W=0;W<=n.e.r;++W)z=[],z[1]=z[2]=[{type:0,data:we(W+726270)}],ne[4].push({type:2,data:$e(z)}),ne[5].push({type:0,data:we(W)}),ne[6].push({type:0,data:we(W)});j[Q].messages[0].data=$e(ne)}O.content=kt(Ct(j)),O.size=O.content.length,delete R[46];var X=We(R[4][0].data);{X[7][0].data=we(n.e.r+1);var J=We(X[1][0].data),Z=Qr(J[2][0].data);O=Ce.find(l,f[Z].location),j=kr(Or(O.content));{if(j[0].id!=Z)throw"Bad HeaderStorageBucket";var ae=We(j[0].messages[0].data);for(W=0;W<i.length;++W){var pe=We(ae[2][0].data);pe[1][0].data=we(W),pe[4][0].data=we(i[W].length),ae[2][W]={type:ae[2][0].type,data:$e(pe)}}j[0].messages[0].data=$e(ae)}O.content=kt(Ct(j)),O.size=O.content.length;var ue=Qr(X[2][0].data);O=Ce.find(l,f[ue].location),j=kr(Or(O.content));{if(j[0].id!=ue)throw"Bad HeaderStorageBucket";for(ae=We(j[0].messages[0].data),k=0;k<=n.e.c;++k)pe=We(ae[2][0].data),pe[1][0].data=we(k),pe[4][0].data=we(n.e.r+1),ae[2][k]={type:ae[2][0].type,data:$e(pe)};j[0].messages[0].data=$e(ae)}O.content=kt(Ct(j)),O.size=O.content.length;var Me=Qr(X[4][0].data);(function(){for(var b=Ce.find(l,f[Me].location),C=kr(Or(b.content)),F,K=0;K<C.length;++K){var le=C[K];le.id==Me&&(F=le)}var ce=We(F.messages[0].data);{ce[3]=[];var fe=[];s.forEach(function(de,Qe){fe[1]=[{type:0,data:we(Qe)}],fe[2]=[{type:0,data:we(1)}],fe[3]=[{type:2,data:Z1(de)}],ce[3].push({type:2,data:$e(fe)})})}F.messages[0].data=$e(ce);var ie=Ct(C),ge=kt(ie);b.content=ge,b.size=b.content.length})();var Ne=We(X[3][0].data);{var ur=Ne[1][0];delete Ne[2];var Be=We(ur.data);{var ze=Qr(Be[2][0].data);(function(){for(var b=Ce.find(l,f[ze].location),C=kr(Or(b.content)),F,K=0;K<C.length;++K){var le=C[K];le.id==ze&&(F=le)}var ce=We(F.messages[0].data);{delete ce[6],delete Ne[7];var fe=new Uint8Array(ce[5][0].data);ce[5]=[];for(var ie=0,ge=0;ge<=n.e.r;++ge){var de=We(fe);ie+=tx(de,i[ge],s),de[1][0].data=we(ge),ce[5].push({data:$e(de),type:2})}ce[1]=[{type:0,data:we(n.e.c+1)}],ce[2]=[{type:0,data:we(n.e.r+1)}],ce[3]=[{type:0,data:we(ie)}],ce[4]=[{type:0,data:we(n.e.r+1)}]}F.messages[0].data=$e(ce);var Qe=Ct(C),Te=kt(Qe);b.content=Te,b.size=b.content.length})()}ur.data=$e(Be)}X[3][0].data=$e(Ne)}R[4][0].data=$e(X)}x.messages[0].data=$e(R);var Ye=Ct(d),y=kt(Ye);return m.content=y,m.size=m.content.length,l}function ax(e){return function(r){for(var n=0;n!=e.length;++n){var a=e[n];r[a[0]]===void 0&&(r[a[0]]=a[1]),a[2]==="n"&&(r[a[0]]=Number(r[a[0]]))}}}function Ba(e){ax([["cellDates",!1],["bookSST",!1],["bookType","xlsx"],["compression",!1],["WTF",!1]])(e)}function ix(e,t){return t.bookType=="ods"?ls(e,t):t.bookType=="numbers"?nx(e,t):t.bookType=="xlsb"?sx(e,t):fx(e,t)}function sx(e,t){It=1024,e&&!e.SSF&&(e.SSF=hr(Le)),e&&e.SSF&&(jn(),Wn(e.SSF),t.revssf=Hn(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,qt?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r=t.bookType=="xlsb"?"bin":"xml",n=V0.indexOf(t.bookType)>-1,a=_0();Ba(t=t||{});var i=Ea(),s="",f=0;if(t.cellXfs=[],it(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),s="docProps/core.xml",me(i,s,S0(e.Props,t)),a.coreprops.push(s),Se(t.rels,2,s,_e.CORE_PROPS),s="docProps/app.xml",!(e.Props&&e.Props.SheetNames))if(!e.Workbook||!e.Workbook.Sheets)e.Props.SheetNames=e.SheetNames;else{for(var o=[],l=0;l<e.SheetNames.length;++l)(e.Workbook.Sheets[l]||{}).Hidden!=2&&o.push(e.SheetNames[l]);e.Props.SheetNames=o}for(e.Props.Worksheets=e.Props.SheetNames.length,me(i,s,A0(e.Props)),a.extprops.push(s),Se(t.rels,3,s,_e.EXT_PROPS),e.Custprops!==e.Props&&Ze(e.Custprops||{}).length>0&&(s="docProps/custom.xml",me(i,s,F0(e.Custprops)),a.custprops.push(s),Se(t.rels,4,s,_e.CUST_PROPS)),f=1;f<=e.SheetNames.length;++f){var c={"!id":{}},m=e.Sheets[e.SheetNames[f-1]],d=(m||{})["!type"]||"sheet";switch(d){case"chart":default:s="xl/worksheets/sheet"+f+"."+r,me(i,s,o1(f-1,s,t,e,c)),a.sheets.push(s),Se(t.wbrels,-1,"worksheets/sheet"+f+"."+r,_e.WS[0])}if(m){var x=m["!comments"],v=!1,u="";x&&x.length>0&&(u="xl/comments"+f+"."+r,me(i,u,u1(x,u)),a.comments.push(u),Se(c,-1,"../comments"+f+"."+r,_e.CMNT),v=!0),m["!legacy"]&&v&&me(i,"xl/drawings/vmlDrawing"+f+".vml",H0(f,m["!comments"])),delete m["!comments"],delete m["!legacy"]}c["!id"].rId1&&me(i,E0(s),Nt(c))}return t.Strings!=null&&t.Strings.length>0&&(s="xl/sharedStrings."+r,me(i,s,h1(t.Strings,s,t)),a.strs.push(s),Se(t.wbrels,-1,"sharedStrings."+r,_e.SST)),s="xl/workbook."+r,me(i,s,l1(e,s)),a.workbooks.push(s),Se(t.rels,1,s,_e.WB),s="xl/theme/theme1.xml",me(i,s,W0(e.Themes,t)),a.themes.push(s),Se(t.wbrels,-1,"theme/theme1.xml",_e.THEME),s="xl/styles."+r,me(i,s,c1(e,s,t)),a.styles.push(s),Se(t.wbrels,-1,"styles."+r,_e.STY),e.vbaraw&&n&&(s="xl/vbaProject.bin",me(i,s,e.vbaraw),a.vba.push(s),Se(t.wbrels,-1,"vbaProject.bin",_e.VBA)),s="xl/metadata."+r,me(i,s,x1(s)),a.metadata.push(s),Se(t.wbrels,-1,"metadata."+r,_e.XLMETA),me(i,"[Content_Types].xml",T0(a,t)),me(i,"_rels/.rels",Nt(t.rels)),me(i,"xl/_rels/workbook."+r+".rels",Nt(t.wbrels)),delete t.revssf,delete t.ssf,i}function fx(e,t){It=1024,e&&!e.SSF&&(e.SSF=hr(Le)),e&&e.SSF&&(jn(),Wn(e.SSF),t.revssf=Hn(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,qt?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r="xml",n=V0.indexOf(t.bookType)>-1,a=_0();Ba(t=t||{});var i=Ea(),s="",f=0;if(t.cellXfs=[],it(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),s="docProps/core.xml",me(i,s,S0(e.Props,t)),a.coreprops.push(s),Se(t.rels,2,s,_e.CORE_PROPS),s="docProps/app.xml",!(e.Props&&e.Props.SheetNames))if(!e.Workbook||!e.Workbook.Sheets)e.Props.SheetNames=e.SheetNames;else{for(var o=[],l=0;l<e.SheetNames.length;++l)(e.Workbook.Sheets[l]||{}).Hidden!=2&&o.push(e.SheetNames[l]);e.Props.SheetNames=o}e.Props.Worksheets=e.Props.SheetNames.length,me(i,s,A0(e.Props)),a.extprops.push(s),Se(t.rels,3,s,_e.EXT_PROPS),e.Custprops!==e.Props&&Ze(e.Custprops||{}).length>0&&(s="docProps/custom.xml",me(i,s,F0(e.Custprops)),a.custprops.push(s),Se(t.rels,4,s,_e.CUST_PROPS));var c=["SheetJ5"];for(t.tcid=0,f=1;f<=e.SheetNames.length;++f){var m={"!id":{}},d=e.Sheets[e.SheetNames[f-1]],x=(d||{})["!type"]||"sheet";switch(x){case"chart":default:s="xl/worksheets/sheet"+f+"."+r,me(i,s,Q0(f-1,t,e,m)),a.sheets.push(s),Se(t.wbrels,-1,"worksheets/sheet"+f+"."+r,_e.WS[0])}if(d){var v=d["!comments"],u=!1,_="";if(v&&v.length>0){var R=!1;v.forEach(function(P){P[1].forEach(function(O){O.T==!0&&(R=!0)})}),R&&(_="xl/threadedComments/threadedComment"+f+"."+r,me(i,_,bo(v,c,t)),a.threadedcomments.push(_),Se(m,-1,"../threadedComments/threadedComment"+f+"."+r,_e.TCMNT)),_="xl/comments"+f+"."+r,me(i,_,G0(v)),a.comments.push(_),Se(m,-1,"../comments"+f+"."+r,_e.CMNT),u=!0}d["!legacy"]&&u&&me(i,"xl/drawings/vmlDrawing"+f+".vml",H0(f,d["!comments"])),delete d["!comments"],delete d["!legacy"]}m["!id"].rId1&&me(i,E0(s),Nt(m))}return t.Strings!=null&&t.Strings.length>0&&(s="xl/sharedStrings."+r,me(i,s,P0(t.Strings,t)),a.strs.push(s),Se(t.wbrels,-1,"sharedStrings."+r,_e.SST)),s="xl/workbook."+r,me(i,s,ts(e)),a.workbooks.push(s),Se(t.rels,1,s,_e.WB),s="xl/theme/theme1.xml",me(i,s,W0(e.Themes,t)),a.themes.push(s),Se(t.wbrels,-1,"theme/theme1.xml",_e.THEME),s="xl/styles."+r,me(i,s,b0(e,t)),a.styles.push(s),Se(t.wbrels,-1,"styles."+r,_e.STY),e.vbaraw&&n&&(s="xl/vbaProject.bin",me(i,s,e.vbaraw),a.vba.push(s),Se(t.wbrels,-1,"vbaProject.bin",_e.VBA)),s="xl/metadata."+r,me(i,s,j0()),a.metadata.push(s),Se(t.wbrels,-1,"metadata."+r,_e.XLMETA),c.length>1&&(s="xl/persons/person.xml",me(i,s,Uo(c)),a.people.push(s),Se(t.wbrels,-1,"persons/person.xml",_e.PEOPLE)),me(i,"[Content_Types].xml",T0(a,t)),me(i,"_rels/.rels",Nt(t.rels)),me(i,"xl/_rels/workbook."+r+".rels",Nt(t.wbrels)),delete t.revssf,delete t.ssf,i}function lx(e,t){var r="";switch((t||{}).type||"base64"){case"buffer":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=zr(e.slice(0,12));break;case"binary":r=e;break;case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];default:throw new Error("Unrecognized type "+(t&&t.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}function os(e,t){switch(t.type){case"base64":case"binary":break;case"buffer":case"array":t.type="";break;case"file":return fn(t.file,Ce.write(e,{type:Ee?"buffer":""}));case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");default:throw new Error("Unrecognized type "+t.type)}return Ce.write(e,t)}function ox(e,t){var r=hr(t||{}),n=ix(e,r);return cx(n,r)}function cx(e,t){var r={},n=Ee?"nodebuffer":typeof Uint8Array!="undefined"?"array":"string";if(t.compression&&(r.compression="DEFLATE"),t.password)r.type=n;else switch(t.type){case"base64":r.type="base64";break;case"binary":r.type="string";break;case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");case"buffer":case"file":r.type=n;break;default:throw new Error("Unrecognized type "+t.type)}var a=e.FullPaths?Ce.write(e,{fileType:"zip",type:{nodebuffer:"buffer",string:"binary"}[r.type]||r.type,compression:!!t.compression}):e.generate(r);if(typeof Deno!="undefined"&&typeof a=="string"){if(t.type=="binary"||t.type=="base64")return a;a=new Uint8Array(Un(a))}return t.password&&typeof encrypt_agile!="undefined"?os(encrypt_agile(a,t.password),t):t.type==="file"?fn(t.file,a):t.type=="string"?$t(a):a}function hx(e,t){var r=t||{},n=C1(e,r);return os(n,r)}function Mr(e,t,r){r||(r="");var n=r+e;switch(t.type){case"base64":return Qt(rn(n));case"binary":return rn(n);case"string":return e;case"file":return fn(t.file,n,"utf8");case"buffer":return Ee?$r(n,"utf8"):typeof TextEncoder!="undefined"?new TextEncoder().encode(n):Mr(n,{type:"binary"}).split("").map(function(a){return a.charCodeAt(0)})}throw new Error("Unrecognized type "+t.type)}function ux(e,t){switch(t.type){case"base64":return Qt(e);case"binary":return e;case"string":return e;case"file":return fn(t.file,e,"binary");case"buffer":return Ee?$r(e,"binary"):e.split("").map(function(r){return r.charCodeAt(0)})}throw new Error("Unrecognized type "+t.type)}function yn(e,t){switch(t.type){case"string":case"base64":case"binary":for(var r="",n=0;n<e.length;++n)r+=String.fromCharCode(e[n]);return t.type=="base64"?Qt(r):t.type=="string"?$t(r):r;case"file":return fn(t.file,e);case"buffer":return e;default:throw new Error("Unrecognized type "+t.type)}}function cs(e,t){bs(),qu(e);var r=hr(t||{});if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),r.type=="array"){r.type="binary";var n=cs(e,r);return r.type="array",Un(n)}var a=0;if(r.sheet&&(typeof r.sheet=="number"?a=r.sheet:a=e.SheetNames.indexOf(r.sheet),!e.SheetNames[a]))throw new Error("Sheet not found: "+r.sheet+" : "+typeof r.sheet);switch(r.bookType||"xlsb"){case"xml":case"xlml":return Mr(A1(e,r),r);case"slk":case"sylk":return Mr($l.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"htm":case"html":return Mr(is(e.Sheets[e.SheetNames[a]],r),r);case"txt":return ux(hs(e.Sheets[e.SheetNames[a]],r),r);case"csv":return Mr(ba(e.Sheets[e.SheetNames[a]],r),r,"\uFEFF");case"dif":return Mr(Xl.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"dbf":return yn(Yl.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"prn":return Mr(Kl.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"rtf":return Mr(to.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"eth":return Mr(R0.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"fods":return Mr(ls(e,r),r);case"wk1":return yn(Ai.sheet_to_wk1(e.Sheets[e.SheetNames[a]],r),r);case"wk3":return yn(Ai.book_to_wk3(e,r),r);case"biff2":r.biff||(r.biff=2);case"biff3":r.biff||(r.biff=3);case"biff4":return r.biff||(r.biff=4),yn(as(e,r),r);case"biff5":r.biff||(r.biff=5);case"biff8":case"xla":case"xls":return r.biff||(r.biff=8),hx(e,r);case"xlsx":case"xlsm":case"xlam":case"xlsb":case"numbers":case"ods":return ox(e,r);default:throw new Error("Unrecognized bookType |"+r.bookType+"|")}}function xx(e){if(!e.bookType){var t={xls:"biff8",htm:"html",slk:"sylk",socialcalc:"eth",Sh33tJS:"WTF"},r=e.file.slice(e.file.lastIndexOf(".")).toLowerCase();r.match(/^\.[a-z]+$/)&&(e.bookType=r.slice(1)),e.bookType=t[e.bookType]||e.bookType}}function dx(e,t,r){var n={};return n.type="file",n.file=t,xx(n),cs(e,n)}function mx(e,t,r,n,a,i,s,f){var o=qe(r),l=f.defval,c=f.raw||!Object.prototype.hasOwnProperty.call(f,"raw"),m=!0,d=a===1?[]:{};if(a!==1)if(Object.defineProperty)try{Object.defineProperty(d,"__rowNum__",{value:r,enumerable:!1})}catch(_){d.__rowNum__=r}else d.__rowNum__=r;if(!s||e[r])for(var x=t.s.c;x<=t.e.c;++x){var v=s?e[r][x]:e[n[x]+o];if(v===void 0||v.t===void 0){if(l===void 0)continue;i[x]!=null&&(d[i[x]]=l);continue}var u=v.v;switch(v.t){case"z":if(u==null)break;continue;case"e":u=u==0?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw new Error("unrecognized type "+v.t)}if(i[x]!=null){if(u==null)if(v.t=="e"&&u===null)d[i[x]]=null;else if(l!==void 0)d[i[x]]=l;else if(c&&u===null)d[i[x]]=null;else continue;else d[i[x]]=c&&(v.t!=="n"||v.t==="n"&&f.rawNumbers!==!1)?u:Yr(v,u,f);u!=null&&(m=!1)}}return{row:d,isempty:m}}function bn(e,t){if(e==null||e["!ref"]==null)return[];var r={t:"n",v:0},n=0,a=1,i=[],s=0,f="",o={s:{r:0,c:0},e:{r:0,c:0}},l=t||{},c=l.range!=null?l.range:e["!ref"];switch(l.header===1?n=1:l.header==="A"?n=2:Array.isArray(l.header)?n=3:l.header==null&&(n=0),typeof c){case"string":o=Ie(c);break;case"number":o=Ie(e["!ref"]),o.s.r=c;break;default:o=c}n>0&&(a=0);var m=qe(o.s.r),d=[],x=[],v=0,u=0,_=Array.isArray(e),R=o.s.r,P=0,O={};_&&!e[R]&&(e[R]=[]);var j=l.skipHidden&&e["!cols"]||[],Q=l.skipHidden&&e["!rows"]||[];for(P=o.s.c;P<=o.e.c;++P)if(!(j[P]||{}).hidden)switch(d[P]=tr(P),r=_?e[R][P]:e[d[P]+m],n){case 1:i[P]=P-o.s.c;break;case 2:i[P]=d[P];break;case 3:i[P]=l.header[P-o.s.c];break;default:if(r==null&&(r={w:"__EMPTY",t:"s"}),f=s=Yr(r,null,l),u=O[s]||0,!u)O[s]=1;else{do f=s+"_"+u++;while(O[f]);O[s]=u,O[f]=1}i[P]=f}for(R=o.s.r+a;R<=o.e.r;++R)if(!(Q[R]||{}).hidden){var ne=mx(e,o,R,d,n,i,_,l);(ne.isempty===!1||(n===1?l.blankrows!==!1:l.blankrows))&&(x[v++]=ne.row)}return x.length=v,x}var Ri=/"/g;function px(e,t,r,n,a,i,s,f){for(var o=!0,l=[],c="",m=qe(r),d=t.s.c;d<=t.e.c;++d)if(n[d]){var x=f.dense?(e[r]||[])[d]:e[n[d]+m];if(x==null)c="";else if(x.v!=null){o=!1,c=""+(f.rawNumbers&&x.t=="n"?x.v:Yr(x,null,f));for(var v=0,u=0;v!==c.length;++v)if((u=c.charCodeAt(v))===a||u===i||u===34||f.forceQuotes){c='"'+c.replace(Ri,'""')+'"';break}c=="ID"&&(c='"ID"')}else x.f!=null&&!x.F?(o=!1,c="="+x.f,c.indexOf(",")>=0&&(c='"'+c.replace(Ri,'""')+'"')):c="";l.push(c)}return f.blankrows===!1&&o?null:l.join(s)}function ba(e,t){var r=[],n=t==null?{}:t;if(e==null||e["!ref"]==null)return"";var a=Ie(e["!ref"]),i=n.FS!==void 0?n.FS:",",s=i.charCodeAt(0),f=n.RS!==void 0?n.RS:`
`,o=f.charCodeAt(0),l=new RegExp((i=="|"?"\\|":i)+"+$"),c="",m=[];n.dense=Array.isArray(e);for(var d=n.skipHidden&&e["!cols"]||[],x=n.skipHidden&&e["!rows"]||[],v=a.s.c;v<=a.e.c;++v)(d[v]||{}).hidden||(m[v]=tr(v));for(var u=0,_=a.s.r;_<=a.e.r;++_)(x[_]||{}).hidden||(c=px(e,a,_,m,s,o,i,n),c!=null&&(n.strip&&(c=c.replace(l,"")),(c||n.blankrows!==!1)&&r.push((u++?f:"")+c)));return delete n.dense,r.join("")}function hs(e,t){t||(t={}),t.FS="	",t.RS=`
`;var r=ba(e,t);return r}function gx(e){var t="",r,n="";if(e==null||e["!ref"]==null)return[];var a=Ie(e["!ref"]),i="",s=[],f,o=[],l=Array.isArray(e);for(f=a.s.c;f<=a.e.c;++f)s[f]=tr(f);for(var c=a.s.r;c<=a.e.r;++c)for(i=qe(c),f=a.s.c;f<=a.e.c;++f)if(t=s[f]+i,r=l?(e[c]||[])[f]:e[t],n="",r!==void 0){if(r.F!=null){if(t=r.F,!r.f)continue;n=r.f,t.indexOf(":")==-1&&(t=t+":"+t)}if(r.f!=null)n=r.f;else{if(r.t=="z")continue;if(r.t=="n"&&r.v!=null)n=""+r.v;else if(r.t=="b")n=r.v?"TRUE":"FALSE";else if(r.w!==void 0)n="'"+r.w;else{if(r.v===void 0)continue;r.t=="s"?n="'"+r.v:n=""+r.v}}o[o.length]=t+"="+n}return o}function us(e,t,r){var n=r||{},a=+!n.skipHeader,i=e||{},s=0,f=0;if(i&&n.origin!=null)if(typeof n.origin=="number")s=n.origin;else{var o=typeof n.origin=="string"?Ge(n.origin):n.origin;s=o.r,f=o.c}var l,c={s:{c:0,r:0},e:{c:f,r:s+t.length-1+a}};if(i["!ref"]){var m=Ie(i["!ref"]);c.e.c=Math.max(c.e.c,m.e.c),c.e.r=Math.max(c.e.r,m.e.r),s==-1&&(s=m.e.r+1,c.e.r=s+t.length-1+a)}else s==-1&&(s=0,c.e.r=t.length-1+a);var d=n.header||[],x=0;t.forEach(function(u,_){Ze(u).forEach(function(R){(x=d.indexOf(R))==-1&&(d[x=d.length]=R);var P=u[R],O="z",j="",Q=Ae({c:f+x,r:s+_+a});l=an(i,Q),P&&typeof P=="object"&&!(P instanceof Date)?i[Q]=P:(typeof P=="number"?O="n":typeof P=="boolean"?O="b":typeof P=="string"?O="s":P instanceof Date?(O="d",n.cellDates||(O="n",P=cr(P)),j=n.dateNF||Le[14]):P===null&&n.nullError&&(O="e",P=0),l?(l.t=O,l.v=P,delete l.w,delete l.R,j&&(l.z=j)):i[Q]=l={t:O,v:P},j&&(l.z=j))})}),c.e.c=Math.max(c.e.c,f+d.length-1);var v=qe(s);if(a)for(x=0;x<d.length;++x)i[tr(x+f)+v]={t:"s",v:d[x]};return i["!ref"]=je(c),i}function vx(e,t){return us(null,e,t)}function an(e,t,r){if(typeof t=="string"){if(Array.isArray(e)){var n=Ge(t);return e[n.r]||(e[n.r]=[]),e[n.r][n.c]||(e[n.r][n.c]={t:"z"})}return e[t]||(e[t]={t:"z"})}return typeof t!="number"?an(e,Ae(t)):an(e,Ae({r:t,c:r||0}))}function _x(e,t){if(typeof t=="number"){if(t>=0&&e.SheetNames.length>t)return t;throw new Error("Cannot find sheet # "+t)}else if(typeof t=="string"){var r=e.SheetNames.indexOf(t);if(r>-1)return r;throw new Error("Cannot find sheet name |"+t+"|")}else throw new Error("Cannot find sheet |"+t+"|")}function Tx(){return{SheetNames:[],Sheets:{}}}function Ex(e,t,r,n){var a=1;if(!r)for(;a<=65535&&e.SheetNames.indexOf(r="Sheet"+a)!=-1;++a,r=void 0);if(!r||e.SheetNames.length>=65535)throw new Error("Too many worksheets");if(n&&e.SheetNames.indexOf(r)>=0){var i=r.match(/(^.*?)(\d+)$/);a=i&&+i[2]||0;var s=i&&i[1]||r;for(++a;a<=65535&&e.SheetNames.indexOf(r=s+a)!=-1;++a);}if(rs(r),e.SheetNames.indexOf(r)>=0)throw new Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=t,r}function wx(e,t,r){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var n=_x(e,t);switch(e.Workbook.Sheets[n]||(e.Workbook.Sheets[n]={}),r){case 0:case 1:case 2:break;default:throw new Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[n].Hidden=r}function Sx(e,t){return e.z=t,e}function xs(e,t,r){return t?(e.l={Target:t},r&&(e.l.Tooltip=r)):delete e.l,e}function yx(e,t,r){return xs(e,"#"+t,r)}function Ax(e,t,r){e.c||(e.c=[]),e.c.push({t,a:r||"SheetJS"})}function Fx(e,t,r,n){for(var a=typeof t!="string"?t:Ie(t),i=typeof t=="string"?t:je(t),s=a.s.r;s<=a.e.r;++s)for(var f=a.s.c;f<=a.e.c;++f){var o=an(e,s,f);o.t="n",o.F=i,delete o.v,s==a.s.r&&f==a.s.c&&(o.f=r,n&&(o.D=!0))}return e}var ua={encode_col:tr,encode_row:qe,encode_cell:Ae,encode_range:je,decode_col:Ca,decode_row:Fa,split_cell:Uf,decode_cell:Ge,decode_range:gr,format_cell:Yr,sheet_add_aoa:x0,sheet_add_json:us,sheet_add_dom:ss,aoa_to_sheet:Bt,json_to_sheet:vx,table_to_sheet:fs,table_to_book:K1,sheet_to_csv:ba,sheet_to_txt:hs,sheet_to_json:bn,sheet_to_html:is,sheet_to_formulae:gx,sheet_to_row_object_array:bn,sheet_get_cell:an,book_new:Tx,book_append_sheet:Ex,book_set_sheet_visibility:wx,cell_set_number_format:Sx,cell_set_hyperlink:xs,cell_set_internal_link:yx,cell_add_comment:Ax,sheet_set_array_formula:Fx,consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}};function Cx(e,t){const[r,n]=xe.useState(null),[a,i]=xe.useState(!0),[s,f]=xe.useState(null),o=xe.useCallback(async()=>{var l;i(!0),f(null);try{const m=(await ds.get(e,{...t,headers:{...t==null?void 0:t.headers,"Cache-Control":"no-cache",Pragma:"no-cache"}})).data,d=m&&m.data!==void 0?m.data:m;n(d)}catch(c){let m="Veri yüklenirken hata oluştu";c.response?(m=`API'ye erişim sırasında hata oluştu (${c.response.status})`,(l=c.response.data)!=null&&l.error&&(m+=`: ${c.response.data.error}`)):c.request?m="API sunucusuna erişilemiyor. Lütfen internet bağlantınızı kontrol edin.":c.message&&(m=`İstek hatası: ${c.message}`),e.includes("tasks")&&(m="Veri yükleme hatası: API'ye erişim sırasında hata oluştu, görevler yüklenemedi"),f(m)}finally{i(!1)}},[e,JSON.stringify(t)]);return xe.useEffect(()=>{o()},[o]),{data:r,loading:a,error:s,refetch:o}}const kx=[{id:1,username:"yayinci1",isim_soyisim:"Ahmet Yılmaz",telefon:"5551112233",mail:"<EMAIL>",dogum_tarihi:"1995-05-10",sehir:"İstanbul",meslek:"Öğrenci",kayit_tarihi:"2024-01-15"},{id:2,username:"yayinci2",isim_soyisim:"Ayşe Demir",telefon:"5553334455",mail:"<EMAIL>",dogum_tarihi:"1990-08-22",sehir:"Ankara",meslek:"Mühendis",kayit_tarihi:"2024-01-20"},{id:3,username:"yayinci3",isim_soyisim:"Mehmet Kaya",telefon:"5556667788",mail:"<EMAIL>",dogum_tarihi:"1992-11-15",sehir:"İzmir",meslek:"Grafiker",kayit_tarihi:"2024-02-01"},{id:4,username:"yayinci4",isim_soyisim:"Zeynep Şahin",telefon:"5552223344",mail:"<EMAIL>",dogum_tarihi:"1997-03-25",sehir:"Bursa",meslek:"Öğretmen",kayit_tarihi:"2024-02-10"},{id:5,username:"yayinci5",isim_soyisim:"Can Özkan",telefon:"5554445566",mail:"<EMAIL>",dogum_tarihi:"1993-07-18",sehir:"Antalya",meslek:"Doktor",kayit_tarihi:"2024-02-15"}],Ir=!1,Ox=()=>{const e=localStorage.getItem("publisherProfileImageProgress");if(e)try{const t=JSON.parse(e);if(t&&typeof t.total=="number"&&typeof t.current=="number"&&typeof t.success=="number"&&typeof t.fail=="number"&&typeof t.running=="boolean")return t}catch(t){localStorage.removeItem("publisherProfileImageProgress")}return{total:0,current:0,success:0,fail:0,running:!1}};class xa extends xe.Component{constructor(t){super(t),this.state={hasError:!1,error:null}}static getDerivedStateFromError(t){return{hasError:!0,error:t}}componentDidCatch(t,r){}render(){var t;return this.state.hasError?this.props.fallback||D.jsxs("div",{className:"p-8 text-center",children:[D.jsx("h2",{className:"text-xl font-bold text-red-600 mb-4",children:"Sayfa yüklenirken beklenmeyen bir hata oluştu"}),D.jsxs("p",{className:"mb-4",children:["Teknik detay: ",(t=this.state.error)==null?void 0:t.message]}),D.jsx("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Sayfayı Yenile"})]}):this.props.children}}function Hx({hideHeader:e=!1,isMobileView:t=!1,onDataChange:r,darkTheme:n}){const{data:a=[],loading:i,error:s,refetch:f}=Cx(`${Cr.X_SITE_BASE_URL}${Cr.ENDPOINTS.PUBLISHERS}`),[o,l]=xe.useState(Ir),[c,m]=xe.useState(kx),[d,x]=xe.useState(i),v=Array.isArray(a)?a:[],[u,_]=xe.useState(""),[R,P]=xe.useState(!1),[O,j]=xe.useState(null),[Q]=ve.useForm(),{darkMode:ne}=ms(),[k,z]=xe.useState([]),[W,X]=xe.useState(!1),[J]=ve.useForm(),[Z,ae]=xe.useState(!1),[pe]=ve.useForm(),[ue,Me]=xe.useState([]),[Ne,ur]=xe.useState(void 0),[Be,ze]=xe.useState(!1),[Ye]=ve.useForm(),[y,b]=xe.useState([]),[C,F]=xe.useState(void 0),[K,le]=xe.useState(!1),[ce,fe]=xe.useState(!1),[ie,ge]=xe.useState(null),[de]=ve.useForm(),[Qe,Te]=xe.useState(null),[Pr,Re]=xe.useState(null),[se,ar]=xe.useState([]),[Ar,Fr]=xe.useState(!1),[Xr,un]=xe.useState(""),[Tt,vr]=xe.useState(""),[Et,Kr]=xe.useState(!1),[Wt,_r]=xe.useState(!1),[xn,dn]=xe.useState(null),[Jr,wt]=xe.useState(1),[st,mn]=xe.useState(10),[St,ft]=xe.useState(null),[yt,jt]=xe.useState(void 0),[Tr,br]=xe.useState([]),[Ur,qr]=xe.useState(Ox());xe.useEffect(()=>{s&&l(!0),x(i&&!0)},[s,i]),xe.useEffect(()=>{try{if(!Ir&&Array.isArray(v)&&v.length>0){const S=v.map(A=>({...A,isim_soyisim:A.isim_soyisim||"",username:A.username||"",mail:A.mail||"",telefon:A.telefon||"",sehir:A.sehir||"",meslek:A.meslek||"",id:A.id!==void 0?typeof A.id=="string"?parseInt(A.id,10):A.id:void 0}));br(S)}}catch(S){br([])}},[v,Ir]);const ir=Ua.useMemo(()=>{try{return o?Array.isArray(c)?c:[]:Array.isArray(Tr)?Tr:[]}catch(S){return[]}},[o,c,Tr]),$n=()=>{f()};xe.useEffect(()=>{try{const S=localStorage.getItem("publisherEmailTemplates");if(S)Me(JSON.parse(S));else{const A=[{name:"Yayıncı Bilgilendirme",subject:"Önemli Duyuru",content:"Merhaba {username}, yeni kampanyamız hakkında bilgilendirmek istiyoruz. Detaylar için lütfen bizimle iletişime geçin."},{name:"Etkinlik Daveti",subject:"Yeni Etkinlik Daveti",content:"Merhaba {username}, yaklaşan etkinliğimize sizi davet etmek istiyoruz. Katılım durumunuzu en kısa sürede bildirmenizi rica ederiz."}];Me(A),localStorage.setItem("publisherEmailTemplates",JSON.stringify(A))}}catch(S){}},[]),xe.useEffect(()=>{try{const S=localStorage.getItem("publisherWhatsappTemplates");if(S)b(JSON.parse(S));else{const A=[{name:"Yayın Hatırlatması",content:"Merhaba {username}, bugün planlanan yayınınız hakkında hatırlatma yapmak istedik. İyi yayınlar dileriz."},{name:"Performans Bilgisi",content:"Merhaba {username}, geçen haftaki performansınız hakkında bilgi vermek istiyoruz. Detayları görmek ister misiniz?"}];b(A),localStorage.setItem("publisherWhatsappTemplates",JSON.stringify(A))}}catch(S){}},[]);const lt=Ua.useMemo(()=>{try{const S=Ir?c:Tr;if(!Array.isArray(S))return[];if(!u.trim())return S;const A=u.toLowerCase();return S.filter(I=>I?(I.isim_soyisim||"").toLowerCase().includes(A)||(I.mail||"").toLowerCase().includes(A)||(I.username||"").toLowerCase().includes(A)||(I.sehir||"").toLowerCase().includes(A)||(I.meslek||"").toLowerCase().includes(A):!1)}catch(S){return[]}},[Ir,c,Tr,u]),Xn=async S=>{if(!S){De.error("Geçersiz yayıncı ID'si.");return}x(!0);try{let A;if(!Ir){let I=localStorage.getItem(jr.TOKEN_KEY)||"";if(!I)try{const he=localStorage.getItem(jr.USER_KEY);if(he){const Fe=JSON.parse(he);Fe&&Fe.token&&(I=Fe.token)}}catch(he){}if(!I){De.error("Oturum bilgileriniz bulunamadı. Lütfen tekrar giriş yapın."),setTimeout(()=>{window.location.href="/login"},2e3);return}const L=await fetch(`${Cr.X_SITE_BASE_URL}${Cr.ENDPOINTS.PUBLISHERS}?id=${S}`,{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:`Bearer ${I}`},body:JSON.stringify({id:S})});if(!L.ok){const he=await L.text();if(L.status===401){De.error("Oturumunuz sonlandı. Lütfen tekrar giriş yapın."),setTimeout(()=>{window.location.href="/login"},2e3);return}throw new Error(`Sunucu hatası: ${L.status} ${he}`)}const V=await L.json();if(!V.success)throw new Error(V.error||"Silme başarısız");A=Tr.filter(he=>he.id!==S),br(A);const te=A.length,oe=Math.ceil(te/st)||1;(Jr-1)*st>=te&&Jr>1&&wt(Jr-1),r&&r(A.length)}De.success("Yayıncı başarıyla silindi")}catch(A){De.error("Yayıncı silinirken bir hata oluştu: "+((A==null?void 0:A.message)||""))}x(!1)},Kn=async S=>{try{x(!0);const A={...S};if(A.isim_soyisim=A.isim_soyisim||"Belirtilmedi",A.username=A.username||"user_"+Date.now(),A.telefon=A.telefon||"0000000000",A.mail=A.mail||"<EMAIL>",A.dogum_tarihi=A.dogum_tarihi?At(A.dogum_tarihi).format("YYYY-MM-DD"):At().format("YYYY-MM-DD"),A.sehir=A.sehir||"Belirtilmedi",A.meslek=A.meslek||"Belirtilmedi",!Ir){let I=localStorage.getItem(jr.TOKEN_KEY)||"";if(!I)try{const L=localStorage.getItem(jr.USER_KEY);if(L){const V=JSON.parse(L);V&&V.token&&(I=V.token)}}catch(L){}if(!I){De.error("Oturum bilgileriniz bulunamadı. Lütfen tekrar giriş yapın."),setTimeout(()=>{window.location.href="/login"},2e3);return}try{const L=`${Cr.X_SITE_BASE_URL}${Cr.ENDPOINTS.PUBLISHERS}`;if(O!=null&&O.id){const V=await fetch(L,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${I}`},body:JSON.stringify({...A,id:O.id})}),te=await V.text();if(!V.ok){if(V.status===401){De.error("Oturumunuz sonlandı. Lütfen tekrar giriş yapın."),setTimeout(()=>{window.location.href="/login"},2e3);return}throw new Error(`Sunucu hatası: ${V.status} - ${te}`)}try{const oe=JSON.parse(te);if(!oe.success)throw new Error(oe.error||"Güncelleme başarısız")}catch(oe){if(!V.ok)throw new Error("Sunucu yanıtı geçersiz format içeriyor")}De.success("Yayıncı başarıyla güncellendi")}else{let V=1;if(Array.isArray(ir)&&ir.length>0){const he=ir.map(Fe=>Fe.id).filter(Fe=>Fe!=null).map(Fe=>typeof Fe=="string"?parseInt(Fe,10):Fe);he.length>0&&(V=Math.max(...he)+1)}A.id=V;const te=await fetch(L,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${I}`},body:JSON.stringify(A)}),oe=await te.text();if(!te.ok){if(te.status===401){De.error("Oturumunuz sonlandı. Lütfen tekrar giriş yapın."),setTimeout(()=>{window.location.href="/login"},2e3);return}throw new Error(`Sunucu hatası: ${te.status} - ${oe}`)}try{const he=JSON.parse(oe);if(!he.success)throw new Error(he.error||"Ekleme başarısız")}catch(he){if(!te.ok)throw new Error("Sunucu yanıtı geçersiz format içeriyor")}De.success("Yayıncı başarıyla eklendi")}await $n(),r&&r(ir.length+(O!=null&&O.id?0:1))}catch(L){De.error(`İşlem sırasında hata oluştu: ${L.message}`),x(!1);return}}P(!1),j(null),Q.resetFields()}catch(A){De.error("İşlem sırasında bir hata oluştu: "+((A==null?void 0:A.message)||""))}x(!1)},Jn=()=>{const S=ir.filter(L=>L.id!==void 0).map(({id:L,...V})=>V);if(S.length===0){De("Dışa aktarılacak veri bulunamadı.",{icon:"⚠️"});return}const A=ua.json_to_sheet(S),I=ua.book_new();ua.book_append_sheet(I,A,"Yayıncılar"),dx(I,"yayincilar.xlsx")},qn=S=>{De("Excel'den içe aktarma özelliği henüz tamamlanmadı.",{icon:"ℹ️"}),S.target.value=""},Zn=()=>{j(null),Q.resetFields(),Q.setFieldsValue({dogum_tarihi:null}),P(!0)},Qn=S=>{const A={name:S.name,subject:S.subject,content:S.content},I=[...ue,A];Me(I),localStorage.setItem("publisherEmailTemplates",JSON.stringify(I)),ze(!1),Ye.resetFields()},Ht=S=>{const A={name:S.name,content:S.content},I=[...y,A];b(I),localStorage.setItem("publisherWhatsappTemplates",JSON.stringify(I)),le(!1),Oe.success("Şablon eklendi")},ea=S=>{if(!ie)return;const A=y.map(I=>I.name===ie.name?{name:S.name,content:S.content}:I);b(A),localStorage.setItem("publisherWhatsappTemplates",JSON.stringify(A)),fe(!1),ge(null),de.resetFields(),Oe.success("Şablon güncellendi")},ra=S=>{const A=y.filter(I=>I.name!==S);b(A),localStorage.setItem("publisherWhatsappTemplates",JSON.stringify(A)),C===S&&F(void 0),Oe.success("Şablon silindi")},ta=S=>{ge(S),de.setFieldsValue(S),fe(!0)},h=S=>{if(!S.telefon){Oe.error("Bu yayıncının telefon numarası bulunamadı");return}try{let A=S.telefon.toString().replace(/\s+/g,"");A.startsWith("0")&&(A=A.substring(1)),A.startsWith("+")||(A="+90"+A);const I=`Merhaba ${S.isim_soyisim||S.username||""},`,L=`https://wa.me/${A}?text=${encodeURIComponent(I)}`;window.open(L,"_blank"),Oe.success("WhatsApp mesajı açılıyor...")}catch(A){Oe.error("WhatsApp mesajı oluşturulurken bir hata oluştu")}},T=S=>{if(!S.mail){Oe.error("Bu yayıncının e-posta adresi bulunamadı");return}try{const A=`mailto:${S.mail}?subject=${encodeURIComponent("Merhaba")}&body=${encodeURIComponent(`Merhaba ${S.isim_soyisim||S.username||""},`)}`;window.open(A,"_blank"),Oe.success("E-posta istemcisi açılıyor...")}catch(A){Oe.error("E-posta oluşturulurken bir hata oluştu")}},p=async()=>{try{const S=await J.validateFields();Kr(!0);let A=[];if(!Array.isArray(k)||k.length===0){if(Oe.info("Hiçbir yayıncı seçmediniz. Tablo üzerinde seçim yapmak istediğiniz yayıncıları işaretleyebilirsiniz."),!Array.isArray(lt))throw new Error("Filtrelenmiş yayıncı listesi alınamadı");A=lt.filter(L=>L&&L.mail)}else{if(!Array.isArray(ir))throw new Error("Etkin yayıncı listesi alınamadı");A=ir.filter(L=>L&&k.includes(L.id)&&L.mail)}if(A.length===0){Oe.error("Geçerli e-posta adresi olan yayıncı bulunamadı"),Kr(!1);return}const I=A.map(L=>L.mail).filter(L=>L&&typeof L=="string"&&L.includes("@"));if(I.length===0){Oe.error("Geçerli e-posta adresi olan yayıncı bulunamadı"),Kr(!1);return}if(!Ir){const L=await Promise.all(se.map(async V=>({filename:V.name,content:V.originFileObj,contentType:V.type})));Oe.loading({content:"Mailler gönderiliyor...",key:"bulk-email"}),await E(I,S.subject,S.content),Oe.success({content:"Mailler gönderildi",key:"bulk-email"})}X(!1),J.resetFields(),z([]),ar([])}catch(S){Oe.error({content:"Mail gönderilemedi: "+(S instanceof Error?S.message:"Bilinmeyen hata"),key:"bulk-email"})}finally{Kr(!1)}},g=async()=>{try{const S=await J.validateFields();vr(S.subject),un(S.content),Fr(!0)}catch(S){Oe.error("Lütfen tüm alanları doldurun")}},E=async(S,A,I)=>{if(!S||S.length===0)return;const L=20,V=[];for(let te=0;te<S.length;te+=L)V.push(S.slice(te,te+L));V.forEach((te,oe)=>{setTimeout(()=>{const he=`mailto:${te.join(",")}?subject=${encodeURIComponent(A)}&body=${encodeURIComponent(I)}`;window.open(he,"_blank")},oe*500)})},w=async()=>{try{const S=await pe.validateFields();let A=[];if(!Array.isArray(k)||k.length===0){if(Oe.info("Hiçbir yayıncı seçmediniz. Tablo üzerinde seçim yapmak istediğiniz yayıncıları işaretleyebilirsiniz."),!Array.isArray(lt))throw new Error("Filtrelenmiş yayıncı listesi alınamadı");A=lt.filter(V=>V&&V.telefon)}else{if(!Array.isArray(ir))throw new Error("Etkin yayıncı listesi alınamadı");A=ir.filter(V=>V&&k.includes(V.id)&&V.telefon)}if(A.length===0){Oe.error("Geçerli telefon numarası olan yayıncı bulunamadı");return}let I=0,L=0;Oe.loading({content:"WhatsApp mesajları hazırlanıyor...",key:"whatsapp-sending"});for(let V=0;V<A.length;V++){const te=A[V];try{if(!te.telefon){L++;continue}let oe=te.telefon.toString().replace(/\s+/g,"");oe.startsWith("0")&&(oe=oe.substring(1)),oe.startsWith("+")||(oe="+90"+oe);const he=S.content.replace("{username}",te.isim_soyisim||te.username||""),Fe=`https://wa.me/${oe}?text=${encodeURIComponent(he)}`;await new Promise(er=>setTimeout(er,300)),window.open(Fe,"_blank"),I++}catch(oe){L++}}Oe.success({content:`WhatsApp mesajları açılıyor... (Başarılı: ${I}, Hatalı: ${L})`,key:"whatsapp-sending"}),ae(!1),pe.resetFields()}catch(S){Oe.error({content:"WhatsApp mesajları gönderilemedi: "+(S instanceof Error?S.message:"Bilinmeyen hata"),key:"whatsapp-sending"})}},M={selectedRowKeys:k,onChange:S=>z(S)},G=async(S,A,I)=>{const L=S[A];let V=I;if(A==="dogum_tarihi"&&I&&(V=At(I).format("YYYY-MM-DD")),L===V){ft(null);return}x(!0);try{const te={...S,[A]:V};let oe={success:!0};if(!Ir){let he=localStorage.getItem(jr.TOKEN_KEY)||"";if(!he)try{const er=localStorage.getItem(jr.USER_KEY);if(er){const Er=JSON.parse(er);Er&&Er.token&&(he=Er.token)}}catch(er){}if(!he){De.error("Oturum bilgileriniz bulunamadı. Lütfen tekrar giriş yapın."),setTimeout(()=>{window.location.href="/login"},2e3);return}const Fe=await fetch(`${Cr.X_SITE_BASE_URL}${Cr.ENDPOINTS.PUBLISHERS}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${he}`},body:JSON.stringify({...te,id:S.id})});if(!Fe.ok){const er=await Fe.text();if(Fe.status===401){De.error("Oturumunuz sonlandı. Lütfen tekrar giriş yapın."),setTimeout(()=>{window.location.href="/login"},2e3);return}throw new Error(`Sunucu hatası: ${Fe.status} ${er}`)}try{oe=await Fe.json()}catch(er){throw new Error("Sunucu yanıtı geçersiz format içeriyor")}if(!oe.success)throw new Error("Güncelleme başarısız");br(er=>er.map(Er=>Er.id===S.id?te:Er))}De.success("Güncellendi"),ft(null),r&&r((Ir?c:Tr).length)}catch(te){De.error("Güncelleme hatası: "+((te==null?void 0:te.message)||"")),br(oe=>oe.map(he=>he.id===S.id?{...he,[A]:L}:he)),ft(null)}x(!1)},N=S=>(A,I)=>{if(St&&St.key===I.id&&St.dataIndex===S){let V="text";return S==="dogum_tarihi"&&(V="date"),D.jsx("input",{type:V,autoFocus:!0,value:yt!==void 0?yt:S==="dogum_tarihi"&&A?At(A).format("YYYY-MM-DD"):A||"",onChange:te=>jt(te.target.value),onBlur:te=>G(I,S,V==="date"?te.target.value:yt),onKeyDown:te=>{te.key==="Enter"?G(I,S,V==="date"?te.target.value:yt):te.key==="Escape"&&ft(null)},style:{width:"100%"}})}return D.jsx("div",{style:{minHeight:24,cursor:"pointer"},onClick:()=>{I.id!==void 0&&I.id!==null?(ft({key:I.id,dataIndex:S}),jt(S==="dogum_tarihi"&&A?At(A).format("YYYY-MM-DD"):A)):Oe.warning("Bu kayıt düzenlenemez, kayıt ID'si eksik.")},children:S==="dogum_tarihi"&&A?At(A,"YYYY-MM-DD").format("DD.MM.YYYY"):A||"-"})},B=()=>{const S="https://x.tuberajans.com",A=[{title:" ",dataIndex:"profile_image",key:"profile_image",width:48,render:(I,L)=>{if(!L.username)return D.jsx(za,{icon:D.jsx(Ha,{}),size:36,style:{backgroundColor:"#f0f0f0",marginRight:8},alt:"Kullanıcı"});const V=L.profile_image?`${S}${L.profile_image}`:`${S}/img/${L.username}.jpg`;return D.jsx(za,{src:V,icon:D.jsx(Ha,{}),size:36,style:{backgroundColor:"#f0f0f0",marginRight:8},alt:L.isim_soyisim||L.username||"Kullanıcı"})},fixed:t?void 0:"left"},{title:"İSİM SOYİSİM",dataIndex:"isim_soyisim",key:"isim_soyisim",sorter:(I,L)=>{const V=I.isim_soyisim||"",te=L.isim_soyisim||"";return V.localeCompare(te)},width:t?120:180,ellipsis:t,render:N("isim_soyisim")},{title:"KULLANICI ADI",dataIndex:"username",key:"username",sorter:(I,L)=>{const V=I.username||"",te=L.username||"";return V.localeCompare(te)},width:t?100:150,ellipsis:t,render:N("username")},{title:"TELEFON",dataIndex:"telefon",key:"telefon",width:t?120:150,ellipsis:t,render:N("telefon")},{title:"E-POSTA",dataIndex:"mail",key:"mail",width:t?150:200,ellipsis:t,render:N("mail")},{title:"DOĞUM TARİHİ",dataIndex:"dogum_tarihi",key:"dogum_tarihi",sorter:(I,L)=>{try{const V=I.dogum_tarihi?new Date(I.dogum_tarihi):new Date(0),te=L.dogum_tarihi?new Date(L.dogum_tarihi):new Date(0),oe=isNaN(V.getTime())?0:V.getTime(),he=isNaN(te.getTime())?0:te.getTime();return oe-he}catch(V){return 0}},width:t?120:150,ellipsis:t,render:N("dogum_tarihi")},{title:"ŞEHİR",dataIndex:"sehir",key:"sehir",width:t?100:120,ellipsis:t,render:N("sehir")},{title:"MESLEK",dataIndex:"meslek",key:"meslek",width:t?100:120,ellipsis:t,render:N("meslek")},{title:"İŞLEMLER",key:"actions",width:t?100:150,fixed:t?"right":void 0,render:(I,L)=>D.jsxs("div",{className:t?"action-column-mobile":"action-column",children:[D.jsx(ke,{type:"text",danger:!0,icon:D.jsx(ja,{}),onClick:()=>{var V;k.length>0||dn((V=L.id)!=null?V:null),_r(!0)},size:t?"small":"middle"}),D.jsx(ke,{type:"text",icon:D.jsx(Ts,{}),style:{color:"#25D366"},onClick:()=>h(L),size:t?"small":"middle"}),D.jsx(ke,{type:"text",icon:D.jsx(Es,{}),style:{color:"#1890ff"},onClick:()=>T(L),size:t?"small":"middle"})]})}];return t?A.filter(I=>I.key!=="isim_soyisim"&&I.key!=="dogum_tarihi"&&I.key!=="sehir"&&I.key!=="meslek"):A};xe.useEffect(()=>{const S=o?c:v;r&&Array.isArray(S)&&r(S.length)},[o,c,v,r]);const U=async()=>{const S=Tr.map(V=>V.username).filter(Boolean);if(S.length===0){Oe.info("Profil resmi çekilecek yayıncı bulunamadı."),qr({total:0,current:0,success:0,fail:0,running:!1}),localStorage.removeItem("publisherProfileImageProgress");return}qr({total:S.length,current:0,success:0,fail:0,running:!0}),localStorage.setItem("publisherProfileImageProgress",JSON.stringify({total:S.length,current:0,success:0,fail:0,running:!0}));const A=localStorage.getItem(jr.TOKEN_KEY);if(!A){Oe.error("Oturum bilgileriniz bulunamadı. Lütfen tekrar giriş yapın."),qr({total:0,current:0,success:0,fail:0,running:!1}),localStorage.removeItem("publisherProfileImageProgress"),setTimeout(()=>{window.location.href="/login"},1500);return}let I=0,L=0;for(let V=0;V<S.length;V++){const te=S[V];try{const oe=await fetch(`${Cr.X_SITE_BASE_URL}/fetch_profile_images.php`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${A}`},body:JSON.stringify({usernames:[te],type:"publisher"})});if(oe.ok)try{const he=await oe.json();he.success&&he.results&&he.results[0]&&he.results[0].success?I++:L++}catch(he){try{const Fe=await oe.text()}catch(Fe){}L++}else{try{const he=await oe.text()}catch(he){}L++}}catch(oe){L++}qr({total:S.length,current:V+1,success:I,fail:L,running:!0}),localStorage.setItem("publisherProfileImageProgress",JSON.stringify({total:S.length,current:V+1,success:I,fail:L,running:!0}))}qr({total:S.length,current:S.length,success:I,fail:L,running:!1}),localStorage.removeItem("publisherProfileImageProgress"),Oe.success(`Profil resimleri çekildi! Başarılı: ${I}, Başarısız: ${L}`),typeof f=="function"&&f()};if(xe.useEffect(()=>{const S=localStorage.getItem("publisherProfileImageProgress");if(S)try{const A=JSON.parse(S);A&&typeof A.total=="number"&&typeof A.current=="number"&&typeof A.success=="number"&&typeof A.fail=="number"&&typeof A.running=="boolean"&&A.running?qr(A):localStorage.removeItem("publisherProfileImageProgress")}catch(A){localStorage.removeItem("publisherProfileImageProgress")}},[]),s&&!o)return D.jsx(xa,{children:D.jsx("div",{className:"m-6",children:D.jsx(Ga,{message:"Veri Yükleme Hatası",description:D.jsxs("div",{children:[D.jsxs("p",{children:["API sunucusu yanıt vermiyor veya bir sorun oluştu: ",s]}),D.jsx(ke,{type:"primary",onClick:()=>l(!0),className:"mt-4",children:"Demo Verileri İle Devam Et"})]}),type:"error",showIcon:!0})})});if(d)return D.jsx(xa,{children:D.jsx(ws,{tip:"Yükleniyor...",size:"large",className:"flex justify-center items-center h-full p-6"})});const q=async()=>{if(k.length===0){De.error("Silinecek yayıncı seçilmedi.");return}x(!0);try{let S=0,A=0;if(!Ir){let I=localStorage.getItem(jr.TOKEN_KEY)||"";if(!I)try{const L=localStorage.getItem(jr.USER_KEY);if(L){const V=JSON.parse(L);V&&V.token&&(I=V.token)}}catch(L){}if(!I){De.error("Oturum bilgileriniz bulunamadı. Lütfen tekrar giriş yapın."),setTimeout(()=>{window.location.href="/login"},2e3);return}for(const L of k){const V=typeof L=="string"?parseInt(L,10):L;try{const te=await fetch(`${Cr.X_SITE_BASE_URL}${Cr.ENDPOINTS.PUBLISHERS}?id=${V}`,{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:`Bearer ${I}`},body:JSON.stringify({id:V})});if(!te.ok){const he=await te.text();if(te.status===401){De.error("Oturumunuz sonlandı. Lütfen tekrar giriş yapın."),setTimeout(()=>{window.location.href="/login"},2e3);return}throw new Error(`Sunucu hatası: ${te.status} ${he}`)}const oe=await te.json();if(!oe.success)throw new Error(oe.error||"Silme başarısız");S++}catch(te){A++}}if(S>0){const L=Tr.filter(oe=>!k.includes(oe.id));br(L);const V=L.length,te=Math.ceil(V/st)||1;Jr>te&&wt(Math.max(1,te)),r&&r(L.length)}}A===0?De.success(`${S} yayıncı başarıyla silindi`):De.success(`${S} yayıncı silindi, ${A} yayıncı silinemedi`),z([])}catch(S){De.error("Yayıncılar silinirken bir hata oluştu: "+((S==null?void 0:S.message)||""))}x(!1)};return D.jsx(xa,{children:D.jsxs("div",{className:e?"bg-transparent p-0":"m-6",children:[!e&&D.jsxs("div",{className:"flex justify-between items-center mb-6",children:[D.jsx("h1",{className:"text-2xl font-semibold text-violet-600",children:"Yayıncılar"}),D.jsxs("span",{className:"bg-violet-100 text-violet-800 text-sm font-medium px-2.5 py-1 rounded",children:["Toplam: ",(v==null?void 0:v.length)||(c==null?void 0:c.length)||0]})]}),D.jsxs("div",{className:`bg-white dark:bg-gray-800 ${e?"rounded-none shadow-none border-0":"rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700"} ${n?"dark-theme":""}`,children:[Ur.running&&D.jsxs("div",{style:{marginBottom:16},children:[D.jsx(Ss,{percent:Math.round(Ur.current/Ur.total*100),status:"active"}),D.jsx("div",{style:{marginTop:8,fontSize:14},children:`Çekilen: ${Ur.current} / ${Ur.total} | Başarılı: ${Ur.success} | Hatalı: ${Ur.fail}`})]}),D.jsxs("div",{className:"flex flex-wrap gap-4 mb-6 px-6 pt-6 pb-2",children:[D.jsxs("div",{className:"relative",style:{width:384},children:[D.jsx(ps,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),D.jsx("input",{type:"text",placeholder:"Yayıncı ara (İsim, Mail, Kullanıcı Adı...)",value:u,onChange:S=>_(S.target.value),className:"pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent dark:bg-gray-700 dark:text-white"}),u&&D.jsx("button",{onClick:()=>_(""),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:D.jsx(Is,{className:"w-4 h-4"})})]}),D.jsxs("div",{className:"flex flex-wrap gap-2 items-center",children:[D.jsx("input",{id:"excel-import-input",type:"file",accept:".xlsx, .xls",onChange:qn,className:"hidden"}),D.jsxs(ke,{className:"flex items-center",onClick:()=>X(!0),children:[D.jsx(Ls,{className:"h-4 w-4 mr-2"}),"Toplu Mail Gönder"]}),D.jsxs(ke,{className:"flex items-center",onClick:()=>ae(!0),children:[D.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",stroke:"none",className:"h-4 w-4 mr-2",children:D.jsx("path",{d:"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z"})}),"Toplu WhatsApp"]}),D.jsx(ys,{overlay:D.jsxs(Gt,{children:[D.jsx(Gt.Item,{icon:D.jsx(gs,{}),onClick:U,children:"Profil Resimlerini Çek"},"fetchProfileImages"),D.jsx(Gt.Item,{icon:D.jsx(Wa,{}),onClick:Zn,children:"Yeni Yayıncı"},"1"),D.jsx(Gt.Item,{icon:D.jsx(vs,{}),onClick:Jn,children:"Excel'e Aktar"},"2"),D.jsx(Gt.Item,{icon:D.jsx(Ya,{}),onClick:()=>{var S;return(S=document.getElementById("excel-import-input"))==null?void 0:S.click()},children:"Excel'den İçe Aktar"},"3")]}),trigger:["click"],children:D.jsx(ke,{icon:D.jsx(Ns,{}),type:"text",style:{border:"none",background:"transparent",boxShadow:"none"}})})]})]}),k.length>0&&D.jsx("div",{style:{marginBottom:16,padding:"0 24px"},children:D.jsx(Ga,{message:D.jsxs("span",{children:[D.jsx("strong",{children:k.length})," yayıncı seçildi."," ",D.jsx(ke,{type:"link",size:"small",onClick:()=>z([]),style:{padding:"0 4px"},children:"Seçimi Temizle"})]}),type:"info",showIcon:!0})}),D.jsx("div",{className:"w-full px-0",children:D.jsx(As,{rowSelection:M,columns:B(),dataSource:lt,loading:d,rowKey:S=>S?S.id!==void 0&&S.id!==null?S.id.toString():S.username?`username-${S.username}`:`temp-${Math.random()}`:Math.random().toString(),pagination:{current:Jr,pageSize:st,onChange:(S,A)=>{wt(S),mn(A)},position:["bottomCenter"],showSizeChanger:!0,pageSizeOptions:[10,20,50,100]},locale:{emptyText:ir.length>0?"Arama kriterlerine uygun yayıncı bulunamadı.":"Henüz yayıncı eklenmemiş."},className:"publishers-table w-full",scroll:{x:"max-content"},style:{width:"100%"}})})]}),D.jsx(Zr,{title:O?"Yayıncı Düzenle":"Yeni Yayıncı Ekle",visible:R,onCancel:()=>{P(!1),j(null),Q.resetFields()},footer:null,destroyOnClose:!0,width:600,children:D.jsxs(ve,{form:Q,layout:"vertical",onFinish:Kn,children:[D.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-x-4",children:[D.jsx(ve.Item,{name:"isim_soyisim",label:"İsim Soyisim",children:D.jsx(rr,{placeholder:"Örn: Ahmet Yılmaz"})}),D.jsx(ve.Item,{name:"username",label:"Kullanıcı Adı",rules:[{required:!0,message:"Lütfen kullanıcı adı girin"}],children:D.jsx(rr,{placeholder:"Örn: ahmetyilmaz"})}),D.jsx(ve.Item,{name:"telefon",label:"Telefon",children:D.jsx(rr,{placeholder:"Örn: 5551234567"})}),D.jsx(ve.Item,{name:"mail",label:"E-posta",rules:[{required:!1,type:"email",message:"Lütfen geçerli bir e-posta girin"}],children:D.jsx(rr,{placeholder:"Örn: <EMAIL>"})}),D.jsx(ve.Item,{name:"dogum_tarihi",label:"Doğum Tarihi",children:D.jsx(Fs,{format:"DD.MM.YYYY",style:{width:"100%"},placeholder:"Tarih Seçin"})}),D.jsx(ve.Item,{name:"sehir",label:"Şehir",children:D.jsx(rr,{placeholder:"Örn: İstanbul"})}),D.jsx(ve.Item,{name:"meslek",label:"Meslek",children:D.jsx(rr,{placeholder:"Örn: Yayıncı"})})]}),D.jsx(ve.Item,{className:"mt-4",children:D.jsx(ke,{type:"primary",htmlType:"submit",loading:d,block:!0,size:"large",className:"bg-indigo-600 hover:bg-indigo-700 border-0 text-white",children:O?"Değişiklikleri Kaydet":"Yeni Yayıncı Oluştur"})})]})}),D.jsx(Zr,{title:"Toplu E-posta Gönder",open:W,onCancel:()=>X(!1),footer:[D.jsx(ke,{onClick:()=>X(!1),style:{borderColor:ne?"#6b7280":"#d1d5db",color:ne?"#e5e7eb":"#374151"},children:"İptal"},"back"),D.jsx(ke,{onClick:g,style:{borderColor:"#1890ff",color:"#1890ff"},children:"Önizle"},"preview"),D.jsx(ke,{type:"primary",onClick:p,loading:Et,style:{backgroundColor:"#1890ff",borderColor:"#1890ff",color:"white"},children:"Gönder"},"submit")],width:700,centered:!0,bodyStyle:{paddingRight:16},style:{top:20},children:D.jsxs(ve,{form:J,layout:"vertical",children:[D.jsx(ve.Item,{label:"Şablon",children:D.jsx(pn,{placeholder:"Şablon seçin (opsiyonel)",value:Ne,onChange:S=>{ur(S);const A=ue.find(I=>I.name===S);A&&J.setFieldsValue({subject:A.subject,content:A.content})},allowClear:!0,style:{width:"100%"},dropdownRender:S=>D.jsxs(D.Fragment,{children:[S,D.jsx(Va,{style:{margin:"8px 0"}}),D.jsx("div",{style:{display:"flex",flexWrap:"nowrap",padding:"8px"},children:D.jsx(ke,{type:"link",onClick:()=>ze(!0),icon:D.jsx(Rs,{size:14}),children:"Yeni Şablon Ekle"})})]}),children:ue.map(S=>D.jsx(pn.Option,{value:S.name,children:S.name},S.name))})}),D.jsx(ve.Item,{name:"subject",label:"Konu",rules:[{required:!0,message:"Lütfen bir konu girin!"}],children:D.jsx(rr,{placeholder:"E-posta Konusu"})}),D.jsx(ve.Item,{name:"content",label:"İçerik",rules:[{required:!0,message:"Lütfen bir mesaj girin!"}],children:D.jsx(rr.TextArea,{placeholder:"E-posta İçeriği - {username} ifadesi yayıncının adıyla değiştirilecektir.",rows:6})}),D.jsxs(ve.Item,{label:"Dosya Ekle",children:[D.jsx(Cs,{listType:"picture",fileList:se,onChange:({fileList:S})=>ar(S),beforeUpload:()=>!1,multiple:!0,children:D.jsx(ke,{icon:D.jsx(Ya,{}),children:"Dosya Seç"})}),D.jsx("div",{className:"text-xs text-gray-500 mt-1",children:"Desteklenen dosya türleri: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG, GIF (maks. 10MB)"})]})]})}),D.jsx(Zr,{title:`E-posta Önizleme: ${Tt}`,open:Ar,onCancel:()=>Fr(!1),footer:[D.jsx(ke,{onClick:()=>Fr(!1),style:{borderColor:ne?"#6b7280":"#d1d5db",color:ne?"#e5e7eb":"#374151"},children:"Kapat"},"back")],width:700,children:D.jsx("div",{className:"email-preview-container",children:D.jsxs("div",{style:{border:"1px solid #e6e6e6",borderRadius:"4px",padding:"20px",backgroundColor:"#fff"},children:[D.jsxs("div",{style:{marginBottom:"15px",borderBottom:"1px solid #f0f0f0",paddingBottom:"10px"},children:[D.jsxs("div",{children:[D.jsx("strong",{children:"Konu:"})," ",Tt]}),D.jsxs("div",{children:[D.jsx("strong",{children:"Ekler:"})," ",se.length>0?se.map(S=>S.name).join(", "):"Yok"]})]}),D.jsx("div",{dangerouslySetInnerHTML:{__html:Xr},style:{padding:"10px",minHeight:"300px"}})]})})}),D.jsx(Zr,{title:"Toplu WhatsApp Mesajı Gönder",open:Z,onCancel:()=>ae(!1),footer:[D.jsx(ke,{onClick:()=>ae(!1),style:{borderColor:ne?"#6b7280":"#d1d5db",color:ne?"#e5e7eb":"#374151"},children:"İptal"},"back"),D.jsx(ke,{type:"primary",onClick:w,style:{backgroundColor:"#25D366",borderColor:"#25D366",color:"white"},children:"Gönder"},"submit")],width:600,centered:!0,bodyStyle:{paddingRight:16},style:{top:20},children:D.jsxs(ve,{form:pe,layout:"vertical",children:[D.jsx(ve.Item,{label:"Şablon",children:D.jsx(pn,{placeholder:"Şablon seçin (opsiyonel)",value:C,onChange:S=>{F(S);const A=y.find(I=>I.name===S);A&&pe.setFieldsValue({content:A.content})},allowClear:!0,style:{width:"100%"},dropdownRender:S=>D.jsxs(D.Fragment,{children:[S,D.jsx(Va,{style:{margin:"8px 0"}}),D.jsxs("div",{style:{display:"flex",justifyContent:"space-between",padding:"8px"},children:[D.jsx(ke,{type:"link",onClick:()=>le(!0),icon:D.jsx(Wa,{}),children:"Yeni Şablon Ekle"}),C&&D.jsxs(ks,{children:[D.jsx(ke,{type:"link",onClick:()=>{const A=y.find(I=>I.name===C);A&&ta(A)},icon:D.jsx(_s,{}),children:"Düzenle"}),D.jsx(Os,{title:"Bu şablonu silmek istediğinize emin misiniz?",onConfirm:()=>ra(C),okText:"Evet",cancelText:"Hayır",children:D.jsx(ke,{type:"link",danger:!0,icon:D.jsx(ja,{}),children:"Sil"})})]})]})]}),children:y.map(S=>D.jsx(pn.Option,{value:S.name,children:S.name},S.name))})}),D.jsx(ve.Item,{name:"content",label:"Mesaj",rules:[{required:!0,message:"Lütfen bir mesaj girin!"}],children:D.jsx(rr.TextArea,{placeholder:"WhatsApp Mesajı - {username} ifadesi yayıncının adıyla değiştirilecektir.",rows:6})})]})}),D.jsx(Zr,{title:"Yeni E-posta Şablonu",open:Be,onCancel:()=>ze(!1),footer:[D.jsx(ke,{onClick:()=>ze(!1),children:"İptal"},"back"),D.jsx(ke,{type:"primary",onClick:()=>Ye.submit(),style:{backgroundColor:"#1890ff",borderColor:"#1890ff"},children:"Kaydet"},"submit")],width:600,centered:!0,bodyStyle:{paddingRight:16},style:{top:20},children:D.jsxs(ve,{form:Ye,layout:"vertical",onFinish:Qn,children:[D.jsx(ve.Item,{name:"name",label:"Şablon Adı",rules:[{required:!0,message:"Lütfen şablon adı girin!"}],children:D.jsx(rr,{placeholder:"Örn: Marka İşbirliği Teklifi"})}),D.jsx(ve.Item,{name:"subject",label:"E-posta Konusu",rules:[{required:!0,message:"Lütfen e-posta konusu girin!"}],children:D.jsx(rr,{placeholder:"Örn: Yeni İşbirliği Teklifi"})}),D.jsx(ve.Item,{name:"content",label:"E-posta İçeriği",rules:[{required:!0,message:"Lütfen e-posta içeriği girin!"}],children:D.jsx(rr.TextArea,{placeholder:"E-posta içeriği... {username} ifadesi yayıncının adıyla değiştirilecektir.",rows:6})})]})}),D.jsx(Zr,{title:"Yeni WhatsApp Şablonu",open:K,onCancel:()=>le(!1),footer:[D.jsx(ke,{onClick:()=>le(!1),children:"İptal"},"back"),D.jsx(ke,{type:"primary",onClick:()=>de.submit(),children:"Kaydet"},"submit")],width:600,centered:!0,bodyStyle:{paddingRight:16},style:{top:20},children:D.jsxs(ve,{form:de,layout:"vertical",onFinish:Ht,children:[D.jsx(ve.Item,{name:"name",label:"Şablon Adı",rules:[{required:!0,message:"Lütfen şablon adı girin!"}],children:D.jsx(rr,{placeholder:"Örn: Etkinlik Daveti"})}),D.jsx(ve.Item,{name:"content",label:"Mesaj İçeriği",rules:[{required:!0,message:"Lütfen mesaj içeriği girin!"}],children:D.jsx(rr.TextArea,{placeholder:"Mesaj içeriği... {username} ifadesi yayıncının adıyla değiştirilecektir.",rows:6})})]})}),D.jsx(Zr,{title:"WhatsApp Şablonunu Düzenle",open:ce,onCancel:()=>{fe(!1),ge(null)},footer:[D.jsx(ke,{onClick:()=>{fe(!1),ge(null)},children:"İptal"},"back"),D.jsx(ke,{type:"primary",onClick:()=>de.submit(),children:"Güncelle"},"submit")],width:600,centered:!0,bodyStyle:{paddingRight:16},style:{top:20},children:D.jsxs(ve,{form:de,layout:"vertical",onFinish:ea,children:[D.jsx(ve.Item,{name:"name",label:"Şablon Adı",rules:[{required:!0,message:"Lütfen şablon adı girin!"}],children:D.jsx(rr,{placeholder:"Örn: Etkinlik Daveti"})}),D.jsx(ve.Item,{name:"content",label:"Mesaj İçeriği",rules:[{required:!0,message:"Lütfen mesaj içeriği girin!"}],children:D.jsx(rr.TextArea,{placeholder:"Mesaj içeriği... {username} ifadesi yayıncının adıyla değiştirilecektir.",rows:6})})]})}),D.jsx(Zr,{open:Wt,onCancel:()=>_r(!1),onOk:()=>{k.length>0?q():xn&&Xn(xn),_r(!1)},okText:"Evet, Sil",cancelText:"Vazgeç",title:k.length>0?"Yayıncıları Sil":"Yayıncıyı Sil",centered:!0,bodyStyle:{padding:24},style:{top:120},children:k.length>0?D.jsxs("p",{children:[k.length," yayıncıyı silmek istediğinize emin misiniz?"]}):D.jsx("p",{children:"Bu yayıncıyı silmek istediğinize emin misiniz?"})})]})})}export{Hx as default};
