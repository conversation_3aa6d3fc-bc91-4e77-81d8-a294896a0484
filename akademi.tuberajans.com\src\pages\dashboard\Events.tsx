import React, { useState, useEffect, useContext } from 'react';
import { motion } from 'framer-motion';
import { FaCalendarAlt, FaCalendarDay, FaChevronLeft, FaChevronRight, FaMapMarkerAlt, FaClock } from 'react-icons/fa';
import { BsCalendar3 } from 'react-icons/bs';
import axios from 'axios';
import { SidebarContext } from '../../contexts/SidebarContext';

// Etkinlik tipi
interface Event {
  id: number;
  title: string;
  description: string;
  start_date: string;
  end_date: string;
  location: string;
  capacity: number;
  registered: number;
  thumbnail: string;
  instructor: string;
  category: string;
  is_featured: boolean | number; // API'den 0/1 olarak da gelebilir
  status: string;
  date?: Date; // Frontend için eklenen tarih nesnesi
  endDate?: Date; // Frontend için eklenen bitiş tarihi nesnesi
  isFeatured?: boolean; // Frontend için eklenen özellik
  isPastEvent?: boolean; // Frontend için eklenen özellik
}

const Events: React.FC = () => {
  const [currentMonth, setCurrentMonth] = useState<number>(new Date().getMonth());
  const [currentYear, setCurrentYear] = useState<number>(new Date().getFullYear());
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Sidebar context'inden isMobile değerini al
  const { isMobile } = useContext(SidebarContext);

  // Etkinlikleri API'den çek
  useEffect(() => {
    const fetchEvents = async () => {
      setLoading(true);
      setError(null);

      try {
        console.log('Etkinlikler API çağrısı yapılıyor...');

        // Her zaman mock veri kullan (geliştirme aşamasında)
        const isDevMode = true; // import.meta.env.VITE_DISABLE_AUTH === 'true' || import.meta.env.DEV;

        if (isDevMode) {
          console.log('Development mode: Mock etkinlik verileri kullanılıyor');

          const mockEvents: Event[] = [
            {
              id: 1,
              title: 'TikTok Trendleri Workshop',
              description: 'Güncel TikTok trendlerini keşfedin ve kendi içeriklerinizde nasıl kullanacağınızı öğrenin. Uzman eğitmenlerimizle birlikte trend analizi yapacağız.',
              start_date: '2025-01-28T19:00:00Z',
              end_date: '2025-01-28T21:00:00Z',
              location: 'Online - Zoom',
              capacity: 50,
              registered: 23,
              thumbnail: '',
              instructor: 'Ahmet Yılmaz',
              category: 'Workshop',
              is_featured: true,
              status: 'active'
            },
            {
              id: 2,
              title: 'İçerik Üretimi Masterclass',
              description: 'Profesyonel içerik üretimi teknikleri, video çekimi, düzenleme ve yayınlama stratejileri üzerine kapsamlı bir eğitim.',
              start_date: '2025-02-05T14:00:00Z',
              end_date: '2025-02-05T17:00:00Z',
              location: 'İstanbul - Tuber Akademi Merkezi',
              capacity: 30,
              registered: 12,
              thumbnail: '',
              instructor: 'Zeynep Kaya',
              category: 'Masterclass',
              is_featured: false,
              status: 'active'
            },
            {
              id: 3,
              title: 'Viral Video Stratejileri Semineri',
              description: 'Viral olan videoların ortak özelliklerini analiz ederek kendi içeriklerinizi nasıl viral yapabileceğinizi öğrenin.',
              start_date: '2025-02-12T16:00:00Z',
              end_date: '2025-02-12T18:00:00Z',
              location: 'Online - YouTube Live',
              capacity: 100,
              registered: 67,
              thumbnail: '',
              instructor: 'Mehmet Özkan',
              category: 'Seminer',
              is_featured: true,
              status: 'active'
            },
            {
              id: 4,
              title: 'Sosyal Medya Pazarlama Atölyesi',
              description: 'Sosyal medya platformlarında etkili pazarlama stratejileri geliştirme ve marka bilinirliği artırma teknikleri.',
              start_date: '2025-02-18T13:00:00Z',
              end_date: '2025-02-18T16:00:00Z',
              location: 'Ankara - Tuber Akademi Şubesi',
              capacity: 25,
              registered: 18,
              thumbnail: '',
              instructor: 'Ayşe Demir',
              category: 'Atölye',
              is_featured: false,
              status: 'active'
            },
            {
              id: 5,
              title: 'Canlı Yayın Teknikleri Webinarı',
              description: 'Profesyonel canlı yayın yapma teknikleri, ekipman seçimi ve izleyici etkileşimi artırma yöntemleri.',
              start_date: '2025-02-25T20:00:00Z',
              end_date: '2025-02-25T22:00:00Z',
              location: 'Online - Twitch',
              capacity: 75,
              registered: 34,
              thumbnail: '',
              instructor: 'Can Yıldız',
              category: 'Webinar',
              is_featured: false,
              status: 'active'
            },
            {
              id: 6,
              title: 'Yayıncı Buluşması - Networking',
              description: 'Tuber Akademi yayıncılarının bir araya geldiği networking etkinliği. Deneyim paylaşımı ve iş birliği fırsatları.',
              start_date: '2025-03-05T18:00:00Z',
              end_date: '2025-03-05T21:00:00Z',
              location: 'İstanbul - Tuber Akademi Merkezi',
              capacity: 40,
              registered: 28,
              thumbnail: '',
              instructor: 'Tuber Akademi Ekibi',
              category: 'Buluşma',
              is_featured: true,
              status: 'active'
            }
          ];

          // Mock verileri işle
          const processedEvents = mockEvents.map((event: any) => {
            const startDate = new Date(event.start_date);
            const endDate = new Date(event.end_date);

            return {
              ...event,
              date: startDate,
              endDate: endDate,
              isFeatured: Boolean(event.is_featured),
              isPastEvent: startDate <= new Date()
            };
          });

          console.log('Mock etkinlikler yüklendi:', processedEvents);
          setEvents(processedEvents);
          setLoading(false);
          return;
        }

        // Production modunda gerçek API çağrısı
        const response = await axios.get('/backend/api/api_data.php', {
          params: {
            endpoint: 'events'
          },
          timeout: 10000 // 10 saniye timeout
        });

        console.log('API yanıtı:', response.data);

        if (response.data && response.data.status === 'success') {
          // API'den gelen verileri işle
          if (Array.isArray(response.data.data)) {
            if (response.data.data.length === 0) {
              console.log('API yanıtında etkinlik bulunamadı');
              setEvents([]);
            } else {
              const processedEvents = response.data.data.map((event: Event) => {
                const startDate = new Date(event.start_date);
                const endDate = new Date(event.end_date);

                return {
                  ...event,
                  date: startDate,
                  endDate: endDate,
                  isFeatured: Boolean(event.is_featured),
                  isPastEvent: startDate <= new Date()
                };
              });

              console.log('İşlenmiş etkinlikler:', processedEvents);
              setEvents(processedEvents);
            }
          } else {
            console.error('API yanıtında data dizisi bulunamadı:', response.data);
            setError('API yanıtı beklenen formatta değil.');
          }
        } else {
          console.error('API başarısız yanıt döndü:', response.data);
          setError(response.data?.message || 'Etkinlikler yüklenirken bir hata oluştu.');
        }
      } catch (err: any) {
        console.error('Etkinlikler yüklenirken hata:', err);

        // Daha detaylı hata mesajı
        if (err.response) {
          // Sunucu yanıtı ile dönen hata
          console.error('Sunucu yanıtı:', err.response.data);
          console.error('Durum kodu:', err.response.status);
          setError(`Sunucu hatası: ${err.response.status} - ${err.response.data?.message || 'Bilinmeyen hata'}`);
        } else if (err.request) {
          // İstek yapıldı ama yanıt alınamadı
          console.error('İstek yapıldı ama yanıt alınamadı');
          setError('Sunucuya bağlanılamadı. Lütfen internet bağlantınızı kontrol edin.');
        } else {
          // İstek oluşturulurken hata oluştu
          console.error('İstek hatası:', err.message);
          setError(`İstek hatası: ${err.message}`);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchEvents();
  }, []);

  // Hangi etkinliklerin gösterileceğini belirle (gelecek veya geçmiş)
  const now = new Date();

  console.log('Şu anki tarih:', now);
  console.log('Tüm etkinlikler:', events);

  // Etkinlikleri filtrele ve "Öne Çıkan" özelliğini güncelle
  const filteredEvents = events.length > 0
    ? events
      // Önce date özelliği olan etkinlikleri filtrele
      .filter(event => {
        console.log('Etkinlik tarihi:', event.date, 'Şu anki tarih:', now, 'Gelecek mi?', event.date && event.date > now);
        return event.date !== undefined;
      })
      .filter(event => {
        // Sadece aktif etkinlikleri göster (tarih kontrolü geçici olarak kaldırıldı)
        const isActive = event.status === 'active';
        const isFuture = event.date! > now;
        console.log(`Etkinlik ${event.title}: aktif=${isActive}, gelecek=${isFuture}`);
        return isActive; // Sadece aktif olanları göster, tarih kontrolü yok
      })
      // Etkinlikleri tarihe göre sırala - Yaklaşan etkinlikler için en yakın tarih en üstte
      .sort((a, b) => {
        // Yaklaşan etkinlikler için artan sıralama (en yakın tarih en üstte)
        return a.date!.getTime() - b.date!.getTime();
      })
    : [];

  console.log('Filtrelenmiş etkinlikler:', filteredEvents);

  // Takvim görünümü için ay isimlerini tanımla
  const monthNames = [
    'Ocak', 'Şubat', 'Mart', 'Nisan', 'Mayıs', 'Haziran',
    'Temmuz', 'Ağustos', 'Eylül', 'Ekim', 'Kasım', 'Aralık'
  ];

  // Önceki aya git
  const prevMonth = () => {
    if (currentMonth === 0) {
      setCurrentMonth(11);
      setCurrentYear(currentYear - 1);
    } else {
      setCurrentMonth(currentMonth - 1);
    }
  };

  // Sonraki aya git
  const nextMonth = () => {
    if (currentMonth === 11) {
      setCurrentMonth(0);
      setCurrentYear(currentYear + 1);
    } else {
      setCurrentMonth(currentMonth + 1);
    }
  };

  return (
    <div className="min-h-screen w-full">
      <div className="container" style={{
        maxWidth: isMobile ? '100%' : 'none',
        overflowX: 'hidden'
      }}>
        {/* Loading durumu */}
        {loading && (
          <div className="flex items-center justify-center min-h-screen">
            <div className="animate-spin rounded-full h-8 w-8 sm:h-12 sm:w-12 border-t-2 border-b-2 border-[#FF3E71]"></div>
          </div>
        )}

        {/* Hata durumu */}
        {error && !loading && (
          <div className="flex items-center justify-center min-h-screen px-2">
            <div className="text-center p-3 sm:p-6 bg-white dark:bg-[#16151c] rounded-lg shadow-sm">
              <div className="text-red-500 text-lg sm:text-xl mb-2 sm:mb-4">⚠️</div>
              <h3 className="text-sm sm:text-lg font-medium text-gray-900 dark:text-white">Hata</h3>
              <p className="mt-1 sm:mt-2 text-xs sm:text-sm text-gray-500 dark:text-gray-400">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="mt-2 sm:mt-4 px-3 py-1.5 sm:px-4 sm:py-2 bg-[#FF3E71] text-white rounded-md hover:bg-[#FF5F87] transition-colors text-xs sm:text-sm"
              >
                Yeniden Dene
              </button>
            </div>
          </div>
        )}

        {/* İki Sütunlu Düzen - Sadece loading ve error yoksa göster */}
        {!loading && !error && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-2 sm:gap-4">
            {/* Ana İçerik - Etkinlik Listesi */}
            <div className="lg:col-span-2">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-6 mb-2 sm:mb-6">
                {filteredEvents.map((event, index) => (
                  <motion.div
                    key={event.id}
                    className={`${
                      event.isFeatured
                        ? 'bg-gradient-to-br from-white to-pink-50/30 dark:from-[#16151c] dark:to-[#1f1e27] ring-1 ring-[#FF3E71]/20'
                        : 'bg-white dark:bg-[#16151c]'
                    } rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-all duration-300`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    <div className="relative">
                      {/* Thumbnail ve Kategori Badge */}
                      <div className="relative">
                        <div className="h-32 sm:h-44 w-full bg-gradient-to-br from-[#FF3E71]/90 to-[#FF5F87]/90 brightness-90"></div>
                        <img
                          className="absolute inset-0 h-32 sm:h-44 w-full object-cover object-center brightness-75"
                          src={event.thumbnail}
                          alt={event.title}
                          onError={(e) => {
                            // Görsel yüklenemezse gradient arka plan zaten gösterilecek
                            e.currentTarget.style.display = 'none';
                          }}
                          loading="lazy"
                        />

                        {/* Etkinlik Başlığı - Thumbnail Üzerinde */}
                        <div className="absolute inset-0 flex flex-col justify-center items-center p-2 sm:p-4 text-center">
                          <h2 className={`text-sm sm:text-lg md:text-xl font-bold text-white drop-shadow-lg line-clamp-2 mb-1 sm:mb-2 ${
                            event.isFeatured && !event.isPastEvent ? 'bg-gradient-to-r from-white to-pink-100 bg-clip-text text-transparent' : ''
                          }`}>
                            {event.title}
                          </h2>
                        </div>

                        <div className="absolute top-0 left-0 right-0 p-1.5 sm:p-3">
                          <div className="flex flex-wrap gap-1">
                            <span className={`inline-flex items-center px-1.5 sm:px-2 py-0.5 rounded-full text-xs font-medium backdrop-blur-md ${
                              event.category === 'Webinar' ? 'bg-blue-500/80 text-white' :
                              event.category === 'Seminer' ? 'bg-purple-500/80 text-white' :
                              event.category === 'Buluşma' ? 'bg-green-500/80 text-white' :
                              event.category === 'Atölye' ? 'bg-yellow-500/80 text-white' :
                              'bg-gray-500/80 text-white'
                            }`}>
                              {event.category}
                            </span>
                            {event.isFeatured && !event.isPastEvent && (
                              <span className="inline-flex items-center px-1.5 sm:px-2 py-0.5 rounded-full text-xs font-medium bg-[#FF3E71]/80 text-white backdrop-blur-md">
                                Öne Çıkan
                              </span>
                            )}
                          </div>
                        </div>

                        {/* Tarih Badge - Yatay Düzen */}
                        <div className={`absolute bottom-0 left-0 m-1.5 sm:m-3 ${
                          event.isFeatured
                            ? 'bg-gradient-to-r from-[#FF3E71]/90 to-[#FF5F87]/90 text-white'
                            : 'bg-white/90 dark:bg-gray-800/90'
                        } backdrop-blur-md rounded-lg px-1.5 sm:px-2.5 py-1 sm:py-1.5 shadow-md`}>
                          <div className="flex items-center space-x-1">
                            <div className={`text-sm sm:text-lg font-bold ${
                              event.isFeatured ? 'text-white' : 'text-[#FF3E71]'
                            }`}>
                              {event.date!.getDate()}
                            </div>
                            <div className={`text-xs font-medium ${
                              event.isFeatured ? 'text-white' : 'text-gray-700 dark:text-gray-300'
                            }`}>
                              {event.date!.toLocaleDateString('tr-TR', { month: 'short' })}
                            </div>
                            <div className={`text-xs font-medium ${
                              event.isFeatured ? 'text-white/90' : 'text-gray-600 dark:text-gray-400'
                            }`}>
                              {event.date!.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' })}
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* İçerik Bölümü */}
                      <div className="p-2 sm:p-4">
                        <p className="text-xs text-gray-600 dark:text-gray-300 line-clamp-2">{event.description}</p>

                        <div className="mt-2 sm:mt-3 flex flex-col sm:flex-row gap-1 sm:gap-3">
                          <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                            <FaMapMarkerAlt className="mr-1 text-[#FF3E71]" />
                            <span className="truncate">{event.location}</span>
                          </div>

                          <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                            <FaCalendarDay className="mr-1 text-[#FF3E71]" />
                            <span className="truncate">
                              {event.date!.toLocaleDateString('tr-TR', {
                                day: 'numeric',
                                month: 'long',
                                year: 'numeric',
                              })}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>

              {filteredEvents.length === 0 && (
                <div className="text-center py-6 sm:py-10 bg-white dark:bg-[#16151c] rounded-lg shadow-sm px-2">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 mx-auto bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-300 rounded-full flex items-center justify-center mb-2 sm:mb-4">
                    <FaCalendarAlt className="text-base sm:text-xl" />
                  </div>
                  <h3 className="text-sm sm:text-lg font-medium text-gray-900 dark:text-white">Etkinlik Yok</h3>
                  <p className="mt-0.5 sm:mt-1 text-xs sm:text-sm text-gray-500 dark:text-gray-400">Bu dönemde etkinlik yok.</p>
                </div>
              )}
            </div>

            {/* Yan Panel - Etkinlik Takvimi */}
            <div>
              {/* Etkinlik Takvimi */}
              <div className="bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden mb-2 sm:mb-6">
                <div className="flex items-center justify-between px-2 sm:px-3 py-1.5 sm:py-2.5 border-b border-gray-100 dark:border-gray-700">
                  <div className="flex items-center">
                    <BsCalendar3 className="text-gray-700 dark:text-gray-300 mr-1" />
                    <h2 className="text-xs sm:text-sm font-semibold text-gray-800 dark:text-white">Takvim</h2>
                  </div>
                  <div className="flex space-x-1">
                    <button
                      onClick={prevMonth}
                      className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400"
                      aria-label="Önceki Ay"
                      title="Önceki Ay"
                    >
                      <FaChevronLeft className="text-xs" />
                    </button>
                    <button
                      onClick={nextMonth}
                      className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400"
                      aria-label="Sonraki Ay"
                      title="Sonraki Ay"
                    >
                      <FaChevronRight className="text-xs" />
                    </button>
                  </div>
                </div>

                <div className="p-2 sm:p-3">
                  <div className="text-center mb-2 sm:mb-3">
                    <h4 className="text-sm sm:text-base font-semibold text-[#FF3E71]">
                      {monthNames[currentMonth]} {currentYear}
                    </h4>
                  </div>

                  {/* Gün başlıkları */}
                  <div className="grid grid-cols-7 gap-0.5 sm:gap-1 mb-1 sm:mb-2">
                    {['Pt', 'Sa', 'Ça', 'Pe', 'Cu', 'Ct', 'Pz'].map((day, idx) => (
                      <div key={idx} className="text-center text-xs font-medium text-gray-500 dark:text-gray-400 py-0.5">
                        {day}
                      </div>
                    ))}
                  </div>

                  {/* Günler */}
                  <div className="grid grid-cols-7 gap-0.5 sm:gap-1">
                    {(() => {
                      // Ayın ilk günü ve toplam gün sayısı
                      const firstDayOfMonth = new Date(currentYear, currentMonth, 1).getDay();
                      const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
                      // Takvimde haftanın ilk günü Pazartesi (JS: 1), ama grid 0-indexli, Pazar=0
                      const startOffset = (firstDayOfMonth === 0 ? 6 : firstDayOfMonth - 1);
                      const today = new Date();
                      today.setHours(0, 0, 0, 0);
                      const now = today;
                      // Grid için boşlukları ve günleri oluştur
                      const daysArray = [
                        ...Array(startOffset).fill(null),
                        ...Array(daysInMonth).fill(0).map((_, i) => i + 1)
                      ];
                      return daysArray.map((date, idx) => {
                        if (date === null) {
                          return <div key={"empty-" + idx}></div>;
                        }
                        // Bu günün tarihi
                        const thisDate = new Date(currentYear, currentMonth, date);
                        thisDate.setHours(0, 0, 0, 0);
                        // Geçmiş gün mü?
                        const isPast = thisDate < now;
                        // Bugün mü?
                        const isToday = thisDate.getTime() === now.getTime();
                        // Gelecek etkinlik günü mü?
                        const hasEvent = events.some(
                          e => e.date &&
                            e.date.getFullYear() === currentYear &&
                            e.date.getMonth() === currentMonth &&
                            e.date.getDate() === date &&
                            e.date >= now
                        );
                        // Seçili gün (isteğe bağlı: bir state ile seçili gün tutulabilir)
                        // const isSelected = selectedDate && thisDate.getTime() === selectedDate.getTime();
                        return (
                          <div
                            key={date}
                            className={`
                              text-center py-1.5 text-xs rounded-md transition-all duration-200 select-none
                              ${isPast ? 'bg-gray-100 text-gray-400 opacity-50 pointer-events-none' : ''}
                              ${isToday ? 'ring-2 ring-[#FF3E71] font-bold bg-white text-[#FF3E71]' : ''}
                              ${hasEvent && !isToday ? 'bg-gradient-to-br from-[#FF3E71] to-[#FF5F87] text-white font-bold shadow-sm' : ''}
                              ${!isPast && !isToday && !hasEvent ? 'hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 hover:shadow-sm cursor-pointer' : ''}
                            `}
                            // onClick={() => setSelectedDate(thisDate)} // Seçili gün için eklenebilir
                          >
                            {date}
                          </div>
                        );
                      });
                    })()}
                  </div>
                </div>
              </div>

              {/* Yaklaşan Etkinlikler Listesi */}
              <div className="bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden mb-6">
                <div className="flex items-center justify-between px-3 py-2.5 border-b border-gray-100 dark:border-gray-700">
                  <div className="flex items-center">
                    <FaCalendarAlt className="text-gray-700 dark:text-gray-300 mr-1.5" />
                    <h2 className="text-sm font-semibold text-gray-800 dark:text-white">Yaklaşan Etkinlikler</h2>
                  </div>
                </div>

                <div className="divide-y divide-gray-100 dark:divide-gray-700">
                  {/* İlk etkinliği öne çıkan olarak göster */}
                  {filteredEvents.length > 0 && filteredEvents[0].date && (
                    <div key={filteredEvents[0].id} className="p-3 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                      <div className="flex items-start space-x-2">
                        {/* Tarih Badge */}
                        <div className="flex-shrink-0 bg-gradient-to-r from-[#FF3E71]/80 to-[#FF5F87]/80 text-white rounded-lg px-2 py-1.5 shadow-sm">
                          <div className="flex items-center space-x-1">
                            <div className="text-base font-bold text-white">
                              {filteredEvents[0].date!.getDate()}
                            </div>
                            <div className="text-xs font-medium text-white">
                              {filteredEvents[0].date!.toLocaleDateString('tr-TR', { month: 'short' })}
                            </div>
                            <div className="text-xs font-medium text-white/90">
                              {filteredEvents[0].date!.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' })}
                            </div>
                          </div>
                        </div>

                        {/* Etkinlik Bilgileri */}
                        <div className="flex-1 min-w-0">
                          <h4 className="text-xs font-semibold text-[#FF3E71] dark:text-[#FF5F87] truncate">
                            {filteredEvents[0].title}
                          </h4>
                          <div className="flex items-center mt-1 text-xs text-gray-500 dark:text-gray-400">
                            <FaMapMarkerAlt className="mr-1 text-[#FF3E71] text-xs" />
                            <span className="truncate text-xs">{filteredEvents[0].location}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Diğer Yaklaşan Etkinlikler - En yakın tarihli etkinlik en üstte */}
                  {filteredEvents
                    .slice(1, 4) // İlk etkinliği atla, sonraki 3 etkinliği al
                    .map((event) => {

                      return (
                        <div key={event.id} className="p-3 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                          <div className="flex items-start space-x-2">
                            {/* Tarih Badge */}
                            <div className="flex-shrink-0 rounded-lg px-1.5 py-0.5 bg-white dark:bg-[#16151c]">
                              <div className="flex items-center space-x-1">
                                <div className="text-base font-bold text-[#FF3E71]">
                                  {event.date!.getDate()}
                                </div>
                                <div className="text-xs font-medium text-gray-600 dark:text-gray-300">
                                  {event.date!.toLocaleDateString('tr-TR', { month: 'short' })}
                                </div>
                              </div>
                            </div>

                            {/* Etkinlik Bilgileri */}
                            <div className="flex-1 min-w-0">
                              <h4 className="text-xs font-medium text-gray-900 dark:text-white truncate">
                                {event.title}

                              </h4>
                              <div className="flex items-center mt-1 text-xs text-gray-500 dark:text-gray-400">
                                <FaMapMarkerAlt className="mr-1 text-[#FF3E71] text-xs" />
                                <span className="truncate text-xs">{event.location}</span>
                              </div>
                              {/* Saat bilgisi */}
                              <div className="flex items-center mt-1 text-xs text-[#FF3E71] dark:text-[#FF3E71]">
                                <FaClock className="mr-1 text-[#FF3E71] text-xs" />
                                <span className="text-xs">
                                  {event.date!.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' })}
                                </span>
                              </div>
                            </div>


                          </div>
                        </div>
                      );
                    })}

                  {filteredEvents.length === 0 && (
                    <div className="p-6 text-center">
                      <p className="text-sm text-gray-500 dark:text-gray-400">Yaklaşan etkinlik bulunmamaktadır.</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Events;