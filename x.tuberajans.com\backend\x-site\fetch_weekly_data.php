<?php
// Hata ayıklama için
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Config dosyasını dahil et
require_once '../config.php';

// Log dosyası
$log_file = __DIR__ . '/fetch_weekly_data.log';

/**
 * Log mesajını dosyaya yazar
 */
function log_message($message) {
    global $log_file;
    $date = date('Y-m-d H:i:s');
    $log_entry = "[$date] $message" . PHP_EOL;
    file_put_contents($log_file, $log_entry, FILE_APPEND);
}

// CORS başlıkları
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json');

// OPTIONS isteği için hızlı yanıt
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Sadece POST isteklerini kabul et
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method Not Allowed']);
    exit;
}

// Yeni istek loglanıyor
log_message("Yeni istek alındı");

// Yetkilendirme kontrolü
$headers = getallheaders();
$authHeader = isset($headers['Authorization']) ? $headers['Authorization'] : '';

if (empty($authHeader) || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Geçersiz yetkilendirme.']);
    exit;
}

$token = $matches[1];
// Token kontrolü yapılabilir, şimdilik atlıyoruz

// JSON girdisini al
$json_data = file_get_contents('php://input');
$data = json_decode($json_data, true);

// İstek içeriğini logla
log_message("Alınan istek: " . $json_data);

// startDate parametresinin kontrolü
if (empty($data['startDate'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'startDate parametresi gerekli']);
    log_message("Hata: startDate parametresi eksik");
    exit;
}

$start_date = $data['startDate'];

// Tarih formatını kontrol et (YYYY-MM-DD)
if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $start_date)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Geçersiz tarih formatı. YYYY-MM-DD formatında olmalı']);
    log_message("Hata: Geçersiz tarih formatı: $start_date");
    exit;
}

try {
    // Veritabanı üzerinde otomasyonu başlat
    $db_tiktok->beginTransaction();
    
    // automation_commands tablosuna yeni komut ekle
    $stmt = $db_tiktok->prepare("INSERT INTO automation_commands (command, parameters, status, created_at) VALUES (?, ?, ?, NOW())");
    $command = 'fetch_weekly_data';
    $parameters = json_encode(['start_date' => $start_date]);
    $status = 'pending';
    
    $stmt->execute([$command, $parameters, $status]);
    $commandId = $db_tiktok->lastInsertId();
    
    // İşlemi tamamla
    $db_tiktok->commit();
    
    // Başarılı yanıt döndür
    http_response_code(200);
    echo json_encode([
        'success' => true,
        'message' => 'Haftalık veri çekme işlemi başlatıldı',
        'data' => [
            'command_id' => $commandId,
            'start_date' => $start_date,
            'status' => 'pending'
        ]
    ]);
    
    log_message("Veri çekme komutu veritabanına eklendi. ID: $commandId, Tarih: $start_date");
    
} catch (Exception $e) {
    // Hata durumunda rollback yap
    if ($db_tiktok->inTransaction()) {
        $db_tiktok->rollBack();
    }
    
    log_message("Hata oluştu: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'İşlem sırasında bir hata oluştu',
        'message' => $e->getMessage()
    ]);
}
?>