import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useNavigate } from 'react-router-dom';

// API URL - Geliştirme ortamında yerel API kullanılıyor
// Canlıda akademi.tuberajans.com/backend/api kullanılacak
const API_URL = (typeof import.meta !== 'undefined' && import.meta.env && import.meta.env.VITE_API_URL)
  ? import.meta.env.VITE_API_URL
  : 'https://akademi.tuberajans.com/backend/api';
console.log('AuthContext using API URL:', API_URL);

// Kullanıcı tipi
interface User {
  id: number | string;
  name: string;
  email: string;
  username: string;
  role: string;
  token?: string;
  profile_image?: string;
  permissions?: Record<string, any>;
}

// Context tipi
interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (identifier: string, password: string, userData?: User) => Promise<{ success: boolean; message?: string }>;
  logout: () => Promise<void>;
  isAuthenticated: boolean;
}

// Context oluşturma
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Context hook
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Provider component
export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const navigate = useNavigate();

  // Giriş fonksiyonu
  const login = async (identifier: string, password: string): Promise<{ success: boolean; message?: string }> => {
    try {
      setLoading(true);

      // API'ye istek gönder
      const response = await fetch(`${API_URL}/login.php`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: identifier,
          password,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        return {
          success: false,
          message: errorData.message || `HTTP error! status: ${response.status}`
        };
      }

      const data = await response.json();

      if (data.status !== 'success') {
        return {
          success: false,
          message: data.message || 'Giriş yapılırken bir hata oluştu'
        };
      }

      if (data.user) {
        // Kullanıcı bilgilerini ve token'ı kaydet
        localStorage.setItem('user', JSON.stringify(data.user));

        // Token varsa kaydet
        if (data.token) {
          localStorage.setItem('token', data.token);
        }

        setUser(data.user);
        setIsAuthenticated(true);

        return { success: true };
      } else {
        return {
          success: false,
          message: 'Sunucudan geçersiz yanıt alındı'
        };
      }
    } catch (error) {
      console.error('Login error:', error);
      return {
        success: false,
        message: 'Bir hata oluştu, lütfen tekrar deneyin'
      };
    } finally {
      setLoading(false);
    }
  };

  // Çıkış fonksiyonu
  const logout = async (): Promise<void> => {
    try {
      const token = localStorage.getItem('token');

      if (token) {
        await fetch(`${API_URL}/auth.php?action=logout`, {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Yerel depolamayı temizle
      localStorage.removeItem('user');
      localStorage.removeItem('token');
      localStorage.removeItem('last_auth_check');
      localStorage.removeItem('tiktokUser');
      localStorage.removeItem('tiktok_last_fetch');

      // State'i güncelle
      setUser(null);
      setIsAuthenticated(false);

      // Ana sayfaya yönlendir
      navigate('/');
    }
  };

  // Oturum kontrolü
  const checkAuth = async () => {
    try {
      const token = localStorage.getItem('token');
      const storedUser = localStorage.getItem('user');
      const lastAuthCheck = localStorage.getItem('last_auth_check');
      const now = Date.now();

      if (!token || !storedUser) {
        setUser(null);
        setIsAuthenticated(false);
        setLoading(false);
        return;
      }

      // Son 2 dakika içinde auth check yapıldıysa skip et
      if (lastAuthCheck && (now - parseInt(lastAuthCheck)) < 120000) {
        console.log('AuthContext: Cache aktif, auth check atlanıyor');
        setUser(JSON.parse(storedUser));
        setIsAuthenticated(true);
        setLoading(false);
        return;
      }

      // Token kontrolü - timeout ile
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3000); // 3 saniye timeout

      const response = await fetch(`${API_URL}/auth.php?check=true`, {
        credentials: 'include',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      const data = await response.json();

      // Auth check zamanını güncelle
      localStorage.setItem('last_auth_check', now.toString());

      if (data.authenticated) {
        setUser(JSON.parse(storedUser));
        setIsAuthenticated(true);
      } else {
        // Token geçersiz, çıkış yap
        localStorage.removeItem('user');
        localStorage.removeItem('token');
        localStorage.removeItem('last_auth_check');
        setUser(null);
        setIsAuthenticated(false);
      }
    } catch (error) {
      console.error('Auth check error:', error);
      // Hata durumunda yerel kullanıcı bilgisini kullan
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        setUser(JSON.parse(storedUser));
        setIsAuthenticated(true);
      } else {
        setUser(null);
        setIsAuthenticated(false);
      }
    } finally {
      setLoading(false);
    }
  };

  // Sayfa yüklendiğinde oturum kontrolü
  useEffect(() => {
    checkAuth();
  }, []);

  // Context değeri
  const value: AuthContextType = {
    user,
    loading,
    login,
    logout,
    isAuthenticated
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
