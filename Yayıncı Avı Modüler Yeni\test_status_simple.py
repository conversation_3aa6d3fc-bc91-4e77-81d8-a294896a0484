#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import logging
import threading
import time

# Loglama ayarla
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def test_status_checker():
    """Status Checker'ı basit şekilde test et"""
    try:
        logger.info("🔧 Status Checker import test başlıyor...")
        
        # Import'ları test et
        from database_manager import DatabaseManager
        from status_checker import StatusCheckerThread
        logger.info("✅ Import'lar başarılı")
        
        # Database manager oluştur
        db_manager = DatabaseManager()
        logger.info("✅ DatabaseManager oluşturuldu")
        
        # Test kullanıcıları
        test_publishers = [
            {"username": "test_user1"},
            {"username": "test_user2"}
        ]
        
        logger.info(f"🔧 StatusCheckerThread oluşturuluyor... ({len(test_publishers)} kullanıcı)")
        
        # Status checker oluştur
        status_checker = StatusCheckerThread(
            db_manager=db_manager,
            publishers=test_publishers,
            parent=None
        )
        
        logger.info("✅ StatusCheckerThread oluşturuldu")
        logger.info(f"🔧 Thread tipi: {type(status_checker)}")
        logger.info(f"🔧 Thread daemon: {status_checker.daemon}")
        
        logger.info("🚀 StatusCheckerThread başlatılıyor...")
        
        # Thread'i başlat
        status_checker.start()
        logger.info("✅ StatusCheckerThread.start() çağrıldı")
        
        # Thread'in çalışıp çalışmadığını kontrol et
        time.sleep(2)
        logger.info(f"🔧 Thread alive: {status_checker.is_alive()}")
        
        # 10 saniye bekle
        for i in range(10):
            if status_checker.is_alive():
                logger.info(f"⏳ Thread hala çalışıyor... ({i+1}/10)")
                time.sleep(1)
            else:
                logger.info(f"🏁 Thread tamamlandı ({i+1}. saniyede)")
                break
        
        logger.info("✅ Test tamamlandı")
        
    except Exception as e:
        logger.error(f"❌ Test hatası: {e}")
        import traceback
        logger.error(f"❌ Hata detayı: {traceback.format_exc()}")

if __name__ == "__main__":
    test_status_checker()
