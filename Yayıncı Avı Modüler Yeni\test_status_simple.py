#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import logging
import threading
import time

# Loglama ayarla
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def test_status_checker():
    """Status Checker'ı gerçek kullanıcılarla test et"""
    try:
        logger.info("🚀 Status Checker GERÇEK TEST başlıyor...")
        logger.info("📝 Bu test şunları yapacak:")
        logger.info("  1. Chrome'u açacak")
        logger.info("  2. TikTok Backstage sayfasına gidecek")
        logger.info("  3. 3 kullanıcının durumunu sorgulayacak")
        logger.info("  4. Sonuçları veritabanına kaydedecek")
        logger.info("")
        
        # Import'ları test et
        from database_manager import DatabaseManager
        from status_checker import StatusCheckerThread
        logger.info("✅ Import'lar başarılı")
        
        # Database manager oluştur
        db_manager = DatabaseManager()
        logger.info("✅ DatabaseManager oluşturuldu")
        
        # Gerçek kullanıcıları test et
        test_publishers = [
            {"username": "tarotunsesinden"},
            {"username": "theangarabebesi"},
            {"username": "runbarunn"}
        ]
        
        logger.info(f"🔧 StatusCheckerThread oluşturuluyor... ({len(test_publishers)} kullanıcı)")
        logger.info("📋 Test edilecek kullanıcılar:")
        for i, user in enumerate(test_publishers, 1):
            logger.info(f"  {i}. {user['username']}")

        # Status checker oluştur
        status_checker = StatusCheckerThread(
            db_manager=db_manager,
            publishers=test_publishers,
            parent=None
        )
        
        logger.info("✅ StatusCheckerThread oluşturuldu")
        logger.info(f"🔧 Thread tipi: {type(status_checker)}")
        logger.info(f"🔧 Thread daemon: {status_checker.daemon}")
        
        logger.info("🚀 StatusCheckerThread başlatılıyor...")
        
        # Thread'i başlat
        status_checker.start()
        logger.info("✅ StatusCheckerThread.start() çağrıldı")
        
        # Thread'in çalışıp çalışmadığını kontrol et
        time.sleep(2)
        logger.info(f"🔧 Thread alive: {status_checker.is_alive()}")
        
        # 5 dakika bekle - Status Checker'ın tam çalışmasını görmek için
        logger.info("⏳ Status Checker'ın çalışmasını izliyoruz (5 dakika)...")
        for i in range(300):  # 5 dakika = 300 saniye
            if status_checker.is_alive():
                if i % 30 == 0:  # Her 30 saniyede bir durum raporu
                    logger.info(f"⏳ Thread çalışıyor... ({i//60} dakika {i%60} saniye)")
                time.sleep(1)
            else:
                logger.info(f"🏁 Thread tamamlandı ({i//60} dakika {i%60} saniyede)")
                break

        # Thread hala çalışıyorsa
        if status_checker.is_alive():
            logger.info("⏰ 5 dakika doldu, thread hala çalışıyor")

        logger.info("✅ Test tamamlandı")
        
    except Exception as e:
        logger.error(f"❌ Test hatası: {e}")
        import traceback
        logger.error(f"❌ Hata detayı: {traceback.format_exc()}")

if __name__ == "__main__":
    test_status_checker()
