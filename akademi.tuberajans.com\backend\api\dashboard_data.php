<?php
/**
 * Dashboard Verileri API
 * Bu API, dashboard sayfası için gerekli tüm verileri tek bir çağrıda döndürür
 */

// Hata raporlama
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Debug log
error_log("dashboard_data.php called with method: " . $_SERVER['REQUEST_METHOD']);

// Yapılandırma dosyasını dahil et
require_once __DIR__ . '/../config/config.php';

// CORS başlıkları
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Origin, Content-Type, Accept, Authorization');

// Zaman aşımını önlemek için
set_time_limit(60); // 60 saniye

// OPTIONS isteği için erken yanıt
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(204);
    exit;
}

// Veritabanı bağlantısını al
$db = getDB();

// İstek methodunu al
$method = $_SERVER['REQUEST_METHOD'];

// Sadece GET isteklerini kabul et
if ($method !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'status' => 'error',
        'message' => 'Desteklenmeyen HTTP metodu. Sadece GET istekleri kabul edilir.'
    ]);
    exit;
}

try {
    error_log("Starting dashboard data retrieval");

    // Tüm verileri içerecek dizi
    $dashboardData = [
        'status' => 'success',
        'announcements' => [],
        'courses' => [],
        'events' => []
    ];

    // Veritabanı bağlantısını kontrol et
    if (!$db) {
        throw new Exception("Veritabanı bağlantısı başarısız");
    }
    error_log("Database connection successful");

    // 1. Duyuruları al
    try {
        error_log("Fetching announcements");
        $announcementsQuery = "SELECT * FROM announcements WHERE status = 'active' ORDER BY created_at DESC LIMIT 4";
        $announcementsStmt = $db->prepare($announcementsQuery);
        $announcementsStmt->execute();
        $dashboardData['announcements'] = $announcementsStmt->fetchAll(PDO::FETCH_ASSOC);
        error_log("Announcements fetched: " . count($dashboardData['announcements']));
    } catch (PDOException $e) {
        error_log("Error fetching announcements: " . $e->getMessage());
        // Hata olsa bile devam et
    }

    // 2. Eğitimleri al
    try {
        error_log("Fetching courses");
        $coursesQuery = "SELECT * FROM courses WHERE status = 'active' ORDER BY created_at DESC";
        $coursesStmt = $db->prepare($coursesQuery);
        $coursesStmt->execute();
        $dashboardData['courses'] = $coursesStmt->fetchAll(PDO::FETCH_ASSOC);
        error_log("Courses fetched: " . count($dashboardData['courses']));
    } catch (PDOException $e) {
        error_log("Error fetching courses: " . $e->getMessage());
        // Hata olsa bile devam et
    }

    // 3. Etkinlikleri al
    try {
        error_log("Fetching events");
        $now = date('Y-m-d H:i:s');
        $eventsQuery = "SELECT * FROM events WHERE status = 'active' AND start_date > :now ORDER BY start_date ASC LIMIT 3";
        $eventsStmt = $db->prepare($eventsQuery);
        $eventsStmt->bindParam(':now', $now);
        $eventsStmt->execute();
        $dashboardData['events'] = $eventsStmt->fetchAll(PDO::FETCH_ASSOC);
        error_log("Events fetched: " . count($dashboardData['events']));
    } catch (PDOException $e) {
        error_log("Error fetching events: " . $e->getMessage());
        // Hata olsa bile devam et
    }

    // Yanıtı döndür
    $jsonResponse = json_encode($dashboardData);
    if ($jsonResponse === false) {
        error_log("JSON encode error: " . json_last_error_msg());
        throw new Exception("JSON encode error: " . json_last_error_msg());
    }

    error_log("Sending response with data size: " . strlen($jsonResponse));
    echo $jsonResponse;

} catch (PDOException $e) {
    error_log("PDO Exception in dashboard_data.php: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());

    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Veritabanı hatası: ' . $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
} catch (Exception $e) {
    error_log("Exception in dashboard_data.php: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());

    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
}
