#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
import time

def test_chrome():
    try:
        print("Chrome test başlatılıyor...")
        
        # Chrome seçenekleri
        options = Options()
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-gpu")
        options.add_argument("--disable-notifications")
        options.add_argument("--disable-popup-blocking")
        options.add_argument("--start-maximized")
        options.add_argument("--lang=tr-TR")
        options.add_argument("--remote-debugging-port=0")
        
        # Chrome binary yolu
        chrome_binary_path = r"C:\Users\<USER>\Downloads\chrome-win64\chrome-win64\chrome.exe"
        options.binary_location = chrome_binary_path
        
        # ChromeDriver yolu
        chromedriver_path = r"C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe"
        service = Service(executable_path=chromedriver_path)
        
        print("Chrome başlatılıyor...")
        driver = webdriver.Chrome(service=service, options=options)
        
        print("Google'a gidiliyor...")
        driver.get("https://www.google.com")
        
        print("Sayfa başlığı:", driver.title)
        
        time.sleep(3)
        
        print("Chrome kapatılıyor...")
        driver.quit()
        
        print("Test başarılı!")
        return True
        
    except Exception as e:
        print(f"Test başarısız: {e}")
        return False

if __name__ == "__main__":
    test_chrome()
