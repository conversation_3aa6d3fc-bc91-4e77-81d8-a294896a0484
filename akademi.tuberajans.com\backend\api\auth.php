<?php
/**
 * Akademi Auth API
 *
 * <PERSON>u dosya, akademi.tuberajans.com sitesi için kimlik doğrulama API'sini sağlar.
 * Kullanıcı girişi, çıkışı ve token doğrulama işlemlerini yönetir.
 */

// Doğrudan erişimi engelle
if (count(get_included_files()) == 1) {
    // Bu dosya doğrudan çalıştırılıyor, JSON yanıtı döndür
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'status' => 'error',
        'message' => 'API endpoint is working but should be accessed through proper channels',
        'time' => date('Y-m-d H:i:s')
    ]);
    exit;
}

// Hata raporlama ayarları
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Gerekli dosyaları dahil et
try {
    require_once dirname(__DIR__) . '/config/config.php';
} catch (Exception $e) {
    // Hata mesajını göster
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'error' => 'Dosya dahil etme hatası: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
    exit;
}

// CORS başlıkları
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
header("Access-Control-Max-Age: 86400");

// OPTIONS isteği için erken yanıt
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(204);
    exit();
}

// Content-Type header'ını ayarla
header('Content-Type: application/json; charset=utf-8');

// Veritabanı bağlantısı kontrolü
if (!isset($conn)) {
    die(json_encode([
        'status' => 'error',
        'message' => 'Veritabanı bağlantısı bulunamadı'
    ]));
}

/**
 * Auth Sınıfı
 * 
 * Kullanıcı kimlik doğrulama ve yetkilendirme işlemlerini yönetir.
 */
class Auth {
    private $conn;
    private $db;
    private $db_takip;

    /**
     * Constructor
     * 
     * @param mysqli $conn MySQLi bağlantısı
     */
    public function __construct($conn) {
        $this->conn = $conn;
        $this->db = $GLOBALS['db'] ?? null;
        $this->db_takip = $GLOBALS['db_takip'] ?? null;
    }

    /**
     * Mevcut kullanıcıyı döndürür
     * 
     * @return array|null Kullanıcı bilgileri veya null
     */
    public function getCurrentUser() {
        if (!isset($_SESSION['user_id'])) {
            return null;
        }

        $stmt = $this->conn->prepare("
            SELECT 
                id,
                username,
                email,
                name,
                role,
                permissions,
                profile_image,
                bio,
                social_media,
                tiktok_username,
                tiktok_open_id,
                tiktok_union_id,
                tiktok_linked_at,
                follower_count,
                following_count,
                likes_count,
                video_count,
                is_verified,
                tiktok_bio,
                tiktok_avatar_url,
                tiktok_display_name,
                is_agency_publisher,
                publisher_verified_at,
                created_at,
                updated_at,
                last_login,
                status
            FROM users 
            WHERE id = ? AND status = 'active'
        ");

        $stmt->bind_param('s', $_SESSION['user_id']);
        
        if (!$stmt->execute()) {
            error_log('Auth error: Failed to fetch user data');
            return null;
        }

        $result = $stmt->get_result();
        $user = $result->fetch_assoc();

        if (!$user) {
            error_log('Auth error: User not found or inactive - ID: ' . $_SESSION['user_id']);
            return null;
        }

        // Hassas bilgileri temizle
        unset($user['password']);
        unset($user['auth_token']);
        unset($user['auth_token_expires_at']);
        unset($user['access_token']);
        unset($user['refresh_token']);
        unset($user['token_expires_at']);

        return $user;
    }
}

// Bearer token'ı al
function getBearerToken() {
    $headers = null;
    if (isset($_SERVER['Authorization'])) {
        $headers = trim($_SERVER['Authorization']);
    } elseif (isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers = trim($_SERVER['HTTP_AUTHORIZATION']);
    } elseif (function_exists('apache_request_headers')) {
        $requestHeaders = apache_request_headers();
        $requestHeaders = array_combine(
            array_map('ucwords', array_keys($requestHeaders)),
            array_values($requestHeaders)
        );
        if (isset($requestHeaders['Authorization'])) {
            $headers = trim($requestHeaders['Authorization']);
        }
    }

    // Bearer token'ı ayıkla
    if (!empty($headers)) {
        if (preg_match('/Bearer\s(\S+)/', $headers, $matches)) {
            return $matches[1];
        }
    }
    return null;
}

// Token ile kimlik doğrulaması
function requireAuthToken() {
    $token = getBearerToken();
    if (!$token) {
        http_response_code(401);
        echo json_encode([
            'status' => 'error',
            'message' => 'Token gerekli'
        ]);
        exit;
    }

    global $db_takip;

    try {
        $stmt = $db_takip->prepare("SELECT user_id FROM user_tokens WHERE token = ? AND expires_at > NOW()");
        $stmt->execute([$token]);
        $result = $stmt->fetch();

        if ($result && isset($result['user_id'])) {
            return $result['user_id'];
        } else {
            http_response_code(401);
            echo json_encode([
                'status' => 'error',
                'message' => 'Geçersiz veya süresi dolmuş token'
            ]);
            exit;
        }
    } catch (Exception $e) {
        error_log('Token validation error: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'status' => 'error',
            'message' => 'Token doğrulama hatası'
        ]);
        exit;
    }
}

// Debug endpoint - Sadece geliştirme aşamasında kullanılır
if (isset($_GET['debug'])) {
    $debug_info = [
        'server' => $_SERVER,
        'request_method' => $_SERVER['REQUEST_METHOD'],
        'http_origin' => isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : 'not set',
        'remote_addr' => $_SERVER['REMOTE_ADDR'],
        'db_takip' => isset($db_takip) ? 'connected' : 'not connected',
        'db' => isset($db) ? 'connected' : 'not connected',
        'php_version' => phpversion(),
        'time' => date('Y-m-d H:i:s')
    ];

    jsonResponse($debug_info);
}

// Auth kontrolü
if (isset($_GET['check'])) {
    $token = getBearerToken();

    if (!$token) {
        jsonResponse(['authenticated' => false, 'message' => 'Token bulunamadı']);
    }

    try {
        $stmt = $db_takip->prepare("
            SELECT u.id, u.name, u.email, u.username, u.role
            FROM users u
            JOIN user_tokens t ON u.id = t.user_id
            WHERE t.token = ? AND t.expires_at > NOW()
        ");
        $stmt->execute([$token]);
        $user = $stmt->fetch();

        if ($user) {
            // Token süresini uzat
            $expiresAt = date('Y-m-d H:i:s', strtotime('+24 hours'));
            $updateStmt = $db_takip->prepare("UPDATE user_tokens SET expires_at = ? WHERE token = ?");
            $updateStmt->execute([$expiresAt, $token]);

            jsonResponse([
                'authenticated' => true,
                'user' => [
                    'id' => $user['id'],
                    'name' => $user['name'],
                    'email' => $user['email'],
                    'username' => $user['username'],
                    'role' => $user['role']
                ]
            ]);
        }
    } catch (PDOException $e) {
        error_log("Veritabanı hatası: " . $e->getMessage());
    }

    // Token geçersizse
    jsonResponse([
        'authenticated' => false,
        'message' => 'Oturum bulunamadı veya süresi doldu. Lütfen giriş yapın.'
    ]);
}

// Giriş işlemi
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isset($_GET['action'])) {
    // Gelen JSON verisini al
    $data = getJsonInput();

    // Debug log
    error_log("Login attempt: " . json_encode([
        'data' => $data,
        'headers' => getallheaders()
    ]));

    if ((!isset($data['email']) && !isset($data['username'])) || !isset($data['password'])) {
        errorResponse('Kullanıcı adı/E-posta ve şifre gerekli');
    }

    try {
        // Kullanıcı adı veya e-posta ile giriş yapabilme
        $identifier = isset($data['email']) ? $data['email'] : $data['username'];
        $identifier = strtolower(trim($identifier));

        // E-posta veya kullanıcı adı ile kullanıcıyı bul
        $stmt = $db_takip->prepare("SELECT id, email, username, password, role, name FROM users WHERE LOWER(email) = LOWER(?) OR LOWER(username) = LOWER(?)");
        $stmt->execute([$identifier, $identifier]);
        $user = $stmt->fetch();

        if (!$user) {
            errorResponse('Geçersiz kullanıcı adı veya şifre', 401);
        }

        // Şifre doğrulama işlemi
        $password_verified = password_verify($data['password'], $user['password']);
        $ip = $_SERVER['REMOTE_ADDR'] ?? '';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

        if (!$password_verified) {
            // Giriş başarısızsa, login_attempts tablosuna kayıt ekle
            $status = 'fail';
            $stmtLog = $db_takip->prepare("INSERT INTO login_attempts (email, attempt_time, status, ip_address, user_agent) VALUES (?, NOW(), ?, ?, ?)");
            $stmtLog->execute([$user['email'], $status, $ip, $userAgent]);

            errorResponse('Geçersiz kullanıcı adı veya şifre', 401);
        }

        // Giriş başarılıysa, login_attempts tablosuna kayıt ekle
        $status = 'success';
        $stmtLog = $db_takip->prepare("INSERT INTO login_attempts (email, attempt_time, status, ip_address, user_agent) VALUES (?, NOW(), ?, ?, ?)");
        $stmtLog->execute([$user['email'], $status, $ip, $userAgent]);

        // Token oluştur
        $token = bin2hex(random_bytes(32));
        $expires = date('Y-m-d H:i:s', strtotime('+24 hours'));

        // Önceki token'ları temizle
        $stmtClean = $db_takip->prepare("DELETE FROM user_tokens WHERE user_id = ?");
        $stmtClean->execute([$user['id']]);

        // Yeni token'ı kaydet
        $stmtToken = $db_takip->prepare("INSERT INTO user_tokens (user_id, token, expires_at) VALUES (?, ?, ?)");
        $stmtToken->execute([$user['id'], $token, $expires]);

        // Oturum bilgilerini ayarla
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_role'] = $user['role'];

        successResponse([
            'token' => $token,
            'expires_at' => $expires,
            'user' => [
                'id' => $user['id'],
                'email' => $user['email'],
                'username' => $user['username'],
                'role' => $user['role'],
                'name' => $user['name']
            ]
        ]);
    } catch (PDOException $e) {
        error_log("Veritabanı hatası: " . $e->getMessage());
        errorResponse('Sunucu hatası: ' . $e->getMessage(), 500);
    }
}

// Çıkış yap
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_GET['action']) && $_GET['action'] === 'logout') {
    $token = getBearerToken();

    if (!$token) {
        errorResponse('Token gerekli', 401);
    }

    try {
        $stmt = $db_takip->prepare("DELETE FROM user_tokens WHERE token = ?");
        $stmt->execute([$token]);

        // Oturumu temizle
        session_unset();
        session_destroy();

        successResponse([], 'Çıkış başarılı');
    } catch (PDOException $e) {
        errorResponse('Sunucu hatası', 500);
    }
}
