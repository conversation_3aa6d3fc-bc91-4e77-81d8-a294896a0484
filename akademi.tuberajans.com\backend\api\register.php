<?php
require_once __DIR__ . '/../config/config.php';
header('Content-Type: application/json');

// Debug log
error_log("Register.php called with method: " . $_SERVER['REQUEST_METHOD']);

// CORS headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'status' => 'error',
        'message' => 'Sadece POST metodu desteklenir'
    ]);
    exit;
}

// JSON verisini al
$json_data = file_get_contents('php://input');
error_log("Received JSON data: " . $json_data);

$input = json_decode($json_data, true);

if (!$input) {
    error_log("JSON decode failed: " . json_last_error_msg());
    echo json_encode([
        'status' => 'error',
        'message' => 'Geçersiz JSON verisi'
    ]);
    exit;
}

// Gerekli alanları kontrol et
$required_fields = ['name', 'username', 'email', 'password'];
foreach ($required_fields as $field) {
    if (empty($input[$field])) {
        echo json_encode([
            'status' => 'error',
            'message' => 'Lütfen tüm alanları doldurun'
        ]);
        exit;
    }
}

$name = trim($input['name']);
$username = trim($input['username']);
$email = trim($input['email']);
$password = $input['password'];
// Kullanıcı adı aynı zamanda TikTok kullanıcı adı olarak kullanılacak
$tiktok_username = isset($input['tiktok_username']) ? trim($input['tiktok_username']) : $username;

// Validasyonlar
if (strlen($password) < 6) {
    echo json_encode([
        'status' => 'error',
        'message' => 'Şifre en az 6 karakter olmalıdır'
    ]);
    exit;
}

if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    echo json_encode([
        'status' => 'error',
        'message' => 'Geçersiz e-posta adresi'
    ]);
    exit;
}

if (strlen($username) < 3) {
    echo json_encode([
        'status' => 'error',
        'message' => 'Kullanıcı adı en az 3 karakter olmalıdır'
    ]);
    exit;
}

// Kullanıcı adı ve e-posta kontrolü
try {
    // Kullanıcı adı kontrolü
    $stmt = $db->prepare("SELECT id FROM users WHERE username = ?");
    $stmt->execute([$username]);
    if ($stmt->fetch()) {
        echo json_encode([
            'status' => 'error',
            'message' => 'Bu kullanıcı adı zaten kullanılıyor'
        ]);
        exit;
    }

    // E-posta kontrolü
    $stmt = $db->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute([$email]);
    if ($stmt->fetch()) {
        echo json_encode([
            'status' => 'error',
            'message' => 'Bu e-posta adresi zaten kayıtlı'
        ]);
        exit;
    }

    // Şifreyi hashle
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);

    // Ajans üyeliği kontrolü (TikTok username varsa)
    $is_agency_publisher = false;
    if (!empty($tiktok_username)) {
        try {
            // @ işaretini kaldır
            $clean_tiktok_username = ltrim($tiktok_username, '@');

            // tuberaja_yayinci_takip.publisher_info tablosundan kontrol et
            $stmt = $db->prepare("
                SELECT COUNT(*)
                FROM tuberaja_yayinci_takip.publisher_info
                WHERE username = ? AND username IS NOT NULL AND username != ''
            ");
            $stmt->execute([$clean_tiktok_username]);
            $is_agency_publisher = $stmt->fetchColumn() > 0;

            error_log("Agency publisher check for " . $clean_tiktok_username . ": " . ($is_agency_publisher ? 'true' : 'false'));
        } catch (Exception $e) {
            error_log("Agency publisher check error during registration: " . $e->getMessage());
            $is_agency_publisher = false;
        }
    }

    // Kullanıcıyı veritabanına ekle
    $stmt = $db->prepare("
        INSERT INTO users (
            username, email, password, name, role, permissions,
            tiktok_username, is_agency_publisher,
            created_at, updated_at, status
        ) VALUES (?, ?, ?, ?, 'user', ?, ?, ?, NOW(), NOW(), 'active')
    ");

    $permissions = json_encode(['view_public' => true]);

    $result = $stmt->execute([
        $username,
        $email,
        $hashed_password,
        $name,
        $permissions,
        $tiktok_username ? ltrim($tiktok_username, '@') : null,
        $is_agency_publisher
    ]);

    if ($result) {
        $user_id = $db->lastInsertId();
        
        echo json_encode([
            'status' => 'success',
            'message' => 'Kayıt başarıyla tamamlandı',
            'user_id' => $user_id
        ]);
    } else {
        echo json_encode([
            'status' => 'error',
            'message' => 'Kayıt işlemi başarısız oldu'
        ]);
    }

} catch (Exception $e) {
    error_log('Kayıt hatası: ' . $e->getMessage());
    echo json_encode([
        'status' => 'error',
        'message' => 'Kayıt işlemi sırasında hata oluştu'
    ]);
}
?>
