<?php
/**
 * Auth API Test
 * 
 * Bu dosya, auth.php API'sini test etmek için kullanılır.
 */

// CORS başlıkları
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
header("Content-Type: application/json; charset=utf-8");

require_once __DIR__ . '/../config/config.php';

// Veritabanı bağlantısını kontrol et
if (isset($db_takip)) {
    echo json_encode([
        'status' => 'success',
        'message' => 'Veritabanı bağlantısı başarılı',
        'db_takip' => true
    ]);
} else {
    echo json_encode([
        'status' => 'error',
        'message' => 'Veritabanı bağlantısı başarısız',
        'db_takip' => false
    ]);
}
