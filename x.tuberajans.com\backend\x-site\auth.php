<?php
// Optimized Auth.php - No Dependencies Version
header('Content-Type: application/json; charset=utf-8');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Fast config (optional database)
$db = null;
try {
    $servername = "**************";
    $db_username = "root";
    $db_password = "Bebek845396!";
    
    $db = new PDO("mysql:host=$servername;dbname=tuberaja_yayinci_takip;charset=utf8mb4", 
                  $db_username, $db_password, [
                      PDO::ATTR_TIMEOUT => 1,
                      PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
                  ]);
} catch (Exception $e) {
    // Database unavailable - continue without it
    error_log("Auth DB unavailable: " . $e->getMessage());
}

function getBearerToken() {
    $headers = apache_request_headers();
    if (isset($headers['Authorization'])) {
        if (preg_match('/Bearer\s(\S+)/', $headers['Authorization'], $matches)) {
            return $matches[1];
        }
    }
    return null;
}

function getUserPermissions($userId) {
    // Simple role-based permissions
    return [
        'dashboard' => true,
        'users' => ['view' => true, 'edit' => false, 'delete' => false],
        'publishers' => ['view' => true, 'edit' => true, 'delete' => false],
        'analytics' => ['view' => true, 'export' => false]
    ];
}

function requireAuthToken() {
    global $db;
    
    $token = getBearerToken();
    
    if (!$token) {
        throw new Exception('Authorization token required');
    }
    
    if (!$db) {
        // Geliştirme modunda veya database bağlantısı yoksa dummy user id döndür
        return 1;
    }
    
    try {
        $stmt = $db->prepare("
            SELECT u.id, u.name, u.email, u.role
            FROM users u
            JOIN user_tokens t ON u.id = t.user_id
            WHERE t.token = ? AND t.expires_at > NOW()
            LIMIT 1
        ");
        $stmt->execute([$token]);
        $user = $stmt->fetch();

        if ($user) {
            // Token süresini uzat
            $db->prepare("UPDATE user_tokens SET expires_at = DATE_ADD(NOW(), INTERVAL 24 HOUR) WHERE token = ?")
               ->execute([$token]);
            return $user['id'];
        } else {
            throw new Exception('Invalid or expired token');
        }
    } catch (PDOException $e) {
        error_log("Token validation error: " . $e->getMessage());
        throw new Exception('Token validation failed');
    }
}

// Auth kontrolü
if (isset($_GET['check'])) {
    $token = getBearerToken();
    
    if ($token && $db) {
        try {
            $stmt = $db->prepare("
                SELECT u.id, u.name, u.email, u.role
                FROM users u
                JOIN user_tokens t ON u.id = t.user_id
                WHERE t.token = ? AND t.expires_at > NOW()
                LIMIT 1
            ");
            $stmt->execute([$token]);
            $user = $stmt->fetch();

            if ($user) {
                $permissions = getUserPermissions($user['id']);
                
                $userData = [
                    'id' => $user['id'],
                    'name' => $user['name'],
                    'email' => $user['email'],
                    'role' => $user['role'],
                    'permissions' => $permissions
                ];
                
                // Token süresini uzat
                $db->prepare("UPDATE user_tokens SET expires_at = DATE_ADD(NOW(), INTERVAL 24 HOUR) WHERE token = ?")
                   ->execute([$token]);
                
                http_response_code(200);
                echo json_encode([
                    'authenticated' => true,
                    'user' => $userData
                ]);
                exit;
            }
        } catch (Exception $e) {
            error_log("Auth query error: " . $e->getMessage());
        }
    }

    // Token geçersiz, yok, veya database unavailable
    http_response_code(200);
    echo json_encode([
        'authenticated' => false,
        'message' => 'Oturum bulunamadı veya süresi doldu.',
        'database_status' => $db ? 'connected' : 'unavailable'
    ]);
    exit;
}

// Giriş işlemi
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isset($_GET['action'])) {
    // Gelen JSON verisini al
    $raw_input = file_get_contents('php://input');
    $data = json_decode($raw_input, true);

    if (!$data || !isset($data['email']) || !isset($data['password'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Email ve şifre gerekli']);
        exit();
    }

    if (!$db) {
        http_response_code(503);
        echo json_encode(['error' => 'Veritabanı geçici olarak kullanılamıyor']);
        exit();
    }

    try {
        $stmt = $db->prepare("SELECT id, name, email, role, password FROM users WHERE email = ? LIMIT 1");
        $stmt->execute([$data['email']]);
        $user = $stmt->fetch();

        if ($user && password_verify($data['password'], $user['password'])) {
            // Token oluştur
            $token = bin2hex(random_bytes(32));
            $expiresAt = date('Y-m-d H:i:s', strtotime('+24 hours'));

            // Token'ı veritabanına kaydet
            $stmt = $db->prepare("INSERT INTO user_tokens (user_id, token, expires_at) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE token = VALUES(token), expires_at = VALUES(expires_at)");
            $stmt->execute([$user['id'], $token, $expiresAt]);

            $permissions = getUserPermissions($user['id']);

            http_response_code(200);
            echo json_encode([
                'success' => true,
                'token' => $token,
                'user' => [
                    'id' => $user['id'],
                    'name' => $user['name'],
                    'email' => $user['email'],
                    'role' => $user['role'],
                    'permissions' => $permissions
                ]
            ]);
            exit();
        } else {
            http_response_code(401);
            echo json_encode(['error' => 'Geçersiz email veya şifre']);
            exit();
        }

    } catch (PDOException $e) {
        error_log("Login error: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Giriş işlemi sırasında hata oluştu']);
        exit();
    }
}

// Diğer durumlar
http_response_code(400);
echo json_encode(['error' => 'Geçersiz istek']);
?>