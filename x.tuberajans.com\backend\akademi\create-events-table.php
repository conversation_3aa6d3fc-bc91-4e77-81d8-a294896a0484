<?php
/**
 * Events tablosunu oluşturmak için script
 */

ini_set('display_errors', 1);
error_reporting(E_ALL);

require_once __DIR__ . '/../config/config.php';

// CORS ayarları
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json');

try {
    // Tablo var mı kontrol et
    $stmt = $db_akademi->query("SHOW TABLES LIKE 'events'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        // Tablo yoksa oluştur
        $createTableSQL = "CREATE TABLE `events` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `title` varchar(255) NOT NULL,
            `description` text,
            `start_date` datetime NOT NULL,
            `end_date` datetime NOT NULL,
            `location` varchar(255) NOT NULL,
            `capacity` int(11) DEFAULT '0',
            `registered` int(11) DEFAULT '0',
            `thumbnail` varchar(255) DEFAULT NULL,
            `instructor` varchar(255) DEFAULT NULL,
            `category` varchar(100) DEFAULT NULL,
            `is_featured` tinyint(1) DEFAULT '0',
            `created_by` int(11) NOT NULL,
            `created_at` datetime NOT NULL,
            `updated_at` datetime NOT NULL,
            `status` enum('active','cancelled','completed') NOT NULL DEFAULT 'active',
            PRIMARY KEY (`id`),
            KEY `created_by` (`created_by`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";
        
        $db_akademi->exec($createTableSQL);
        
        // Örnek etkinlik ekle
        $insertSampleSQL = "INSERT INTO `events` 
            (`title`, `description`, `start_date`, `end_date`, `location`, `capacity`, 
            `registered`, `thumbnail`, `instructor`, `category`, `is_featured`, 
            `created_by`, `created_at`, `updated_at`, `status`) 
        VALUES 
            ('TikTok Trendleri Webinarı', 'TikTok\'ta son trendler ve içerik stratejileri hakkında bilgilendirici bir webinar.', 
            DATE_ADD(NOW(), INTERVAL 7 DAY), DATE_ADD(NOW(), INTERVAL 7 DAY + INTERVAL 2 HOUR), 
            'Online', 100, 0, '/uploads/events/default-event.jpg', 'Ahmet Yılmaz', 
            'Webinar', 1, 1, NOW(), NOW(), 'active'),
            
            ('Yayıncı Buluşması', 'Tuber Ajans yayıncıları ile yüz yüze networking etkinliği.', 
            DATE_ADD(NOW(), INTERVAL 14 DAY), DATE_ADD(NOW(), INTERVAL 14 DAY + INTERVAL 3 HOUR), 
            'İstanbul', 50, 0, '/uploads/events/default-event.jpg', 'Mehmet Demir', 
            'Buluşma', 0, 1, NOW(), NOW(), 'active'),
            
            ('İçerik Üretimi Atölyesi', 'Profesyonel içerik üretimi teknikleri hakkında uygulamalı atölye çalışması.', 
            DATE_ADD(NOW(), INTERVAL 21 DAY), DATE_ADD(NOW(), INTERVAL 21 DAY + INTERVAL 4 HOUR), 
            'Ankara', 30, 0, '/uploads/events/default-event.jpg', 'Ayşe Kaya', 
            'Atölye', 0, 1, NOW(), NOW(), 'active')";
            
        $db_akademi->exec($insertSampleSQL);
        
        echo json_encode([
            'success' => true,
            'message' => 'Events tablosu başarıyla oluşturuldu ve örnek veriler eklendi.',
            'table_created' => true
        ]);
    } else {
        // Tablo zaten var
        echo json_encode([
            'success' => true,
            'message' => 'Events tablosu zaten mevcut.',
            'table_created' => false
        ]);
        
        // Tablo yapısını kontrol et
        $stmt = $db_akademi->query("DESCRIBE events");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Etkinlik sayısını kontrol et
        $stmt = $db_akademi->query("SELECT COUNT(*) as count FROM events");
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // Örnek bir etkinlik al
        $sampleEvent = null;
        if ($count > 0) {
            $stmt = $db_akademi->query("SELECT * FROM events LIMIT 1");
            $sampleEvent = $stmt->fetch(PDO::FETCH_ASSOC);
        }
        
        echo json_encode([
            'success' => true,
            'table_exists' => true,
            'columns' => $columns,
            'event_count' => $count,
            'sample_event' => $sampleEvent
        ]);
    }
} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Veritabanı hatası: ' . $e->getMessage()
    ]);
}
