import React, { useState, useEffect, useContext } from 'react';
import { motion } from 'framer-motion';
import {
  FaBell,
  FaBullhorn,
  FaArrowLeft,
  FaPaperclip,
  FaFilePdf,
  FaImage,
  FaDownload
} from 'react-icons/fa';
import { BsCalendar3 } from 'react-icons/bs';
import { useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { SidebarContext } from '../../contexts/SidebarContext';

const Announcements: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('Tümü');
  const [selectedAnnouncement, setSelectedAnnouncement] = useState<any>(null);
  const [announcements, setAnnouncements] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const params = useParams();
  const navigate = useNavigate();
  const announcementId = params.id ? parseInt(params.id, 10) : null;

  // Sidebar context'inden isMobile ve isSidebarOpen değerlerini al
  const { isMobile, isSidebarOpen } = useContext(SidebarContext);

  // Duyuruları API'den çek
  const fetchAnnouncements = async () => {
    setLoading(true);
    setError(null);

    try {
      // Development modunda her zaman mock veri kullan
      const isDevMode = window.location.hostname === 'localhost' || 
                       window.location.hostname === '127.0.0.1' ||
                       import.meta.env.DEV;

      if (isDevMode) {
        // Mock veri yükleniyor mesajı sadece development'ta
        if (import.meta.env.DEV) {
          console.log('Development mode: Mock duyuru verileri kullanılıyor');
        }

        const mockAnnouncements = [
          {
            id: 1,
            title: 'Yeni Eğitim Programı Başlıyor!',
            content: 'TikTok içerik üretimi konusunda yeni eğitim programımız 15 Şubat\'ta başlıyor. Bu programda viral içerik üretimi, algoritma optimizasyonu ve monetizasyon stratejilerini öğreneceksiniz. Kayıtlar sınırlı sayıda olup, erken kayıt indirimi bulunmaktadır.',
            date: '10 Ocak 2025',
            category: 'Eğitim',
            type: 'Genel',
            important: true,
            created_at: '2025-01-10T10:00:00Z'
          },
          {
            id: 2,
            title: 'Topluluk Etkinliği - Canlı Yayın',
            content: 'Bu akşam saat 20:00\'da topluluk canlı yayınımızda buluşuyoruz. Sorularınızı hazırlayın! TikTok trendleri, algoritma değişiklikleri ve başarı hikayeleri hakkında konuşacağız. Katılım ücretsizdir.',
            date: '8 Ocak 2025',
            category: 'Etkinlik',
            type: 'Etkinlik',
            important: false,
            created_at: '2025-01-08T15:30:00Z'
          },
          {
            id: 3,
            title: 'Platform Güncellemesi',
            content: 'Akademi platformumuzda yeni özellikler eklendi. Artık eğitim ilerlemelerinizi takip edebilir, sertifikalarınızı indirebilir ve diğer öğrencilerle etkileşime geçebilirsiniz.',
            date: '5 Ocak 2025',
            category: 'Genel',
            type: 'Genel',
            important: false,
            created_at: '2025-01-05T09:15:00Z'
          }
        ];

        setAnnouncements(mockAnnouncements);
        setLoading(false);
        return;
      }

      // Production modunda gerçek API çağrısı
      try {
        const response = await axios.get('/backend/api/api_data.php', {
          params: {
            endpoint: 'announcements'
          },
          timeout: 10000 // 10 saniye timeout
        });

        if (response.data && response.data.status === 'success') {
          // API'den gelen verileri formatla
          const formattedAnnouncements = response.data.data.map((announcement: any) => {
            return {
              id: announcement.id,
              title: announcement.title,
              content: announcement.content,
              date: new Date(announcement.created_at).toLocaleDateString('tr-TR', {
                day: 'numeric',
                month: 'long',
                year: 'numeric'
              }),
              category: getCategoryName(announcement.category),
              type: getCategoryName(announcement.type || announcement.category),
              important: announcement.is_important === 1 || announcement.is_important === true,
              created_at: announcement.created_at
            };
          });

          setAnnouncements(formattedAnnouncements);
        } else {
          throw new Error('API yanıtı başarısız: ' + (response.data?.message || 'Bilinmeyen hata'));
        }
      } catch (apiError) {
        // API başarısız olduğunda sessizce mock veri kullan
        if (import.meta.env.DEV) {
          console.warn('API çağrısı başarısız, mock veri kullanılıyor:', apiError);
        }
        // API başarısız olduğunda mock veri kullan
        setAnnouncements([
          {
            id: 1,
            title: 'Hoş Geldiniz!',
            content: 'Tuber Akademi\'ye hoş geldiniz. Platform özellikleri hakkında duyurular burada yer alacak.',
            date: new Date().toLocaleDateString('tr-TR'),
            category: 'Sistem',
            type: 'Genel',
            important: false,
            created_at: new Date().toISOString()
          }
        ]);
      }
    } catch (err) {
      if (import.meta.env.DEV) {
        console.error('Duyurular alınırken hata oluştu:', err);
      }
      setError('Duyurular yüklenirken bir hata oluştu. Lütfen sayfayı yenileyin.');
    } finally {
      setLoading(false);
    }
  };

  // Kategori kodunu okunabilir isme dönüştür
  const getCategoryName = (categoryCode: string) => {
    switch (categoryCode) {
      case 'general': return 'Genel';
      case 'events': return 'Etkinlikler';
      case 'important': return 'Önemli';
      case 'brand': return 'Marka İş Birliği';
      default: return categoryCode;
    }
  };

  // Sayfa yüklendiğinde duyuruları çek
  useEffect(() => {
    fetchAnnouncements();
  }, []);

  // Duyuru detayını URL'den alınan ID'ye göre ayarla
  useEffect(() => {
    if (announcementId && announcements.length > 0) {
      const announcement = announcements.find(a => a.id === announcementId);
      if (announcement) {
        setSelectedAnnouncement(announcement);
      } else {
        // Eğer duyuru bulunamazsa ana listeye geri dön
        navigate('/dashboard/announcements');
      }
    } else if (!announcementId) {
      setSelectedAnnouncement(null);
    }
    // announcements.length === 0 durumunda hiçbir şey yapmıyoruz, 
    // çünkü fetchAnnouncements zaten ilk useEffect'te çağrılıyor
  }, [announcementId, navigate, announcements.length]);

  const categories = ['Tümü', 'Genel', 'Etkinlik', 'Önemli', 'Marka İş Birliği'];

  const filteredAnnouncements = selectedCategory === 'Tümü'
    ? announcements
    : announcements.filter(a => a.type === selectedCategory);

  // Eğer duyuru detayı gösterilecekse
  if (selectedAnnouncement) {
    // Mock uzun duyuru içeriği
    const mockLongContent = `
TikTok İçerik Üretimi ve Algoritma Optimizasyonu Eğitimi

Sevgili Tuber Akademi Üyeleri,

Bu kapsamlı eğitim programında, TikTok platformunda başarılı olmak için gereken tüm stratejileri ve teknikleri öğreneceksiniz. Eğitimimiz 4 ana bölümden oluşmaktadır:

**1. TikTok Algoritması Derinlemesine Analiz**
TikTok algoritmasının nasıl çalıştığını, hangi faktörlerin içerik dağıtımını etkilediğini ve algoritmanın favor ettiği içerik türlerini detaylı olarak inceleyeceğiz. Algoritma güncellemelerini takip etme yöntemleri ve değişikliklere hızlı adaptasyon stratejileri üzerinde duracağız.

**2. Viral İçerik Üretim Teknikleri**
Viral olma potansiyeli yüksek içerikler nasıl üretilir? Trend analizi, hashtag stratejileri, optimal paylaşım zamanları ve içerik formatları hakkında pratik bilgiler edineceğiz. Ayrıca, kendi nişinizde trend yaratma yöntemlerini öğreneceksiniz.

**3. Topluluk Yönetimi ve Etkileşim Artırma**
Takipçi kitlenizle güçlü bağlar kurma, yorumlara etkili yanıt verme, live yayın stratejileri ve topluluk oluşturma teknikleri. Sadık bir takipçi kitlesi oluşturmanın sırlarını keşfedeceğiz.

**4. Monetizasyon Stratejileri**
TikTok'ta para kazanmanın farklı yolları: Creator Fund, marka iş birlikleri, affiliate marketing, ürün satışı ve diğer gelir kaynakları. Profesyonel bir içerik üreticisi olma yolunda atacağınız adımları planlayacağız.

**Eğitim Detayları:**
- Süre: 6 hafta (haftada 2 saat)
- Format: Canlı online eğitim + kayıtlı videolar
- Sertifika: Eğitim sonunda katılım sertifikası
- Bonus: Özel WhatsApp grubu ve 1 aylık mentörlük desteği

**Kimler Katılabilir:**
- Yeni başlayan içerik üreticileri
- Mevcut hesabını büyütmek isteyen creators
- Marka temsilcileri ve pazarlama uzmanları
- TikTok'ta iş geliştirmek isteyen girişimciler

**Eğitmen Bilgileri:**
Eğitimimiz, TikTok'ta toplam 50M+ görüntülenme sayısına ulaşmış, birçok viral içerik üretmiş deneyimli creators ve dijital pazarlama uzmanları tarafından verilecektir.

**Kayıt ve Ücret:**
Erken kayıt fırsatı ile %30 indirim! Normal fiyat: 2.500 TL, Erken kayıt: 1.750 TL
Kayıt için akademi portalınızdan "Eğitimler" bölümüne gidiniz.

**Önemli Notlar:**
- Kontenjan sınırlıdır (maksimum 50 kişi)
- Kayıt son tarihi: 15 Şubat 2025
- Eğitim başlangıç tarihi: 1 Mart 2025
- Tüm katılımcılara özel kaynak materyalleri sağlanacaktır

Bu eğitim, TikTok kariyerinizde yeni bir sayfa açmanız için mükemmel bir fırsat. Sorularınız için <EMAIL> adresinden bizimle iletişime geçebilirsiniz.

Başarılar dileriz!
Tuber Akademi Eğitim Ekibi
    `;

    return (
      <div className="min-h-screen w-full">
        <motion.div
          className="container"
          style={{
            maxWidth: isMobile ? '100%' : 'none',
            overflowX: 'hidden'
          }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {/* Header Section */}
          <div className="bg-white dark:bg-[#16151c] rounded-xl shadow-lg overflow-hidden mb-6">
            <div className="relative">
              {/* Hero Background */}
              <div className="h-16 lg:h-20 bg-gradient-to-r from-[#FF3E71] via-[#FF5F87] to-[#FF7BA3] relative overflow-hidden">
                <div className="absolute inset-0 bg-black/20"></div>
                <div className="absolute top-2 sm:top-3 left-2 sm:left-4">
                  <button
                    onClick={() => navigate('/dashboard/announcements')}
                    className="flex items-center bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 transition-all duration-200 px-2 sm:px-3 py-1 sm:py-1.5 rounded-full text-xs sm:text-sm"
                  >
                    <FaArrowLeft className="mr-2" />
                    <span className="font-medium">Geri</span>
                  </button>
                </div>
              </div>

              {/* Content Header */}
              <div className="px-2 sm:px-4 py-4 -mt-6 relative z-10">
                <div className="bg-white dark:bg-[#16151c] rounded-xl shadow-lg p-2 sm:p-4">
                  <div className="flex flex-col">
                    <div className="flex-1">
                      <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 dark:text-white mb-2 leading-tight">
                        {selectedAnnouncement.title}
                      </h1>

                      <div className="flex flex-wrap items-center gap-2 mb-3">
                        <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                          <BsCalendar3 className="mr-1" />
                          <span>{selectedAnnouncement.date}</span>
                        </div>
                      </div>

                      <div className="flex flex-wrap gap-1 sm:gap-2">
                        {['Genel', 'Etkinlik', 'Önemli', 'Güncelleme', 'Bilgilendirme', 'Marka İş Birliği'].includes(selectedAnnouncement.type || '') && (
                          <span className={`text-xs sm:text-sm px-2 sm:px-3 py-1 rounded-full font-medium ${
                            selectedAnnouncement.type === 'Genel' ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300' :
                            selectedAnnouncement.type === 'Etkinlik' ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300' :
                            selectedAnnouncement.type === 'Önemli' ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300' :
                            selectedAnnouncement.type === 'Marka İş Birliği' ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300' :
                            selectedAnnouncement.type === 'Eğitim' ? 'bg-pink-100 dark:bg-pink-900/30 text-pink-800 dark:text-pink-300' :
                            'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300'
                          }`}>
                            {selectedAnnouncement.type}
                          </span>
                        )}

                        {selectedAnnouncement.important && selectedAnnouncement.type !== 'Önemli' && (
                          <span className="text-xs sm:text-sm px-2 sm:px-3 py-1 rounded-full font-medium bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
                            Önemli
                          </span>
                        )}

                        <span className="text-xs sm:text-sm px-2 sm:px-3 py-1 rounded-full font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                          Eğitim
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="w-full">
            {/* Content Area */}
            <div className="w-full">
              <div className="bg-white dark:bg-[#16151c] rounded-xl shadow-lg overflow-hidden">
                <div className="p-3 sm:p-6">
                  {/* Content */}
                  <div className="prose prose-lg max-w-none dark:prose-invert">
                    <div
                      className="text-gray-700 dark:text-gray-200 leading-relaxed"
                      dangerouslySetInnerHTML={{
                        __html: mockLongContent
                          .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                          .replace(/\n/g, '<br>')
                      }}
                    />
                  </div>

                  {/* Attachments Section */}
                  <div className="mt-4 sm:mt-6 pt-3 sm:pt-4 border-t border-gray-100 dark:border-gray-700">
                    <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-2 sm:mb-3 flex items-center">
                      <FaPaperclip className="mr-1 sm:mr-2 text-[#FF3E71]" />
                      Ekler
                    </h3>

                    <div className="grid grid-cols-1 gap-2 sm:gap-3">
                      {/* PDF Attachment */}
                      <div className="flex items-center p-2 sm:p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-100 dark:border-gray-700 hover:shadow-md transition-shadow">
                        <div className="flex-shrink-0 w-8 h-8 sm:w-12 sm:h-12 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center mr-2 sm:mr-4">
                          <FaFilePdf className="text-red-600 dark:text-red-400 text-lg" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className="text-xs sm:text-sm font-medium text-gray-900 dark:text-white truncate">
                            Eğitim Detayları.pdf
                          </h4>
                          <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400">2.4 MB</p>
                        </div>
                        <button className="flex-shrink-0 p-1 sm:p-2 text-gray-400 hover:text-[#FF3E71] transition-colors">
                          <FaDownload className="text-sm sm:text-lg" />
                        </button>
                      </div>

                      {/* Image Attachment */}
                      <div className="flex items-center p-2 sm:p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-100 dark:border-gray-700 hover:shadow-md transition-shadow">
                        <div className="flex-shrink-0 w-8 h-8 sm:w-12 sm:h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mr-2 sm:mr-4">
                          <FaImage className="text-blue-600 dark:text-blue-400 text-lg" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className="text-xs sm:text-sm font-medium text-gray-900 dark:text-white truncate">
                            Program Görseli.jpg
                          </h4>
                          <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400">1.8 MB</p>
                        </div>
                        <button className="flex-shrink-0 p-1 sm:p-2 text-gray-400 hover:text-[#FF3E71] transition-colors">
                          <FaDownload className="text-sm sm:text-lg" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    );
  }

  // Duyuru listesi
  return (
    <div className="min-h-screen w-full">
      <div className="container" style={{
        maxWidth: isMobile ? '100%' : (isSidebarOpen ? 'calc(100vw - 280px - 30px)' : 'calc(100vw - 78px - 30px)'),
        overflowX: 'hidden'
      }}>
        <div className="bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden mb-6">
          <div className="flex items-center justify-between px-4 py-3 border-b border-gray-100 dark:border-gray-700">
            <div className="flex items-center">
              <FaBullhorn className="text-gray-700 dark:text-gray-300 mr-2" />
              <h2 className="text-base font-semibold text-gray-800 dark:text-white">Duyurular</h2>
            </div>
            <div className="flex gap-2 overflow-x-auto" style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}>
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`whitespace-nowrap px-3 py-1 text-xs font-medium rounded-full transition-all duration-200 flex-shrink-0 ${
                    selectedCategory === category
                      ? 'bg-[#FF3E71] text-white'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>

          <div className="divide-y divide-gray-100 dark:divide-gray-700">
            {loading ? (
              <div className="text-center py-10 px-2 sm:px-4">
                <div className="w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-300 rounded-full flex items-center justify-center mb-4 animate-pulse">
                  <FaBell className="w-6 h-6" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">Yükleniyor...</h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">Bekleyin.</p>
              </div>
            ) : error ? (
              <div className="text-center py-10 px-2 sm:px-4">
                <div className="w-16 h-16 mx-auto bg-red-100 dark:bg-red-900/30 text-red-500 dark:text-red-300 rounded-full flex items-center justify-center mb-4">
                  <FaBell className="w-6 h-6" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">Hata</h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 break-words">{error}</p>
                <button
                  onClick={fetchAnnouncements}
                  className="mt-4 px-4 py-2 bg-[#FF3E71] text-white rounded-full text-sm"
                >
                  Tekrar Dene
                </button>
              </div>
            ) : filteredAnnouncements.length > 0 ? (
              filteredAnnouncements.map((announcement) => (
                <div
                  key={announcement.id}
                  className="p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors cursor-pointer"
                  onClick={() => navigate(`/dashboard/announcements/${announcement.id}`)}
                >
                  <div className="flex items-start">
                    <div className="min-w-[4px] h-16 bg-[#FF3E71] rounded-full mr-3"></div>
                    <div className="flex-1 overflow-hidden">
                      <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mb-1">
                        <BsCalendar3 className="text-gray-500 dark:text-gray-400 mr-1 text-xs" />
                        <span>{announcement.date}</span>
                        {['Genel', 'Etkinlik', 'Önemli', 'Marka İş Birliği'].includes(announcement.type || '') && (
                          <span className={`ml-2 px-2 py-0.5 rounded-full text-xs font-semibold ${
                            announcement.type === 'Genel' ? 'bg-blue-100 text-blue-700' :
                            announcement.type === 'Etkinlik' ? 'bg-green-100 text-green-700' :
                            announcement.type === 'Önemli' ? 'bg-red-100 text-red-700' :
                            announcement.type === 'Marka İş Birliği' ? 'bg-purple-100 text-purple-700' : ''
                          }`}>
                            {announcement.type}
                          </span>
                        )}
                      </div>
                      <h3 className="text-sm font-medium mb-1 text-gray-800 dark:text-white truncate">{announcement.title}</h3>
                      <p className="text-xs text-gray-500 dark:text-gray-400 line-clamp-2">{announcement.content}</p>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-10 px-2 sm:px-4">
                <div className="w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-300 rounded-full flex items-center justify-center mb-4">
                  <FaBell className="w-6 h-6" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">Duyuru Yok</h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">Bu kategoride duyuru yok.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Announcements;