<?php
// Ya<PERSON><PERSON>landırma dosyasını dahil et
require_once __DIR__ . '/../config/config.php';

// Bildirim tetikleyicilerini dahil et
require_once __DIR__ . '/notification_triggers.php';

// CORS başlıkları
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Origin, Content-Type, Accept, Authorization');

// Zaman aşımını önlemek için
set_time_limit(60); // 60 saniye

// Debug log
error_log("api_data.php called with method: " . $_SERVER['REQUEST_METHOD'] . ", endpoint: " . (isset($_GET['endpoint']) ? $_GET['endpoint'] : 'none'));

// OPTIONS isteği için erken yanıt
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(204);
    exit;
}

// Veritabanı bağlantısını al
$db = getDB();

// İstek parametrelerini al
$endpoint = isset($_GET['endpoint']) ? $_GET['endpoint'] : '';
$method = $_SERVER['REQUEST_METHOD'];

// Endpoint'e göre işlem yap
switch ($endpoint) {
    case 'announcements':
        handleAnnouncements($db, $method);
        break;
    case 'feed':
        handleFeed($db, $method);
        break;
    case 'courses':
        handleCourses($db, $method);
        break;
    case 'events':
        handleEvents($db, $method);
        break;
    case 'like_post':
        handleLikePost($db, $method);
        break;
    case 'create_post':
        handleCreatePost($db, $method);
        break;
    case 'notifications':
        include_once __DIR__ . '/notifications.php';
        break;
    default:
        http_response_code(404);
        echo json_encode([
            'status' => 'error',
            'message' => 'Endpoint bulunamadı: ' . $endpoint
        ]);
        break;
}

// Duyurular endpoint'i
function handleAnnouncements($db, $method) {
    if ($method === 'GET') {
        try {
            // Tablo varlığını kontrol et
            $checkTableQuery = "SHOW TABLES LIKE 'announcements'";
            $checkTableStmt = $db->prepare($checkTableQuery);
            $checkTableStmt->execute();

            if ($checkTableStmt->rowCount() === 0) {
                throw new Exception("'announcements' tablosu veritabanında bulunamadı");
            }

            // Tablo yapısını kontrol et
            $checkColumnsQuery = "SHOW COLUMNS FROM announcements";
            $checkColumnsStmt = $db->prepare($checkColumnsQuery);
            $checkColumnsStmt->execute();
            $columns = $checkColumnsStmt->fetchAll(PDO::FETCH_COLUMN);

            // Gerekli sütunları kontrol et
            $requiredColumns = ['id', 'title', 'content', 'status', 'created_at'];
            $missingColumns = array_diff($requiredColumns, $columns);

            if (!empty($missingColumns)) {
                throw new Exception("'announcements' tablosunda eksik sütunlar: " . implode(', ', $missingColumns));
            }

            // Veri çek
            $query = "SELECT * FROM announcements WHERE status = 'active' ORDER BY created_at DESC";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $announcements = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if (empty($announcements)) {
                // Veri yoksa boş dizi döndür ama hata değil
                echo json_encode([
                    'status' => 'success',
                    'data' => [],
                    'message' => 'Aktif duyuru bulunamadı'
                ]);
                return;
            }

            echo json_encode([
                'status' => 'success',
                'data' => $announcements
            ]);
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Veritabanı hatası: ' . $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    } else if ($method === 'POST') {
        try {
            // JSON verisini al
            $data = json_decode(file_get_contents('php://input'), true);

            // Gerekli alanları kontrol et
            if (!isset($data['title']) || !isset($data['content']) || !isset($data['type'])) {
                http_response_code(400);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Eksik parametreler: title, content ve type gerekli'
                ]);
                return;
            }

            // Duyuruyu ekle
            $sql = "INSERT INTO announcements (
                title, content, type, status, image, created_by, created_at, updated_at, expire_date
            ) VALUES (
                :title, :content, :type, :status, :image, :created_by, NOW(), NOW(), :expire_date
            )";

            $stmt = $db->prepare($sql);
            $stmt->bindParam(':title', $data['title'], PDO::PARAM_STR);
            $stmt->bindParam(':content', $data['content'], PDO::PARAM_STR);
            $stmt->bindParam(':type', $data['type'], PDO::PARAM_STR);
            $stmt->bindParam(':status', $data['status'] ?? 'active', PDO::PARAM_STR);
            $stmt->bindParam(':image', $data['image'] ?? null, PDO::PARAM_STR);
            $stmt->bindParam(':created_by', $data['created_by'] ?? 1, PDO::PARAM_INT);
            $stmt->bindParam(':expire_date', $data['expire_date'] ?? null, PDO::PARAM_STR);
            $stmt->execute();

            $announcement_id = $db->lastInsertId();

            // Bildirim oluştur
            createAnnouncementNotification(
                $announcement_id,
                $data['title'],
                $data['content'],
                $data['type'],
                $data['created_by'] ?? 1
            );

            echo json_encode([
                'status' => 'success',
                'message' => 'Duyuru başarıyla oluşturuldu',
                'id' => $announcement_id
            ]);
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Duyuru oluşturulurken hata oluştu: ' . $e->getMessage()
            ]);
        }
    } else {
        http_response_code(405);
        echo json_encode([
            'status' => 'error',
            'message' => 'Desteklenmeyen metod: ' . $method
        ]);
    }
}

// Akış endpoint'i
function handleFeed($db, $method) {
    if ($method === 'GET') {
        try {
            // Tablo varlığını kontrol et
            $checkTableQuery = "SHOW TABLES LIKE 'feed_posts'";
            $checkTableStmt = $db->prepare($checkTableQuery);
            $checkTableStmt->execute();

            if ($checkTableStmt->rowCount() === 0) {
                throw new Exception("'feed_posts' tablosu veritabanında bulunamadı");
            }

            // Tablo yapısını kontrol et
            $checkColumnsQuery = "SHOW COLUMNS FROM feed_posts";
            $checkColumnsStmt = $db->prepare($checkColumnsQuery);
            $checkColumnsStmt->execute();
            $columns = $checkColumnsStmt->fetchAll(PDO::FETCH_COLUMN);

            // Gerekli sütunları kontrol et
            $requiredColumns = ['id', 'user_id', 'content', 'created_at'];
            $missingColumns = array_diff($requiredColumns, $columns);

            if (!empty($missingColumns)) {
                throw new Exception("'feed_posts' tablosunda eksik sütunlar: " . implode(', ', $missingColumns));
            }

            // Tablo varlığını kontrol et
            $checkLikesTableQuery = "SHOW TABLES LIKE 'feed_likes'";
            $checkLikesTableStmt = $db->prepare($checkLikesTableQuery);
            $checkLikesTableStmt->execute();

            if ($checkLikesTableStmt->rowCount() === 0) {
                // feed_likes tablosu yoksa oluştur
                $createLikesTableSql = "CREATE TABLE IF NOT EXISTS `feed_likes` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `post_id` int(11) NOT NULL,
                    `user_id` int(11) NOT NULL,
                    `created_at` datetime NOT NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `post_user_unique` (`post_id`, `user_id`),
                    KEY `post_id` (`post_id`),
                    KEY `user_id` (`user_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";

                $db->exec($createLikesTableSql);

                // Yabancı anahtar kısıtlamaları ekle
                try {
                    $alterTableSql = "ALTER TABLE `feed_likes`
                        ADD CONSTRAINT `feed_likes_post_id_fk` FOREIGN KEY (`post_id`) REFERENCES `feed_posts` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
                        ADD CONSTRAINT `feed_likes_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;";

                    $db->exec($alterTableSql);
                } catch (PDOException $e) {
                    // Yabancı anahtar eklenirken hata olursa devam et
                    // Bu genellikle kısıtlamalar zaten varsa olur
                }
            }

            // Tablo varlığını kontrol et
            $checkCommentsTableQuery = "SHOW TABLES LIKE 'feed_comments'";
            $checkCommentsTableStmt = $db->prepare($checkCommentsTableQuery);
            $checkCommentsTableStmt->execute();

            if ($checkCommentsTableStmt->rowCount() === 0) {
                // feed_comments tablosu yoksa oluştur
                $createCommentsTableSql = "CREATE TABLE IF NOT EXISTS `feed_comments` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `post_id` int(11) NOT NULL,
                    `user_id` int(11) NOT NULL,
                    `content` text NOT NULL,
                    `created_at` datetime NOT NULL,
                    `updated_at` datetime NOT NULL,
                    PRIMARY KEY (`id`),
                    KEY `post_id` (`post_id`),
                    KEY `user_id` (`user_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";

                $db->exec($createCommentsTableSql);

                // Yabancı anahtar kısıtlamaları ekle
                try {
                    $alterTableSql = "ALTER TABLE `feed_comments`
                        ADD CONSTRAINT `feed_comments_post_id_fk` FOREIGN KEY (`post_id`) REFERENCES `feed_posts` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
                        ADD CONSTRAINT `feed_comments_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;";

                    $db->exec($alterTableSql);
                } catch (PDOException $e) {
                    // Yabancı anahtar eklenirken hata olursa devam et
                }
            }

            // Kullanıcı ID'sini al (varsa)
            $user_id = isset($_GET['user_id']) ? intval($_GET['user_id']) : 1;

            // Veri çek
            $query = "SELECT p.*, u.username, u.profile_image, u.role,
                     (SELECT COUNT(*) FROM feed_likes WHERE post_id = p.id) as likes_count,
                     (SELECT COUNT(*) FROM feed_comments WHERE post_id = p.id) as comments_count,
                     (SELECT COUNT(*) FROM feed_likes WHERE post_id = p.id AND user_id = :user_id) as is_liked
                     FROM feed_posts p
                     LEFT JOIN users u ON p.user_id = u.id
                     ORDER BY p.created_at DESC";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->execute();
            $posts = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if (empty($posts)) {
                // Veri yoksa boş dizi döndür ama hata değil
                echo json_encode([
                    'status' => 'success',
                    'data' => [],
                    'message' => 'Akış gönderisi bulunamadı'
                ]);
                return;
            }

            echo json_encode([
                'status' => 'success',
                'data' => $posts
            ]);
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Veritabanı hatası: ' . $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    } else {
        http_response_code(405);
        echo json_encode([
            'status' => 'error',
            'message' => 'Desteklenmeyen metod: ' . $method
        ]);
    }
}

// Eğitimler endpoint'i
function handleCourses($db, $method) {
    if ($method === 'GET') {
        try {
            $query = "SELECT * FROM courses WHERE status = 'active' ORDER BY created_at DESC";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);

            echo json_encode([
                'status' => 'success',
                'data' => $courses
            ]);
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Eğitimler alınırken hata oluştu: ' . $e->getMessage()
            ]);
        }
    } else if ($method === 'POST') {
        try {
            // JSON verisini al
            $data = json_decode(file_get_contents('php://input'), true);

            // Gerekli alanları kontrol et
            if (!isset($data['title']) || !isset($data['description']) || !isset($data['instructor'])) {
                http_response_code(400);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Eksik parametreler: title, description ve instructor gerekli'
                ]);
                return;
            }

            // Eğitimi ekle
            $sql = "INSERT INTO courses (
                title, description, instructor, thumbnail, category, duration, level, status, created_by, created_at, updated_at
            ) VALUES (
                :title, :description, :instructor, :thumbnail, :category, :duration, :level, :status, :created_by, NOW(), NOW()
            )";

            $stmt = $db->prepare($sql);
            $stmt->bindParam(':title', $data['title'], PDO::PARAM_STR);
            $stmt->bindParam(':description', $data['description'], PDO::PARAM_STR);
            $stmt->bindParam(':instructor', $data['instructor'], PDO::PARAM_STR);
            $stmt->bindParam(':thumbnail', $data['thumbnail'] ?? null, PDO::PARAM_STR);
            $stmt->bindParam(':category', $data['category'] ?? 'genel', PDO::PARAM_STR);
            $stmt->bindParam(':duration', $data['duration'] ?? null, PDO::PARAM_STR);
            $stmt->bindParam(':level', $data['level'] ?? 'beginner', PDO::PARAM_STR);
            $stmt->bindParam(':status', $data['status'] ?? 'active', PDO::PARAM_STR);
            $stmt->bindParam(':created_by', $data['created_by'] ?? 1, PDO::PARAM_INT);
            $stmt->execute();

            $course_id = $db->lastInsertId();

            // Bildirim oluştur
            createCourseNotification(
                $course_id,
                $data['title'],
                $data['description'],
                $data['created_by'] ?? 1
            );

            echo json_encode([
                'status' => 'success',
                'message' => 'Eğitim başarıyla oluşturuldu',
                'id' => $course_id
            ]);
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Eğitim oluşturulurken hata oluştu: ' . $e->getMessage()
            ]);
        }
    } else {
        http_response_code(405);
        echo json_encode([
            'status' => 'error',
            'message' => 'Desteklenmeyen metod: ' . $method
        ]);
    }
}

// Etkinlikler endpoint'i
function handleEvents($db, $method) {
    if ($method === 'GET') {
        try {
            $query = "SELECT * FROM events WHERE status = 'active' ORDER BY start_date ASC";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $events = $stmt->fetchAll(PDO::FETCH_ASSOC);

            echo json_encode([
                'status' => 'success',
                'data' => $events
            ]);
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Etkinlikler alınırken hata oluştu: ' . $e->getMessage()
            ]);
        }
    } else if ($method === 'POST') {
        try {
            // JSON verisini al
            $data = json_decode(file_get_contents('php://input'), true);

            // Gerekli alanları kontrol et
            if (!isset($data['title']) || !isset($data['description']) || !isset($data['start_date']) || !isset($data['end_date']) || !isset($data['location'])) {
                http_response_code(400);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Eksik parametreler: title, description, start_date, end_date ve location gerekli'
                ]);
                return;
            }

            // Etkinliği ekle
            $sql = "INSERT INTO events (
                title, description, start_date, end_date, location, capacity, registered, thumbnail, instructor, category, is_featured, created_by, created_at, updated_at, status
            ) VALUES (
                :title, :description, :start_date, :end_date, :location, :capacity, :registered, :thumbnail, :instructor, :category, :is_featured, :created_by, NOW(), NOW(), :status
            )";

            $stmt = $db->prepare($sql);
            $stmt->bindParam(':title', $data['title'], PDO::PARAM_STR);
            $stmt->bindParam(':description', $data['description'], PDO::PARAM_STR);
            $stmt->bindParam(':start_date', $data['start_date'], PDO::PARAM_STR);
            $stmt->bindParam(':end_date', $data['end_date'], PDO::PARAM_STR);
            $stmt->bindParam(':location', $data['location'], PDO::PARAM_STR);
            $stmt->bindParam(':capacity', $data['capacity'] ?? 0, PDO::PARAM_INT);
            $stmt->bindParam(':registered', $data['registered'] ?? 0, PDO::PARAM_INT);
            $stmt->bindParam(':thumbnail', $data['thumbnail'] ?? null, PDO::PARAM_STR);
            $stmt->bindParam(':instructor', $data['instructor'] ?? null, PDO::PARAM_STR);
            $stmt->bindParam(':category', $data['category'] ?? null, PDO::PARAM_STR);
            $stmt->bindParam(':is_featured', $data['is_featured'] ?? 0, PDO::PARAM_INT);
            $stmt->bindParam(':created_by', $data['created_by'] ?? 1, PDO::PARAM_INT);
            $stmt->bindParam(':status', $data['status'] ?? 'active', PDO::PARAM_STR);
            $stmt->execute();

            $event_id = $db->lastInsertId();

            // Bildirim oluştur
            createEventNotification(
                $event_id,
                $data['title'],
                $data['description'],
                $data['start_date'],
                $data['created_by'] ?? 1
            );

            echo json_encode([
                'status' => 'success',
                'message' => 'Etkinlik başarıyla oluşturuldu',
                'id' => $event_id
            ]);
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Etkinlik oluşturulurken hata oluştu: ' . $e->getMessage()
            ]);
        }
    } else {
        http_response_code(405);
        echo json_encode([
            'status' => 'error',
            'message' => 'Desteklenmeyen metod: ' . $method
        ]);
    }
}

// Gönderi beğenme endpoint'i
function handleLikePost($db, $method) {
    if ($method === 'POST') {
        $data = json_decode(file_get_contents('php://input'), true);
        $post_id = isset($data['post_id']) ? $data['post_id'] : null;
        $user_id = isset($data['user_id']) ? $data['user_id'] : null;

        if (!$post_id || !$user_id) {
            http_response_code(400);
            echo json_encode([
                'status' => 'error',
                'message' => 'Eksik parametreler: post_id ve user_id gerekli'
            ]);
            return;
        }

        try {
            // Beğeni var mı kontrol et
            $checkQuery = "SELECT id FROM feed_likes WHERE post_id = :post_id AND user_id = :user_id";
            $checkStmt = $db->prepare($checkQuery);
            $checkStmt->bindParam(':post_id', $post_id);
            $checkStmt->bindParam(':user_id', $user_id);
            $checkStmt->execute();

            if ($checkStmt->rowCount() > 0) {
                // Beğeni varsa kaldır
                $deleteQuery = "DELETE FROM feed_likes WHERE post_id = :post_id AND user_id = :user_id";
                $deleteStmt = $db->prepare($deleteQuery);
                $deleteStmt->bindParam(':post_id', $post_id);
                $deleteStmt->bindParam(':user_id', $user_id);
                $deleteStmt->execute();

                echo json_encode([
                    'status' => 'success',
                    'message' => 'Beğeni kaldırıldı',
                    'action' => 'unlike'
                ]);
            } else {
                // Beğeni yoksa ekle
                $insertQuery = "INSERT INTO feed_likes (post_id, user_id, created_at) VALUES (:post_id, :user_id, NOW())";
                $insertStmt = $db->prepare($insertQuery);
                $insertStmt->bindParam(':post_id', $post_id);
                $insertStmt->bindParam(':user_id', $user_id);
                $insertStmt->execute();

                echo json_encode([
                    'status' => 'success',
                    'message' => 'Gönderi beğenildi',
                    'action' => 'like'
                ]);
            }
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Beğeni işlemi sırasında hata oluştu: ' . $e->getMessage()
            ]);
        }
    } else {
        http_response_code(405);
        echo json_encode([
            'status' => 'error',
            'message' => 'Desteklenmeyen metod: ' . $method
        ]);
    }
}

// Gönderi oluşturma endpoint'i
function handleCreatePost($db, $method) {
    if ($method === 'POST') {
        $user_id = isset($_POST['user_id']) ? $_POST['user_id'] : null;
        $content = isset($_POST['content']) ? $_POST['content'] : null;

        if (!$user_id || !$content) {
            http_response_code(400);
            echo json_encode([
                'status' => 'error',
                'message' => 'Eksik parametreler: user_id ve content gerekli'
            ]);
            return;
        }

        try {
            $media_url = null;

            // Medya dosyası yükleme
            if (isset($_FILES['media']) && $_FILES['media']['error'] === UPLOAD_ERR_OK) {
                $upload_dir = $_SERVER['DOCUMENT_ROOT'] . '/uploads/feed/';

                // Dizin yoksa oluştur
                if (!file_exists($upload_dir)) {
                    mkdir($upload_dir, 0777, true);
                }

                $file_name = time() . '_' . basename($_FILES['media']['name']);
                $upload_file = $upload_dir . $file_name;

                if (move_uploaded_file($_FILES['media']['tmp_name'], $upload_file)) {
                    $media_url = '/uploads/feed/' . $file_name;
                }
            }

            // Gönderiyi veritabanına ekle
            $query = "INSERT INTO feed_posts (user_id, content, media_url, created_at, updated_at)
                     VALUES (:user_id, :content, :media_url, NOW(), NOW())";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->bindParam(':content', $content);
            $stmt->bindParam(':media_url', $media_url);
            $stmt->execute();

            $post_id = $db->lastInsertId();

            echo json_encode([
                'status' => 'success',
                'message' => 'Gönderi başarıyla oluşturuldu',
                'post_id' => $post_id
            ]);
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Gönderi oluşturulurken hata oluştu: ' . $e->getMessage()
            ]);
        }
    } else {
        http_response_code(405);
        echo json_encode([
            'status' => 'error',
            'message' => 'Desteklenmeyen metod: ' . $method
        ]);
    }
}