<?php
require_once __DIR__ . '/../config/config.php';

header('Content-Type: application/json; charset=utf-8');

// CORS başlıkları
header('Access-Control-Allow-Origin: https://akademi.tuberajans.com');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // Development modunda mock veri döndür
    $isDevMode = isset($_GET['dev']) || 
                 (isset($_SERVER['HTTP_HOST']) && strpos($_SERVER['HTTP_HOST'], 'localhost') !== false);
    
    if ($isDevMode) {
        echo json_encode([
            'success' => true,
            'data' => [
                'id' => 1,
                'name' => 'Test Kullanıcı',
                'username' => 'test_user',
                'email' => '<EMAIL>',
                'profile_image' => null,
                'avatar_url' => null,
                'is_verified' => false,
                'role' => 'user'
            ]
        ]);
        exit;
    }
    
    // Session'dan kullanıcı bilgilerini al
    $userId = null;

    // Debug: Session durumunu logla
    error_log('User Info API: Session durumu - user_id: ' . (isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'yok') .
              ', tiktok_user: ' . (isset($_SESSION['tiktok_user']) ? 'var' : 'yok'));

    // Önce normal session'ı kontrol et
    if (isset($_SESSION['user_id']) && !empty($_SESSION['user_id'])) {
        $userId = $_SESSION['user_id'];
        error_log('User Info API: Normal session kullanılıyor - user_id: ' . $userId);
    }
    // Eğer normal session yoksa ama TikTok session'ı varsa, otomatik giriş yap
    elseif (isset($_SESSION['tiktok_user']['user_id']) && !empty($_SESSION['tiktok_user']['user_id'])) {
        $tiktok_user_id = $_SESSION['tiktok_user']['user_id'];
        error_log('User Info API: TikTok session bulundu - tiktok_user_id: ' . $tiktok_user_id);

        // Veritabanından kullanıcıyı kontrol et
        $stmt = $db->prepare("SELECT id, username, name FROM users WHERE id = ?");
        $stmt->execute([$tiktok_user_id]);
        $db_user = $stmt->fetch();

        if ($db_user) {
            // Normal session'ı oluştur
            $_SESSION['user_id'] = $db_user['id'];
            $_SESSION['user_name'] = $db_user['name'] ?? $db_user['username'] ?? 'TikTok Kullanıcısı';
            $_SESSION['username'] = $db_user['username'] ?? 'tiktok_user_' . $db_user['id'];
            $userId = $db_user['id'];

            error_log('User Info API: Otomatik giriş yapıldı - user_id: ' . $userId);
        } else {
            error_log('User Info API: TikTok user_id ile kullanıcı bulunamadı: ' . $tiktok_user_id);
        }
    } else {
        error_log('User Info API: Hiçbir session bulunamadı');
    }

    if ($userId) {
        // Akademi veritabanından kullanıcı bilgilerini çek
        $stmt = $db->prepare("
            SELECT 
                id, 
                username, 
                name, 
                email, 
                profile_image, 
                role, 
                tiktok_username,
                tiktok_open_id,
                tiktok_avatar_url,
                is_verified,
                tiktok_display_name,
                tiktok_bio,
                follower_count,
                following_count,
                likes_count,
                video_count,
                is_agency_publisher,
                publisher_verified_at,
                status
            FROM users 
            WHERE id = ? AND status = 'active'
        ");
        $stmt->execute([$userId]);
        $user = $stmt->fetch();

        if ($user) {
            // Avatar URL'sini tam path yap - TikTok'tan gelen avatar'ı öncelikle kullan
            $avatar_url = null;

            // Eğer TikTok bağlantısı varsa, TikTok avatar'ını kullan
            if ($user['tiktok_open_id'] && $user['tiktok_avatar_url']) {
                $avatar_url = $user['tiktok_avatar_url'];
            }
            // TikTok avatar yoksa, profil resmini kullan
            elseif ($user['profile_image']) {
                $avatar_url = '/backend/' . $user['profile_image'];
            }

            $user['avatar_url'] = $avatar_url;
            
            // Hassas bilgileri temizle
            unset($user['password']);
            unset($user['auth_token']);
            unset($user['auth_token_expires_at']);
            unset($user['access_token']);
            unset($user['refresh_token']);
            unset($user['token_expires_at']);
            
            echo json_encode([
                'success' => true,
                'data' => $user
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Kullanıcı bulunamadı'
            ]);
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Oturum bulunamadı'
        ]);
    }
    
} catch (Exception $e) {
    error_log('User Info API error: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Bir hata oluştu: ' . $e->getMessage()
    ]);
}
?> 