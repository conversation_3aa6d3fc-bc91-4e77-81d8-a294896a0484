<?php
// Bu bir proxy dosyasıdır - auth.php için tüm istekleri iletir
// CORS sorunlarını çözmek için

// Önce CORS başlıklarını ayarla (her türlü istekte)
header("Content-Type: application/json; charset=UTF-8");
$origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';
error_log("Proxy-Auth: İstek origin: " . $origin);

// Tüm domainlere izin ver (geliştirme ve test aşaması için)
header("Access-Control-Allow-Origin: " . $origin);
header("Access-Control-Allow-Credentials: true");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Origin, Content-Type, X-Auth-Token, Authorization, X-Requested-With, Accept, X-DB-Schema, X-Web-Search-Enabled, X-Continuous-Learning, Cache-Control");
header("Access-Control-Max-Age: 3600");

// OPTIONS isteklerini hemen yanıtla ve bitir
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    error_log("Proxy-Auth: OPTIONS isteği - 204 No Content yanıtı gönderiliyor");
    http_response_code(204);
    exit();
}

// Config.php dosyasını dahil et
require_once __DIR__ . '/../config/config.php';

// Giriş işlemi
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isset($_GET['action'])) {
    error_log("Proxy-Auth: Giriş isteği alındı");
    
    // Gelen JSON verisini al
    $rawInput = file_get_contents('php://input');
    $data = json_decode($rawInput, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        http_response_code(400);
        echo json_encode(['error' => 'Geçersiz JSON formatı']);
        exit();
    }
    
    if (!isset($data['email']) || !isset($data['password'])) {
        http_response_code(400);
        echo json_encode(['error' => 'E-posta ve şifre gerekli']);
        exit();
    }
    
    try {
        // E-posta adresini lowercase'e çevirelim (case-insensitive karşılaştırma için)
        $email = strtolower(trim($data['email']));
        
        // Kullanıcıyı kontrol et
        $stmt = $db->prepare("SELECT id, email, password, role, name FROM users WHERE LOWER(email) = LOWER(?)");
        $stmt->execute([$email]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user && password_verify($data['password'], $user['password'])) {
            // Token oluştur
            $token = bin2hex(random_bytes(32));
            $expires = date('Y-m-d H:i:s', strtotime('+24 hours'));
            
            // Token'ı kaydet
            $stmt = $db->prepare("INSERT INTO user_tokens (user_id, token, expires_at) VALUES (?, ?, ?)");
            $stmt->execute([$user['id'], $token, $expires]);
            
            // Başarılı yanıt
            echo json_encode([
                'success' => true,
                'token' => $token,
                'expires_at' => $expires,
                'user' => [
                    'id' => $user['id'],
                    'email' => $user['email'],
                    'role' => $user['role'],
                    'name' => $user['name']
                ]
            ]);
        } else {
            http_response_code(401);
            echo json_encode(['error' => 'Geçersiz kullanıcı adı veya şifre']);
        }
    } catch (PDOException $e) {
        error_log("Proxy-Auth: Veritabanı hatası: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Sunucu hatası']);
    }
    exit();
}

// Auth kontrolü
if (isset($_GET['check']) && $_GET['check'] === 'true') {
    error_log("Proxy-Auth: Auth kontrol isteği alındı");
    
    $token = getBearerToken();
    
    if ($token) {
        try {
            $stmt = $db->prepare("
                SELECT u.id, u.name, u.email, u.role 
                FROM users u
                JOIN user_tokens t ON u.id = t.user_id
                WHERE t.token = ? AND t.expires_at > NOW()
            ");
            $stmt->execute([$token]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user) {
                echo json_encode([
                    'authenticated' => true,
                    'user' => $user
                ]);
            } else {
                echo json_encode([
                    'authenticated' => false,
                    'message' => 'Oturum bulunamadı veya süresi doldu'
                ]);
            }
        } catch (PDOException $e) {
            error_log("Proxy-Auth: Veritabanı hatası: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Sunucu hatası']);
        }
    } else {
        echo json_encode([
            'authenticated' => false,
            'message' => 'Oturum bulunamadı veya süresi doldu'
        ]);
    }
    exit();
}

// Çıkış yap
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_GET['action']) && $_GET['action'] === 'logout') {
    error_log("Proxy-Auth: Çıkış isteği alındı");
    
    $token = getBearerToken();
    
    if ($token) {
        try {
            $stmt = $db->prepare("DELETE FROM user_tokens WHERE token = ?");
            $stmt->execute([$token]);
            echo json_encode(['success' => true, 'message' => 'Çıkış başarılı']);
        } catch (PDOException $e) {
            error_log("Proxy-Auth: Veritabanı hatası: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Sunucu hatası']);
        }
    } else {
        http_response_code(401);
        echo json_encode(['error' => 'Token gerekli']);
    }
    exit();
}

// Diğer durumlar için
http_response_code(404);
echo json_encode(['error' => 'Geçersiz istek']);

// Bearer token'ı header'dan çeken yardımcı fonksiyon
function getBearerToken() {
    $headers = null;
    if (isset($_SERVER['Authorization'])) {
        $headers = trim($_SERVER["Authorization"]);
    } else if (isset($_SERVER['HTTP_AUTHORIZATION'])) { // Nginx veya fast CGI
        $headers = trim($_SERVER["HTTP_AUTHORIZATION"]);
    } elseif (function_exists('apache_request_headers')) {
        $requestHeaders = apache_request_headers();
        // Anahtarlar büyük/küçük harf duyarlı olabilir
        $requestHeaders = array_combine(array_map('ucwords', array_keys($requestHeaders)), array_values($requestHeaders));
        if (isset($requestHeaders['Authorization'])) {
            $headers = trim($requestHeaders['Authorization']);
        }
    }
    // Bearer token'ı ayıkla
    if (!empty($headers)) {
        if (preg_match('/Bearer\s(\S+)/', $headers, $matches)) {
            return $matches[1];
        }
    }
    return null;
}