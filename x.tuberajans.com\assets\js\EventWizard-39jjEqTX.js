import{j as e}from"./reactDnd-CIvPAkL_.js";import{r as u,u as W,h as J}from"./vendor-CnpYymF8.js";import{h as j}from"./utils-CtuI0RRe.js";import{I as Q,j as U,C as X}from"./App-DHTOkUiy.js";import{P as Z}from"./ThemeStyles-Ct0n9BwN.js";import{a as ee,c as ae}from"./event-api-Wf_PSqc4.js";import{m as n,v as b,I as z,a3 as m,_ as ie,K as re,Y as I,a7 as te,T as le,s as P,l as se,a8 as A,i as _}from"./antd-gS---Efz.js";import{R as ne}from"./ArrowLeftOutlined-bXnXDBcV.js";import"./index-Bso3pfdw.js";import"./charts-CXWFy-zF.js";import"./createLucideIcon-DxVmGoQf.js";var oe={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M869 487.8L491.2 159.9c-2.9-2.5-6.6-3.9-10.5-3.9h-88.5c-7.4 0-10.8 9.2-5.2 14l350.2 304H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h585.1L386.9 854c-5.6 4.9-2.2 14 5.2 14h91.5c1.9 0 3.8-.7 5.2-2L869 536.2a32.07 32.07 0 000-48.4z"}}]},name:"arrow-right",theme:"outlined"};function D(){return D=Object.assign?Object.assign.bind():function(l){for(var c=1;c<arguments.length;c++){var y=arguments[c];for(var f in y)Object.prototype.hasOwnProperty.call(y,f)&&(l[f]=y[f])}return l},D.apply(this,arguments)}const ce=(l,c)=>u.createElement(Q,D({},l,{ref:c,icon:oe})),ue=u.forwardRef(ce),{RangePicker:de}=re,{Option:h}=P,{Title:me,Text:M,Paragraph:E}=le,{Step:he}=A,{TextArea:O}=z,Te=()=>{const[l,c]=u.useState(0),[y,f]=u.useState(!1),[p,H]=u.useState([]),[o,w]=u.useState([]),[r,S]=u.useState({etkinlik_tipi:"lider_tablosu",kayit_uygunlugu:"otomatik",puan_sistemi:"elmas"}),[d]=n.useForm(),B=W(),T=J(),{darkMode:i}=U();u.useEffect(()=>{var a;if((a=T.state)!=null&&a.prefillData){const t=T.state.prefillData;if(S(s=>({...s,...t})),t.baslangic_tarihi&&t.bitis_tarihi){const s=[j(t.baslangic_tarihi),j(t.bitis_tarihi)];d.setFieldsValue({...t,tarih_araligi:s})}else d.setFieldsValue(t);t.yayincilar&&Array.isArray(t.yayincilar)&&w(t.yayincilar)}},[T.state]),u.useEffect(()=>{N()},[]);const N=async()=>{try{const a=await ee();H(Array.isArray(a)?a:[])}catch(a){b.error("Yayıncı verileri alınamadı")}},C=(a,t)=>{S({...r,...a})},K=()=>{d.validateFields().then(()=>{c(l+1)}).catch(a=>{})},F=()=>{c(l-1)},R=async()=>{var a,t;try{f(!0);const s=await d.validateFields();if(!s.tarih_araligi||!s.tarih_araligi[0]||!s.tarih_araligi[1]){b.error("Etkinlik tarihleri seçilmelidir"),c(0);return}let Y=[];if(r.etkinlik_tipi==="pk_turnuva"&&o.length%2===0&&o.length>0)for(let k=0;k<o.length;k+=2)Y.push({yayinci1_username:(a=p.find(g=>g.id===o[k]))==null?void 0:a.username,yayinci2_username:(t=p.find(g=>g.id===o[k+1]))==null?void 0:t.username,planlanan_zaman:s.tarih_araligi[0].format("YYYY-MM-DD HH:mm:ss")});const q={...r,...s,yayincilar:o.map(k=>{var g;return(g=p.find(G=>G.id===k))==null?void 0:g.username}),tarih_araligi:[s.tarih_araligi[0].format("YYYY-MM-DD HH:mm:ss"),s.tarih_araligi[1].format("YYYY-MM-DD HH:mm:ss")],...Y.length>0?{eslesme_bilgileri:Y}:{}};await ae(q),b.success("Etkinlik başarıyla oluşturuldu!"),B("/events")}catch(s){b.error("Etkinlik oluşturulurken bir hata oluştu")}finally{f(!1)}},V=p.map(a=>({key:a.id,title:a.isim_soyisim,description:`@${a.username}`,chosen:!1})),$=a=>{w(a)},x={primary:{backgroundColor:"#1890ff",borderColor:"#1890ff",color:"#fff"},light:{backgroundColor:i?"#1f1f1f":"#fff",borderColor:i?"#434343":"#d9d9d9",color:i?"#fff":"rgba(0, 0, 0, 0.85)"}},L=()=>({components:{DatePicker:{colorPrimary:"#1890ff",colorBgContainer:i?"#1f1f1f":"#fff",colorBgElevated:i?"#1f1f1f":"#fff",colorText:i?"#fff":"rgba(0, 0, 0, 0.85)",colorPrimaryText:"#fff",colorTextPlaceholder:i?"rgba(255, 255, 255, 0.25)":"rgba(0, 0, 0, 0.25)",colorBorder:i?"#434343":"#d9d9d9",colorIcon:i?"rgba(255, 255, 255, 0.45)":"rgba(0, 0, 0, 0.45)",colorIconHover:i?"rgba(255, 255, 255, 0.85)":"rgba(0, 0, 0, 0.85)",colorSplit:i?"#303030":"#f0f0f0",fontSize:14},Button:{colorPrimary:"#1890ff",colorPrimaryHover:"#40a9ff",colorPrimaryActive:"#096dd9",colorText:i?"#fff":"rgba(0, 0, 0, 0.85)",colorBgContainer:i?"#1f1f1f":"#fff",colorBorder:i?"#434343":"#d9d9d9"}}}),v=[{title:"Etkinlik Bilgileri",content:e.jsxs(n,{layout:"vertical",form:d,initialValues:r,onValuesChange:C,children:[e.jsx(n.Item,{name:"etkinlik_adi",label:"Etkinlik Adı",rules:[{required:!0,message:"Etkinlik adı zorunludur"}],children:e.jsx(z,{placeholder:"Etkinlik adını girin"})}),e.jsx(n.Item,{name:"etkinlik_tipi",label:"Etkinlik Türü",rules:[{required:!0,message:"Etkinlik türü seçilmelidir"}],children:e.jsxs(m.Group,{children:[e.jsx(m.Button,{value:"lider_tablosu",children:"Lider Tablosu"}),e.jsx(m.Button,{value:"pk_turnuva",children:"PK Turnuvası"})]})}),e.jsx(n.Item,{name:"tarih_araligi",label:"Etkinlik Tarihleri",rules:[{required:!0,message:"Etkinlik tarihleri zorunludur"}],children:e.jsx(ie,{theme:L(),children:e.jsx(de,{showTime:!0,format:"DD.MM.YYYY HH:mm",placeholder:["Başlangıç","Bitiş"],style:{width:"100%"},onOk:a=>{d.setFieldsValue({tarih_araligi:a})}})})}),e.jsx(n.Item,{name:"aciklama",label:"Etkinlik Açıklaması",children:e.jsx(O,{rows:4,placeholder:"Etkinlik hakkında kısa bir açıklama"})})]})},{title:"Katılımcılar",content:e.jsxs("div",{children:[e.jsx(I,{message:"Etkinliğe katılacak yayıncıları seçin",description:"Sağdaki listeye eklenen yayıncılar etkinliğe katılacaktır. PK Turnuvası için çift sayıda yayıncı seçmeniz önerilir.",type:"info",showIcon:!0,className:"mb-4"}),e.jsx(te,{dataSource:V,titles:["Mevcut Yayıncılar","Katılımcılar"],targetKeys:o,onChange:$,render:a=>a.title+" "+a.description,listStyle:{width:350,height:400}}),e.jsx("div",{className:"mt-4",children:e.jsxs(M,{type:"secondary",children:[o.length," yayıncı seçildi.",r.etkinlik_tipi==="pk_turnuva"&&o.length%2!==0&&e.jsx(M,{type:"danger",children:" PK Turnuvası için çift sayıda yayıncı seçmelisiniz!"})]})})]})},{title:"Etkinlik Ayarları",content:e.jsxs(n,{layout:"vertical",form:d,initialValues:r,onValuesChange:C,children:[e.jsx(n.Item,{name:"kayit_uygunlugu",label:"Katılım Şekli",children:e.jsxs(m.Group,{children:[e.jsx(m.Button,{value:"otomatik",children:"Otomatik Atama"}),e.jsx(m.Button,{value:"kayit",children:"Kayıt Olma"}),e.jsx(m.Button,{value:"davet",children:"Sadece Davetli"})]})}),r.etkinlik_tipi==="lider_tablosu"&&e.jsx(n.Item,{name:"puan_sistemi",label:"Puan Sistemi",children:e.jsxs(P,{children:[e.jsx(h,{value:"elmas",children:"Elmas Bazlı"}),e.jsx(h,{value:"yayin_suresi",children:"Yayın Süresi Bazlı"}),e.jsx(h,{value:"takipci",children:"Takipçi Artışı Bazlı"}),e.jsx(h,{value:"karma",children:"Karma Sistem"})]})}),r.etkinlik_tipi==="pk_turnuva"&&e.jsx(n.Item,{name:"tur_sayisi",label:"Tur Sayısı",children:e.jsxs(P,{defaultValue:"1",children:[e.jsx(h,{value:"1",children:"Tek Tur"}),e.jsx(h,{value:"2",children:"2 Tur"}),e.jsx(h,{value:"3",children:"3 Tur (Çeyrek final, Yarı final, Final)"})]})}),e.jsx(n.Item,{name:"kurallar",label:"Etkinlik Kuralları",children:e.jsx(O,{rows:6,placeholder:"Etkinlik kurallarını detaylı olarak yazın"})})]})},{title:"Onay",content:e.jsxs("div",{children:[e.jsx(I,{message:"Etkinlik Oluşturma Onayı",description:"Etkinlik bilgilerini kontrol edin ve doğru ise 'Etkinliği Oluştur' butonuna tıklayın.",type:"success",showIcon:!0,className:"mb-4"}),e.jsxs(se,{className:"mb-4",children:[e.jsx(me,{level:4,children:r.etkinlik_adi||"İsimsiz Etkinlik"}),e.jsxs(E,{children:[e.jsx("strong",{children:"Tür:"})," ",r.etkinlik_tipi==="lider_tablosu"?"Lider Tablosu":"PK Turnuvası"]}),e.jsxs(E,{children:[e.jsx("strong",{children:"Tarihler:"})," ",r.tarih_araligi?`${j(r.tarih_araligi[0]).format("DD.MM.YYYY HH:mm")} - ${j(r.tarih_araligi[1]).format("DD.MM.YYYY HH:mm")}`:"Belirtilmedi"]}),e.jsxs(E,{children:[e.jsx("strong",{children:"Katılımcı Sayısı:"})," ",o.length," yayıncı"]}),r.aciklama&&e.jsxs(E,{children:[e.jsx("strong",{children:"Açıklama:"})," ",r.aciklama]})]})]})}];return e.jsxs("div",{className:`event-wizard-container p-6 ${i?"dark":""}`,children:[e.jsx(Z,{className:"mb-6",children:"Etkinlik Oluşturma Sihirbazı"}),e.jsx(A,{current:l,className:"mb-8",children:v.map(a=>e.jsx(he,{title:a.title},a.title))}),e.jsx("div",{className:`steps-content p-6 rounded-lg shadow ${i?"bg-gray-800":"bg-white"}`,children:v[l].content}),e.jsxs("div",{className:"steps-action mt-6 flex justify-between",children:[l>0&&e.jsx(_,{icon:e.jsx(ne,{}),onClick:F,style:x.light,className:i?"hover:bg-gray-700":"hover:bg-gray-100",children:"Geri"}),l<v.length-1&&e.jsx(_,{type:"primary",onClick:K,icon:e.jsx(ue,{}),style:x.primary,className:"hover:bg-blue-600",children:"İleri"}),l===0&&e.jsx(_,{type:"default",onClick:()=>B("/events"),style:x.light,className:i?"hover:bg-gray-700":"hover:bg-gray-100",children:"İptal"}),l===v.length-1&&e.jsx(_,{type:"primary",onClick:R,loading:y,icon:e.jsx(X,{}),style:x.primary,className:"hover:bg-blue-600",children:"Etkinliği Oluştur"})]})]})};export{Te as default};
