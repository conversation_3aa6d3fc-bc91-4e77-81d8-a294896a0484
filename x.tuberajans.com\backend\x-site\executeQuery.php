<?php
// executeQuery.php - SQL sorgularını güvenli şekilde çalıştırmak için fonksiyon

require_once __DIR__ . '/../config/config.php';

/**
 * SQL sorgusunu güvenli bir şekilde çalıştırır
 * 
 * @param string $query SQL sorgusu
 * @param array $params Sorgu parametreleri
 * @return array Sonuç dizisi
 */
function executeQuery($query, $params = []) {
    global $db;
    
    // Sorgu güvenlik kontrolleri
    $query = trim($query);
    
    // Sadece SELECT sorgularına izin ver
    if (!preg_match('/^SELECT/i', $query)) {
        return [
            'success' => false,
            'error' => 'Sadece SELECT sorguları desteklenmektedir'
        ];
    }
    
    // Tehlikeli SQL komutlarını engelle
    $disallowedPatterns = [
        '/DROP/i', 
        '/DELETE/i', 
        '/UPDATE/i', 
        '/INSERT/i', 
        '/ALTER/i', 
        '/TRUNCATE/i', 
        '/GRANT/i',
        '/REVOKE/i',
        '/;.*;/i'  // Çoklu SQL sorguları
    ];
    
    foreach ($disallowedPatterns as $pattern) {
        if (preg_match($pattern, $query)) {
            return [
                'success' => false,
                'error' => 'Güvenlik nedeniyle bazı SQL komutlarına izin verilmemektedir'
            ];
        }
    }
    
    // Query'yi logla
    error_log("Güvenli SQL Sorgusu: " . $query);
    if (!empty($params)) {
        error_log("Sorgu parametreleri: " . json_encode($params));
    }
    
    try {
        $stmt = $db->prepare($query);
        $stmt->execute($params);
        
        // Sonuçları al
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return [
            'success' => true,
            'data' => $results,
            'count' => count($results)
        ];
    } catch (PDOException $e) {
        error_log("SQL Sorgu Hatası: " . $e->getMessage());
        return [
            'success' => false,
            'error' => 'Veritabanı sorgu hatası: ' . $e->getMessage()
        ];
    }
} 