.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Glass effect */
.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.glass-dark {
  background: rgba(15, 23, 42, 0.25);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(15, 23, 42, 0.18);
}

/* Dashboard content styles */
/* Dashboard content styles - Tüm sayfalarda tutarlı hizalama için */
.dashboard-content {
  transition: margin-left 0.3s cubic-bezier(.4,0,.2,1);
  box-sizing: border-box;
  overflow-x: hidden;
}

.sidebar-open .dashboard-content {
  margin-left: 280px;
  width: calc(100vw - 280px);
  max-width: calc(100vw - 280px - 5px);
  padding: 0.75rem 0.75rem 0.75rem 0.25rem;
}

.sidebar-closed .dashboard-content {
  margin-left: 78px;
  width: calc(100vw - 78px);
  max-width: calc(100vw - 78px - 30px);
  padding: 0.75rem;
}

/* Tüm içerik alanlarına overflow-x: hidden ekle */
.dashboard-content > div,
.dashboard-content > div > div {
  overflow-x: hidden;
  max-width: 100%;
}

/* Mobil görünüm için düzenlemeler */
@media (max-width: 768px) {
  .dashboard-content,
  .sidebar-open .dashboard-content {
    margin-left: 0 !important;
    padding: 0 !important;
    width: 100vw !important;
    max-width: 100vw !important;
  }

  .container,
  .container-fluid,
  .sidebar-open .container,
  .sidebar-open .container-fluid {
    width: 100vw !important;
    max-width: 100vw !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}

/* Konteynır hizalamaları - Anasayfa referans alınarak */
.container,
.container-fluid,
.card,
.page-header,
.dashboard-container,
.announcement-container,
.education-container,
.events-container,
.feed-container,
.profile-container,
.settings-container,
.support-container {
  width: calc(100vw - 78px - 15px);
  max-width: calc(100vw - 78px - 15px);
  padding-right: 15px;
  padding-left: 0px;
  margin-right: auto;
  margin-left: 0;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* Mobil için container padding'leri kaldır */
@media (max-width: 768px) {
  .container,
  .container-fluid,
  .card,
  .page-header,
  .dashboard-container,
  .announcement-container,
  .education-container,
  .events-container,
  .feed-container,
  .profile-container,
  .settings-container,
  .support-container {
    width: 100vw !important;
    max-width: 100vw !important;
    padding-right: 0 !important;
    padding-left: 0 !important;
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
}

/* Sidebar açık ve kapalı durumlarda tutarlı hizalama */
.sidebar-open .container,
.sidebar-open .container-fluid,
.sidebar-open .card,
.sidebar-open .page-header,
.sidebar-open .dashboard-container,
.sidebar-open .announcement-container,
.sidebar-open .education-container,
.sidebar-open .events-container,
.sidebar-open .feed-container,
.sidebar-open .profile-container,
.sidebar-open .settings-container,
.sidebar-open .support-container {
  padding-left: 0px;
  padding-right: 40px;
  max-width: calc(100vw - 280px - 40px);
  width: calc(100vw - 280px - 40px);
  overflow-x: hidden;
}

/* Tüm sayfalarda tutarlı konteynır hizalaması */
.dashboard-content > div:first-child,
.dashboard-content > .container {
  margin-left: 0;
  padding-left: 0;
  padding-right: 15px;
}

/* Tüm sayfalarda ilk konteynır hizalaması */
.dashboard-content > div:first-child .container,
.dashboard-content > .container {
  margin-left: 0;
  padding-left: 0;
}

/* Tüm sayfalarda konteynır içeriği hizalaması */
.container > div:first-child {
  padding-left: 0;
}

/* Tüm sayfalarda ilk konteynır hizalaması için genel stil */
.container {
  padding-left: 0 !important;
}

/* Kategori butonları için stil */
.overflow-x-auto::-webkit-scrollbar,
.category-scroll::-webkit-scrollbar {
  display: none !important;
}

.overflow-x-auto,
.category-scroll {
  -ms-overflow-style: none !important;
  scrollbar-width: none !important;
  max-width: 100% !important;
  overflow-x: auto !important;
  padding-bottom: 4px !important;
}

/* Kategori butonları konteynırı için özel stil */
.category-scroll {
  width: 100% !important;
  overflow-x: auto !important;
  padding-bottom: 4px !important;
  margin-right: 0 !important;
  overflow-y: hidden !important;
  padding-left: 0 !important;
  margin-left: 0 !important;
}

/* Eğitimler sayfası için özel kategori stil düzenlemesi */
.dashboard-content .container .bg-white .category-scroll {
  padding-left: 8px !important;
}

.category-scroll > div {
  display: flex !important;
  flex-wrap: nowrap !important;
  gap: 0.375rem !important;
  width: max-content !important;
  min-width: max-content !important;
  flex-direction: row !important;
  padding-left: 0 !important;
  margin-left: 0 !important;
}

/* Kategori butonları için stil */
.category-scroll button {
  flex-shrink: 0 !important;
  white-space: nowrap !important;
  display: inline-block !important;
  margin-right: 8px !important;
}

/* Tüm Kategoriler butonları için özel stil */
button[class*="rounded-full"] {
  border-radius: 9999px !important;
}

/* Mobil görünüm için kategori butonları */
@media (max-width: 768px) {
  .category-scroll {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
    padding-left: 0 !important;
    margin-left: 0 !important;
  }

  .category-scroll > div {
    padding-right: 16px !important;
    padding-left: 0 !important;
    margin-left: 0 !important;
  }
}