<?php
// Detaylı hata ayıklama
ini_set('display_errors', 1); // Geçici olarak hataları göster
error_reporting(E_ALL);

// Debug: API'nin başladığını bildir
error_log("Publisher Discovery API başlatılıyor - " . date('Y-m-d H:i:s'));

require_once __DIR__ . '/../config/config.php';

// Debug: API'nin buraya kadar çalıştığını bildir
error_log("Publisher Discovery API: Tüm include'lar tamamlandı - " . date('Y-m-d H:i:s'));

// CORS ayarları
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// OPTIONS isteği varsa hızlıca cevap ver
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// JSON yanıt fonksiyonu - config.php'de tanımlı değilse buradan tanımla
if (!function_exists('jsonResponse')) {
    function jsonResponse($data, $status = 200) {
        http_response_code($status);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}

// Auth kontrolü
if ($_SERVER['REQUEST_METHOD'] !== 'OPTIONS') {
    if (!checkAuth()) {
        error_log("Publisher Discovery API: Auth başarısız");
        jsonResponse(['error' => 'Unauthorized', 'message' => 'Bu işlemi gerçekleştirmek için giriş yapmalısınız.'], 401);
    }
    error_log("Publisher Discovery API: Auth başarılı");
}

// Dinamik status listesi
if (isset($_GET['distinct_status']) && $_GET['distinct_status'] == '1') {
    try {
        // Database auto-detection için distinct status sorgusu
        $databases = ['tuberaja_yayinci_takip', 'tuberaja_yayinci_akademi', 'tiktok_live_data'];
        $liveDataDatabase = null;
        
        foreach ($databases as $database) {
            try {
                $testQuery = "SELECT 1 FROM $database.live_data LIMIT 1";
                $db->query($testQuery);
                $liveDataDatabase = $database;
                break;
            } catch (PDOException $e) {
                continue;
            }
        }
        
        if (!$liveDataDatabase) {
            echo json_encode(['success' => false, 'error' => 'live_data tablosu bulunamadı']);
            exit();
        }
        
        // NULL ve boş değerleri filtrele
        $stmt = $db->query("SELECT DISTINCT status FROM $liveDataDatabase.live_data WHERE status IS NOT NULL AND status != '' ORDER BY status ASC");
        $statuses = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo json_encode(['success' => true, 'statuses' => $statuses]);
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'error' => 'Status sorgu hatası: ' . $e->getMessage()]);
    }
    exit();
}

// Filtreli veri çekme
$status = isset($_GET['status']) && $_GET['status'] !== 'all' ? $_GET['status'] : null;
$todayOnly = isset($_GET['today_only']) && $_GET['today_only'] === 'true';

// Toplam kullanıcı sayısı
try {
    // Database auto-detection kullan
    $databases = ['tuberaja_yayinci_takip', 'tuberaja_yayinci_akademi', 'tiktok_live_data'];
    $liveDataDatabase = null;
    
    foreach ($databases as $database) {
        try {
            $testQuery = "SELECT 1 FROM $database.live_data LIMIT 1";
            $db->query($testQuery);
            $liveDataDatabase = $database;
            error_log("Publisher Discovery API: live_data tablosu bulundu: $database");
            break;
        } catch (PDOException $e) {
            continue;
        }
    }
    
    if (!$liveDataDatabase) {
        throw new Exception("live_data tablosu bulunamadı");
    }
    
    $totalCount = $db->query("SELECT COUNT(*) FROM $liveDataDatabase.live_data")->fetchColumn();

    // WHERE koşullarını oluştur
    $whereConditions = [];
    $params = [];

    if ($status) {
        $whereConditions[] = "LOWER(status) = LOWER(:status)";
        $params[':status'] = $status;
    }

    if ($todayOnly) {
        $whereConditions[] = "DATE(timestamp) = CURDATE()";
    }

    $whereClause = !empty($whereConditions) ? " WHERE " . implode(" AND ", $whereConditions) : "";

    // Filtreli kullanıcı sayısı
    if (!empty($whereConditions)) {
        $countSql = "SELECT COUNT(*) FROM $liveDataDatabase.live_data" . $whereClause;
        $countStmt = $db->prepare($countSql);
        foreach ($params as $key => $value) {
            $countStmt->bindValue($key, $value);
        }
        $countStmt->execute();
        $filteredCount = $countStmt->fetchColumn();
    } else {
        $filteredCount = $totalCount;
    }

    // Ana sorgu
    $sql = "SELECT id, username, viewer_count, status, link, timestamp, message_log, sorgu_tarihi FROM $liveDataDatabase.live_data";
    $sql .= $whereClause;
    $sql .= " ORDER BY timestamp DESC LIMIT 100";

    $stmt = $db->prepare($sql);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->execute();
    $publishers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $data = array_map(function($row) {
        return [
            'id' => $row['id'],
            'username' => $row['username'],
            'viewerCount' => $row['viewer_count'],
            'status' => strtolower($row['status'] ?: '-'),
            'createdAt' => $row['timestamp'],
            'messageStatus' => strtolower($row['message_log'] ?: '-'),
            'platform' => 'TikTok',
            'lastChecked' => $row['sorgu_tarihi'],
        ];
    }, $publishers);

    // Status bazlı sayılar
    $statusCounts = [];
    $stmtStatus = $db->query("SELECT status, COUNT(*) as count FROM $liveDataDatabase.live_data GROUP BY status");
    foreach ($stmtStatus->fetchAll(PDO::FETCH_ASSOC) as $row) {
        if ($row['status'] !== null && $row['status'] !== '') {
            $statusCounts[strtolower($row['status'])] = (int)$row['count'];
        }
    }

    // Genel sekme için istatistikler
    $suitableCount = $db->query("SELECT COUNT(*) FROM $liveDataDatabase.live_data WHERE status IN ('Uygun', 'Uygun Elite')")->fetchColumn();
    $messageSent = $db->query("SELECT COUNT(*) FROM $liveDataDatabase.live_data WHERE message_log = 'Mesaj Gönderildi'")->fetchColumn();
    $averageViewers = (int)$db->query("SELECT AVG(viewer_count) FROM $liveDataDatabase.live_data")->fetchColumn();
    $todayDiscovered = $db->query("SELECT COUNT(*) FROM $liveDataDatabase.live_data WHERE DATE(timestamp) = CURDATE()")->fetchColumn();

    // Bugün bulunan Uygun Elite ve Uygun sayıları
    $todayElite = $db->query("SELECT COUNT(*) FROM $liveDataDatabase.live_data WHERE status = 'Uygun Elite' AND DATE(timestamp) = CURDATE()")->fetchColumn();
    $todaySuitable = $db->query("SELECT COUNT(*) FROM $liveDataDatabase.live_data WHERE status = 'Uygun' AND DATE(timestamp) = CURDATE()")->fetchColumn();

    echo json_encode([
        "success" => true,
        "data" => [
            "publishers" => $data,
            "stats" => [
                'statusCounts' => $statusCounts,
                'totalPublishers' => (int)$totalCount,
                'suitablePublishers' => (int)$suitableCount,
                'messageSent' => (int)$messageSent,
                'averageViewers' => (int)$averageViewers,
                'todayDiscovered' => (int)$todayDiscovered,
                'todayElite' => (int)$todayElite,
                'todaySuitable' => (int)$todaySuitable
            ],
            'totalCount' => $totalCount,
            'filteredCount' => $filteredCount
        ]
    ]);
    exit();
} catch (Exception $e) {
    echo json_encode([
        "success" => false,
        "message" => $e->getMessage(),
        "data" => [
            "publishers" => [],
            "stats" => []
        ]
    ]);
    exit();
}