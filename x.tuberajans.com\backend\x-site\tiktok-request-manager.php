<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../vendor/autoload.php'; // Composer autoloader

// Error reporting'i kapat (JSON response'u bozmasın)
error_reporting(0);
ini_set('display_errors', 0);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

class TikTokRequestManager {
    private $db;

    public function __construct() {
        try {
            $this->db = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=social_media_analytics;charset=utf8mb4",
                DB_USER,
                DB_PASS,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
                ]
            );
        } catch (PDOException $e) {
            throw new Exception('Veritabanı bağlantı hatası: ' . $e->getMessage());
        }
    }

    public function createAnalysisRequest($username, $videoCount = 10) {
        try {
            // Kullanıcı adından @ işaretini kaldır
            $username = str_replace('@', '', trim($username));

            if (empty($username)) {
                throw new Exception('Kullanıcı adı gerekli');
            }

            // Aynı kullanıcı için pending/processing istek var mı kontrol et
            $stmt = $this->db->prepare("
                SELECT id, status FROM tiktok_requests
                WHERE username = ? AND status IN ('pending', 'processing')
                ORDER BY created_at DESC LIMIT 1
            ");
            $stmt->execute([$username]);
            $existingRequest = $stmt->fetch();

            if ($existingRequest) {
                return [
                    'success' => true,
                    'request_id' => $existingRequest['id'],
                    'current_step' => 'Mevcut istek işleniyor...',
                    'message' => 'Bu kullanıcı için zaten bir analiz işlemi devam ediyor'
                ];
            }

            // Video sayısını sınırla
            $videoCount = max(5, min(100, intval($videoCount)));

            // Yeni istek oluştur
            $stmt = $this->db->prepare("
                INSERT INTO tiktok_requests (username, video_count, status, created_at)
                VALUES (?, ?, 'pending', NOW())
            ");
            $stmt->execute([$username, $videoCount]);
            $requestId = $this->db->lastInsertId();

            return [
                'success' => true,
                'request_id' => $requestId,
                'current_step' => "İstek oluşturuldu, VDS tarafından işlenmeyi bekliyor... ({$videoCount} video analiz edilecek)",
                'message' => "Analiz isteği başarıyla oluşturuldu ({$videoCount} video)"
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    public function getRequestStatus($requestId) {
        try {
            error_log("🔍 Getting status for request ID: $requestId");

            $stmt = $this->db->prepare("
                SELECT r.*, a.* FROM tiktok_requests r
                LEFT JOIN tiktok_analysis a ON r.result_id = a.id
                WHERE r.id = ?
            ");
            $stmt->execute([$requestId]);
            $request = $stmt->fetch();

            if (!$request) {
                error_log("❌ Request not found: $requestId");
                throw new Exception('İstek bulunamadı');
            }

            error_log("📊 Request found - Status: " . $request['status'] . ", Result ID: " . ($request['result_id'] ?: 'NULL'));

            $response = [
                'success' => true,
                'status' => $request['status'],
                'current_step' => $request['current_step'] ?: $this->getStepMessage($request['status']),
                'progress' => $request['progress'] ?: 0
            ];

            if ($request['status'] === 'completed' && $request['result_id']) {
                error_log("✅ Analysis completed, formatting data...");
                // Analiz sonuçlarını formatla
                $response['data'] = $this->formatAnalysisData($request);
                error_log("📋 Data formatted successfully for request $requestId");
                error_log("📋 Videos count: " . (isset($response['data']['videos']) ? count($response['data']['videos']) : 0));
            } elseif ($request['status'] === 'completed' && !$request['result_id']) {
                error_log("❌ Request $requestId is completed but has no result_id");
                $response['error'] = 'Analiz tamamlandı ama sonuç bulunamadı';
            } elseif ($request['status'] === 'failed') {
                error_log("❌ Request $requestId failed: " . ($request['error_message'] ?: 'Unknown error'));
                $response['error'] = $request['error_message'] ?: 'Analiz başarısız oldu';
            }

            error_log("📤 Returning response: " . json_encode($response));
            return $response;

        } catch (Exception $e) {
            error_log("💥 Error in getRequestStatus: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    private function getStepMessage($status) {
        switch ($status) {
            case 'pending':
                return 'VDS tarafından işlenmeyi bekliyor...';
            case 'processing':
                return 'Profil analiz ediliyor...';
            case 'completed':
                return 'Analiz tamamlandı!';
            case 'failed':
                return 'Analiz başarısız oldu';
            default:
                return 'Bilinmeyen durum';
        }
    }

    private function formatAnalysisData($request) {
        // Videos JSON'ını parse et
        $videos = [];
        if (!empty($request['videos_data'])) {
            $videos = json_decode($request['videos_data'], true) ?: [];
        }

        // Most used hashtags JSON'ını parse et
        $hashtags = [];
        if (!empty($request['most_used_hashtags'])) {
            $hashtags = json_decode($request['most_used_hashtags'], true) ?: [];
        }

        return [
            'username' => $request['username'],
            'nickname' => $request['nickname'] ?: $request['username'],
            'bio' => $request['bio'] ?: '',
            'avatar_url' => $request['avatar_url'] ?: '',
            'followers' => $request['followers_text'] ?: '0',
            'followers_count' => $request['followers_count'] ?: 0,
            'following' => $request['following_text'] ?: '0',
            'following_count' => $request['following_count'] ?: 0,
            'likes' => $request['likes_text'] ?: '0',
            'likes_count' => $request['likes_count'] ?: 0,
            'videos' => $videos,
            'total_videos' => $request['total_videos'] ?: count($videos),
            'total_views' => $request['total_views'] ?: 0,
            'total_likes' => $request['total_video_likes'] ?: 0,
            'total_comments' => $request['total_comments'] ?: 0,
            'total_shares' => $request['total_shares'] ?: 0,
            'total_saves' => $request['total_saves'] ?: 0,
            'total_engagement' => $request['total_engagement'] ?: 0,
            'total_engagement_rate' => $request['engagement_rate'] ?: 0,
            'average_views' => $request['average_views'] ?: 0,
            'average_likes' => $request['average_likes'] ?: 0,
            'average_comments' => $request['average_comments'] ?: 0,
            'average_shares' => $request['average_shares'] ?: 0,
            'average_saves' => $request['average_saves'] ?: 0,
            'most_used_hashtags' => $hashtags
        ];
    }

    public function generatePDFReport($data) {
        // TCPDF kütüphanesini dahil et
        if (!class_exists('TCPDF')) {
            // Composer autoloader ile yüklenmişse
            if (class_exists('\TCPDF')) {
                class_alias('\TCPDF', 'TCPDF');
            }
        }
        $tcpdf_paths = [
            '../TCPDF-main/tcpdf.php',
            '../../TCPDF-main/tcpdf.php',
            '../../../TCPDF-main/tcpdf.php',
            __DIR__ . '/../TCPDF-main/tcpdf.php',
            __DIR__ . '/../../TCPDF-main/tcpdf.php',
            __DIR__ . '/../../../TCPDF-main/tcpdf.php'
        ];

        $tcpdf_loaded = false;
        foreach ($tcpdf_paths as $path) {
            if (file_exists($path)) {
                require_once $path;
                $tcpdf_loaded = true;
                break;
            }
        }

        if (!$tcpdf_loaded) {
            throw new Exception('TCPDF kütüphanesi bulunamadı. Lütfen TCPDF-main klasörünün doğru konumda olduğundan emin olun.');
        }

        // TCPDF kütüphanesini kontrol et
        if (!class_exists('TCPDF')) {
            // TCPDF yüklü değilse basit HTML raporu döndür
            header('Content-Type: text/html; charset=utf-8');
            echo $this->generateHTMLReport($data);
            exit;
        }

        // PDF oluştur - Basit ayarlarla
        $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);

        // PDF meta bilgileri
        $pdf->SetCreator('Tuber Ajans X-Site');
        $pdf->SetAuthor('Tuber Ajans');
        $pdf->SetTitle('TikTok Profil Analiz Raporu - @' . $data['username']);
        $pdf->SetSubject('TikTok Profil Analizi');
        $pdf->SetKeywords('TikTok, Analiz, Profil, Rapor');

        // Sayfa ayarları
        $pdf->SetMargins(15, 27, 15);
        $pdf->SetHeaderMargin(5);
        $pdf->SetFooterMargin(10);
        $pdf->SetAutoPageBreak(TRUE, 25);

        // Header ve Footer'ı kaldır
        $pdf->setPrintHeader(false);
        $pdf->setPrintFooter(false);

        // Font ayarları - Güvenli font kullan
        try {
            // Önce dejavusans dene
            $pdf->SetFont('dejavusans', '', 12);
        } catch (Exception $e) {
            try {
                // dejavusans yoksa times dene
                $pdf->SetFont('times', '', 12);
            } catch (Exception $e2) {
                // times da yoksa courier kullan (her zaman mevcut)
                $pdf->SetFont('courier', '', 12);
            }
        }

        // Sayfa ekle
        $pdf->AddPage();

        // Font helper fonksiyonu
        $setFont = function($style = '', $size = 12) use ($pdf) {
            try {
                $pdf->SetFont('dejavusans', $style, $size);
            } catch (Exception $e) {
                try {
                    $pdf->SetFont('times', $style, $size);
                } catch (Exception $e2) {
                    $pdf->SetFont('courier', $style, $size);
                }
            }
        };

        // Başlık
        $setFont('B', 20);
        $pdf->SetTextColor(254, 44, 85); // TikTok rengi
        $pdf->Cell(0, 15, 'TikTok Profil Analiz Raporu', 0, 1, 'C');
        $pdf->Ln(5);

        // Kullanıcı bilgileri
        $setFont('B', 16);
        $pdf->SetTextColor(0, 0, 0);
        $pdf->Cell(0, 10, '@' . $data['username'], 0, 1, 'C');

        if (!empty($data['nickname'])) {
            $setFont('', 12);
            $pdf->SetTextColor(100, 100, 100);
            $pdf->Cell(0, 8, $data['nickname'], 0, 1, 'C');
        }

        $pdf->Ln(10);

        // Profil istatistikleri tablosu
        $setFont('B', 14);
        $pdf->SetTextColor(0, 0, 0);
        $pdf->Cell(0, 10, 'Profil Istatistikleri', 0, 1, 'L');
        $pdf->Ln(5);

        $profileStats = [
            ['Takipçi', $data['followers']],
            ['Takip Edilen', $data['following']],
            ['Toplam Beğeni', $data['likes']],
            ['Toplam Video İzlenme', number_format($data['total_views'])],
            ['Toplam Video Beğeni', number_format($data['total_likes'])],
            ['Toplam Yorum', number_format($data['total_comments'])],
            ['Toplam Paylaşım', number_format($data['total_shares'])],
            ['Toplam Kaydetme', number_format($data['total_saves'])],
            ['Etkileşim Oranı', number_format($data['total_engagement_rate'], 2) . '%']
        ];

        $setFont('', 10);
        foreach ($profileStats as $stat) {
            $pdf->Cell(80, 8, $stat[0], 1, 0, 'L');
            $pdf->Cell(80, 8, $stat[1], 1, 1, 'R');
        }

        $pdf->Ln(10);

        // Video performans ortalamaları
        $setFont('B', 14);
        $pdf->Cell(0, 10, 'Video Performans Ortalamalari', 0, 1, 'L');
        $pdf->Ln(5);

        $avgStats = [
            ['Ortalama Izlenme', number_format($data['average_views'])],
            ['Ortalama Begeni', number_format($data['average_likes'])],
            ['Ortalama Yorum', number_format($data['average_comments'])],
            ['Ortalama Paylasim', number_format($data['average_shares'])],
            ['Ortalama Kaydetme', number_format($data['average_saves'])]
        ];

        $setFont('', 10);
        foreach ($avgStats as $stat) {
            $pdf->Cell(80, 8, $stat[0], 1, 0, 'L');
            $pdf->Cell(80, 8, $stat[1], 1, 1, 'R');
        }

        $pdf->Ln(10);

        // En çok kullanılan hashtag'ler
        if (!empty($data['most_used_hashtags'])) {
            $setFont('B', 14);
            $pdf->Cell(0, 10, 'En Cok Kullanilan Hashtagler', 0, 1, 'L');
            $pdf->Ln(5);

            $setFont('', 10);
            $hashtagText = '';
            // Hashtag array formatını kontrol et
            if (is_array($data['most_used_hashtags'])) {
                foreach ($data['most_used_hashtags'] as $hashtagData) {
                    if (is_array($hashtagData) && count($hashtagData) >= 2) {
                        // [hashtag, count] formatı
                        $hashtagText .= '#' . $hashtagData[0] . ' (' . $hashtagData[1] . '), ';
                    } else if (is_string($hashtagData)) {
                        // Sadece hashtag string'i
                        $hashtagText .= '#' . $hashtagData . ', ';
                    }
                }
            }
            $hashtagText = rtrim($hashtagText, ', ');
            if (!empty($hashtagText)) {
                $pdf->MultiCell(0, 8, $hashtagText, 1, 'L');
            } else {
                $pdf->MultiCell(0, 8, 'Hashtag bilgisi bulunamadi.', 1, 'L');
            }
            $pdf->Ln(5);
        }

        // Rapor tarihi
        $setFont('', 8);
        $pdf->SetTextColor(100, 100, 100);
        $pdf->Cell(0, 5, 'Rapor Tarihi: ' . date('d.m.Y H:i'), 0, 1, 'R');

        // PDF'i indir
        $filename = 'tiktok_analiz_' . $data['username'] . '_' . date('Y-m-d') . '.pdf';
        $pdf->Output($filename, 'D');
        exit;
    }

    private function generateHTMLReport($data) {
        $html = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>TikTok Profil Analiz Raporu - @' . htmlspecialchars($data['username']) . '</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; color: #fe2c55; margin-bottom: 30px; }
        .stats-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .stats-table th, .stats-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .stats-table th { background-color: #f2f2f2; }
        .section { margin: 30px 0; }
        .print-btn { background: #fe2c55; color: white; padding: 10px 20px; border: none; cursor: pointer; }
    </style>
</head>
<body>
    <div class="header">
        <h1>TikTok Profil Analiz Raporu</h1>
        <h2>@' . htmlspecialchars($data['username']) . '</h2>
        ' . (!empty($data['nickname']) ? '<p>' . htmlspecialchars($data['nickname']) . '</p>' : '') . '
    </div>
    
    <div class="section">
        <h3>Profil İstatistikleri</h3>
        <table class="stats-table">
            <tr><td>Takipçi</td><td>' . number_format($data['followers']) . '</td></tr>
            <tr><td>Takip Edilen</td><td>' . number_format($data['following']) . '</td></tr>
            <tr><td>Toplam Beğeni</td><td>' . number_format($data['likes']) . '</td></tr>
            <tr><td>Toplam Video İzlenme</td><td>' . number_format($data['total_views']) . '</td></tr>
            <tr><td>Toplam Video Beğeni</td><td>' . number_format($data['total_likes']) . '</td></tr>
            <tr><td>Toplam Yorum</td><td>' . number_format($data['total_comments']) . '</td></tr>
            <tr><td>Toplam Paylaşım</td><td>' . number_format($data['total_shares']) . '</td></tr>
            <tr><td>Etkileşim Oranı</td><td>' . number_format($data['total_engagement_rate'], 2) . '%</td></tr>
        </table>
    </div>
    
    <div class="section">
        <h3>Video Performans Ortalamaları</h3>
        <table class="stats-table">
            <tr><td>Ortalama İzlenme</td><td>' . number_format($data['average_views']) . '</td></tr>
            <tr><td>Ortalama Beğeni</td><td>' . number_format($data['average_likes']) . '</td></tr>
            <tr><td>Ortalama Yorum</td><td>' . number_format($data['average_comments']) . '</td></tr>
            <tr><td>Ortalama Paylaşım</td><td>' . number_format($data['average_shares']) . '</td></tr>
        </table>
    </div>
    
    <div style="text-align: center; margin: 30px 0;">
        <button class="print-btn" onclick="window.print()">Raporu Yazdır</button>
    </div>
    
    <div style="text-align: right; color: #666; font-size: 12px; margin-top: 50px;">
        Rapor Tarihi: ' . date('d.m.Y H:i') . '
    </div>
</body>
</html>';
        return $html;
    }

    public function getDebugInfo($requestId) {
        try {
            // Request bilgilerini al
            $stmt = $this->db->prepare("
                SELECT r.*, a.* FROM tiktok_requests r
                LEFT JOIN tiktok_analysis a ON r.result_id = a.id
                WHERE r.id = ?
            ");
            $stmt->execute([$requestId]);
            $request = $stmt->fetch();

            // Tablo varlığını kontrol et
            $tablesExist = [
                'tiktok_requests' => $this->db->query("SHOW TABLES LIKE 'tiktok_requests'")->rowCount() > 0,
                'tiktok_analysis' => $this->db->query("SHOW TABLES LIKE 'tiktok_analysis'")->rowCount() > 0
            ];

            // Tablo kayıt sayılarını al
            $recordCounts = [];
            if ($tablesExist['tiktok_requests']) {
                $stmt = $this->db->query("SELECT COUNT(*) as count FROM tiktok_requests");
                $recordCounts['tiktok_requests'] = $stmt->fetch()['count'];
            }
            if ($tablesExist['tiktok_analysis']) {
                $stmt = $this->db->query("SELECT COUNT(*) as count FROM tiktok_analysis");
                $recordCounts['tiktok_analysis'] = $stmt->fetch()['count'];
            }

            // Video thumbnail'larını kontrol et
            $thumbnailCheck = [];
            if ($request && isset($request['videos_data'])) {
                $videosData = json_decode($request['videos_data'], true);
                if ($videosData && is_array($videosData)) {
                    foreach ($videosData as $i => $video) {
                        $thumbnailCheck[$i] = [
                            'has_thumbnail' => isset($video['thumbnail']),
                            'thumbnail_value' => $video['thumbnail'] ?? 'NULL',
                            'thumbnail_type' => gettype($video['thumbnail'] ?? null),
                            'is_valid_url' => isset($video['thumbnail']) && filter_var($video['thumbnail'], FILTER_VALIDATE_URL),
                            'thumbnail_length' => isset($video['thumbnail']) ? strlen($video['thumbnail']) : 0
                        ];
                    }
                }
            }

            return [
                'request_found' => $request ? true : false,
                'request_data' => $request,
                'tables_exist' => $tablesExist,
                'record_counts' => $recordCounts,
                'database_name' => 'social_media_analytics',
                'thumbnail_debug' => $thumbnailCheck
            ];

        } catch (Exception $e) {
            return [
                'error' => $e->getMessage(),
                'request_found' => false
            ];
        }
    }
}

// API endpoint handler
try {
    $action = $_GET['action'] ?? '';
    $manager = new TikTokRequestManager();

    switch ($action) {
        case 'analyze':
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('Sadece POST istekleri desteklenir');
            }

            $input = json_decode(file_get_contents('php://input'), true);
            $username = $input['username'] ?? '';
            $videoCount = $input['video_count'] ?? 10;

            if (empty($username)) {
                throw new Exception('Kullanıcı adı gerekli');
            }

            // Video sayısını sınırla
            if ($videoCount < 5 || $videoCount > 100) {
                $videoCount = 10;
            }

            $result = $manager->createAnalysisRequest($username, $videoCount);
            echo json_encode($result);
            break;

        case 'status':
            if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
                throw new Exception('Sadece GET istekleri desteklenir');
            }

            $requestId = $_GET['request_id'] ?? '';

            if (empty($requestId)) {
                throw new Exception('İstek ID gerekli');
            }

            $result = $manager->getRequestStatus($requestId);
            echo json_encode($result);
            break;

        case 'export_pdf':
            if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
                throw new Exception('Sadece GET istekleri desteklenir');
            }

            $requestId = $_GET['request_id'] ?? '';

            if (empty($requestId)) {
                throw new Exception('İstek ID gerekli');
            }

            $result = $manager->getRequestStatus($requestId);

            if (!$result['success']) {
                throw new Exception($result['error']);
            }

            if ($result['status'] !== 'completed') {
                throw new Exception('Analiz henüz tamamlanmadı');
            }

            // PDF oluştur ve indir
            $manager->generatePDFReport($result['data']);
            break;

        case 'debug':
            // Debug endpoint - tablo durumunu kontrol et
            if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
                throw new Exception('Sadece GET istekleri desteklenir');
            }

            $requestId = $_GET['request_id'] ?? '';
            if (empty($requestId)) {
                throw new Exception('İstek ID gerekli');
            }

            // Raw veritabanı sorgusu
            $debugInfo = $manager->getDebugInfo($requestId);
            echo json_encode([
                'success' => true,
                'debug_info' => $debugInfo
            ]);
            break;

        default:
            throw new Exception('Geçersiz action parametresi');
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
