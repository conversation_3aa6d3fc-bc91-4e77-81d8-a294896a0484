import React, { useState } from 'react';

// API URL - Geliştirme ortamında yerel API kullanılıyor
const API_URL = import.meta.env.VITE_API_URL || '/backend/api';

const TestPage: React.FC = () => {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const testConnection = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`${API_URL}/test.php`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();
      setResult(JSON.stringify(data, null, 2));
    } catch (err) {
      console.error('Test error:', err);
      setError('Bağlantı hatası: ' + (err instanceof Error ? err.message : String(err)));
    } finally {
      setLoading(false);
    }
  };

  const testDebug = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`${API_URL}/debug.php?db_test=true`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();
      setResult(JSON.stringify(data, null, 2));
    } catch (err) {
      console.error('Debug error:', err);
      setError('Bağlantı hatası: ' + (err instanceof Error ? err.message : String(err)));
    } finally {
      setLoading(false);
    }
  };

  const createTestUser = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`${API_URL}/create_test_user.php`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();
      setResult(JSON.stringify(data, null, 2));
    } catch (err) {
      console.error('Create test user error:', err);
      setError('Bağlantı hatası: ' + (err instanceof Error ? err.message : String(err)));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container" style={{ maxWidth: '800px', margin: '20px auto', padding: '20px' }}>
      <div style={{
        padding: '20px',
        backgroundColor: '#fff',
        borderRadius: '8px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
      }}>
        <h2>API Bağlantı Testi</h2>

        <div style={{ marginBottom: '20px' }}>
          <button
            onClick={testConnection}
            disabled={loading}
            style={{
              padding: '10px 15px',
              backgroundColor: '#4a90e2',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              marginRight: '10px',
              cursor: loading ? 'not-allowed' : 'pointer'
            }}
          >
            Veritabanı Bağlantısını Test Et
          </button>

          <button
            onClick={testDebug}
            disabled={loading}
            style={{
              padding: '10px 15px',
              backgroundColor: '#9c27b0',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              marginRight: '10px',
              cursor: loading ? 'not-allowed' : 'pointer'
            }}
          >
            Debug Bilgilerini Göster
          </button>

          <button
            onClick={createTestUser}
            disabled={loading}
            style={{
              padding: '10px 15px',
              backgroundColor: '#ff5722',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: loading ? 'not-allowed' : 'pointer'
            }}
          >
            Test Kullanıcısı Oluştur
          </button>
        </div>

        {error && (
          <div style={{
            backgroundColor: '#ffebee',
            color: '#c62828',
            padding: '10px',
            borderRadius: '4px',
            marginBottom: '15px'
          }}>
            {error}
          </div>
        )}

        {result && (
          <div style={{ marginTop: '20px' }}>
            <h4>Sonuç:</h4>
            <div style={{
              padding: '15px',
              backgroundColor: '#f5f5f5',
              borderRadius: '4px',
              maxHeight: '400px',
              overflow: 'auto'
            }}>
              <pre>{result}</pre>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TestPage;
