import{j as e}from"./reactDnd-uQSTYBkW.js";import{k as E,r as i,u as B}from"./vendor-CnpYymF8.js";import{h as d}from"./utils-CtuI0RRe.js";import{E as S}from"./App-7woMCAzq.js";import{P as C}from"./ThemeStyles-D7U6Bfn4.js";import{e as H,u as w}from"./event-api-CItURaI-.js";import{o as m,x as p,k as u,X as I,T as $,t as F,O as P,u as y,n as R}from"./antd-BfejY-CV.js";import{R as O}from"./LeftOutlined-Cvarkk5b.js";import"./index-n-wHKc0W.js";import"./charts-6B1FLgFz.js";import"./createLucideIcon-DxVmGoQf.js";const{Option:t}=y,{Title:K,Text:b}=$;d.locale("tr");const ee=()=>{const{eventId:x}=E(),[h,f]=i.useState([]),[s,M]=i.useState(null),[_,j]=i.useState(!0),[k,c]=i.useState(!1),[l,N]=i.useState(null),[g]=m.useForm(),z=B();i.useEffect(()=>{v()},[x]);const v=async()=>{j(!0);try{const n=await H(x);f(n.matches||[]),M(n.eventDetails||null)}catch(n){p.error("Turnuva verileri yüklenemedi")}finally{j(!1)}},D=n=>{N(n),g.setFieldsValue({planlanan_zaman:n.planlanan_zaman?d(n.planlanan_zaman):null,durum:n.durum||"bekleniyor",kazanan_username:n.kazanan_username||null}),c(!0)},Y=async n=>{try{const o={...n,planlanan_zaman:n.planlanan_zaman?n.planlanan_zaman.format("YYYY-MM-DD HH:mm:ss"):null};await w(l.id,o),p.success("Eşleşme güncellendi"),c(!1),v()}catch(o){p.error("Eşleşme güncellenemedi")}},T=()=>{const n={};h.forEach(r=>{n[r.round]||(n[r.round]=[]),n[r.round].push(r)});const o=Object.keys(n).sort((r,a)=>Number(r)-Number(a));return e.jsx("div",{className:"tournament-bracket",children:o.map(r=>e.jsxs("div",{className:"tournament-round",children:[e.jsxs("h3",{className:"text-lg font-semibold mb-4",children:["Tur ",r]}),e.jsx("div",{className:"matches-container grid gap-4",children:n[r].map(a=>e.jsxs(R,{className:"match-card",title:`Eşleşme #${a.id}`,extra:e.jsx(u,{type:"link",onClick:()=>D(a),children:"Düzenle"}),children:[e.jsxs("div",{className:"match-participants space-y-4",children:[e.jsxs("div",{className:`participant p-3 rounded ${a.kazanan_username===a.yayinci1_username?"bg-green-50 border border-green-200":"bg-gray-50"}`,children:[a.yayinci1_isim," (@",a.yayinci1_username,")"]}),e.jsx("div",{className:"vs text-center font-bold text-gray-500",children:"VS"}),e.jsxs("div",{className:`participant p-3 rounded ${a.kazanan_username===a.yayinci2_username?"bg-green-50 border border-green-200":"bg-gray-50"}`,children:[a.yayinci2_isim," (@",a.yayinci2_username,")"]})]}),e.jsx("div",{className:"match-time mt-4 text-sm text-gray-500",children:a.planlanan_zaman?d(a.planlanan_zaman).format("DD MMMM YYYY, HH:mm"):"Zaman belirlenmedi"}),e.jsx("div",{className:"match-status mt-2",children:e.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${a.durum==="bekleniyor"?"bg-yellow-100 text-yellow-800":a.durum==="tamamlandi"?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:a.durum==="bekleniyor"?"Beklemede":a.durum==="tamamlandi"?"Tamamlandı":a.durum==="iptal"?"İptal":"Belirsiz"})})]},a.id))})]},r))})};return e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs(C,{children:[e.jsx(S,{className:"mr-2"})," Turnuva Eşleşmeleri"]}),e.jsx(u,{icon:e.jsx(O,{}),onClick:()=>z(`/events/${x}`),children:"Etkinliğe Geri Dön"})]}),_?e.jsxs("div",{className:"text-center py-10",children:[e.jsx(I,{size:"large"}),e.jsx("p",{className:"mt-4",children:"Turnuva verileri yükleniyor..."})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mb-6",children:[e.jsx(K,{level:2,children:(s==null?void 0:s.etkinlik_adi)||"Turnuva"}),e.jsxs(b,{type:"secondary",children:[(s==null?void 0:s.baslangic_tarihi)&&d(s==null?void 0:s.baslangic_tarihi).format("DD MMMM YYYY")," -",(s==null?void 0:s.bitis_tarihi)&&d(s==null?void 0:s.bitis_tarihi).format("DD MMMM YYYY")]})]}),h.length>0?T():e.jsx("div",{className:"text-center py-10 bg-gray-50 rounded-lg",children:e.jsx(b,{type:"secondary",children:"Bu turnuva için henüz eşleşme bulunmuyor"})}),e.jsx(F,{title:"Eşleşme Düzenle",open:k,onCancel:()=>c(!1),footer:null,children:l&&e.jsxs(m,{form:g,layout:"vertical",onFinish:Y,children:[e.jsx(m.Item,{name:"planlanan_zaman",label:"Planlanmış Zaman",children:e.jsx(P,{showTime:!0,format:"YYYY-MM-DD HH:mm:ss"})}),e.jsx(m.Item,{name:"durum",label:"Durum",children:e.jsxs(y,{children:[e.jsx(t,{value:"bekleniyor",children:"Beklemede"}),e.jsx(t,{value:"tamamlandi",children:"Tamamlandı"}),e.jsx(t,{value:"iptal",children:"İptal"})]})}),e.jsx(m.Item,{name:"kazanan_username",label:"Kazanan",children:e.jsxs(y,{children:[e.jsx(t,{value:null,children:"Henüz Belli Değil"}),e.jsx(t,{value:l.yayinci1_username,children:l.yayinci1_isim}),e.jsx(t,{value:l.yayinci2_username,children:l.yayinci2_isim})]})}),e.jsxs("div",{className:"flex justify-end",children:[e.jsx(u,{onClick:()=>c(!1),className:"mr-2",children:"İptal"}),e.jsx(u,{type:"primary",htmlType:"submit",children:"Kaydet"})]})]})})]})]})};export{ee as default};
