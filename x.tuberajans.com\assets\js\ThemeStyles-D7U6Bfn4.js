import{j as e}from"./reactDnd-uQSTYBkW.js";import{c as i}from"./createLucideIcon-DxVmGoQf.js";const o=[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]],n=i("arrow-up",o),x={gradients:{primary:"linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)",success:"linear-gradient(135deg, #10B981 0%, #059669 100%)",info:"linear-gradient(135deg, #3B82F6 0%, #2563EB 100%)",warning:"linear-gradient(135deg, #F97316 0%, #EA580C 100%)"},solid:{primary:"#6366F1",success:"#10B981",info:"#3B82F6",warning:"#F97316"}},g=({title:r,description:t,children:s,className:a})=>e.jsxs("div",{className:`mb-6 ${a||""}`,children:[r&&e.jsx("h1",{className:"text-2xl font-bold bg-gradient-to-r from-indigo-500 to-purple-600 bg-clip-text text-transparent",children:r}),t&&e.jsx("p",{className:"text-gray-500 dark:text-gray-400 mt-1",children:t}),s]}),m=({title:r,value:t,icon:s,gradient:a,growth:d})=>e.jsxs("div",{className:"bg-white dark:bg-gray-800 p-5 rounded-xl shadow-md hover:shadow-lg transition-shadow duration-300 relative overflow-hidden group",children:[e.jsxs("div",{className:"relative z-10",children:[e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:r}),e.jsx("p",{className:"text-2xl font-bold text-gray-800 dark:text-gray-100 mt-1",children:t})]}),e.jsx("div",{className:"p-3 rounded-lg",style:{background:a},children:e.jsx("div",{className:"text-white",children:s})})]}),d&&e.jsxs("div",{className:"mt-2 flex items-center",children:[e.jsx(n,{className:"w-4 h-4 text-emerald-600 dark:text-emerald-400 mr-1"}),e.jsxs("span",{className:"text-xs font-medium text-emerald-600 dark:text-emerald-400",children:[d," son 30 günde"]})]})]}),e.jsx("div",{className:"absolute -right-8 -bottom-8 w-32 h-32 rounded-full opacity-10 transform transition-transform group-hover:scale-110",style:{background:a}}),e.jsx("div",{className:"absolute -right-6 -top-6 w-16 h-16 rounded-full opacity-5 transform transition-transform group-hover:scale-110",style:{background:a}})]}),y={container:"overflow-x-auto rounded-lg border border-gray-200 dark:border-gray-700",table:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",thead:"bg-gray-50 dark:bg-gray-800",th:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",tbody:"bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700",td:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300"};export{n as A,g as P,m as S,y as T,x as c};
