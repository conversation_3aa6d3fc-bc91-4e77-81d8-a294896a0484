<?php
require_once __DIR__ . '/../config/config.php';
header('Content-Type: application/json');

try {
    $stmt = $db->query("SELECT COUNT(*) as total FROM announcements");
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    echo json_encode(['success' => true, 'data' => ['total' => (int)$row['total']]]);
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'İstatistikler alınamadı.']);
} 