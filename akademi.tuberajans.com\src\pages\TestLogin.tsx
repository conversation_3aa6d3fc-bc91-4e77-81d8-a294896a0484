// import React from 'react';

const TestLogin = () => {
  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Arka plan görseli - Ana sitedeki slider görseli */}
      <div className="absolute inset-0">
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `url('/images/arkaplannew.webp')`
          }}
        ></div>
        {/* Overlay gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/70 via-black/50 to-red-900/30"></div>
        {/* Subtle pattern */}
        <div className="absolute inset-0 opacity-20" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.03) 1px, transparent 1px)`,
          backgroundSize: '50px 50px'
        }}></div>
      </div>

      {/* Split screen layout - <PERSON><PERSON><PERSON> oran (3:2) */}
      <div className="relative z-10 flex min-h-screen flex-col lg:flex-row">
        {/* Sol bölüm - Portal tanıtımı (3/5 genişlik) */}
        <div className="relative lg:w-3/5 overflow-hidden lg:flex flex-col justify-center items-start hidden">
          {/* Content */}
          <div className="relative z-10 p-12 lg:p-24 flex flex-col items-start justify-center h-full max-w-4xl">
            {/* Logo */}
            <div className="mb-12">
              <img
                src="/images/logotuber1.png"
                alt="Tuber Ajans Logo"
                className="h-20 w-auto select-none"
                style={{objectFit: 'contain'}}
              />
            </div>

            {/* Main heading - Yayıncı Portalı için özelleştirilmiş */}
            <div className="text-left">
              <p className="text-red-500 text-base font-semibold mb-6 tracking-wide uppercase">
                Tuber Akademi Yayıncı Portalı
              </p>
              <h1 className="text-6xl md:text-7xl font-bold tracking-tight mb-8 leading-tight">
                <span className="text-white">Yayıncılık</span><br />
                <span className="text-white">yolculuğunuz burada.</span>
              </h1>
              <p className="text-2xl text-gray-300 max-w-2xl mb-12 leading-relaxed">
                Özel eğitimlerimizle becerilerinizi geliştirin, diğer yayıncılarla beyin fırtınası yapın,
                teknik destek alın ve topluluk ortamında birlikte büyüyün.
              </p>

              {/* Features List */}
              <div className="space-y-5 mb-8">
                <div className="flex items-center text-gray-300 text-lg">
                  <div className="w-3 h-3 bg-red-500 rounded-full mr-4"></div>
                  <span>Özel eğitim içerikleri ve canlı webinarlar</span>
                </div>
                <div className="flex items-center text-gray-300 text-lg">
                  <div className="w-3 h-3 bg-red-500 rounded-full mr-4"></div>
                  <span>Yayıncı topluluğu ve beyin fırtınası ortamı</span>
                </div>
                <div className="flex items-center text-gray-300 text-lg">
                  <div className="w-3 h-3 bg-red-500 rounded-full mr-4"></div>
                  <span>7/24 teknik destek ve ihlal yönetimi</span>
                </div>
                <div className="flex items-center text-gray-300 text-lg">
                  <div className="w-3 h-3 bg-red-500 rounded-full mr-4"></div>
                  <span>Özel etkinlikler ve networking fırsatları</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Sağ bölüm - Login formu (2/5 genişlik) */}
        <div className="lg:w-2/5 flex items-center justify-center p-6 lg:p-12 relative z-10 min-h-screen">
          <div className="w-full max-w-md">
            {/* Mobile logo */}
            <div className="lg:hidden mb-8 text-center">
              <img
                src="/images/logotuber1.png"
                alt="Tuber Ajans Logo"
                className="h-16 w-auto select-none mx-auto"
                style={{objectFit: 'contain'}}
              />
              <div className="mt-4">
                <h1 className="text-2xl font-bold text-white mb-2">Tuber Akademi</h1>
                <p className="text-red-500 text-sm font-medium tracking-wide uppercase">
                  Yayıncı Portalı
                </p>
              </div>
            </div>

            {/* Login container */}
            <div className="bg-gray-900/80 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-2xl p-8 transition-all duration-300">
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-white mb-2">Test Sayfası</h2>
                <p className="text-gray-300 text-sm">Arka plan görseli test ediliyor</p>
              </div>

              <div className="text-center">
                <p className="text-white text-lg">✅ Sayfa başarıyla yüklendi!</p>
                <p className="text-gray-300 mt-4">Ana sitedeki slider görseli arka planda görünüyor.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestLogin;
