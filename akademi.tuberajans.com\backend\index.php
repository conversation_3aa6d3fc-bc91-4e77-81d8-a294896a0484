<?php
/**
 * Akademi Backend Ana Sayfa
 * 
 * <PERSON><PERSON> dosya, akademi.tuberajans.com sitesi için backend ana sayfasını sağlar.
 * API'nin çalışıp çalışmadığını kontrol etmek için kullanılır.
 */

// CORS başlıkları
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept");
header("Content-Type: application/json; charset=utf-8");

// API bilgilerini döndür
$api_info = [
    'name' => 'Tuber Akademi API',
    'version' => '1.0.0',
    'status' => 'active',
    'endpoints' => [
        '/api/auth.php' => 'Kimlik doğrulama API\'si',
        '/api/test.php' => 'Test API\'si'
    ],
    'documentation' => 'Henüz mevcut değil',
    'contact' => '<EMAIL>',
    'time' => date('Y-m-d H:i:s')
];

// JSON yanıtı döndür
echo json_encode($api_info);
