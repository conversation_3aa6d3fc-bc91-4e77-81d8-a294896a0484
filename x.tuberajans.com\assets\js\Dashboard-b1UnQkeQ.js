import{j as e}from"./reactDnd-uQSTYBkW.js";import{r as x,L as n}from"./vendor-CnpYymF8.js";import{n as d,T as D,G as j,H as l,Y as o,k as m,s as R,Z as I,z as N,q as $}from"./antd-BfejY-CV.js";import{I as z,F as w,c as g,G as T,H as O,g as L,J as _,K as G}from"./App-C_UskXOj.js";import"./index-BVn_ohNQ.js";import"./utils-CtuI0RRe.js";import"./charts-6B1FLgFz.js";var E={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M917 211.1l-199.2 24c-6.6.8-9.4 8.9-4.7 13.6l59.3 59.3-226 226-101.8-101.7c-6.3-6.3-16.4-6.2-22.6 0L100.3 754.1a8.03 8.03 0 000 11.3l45 45.2c3.1 3.1 8.2 3.1 11.3 0L433.3 534 535 635.7c6.3 6.2 16.4 6.2 22.6 0L829 364.5l59.3 59.3a8.01 8.01 0 0013.6-4.7l24-199.2c.7-5.1-3.7-9.5-8.9-8.8z"}}]},name:"rise",theme:"outlined"};function y(){return y=Object.assign?Object.assign.bind():function(c){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var u in a)Object.prototype.hasOwnProperty.call(a,u)&&(c[u]=a[u])}return c},y.apply(this,arguments)}const B=(c,t)=>x.createElement(z,y({},c,{ref:t,icon:E})),A=x.forwardRef(B),{Title:k,Text:Y}=D,{TabPane:q}=$,J=()=>{const[c,t]=x.useState(!1),[a,u]=x.useState({bekleyenTasarim:5,onaylananTasarim:12,reddedilenTasarim:3,toplamTasarim:20,aktifUrun:45,bekleyenUrun:8,toplamSatis:128,gunlukSatis:7,haftalikSatis:32}),[S,f]=x.useState([]),p=[{id:1,title:"Abstract Watercolor Art",created_at:"2023-06-15T10:30:00",status:"beklemede",category:"Soyut"},{id:2,title:"Mountain Landscape",created_at:"2023-06-14T14:45:00",status:"onaylandi",category:"Manzara"},{id:3,title:"Cosmic Space Scene",created_at:"2023-06-14T09:15:00",status:"reddedildi",category:"Uzay"},{id:4,title:"Minimalist Geometry",created_at:"2023-06-13T16:30:00",status:"onaylandi",category:"Geometrik"},{id:5,title:"Surreal Dreamscape",created_at:"2023-06-13T11:30:00",status:"onaylandi",category:"Fantezi"}],v=async()=>{t(!0);try{setTimeout(()=>{f(p),t(!1)},1e3)}catch(r){f(p),t(!1)}};x.useEffect(()=>{v()},[]);const b=[{title:"ID",dataIndex:"id",key:"id",width:80},{title:"Tasarım Adı",dataIndex:"title",key:"title",render:(r,s)=>e.jsx(n,{to:`/etsy/tasarim-onaylari?id=${s.id}`,children:r})},{title:"Kategori",dataIndex:"category",key:"category"},{title:"Durum",dataIndex:"status",key:"status",render:r=>{let s="",i="",h=null;switch(r){case"beklemede":s="blue",i="Beklemede",h=e.jsx(G,{});break;case"onaylandi":s="green",i="Onaylandı",h=e.jsx(g,{});break;case"reddedildi":s="red",i="Reddedildi",h=e.jsx(T,{});break;default:s="default",i="Bilinmiyor"}return e.jsx(N,{color:s,icon:h,children:i})}},{title:"Oluşturma Tarihi",dataIndex:"created_at",key:"created_at",render:r=>{const s=new Date(r).toLocaleDateString("tr-TR"),i=new Date(r).toLocaleTimeString("tr-TR",{hour:"2-digit",minute:"2-digit"});return`${s} ${i}`}}];return e.jsx("div",{className:"p-4",children:e.jsxs(d,{children:[e.jsx(k,{level:3,children:"ETSY Operasyonu Dashboard"}),e.jsxs(j,{gutter:[16,16],className:"mb-4",children:[e.jsx(l,{xs:24,sm:12,md:8,lg:6,children:e.jsxs(d,{className:"h-full",children:[e.jsx(o,{title:"Bekleyen Tasarımlar",value:a.bekleyenTasarim,prefix:e.jsx(w,{}),valueStyle:{color:"#1890ff"}}),e.jsx("div",{className:"mt-2",children:e.jsx(m,{type:"link",size:"small",children:e.jsx(n,{to:"/etsy/tasarim-onaylari",children:"Detaylar"})})})]})}),e.jsx(l,{xs:24,sm:12,md:8,lg:6,children:e.jsxs(d,{className:"h-full",children:[e.jsx(o,{title:"Onaylanan Tasarımlar",value:a.onaylananTasarim,prefix:e.jsx(g,{}),valueStyle:{color:"#52c41a"}}),e.jsx("div",{className:"mt-2",children:e.jsx(m,{type:"link",size:"small",children:e.jsx(n,{to:"/etsy/tasarim-onaylari",children:"Detaylar"})})})]})}),e.jsx(l,{xs:24,sm:12,md:8,lg:6,children:e.jsxs(d,{className:"h-full",children:[e.jsx(o,{title:"Reddedilen Tasarımlar",value:a.reddedilenTasarim,prefix:e.jsx(T,{}),valueStyle:{color:"#ff4d4f"}}),e.jsx("div",{className:"mt-2",children:e.jsx(m,{type:"link",size:"small",children:e.jsx(n,{to:"/etsy/tasarim-onaylari",children:"Detaylar"})})})]})}),e.jsx(l,{xs:24,sm:12,md:8,lg:6,children:e.jsxs(d,{className:"h-full",children:[e.jsx(o,{title:"Aktif Ürünler",value:a.aktifUrun,prefix:e.jsx(O,{}),valueStyle:{color:"#722ed1"}}),e.jsx("div",{className:"mt-2",children:e.jsx(m,{type:"link",size:"small",children:e.jsx(n,{to:"/etsy/urunler",children:"Detaylar"})})})]})})]}),e.jsxs(j,{gutter:[16,16],children:[e.jsx(l,{xs:24,md:12,children:e.jsx(d,{title:"Son Tasarımlar",extra:e.jsx(n,{to:"/etsy/tasarim-onaylari",children:"Tümünü Gör"}),children:e.jsx(R,{dataSource:S,columns:b,rowKey:"id",pagination:!1,loading:c,size:"small"})})}),e.jsx(l,{xs:24,md:12,children:e.jsxs(d,{title:"Satış İstatistikleri",extra:e.jsx(m,{type:"text",icon:e.jsx(_,{})}),children:[e.jsxs(j,{gutter:[16,16],children:[e.jsx(l,{span:12,children:e.jsx(o,{title:"Toplam Satış",value:a.toplamSatis,prefix:e.jsx(L,{})})}),e.jsx(l,{span:12,children:e.jsx(o,{title:"Günlük Satış",value:a.gunlukSatis,prefix:e.jsx(A,{}),valueStyle:{color:"#3f8600"}})})]}),e.jsxs("div",{className:"mt-4",children:[e.jsx(k,{level:5,children:"Haftalık Hedef"}),e.jsx(I,{percent:a.haftalikSatis/50*100,format:()=>`${a.haftalikSatis}/50`})]}),e.jsx("div",{className:"mt-4",children:e.jsx(m,{type:"link",size:"small",children:e.jsx(n,{to:"/etsy/urunler",children:"Satış Raporunu Gör"})})})]})})]})]})})};export{J as default};
