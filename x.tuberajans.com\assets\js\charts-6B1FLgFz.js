import{_ as qw,r as Lw}from"./antd-BfejY-CV.js";import{g as ae,d as Ii,r as q,o as A}from"./vendor-CnpYymF8.js";function Kq(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,qw(e,t)}var Ro={exports:{}},Do,fh;function Bw(){if(fh)return Do;fh=1;var e="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";return Do=e,Do}var No,ph;function Fw(){if(ph)return No;ph=1;var e=Bw();function t(){}function r(){}return r.resetWarningCache=t,No=function(){function n(o,u,c,s,f,l){if(l!==e){var p=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw p.name="Invariant Violation",p}}n.isRequired=n;function i(){return n}var a={array:n,bigint:n,bool:n,func:n,number:n,object:n,string:n,symbol:n,any:n,arrayOf:i,element:n,elementType:n,instanceOf:i,node:n,objectOf:i,oneOf:i,oneOfType:i,shape:i,exact:i,checkPropTypes:r,resetWarningCache:t};return a.PropTypes=a,a},No}var hh;function zw(){return hh||(hh=1,Ro.exports=Fw()()),Ro.exports}var Ww=zw();const ne=ae(Ww);var Uw="Invariant failed";function sr(e,t){throw new Error(Uw)}function s0(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e)){var i=e.length;for(t=0;t<i;t++)e[t]&&(r=s0(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function Z(){for(var e,t,r=0,n="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=s0(e))&&(n&&(n+=" "),n+=t);return n}var qo,dh;function Le(){if(dh)return qo;dh=1;var e=Array.isArray;return qo=e,qo}var Lo,vh;function l0(){if(vh)return Lo;vh=1;var e=typeof Ii=="object"&&Ii&&Ii.Object===Object&&Ii;return Lo=e,Lo}var Bo,yh;function ht(){if(yh)return Bo;yh=1;var e=l0(),t=typeof self=="object"&&self&&self.Object===Object&&self,r=e||t||Function("return this")();return Bo=r,Bo}var Fo,mh;function xi(){if(mh)return Fo;mh=1;var e=ht(),t=e.Symbol;return Fo=t,Fo}var zo,gh;function Kw(){if(gh)return zo;gh=1;var e=xi(),t=Object.prototype,r=t.hasOwnProperty,n=t.toString,i=e?e.toStringTag:void 0;function a(o){var u=r.call(o,i),c=o[i];try{o[i]=void 0;var s=!0}catch(l){}var f=n.call(o);return s&&(u?o[i]=c:delete o[i]),f}return zo=a,zo}var Wo,bh;function Hw(){if(bh)return Wo;bh=1;var e=Object.prototype,t=e.toString;function r(n){return t.call(n)}return Wo=r,Wo}var Uo,xh;function Pt(){if(xh)return Uo;xh=1;var e=xi(),t=Kw(),r=Hw(),n="[object Null]",i="[object Undefined]",a=e?e.toStringTag:void 0;function o(u){return u==null?u===void 0?i:n:a&&a in Object(u)?t(u):r(u)}return Uo=o,Uo}var Ko,Oh;function Tt(){if(Oh)return Ko;Oh=1;function e(t){return t!=null&&typeof t=="object"}return Ko=e,Ko}var Ho,wh;function sn(){if(wh)return Ho;wh=1;var e=Pt(),t=Tt(),r="[object Symbol]";function n(i){return typeof i=="symbol"||t(i)&&e(i)==r}return Ho=n,Ho}var Go,_h;function op(){if(_h)return Go;_h=1;var e=Le(),t=sn(),r=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,n=/^\w*$/;function i(a,o){if(e(a))return!1;var u=typeof a;return u=="number"||u=="symbol"||u=="boolean"||a==null||t(a)?!0:n.test(a)||!r.test(a)||o!=null&&a in Object(o)}return Go=i,Go}var Vo,Ah;function Ft(){if(Ah)return Vo;Ah=1;function e(t){var r=typeof t;return t!=null&&(r=="object"||r=="function")}return Vo=e,Vo}var Xo,Sh;function up(){if(Sh)return Xo;Sh=1;var e=Pt(),t=Ft(),r="[object AsyncFunction]",n="[object Function]",i="[object GeneratorFunction]",a="[object Proxy]";function o(u){if(!t(u))return!1;var c=e(u);return c==n||c==i||c==r||c==a}return Xo=o,Xo}var Yo,Ph;function Gw(){if(Ph)return Yo;Ph=1;var e=ht(),t=e["__core-js_shared__"];return Yo=t,Yo}var Zo,Th;function Vw(){if(Th)return Zo;Th=1;var e=Gw(),t=function(){var n=/[^.]+$/.exec(e&&e.keys&&e.keys.IE_PROTO||"");return n?"Symbol(src)_1."+n:""}();function r(n){return!!t&&t in n}return Zo=r,Zo}var Jo,Eh;function f0(){if(Eh)return Jo;Eh=1;var e=Function.prototype,t=e.toString;function r(n){if(n!=null){try{return t.call(n)}catch(i){}try{return n+""}catch(i){}}return""}return Jo=r,Jo}var Qo,jh;function Xw(){if(jh)return Qo;jh=1;var e=up(),t=Vw(),r=Ft(),n=f0(),i=/[\\^$.*+?()[\]{}|]/g,a=/^\[object .+?Constructor\]$/,o=Function.prototype,u=Object.prototype,c=o.toString,s=u.hasOwnProperty,f=RegExp("^"+c.call(s).replace(i,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function l(p){if(!r(p)||t(p))return!1;var h=e(p)?f:a;return h.test(n(p))}return Qo=l,Qo}var eu,$h;function Yw(){if($h)return eu;$h=1;function e(t,r){return t==null?void 0:t[r]}return eu=e,eu}var tu,Mh;function hr(){if(Mh)return tu;Mh=1;var e=Xw(),t=Yw();function r(n,i){var a=t(n,i);return e(a)?a:void 0}return tu=r,tu}var ru,Ih;function Va(){if(Ih)return ru;Ih=1;var e=hr(),t=e(Object,"create");return ru=t,ru}var nu,Ch;function Zw(){if(Ch)return nu;Ch=1;var e=Va();function t(){this.__data__=e?e(null):{},this.size=0}return nu=t,nu}var iu,kh;function Jw(){if(kh)return iu;kh=1;function e(t){var r=this.has(t)&&delete this.__data__[t];return this.size-=r?1:0,r}return iu=e,iu}var au,Rh;function Qw(){if(Rh)return au;Rh=1;var e=Va(),t="__lodash_hash_undefined__",r=Object.prototype,n=r.hasOwnProperty;function i(a){var o=this.__data__;if(e){var u=o[a];return u===t?void 0:u}return n.call(o,a)?o[a]:void 0}return au=i,au}var ou,Dh;function e_(){if(Dh)return ou;Dh=1;var e=Va(),t=Object.prototype,r=t.hasOwnProperty;function n(i){var a=this.__data__;return e?a[i]!==void 0:r.call(a,i)}return ou=n,ou}var uu,Nh;function t_(){if(Nh)return uu;Nh=1;var e=Va(),t="__lodash_hash_undefined__";function r(n,i){var a=this.__data__;return this.size+=this.has(n)?0:1,a[n]=e&&i===void 0?t:i,this}return uu=r,uu}var cu,qh;function r_(){if(qh)return cu;qh=1;var e=Zw(),t=Jw(),r=Qw(),n=e_(),i=t_();function a(o){var u=-1,c=o==null?0:o.length;for(this.clear();++u<c;){var s=o[u];this.set(s[0],s[1])}}return a.prototype.clear=e,a.prototype.delete=t,a.prototype.get=r,a.prototype.has=n,a.prototype.set=i,cu=a,cu}var su,Lh;function n_(){if(Lh)return su;Lh=1;function e(){this.__data__=[],this.size=0}return su=e,su}var lu,Bh;function cp(){if(Bh)return lu;Bh=1;function e(t,r){return t===r||t!==t&&r!==r}return lu=e,lu}var fu,Fh;function Xa(){if(Fh)return fu;Fh=1;var e=cp();function t(r,n){for(var i=r.length;i--;)if(e(r[i][0],n))return i;return-1}return fu=t,fu}var pu,zh;function i_(){if(zh)return pu;zh=1;var e=Xa(),t=Array.prototype,r=t.splice;function n(i){var a=this.__data__,o=e(a,i);if(o<0)return!1;var u=a.length-1;return o==u?a.pop():r.call(a,o,1),--this.size,!0}return pu=n,pu}var hu,Wh;function a_(){if(Wh)return hu;Wh=1;var e=Xa();function t(r){var n=this.__data__,i=e(n,r);return i<0?void 0:n[i][1]}return hu=t,hu}var du,Uh;function o_(){if(Uh)return du;Uh=1;var e=Xa();function t(r){return e(this.__data__,r)>-1}return du=t,du}var vu,Kh;function u_(){if(Kh)return vu;Kh=1;var e=Xa();function t(r,n){var i=this.__data__,a=e(i,r);return a<0?(++this.size,i.push([r,n])):i[a][1]=n,this}return vu=t,vu}var yu,Hh;function Ya(){if(Hh)return yu;Hh=1;var e=n_(),t=i_(),r=a_(),n=o_(),i=u_();function a(o){var u=-1,c=o==null?0:o.length;for(this.clear();++u<c;){var s=o[u];this.set(s[0],s[1])}}return a.prototype.clear=e,a.prototype.delete=t,a.prototype.get=r,a.prototype.has=n,a.prototype.set=i,yu=a,yu}var mu,Gh;function sp(){if(Gh)return mu;Gh=1;var e=hr(),t=ht(),r=e(t,"Map");return mu=r,mu}var gu,Vh;function c_(){if(Vh)return gu;Vh=1;var e=r_(),t=Ya(),r=sp();function n(){this.size=0,this.__data__={hash:new e,map:new(r||t),string:new e}}return gu=n,gu}var bu,Xh;function s_(){if(Xh)return bu;Xh=1;function e(t){var r=typeof t;return r=="string"||r=="number"||r=="symbol"||r=="boolean"?t!=="__proto__":t===null}return bu=e,bu}var xu,Yh;function Za(){if(Yh)return xu;Yh=1;var e=s_();function t(r,n){var i=r.__data__;return e(n)?i[typeof n=="string"?"string":"hash"]:i.map}return xu=t,xu}var Ou,Zh;function l_(){if(Zh)return Ou;Zh=1;var e=Za();function t(r){var n=e(this,r).delete(r);return this.size-=n?1:0,n}return Ou=t,Ou}var wu,Jh;function f_(){if(Jh)return wu;Jh=1;var e=Za();function t(r){return e(this,r).get(r)}return wu=t,wu}var _u,Qh;function p_(){if(Qh)return _u;Qh=1;var e=Za();function t(r){return e(this,r).has(r)}return _u=t,_u}var Au,ed;function h_(){if(ed)return Au;ed=1;var e=Za();function t(r,n){var i=e(this,r),a=i.size;return i.set(r,n),this.size+=i.size==a?0:1,this}return Au=t,Au}var Su,td;function lp(){if(td)return Su;td=1;var e=c_(),t=l_(),r=f_(),n=p_(),i=h_();function a(o){var u=-1,c=o==null?0:o.length;for(this.clear();++u<c;){var s=o[u];this.set(s[0],s[1])}}return a.prototype.clear=e,a.prototype.delete=t,a.prototype.get=r,a.prototype.has=n,a.prototype.set=i,Su=a,Su}var Pu,rd;function p0(){if(rd)return Pu;rd=1;var e=lp(),t="Expected a function";function r(n,i){if(typeof n!="function"||i!=null&&typeof i!="function")throw new TypeError(t);var a=function(){var o=arguments,u=i?i.apply(this,o):o[0],c=a.cache;if(c.has(u))return c.get(u);var s=n.apply(this,o);return a.cache=c.set(u,s)||c,s};return a.cache=new(r.Cache||e),a}return r.Cache=e,Pu=r,Pu}var Tu,nd;function d_(){if(nd)return Tu;nd=1;var e=p0(),t=500;function r(n){var i=e(n,function(o){return a.size===t&&a.clear(),o}),a=i.cache;return i}return Tu=r,Tu}var Eu,id;function v_(){if(id)return Eu;id=1;var e=d_(),t=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,r=/\\(\\)?/g,n=e(function(i){var a=[];return i.charCodeAt(0)===46&&a.push(""),i.replace(t,function(o,u,c,s){a.push(c?s.replace(r,"$1"):u||o)}),a});return Eu=n,Eu}var ju,ad;function fp(){if(ad)return ju;ad=1;function e(t,r){for(var n=-1,i=t==null?0:t.length,a=Array(i);++n<i;)a[n]=r(t[n],n,t);return a}return ju=e,ju}var $u,od;function y_(){if(od)return $u;od=1;var e=xi(),t=fp(),r=Le(),n=sn(),i=e?e.prototype:void 0,a=i?i.toString:void 0;function o(u){if(typeof u=="string")return u;if(r(u))return t(u,o)+"";if(n(u))return a?a.call(u):"";var c=u+"";return c=="0"&&1/u==-1/0?"-0":c}return $u=o,$u}var Mu,ud;function h0(){if(ud)return Mu;ud=1;var e=y_();function t(r){return r==null?"":e(r)}return Mu=t,Mu}var Iu,cd;function d0(){if(cd)return Iu;cd=1;var e=Le(),t=op(),r=v_(),n=h0();function i(a,o){return e(a)?a:t(a,o)?[a]:r(n(a))}return Iu=i,Iu}var Cu,sd;function Ja(){if(sd)return Cu;sd=1;var e=sn();function t(r){if(typeof r=="string"||e(r))return r;var n=r+"";return n=="0"&&1/r==-1/0?"-0":n}return Cu=t,Cu}var ku,ld;function pp(){if(ld)return ku;ld=1;var e=d0(),t=Ja();function r(n,i){i=e(i,n);for(var a=0,o=i.length;n!=null&&a<o;)n=n[t(i[a++])];return a&&a==o?n:void 0}return ku=r,ku}var Ru,fd;function v0(){if(fd)return Ru;fd=1;var e=pp();function t(r,n,i){var a=r==null?void 0:e(r,n);return a===void 0?i:a}return Ru=t,Ru}var m_=v0();const Ke=ae(m_);var Du,pd;function g_(){if(pd)return Du;pd=1;function e(t){return t==null}return Du=e,Du}var b_=g_();const G=ae(b_);var Nu,hd;function x_(){if(hd)return Nu;hd=1;var e=Pt(),t=Le(),r=Tt(),n="[object String]";function i(a){return typeof a=="string"||!t(a)&&r(a)&&e(a)==n}return Nu=i,Nu}var O_=x_();const Oi=ae(O_);var w_=up();const H=ae(w_);var __=Ft();const ln=ae(__);var qu,dd;function y0(){if(dd)return qu;dd=1;var e=Pt(),t=Tt(),r="[object Number]";function n(i){return typeof i=="number"||t(i)&&e(i)==r}return qu=n,qu}var Lu,vd;function A_(){if(vd)return Lu;vd=1;var e=y0();function t(r){return e(r)&&r!=+r}return Lu=t,Lu}var S_=A_();const fn=ae(S_);var P_=y0();const T_=ae(P_);var Ie=function(t){return t===0?0:t>0?1:-1},rr=function(t){return Oi(t)&&t.indexOf("%")===t.length-1},N=function(t){return T_(t)&&!fn(t)},_e=function(t){return N(t)||Oi(t)},E_=0,zt=function(t){var r=++E_;return"".concat(t||"").concat(r)},Ce=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(!N(t)&&!Oi(t))return n;var a;if(rr(t)){var o=t.indexOf("%");a=r*parseFloat(t.slice(0,o))/100}else a=+t;return fn(a)&&(a=n),i&&a>r&&(a=r),a},Ct=function(t){if(!t)return null;var r=Object.keys(t);return r&&r.length?t[r[0]]:null},j_=function(t){if(!Array.isArray(t))return!1;for(var r=t.length,n={},i=0;i<r;i++)if(!n[t[i]])n[t[i]]=!0;else return!0;return!1},de=function(t,r){return N(t)&&N(r)?function(n){return t+n*(r-t)}:function(){return r}};function Gi(e,t,r){return!e||!e.length?null:e.find(function(n){return n&&(typeof t=="function"?t(n):Ke(n,t))===r})}var $_=function(t){if(!t||!t.length)return null;for(var r=t.length,n=0,i=0,a=0,o=0,u=1/0,c=-1/0,s=0,f=0,l=0;l<r;l++)s=t[l].cx||0,f=t[l].cy||0,n+=s,i+=f,a+=s*f,o+=s*s,u=Math.min(u,s),c=Math.max(c,s);var p=r*o!==n*n?(r*a-n*i)/(r*o-n*n):0;return{xmin:u,xmax:c,a:p,b:(i-p*n)/r}};function jr(e,t){for(var r in e)if({}.hasOwnProperty.call(e,r)&&(!{}.hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if({}.hasOwnProperty.call(t,n)&&!{}.hasOwnProperty.call(e,n))return!1;return!0}function Rl(e){"@babel/helpers - typeof";return Rl=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Rl(e)}var M_=["viewBox","children"],I_=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],yd=["points","pathLength"],Bu={svg:M_,polygon:yd,polyline:yd},hp=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],Vi=function(t,r){if(!t||typeof t=="function"||typeof t=="boolean")return null;var n=t;if(q.isValidElement(t)&&(n=t.props),!ln(n))return null;var i={};return Object.keys(n).forEach(function(a){hp.includes(a)&&(i[a]=r||function(o){return n[a](n,o)})}),i},C_=function(t,r,n){return function(i){return t(r,n,i),null}},Lt=function(t,r,n){if(!ln(t)||Rl(t)!=="object")return null;var i=null;return Object.keys(t).forEach(function(a){var o=t[a];hp.includes(a)&&typeof o=="function"&&(i||(i={}),i[a]=C_(o,r,n))}),i},k_=["children"],R_=["children"];function md(e,t){if(e==null)return{};var r=D_(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function D_(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Dl(e){"@babel/helpers - typeof";return Dl=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Dl(e)}var gd={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},Ot=function(t){return typeof t=="string"?t:t?t.displayName||t.name||"Component":""},bd=null,Fu=null,dp=function e(t){if(t===bd&&Array.isArray(Fu))return Fu;var r=[];return q.Children.forEach(t,function(n){G(n)||(Lw.isFragment(n)?r=r.concat(e(n.props.children)):r.push(n))}),Fu=r,bd=t,r};function Re(e,t){var r=[],n=[];return Array.isArray(t)?n=t.map(function(i){return Ot(i)}):n=[Ot(t)],dp(e).forEach(function(i){var a=Ke(i,"type.displayName")||Ke(i,"type.name");n.indexOf(a)!==-1&&r.push(i)}),r}function We(e,t){var r=Re(e,t);return r&&r[0]}var xd=function(t){if(!t||!t.props)return!1;var r=t.props,n=r.width,i=r.height;return!(!N(n)||n<=0||!N(i)||i<=0)},N_=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],q_=function(t){return t&&t.type&&Oi(t.type)&&N_.indexOf(t.type)>=0},m0=function(t){return t&&Dl(t)==="object"&&"clipDot"in t},L_=function(t,r,n,i){var a,o=(a=Bu==null?void 0:Bu[i])!==null&&a!==void 0?a:[];return r.startsWith("data-")||!H(t)&&(i&&o.includes(r)||I_.includes(r))||n&&hp.includes(r)},U=function(t,r,n){if(!t||typeof t=="function"||typeof t=="boolean")return null;var i=t;if(q.isValidElement(t)&&(i=t.props),!ln(i))return null;var a={};return Object.keys(i).forEach(function(o){var u;L_((u=i)===null||u===void 0?void 0:u[o],o,r,n)&&(a[o]=i[o])}),a},Nl=function e(t,r){if(t===r)return!0;var n=q.Children.count(t);if(n!==q.Children.count(r))return!1;if(n===0)return!0;if(n===1)return Od(Array.isArray(t)?t[0]:t,Array.isArray(r)?r[0]:r);for(var i=0;i<n;i++){var a=t[i],o=r[i];if(Array.isArray(a)||Array.isArray(o)){if(!e(a,o))return!1}else if(!Od(a,o))return!1}return!0},Od=function(t,r){if(G(t)&&G(r))return!0;if(!G(t)&&!G(r)){var n=t.props||{},i=n.children,a=md(n,k_),o=r.props||{},u=o.children,c=md(o,R_);return i&&u?jr(a,c)&&Nl(i,u):!i&&!u?jr(a,c):!1}return!1},wd=function(t,r){var n=[],i={};return dp(t).forEach(function(a,o){if(q_(a))n.push(a);else if(a){var u=Ot(a.type),c=r[u]||{},s=c.handler,f=c.once;if(s&&(!f||!i[u])){var l=s(a,u,o);n.push(l),i[u]=!0}}}),n},B_=function(t){var r=t&&t.type;return r&&gd[r]?gd[r]:null},F_=function(t,r){return dp(r).indexOf(t)},z_=["children","width","height","viewBox","className","style","title","desc"];function ql(){return ql=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ql.apply(this,arguments)}function W_(e,t){if(e==null)return{};var r=U_(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function U_(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Ll(e){var t=e.children,r=e.width,n=e.height,i=e.viewBox,a=e.className,o=e.style,u=e.title,c=e.desc,s=W_(e,z_),f=i||{width:r,height:n,x:0,y:0},l=Z("recharts-surface",a);return A.createElement("svg",ql({},U(s,!0,"svg"),{className:l,width:r,height:n,style:o,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),A.createElement("title",null,u),A.createElement("desc",null,c),t)}var K_=["children","className"];function Bl(){return Bl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Bl.apply(this,arguments)}function H_(e,t){if(e==null)return{};var r=G_(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function G_(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var J=A.forwardRef(function(e,t){var r=e.children,n=e.className,i=H_(e,K_),a=Z("recharts-layer",n);return A.createElement("g",Bl({className:a},U(i,!0),{ref:t}),r)}),at=function(t,r){for(var n=arguments.length,i=new Array(n>2?n-2:0),a=2;a<n;a++)i[a-2]=arguments[a]},zu,_d;function V_(){if(_d)return zu;_d=1;function e(t,r,n){var i=-1,a=t.length;r<0&&(r=-r>a?0:a+r),n=n>a?a:n,n<0&&(n+=a),a=r>n?0:n-r>>>0,r>>>=0;for(var o=Array(a);++i<a;)o[i]=t[i+r];return o}return zu=e,zu}var Wu,Ad;function X_(){if(Ad)return Wu;Ad=1;var e=V_();function t(r,n,i){var a=r.length;return i=i===void 0?a:i,!n&&i>=a?r:e(r,n,i)}return Wu=t,Wu}var Uu,Sd;function g0(){if(Sd)return Uu;Sd=1;var e="\\ud800-\\udfff",t="\\u0300-\\u036f",r="\\ufe20-\\ufe2f",n="\\u20d0-\\u20ff",i=t+r+n,a="\\ufe0e\\ufe0f",o="\\u200d",u=RegExp("["+o+e+i+a+"]");function c(s){return u.test(s)}return Uu=c,Uu}var Ku,Pd;function Y_(){if(Pd)return Ku;Pd=1;function e(t){return t.split("")}return Ku=e,Ku}var Hu,Td;function Z_(){if(Td)return Hu;Td=1;var e="\\ud800-\\udfff",t="\\u0300-\\u036f",r="\\ufe20-\\ufe2f",n="\\u20d0-\\u20ff",i=t+r+n,a="\\ufe0e\\ufe0f",o="["+e+"]",u="["+i+"]",c="\\ud83c[\\udffb-\\udfff]",s="(?:"+u+"|"+c+")",f="[^"+e+"]",l="(?:\\ud83c[\\udde6-\\uddff]){2}",p="[\\ud800-\\udbff][\\udc00-\\udfff]",h="\\u200d",v=s+"?",d="["+a+"]?",y="(?:"+h+"(?:"+[f,l,p].join("|")+")"+d+v+")*",b=d+v+y,g="(?:"+[f+u+"?",u,l,p,o].join("|")+")",x=RegExp(c+"(?="+c+")|"+g+b,"g");function w(m){return m.match(x)||[]}return Hu=w,Hu}var Gu,Ed;function J_(){if(Ed)return Gu;Ed=1;var e=Y_(),t=g0(),r=Z_();function n(i){return t(i)?r(i):e(i)}return Gu=n,Gu}var Vu,jd;function Q_(){if(jd)return Vu;jd=1;var e=X_(),t=g0(),r=J_(),n=h0();function i(a){return function(o){o=n(o);var u=t(o)?r(o):void 0,c=u?u[0]:o.charAt(0),s=u?e(u,1).join(""):o.slice(1);return c[a]()+s}}return Vu=i,Vu}var Xu,$d;function eA(){if($d)return Xu;$d=1;var e=Q_(),t=e("toUpperCase");return Xu=t,Xu}var tA=eA();const Qa=ae(tA);function ce(e){return function(){return e}}const b0=Math.cos,Xi=Math.sin,ut=Math.sqrt,Yi=Math.PI,eo=2*Yi,Fl=Math.PI,zl=2*Fl,Qt=1e-6,rA=zl-Qt;function x0(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}function nA(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return x0;const r=10**t;return function(n){this._+=n[0];for(let i=1,a=n.length;i<a;++i)this._+=Math.round(arguments[i]*r)/r+n[i]}}class iA{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=t==null?x0:nA(t)}moveTo(t,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,r){this._append`L${this._x1=+t},${this._y1=+r}`}quadraticCurveTo(t,r,n,i){this._append`Q${+t},${+r},${this._x1=+n},${this._y1=+i}`}bezierCurveTo(t,r,n,i,a,o){this._append`C${+t},${+r},${+n},${+i},${this._x1=+a},${this._y1=+o}`}arcTo(t,r,n,i,a){if(t=+t,r=+r,n=+n,i=+i,a=+a,a<0)throw new Error(`negative radius: ${a}`);let o=this._x1,u=this._y1,c=n-t,s=i-r,f=o-t,l=u-r,p=f*f+l*l;if(this._x1===null)this._append`M${this._x1=t},${this._y1=r}`;else if(p>Qt)if(!(Math.abs(l*c-s*f)>Qt)||!a)this._append`L${this._x1=t},${this._y1=r}`;else{let h=n-o,v=i-u,d=c*c+s*s,y=h*h+v*v,b=Math.sqrt(d),g=Math.sqrt(p),x=a*Math.tan((Fl-Math.acos((d+p-y)/(2*b*g)))/2),w=x/g,m=x/b;Math.abs(w-1)>Qt&&this._append`L${t+w*f},${r+w*l}`,this._append`A${a},${a},0,0,${+(l*h>f*v)},${this._x1=t+m*c},${this._y1=r+m*s}`}}arc(t,r,n,i,a,o){if(t=+t,r=+r,n=+n,o=!!o,n<0)throw new Error(`negative radius: ${n}`);let u=n*Math.cos(i),c=n*Math.sin(i),s=t+u,f=r+c,l=1^o,p=o?i-a:a-i;this._x1===null?this._append`M${s},${f}`:(Math.abs(this._x1-s)>Qt||Math.abs(this._y1-f)>Qt)&&this._append`L${s},${f}`,n&&(p<0&&(p=p%zl+zl),p>rA?this._append`A${n},${n},0,1,${l},${t-u},${r-c}A${n},${n},0,1,${l},${this._x1=s},${this._y1=f}`:p>Qt&&this._append`A${n},${n},0,${+(p>=Fl)},${l},${this._x1=t+n*Math.cos(a)},${this._y1=r+n*Math.sin(a)}`)}rect(t,r,n,i){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}h${n=+n}v${+i}h${-n}Z`}toString(){return this._}}function vp(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(r==null)t=null;else{const n=Math.floor(r);if(!(n>=0))throw new RangeError(`invalid digits: ${r}`);t=n}return e},()=>new iA(t)}function yp(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function O0(e){this._context=e}O0.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t);break}}};function to(e){return new O0(e)}function w0(e){return e[0]}function _0(e){return e[1]}function A0(e,t){var r=ce(!0),n=null,i=to,a=null,o=vp(u);e=typeof e=="function"?e:e===void 0?w0:ce(e),t=typeof t=="function"?t:t===void 0?_0:ce(t);function u(c){var s,f=(c=yp(c)).length,l,p=!1,h;for(n==null&&(a=i(h=o())),s=0;s<=f;++s)!(s<f&&r(l=c[s],s,c))===p&&((p=!p)?a.lineStart():a.lineEnd()),p&&a.point(+e(l,s,c),+t(l,s,c));if(h)return a=null,h+""||null}return u.x=function(c){return arguments.length?(e=typeof c=="function"?c:ce(+c),u):e},u.y=function(c){return arguments.length?(t=typeof c=="function"?c:ce(+c),u):t},u.defined=function(c){return arguments.length?(r=typeof c=="function"?c:ce(!!c),u):r},u.curve=function(c){return arguments.length?(i=c,n!=null&&(a=i(n)),u):i},u.context=function(c){return arguments.length?(c==null?n=a=null:a=i(n=c),u):n},u}function Ci(e,t,r){var n=null,i=ce(!0),a=null,o=to,u=null,c=vp(s);e=typeof e=="function"?e:e===void 0?w0:ce(+e),t=typeof t=="function"?t:ce(t===void 0?0:+t),r=typeof r=="function"?r:r===void 0?_0:ce(+r);function s(l){var p,h,v,d=(l=yp(l)).length,y,b=!1,g,x=new Array(d),w=new Array(d);for(a==null&&(u=o(g=c())),p=0;p<=d;++p){if(!(p<d&&i(y=l[p],p,l))===b)if(b=!b)h=p,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),v=p-1;v>=h;--v)u.point(x[v],w[v]);u.lineEnd(),u.areaEnd()}b&&(x[p]=+e(y,p,l),w[p]=+t(y,p,l),u.point(n?+n(y,p,l):x[p],r?+r(y,p,l):w[p]))}if(g)return u=null,g+""||null}function f(){return A0().defined(i).curve(o).context(a)}return s.x=function(l){return arguments.length?(e=typeof l=="function"?l:ce(+l),n=null,s):e},s.x0=function(l){return arguments.length?(e=typeof l=="function"?l:ce(+l),s):e},s.x1=function(l){return arguments.length?(n=l==null?null:typeof l=="function"?l:ce(+l),s):n},s.y=function(l){return arguments.length?(t=typeof l=="function"?l:ce(+l),r=null,s):t},s.y0=function(l){return arguments.length?(t=typeof l=="function"?l:ce(+l),s):t},s.y1=function(l){return arguments.length?(r=l==null?null:typeof l=="function"?l:ce(+l),s):r},s.lineX0=s.lineY0=function(){return f().x(e).y(t)},s.lineY1=function(){return f().x(e).y(r)},s.lineX1=function(){return f().x(n).y(t)},s.defined=function(l){return arguments.length?(i=typeof l=="function"?l:ce(!!l),s):i},s.curve=function(l){return arguments.length?(o=l,a!=null&&(u=o(a)),s):o},s.context=function(l){return arguments.length?(l==null?a=u=null:u=o(a=l),s):a},s}class S0{constructor(t,r){this._context=t,this._x=r}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(t,r){switch(t=+t,r=+r,this._point){case 0:{this._point=1,this._line?this._context.lineTo(t,r):this._context.moveTo(t,r);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,r,t,r):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+r)/2,t,this._y0,t,r);break}}this._x0=t,this._y0=r}}function aA(e){return new S0(e,!0)}function oA(e){return new S0(e,!1)}const mp={draw(e,t){const r=ut(t/Yi);e.moveTo(r,0),e.arc(0,0,r,0,eo)}},uA={draw(e,t){const r=ut(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},P0=ut(1/3),cA=P0*2,sA={draw(e,t){const r=ut(t/cA),n=r*P0;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},lA={draw(e,t){const r=ut(t),n=-r/2;e.rect(n,n,r,r)}},fA=.8908130915292852,T0=Xi(Yi/10)/Xi(7*Yi/10),pA=Xi(eo/10)*T0,hA=-b0(eo/10)*T0,dA={draw(e,t){const r=ut(t*fA),n=pA*r,i=hA*r;e.moveTo(0,-r),e.lineTo(n,i);for(let a=1;a<5;++a){const o=eo*a/5,u=b0(o),c=Xi(o);e.lineTo(c*r,-u*r),e.lineTo(u*n-c*i,c*n+u*i)}e.closePath()}},Yu=ut(3),vA={draw(e,t){const r=-ut(t/(Yu*3));e.moveTo(0,r*2),e.lineTo(-Yu*r,-r),e.lineTo(Yu*r,-r),e.closePath()}},He=-.5,Ge=ut(3)/2,Wl=1/ut(12),yA=(Wl/2+1)*3,mA={draw(e,t){const r=ut(t/yA),n=r/2,i=r*Wl,a=n,o=r*Wl+r,u=-a,c=o;e.moveTo(n,i),e.lineTo(a,o),e.lineTo(u,c),e.lineTo(He*n-Ge*i,Ge*n+He*i),e.lineTo(He*a-Ge*o,Ge*a+He*o),e.lineTo(He*u-Ge*c,Ge*u+He*c),e.lineTo(He*n+Ge*i,He*i-Ge*n),e.lineTo(He*a+Ge*o,He*o-Ge*a),e.lineTo(He*u+Ge*c,He*c-Ge*u),e.closePath()}};function gA(e,t){let r=null,n=vp(i);e=typeof e=="function"?e:ce(e||mp),t=typeof t=="function"?t:ce(t===void 0?64:+t);function i(){let a;if(r||(r=a=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),a)return r=null,a+""||null}return i.type=function(a){return arguments.length?(e=typeof a=="function"?a:ce(a),i):e},i.size=function(a){return arguments.length?(t=typeof a=="function"?a:ce(+a),i):t},i.context=function(a){return arguments.length?(r=a==null?null:a,i):r},i}function Zi(){}function Ji(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function E0(e){this._context=e}E0.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:Ji(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:Ji(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function bA(e){return new E0(e)}function j0(e){this._context=e}j0.prototype={areaStart:Zi,areaEnd:Zi,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x2,this._y2),this._context.closePath();break}case 2:{this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break}case 3:{this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4);break}}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:Ji(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function xA(e){return new j0(e)}function $0(e){this._context=e}$0.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:Ji(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function OA(e){return new $0(e)}function M0(e){this._context=e}M0.prototype={areaStart:Zi,areaEnd:Zi,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function wA(e){return new M0(e)}function Md(e){return e<0?-1:1}function Id(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0),u=(a*i+o*n)/(n+i);return(Md(a)+Md(o))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs(u))||0}function Cd(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function Zu(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,u=(a-n)/3;e._context.bezierCurveTo(n+u,i+u*t,a-u,o-u*r,a,o)}function Qi(e){this._context=e}Qi.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:Zu(this,this._t0,Cd(this,this._t0));break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(e=+e,t=+t,!(e===this._x1&&t===this._y1)){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,Zu(this,Cd(this,r=Id(this,e,t)),r);break;default:Zu(this,this._t0,r=Id(this,e,t));break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}};function I0(e){this._context=new C0(e)}(I0.prototype=Object.create(Qi.prototype)).point=function(e,t){Qi.prototype.point.call(this,t,e)};function C0(e){this._context=e}C0.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}};function _A(e){return new Qi(e)}function AA(e){return new I0(e)}function k0(e){this._context=e}k0.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),r===2)this._context.lineTo(e[1],t[1]);else for(var n=kd(e),i=kd(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||this._line!==0&&r===1)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}};function kd(e){var t,r=e.length-1,n,i=new Array(r),a=new Array(r),o=new Array(r);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<r-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[r-1]=2,a[r-1]=7,o[r-1]=8*e[r-1]+e[r],t=1;t<r;++t)n=i[t]/a[t-1],a[t]-=n,o[t]-=n*o[t-1];for(i[r-1]=o[r-1]/a[r-1],t=r-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(a[r-1]=(e[r]+i[r-1])/2,t=0;t<r-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function SA(e){return new k0(e)}function ro(e,t){this._context=e,this._t=t}ro.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&this._point===2&&this._context.lineTo(this._x,this._y),(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:{if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}break}}this._x=e,this._y=t}};function PA(e){return new ro(e,.5)}function TA(e){return new ro(e,0)}function EA(e){return new ro(e,1)}function Cr(e,t){if((o=e.length)>1)for(var r=1,n,i,a=e[t[0]],o,u=a.length;r<o;++r)for(i=a,a=e[t[r]],n=0;n<u;++n)a[n][1]+=a[n][0]=isNaN(i[n][1])?i[n][0]:i[n][1]}function Ul(e){for(var t=e.length,r=new Array(t);--t>=0;)r[t]=t;return r}function jA(e,t){return e[t]}function $A(e){const t=[];return t.key=e,t}function MA(){var e=ce([]),t=Ul,r=Cr,n=jA;function i(a){var o=Array.from(e.apply(this,arguments),$A),u,c=o.length,s=-1,f;for(const l of a)for(u=0,++s;u<c;++u)(o[u][s]=[0,+n(l,o[u].key,s,a)]).data=l;for(u=0,f=yp(t(o));u<c;++u)o[f[u]].index=u;return r(o,f),o}return i.keys=function(a){return arguments.length?(e=typeof a=="function"?a:ce(Array.from(a)),i):e},i.value=function(a){return arguments.length?(n=typeof a=="function"?a:ce(+a),i):n},i.order=function(a){return arguments.length?(t=a==null?Ul:typeof a=="function"?a:ce(Array.from(a)),i):t},i.offset=function(a){return arguments.length?(r=a==null?Cr:a,i):r},i}function IA(e,t){if((n=e.length)>0){for(var r,n,i=0,a=e[0].length,o;i<a;++i){for(o=r=0;r<n;++r)o+=e[r][i][1]||0;if(o)for(r=0;r<n;++r)e[r][i][1]/=o}Cr(e,t)}}function CA(e,t){if((i=e.length)>0){for(var r=0,n=e[t[0]],i,a=n.length;r<a;++r){for(var o=0,u=0;o<i;++o)u+=e[o][r][1]||0;n[r][1]+=n[r][0]=-u/2}Cr(e,t)}}function kA(e,t){if(!(!((o=e.length)>0)||!((a=(i=e[t[0]]).length)>0))){for(var r=0,n=1,i,a,o;n<a;++n){for(var u=0,c=0,s=0;u<o;++u){for(var f=e[t[u]],l=f[n][1]||0,p=f[n-1][1]||0,h=(l-p)/2,v=0;v<u;++v){var d=e[t[v]],y=d[n][1]||0,b=d[n-1][1]||0;h+=y-b}c+=l,s+=h*l}i[n-1][1]+=i[n-1][0]=r,c&&(r-=s/c)}i[n-1][1]+=i[n-1][0]=r,Cr(e,t)}}function Ln(e){"@babel/helpers - typeof";return Ln=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ln(e)}var RA=["type","size","sizeType"];function Kl(){return Kl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Kl.apply(this,arguments)}function Rd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Dd(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Rd(Object(r),!0).forEach(function(n){DA(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Rd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function DA(e,t,r){return t=NA(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function NA(e){var t=qA(e,"string");return Ln(t)=="symbol"?t:t+""}function qA(e,t){if(Ln(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ln(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function LA(e,t){if(e==null)return{};var r=BA(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function BA(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var R0={symbolCircle:mp,symbolCross:uA,symbolDiamond:sA,symbolSquare:lA,symbolStar:dA,symbolTriangle:vA,symbolWye:mA},FA=Math.PI/180,zA=function(t){var r="symbol".concat(Qa(t));return R0[r]||mp},WA=function(t,r,n){if(r==="area")return t;switch(n){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":{var i=18*FA;return 1.25*t*t*(Math.tan(i)-Math.tan(i*2)*Math.pow(Math.tan(i),2))}case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},UA=function(t,r){R0["symbol".concat(Qa(t))]=r},no=function(t){var r=t.type,n=r===void 0?"circle":r,i=t.size,a=i===void 0?64:i,o=t.sizeType,u=o===void 0?"area":o,c=LA(t,RA),s=Dd(Dd({},c),{},{type:n,size:a,sizeType:u}),f=function(){var y=zA(n),b=gA().type(y).size(WA(a,u,n));return b()},l=s.className,p=s.cx,h=s.cy,v=U(s,!0);return p===+p&&h===+h&&a===+a?A.createElement("path",Kl({},v,{className:Z("recharts-symbols",l),transform:"translate(".concat(p,", ").concat(h,")"),d:f()})):null};no.registerSymbol=UA;function kr(e){"@babel/helpers - typeof";return kr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},kr(e)}function Hl(){return Hl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Hl.apply(this,arguments)}function Nd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function KA(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Nd(Object(r),!0).forEach(function(n){Bn(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Nd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function HA(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function GA(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,N0(n.key),n)}}function VA(e,t,r){return t&&GA(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function XA(e,t,r){return t=ea(t),YA(e,D0()?Reflect.construct(t,r||[],ea(e).constructor):t.apply(e,r))}function YA(e,t){if(t&&(kr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return ZA(e)}function ZA(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function D0(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(D0=function(){return!!e})()}function ea(e){return ea=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ea(e)}function JA(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Gl(e,t)}function Gl(e,t){return Gl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Gl(e,t)}function Bn(e,t,r){return t=N0(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function N0(e){var t=QA(e,"string");return kr(t)=="symbol"?t:t+""}function QA(e,t){if(kr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(kr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var et=32,gp=function(e){function t(){return HA(this,t),XA(this,t,arguments)}return JA(t,e),VA(t,[{key:"renderIcon",value:function(n){var i=this.props.inactiveColor,a=et/2,o=et/6,u=et/3,c=n.inactive?i:n.color;if(n.type==="plainline")return A.createElement("line",{strokeWidth:4,fill:"none",stroke:c,strokeDasharray:n.payload.strokeDasharray,x1:0,y1:a,x2:et,y2:a,className:"recharts-legend-icon"});if(n.type==="line")return A.createElement("path",{strokeWidth:4,fill:"none",stroke:c,d:"M0,".concat(a,"h").concat(u,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(2*u,",").concat(a,`
            H`).concat(et,"M").concat(2*u,",").concat(a,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(u,",").concat(a),className:"recharts-legend-icon"});if(n.type==="rect")return A.createElement("path",{stroke:"none",fill:c,d:"M0,".concat(et/8,"h").concat(et,"v").concat(et*3/4,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(A.isValidElement(n.legendIcon)){var s=KA({},n);return delete s.legendIcon,A.cloneElement(n.legendIcon,s)}return A.createElement(no,{fill:c,cx:a,cy:a,size:et,sizeType:"diameter",type:n.type})}},{key:"renderItems",value:function(){var n=this,i=this.props,a=i.payload,o=i.iconSize,u=i.layout,c=i.formatter,s=i.inactiveColor,f={x:0,y:0,width:et,height:et},l={display:u==="horizontal"?"inline-block":"block",marginRight:10},p={display:"inline-block",verticalAlign:"middle",marginRight:4};return a.map(function(h,v){var d=h.formatter||c,y=Z(Bn(Bn({"recharts-legend-item":!0},"legend-item-".concat(v),!0),"inactive",h.inactive));if(h.type==="none")return null;var b=H(h.value)?null:h.value;at(!H(h.value),`The name property is also required when using a function for the dataKey of a chart's cartesian components. Ex: <Bar name="Name of my Data"/>`);var g=h.inactive?s:h.color;return A.createElement("li",Hl({className:y,style:l,key:"legend-item-".concat(v)},Lt(n.props,h,v)),A.createElement(Ll,{width:o,height:o,viewBox:f,style:p},n.renderIcon(h)),A.createElement("span",{className:"recharts-legend-item-text",style:{color:g}},d?d(b,h,v):b))})}},{key:"render",value:function(){var n=this.props,i=n.payload,a=n.layout,o=n.align;if(!i||!i.length)return null;var u={padding:0,margin:0,textAlign:a==="horizontal"?o:"left"};return A.createElement("ul",{className:"recharts-default-legend",style:u},this.renderItems())}}])}(q.PureComponent);Bn(gp,"displayName","Legend");Bn(gp,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var Ju,qd;function e1(){if(qd)return Ju;qd=1;var e=Ya();function t(){this.__data__=new e,this.size=0}return Ju=t,Ju}var Qu,Ld;function t1(){if(Ld)return Qu;Ld=1;function e(t){var r=this.__data__,n=r.delete(t);return this.size=r.size,n}return Qu=e,Qu}var ec,Bd;function r1(){if(Bd)return ec;Bd=1;function e(t){return this.__data__.get(t)}return ec=e,ec}var tc,Fd;function n1(){if(Fd)return tc;Fd=1;function e(t){return this.__data__.has(t)}return tc=e,tc}var rc,zd;function i1(){if(zd)return rc;zd=1;var e=Ya(),t=sp(),r=lp(),n=200;function i(a,o){var u=this.__data__;if(u instanceof e){var c=u.__data__;if(!t||c.length<n-1)return c.push([a,o]),this.size=++u.size,this;u=this.__data__=new r(c)}return u.set(a,o),this.size=u.size,this}return rc=i,rc}var nc,Wd;function q0(){if(Wd)return nc;Wd=1;var e=Ya(),t=e1(),r=t1(),n=r1(),i=n1(),a=i1();function o(u){var c=this.__data__=new e(u);this.size=c.size}return o.prototype.clear=t,o.prototype.delete=r,o.prototype.get=n,o.prototype.has=i,o.prototype.set=a,nc=o,nc}var ic,Ud;function a1(){if(Ud)return ic;Ud=1;var e="__lodash_hash_undefined__";function t(r){return this.__data__.set(r,e),this}return ic=t,ic}var ac,Kd;function o1(){if(Kd)return ac;Kd=1;function e(t){return this.__data__.has(t)}return ac=e,ac}var oc,Hd;function L0(){if(Hd)return oc;Hd=1;var e=lp(),t=a1(),r=o1();function n(i){var a=-1,o=i==null?0:i.length;for(this.__data__=new e;++a<o;)this.add(i[a])}return n.prototype.add=n.prototype.push=t,n.prototype.has=r,oc=n,oc}var uc,Gd;function B0(){if(Gd)return uc;Gd=1;function e(t,r){for(var n=-1,i=t==null?0:t.length;++n<i;)if(r(t[n],n,t))return!0;return!1}return uc=e,uc}var cc,Vd;function F0(){if(Vd)return cc;Vd=1;function e(t,r){return t.has(r)}return cc=e,cc}var sc,Xd;function z0(){if(Xd)return sc;Xd=1;var e=L0(),t=B0(),r=F0(),n=1,i=2;function a(o,u,c,s,f,l){var p=c&n,h=o.length,v=u.length;if(h!=v&&!(p&&v>h))return!1;var d=l.get(o),y=l.get(u);if(d&&y)return d==u&&y==o;var b=-1,g=!0,x=c&i?new e:void 0;for(l.set(o,u),l.set(u,o);++b<h;){var w=o[b],m=u[b];if(s)var O=p?s(m,w,b,u,o,l):s(w,m,b,o,u,l);if(O!==void 0){if(O)continue;g=!1;break}if(x){if(!t(u,function(_,S){if(!r(x,S)&&(w===_||f(w,_,c,s,l)))return x.push(S)})){g=!1;break}}else if(!(w===m||f(w,m,c,s,l))){g=!1;break}}return l.delete(o),l.delete(u),g}return sc=a,sc}var lc,Yd;function u1(){if(Yd)return lc;Yd=1;var e=ht(),t=e.Uint8Array;return lc=t,lc}var fc,Zd;function c1(){if(Zd)return fc;Zd=1;function e(t){var r=-1,n=Array(t.size);return t.forEach(function(i,a){n[++r]=[a,i]}),n}return fc=e,fc}var pc,Jd;function bp(){if(Jd)return pc;Jd=1;function e(t){var r=-1,n=Array(t.size);return t.forEach(function(i){n[++r]=i}),n}return pc=e,pc}var hc,Qd;function s1(){if(Qd)return hc;Qd=1;var e=xi(),t=u1(),r=cp(),n=z0(),i=c1(),a=bp(),o=1,u=2,c="[object Boolean]",s="[object Date]",f="[object Error]",l="[object Map]",p="[object Number]",h="[object RegExp]",v="[object Set]",d="[object String]",y="[object Symbol]",b="[object ArrayBuffer]",g="[object DataView]",x=e?e.prototype:void 0,w=x?x.valueOf:void 0;function m(O,_,S,T,j,P,E){switch(S){case g:if(O.byteLength!=_.byteLength||O.byteOffset!=_.byteOffset)return!1;O=O.buffer,_=_.buffer;case b:return!(O.byteLength!=_.byteLength||!P(new t(O),new t(_)));case c:case s:case p:return r(+O,+_);case f:return O.name==_.name&&O.message==_.message;case h:case d:return O==_+"";case l:var $=i;case v:var I=T&o;if($||($=a),O.size!=_.size&&!I)return!1;var M=E.get(O);if(M)return M==_;T|=u,E.set(O,_);var k=n($(O),$(_),T,j,P,E);return E.delete(O),k;case y:if(w)return w.call(O)==w.call(_)}return!1}return hc=m,hc}var dc,ev;function W0(){if(ev)return dc;ev=1;function e(t,r){for(var n=-1,i=r.length,a=t.length;++n<i;)t[a+n]=r[n];return t}return dc=e,dc}var vc,tv;function l1(){if(tv)return vc;tv=1;var e=W0(),t=Le();function r(n,i,a){var o=i(n);return t(n)?o:e(o,a(n))}return vc=r,vc}var yc,rv;function f1(){if(rv)return yc;rv=1;function e(t,r){for(var n=-1,i=t==null?0:t.length,a=0,o=[];++n<i;){var u=t[n];r(u,n,t)&&(o[a++]=u)}return o}return yc=e,yc}var mc,nv;function p1(){if(nv)return mc;nv=1;function e(){return[]}return mc=e,mc}var gc,iv;function h1(){if(iv)return gc;iv=1;var e=f1(),t=p1(),r=Object.prototype,n=r.propertyIsEnumerable,i=Object.getOwnPropertySymbols,a=i?function(o){return o==null?[]:(o=Object(o),e(i(o),function(u){return n.call(o,u)}))}:t;return gc=a,gc}var bc,av;function d1(){if(av)return bc;av=1;function e(t,r){for(var n=-1,i=Array(t);++n<t;)i[n]=r(n);return i}return bc=e,bc}var xc,ov;function v1(){if(ov)return xc;ov=1;var e=Pt(),t=Tt(),r="[object Arguments]";function n(i){return t(i)&&e(i)==r}return xc=n,xc}var Oc,uv;function xp(){if(uv)return Oc;uv=1;var e=v1(),t=Tt(),r=Object.prototype,n=r.hasOwnProperty,i=r.propertyIsEnumerable,a=e(function(){return arguments}())?e:function(o){return t(o)&&n.call(o,"callee")&&!i.call(o,"callee")};return Oc=a,Oc}var Tn={exports:{}},wc,cv;function y1(){if(cv)return wc;cv=1;function e(){return!1}return wc=e,wc}Tn.exports;var sv;function U0(){return sv||(sv=1,function(e,t){var r=ht(),n=y1(),i=t&&!t.nodeType&&t,a=i&&!0&&e&&!e.nodeType&&e,o=a&&a.exports===i,u=o?r.Buffer:void 0,c=u?u.isBuffer:void 0,s=c||n;e.exports=s}(Tn,Tn.exports)),Tn.exports}var _c,lv;function Op(){if(lv)return _c;lv=1;var e=9007199254740991,t=/^(?:0|[1-9]\d*)$/;function r(n,i){var a=typeof n;return i=i==null?e:i,!!i&&(a=="number"||a!="symbol"&&t.test(n))&&n>-1&&n%1==0&&n<i}return _c=r,_c}var Ac,fv;function wp(){if(fv)return Ac;fv=1;var e=9007199254740991;function t(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=e}return Ac=t,Ac}var Sc,pv;function m1(){if(pv)return Sc;pv=1;var e=Pt(),t=wp(),r=Tt(),n="[object Arguments]",i="[object Array]",a="[object Boolean]",o="[object Date]",u="[object Error]",c="[object Function]",s="[object Map]",f="[object Number]",l="[object Object]",p="[object RegExp]",h="[object Set]",v="[object String]",d="[object WeakMap]",y="[object ArrayBuffer]",b="[object DataView]",g="[object Float32Array]",x="[object Float64Array]",w="[object Int8Array]",m="[object Int16Array]",O="[object Int32Array]",_="[object Uint8Array]",S="[object Uint8ClampedArray]",T="[object Uint16Array]",j="[object Uint32Array]",P={};P[g]=P[x]=P[w]=P[m]=P[O]=P[_]=P[S]=P[T]=P[j]=!0,P[n]=P[i]=P[y]=P[a]=P[b]=P[o]=P[u]=P[c]=P[s]=P[f]=P[l]=P[p]=P[h]=P[v]=P[d]=!1;function E($){return r($)&&t($.length)&&!!P[e($)]}return Sc=E,Sc}var Pc,hv;function K0(){if(hv)return Pc;hv=1;function e(t){return function(r){return t(r)}}return Pc=e,Pc}var En={exports:{}};En.exports;var dv;function g1(){return dv||(dv=1,function(e,t){var r=l0(),n=t&&!t.nodeType&&t,i=n&&!0&&e&&!e.nodeType&&e,a=i&&i.exports===n,o=a&&r.process,u=function(){try{var c=i&&i.require&&i.require("util").types;return c||o&&o.binding&&o.binding("util")}catch(s){}}();e.exports=u}(En,En.exports)),En.exports}var Tc,vv;function H0(){if(vv)return Tc;vv=1;var e=m1(),t=K0(),r=g1(),n=r&&r.isTypedArray,i=n?t(n):e;return Tc=i,Tc}var Ec,yv;function b1(){if(yv)return Ec;yv=1;var e=d1(),t=xp(),r=Le(),n=U0(),i=Op(),a=H0(),o=Object.prototype,u=o.hasOwnProperty;function c(s,f){var l=r(s),p=!l&&t(s),h=!l&&!p&&n(s),v=!l&&!p&&!h&&a(s),d=l||p||h||v,y=d?e(s.length,String):[],b=y.length;for(var g in s)(f||u.call(s,g))&&!(d&&(g=="length"||h&&(g=="offset"||g=="parent")||v&&(g=="buffer"||g=="byteLength"||g=="byteOffset")||i(g,b)))&&y.push(g);return y}return Ec=c,Ec}var jc,mv;function x1(){if(mv)return jc;mv=1;var e=Object.prototype;function t(r){var n=r&&r.constructor,i=typeof n=="function"&&n.prototype||e;return r===i}return jc=t,jc}var $c,gv;function G0(){if(gv)return $c;gv=1;function e(t,r){return function(n){return t(r(n))}}return $c=e,$c}var Mc,bv;function O1(){if(bv)return Mc;bv=1;var e=G0(),t=e(Object.keys,Object);return Mc=t,Mc}var Ic,xv;function w1(){if(xv)return Ic;xv=1;var e=x1(),t=O1(),r=Object.prototype,n=r.hasOwnProperty;function i(a){if(!e(a))return t(a);var o=[];for(var u in Object(a))n.call(a,u)&&u!="constructor"&&o.push(u);return o}return Ic=i,Ic}var Cc,Ov;function wi(){if(Ov)return Cc;Ov=1;var e=up(),t=wp();function r(n){return n!=null&&t(n.length)&&!e(n)}return Cc=r,Cc}var kc,wv;function io(){if(wv)return kc;wv=1;var e=b1(),t=w1(),r=wi();function n(i){return r(i)?e(i):t(i)}return kc=n,kc}var Rc,_v;function _1(){if(_v)return Rc;_v=1;var e=l1(),t=h1(),r=io();function n(i){return e(i,r,t)}return Rc=n,Rc}var Dc,Av;function A1(){if(Av)return Dc;Av=1;var e=_1(),t=1,r=Object.prototype,n=r.hasOwnProperty;function i(a,o,u,c,s,f){var l=u&t,p=e(a),h=p.length,v=e(o),d=v.length;if(h!=d&&!l)return!1;for(var y=h;y--;){var b=p[y];if(!(l?b in o:n.call(o,b)))return!1}var g=f.get(a),x=f.get(o);if(g&&x)return g==o&&x==a;var w=!0;f.set(a,o),f.set(o,a);for(var m=l;++y<h;){b=p[y];var O=a[b],_=o[b];if(c)var S=l?c(_,O,b,o,a,f):c(O,_,b,a,o,f);if(!(S===void 0?O===_||s(O,_,u,c,f):S)){w=!1;break}m||(m=b=="constructor")}if(w&&!m){var T=a.constructor,j=o.constructor;T!=j&&"constructor"in a&&"constructor"in o&&!(typeof T=="function"&&T instanceof T&&typeof j=="function"&&j instanceof j)&&(w=!1)}return f.delete(a),f.delete(o),w}return Dc=i,Dc}var Nc,Sv;function S1(){if(Sv)return Nc;Sv=1;var e=hr(),t=ht(),r=e(t,"DataView");return Nc=r,Nc}var qc,Pv;function P1(){if(Pv)return qc;Pv=1;var e=hr(),t=ht(),r=e(t,"Promise");return qc=r,qc}var Lc,Tv;function V0(){if(Tv)return Lc;Tv=1;var e=hr(),t=ht(),r=e(t,"Set");return Lc=r,Lc}var Bc,Ev;function T1(){if(Ev)return Bc;Ev=1;var e=hr(),t=ht(),r=e(t,"WeakMap");return Bc=r,Bc}var Fc,jv;function E1(){if(jv)return Fc;jv=1;var e=S1(),t=sp(),r=P1(),n=V0(),i=T1(),a=Pt(),o=f0(),u="[object Map]",c="[object Object]",s="[object Promise]",f="[object Set]",l="[object WeakMap]",p="[object DataView]",h=o(e),v=o(t),d=o(r),y=o(n),b=o(i),g=a;return(e&&g(new e(new ArrayBuffer(1)))!=p||t&&g(new t)!=u||r&&g(r.resolve())!=s||n&&g(new n)!=f||i&&g(new i)!=l)&&(g=function(x){var w=a(x),m=w==c?x.constructor:void 0,O=m?o(m):"";if(O)switch(O){case h:return p;case v:return u;case d:return s;case y:return f;case b:return l}return w}),Fc=g,Fc}var zc,$v;function j1(){if($v)return zc;$v=1;var e=q0(),t=z0(),r=s1(),n=A1(),i=E1(),a=Le(),o=U0(),u=H0(),c=1,s="[object Arguments]",f="[object Array]",l="[object Object]",p=Object.prototype,h=p.hasOwnProperty;function v(d,y,b,g,x,w){var m=a(d),O=a(y),_=m?f:i(d),S=O?f:i(y);_=_==s?l:_,S=S==s?l:S;var T=_==l,j=S==l,P=_==S;if(P&&o(d)){if(!o(y))return!1;m=!0,T=!1}if(P&&!T)return w||(w=new e),m||u(d)?t(d,y,b,g,x,w):r(d,y,_,b,g,x,w);if(!(b&c)){var E=T&&h.call(d,"__wrapped__"),$=j&&h.call(y,"__wrapped__");if(E||$){var I=E?d.value():d,M=$?y.value():y;return w||(w=new e),x(I,M,b,g,w)}}return P?(w||(w=new e),n(d,y,b,g,x,w)):!1}return zc=v,zc}var Wc,Mv;function _p(){if(Mv)return Wc;Mv=1;var e=j1(),t=Tt();function r(n,i,a,o,u){return n===i?!0:n==null||i==null||!t(n)&&!t(i)?n!==n&&i!==i:e(n,i,a,o,r,u)}return Wc=r,Wc}var Uc,Iv;function $1(){if(Iv)return Uc;Iv=1;var e=q0(),t=_p(),r=1,n=2;function i(a,o,u,c){var s=u.length,f=s,l=!c;if(a==null)return!f;for(a=Object(a);s--;){var p=u[s];if(l&&p[2]?p[1]!==a[p[0]]:!(p[0]in a))return!1}for(;++s<f;){p=u[s];var h=p[0],v=a[h],d=p[1];if(l&&p[2]){if(v===void 0&&!(h in a))return!1}else{var y=new e;if(c)var b=c(v,d,h,a,o,y);if(!(b===void 0?t(d,v,r|n,c,y):b))return!1}}return!0}return Uc=i,Uc}var Kc,Cv;function X0(){if(Cv)return Kc;Cv=1;var e=Ft();function t(r){return r===r&&!e(r)}return Kc=t,Kc}var Hc,kv;function M1(){if(kv)return Hc;kv=1;var e=X0(),t=io();function r(n){for(var i=t(n),a=i.length;a--;){var o=i[a],u=n[o];i[a]=[o,u,e(u)]}return i}return Hc=r,Hc}var Gc,Rv;function Y0(){if(Rv)return Gc;Rv=1;function e(t,r){return function(n){return n==null?!1:n[t]===r&&(r!==void 0||t in Object(n))}}return Gc=e,Gc}var Vc,Dv;function I1(){if(Dv)return Vc;Dv=1;var e=$1(),t=M1(),r=Y0();function n(i){var a=t(i);return a.length==1&&a[0][2]?r(a[0][0],a[0][1]):function(o){return o===i||e(o,i,a)}}return Vc=n,Vc}var Xc,Nv;function C1(){if(Nv)return Xc;Nv=1;function e(t,r){return t!=null&&r in Object(t)}return Xc=e,Xc}var Yc,qv;function k1(){if(qv)return Yc;qv=1;var e=d0(),t=xp(),r=Le(),n=Op(),i=wp(),a=Ja();function o(u,c,s){c=e(c,u);for(var f=-1,l=c.length,p=!1;++f<l;){var h=a(c[f]);if(!(p=u!=null&&s(u,h)))break;u=u[h]}return p||++f!=l?p:(l=u==null?0:u.length,!!l&&i(l)&&n(h,l)&&(r(u)||t(u)))}return Yc=o,Yc}var Zc,Lv;function R1(){if(Lv)return Zc;Lv=1;var e=C1(),t=k1();function r(n,i){return n!=null&&t(n,i,e)}return Zc=r,Zc}var Jc,Bv;function D1(){if(Bv)return Jc;Bv=1;var e=_p(),t=v0(),r=R1(),n=op(),i=X0(),a=Y0(),o=Ja(),u=1,c=2;function s(f,l){return n(f)&&i(l)?a(o(f),l):function(p){var h=t(p,f);return h===void 0&&h===l?r(p,f):e(l,h,u|c)}}return Jc=s,Jc}var Qc,Fv;function pn(){if(Fv)return Qc;Fv=1;function e(t){return t}return Qc=e,Qc}var es,zv;function N1(){if(zv)return es;zv=1;function e(t){return function(r){return r==null?void 0:r[t]}}return es=e,es}var ts,Wv;function q1(){if(Wv)return ts;Wv=1;var e=pp();function t(r){return function(n){return e(n,r)}}return ts=t,ts}var rs,Uv;function L1(){if(Uv)return rs;Uv=1;var e=N1(),t=q1(),r=op(),n=Ja();function i(a){return r(a)?e(n(a)):t(a)}return rs=i,rs}var ns,Kv;function dt(){if(Kv)return ns;Kv=1;var e=I1(),t=D1(),r=pn(),n=Le(),i=L1();function a(o){return typeof o=="function"?o:o==null?r:typeof o=="object"?n(o)?t(o[0],o[1]):e(o):i(o)}return ns=a,ns}var is,Hv;function Z0(){if(Hv)return is;Hv=1;function e(t,r,n,i){for(var a=t.length,o=n+(i?1:-1);i?o--:++o<a;)if(r(t[o],o,t))return o;return-1}return is=e,is}var as,Gv;function B1(){if(Gv)return as;Gv=1;function e(t){return t!==t}return as=e,as}var os,Vv;function F1(){if(Vv)return os;Vv=1;function e(t,r,n){for(var i=n-1,a=t.length;++i<a;)if(t[i]===r)return i;return-1}return os=e,os}var us,Xv;function z1(){if(Xv)return us;Xv=1;var e=Z0(),t=B1(),r=F1();function n(i,a,o){return a===a?r(i,a,o):e(i,t,o)}return us=n,us}var cs,Yv;function W1(){if(Yv)return cs;Yv=1;var e=z1();function t(r,n){var i=r==null?0:r.length;return!!i&&e(r,n,0)>-1}return cs=t,cs}var ss,Zv;function U1(){if(Zv)return ss;Zv=1;function e(t,r,n){for(var i=-1,a=t==null?0:t.length;++i<a;)if(n(r,t[i]))return!0;return!1}return ss=e,ss}var ls,Jv;function K1(){if(Jv)return ls;Jv=1;function e(){}return ls=e,ls}var fs,Qv;function H1(){if(Qv)return fs;Qv=1;var e=V0(),t=K1(),r=bp(),n=1/0,i=e&&1/r(new e([,-0]))[1]==n?function(a){return new e(a)}:t;return fs=i,fs}var ps,ey;function G1(){if(ey)return ps;ey=1;var e=L0(),t=W1(),r=U1(),n=F0(),i=H1(),a=bp(),o=200;function u(c,s,f){var l=-1,p=t,h=c.length,v=!0,d=[],y=d;if(f)v=!1,p=r;else if(h>=o){var b=s?null:i(c);if(b)return a(b);v=!1,p=n,y=new e}else y=s?[]:d;e:for(;++l<h;){var g=c[l],x=s?s(g):g;if(g=f||g!==0?g:0,v&&x===x){for(var w=y.length;w--;)if(y[w]===x)continue e;s&&y.push(x),d.push(g)}else p(y,x,f)||(y!==d&&y.push(x),d.push(g))}return d}return ps=u,ps}var hs,ty;function V1(){if(ty)return hs;ty=1;var e=dt(),t=G1();function r(n,i){return n&&n.length?t(n,e(i,2)):[]}return hs=r,hs}var X1=V1();const ry=ae(X1);function J0(e,t,r){return t===!0?ry(e,r):H(t)?ry(e,t):e}function Rr(e){"@babel/helpers - typeof";return Rr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Rr(e)}var Y1=["ref"];function ny(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function vt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ny(Object(r),!0).forEach(function(n){ao(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ny(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Z1(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function iy(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ex(n.key),n)}}function J1(e,t,r){return t&&iy(e.prototype,t),r&&iy(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Q1(e,t,r){return t=ta(t),eS(e,Q0()?Reflect.construct(t,r||[],ta(e).constructor):t.apply(e,r))}function eS(e,t){if(t&&(Rr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return tS(e)}function tS(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Q0(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(Q0=function(){return!!e})()}function ta(e){return ta=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ta(e)}function rS(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Vl(e,t)}function Vl(e,t){return Vl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Vl(e,t)}function ao(e,t,r){return t=ex(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ex(e){var t=nS(e,"string");return Rr(t)=="symbol"?t:t+""}function nS(e,t){if(Rr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Rr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function iS(e,t){if(e==null)return{};var r=aS(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function aS(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function oS(e){return e.value}function uS(e,t){if(A.isValidElement(e))return A.cloneElement(e,t);if(typeof e=="function")return A.createElement(e,t);t.ref;var r=iS(t,Y1);return A.createElement(gp,r)}var ay=1,$r=function(e){function t(){var r;Z1(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=Q1(this,t,[].concat(i)),ao(r,"lastBoundingBox",{width:-1,height:-1}),r}return rS(t,e),J1(t,[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();return n.height=this.wrapperNode.offsetHeight,n.width=this.wrapperNode.offsetWidth,n}return null}},{key:"updateBBox",value:function(){var n=this.props.onBBoxUpdate,i=this.getBBox();i?(Math.abs(i.width-this.lastBoundingBox.width)>ay||Math.abs(i.height-this.lastBoundingBox.height)>ay)&&(this.lastBoundingBox.width=i.width,this.lastBoundingBox.height=i.height,n&&n(i)):(this.lastBoundingBox.width!==-1||this.lastBoundingBox.height!==-1)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,n&&n(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?vt({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(n){var i=this.props,a=i.layout,o=i.align,u=i.verticalAlign,c=i.margin,s=i.chartWidth,f=i.chartHeight,l,p;if(!n||(n.left===void 0||n.left===null)&&(n.right===void 0||n.right===null))if(o==="center"&&a==="vertical"){var h=this.getBBoxSnapshot();l={left:((s||0)-h.width)/2}}else l=o==="right"?{right:c&&c.right||0}:{left:c&&c.left||0};if(!n||(n.top===void 0||n.top===null)&&(n.bottom===void 0||n.bottom===null))if(u==="middle"){var v=this.getBBoxSnapshot();p={top:((f||0)-v.height)/2}}else p=u==="bottom"?{bottom:c&&c.bottom||0}:{top:c&&c.top||0};return vt(vt({},l),p)}},{key:"render",value:function(){var n=this,i=this.props,a=i.content,o=i.width,u=i.height,c=i.wrapperStyle,s=i.payloadUniqBy,f=i.payload,l=vt(vt({position:"absolute",width:o||"auto",height:u||"auto"},this.getDefaultPosition(c)),c);return A.createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(h){n.wrapperNode=h}},uS(a,vt(vt({},this.props),{},{payload:J0(f,s,oS)})))}}],[{key:"getWithHeight",value:function(n,i){var a=vt(vt({},this.defaultProps),n.props),o=a.layout;return o==="vertical"&&N(n.props.height)?{height:n.props.height}:o==="horizontal"?{width:n.props.width||i}:null}}])}(q.PureComponent);ao($r,"displayName","Legend");ao($r,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var ds,oy;function cS(){if(oy)return ds;oy=1;var e=xi(),t=xp(),r=Le(),n=e?e.isConcatSpreadable:void 0;function i(a){return r(a)||t(a)||!!(n&&a&&a[n])}return ds=i,ds}var vs,uy;function tx(){if(uy)return vs;uy=1;var e=W0(),t=cS();function r(n,i,a,o,u){var c=-1,s=n.length;for(a||(a=t),u||(u=[]);++c<s;){var f=n[c];i>0&&a(f)?i>1?r(f,i-1,a,o,u):e(u,f):o||(u[u.length]=f)}return u}return vs=r,vs}var ys,cy;function sS(){if(cy)return ys;cy=1;function e(t){return function(r,n,i){for(var a=-1,o=Object(r),u=i(r),c=u.length;c--;){var s=u[t?c:++a];if(n(o[s],s,o)===!1)break}return r}}return ys=e,ys}var ms,sy;function lS(){if(sy)return ms;sy=1;var e=sS(),t=e();return ms=t,ms}var gs,ly;function rx(){if(ly)return gs;ly=1;var e=lS(),t=io();function r(n,i){return n&&e(n,i,t)}return gs=r,gs}var bs,fy;function fS(){if(fy)return bs;fy=1;var e=wi();function t(r,n){return function(i,a){if(i==null)return i;if(!e(i))return r(i,a);for(var o=i.length,u=n?o:-1,c=Object(i);(n?u--:++u<o)&&a(c[u],u,c)!==!1;);return i}}return bs=t,bs}var xs,py;function Ap(){if(py)return xs;py=1;var e=rx(),t=fS(),r=t(e);return xs=r,xs}var Os,hy;function nx(){if(hy)return Os;hy=1;var e=Ap(),t=wi();function r(n,i){var a=-1,o=t(n)?Array(n.length):[];return e(n,function(u,c,s){o[++a]=i(u,c,s)}),o}return Os=r,Os}var ws,dy;function pS(){if(dy)return ws;dy=1;function e(t,r){var n=t.length;for(t.sort(r);n--;)t[n]=t[n].value;return t}return ws=e,ws}var _s,vy;function hS(){if(vy)return _s;vy=1;var e=sn();function t(r,n){if(r!==n){var i=r!==void 0,a=r===null,o=r===r,u=e(r),c=n!==void 0,s=n===null,f=n===n,l=e(n);if(!s&&!l&&!u&&r>n||u&&c&&f&&!s&&!l||a&&c&&f||!i&&f||!o)return 1;if(!a&&!u&&!l&&r<n||l&&i&&o&&!a&&!u||s&&i&&o||!c&&o||!f)return-1}return 0}return _s=t,_s}var As,yy;function dS(){if(yy)return As;yy=1;var e=hS();function t(r,n,i){for(var a=-1,o=r.criteria,u=n.criteria,c=o.length,s=i.length;++a<c;){var f=e(o[a],u[a]);if(f){if(a>=s)return f;var l=i[a];return f*(l=="desc"?-1:1)}}return r.index-n.index}return As=t,As}var Ss,my;function vS(){if(my)return Ss;my=1;var e=fp(),t=pp(),r=dt(),n=nx(),i=pS(),a=K0(),o=dS(),u=pn(),c=Le();function s(f,l,p){l.length?l=e(l,function(d){return c(d)?function(y){return t(y,d.length===1?d[0]:d)}:d}):l=[u];var h=-1;l=e(l,a(r));var v=n(f,function(d,y,b){var g=e(l,function(x){return x(d)});return{criteria:g,index:++h,value:d}});return i(v,function(d,y){return o(d,y,p)})}return Ss=s,Ss}var Ps,gy;function yS(){if(gy)return Ps;gy=1;function e(t,r,n){switch(n.length){case 0:return t.call(r);case 1:return t.call(r,n[0]);case 2:return t.call(r,n[0],n[1]);case 3:return t.call(r,n[0],n[1],n[2])}return t.apply(r,n)}return Ps=e,Ps}var Ts,by;function mS(){if(by)return Ts;by=1;var e=yS(),t=Math.max;function r(n,i,a){return i=t(i===void 0?n.length-1:i,0),function(){for(var o=arguments,u=-1,c=t(o.length-i,0),s=Array(c);++u<c;)s[u]=o[i+u];u=-1;for(var f=Array(i+1);++u<i;)f[u]=o[u];return f[i]=a(s),e(n,this,f)}}return Ts=r,Ts}var Es,xy;function gS(){if(xy)return Es;xy=1;function e(t){return function(){return t}}return Es=e,Es}var js,Oy;function ix(){if(Oy)return js;Oy=1;var e=hr(),t=function(){try{var r=e(Object,"defineProperty");return r({},"",{}),r}catch(n){}}();return js=t,js}var $s,wy;function bS(){if(wy)return $s;wy=1;var e=gS(),t=ix(),r=pn(),n=t?function(i,a){return t(i,"toString",{configurable:!0,enumerable:!1,value:e(a),writable:!0})}:r;return $s=n,$s}var Ms,_y;function xS(){if(_y)return Ms;_y=1;var e=800,t=16,r=Date.now;function n(i){var a=0,o=0;return function(){var u=r(),c=t-(u-o);if(o=u,c>0){if(++a>=e)return arguments[0]}else a=0;return i.apply(void 0,arguments)}}return Ms=n,Ms}var Is,Ay;function OS(){if(Ay)return Is;Ay=1;var e=bS(),t=xS(),r=t(e);return Is=r,Is}var Cs,Sy;function wS(){if(Sy)return Cs;Sy=1;var e=pn(),t=mS(),r=OS();function n(i,a){return r(t(i,a,e),i+"")}return Cs=n,Cs}var ks,Py;function oo(){if(Py)return ks;Py=1;var e=cp(),t=wi(),r=Op(),n=Ft();function i(a,o,u){if(!n(u))return!1;var c=typeof o;return(c=="number"?t(u)&&r(o,u.length):c=="string"&&o in u)?e(u[o],a):!1}return ks=i,ks}var Rs,Ty;function _S(){if(Ty)return Rs;Ty=1;var e=tx(),t=vS(),r=wS(),n=oo(),i=r(function(a,o){if(a==null)return[];var u=o.length;return u>1&&n(a,o[0],o[1])?o=[]:u>2&&n(o[0],o[1],o[2])&&(o=[o[0]]),t(a,e(o,1),[])});return Rs=i,Rs}var AS=_S();const Sp=ae(AS);function Fn(e){"@babel/helpers - typeof";return Fn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fn(e)}function Xl(){return Xl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Xl.apply(this,arguments)}function SS(e,t){return jS(e)||ES(e,t)||TS(e,t)||PS()}function PS(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function TS(e,t){if(e){if(typeof e=="string")return Ey(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ey(e,t)}}function Ey(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function ES(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function jS(e){if(Array.isArray(e))return e}function jy(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ds(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?jy(Object(r),!0).forEach(function(n){$S(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):jy(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function $S(e,t,r){return t=MS(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function MS(e){var t=IS(e,"string");return Fn(t)=="symbol"?t:t+""}function IS(e,t){if(Fn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Fn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function CS(e){return Array.isArray(e)&&_e(e[0])&&_e(e[1])?e.join(" ~ "):e}var kS=function(t){var r=t.separator,n=r===void 0?" : ":r,i=t.contentStyle,a=i===void 0?{}:i,o=t.itemStyle,u=o===void 0?{}:o,c=t.labelStyle,s=c===void 0?{}:c,f=t.payload,l=t.formatter,p=t.itemSorter,h=t.wrapperClassName,v=t.labelClassName,d=t.label,y=t.labelFormatter,b=t.accessibilityLayer,g=b===void 0?!1:b,x=function(){if(f&&f.length){var E={padding:0,margin:0},$=(p?Sp(f,p):f).map(function(I,M){if(I.type==="none")return null;var k=Ds({display:"block",paddingTop:4,paddingBottom:4,color:I.color||"#000"},u),R=I.formatter||l||CS,L=I.value,B=I.name,K=L,X=B;if(R&&K!=null&&X!=null){var z=R(L,B,I,M,f);if(Array.isArray(z)){var Y=SS(z,2);K=Y[0],X=Y[1]}else K=z}return A.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(M),style:k},_e(X)?A.createElement("span",{className:"recharts-tooltip-item-name"},X):null,_e(X)?A.createElement("span",{className:"recharts-tooltip-item-separator"},n):null,A.createElement("span",{className:"recharts-tooltip-item-value"},K),A.createElement("span",{className:"recharts-tooltip-item-unit"},I.unit||""))});return A.createElement("ul",{className:"recharts-tooltip-item-list",style:E},$)}return null},w=Ds({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},a),m=Ds({margin:0},s),O=!G(d),_=O?d:"",S=Z("recharts-default-tooltip",h),T=Z("recharts-tooltip-label",v);O&&y&&f!==void 0&&f!==null&&(_=y(d,f));var j=g?{role:"status","aria-live":"assertive"}:{};return A.createElement("div",Xl({className:S,style:w},j),A.createElement("p",{className:T,style:m},A.isValidElement(_)?_:"".concat(_)),x())};function zn(e){"@babel/helpers - typeof";return zn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},zn(e)}function ki(e,t,r){return t=RS(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function RS(e){var t=DS(e,"string");return zn(t)=="symbol"?t:t+""}function DS(e,t){if(zn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(zn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var gn="recharts-tooltip-wrapper",NS={visibility:"hidden"};function qS(e){var t=e.coordinate,r=e.translateX,n=e.translateY;return Z(gn,ki(ki(ki(ki({},"".concat(gn,"-right"),N(r)&&t&&N(t.x)&&r>=t.x),"".concat(gn,"-left"),N(r)&&t&&N(t.x)&&r<t.x),"".concat(gn,"-bottom"),N(n)&&t&&N(t.y)&&n>=t.y),"".concat(gn,"-top"),N(n)&&t&&N(t.y)&&n<t.y))}function $y(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.key,i=e.offsetTopLeft,a=e.position,o=e.reverseDirection,u=e.tooltipDimension,c=e.viewBox,s=e.viewBoxDimension;if(a&&N(a[n]))return a[n];var f=r[n]-u-i,l=r[n]+i;if(t[n])return o[n]?f:l;if(o[n]){var p=f,h=c[n];return p<h?Math.max(l,c[n]):Math.max(f,c[n])}var v=l+u,d=c[n]+s;return v>d?Math.max(f,c[n]):Math.max(l,c[n])}function LS(e){var t=e.translateX,r=e.translateY,n=e.useTranslate3d;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}function BS(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.offsetTopLeft,i=e.position,a=e.reverseDirection,o=e.tooltipBox,u=e.useTranslate3d,c=e.viewBox,s,f,l;return o.height>0&&o.width>0&&r?(f=$y({allowEscapeViewBox:t,coordinate:r,key:"x",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.width,viewBox:c,viewBoxDimension:c.width}),l=$y({allowEscapeViewBox:t,coordinate:r,key:"y",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.height,viewBox:c,viewBoxDimension:c.height}),s=LS({translateX:f,translateY:l,useTranslate3d:u})):s=NS,{cssProperties:s,cssClasses:qS({translateX:f,translateY:l,coordinate:r})}}function Dr(e){"@babel/helpers - typeof";return Dr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Dr(e)}function My(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Iy(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?My(Object(r),!0).forEach(function(n){Zl(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):My(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function FS(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function zS(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ox(n.key),n)}}function WS(e,t,r){return t&&zS(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function US(e,t,r){return t=ra(t),KS(e,ax()?Reflect.construct(t,r||[],ra(e).constructor):t.apply(e,r))}function KS(e,t){if(t&&(Dr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return HS(e)}function HS(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ax(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ax=function(){return!!e})()}function ra(e){return ra=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ra(e)}function GS(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Yl(e,t)}function Yl(e,t){return Yl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Yl(e,t)}function Zl(e,t,r){return t=ox(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ox(e){var t=VS(e,"string");return Dr(t)=="symbol"?t:t+""}function VS(e,t){if(Dr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Dr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Cy=1,XS=function(e){function t(){var r;FS(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=US(this,t,[].concat(i)),Zl(r,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),Zl(r,"handleKeyDown",function(o){if(o.key==="Escape"){var u,c,s,f;r.setState({dismissed:!0,dismissedAtCoordinate:{x:(u=(c=r.props.coordinate)===null||c===void 0?void 0:c.x)!==null&&u!==void 0?u:0,y:(s=(f=r.props.coordinate)===null||f===void 0?void 0:f.y)!==null&&s!==void 0?s:0}})}}),r}return GS(t,e),WS(t,[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();(Math.abs(n.width-this.state.lastBoundingBox.width)>Cy||Math.abs(n.height-this.state.lastBoundingBox.height)>Cy)&&this.setState({lastBoundingBox:{width:n.width,height:n.height}})}else(this.state.lastBoundingBox.width!==-1||this.state.lastBoundingBox.height!==-1)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var n,i;this.props.active&&this.updateBBox(),this.state.dismissed&&(((n=this.props.coordinate)===null||n===void 0?void 0:n.x)!==this.state.dismissedAtCoordinate.x||((i=this.props.coordinate)===null||i===void 0?void 0:i.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,c=i.animationEasing,s=i.children,f=i.coordinate,l=i.hasPayload,p=i.isAnimationActive,h=i.offset,v=i.position,d=i.reverseDirection,y=i.useTranslate3d,b=i.viewBox,g=i.wrapperStyle,x=BS({allowEscapeViewBox:o,coordinate:f,offsetTopLeft:h,position:v,reverseDirection:d,tooltipBox:this.state.lastBoundingBox,useTranslate3d:y,viewBox:b}),w=x.cssClasses,m=x.cssProperties,O=Iy(Iy({transition:p&&a?"transform ".concat(u,"ms ").concat(c):void 0},m),{},{pointerEvents:"none",visibility:!this.state.dismissed&&a&&l?"visible":"hidden",position:"absolute",top:0,left:0},g);return A.createElement("div",{tabIndex:-1,className:w,style:O,ref:function(S){n.wrapperNode=S}},s)}}])}(q.PureComponent),YS=function(){return!(typeof window!="undefined"&&window.document&&window.document.createElement&&window.setTimeout)},Et={isSsr:YS()};function Nr(e){"@babel/helpers - typeof";return Nr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Nr(e)}function ky(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ry(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ky(Object(r),!0).forEach(function(n){Pp(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ky(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ZS(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function JS(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,cx(n.key),n)}}function QS(e,t,r){return t&&JS(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function eP(e,t,r){return t=na(t),tP(e,ux()?Reflect.construct(t,r||[],na(e).constructor):t.apply(e,r))}function tP(e,t){if(t&&(Nr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return rP(e)}function rP(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ux(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ux=function(){return!!e})()}function na(e){return na=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},na(e)}function nP(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Jl(e,t)}function Jl(e,t){return Jl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Jl(e,t)}function Pp(e,t,r){return t=cx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function cx(e){var t=iP(e,"string");return Nr(t)=="symbol"?t:t+""}function iP(e,t){if(Nr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Nr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function aP(e){return e.dataKey}function oP(e,t){return A.isValidElement(e)?A.cloneElement(e,t):typeof e=="function"?A.createElement(e,t):A.createElement(kS,t)}var yt=function(e){function t(){return ZS(this,t),eP(this,t,arguments)}return nP(t,e),QS(t,[{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,c=i.animationEasing,s=i.content,f=i.coordinate,l=i.filterNull,p=i.isAnimationActive,h=i.offset,v=i.payload,d=i.payloadUniqBy,y=i.position,b=i.reverseDirection,g=i.useTranslate3d,x=i.viewBox,w=i.wrapperStyle,m=v!=null?v:[];l&&m.length&&(m=J0(v.filter(function(_){return _.value!=null&&(_.hide!==!0||n.props.includeHidden)}),d,aP));var O=m.length>0;return A.createElement(XS,{allowEscapeViewBox:o,animationDuration:u,animationEasing:c,isAnimationActive:p,active:a,coordinate:f,hasPayload:O,offset:h,position:y,reverseDirection:b,useTranslate3d:g,viewBox:x,wrapperStyle:w},oP(s,Ry(Ry({},this.props),{},{payload:m})))}}])}(q.PureComponent);Pp(yt,"displayName","Tooltip");Pp(yt,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!Et.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var Ns,Dy;function uP(){if(Dy)return Ns;Dy=1;var e=ht(),t=function(){return e.Date.now()};return Ns=t,Ns}var qs,Ny;function cP(){if(Ny)return qs;Ny=1;var e=/\s/;function t(r){for(var n=r.length;n--&&e.test(r.charAt(n)););return n}return qs=t,qs}var Ls,qy;function sP(){if(qy)return Ls;qy=1;var e=cP(),t=/^\s+/;function r(n){return n&&n.slice(0,e(n)+1).replace(t,"")}return Ls=r,Ls}var Bs,Ly;function sx(){if(Ly)return Bs;Ly=1;var e=sP(),t=Ft(),r=sn(),n=NaN,i=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,o=/^0o[0-7]+$/i,u=parseInt;function c(s){if(typeof s=="number")return s;if(r(s))return n;if(t(s)){var f=typeof s.valueOf=="function"?s.valueOf():s;s=t(f)?f+"":f}if(typeof s!="string")return s===0?s:+s;s=e(s);var l=a.test(s);return l||o.test(s)?u(s.slice(2),l?2:8):i.test(s)?n:+s}return Bs=c,Bs}var Fs,By;function lP(){if(By)return Fs;By=1;var e=Ft(),t=uP(),r=sx(),n="Expected a function",i=Math.max,a=Math.min;function o(u,c,s){var f,l,p,h,v,d,y=0,b=!1,g=!1,x=!0;if(typeof u!="function")throw new TypeError(n);c=r(c)||0,e(s)&&(b=!!s.leading,g="maxWait"in s,p=g?i(r(s.maxWait)||0,c):p,x="trailing"in s?!!s.trailing:x);function w($){var I=f,M=l;return f=l=void 0,y=$,h=u.apply(M,I),h}function m($){return y=$,v=setTimeout(S,c),b?w($):h}function O($){var I=$-d,M=$-y,k=c-I;return g?a(k,p-M):k}function _($){var I=$-d,M=$-y;return d===void 0||I>=c||I<0||g&&M>=p}function S(){var $=t();if(_($))return T($);v=setTimeout(S,O($))}function T($){return v=void 0,x&&f?w($):(f=l=void 0,h)}function j(){v!==void 0&&clearTimeout(v),y=0,f=d=l=v=void 0}function P(){return v===void 0?h:T(t())}function E(){var $=t(),I=_($);if(f=arguments,l=this,d=$,I){if(v===void 0)return m(d);if(g)return clearTimeout(v),v=setTimeout(S,c),w(d)}return v===void 0&&(v=setTimeout(S,c)),h}return E.cancel=j,E.flush=P,E}return Fs=o,Fs}var zs,Fy;function fP(){if(Fy)return zs;Fy=1;var e=lP(),t=Ft(),r="Expected a function";function n(i,a,o){var u=!0,c=!0;if(typeof i!="function")throw new TypeError(r);return t(o)&&(u="leading"in o?!!o.leading:u,c="trailing"in o?!!o.trailing:c),e(i,a,{leading:u,maxWait:a,trailing:c})}return zs=n,zs}var pP=fP();const lx=ae(pP);function Wn(e){"@babel/helpers - typeof";return Wn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Wn(e)}function zy(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ri(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?zy(Object(r),!0).forEach(function(n){hP(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):zy(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function hP(e,t,r){return t=dP(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dP(e){var t=vP(e,"string");return Wn(t)=="symbol"?t:t+""}function vP(e,t){if(Wn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Wn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function yP(e,t){return xP(e)||bP(e,t)||gP(e,t)||mP()}function mP(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function gP(e,t){if(e){if(typeof e=="string")return Wy(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Wy(e,t)}}function Wy(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function bP(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function xP(e){if(Array.isArray(e))return e}var Hq=q.forwardRef(function(e,t){var r=e.aspect,n=e.initialDimension,i=n===void 0?{width:-1,height:-1}:n,a=e.width,o=a===void 0?"100%":a,u=e.height,c=u===void 0?"100%":u,s=e.minWidth,f=s===void 0?0:s,l=e.minHeight,p=e.maxHeight,h=e.children,v=e.debounce,d=v===void 0?0:v,y=e.id,b=e.className,g=e.onResize,x=e.style,w=x===void 0?{}:x,m=q.useRef(null),O=q.useRef();O.current=g,q.useImperativeHandle(t,function(){return Object.defineProperty(m.current,"current",{get:function(){return m.current},configurable:!0})});var _=q.useState({containerWidth:i.width,containerHeight:i.height}),S=yP(_,2),T=S[0],j=S[1],P=q.useCallback(function($,I){j(function(M){var k=Math.round($),R=Math.round(I);return M.containerWidth===k&&M.containerHeight===R?M:{containerWidth:k,containerHeight:R}})},[]);q.useEffect(function(){var $=function(B){var K,X=B[0].contentRect,z=X.width,Y=X.height;P(z,Y),(K=O.current)===null||K===void 0||K.call(O,z,Y)};d>0&&($=lx($,d,{trailing:!0,leading:!1}));var I=new ResizeObserver($),M=m.current.getBoundingClientRect(),k=M.width,R=M.height;return P(k,R),I.observe(m.current),function(){I.disconnect()}},[P,d]);var E=q.useMemo(function(){var $=T.containerWidth,I=T.containerHeight;if($<0||I<0)return null;at(rr(o)||rr(c),`The width(%s) and height(%s) are both fixed numbers,
       maybe you don't need to use a ResponsiveContainer.`,o,c),at(!r||r>0,"The aspect(%s) must be greater than zero.",r);var M=rr(o)?$:o,k=rr(c)?I:c;r&&r>0&&(M?k=M/r:k&&(M=k*r),p&&k>p&&(k=p)),at(M>0||k>0,`The width(%s) and height(%s) of chart should be greater than 0,
       please check the style of container, or the props width(%s) and height(%s),
       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the
       height and width.`,M,k,o,c,f,l,r);var R=!Array.isArray(h)&&Ot(h.type).endsWith("Chart");return A.Children.map(h,function(L){return A.isValidElement(L)?q.cloneElement(L,Ri({width:M,height:k},R?{style:Ri({height:"100%",width:"100%",maxHeight:k,maxWidth:M},L.props.style)}:{})):L})},[r,h,c,p,l,f,T,o]);return A.createElement("div",{id:y?"".concat(y):void 0,className:Z("recharts-responsive-container",b),style:Ri(Ri({},w),{},{width:o,height:c,minWidth:f,minHeight:l,maxHeight:p}),ref:m},E)}),uo=function(t){return null};uo.displayName="Cell";function Un(e){"@babel/helpers - typeof";return Un=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Un(e)}function Uy(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ql(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Uy(Object(r),!0).forEach(function(n){OP(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Uy(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function OP(e,t,r){return t=wP(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function wP(e){var t=_P(e,"string");return Un(t)=="symbol"?t:t+""}function _P(e,t){if(Un(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Un(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var xr={widthCache:{},cacheCount:0},AP=2e3,SP={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},Ky="recharts_measurement_span";function PP(e){var t=Ql({},e);return Object.keys(t).forEach(function(r){t[r]||delete t[r]}),t}var $n=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(t==null||Et.isSsr)return{width:0,height:0};var n=PP(r),i=JSON.stringify({text:t,copyStyle:n});if(xr.widthCache[i])return xr.widthCache[i];try{var a=document.getElementById(Ky);a||(a=document.createElement("span"),a.setAttribute("id",Ky),a.setAttribute("aria-hidden","true"),document.body.appendChild(a));var o=Ql(Ql({},SP),n);Object.assign(a.style,o),a.textContent="".concat(t);var u=a.getBoundingClientRect(),c={width:u.width,height:u.height};return xr.widthCache[i]=c,++xr.cacheCount>AP&&(xr.cacheCount=0,xr.widthCache={}),c}catch(s){return{width:0,height:0}}},TP=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}};function Kn(e){"@babel/helpers - typeof";return Kn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Kn(e)}function ia(e,t){return MP(e)||$P(e,t)||jP(e,t)||EP()}function EP(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function jP(e,t){if(e){if(typeof e=="string")return Hy(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Hy(e,t)}}function Hy(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function $P(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function MP(e){if(Array.isArray(e))return e}function IP(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Gy(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,kP(n.key),n)}}function CP(e,t,r){return t&&Gy(e.prototype,t),r&&Gy(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function kP(e){var t=RP(e,"string");return Kn(t)=="symbol"?t:t+""}function RP(e,t){if(Kn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Kn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Vy=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,Xy=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,DP=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,NP=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,fx={cm:96/2.54,mm:96/25.4,pt:96/72,pc:96/6,in:96,Q:96/(2.54*40),px:1},qP=Object.keys(fx),Ar="NaN";function LP(e,t){return e*fx[t]}var Di=function(){function e(t,r){IP(this,e),this.num=t,this.unit=r,this.num=t,this.unit=r,Number.isNaN(t)&&(this.unit=""),r!==""&&!DP.test(r)&&(this.num=NaN,this.unit=""),qP.includes(r)&&(this.num=LP(t,r),this.unit="px")}return CP(e,[{key:"add",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num+r.num,this.unit)}},{key:"subtract",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num-r.num,this.unit)}},{key:"multiply",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num*r.num,this.unit||r.unit)}},{key:"divide",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num/r.num,this.unit||r.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],[{key:"parse",value:function(r){var n,i=(n=NP.exec(r))!==null&&n!==void 0?n:[],a=ia(i,3),o=a[1],u=a[2];return new e(parseFloat(o),u!=null?u:"")}}])}();function px(e){if(e.includes(Ar))return Ar;for(var t=e;t.includes("*")||t.includes("/");){var r,n=(r=Vy.exec(t))!==null&&r!==void 0?r:[],i=ia(n,4),a=i[1],o=i[2],u=i[3],c=Di.parse(a!=null?a:""),s=Di.parse(u!=null?u:""),f=o==="*"?c.multiply(s):c.divide(s);if(f.isNaN())return Ar;t=t.replace(Vy,f.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var l,p=(l=Xy.exec(t))!==null&&l!==void 0?l:[],h=ia(p,4),v=h[1],d=h[2],y=h[3],b=Di.parse(v!=null?v:""),g=Di.parse(y!=null?y:""),x=d==="+"?b.add(g):b.subtract(g);if(x.isNaN())return Ar;t=t.replace(Xy,x.toString())}return t}var Yy=/\(([^()]*)\)/;function BP(e){for(var t=e;t.includes("(");){var r=Yy.exec(t),n=ia(r,2),i=n[1];t=t.replace(Yy,px(i))}return t}function FP(e){var t=e.replace(/\s+/g,"");return t=BP(t),t=px(t),t}function zP(e){try{return FP(e)}catch(t){return Ar}}function Ws(e){var t=zP(e.slice(5,-1));return t===Ar?"":t}var WP=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],UP=["dx","dy","angle","className","breakAll"];function ef(){return ef=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ef.apply(this,arguments)}function Zy(e,t){if(e==null)return{};var r=KP(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function KP(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Jy(e,t){return XP(e)||VP(e,t)||GP(e,t)||HP()}function HP(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function GP(e,t){if(e){if(typeof e=="string")return Qy(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Qy(e,t)}}function Qy(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function VP(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function XP(e){if(Array.isArray(e))return e}var hx=/[ \f\n\r\t\v\u2028\u2029]+/,dx=function(t){var r=t.children,n=t.breakAll,i=t.style;try{var a=[];G(r)||(n?a=r.toString().split(""):a=r.toString().split(hx));var o=a.map(function(c){return{word:c,width:$n(c,i).width}}),u=n?0:$n(" ",i).width;return{wordsWithComputedWidth:o,spaceWidth:u}}catch(c){return null}},YP=function(t,r,n,i,a){var o=t.maxLines,u=t.children,c=t.style,s=t.breakAll,f=N(o),l=u,p=function(){var M=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return M.reduce(function(k,R){var L=R.word,B=R.width,K=k[k.length-1];if(K&&(i==null||a||K.width+B+n<Number(i)))K.words.push(L),K.width+=B+n;else{var X={words:[L],width:B};k.push(X)}return k},[])},h=p(r),v=function(M){return M.reduce(function(k,R){return k.width>R.width?k:R})};if(!f)return h;for(var d="…",y=function(M){var k=l.slice(0,M),R=dx({breakAll:s,style:c,children:k+d}).wordsWithComputedWidth,L=p(R),B=L.length>o||v(L).width>Number(i);return[B,L]},b=0,g=l.length-1,x=0,w;b<=g&&x<=l.length-1;){var m=Math.floor((b+g)/2),O=m-1,_=y(O),S=Jy(_,2),T=S[0],j=S[1],P=y(m),E=Jy(P,1),$=E[0];if(!T&&!$&&(b=m+1),T&&$&&(g=m-1),!T&&$){w=j;break}x++}return w||h},em=function(t){var r=G(t)?[]:t.toString().split(hx);return[{words:r}]},ZP=function(t){var r=t.width,n=t.scaleToFit,i=t.children,a=t.style,o=t.breakAll,u=t.maxLines;if((r||n)&&!Et.isSsr){var c,s,f=dx({breakAll:o,children:i,style:a});if(f){var l=f.wordsWithComputedWidth,p=f.spaceWidth;c=l,s=p}else return em(i);return YP({breakAll:o,children:i,maxLines:u,style:a},c,s,r,n)}return em(i)},tm="#808080",lr=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.lineHeight,u=o===void 0?"1em":o,c=t.capHeight,s=c===void 0?"0.71em":c,f=t.scaleToFit,l=f===void 0?!1:f,p=t.textAnchor,h=p===void 0?"start":p,v=t.verticalAnchor,d=v===void 0?"end":v,y=t.fill,b=y===void 0?tm:y,g=Zy(t,WP),x=q.useMemo(function(){return ZP({breakAll:g.breakAll,children:g.children,maxLines:g.maxLines,scaleToFit:l,style:g.style,width:g.width})},[g.breakAll,g.children,g.maxLines,l,g.style,g.width]),w=g.dx,m=g.dy,O=g.angle,_=g.className,S=g.breakAll,T=Zy(g,UP);if(!_e(n)||!_e(a))return null;var j=n+(N(w)?w:0),P=a+(N(m)?m:0),E;switch(d){case"start":E=Ws("calc(".concat(s,")"));break;case"middle":E=Ws("calc(".concat((x.length-1)/2," * -").concat(u," + (").concat(s," / 2))"));break;default:E=Ws("calc(".concat(x.length-1," * -").concat(u,")"));break}var $=[];if(l){var I=x[0].width,M=g.width;$.push("scale(".concat((N(M)?M/I:1)/I,")"))}return O&&$.push("rotate(".concat(O,", ").concat(j,", ").concat(P,")")),$.length&&(T.transform=$.join(" ")),A.createElement("text",ef({},U(T,!0),{x:j,y:P,className:Z("recharts-text",_),textAnchor:h,fill:b.includes("url")?tm:b}),x.map(function(k,R){var L=k.words.join(S?"":" ");return A.createElement("tspan",{x:j,dy:R===0?E:u,key:"".concat(L,"-").concat(R)},L)}))};function Nt(e,t){return e==null||t==null?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function JP(e,t){return e==null||t==null?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function Tp(e){let t,r,n;e.length!==2?(t=Nt,r=(u,c)=>Nt(e(u),c),n=(u,c)=>e(u)-c):(t=e===Nt||e===JP?e:QP,r=e,n=e);function i(u,c,s=0,f=u.length){if(s<f){if(t(c,c)!==0)return f;do{const l=s+f>>>1;r(u[l],c)<0?s=l+1:f=l}while(s<f)}return s}function a(u,c,s=0,f=u.length){if(s<f){if(t(c,c)!==0)return f;do{const l=s+f>>>1;r(u[l],c)<=0?s=l+1:f=l}while(s<f)}return s}function o(u,c,s=0,f=u.length){const l=i(u,c,s,f-1);return l>s&&n(u[l-1],c)>-n(u[l],c)?l-1:l}return{left:i,center:o,right:a}}function QP(){return 0}function vx(e){return e===null?NaN:+e}function*eT(e,t){for(let r of e)r!=null&&(r=+r)>=r&&(yield r)}const tT=Tp(Nt),_i=tT.right;Tp(vx).center;class rm extends Map{constructor(t,r=iT){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:r}}),t!=null)for(const[n,i]of t)this.set(n,i)}get(t){return super.get(nm(this,t))}has(t){return super.has(nm(this,t))}set(t,r){return super.set(rT(this,t),r)}delete(t){return super.delete(nT(this,t))}}function nm({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):r}function rT({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}function nT({_intern:e,_key:t},r){const n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}function iT(e){return e!==null&&typeof e=="object"?e.valueOf():e}function aT(e=Nt){if(e===Nt)return yx;if(typeof e!="function")throw new TypeError("compare is not a function");return(t,r)=>{const n=e(t,r);return n||n===0?n:(e(r,r)===0)-(e(t,t)===0)}}function yx(e,t){return(e==null||!(e>=e))-(t==null||!(t>=t))||(e<t?-1:e>t?1:0)}const oT=Math.sqrt(50),uT=Math.sqrt(10),cT=Math.sqrt(2);function aa(e,t,r){const n=(t-e)/Math.max(0,r),i=Math.floor(Math.log10(n)),a=n/Math.pow(10,i),o=a>=oT?10:a>=uT?5:a>=cT?2:1;let u,c,s;return i<0?(s=Math.pow(10,-i)/o,u=Math.round(e*s),c=Math.round(t*s),u/s<e&&++u,c/s>t&&--c,s=-s):(s=Math.pow(10,i)*o,u=Math.round(e/s),c=Math.round(t/s),u*s<e&&++u,c*s>t&&--c),c<u&&.5<=r&&r<2?aa(e,t,r*2):[u,c,s]}function tf(e,t,r){if(t=+t,e=+e,r=+r,!(r>0))return[];if(e===t)return[e];const n=t<e,[i,a,o]=n?aa(t,e,r):aa(e,t,r);if(!(a>=i))return[];const u=a-i+1,c=new Array(u);if(n)if(o<0)for(let s=0;s<u;++s)c[s]=(a-s)/-o;else for(let s=0;s<u;++s)c[s]=(a-s)*o;else if(o<0)for(let s=0;s<u;++s)c[s]=(i+s)/-o;else for(let s=0;s<u;++s)c[s]=(i+s)*o;return c}function rf(e,t,r){return t=+t,e=+e,r=+r,aa(e,t,r)[2]}function nf(e,t,r){t=+t,e=+e,r=+r;const n=t<e,i=n?rf(t,e,r):rf(e,t,r);return(n?-1:1)*(i<0?1/-i:i)}function im(e,t){let r;for(const n of e)n!=null&&(r<n||r===void 0&&n>=n)&&(r=n);return r}function am(e,t){let r;for(const n of e)n!=null&&(r>n||r===void 0&&n>=n)&&(r=n);return r}function mx(e,t,r=0,n=1/0,i){if(t=Math.floor(t),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(e.length-1,n)),!(r<=t&&t<=n))return e;for(i=i===void 0?yx:aT(i);n>r;){if(n-r>600){const c=n-r+1,s=t-r+1,f=Math.log(c),l=.5*Math.exp(2*f/3),p=.5*Math.sqrt(f*l*(c-l)/c)*(s-c/2<0?-1:1),h=Math.max(r,Math.floor(t-s*l/c+p)),v=Math.min(n,Math.floor(t+(c-s)*l/c+p));mx(e,t,h,v,i)}const a=e[t];let o=r,u=n;for(bn(e,r,t),i(e[n],a)>0&&bn(e,r,n);o<u;){for(bn(e,o,u),++o,--u;i(e[o],a)<0;)++o;for(;i(e[u],a)>0;)--u}i(e[r],a)===0?bn(e,r,u):(++u,bn(e,u,n)),u<=t&&(r=u+1),t<=u&&(n=u-1)}return e}function bn(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}function sT(e,t,r){if(e=Float64Array.from(eT(e)),!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return am(e);if(t>=1)return im(e);var n,i=(n-1)*t,a=Math.floor(i),o=im(mx(e,a).subarray(0,a+1)),u=am(e.subarray(a+1));return o+(u-o)*(i-a)}}function lT(e,t,r=vx){if(!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e),u=+r(e[a+1],a+1,e);return o+(u-o)*(i-a)}}function fT(e,t,r){e=+e,t=+t,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=Math.max(0,Math.ceil((t-e)/r))|0,a=new Array(i);++n<i;)a[n]=e+n*r;return a}function Qe(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e);break}return this}function jt(e,t){switch(arguments.length){case 0:break;case 1:{typeof e=="function"?this.interpolator(e):this.range(e);break}default:{this.domain(e),typeof t=="function"?this.interpolator(t):this.range(t);break}}return this}const af=Symbol("implicit");function Ep(){var e=new rm,t=[],r=[],n=af;function i(a){let o=e.get(a);if(o===void 0){if(n!==af)return n;e.set(a,o=t.push(a)-1)}return r[o%r.length]}return i.domain=function(a){if(!arguments.length)return t.slice();t=[],e=new rm;for(const o of a)e.has(o)||e.set(o,t.push(o)-1);return i},i.range=function(a){return arguments.length?(r=Array.from(a),i):r.slice()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return Ep(t,r).unknown(n)},Qe.apply(i,arguments),i}function Hn(){var e=Ep().unknown(void 0),t=e.domain,r=e.range,n=0,i=1,a,o,u=!1,c=0,s=0,f=.5;delete e.unknown;function l(){var p=t().length,h=i<n,v=h?i:n,d=h?n:i;a=(d-v)/Math.max(1,p-c+s*2),u&&(a=Math.floor(a)),v+=(d-v-a*(p-c))*f,o=a*(1-c),u&&(v=Math.round(v),o=Math.round(o));var y=fT(p).map(function(b){return v+a*b});return r(h?y.reverse():y)}return e.domain=function(p){return arguments.length?(t(p),l()):t()},e.range=function(p){return arguments.length?([n,i]=p,n=+n,i=+i,l()):[n,i]},e.rangeRound=function(p){return[n,i]=p,n=+n,i=+i,u=!0,l()},e.bandwidth=function(){return o},e.step=function(){return a},e.round=function(p){return arguments.length?(u=!!p,l()):u},e.padding=function(p){return arguments.length?(c=Math.min(1,s=+p),l()):c},e.paddingInner=function(p){return arguments.length?(c=Math.min(1,p),l()):c},e.paddingOuter=function(p){return arguments.length?(s=+p,l()):s},e.align=function(p){return arguments.length?(f=Math.max(0,Math.min(1,p)),l()):f},e.copy=function(){return Hn(t(),[n,i]).round(u).paddingInner(c).paddingOuter(s).align(f)},Qe.apply(l(),arguments)}function gx(e){var t=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return gx(t())},e}function Mn(){return gx(Hn.apply(null,arguments).paddingInner(1))}function jp(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function bx(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function Ai(){}var Gn=.7,oa=1/Gn,Mr="\\s*([+-]?\\d+)\\s*",Vn="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",lt="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",pT=/^#([0-9a-f]{3,8})$/,hT=new RegExp(`^rgb\\(${Mr},${Mr},${Mr}\\)$`),dT=new RegExp(`^rgb\\(${lt},${lt},${lt}\\)$`),vT=new RegExp(`^rgba\\(${Mr},${Mr},${Mr},${Vn}\\)$`),yT=new RegExp(`^rgba\\(${lt},${lt},${lt},${Vn}\\)$`),mT=new RegExp(`^hsl\\(${Vn},${lt},${lt}\\)$`),gT=new RegExp(`^hsla\\(${Vn},${lt},${lt},${Vn}\\)$`),om={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};jp(Ai,Xn,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:um,formatHex:um,formatHex8:bT,formatHsl:xT,formatRgb:cm,toString:cm});function um(){return this.rgb().formatHex()}function bT(){return this.rgb().formatHex8()}function xT(){return xx(this).formatHsl()}function cm(){return this.rgb().formatRgb()}function Xn(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=pT.exec(e))?(r=t[1].length,t=parseInt(t[1],16),r===6?sm(t):r===3?new qe(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):r===8?Ni(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):r===4?Ni(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=hT.exec(e))?new qe(t[1],t[2],t[3],1):(t=dT.exec(e))?new qe(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=vT.exec(e))?Ni(t[1],t[2],t[3],t[4]):(t=yT.exec(e))?Ni(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=mT.exec(e))?pm(t[1],t[2]/100,t[3]/100,1):(t=gT.exec(e))?pm(t[1],t[2]/100,t[3]/100,t[4]):om.hasOwnProperty(e)?sm(om[e]):e==="transparent"?new qe(NaN,NaN,NaN,0):null}function sm(e){return new qe(e>>16&255,e>>8&255,e&255,1)}function Ni(e,t,r,n){return n<=0&&(e=t=r=NaN),new qe(e,t,r,n)}function OT(e){return e instanceof Ai||(e=Xn(e)),e?(e=e.rgb(),new qe(e.r,e.g,e.b,e.opacity)):new qe}function of(e,t,r,n){return arguments.length===1?OT(e):new qe(e,t,r,n==null?1:n)}function qe(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}jp(qe,of,bx(Ai,{brighter(e){return e=e==null?oa:Math.pow(oa,e),new qe(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?Gn:Math.pow(Gn,e),new qe(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new qe(ur(this.r),ur(this.g),ur(this.b),ua(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:lm,formatHex:lm,formatHex8:wT,formatRgb:fm,toString:fm}));function lm(){return`#${nr(this.r)}${nr(this.g)}${nr(this.b)}`}function wT(){return`#${nr(this.r)}${nr(this.g)}${nr(this.b)}${nr((isNaN(this.opacity)?1:this.opacity)*255)}`}function fm(){const e=ua(this.opacity);return`${e===1?"rgb(":"rgba("}${ur(this.r)}, ${ur(this.g)}, ${ur(this.b)}${e===1?")":`, ${e})`}`}function ua(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function ur(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function nr(e){return e=ur(e),(e<16?"0":"")+e.toString(16)}function pm(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new it(e,t,r,n)}function xx(e){if(e instanceof it)return new it(e.h,e.s,e.l,e.opacity);if(e instanceof Ai||(e=Xn(e)),!e)return new it;if(e instanceof it)return e;e=e.rgb();var t=e.r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,u=a-i,c=(a+i)/2;return u?(t===a?o=(r-n)/u+(r<n)*6:r===a?o=(n-t)/u+2:o=(t-r)/u+4,u/=c<.5?a+i:2-a-i,o*=60):u=c>0&&c<1?0:o,new it(o,u,c,e.opacity)}function _T(e,t,r,n){return arguments.length===1?xx(e):new it(e,t,r,n==null?1:n)}function it(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}jp(it,_T,bx(Ai,{brighter(e){return e=e==null?oa:Math.pow(oa,e),new it(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?Gn:Math.pow(Gn,e),new it(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new qe(Us(e>=240?e-240:e+120,i,n),Us(e,i,n),Us(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new it(hm(this.h),qi(this.s),qi(this.l),ua(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=ua(this.opacity);return`${e===1?"hsl(":"hsla("}${hm(this.h)}, ${qi(this.s)*100}%, ${qi(this.l)*100}%${e===1?")":`, ${e})`}`}}));function hm(e){return e=(e||0)%360,e<0?e+360:e}function qi(e){return Math.max(0,Math.min(1,e||0))}function Us(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}const $p=e=>()=>e;function AT(e,t){return function(r){return e+r*t}}function ST(e,t,r){return e=Math.pow(e,r),t=Math.pow(t,r)-e,r=1/r,function(n){return Math.pow(e+n*t,r)}}function PT(e){return(e=+e)==1?Ox:function(t,r){return r-t?ST(t,r,e):$p(isNaN(t)?r:t)}}function Ox(e,t){var r=t-e;return r?AT(e,r):$p(isNaN(e)?t:e)}const dm=function e(t){var r=PT(t);function n(i,a){var o=r((i=of(i)).r,(a=of(a)).r),u=r(i.g,a.g),c=r(i.b,a.b),s=Ox(i.opacity,a.opacity);return function(f){return i.r=o(f),i.g=u(f),i.b=c(f),i.opacity=s(f),i+""}}return n.gamma=e,n}(1);function TT(e,t){t||(t=[]);var r=e?Math.min(t.length,e.length):0,n=t.slice(),i;return function(a){for(i=0;i<r;++i)n[i]=e[i]*(1-a)+t[i]*a;return n}}function ET(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}function jT(e,t){var r=t?t.length:0,n=e?Math.min(r,e.length):0,i=new Array(n),a=new Array(r),o;for(o=0;o<n;++o)i[o]=hn(e[o],t[o]);for(;o<r;++o)a[o]=t[o];return function(u){for(o=0;o<n;++o)a[o]=i[o](u);return a}}function $T(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}function ca(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}function MT(e,t){var r={},n={},i;(e===null||typeof e!="object")&&(e={}),(t===null||typeof t!="object")&&(t={});for(i in t)i in e?r[i]=hn(e[i],t[i]):n[i]=t[i];return function(a){for(i in r)n[i]=r[i](a);return n}}var uf=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Ks=new RegExp(uf.source,"g");function IT(e){return function(){return e}}function CT(e){return function(t){return e(t)+""}}function kT(e,t){var r=uf.lastIndex=Ks.lastIndex=0,n,i,a,o=-1,u=[],c=[];for(e=e+"",t=t+"";(n=uf.exec(e))&&(i=Ks.exec(t));)(a=i.index)>r&&(a=t.slice(r,a),u[o]?u[o]+=a:u[++o]=a),(n=n[0])===(i=i[0])?u[o]?u[o]+=i:u[++o]=i:(u[++o]=null,c.push({i:o,x:ca(n,i)})),r=Ks.lastIndex;return r<t.length&&(a=t.slice(r),u[o]?u[o]+=a:u[++o]=a),u.length<2?c[0]?CT(c[0].x):IT(t):(t=c.length,function(s){for(var f=0,l;f<t;++f)u[(l=c[f]).i]=l.x(s);return u.join("")})}function hn(e,t){var r=typeof t,n;return t==null||r==="boolean"?$p(t):(r==="number"?ca:r==="string"?(n=Xn(t))?(t=n,dm):kT:t instanceof Xn?dm:t instanceof Date?$T:ET(t)?TT:Array.isArray(t)?jT:typeof t.valueOf!="function"&&typeof t.toString!="function"||isNaN(t)?MT:ca)(e,t)}function Mp(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function RT(e,t){t===void 0&&(t=e,e=hn);for(var r=0,n=t.length-1,i=t[0],a=new Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(o){var u=Math.max(0,Math.min(n-1,Math.floor(o*=n)));return a[u](o-u)}}function DT(e){return function(){return e}}function sa(e){return+e}var vm=[0,1];function ke(e){return e}function cf(e,t){return(t-=e=+e)?function(r){return(r-e)/t}:DT(isNaN(t)?NaN:.5)}function NT(e,t){var r;return e>t&&(r=e,e=t,t=r),function(n){return Math.max(e,Math.min(t,n))}}function qT(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=cf(i,n),a=r(o,a)):(n=cf(n,i),a=r(a,o)),function(u){return a(n(u))}}function LT(e,t,r){var n=Math.min(e.length,t.length)-1,i=new Array(n),a=new Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=cf(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(u){var c=_i(e,u,1,n)-1;return a[c](i[c](u))}}function Si(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function co(){var e=vm,t=vm,r=hn,n,i,a,o=ke,u,c,s;function f(){var p=Math.min(e.length,t.length);return o!==ke&&(o=NT(e[0],e[p-1])),u=p>2?LT:qT,c=s=null,l}function l(p){return p==null||isNaN(p=+p)?a:(c||(c=u(e.map(n),t,r)))(n(o(p)))}return l.invert=function(p){return o(i((s||(s=u(t,e.map(n),ca)))(p)))},l.domain=function(p){return arguments.length?(e=Array.from(p,sa),f()):e.slice()},l.range=function(p){return arguments.length?(t=Array.from(p),f()):t.slice()},l.rangeRound=function(p){return t=Array.from(p),r=Mp,f()},l.clamp=function(p){return arguments.length?(o=p?!0:ke,f()):o!==ke},l.interpolate=function(p){return arguments.length?(r=p,f()):r},l.unknown=function(p){return arguments.length?(a=p,l):a},function(p,h){return n=p,i=h,f()}}function Ip(){return co()(ke,ke)}function BT(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)}function la(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function qr(e){return e=la(Math.abs(e)),e?e[1]:NaN}function FT(e,t){return function(r,n){for(var i=r.length,a=[],o=0,u=e[0],c=0;i>0&&u>0&&(c+u+1>n&&(u=Math.max(1,n-c)),a.push(r.substring(i-=u,i+u)),!((c+=u+1)>n));)u=e[o=(o+1)%e.length];return a.reverse().join(t)}}function zT(e){return function(t){return t.replace(/[0-9]/g,function(r){return e[+r]})}}var WT=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function Yn(e){if(!(t=WT.exec(e)))throw new Error("invalid format: "+e);var t;return new Cp({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}Yn.prototype=Cp.prototype;function Cp(e){this.fill=e.fill===void 0?" ":e.fill+"",this.align=e.align===void 0?">":e.align+"",this.sign=e.sign===void 0?"-":e.sign+"",this.symbol=e.symbol===void 0?"":e.symbol+"",this.zero=!!e.zero,this.width=e.width===void 0?void 0:+e.width,this.comma=!!e.comma,this.precision=e.precision===void 0?void 0:+e.precision,this.trim=!!e.trim,this.type=e.type===void 0?"":e.type+""}Cp.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function UT(e){e:for(var t=e.length,r=1,n=-1,i;r<t;++r)switch(e[r]){case".":n=i=r;break;case"0":n===0&&(n=r),i=r;break;default:if(!+e[r])break e;n>0&&(n=0);break}return n>0?e.slice(0,n)+e.slice(i+1):e}var wx;function KT(e,t){var r=la(e,t);if(!r)return e+"";var n=r[0],i=r[1],a=i-(wx=Math.max(-8,Math.min(8,Math.floor(i/3)))*3)+1,o=n.length;return a===o?n:a>o?n+new Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+new Array(1-a).join("0")+la(e,Math.max(0,t+a-1))[0]}function ym(e,t){var r=la(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+new Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+new Array(i-n.length+2).join("0")}const mm={"%":(e,t)=>(e*100).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:BT,e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>ym(e*100,t),r:ym,s:KT,X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function gm(e){return e}var bm=Array.prototype.map,xm=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function HT(e){var t=e.grouping===void 0||e.thousands===void 0?gm:FT(bm.call(e.grouping,Number),e.thousands+""),r=e.currency===void 0?"":e.currency[0]+"",n=e.currency===void 0?"":e.currency[1]+"",i=e.decimal===void 0?".":e.decimal+"",a=e.numerals===void 0?gm:zT(bm.call(e.numerals,String)),o=e.percent===void 0?"%":e.percent+"",u=e.minus===void 0?"−":e.minus+"",c=e.nan===void 0?"NaN":e.nan+"";function s(l){l=Yn(l);var p=l.fill,h=l.align,v=l.sign,d=l.symbol,y=l.zero,b=l.width,g=l.comma,x=l.precision,w=l.trim,m=l.type;m==="n"?(g=!0,m="g"):mm[m]||(x===void 0&&(x=12),w=!0,m="g"),(y||p==="0"&&h==="=")&&(y=!0,p="0",h="=");var O=d==="$"?r:d==="#"&&/[boxX]/.test(m)?"0"+m.toLowerCase():"",_=d==="$"?n:/[%p]/.test(m)?o:"",S=mm[m],T=/[defgprs%]/.test(m);x=x===void 0?6:/[gprs]/.test(m)?Math.max(1,Math.min(21,x)):Math.max(0,Math.min(20,x));function j(P){var E=O,$=_,I,M,k;if(m==="c")$=S(P)+$,P="";else{P=+P;var R=P<0||1/P<0;if(P=isNaN(P)?c:S(Math.abs(P),x),w&&(P=UT(P)),R&&+P==0&&v!=="+"&&(R=!1),E=(R?v==="("?v:u:v==="-"||v==="("?"":v)+E,$=(m==="s"?xm[8+wx/3]:"")+$+(R&&v==="("?")":""),T){for(I=-1,M=P.length;++I<M;)if(k=P.charCodeAt(I),48>k||k>57){$=(k===46?i+P.slice(I+1):P.slice(I))+$,P=P.slice(0,I);break}}}g&&!y&&(P=t(P,1/0));var L=E.length+P.length+$.length,B=L<b?new Array(b-L+1).join(p):"";switch(g&&y&&(P=t(B+P,B.length?b-$.length:1/0),B=""),h){case"<":P=E+P+$+B;break;case"=":P=E+B+P+$;break;case"^":P=B.slice(0,L=B.length>>1)+E+P+$+B.slice(L);break;default:P=B+E+P+$;break}return a(P)}return j.toString=function(){return l+""},j}function f(l,p){var h=s((l=Yn(l),l.type="f",l)),v=Math.max(-8,Math.min(8,Math.floor(qr(p)/3)))*3,d=Math.pow(10,-v),y=xm[8+v/3];return function(b){return h(d*b)+y}}return{format:s,formatPrefix:f}}var Li,kp,_x;GT({thousands:",",grouping:[3],currency:["$",""]});function GT(e){return Li=HT(e),kp=Li.format,_x=Li.formatPrefix,Li}function VT(e){return Math.max(0,-qr(Math.abs(e)))}function XT(e,t){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(qr(t)/3)))*3-qr(Math.abs(e)))}function YT(e,t){return e=Math.abs(e),t=Math.abs(t)-e,Math.max(0,qr(t)-qr(e))+1}function Ax(e,t,r,n){var i=nf(e,t,r),a;switch(n=Yn(n==null?",f":n),n.type){case"s":{var o=Math.max(Math.abs(e),Math.abs(t));return n.precision==null&&!isNaN(a=XT(i,o))&&(n.precision=a),_x(n,o)}case"":case"e":case"g":case"p":case"r":{n.precision==null&&!isNaN(a=YT(i,Math.max(Math.abs(e),Math.abs(t))))&&(n.precision=a-(n.type==="e"));break}case"f":case"%":{n.precision==null&&!isNaN(a=VT(i))&&(n.precision=a-(n.type==="%")*2);break}}return kp(n)}function Wt(e){var t=e.domain;return e.ticks=function(r){var n=t();return tf(n[0],n[n.length-1],r==null?10:r)},e.tickFormat=function(r,n){var i=t();return Ax(i[0],i[i.length-1],r==null?10:r,n)},e.nice=function(r){r==null&&(r=10);var n=t(),i=0,a=n.length-1,o=n[i],u=n[a],c,s,f=10;for(u<o&&(s=o,o=u,u=s,s=i,i=a,a=s);f-- >0;){if(s=rf(o,u,r),s===c)return n[i]=o,n[a]=u,t(n);if(s>0)o=Math.floor(o/s)*s,u=Math.ceil(u/s)*s;else if(s<0)o=Math.ceil(o*s)/s,u=Math.floor(u*s)/s;else break;c=s}return e},e}function fa(){var e=Ip();return e.copy=function(){return Si(e,fa())},Qe.apply(e,arguments),Wt(e)}function Sx(e){var t;function r(n){return n==null||isNaN(n=+n)?t:n}return r.invert=r,r.domain=r.range=function(n){return arguments.length?(e=Array.from(n,sa),r):e.slice()},r.unknown=function(n){return arguments.length?(t=n,r):t},r.copy=function(){return Sx(e).unknown(t)},e=arguments.length?Array.from(e,sa):[0,1],Wt(r)}function Px(e,t){e=e.slice();var r=0,n=e.length-1,i=e[r],a=e[n],o;return a<i&&(o=r,r=n,n=o,o=i,i=a,a=o),e[r]=t.floor(i),e[n]=t.ceil(a),e}function Om(e){return Math.log(e)}function wm(e){return Math.exp(e)}function ZT(e){return-Math.log(-e)}function JT(e){return-Math.exp(-e)}function QT(e){return isFinite(e)?+("1e"+e):e<0?0:e}function eE(e){return e===10?QT:e===Math.E?Math.exp:t=>Math.pow(e,t)}function tE(e){return e===Math.E?Math.log:e===10&&Math.log10||e===2&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e)}function _m(e){return(t,r)=>-e(-t,r)}function Rp(e){const t=e(Om,wm),r=t.domain;let n=10,i,a;function o(){return i=tE(n),a=eE(n),r()[0]<0?(i=_m(i),a=_m(a),e(ZT,JT)):e(Om,wm),t}return t.base=function(u){return arguments.length?(n=+u,o()):n},t.domain=function(u){return arguments.length?(r(u),o()):r()},t.ticks=u=>{const c=r();let s=c[0],f=c[c.length-1];const l=f<s;l&&([s,f]=[f,s]);let p=i(s),h=i(f),v,d;const y=u==null?10:+u;let b=[];if(!(n%1)&&h-p<y){if(p=Math.floor(p),h=Math.ceil(h),s>0){for(;p<=h;++p)for(v=1;v<n;++v)if(d=p<0?v/a(-p):v*a(p),!(d<s)){if(d>f)break;b.push(d)}}else for(;p<=h;++p)for(v=n-1;v>=1;--v)if(d=p>0?v/a(-p):v*a(p),!(d<s)){if(d>f)break;b.push(d)}b.length*2<y&&(b=tf(s,f,y))}else b=tf(p,h,Math.min(h-p,y)).map(a);return l?b.reverse():b},t.tickFormat=(u,c)=>{if(u==null&&(u=10),c==null&&(c=n===10?"s":","),typeof c!="function"&&(!(n%1)&&(c=Yn(c)).precision==null&&(c.trim=!0),c=kp(c)),u===1/0)return c;const s=Math.max(1,n*u/t.ticks().length);return f=>{let l=f/a(Math.round(i(f)));return l*n<n-.5&&(l*=n),l<=s?c(f):""}},t.nice=()=>r(Px(r(),{floor:u=>a(Math.floor(i(u))),ceil:u=>a(Math.ceil(i(u)))})),t}function Tx(){const e=Rp(co()).domain([1,10]);return e.copy=()=>Si(e,Tx()).base(e.base()),Qe.apply(e,arguments),e}function Am(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function Sm(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function Dp(e){var t=1,r=e(Am(t),Sm(t));return r.constant=function(n){return arguments.length?e(Am(t=+n),Sm(t)):t},Wt(r)}function Ex(){var e=Dp(co());return e.copy=function(){return Si(e,Ex()).constant(e.constant())},Qe.apply(e,arguments)}function Pm(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function rE(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function nE(e){return e<0?-e*e:e*e}function Np(e){var t=e(ke,ke),r=1;function n(){return r===1?e(ke,ke):r===.5?e(rE,nE):e(Pm(r),Pm(1/r))}return t.exponent=function(i){return arguments.length?(r=+i,n()):r},Wt(t)}function qp(){var e=Np(co());return e.copy=function(){return Si(e,qp()).exponent(e.exponent())},Qe.apply(e,arguments),e}function iE(){return qp.apply(null,arguments).exponent(.5)}function Tm(e){return Math.sign(e)*e*e}function aE(e){return Math.sign(e)*Math.sqrt(Math.abs(e))}function jx(){var e=Ip(),t=[0,1],r=!1,n;function i(a){var o=aE(e(a));return isNaN(o)?n:r?Math.round(o):o}return i.invert=function(a){return e.invert(Tm(a))},i.domain=function(a){return arguments.length?(e.domain(a),i):e.domain()},i.range=function(a){return arguments.length?(e.range((t=Array.from(a,sa)).map(Tm)),i):t.slice()},i.rangeRound=function(a){return i.range(a).round(!0)},i.round=function(a){return arguments.length?(r=!!a,i):r},i.clamp=function(a){return arguments.length?(e.clamp(a),i):e.clamp()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return jx(e.domain(),t).round(r).clamp(e.clamp()).unknown(n)},Qe.apply(i,arguments),Wt(i)}function $x(){var e=[],t=[],r=[],n;function i(){var o=0,u=Math.max(1,t.length);for(r=new Array(u-1);++o<u;)r[o-1]=lT(e,o/u);return a}function a(o){return o==null||isNaN(o=+o)?n:t[_i(r,o)]}return a.invertExtent=function(o){var u=t.indexOf(o);return u<0?[NaN,NaN]:[u>0?r[u-1]:e[0],u<r.length?r[u]:e[e.length-1]]},a.domain=function(o){if(!arguments.length)return e.slice();e=[];for(let u of o)u!=null&&!isNaN(u=+u)&&e.push(u);return e.sort(Nt),i()},a.range=function(o){return arguments.length?(t=Array.from(o),i()):t.slice()},a.unknown=function(o){return arguments.length?(n=o,a):n},a.quantiles=function(){return r.slice()},a.copy=function(){return $x().domain(e).range(t).unknown(n)},Qe.apply(a,arguments)}function Mx(){var e=0,t=1,r=1,n=[.5],i=[0,1],a;function o(c){return c!=null&&c<=c?i[_i(n,c,0,r)]:a}function u(){var c=-1;for(n=new Array(r);++c<r;)n[c]=((c+1)*t-(c-r)*e)/(r+1);return o}return o.domain=function(c){return arguments.length?([e,t]=c,e=+e,t=+t,u()):[e,t]},o.range=function(c){return arguments.length?(r=(i=Array.from(c)).length-1,u()):i.slice()},o.invertExtent=function(c){var s=i.indexOf(c);return s<0?[NaN,NaN]:s<1?[e,n[0]]:s>=r?[n[r-1],t]:[n[s-1],n[s]]},o.unknown=function(c){return arguments.length&&(a=c),o},o.thresholds=function(){return n.slice()},o.copy=function(){return Mx().domain([e,t]).range(i).unknown(a)},Qe.apply(Wt(o),arguments)}function Ix(){var e=[.5],t=[0,1],r,n=1;function i(a){return a!=null&&a<=a?t[_i(e,a,0,n)]:r}return i.domain=function(a){return arguments.length?(e=Array.from(a),n=Math.min(e.length,t.length-1),i):e.slice()},i.range=function(a){return arguments.length?(t=Array.from(a),n=Math.min(e.length,t.length-1),i):t.slice()},i.invertExtent=function(a){var o=t.indexOf(a);return[e[o-1],e[o]]},i.unknown=function(a){return arguments.length?(r=a,i):r},i.copy=function(){return Ix().domain(e).range(t).unknown(r)},Qe.apply(i,arguments)}const Hs=new Date,Gs=new Date;function Ae(e,t,r,n){function i(a){return e(a=arguments.length===0?new Date:new Date(+a)),a}return i.floor=a=>(e(a=new Date(+a)),a),i.ceil=a=>(e(a=new Date(a-1)),t(a,1),e(a),a),i.round=a=>{const o=i(a),u=i.ceil(a);return a-o<u-a?o:u},i.offset=(a,o)=>(t(a=new Date(+a),o==null?1:Math.floor(o)),a),i.range=(a,o,u)=>{const c=[];if(a=i.ceil(a),u=u==null?1:Math.floor(u),!(a<o)||!(u>0))return c;let s;do c.push(s=new Date(+a)),t(a,u),e(a);while(s<a&&a<o);return c},i.filter=a=>Ae(o=>{if(o>=o)for(;e(o),!a(o);)o.setTime(o-1)},(o,u)=>{if(o>=o)if(u<0)for(;++u<=0;)for(;t(o,-1),!a(o););else for(;--u>=0;)for(;t(o,1),!a(o););}),r&&(i.count=(a,o)=>(Hs.setTime(+a),Gs.setTime(+o),e(Hs),e(Gs),Math.floor(r(Hs,Gs))),i.every=a=>(a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?i.filter(n?o=>n(o)%a===0:o=>i.count(0,o)%a===0):i)),i}const pa=Ae(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);pa.every=e=>(e=Math.floor(e),!isFinite(e)||!(e>0)?null:e>1?Ae(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):pa);pa.range;const gt=1e3,Ye=gt*60,bt=Ye*60,_t=bt*24,Lp=_t*7,Em=_t*30,Vs=_t*365,ir=Ae(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+t*gt)},(e,t)=>(t-e)/gt,e=>e.getUTCSeconds());ir.range;const Bp=Ae(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*gt)},(e,t)=>{e.setTime(+e+t*Ye)},(e,t)=>(t-e)/Ye,e=>e.getMinutes());Bp.range;const Fp=Ae(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+t*Ye)},(e,t)=>(t-e)/Ye,e=>e.getUTCMinutes());Fp.range;const zp=Ae(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*gt-e.getMinutes()*Ye)},(e,t)=>{e.setTime(+e+t*bt)},(e,t)=>(t-e)/bt,e=>e.getHours());zp.range;const Wp=Ae(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+t*bt)},(e,t)=>(t-e)/bt,e=>e.getUTCHours());Wp.range;const Pi=Ae(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*Ye)/_t,e=>e.getDate()-1);Pi.range;const so=Ae(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/_t,e=>e.getUTCDate()-1);so.range;const Cx=Ae(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/_t,e=>Math.floor(e/_t));Cx.range;function dr(e){return Ae(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(t,r)=>{t.setDate(t.getDate()+r*7)},(t,r)=>(r-t-(r.getTimezoneOffset()-t.getTimezoneOffset())*Ye)/Lp)}const lo=dr(0),ha=dr(1),oE=dr(2),uE=dr(3),Lr=dr(4),cE=dr(5),sE=dr(6);lo.range;ha.range;oE.range;uE.range;Lr.range;cE.range;sE.range;function vr(e){return Ae(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCDate(t.getUTCDate()+r*7)},(t,r)=>(r-t)/Lp)}const fo=vr(0),da=vr(1),lE=vr(2),fE=vr(3),Br=vr(4),pE=vr(5),hE=vr(6);fo.range;da.range;lE.range;fE.range;Br.range;pE.range;hE.range;const Up=Ae(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());Up.range;const Kp=Ae(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());Kp.range;const At=Ae(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());At.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:Ae(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)});At.range;const St=Ae(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());St.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:Ae(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)});St.range;function kx(e,t,r,n,i,a){const o=[[ir,1,gt],[ir,5,5*gt],[ir,15,15*gt],[ir,30,30*gt],[a,1,Ye],[a,5,5*Ye],[a,15,15*Ye],[a,30,30*Ye],[i,1,bt],[i,3,3*bt],[i,6,6*bt],[i,12,12*bt],[n,1,_t],[n,2,2*_t],[r,1,Lp],[t,1,Em],[t,3,3*Em],[e,1,Vs]];function u(s,f,l){const p=f<s;p&&([s,f]=[f,s]);const h=l&&typeof l.range=="function"?l:c(s,f,l),v=h?h.range(s,+f+1):[];return p?v.reverse():v}function c(s,f,l){const p=Math.abs(f-s)/l,h=Tp(([,,y])=>y).right(o,p);if(h===o.length)return e.every(nf(s/Vs,f/Vs,l));if(h===0)return pa.every(Math.max(nf(s,f,l),1));const[v,d]=o[p/o[h-1][2]<o[h][2]/p?h-1:h];return v.every(d)}return[u,c]}const[dE,vE]=kx(St,Kp,fo,Cx,Wp,Fp),[yE,mE]=kx(At,Up,lo,Pi,zp,Bp);function Xs(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function Ys(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function xn(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}function gE(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,u=e.months,c=e.shortMonths,s=On(i),f=wn(i),l=On(a),p=wn(a),h=On(o),v=wn(o),d=On(u),y=wn(u),b=On(c),g=wn(c),x={a:R,A:L,b:B,B:K,c:null,d:km,e:km,f:FE,g:ZE,G:QE,H:qE,I:LE,j:BE,L:Rx,m:zE,M:WE,p:X,q:z,Q:Nm,s:qm,S:UE,u:KE,U:HE,V:GE,w:VE,W:XE,x:null,X:null,y:YE,Y:JE,Z:ej,"%":Dm},w={a:Y,A:le,b:me,B:Be,c:null,d:Rm,e:Rm,f:ij,g:dj,G:yj,H:tj,I:rj,j:nj,L:Nx,m:aj,M:oj,p:Gt,q:De,Q:Nm,s:qm,S:uj,u:cj,U:sj,V:lj,w:fj,W:pj,x:null,X:null,y:hj,Y:vj,Z:mj,"%":Dm},m={a:j,A:P,b:E,B:$,c:I,d:Im,e:Im,f:kE,g:Mm,G:$m,H:Cm,I:Cm,j:$E,L:CE,m:jE,M:ME,p:T,q:EE,Q:DE,s:NE,S:IE,u:_E,U:AE,V:SE,w:wE,W:PE,x:M,X:k,y:Mm,Y:$m,Z:TE,"%":RE};x.x=O(r,x),x.X=O(n,x),x.c=O(t,x),w.x=O(r,w),w.X=O(n,w),w.c=O(t,w);function O(F,Q){return function(ee){var D=[],ve=-1,te=0,xe=F.length,Oe,Ne,$t;for(ee instanceof Date||(ee=new Date(+ee));++ve<xe;)F.charCodeAt(ve)===37&&(D.push(F.slice(te,ve)),(Ne=jm[Oe=F.charAt(++ve)])!=null?Oe=F.charAt(++ve):Ne=Oe==="e"?" ":"0",($t=Q[Oe])&&(Oe=$t(ee,Ne)),D.push(Oe),te=ve+1);return D.push(F.slice(te,ve)),D.join("")}}function _(F,Q){return function(ee){var D=xn(1900,void 0,1),ve=S(D,F,ee+="",0),te,xe;if(ve!=ee.length)return null;if("Q"in D)return new Date(D.Q);if("s"in D)return new Date(D.s*1e3+("L"in D?D.L:0));if(Q&&!("Z"in D)&&(D.Z=0),"p"in D&&(D.H=D.H%12+D.p*12),D.m===void 0&&(D.m="q"in D?D.q:0),"V"in D){if(D.V<1||D.V>53)return null;"w"in D||(D.w=1),"Z"in D?(te=Ys(xn(D.y,0,1)),xe=te.getUTCDay(),te=xe>4||xe===0?da.ceil(te):da(te),te=so.offset(te,(D.V-1)*7),D.y=te.getUTCFullYear(),D.m=te.getUTCMonth(),D.d=te.getUTCDate()+(D.w+6)%7):(te=Xs(xn(D.y,0,1)),xe=te.getDay(),te=xe>4||xe===0?ha.ceil(te):ha(te),te=Pi.offset(te,(D.V-1)*7),D.y=te.getFullYear(),D.m=te.getMonth(),D.d=te.getDate()+(D.w+6)%7)}else("W"in D||"U"in D)&&("w"in D||(D.w="u"in D?D.u%7:"W"in D?1:0),xe="Z"in D?Ys(xn(D.y,0,1)).getUTCDay():Xs(xn(D.y,0,1)).getDay(),D.m=0,D.d="W"in D?(D.w+6)%7+D.W*7-(xe+5)%7:D.w+D.U*7-(xe+6)%7);return"Z"in D?(D.H+=D.Z/100|0,D.M+=D.Z%100,Ys(D)):Xs(D)}}function S(F,Q,ee,D){for(var ve=0,te=Q.length,xe=ee.length,Oe,Ne;ve<te;){if(D>=xe)return-1;if(Oe=Q.charCodeAt(ve++),Oe===37){if(Oe=Q.charAt(ve++),Ne=m[Oe in jm?Q.charAt(ve++):Oe],!Ne||(D=Ne(F,ee,D))<0)return-1}else if(Oe!=ee.charCodeAt(D++))return-1}return D}function T(F,Q,ee){var D=s.exec(Q.slice(ee));return D?(F.p=f.get(D[0].toLowerCase()),ee+D[0].length):-1}function j(F,Q,ee){var D=h.exec(Q.slice(ee));return D?(F.w=v.get(D[0].toLowerCase()),ee+D[0].length):-1}function P(F,Q,ee){var D=l.exec(Q.slice(ee));return D?(F.w=p.get(D[0].toLowerCase()),ee+D[0].length):-1}function E(F,Q,ee){var D=b.exec(Q.slice(ee));return D?(F.m=g.get(D[0].toLowerCase()),ee+D[0].length):-1}function $(F,Q,ee){var D=d.exec(Q.slice(ee));return D?(F.m=y.get(D[0].toLowerCase()),ee+D[0].length):-1}function I(F,Q,ee){return S(F,t,Q,ee)}function M(F,Q,ee){return S(F,r,Q,ee)}function k(F,Q,ee){return S(F,n,Q,ee)}function R(F){return o[F.getDay()]}function L(F){return a[F.getDay()]}function B(F){return c[F.getMonth()]}function K(F){return u[F.getMonth()]}function X(F){return i[+(F.getHours()>=12)]}function z(F){return 1+~~(F.getMonth()/3)}function Y(F){return o[F.getUTCDay()]}function le(F){return a[F.getUTCDay()]}function me(F){return c[F.getUTCMonth()]}function Be(F){return u[F.getUTCMonth()]}function Gt(F){return i[+(F.getUTCHours()>=12)]}function De(F){return 1+~~(F.getUTCMonth()/3)}return{format:function(F){var Q=O(F+="",x);return Q.toString=function(){return F},Q},parse:function(F){var Q=_(F+="",!1);return Q.toString=function(){return F},Q},utcFormat:function(F){var Q=O(F+="",w);return Q.toString=function(){return F},Q},utcParse:function(F){var Q=_(F+="",!0);return Q.toString=function(){return F},Q}}}var jm={"-":"",_:" ",0:"0"},Te=/^\s*\d+/,bE=/^%/,xE=/[\\^$*+?|[\]().{}]/g;function re(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?new Array(r-a+1).join(t)+i:i)}function OE(e){return e.replace(xE,"\\$&")}function On(e){return new RegExp("^(?:"+e.map(OE).join("|")+")","i")}function wn(e){return new Map(e.map((t,r)=>[t.toLowerCase(),r]))}function wE(e,t,r){var n=Te.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function _E(e,t,r){var n=Te.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function AE(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function SE(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function PE(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function $m(e,t,r){var n=Te.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function Mm(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function TE(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function EE(e,t,r){var n=Te.exec(t.slice(r,r+1));return n?(e.q=n[0]*3-3,r+n[0].length):-1}function jE(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function Im(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function $E(e,t,r){var n=Te.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function Cm(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function ME(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function IE(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function CE(e,t,r){var n=Te.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function kE(e,t,r){var n=Te.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function RE(e,t,r){var n=bE.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function DE(e,t,r){var n=Te.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function NE(e,t,r){var n=Te.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function km(e,t){return re(e.getDate(),t,2)}function qE(e,t){return re(e.getHours(),t,2)}function LE(e,t){return re(e.getHours()%12||12,t,2)}function BE(e,t){return re(1+Pi.count(At(e),e),t,3)}function Rx(e,t){return re(e.getMilliseconds(),t,3)}function FE(e,t){return Rx(e,t)+"000"}function zE(e,t){return re(e.getMonth()+1,t,2)}function WE(e,t){return re(e.getMinutes(),t,2)}function UE(e,t){return re(e.getSeconds(),t,2)}function KE(e){var t=e.getDay();return t===0?7:t}function HE(e,t){return re(lo.count(At(e)-1,e),t,2)}function Dx(e){var t=e.getDay();return t>=4||t===0?Lr(e):Lr.ceil(e)}function GE(e,t){return e=Dx(e),re(Lr.count(At(e),e)+(At(e).getDay()===4),t,2)}function VE(e){return e.getDay()}function XE(e,t){return re(ha.count(At(e)-1,e),t,2)}function YE(e,t){return re(e.getFullYear()%100,t,2)}function ZE(e,t){return e=Dx(e),re(e.getFullYear()%100,t,2)}function JE(e,t){return re(e.getFullYear()%1e4,t,4)}function QE(e,t){var r=e.getDay();return e=r>=4||r===0?Lr(e):Lr.ceil(e),re(e.getFullYear()%1e4,t,4)}function ej(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+re(t/60|0,"0",2)+re(t%60,"0",2)}function Rm(e,t){return re(e.getUTCDate(),t,2)}function tj(e,t){return re(e.getUTCHours(),t,2)}function rj(e,t){return re(e.getUTCHours()%12||12,t,2)}function nj(e,t){return re(1+so.count(St(e),e),t,3)}function Nx(e,t){return re(e.getUTCMilliseconds(),t,3)}function ij(e,t){return Nx(e,t)+"000"}function aj(e,t){return re(e.getUTCMonth()+1,t,2)}function oj(e,t){return re(e.getUTCMinutes(),t,2)}function uj(e,t){return re(e.getUTCSeconds(),t,2)}function cj(e){var t=e.getUTCDay();return t===0?7:t}function sj(e,t){return re(fo.count(St(e)-1,e),t,2)}function qx(e){var t=e.getUTCDay();return t>=4||t===0?Br(e):Br.ceil(e)}function lj(e,t){return e=qx(e),re(Br.count(St(e),e)+(St(e).getUTCDay()===4),t,2)}function fj(e){return e.getUTCDay()}function pj(e,t){return re(da.count(St(e)-1,e),t,2)}function hj(e,t){return re(e.getUTCFullYear()%100,t,2)}function dj(e,t){return e=qx(e),re(e.getUTCFullYear()%100,t,2)}function vj(e,t){return re(e.getUTCFullYear()%1e4,t,4)}function yj(e,t){var r=e.getUTCDay();return e=r>=4||r===0?Br(e):Br.ceil(e),re(e.getUTCFullYear()%1e4,t,4)}function mj(){return"+0000"}function Dm(){return"%"}function Nm(e){return+e}function qm(e){return Math.floor(+e/1e3)}var Or,Lx,Bx;gj({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function gj(e){return Or=gE(e),Lx=Or.format,Or.parse,Bx=Or.utcFormat,Or.utcParse,Or}function bj(e){return new Date(e)}function xj(e){return e instanceof Date?+e:+new Date(+e)}function Hp(e,t,r,n,i,a,o,u,c,s){var f=Ip(),l=f.invert,p=f.domain,h=s(".%L"),v=s(":%S"),d=s("%I:%M"),y=s("%I %p"),b=s("%a %d"),g=s("%b %d"),x=s("%B"),w=s("%Y");function m(O){return(c(O)<O?h:u(O)<O?v:o(O)<O?d:a(O)<O?y:n(O)<O?i(O)<O?b:g:r(O)<O?x:w)(O)}return f.invert=function(O){return new Date(l(O))},f.domain=function(O){return arguments.length?p(Array.from(O,xj)):p().map(bj)},f.ticks=function(O){var _=p();return e(_[0],_[_.length-1],O==null?10:O)},f.tickFormat=function(O,_){return _==null?m:s(_)},f.nice=function(O){var _=p();return(!O||typeof O.range!="function")&&(O=t(_[0],_[_.length-1],O==null?10:O)),O?p(Px(_,O)):f},f.copy=function(){return Si(f,Hp(e,t,r,n,i,a,o,u,c,s))},f}function Oj(){return Qe.apply(Hp(yE,mE,At,Up,lo,Pi,zp,Bp,ir,Lx).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function wj(){return Qe.apply(Hp(dE,vE,St,Kp,fo,so,Wp,Fp,ir,Bx).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function po(){var e=0,t=1,r,n,i,a,o=ke,u=!1,c;function s(l){return l==null||isNaN(l=+l)?c:o(i===0?.5:(l=(a(l)-r)*i,u?Math.max(0,Math.min(1,l)):l))}s.domain=function(l){return arguments.length?([e,t]=l,r=a(e=+e),n=a(t=+t),i=r===n?0:1/(n-r),s):[e,t]},s.clamp=function(l){return arguments.length?(u=!!l,s):u},s.interpolator=function(l){return arguments.length?(o=l,s):o};function f(l){return function(p){var h,v;return arguments.length?([h,v]=p,o=l(h,v),s):[o(0),o(1)]}}return s.range=f(hn),s.rangeRound=f(Mp),s.unknown=function(l){return arguments.length?(c=l,s):c},function(l){return a=l,r=l(e),n=l(t),i=r===n?0:1/(n-r),s}}function Ut(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function Fx(){var e=Wt(po()(ke));return e.copy=function(){return Ut(e,Fx())},jt.apply(e,arguments)}function zx(){var e=Rp(po()).domain([1,10]);return e.copy=function(){return Ut(e,zx()).base(e.base())},jt.apply(e,arguments)}function Wx(){var e=Dp(po());return e.copy=function(){return Ut(e,Wx()).constant(e.constant())},jt.apply(e,arguments)}function Gp(){var e=Np(po());return e.copy=function(){return Ut(e,Gp()).exponent(e.exponent())},jt.apply(e,arguments)}function _j(){return Gp.apply(null,arguments).exponent(.5)}function Ux(){var e=[],t=ke;function r(n){if(n!=null&&!isNaN(n=+n))return t((_i(e,n,1)-1)/(e.length-1))}return r.domain=function(n){if(!arguments.length)return e.slice();e=[];for(let i of n)i!=null&&!isNaN(i=+i)&&e.push(i);return e.sort(Nt),r},r.interpolator=function(n){return arguments.length?(t=n,r):t},r.range=function(){return e.map((n,i)=>t(i/(e.length-1)))},r.quantiles=function(n){return Array.from({length:n+1},(i,a)=>sT(e,a/n))},r.copy=function(){return Ux(t).domain(e)},jt.apply(r,arguments)}function ho(){var e=0,t=.5,r=1,n=1,i,a,o,u,c,s=ke,f,l=!1,p;function h(d){return isNaN(d=+d)?p:(d=.5+((d=+f(d))-a)*(n*d<n*a?u:c),s(l?Math.max(0,Math.min(1,d)):d))}h.domain=function(d){return arguments.length?([e,t,r]=d,i=f(e=+e),a=f(t=+t),o=f(r=+r),u=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,h):[e,t,r]},h.clamp=function(d){return arguments.length?(l=!!d,h):l},h.interpolator=function(d){return arguments.length?(s=d,h):s};function v(d){return function(y){var b,g,x;return arguments.length?([b,g,x]=y,s=RT(d,[b,g,x]),h):[s(0),s(.5),s(1)]}}return h.range=v(hn),h.rangeRound=v(Mp),h.unknown=function(d){return arguments.length?(p=d,h):p},function(d){return f=d,i=d(e),a=d(t),o=d(r),u=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,h}}function Kx(){var e=Wt(ho()(ke));return e.copy=function(){return Ut(e,Kx())},jt.apply(e,arguments)}function Hx(){var e=Rp(ho()).domain([.1,1,10]);return e.copy=function(){return Ut(e,Hx()).base(e.base())},jt.apply(e,arguments)}function Gx(){var e=Dp(ho());return e.copy=function(){return Ut(e,Gx()).constant(e.constant())},jt.apply(e,arguments)}function Vp(){var e=Np(ho());return e.copy=function(){return Ut(e,Vp()).exponent(e.exponent())},jt.apply(e,arguments)}function Aj(){return Vp.apply(null,arguments).exponent(.5)}const Lm=Object.freeze(Object.defineProperty({__proto__:null,scaleBand:Hn,scaleDiverging:Kx,scaleDivergingLog:Hx,scaleDivergingPow:Vp,scaleDivergingSqrt:Aj,scaleDivergingSymlog:Gx,scaleIdentity:Sx,scaleImplicit:af,scaleLinear:fa,scaleLog:Tx,scaleOrdinal:Ep,scalePoint:Mn,scalePow:qp,scaleQuantile:$x,scaleQuantize:Mx,scaleRadial:jx,scaleSequential:Fx,scaleSequentialLog:zx,scaleSequentialPow:Gp,scaleSequentialQuantile:Ux,scaleSequentialSqrt:_j,scaleSequentialSymlog:Wx,scaleSqrt:iE,scaleSymlog:Ex,scaleThreshold:Ix,scaleTime:Oj,scaleUtc:wj,tickFormat:Ax},Symbol.toStringTag,{value:"Module"}));var Zs,Bm;function vo(){if(Bm)return Zs;Bm=1;var e=sn();function t(r,n,i){for(var a=-1,o=r.length;++a<o;){var u=r[a],c=n(u);if(c!=null&&(s===void 0?c===c&&!e(c):i(c,s)))var s=c,f=u}return f}return Zs=t,Zs}var Js,Fm;function Vx(){if(Fm)return Js;Fm=1;function e(t,r){return t>r}return Js=e,Js}var Qs,zm;function Sj(){if(zm)return Qs;zm=1;var e=vo(),t=Vx(),r=pn();function n(i){return i&&i.length?e(i,r,t):void 0}return Qs=n,Qs}var Pj=Sj();const kt=ae(Pj);var el,Wm;function Xx(){if(Wm)return el;Wm=1;function e(t,r){return t<r}return el=e,el}var tl,Um;function Tj(){if(Um)return tl;Um=1;var e=vo(),t=Xx(),r=pn();function n(i){return i&&i.length?e(i,r,t):void 0}return tl=n,tl}var Ej=Tj();const yo=ae(Ej);var rl,Km;function jj(){if(Km)return rl;Km=1;var e=fp(),t=dt(),r=nx(),n=Le();function i(a,o){var u=n(a)?e:r;return u(a,t(o,3))}return rl=i,rl}var nl,Hm;function $j(){if(Hm)return nl;Hm=1;var e=tx(),t=jj();function r(n,i){return e(t(n,i),1)}return nl=r,nl}var Mj=$j();const Ij=ae(Mj);var il,Gm;function Cj(){if(Gm)return il;Gm=1;var e=_p();function t(r,n){return e(r,n)}return il=t,il}var kj=Cj();const Bt=ae(kj);var dn=1e9,Rj={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},Yp,he=!0,Ze="[DecimalError] ",cr=Ze+"Invalid argument: ",Xp=Ze+"Exponent out of range: ",vn=Math.floor,er=Math.pow,Dj=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,Ue,Se=1e7,fe=7,Yx=9007199254740991,va=vn(Yx/fe),W={};W.absoluteValue=W.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e};W.comparedTo=W.cmp=function(e){var t,r,n,i,a=this;if(e=new a.constructor(e),a.s!==e.s)return a.s||-e.s;if(a.e!==e.e)return a.e>e.e^a.s<0?1:-1;for(n=a.d.length,i=e.d.length,t=0,r=n<i?n:i;t<r;++t)if(a.d[t]!==e.d[t])return a.d[t]>e.d[t]^a.s<0?1:-1;return n===i?0:n>i^a.s<0?1:-1};W.decimalPlaces=W.dp=function(){var e=this,t=e.d.length-1,r=(t-e.e)*fe;if(t=e.d[t],t)for(;t%10==0;t/=10)r--;return r<0?0:r};W.dividedBy=W.div=function(e){return wt(this,new this.constructor(e))};W.dividedToIntegerBy=W.idiv=function(e){var t=this,r=t.constructor;return oe(wt(t,new r(e),0,1),r.precision)};W.equals=W.eq=function(e){return!this.cmp(e)};W.exponent=function(){return be(this)};W.greaterThan=W.gt=function(e){return this.cmp(e)>0};W.greaterThanOrEqualTo=W.gte=function(e){return this.cmp(e)>=0};W.isInteger=W.isint=function(){return this.e>this.d.length-2};W.isNegative=W.isneg=function(){return this.s<0};W.isPositive=W.ispos=function(){return this.s>0};W.isZero=function(){return this.s===0};W.lessThan=W.lt=function(e){return this.cmp(e)<0};W.lessThanOrEqualTo=W.lte=function(e){return this.cmp(e)<1};W.logarithm=W.log=function(e){var t,r=this,n=r.constructor,i=n.precision,a=i+5;if(e===void 0)e=new n(10);else if(e=new n(e),e.s<1||e.eq(Ue))throw Error(Ze+"NaN");if(r.s<1)throw Error(Ze+(r.s?"NaN":"-Infinity"));return r.eq(Ue)?new n(0):(he=!1,t=wt(Zn(r,a),Zn(e,a),a),he=!0,oe(t,i))};W.minus=W.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Qx(t,e):Zx(t,(e.s=-e.s,e))};W.modulo=W.mod=function(e){var t,r=this,n=r.constructor,i=n.precision;if(e=new n(e),!e.s)throw Error(Ze+"NaN");return r.s?(he=!1,t=wt(r,e,0,1).times(e),he=!0,r.minus(t)):oe(new n(r),i)};W.naturalExponential=W.exp=function(){return Jx(this)};W.naturalLogarithm=W.ln=function(){return Zn(this)};W.negated=W.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e};W.plus=W.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Zx(t,e):Qx(t,(e.s=-e.s,e))};W.precision=W.sd=function(e){var t,r,n,i=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(cr+e);if(t=be(i)+1,n=i.d.length-1,r=n*fe+1,n=i.d[n],n){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return e&&t>r?t:r};W.squareRoot=W.sqrt=function(){var e,t,r,n,i,a,o,u=this,c=u.constructor;if(u.s<1){if(!u.s)return new c(0);throw Error(Ze+"NaN")}for(e=be(u),he=!1,i=Math.sqrt(+u),i==0||i==1/0?(t=ct(u.d),(t.length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=vn((e+1)/2)-(e<0||e%2),i==1/0?t="5e"+e:(t=i.toExponential(),t=t.slice(0,t.indexOf("e")+1)+e),n=new c(t)):n=new c(i.toString()),r=c.precision,i=o=r+3;;)if(a=n,n=a.plus(wt(u,a,o+2)).times(.5),ct(a.d).slice(0,o)===(t=ct(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&t=="4999"){if(oe(a,r+1,0),a.times(a).eq(u)){n=a;break}}else if(t!="9999")break;o+=4}return he=!0,oe(n,r)};W.times=W.mul=function(e){var t,r,n,i,a,o,u,c,s,f=this,l=f.constructor,p=f.d,h=(e=new l(e)).d;if(!f.s||!e.s)return new l(0);for(e.s*=f.s,r=f.e+e.e,c=p.length,s=h.length,c<s&&(a=p,p=h,h=a,o=c,c=s,s=o),a=[],o=c+s,n=o;n--;)a.push(0);for(n=s;--n>=0;){for(t=0,i=c+n;i>n;)u=a[i]+h[n]*p[i-n-1]+t,a[i--]=u%Se|0,t=u/Se|0;a[i]=(a[i]+t)%Se|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,he?oe(e,l.precision):e};W.toDecimalPlaces=W.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(pt(e,0,dn),t===void 0?t=n.rounding:pt(t,0,8),oe(r,e+be(r)+1,t))};W.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=fr(n,!0):(pt(e,0,dn),t===void 0?t=i.rounding:pt(t,0,8),n=oe(new i(n),e+1,t),r=fr(n,!0,e+1)),r};W.toFixed=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?fr(i):(pt(e,0,dn),t===void 0?t=a.rounding:pt(t,0,8),n=oe(new a(i),e+be(i)+1,t),r=fr(n.abs(),!1,e+be(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)};W.toInteger=W.toint=function(){var e=this,t=e.constructor;return oe(new t(e),be(e)+1,t.rounding)};W.toNumber=function(){return+this};W.toPower=W.pow=function(e){var t,r,n,i,a,o,u=this,c=u.constructor,s=12,f=+(e=new c(e));if(!e.s)return new c(Ue);if(u=new c(u),!u.s){if(e.s<1)throw Error(Ze+"Infinity");return u}if(u.eq(Ue))return u;if(n=c.precision,e.eq(Ue))return oe(u,n);if(t=e.e,r=e.d.length-1,o=t>=r,a=u.s,o){if((r=f<0?-f:f)<=Yx){for(i=new c(Ue),t=Math.ceil(n/fe+4),he=!1;r%2&&(i=i.times(u),Xm(i.d,t)),r=vn(r/2),r!==0;)u=u.times(u),Xm(u.d,t);return he=!0,e.s<0?new c(Ue).div(i):oe(i,n)}}else if(a<0)throw Error(Ze+"NaN");return a=a<0&&e.d[Math.max(t,r)]&1?-1:1,u.s=1,he=!1,i=e.times(Zn(u,n+s)),he=!0,i=Jx(i),i.s=a,i};W.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?(r=be(i),n=fr(i,r<=a.toExpNeg||r>=a.toExpPos)):(pt(e,1,dn),t===void 0?t=a.rounding:pt(t,0,8),i=oe(new a(i),e,t),r=be(i),n=fr(i,e<=r||r<=a.toExpNeg,e)),n};W.toSignificantDigits=W.tosd=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(pt(e,1,dn),t===void 0?t=n.rounding:pt(t,0,8)),oe(new n(r),e,t)};W.toString=W.valueOf=W.val=W.toJSON=W[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=this,t=be(e),r=e.constructor;return fr(e,t<=r.toExpNeg||t>=r.toExpPos)};function Zx(e,t){var r,n,i,a,o,u,c,s,f=e.constructor,l=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),he?oe(t,l):t;if(c=e.d,s=t.d,o=e.e,i=t.e,c=c.slice(),a=o-i,a){for(a<0?(n=c,a=-a,u=s.length):(n=s,i=o,u=c.length),o=Math.ceil(l/fe),u=o>u?o+1:u+1,a>u&&(a=u,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for(u=c.length,a=s.length,u-a<0&&(a=u,n=s,s=c,c=n),r=0;a;)r=(c[--a]=c[a]+s[a]+r)/Se|0,c[a]%=Se;for(r&&(c.unshift(r),++i),u=c.length;c[--u]==0;)c.pop();return t.d=c,t.e=i,he?oe(t,l):t}function pt(e,t,r){if(e!==~~e||e<t||e>r)throw Error(cr+e)}function ct(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)n=e[t]+"",r=fe-n.length,r&&(a+=It(r)),a+=n;o=e[t],n=o+"",r=fe-n.length,r&&(a+=It(r))}else if(o===0)return"0";for(;o%10===0;)o/=10;return a+o}var wt=function(){function e(n,i){var a,o=0,u=n.length;for(n=n.slice();u--;)a=n[u]*i+o,n[u]=a%Se|0,o=a/Se|0;return o&&n.unshift(o),n}function t(n,i,a,o){var u,c;if(a!=o)c=a>o?1:-1;else for(u=c=0;u<a;u++)if(n[u]!=i[u]){c=n[u]>i[u]?1:-1;break}return c}function r(n,i,a){for(var o=0;a--;)n[a]-=o,o=n[a]<i[a]?1:0,n[a]=o*Se+n[a]-i[a];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,a,o){var u,c,s,f,l,p,h,v,d,y,b,g,x,w,m,O,_,S,T=n.constructor,j=n.s==i.s?1:-1,P=n.d,E=i.d;if(!n.s)return new T(n);if(!i.s)throw Error(Ze+"Division by zero");for(c=n.e-i.e,_=E.length,m=P.length,h=new T(j),v=h.d=[],s=0;E[s]==(P[s]||0);)++s;if(E[s]>(P[s]||0)&&--c,a==null?g=a=T.precision:o?g=a+(be(n)-be(i))+1:g=a,g<0)return new T(0);if(g=g/fe+2|0,s=0,_==1)for(f=0,E=E[0],g++;(s<m||f)&&g--;s++)x=f*Se+(P[s]||0),v[s]=x/E|0,f=x%E|0;else{for(f=Se/(E[0]+1)|0,f>1&&(E=e(E,f),P=e(P,f),_=E.length,m=P.length),w=_,d=P.slice(0,_),y=d.length;y<_;)d[y++]=0;S=E.slice(),S.unshift(0),O=E[0],E[1]>=Se/2&&++O;do f=0,u=t(E,d,_,y),u<0?(b=d[0],_!=y&&(b=b*Se+(d[1]||0)),f=b/O|0,f>1?(f>=Se&&(f=Se-1),l=e(E,f),p=l.length,y=d.length,u=t(l,d,p,y),u==1&&(f--,r(l,_<p?S:E,p))):(f==0&&(u=f=1),l=E.slice()),p=l.length,p<y&&l.unshift(0),r(d,l,y),u==-1&&(y=d.length,u=t(E,d,_,y),u<1&&(f++,r(d,_<y?S:E,y))),y=d.length):u===0&&(f++,d=[0]),v[s++]=f,u&&d[0]?d[y++]=P[w]||0:(d=[P[w]],y=1);while((w++<m||d[0]!==void 0)&&g--)}return v[0]||v.shift(),h.e=c,oe(h,o?a+be(h)+1:a)}}();function Jx(e,t){var r,n,i,a,o,u,c=0,s=0,f=e.constructor,l=f.precision;if(be(e)>16)throw Error(Xp+be(e));if(!e.s)return new f(Ue);for(he=!1,u=l,o=new f(.03125);e.abs().gte(.1);)e=e.times(o),s+=5;for(n=Math.log(er(2,s))/Math.LN10*2+5|0,u+=n,r=i=a=new f(Ue),f.precision=u;;){if(i=oe(i.times(e),u),r=r.times(++c),o=a.plus(wt(i,r,u)),ct(o.d).slice(0,u)===ct(a.d).slice(0,u)){for(;s--;)a=oe(a.times(a),u);return f.precision=l,t==null?(he=!0,oe(a,l)):a}a=o}}function be(e){for(var t=e.e*fe,r=e.d[0];r>=10;r/=10)t++;return t}function al(e,t,r){if(t>e.LN10.sd())throw he=!0,r&&(e.precision=r),Error(Ze+"LN10 precision limit exceeded");return oe(new e(e.LN10),t)}function It(e){for(var t="";e--;)t+="0";return t}function Zn(e,t){var r,n,i,a,o,u,c,s,f,l=1,p=10,h=e,v=h.d,d=h.constructor,y=d.precision;if(h.s<1)throw Error(Ze+(h.s?"NaN":"-Infinity"));if(h.eq(Ue))return new d(0);if(t==null?(he=!1,s=y):s=t,h.eq(10))return t==null&&(he=!0),al(d,s);if(s+=p,d.precision=s,r=ct(v),n=r.charAt(0),a=be(h),Math.abs(a)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)h=h.times(e),r=ct(h.d),n=r.charAt(0),l++;a=be(h),n>1?(h=new d("0."+r),a++):h=new d(n+"."+r.slice(1))}else return c=al(d,s+2,y).times(a+""),h=Zn(new d(n+"."+r.slice(1)),s-p).plus(c),d.precision=y,t==null?(he=!0,oe(h,y)):h;for(u=o=h=wt(h.minus(Ue),h.plus(Ue),s),f=oe(h.times(h),s),i=3;;){if(o=oe(o.times(f),s),c=u.plus(wt(o,new d(i),s)),ct(c.d).slice(0,s)===ct(u.d).slice(0,s))return u=u.times(2),a!==0&&(u=u.plus(al(d,s+2,y).times(a+""))),u=wt(u,new d(l),s),d.precision=y,t==null?(he=!0,oe(u,y)):u;u=c,i+=2}}function Vm(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;)++n;for(i=t.length;t.charCodeAt(i-1)===48;)--i;if(t=t.slice(n,i),t){if(i-=n,r=r-n-1,e.e=vn(r/fe),e.d=[],n=(r+1)%fe,r<0&&(n+=fe),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=fe;n<i;)e.d.push(+t.slice(n,n+=fe));t=t.slice(n),n=fe-t.length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),he&&(e.e>va||e.e<-va))throw Error(Xp+r)}else e.s=0,e.e=0,e.d=[0];return e}function oe(e,t,r){var n,i,a,o,u,c,s,f,l=e.d;for(o=1,a=l[0];a>=10;a/=10)o++;if(n=t-o,n<0)n+=fe,i=t,s=l[f=0];else{if(f=Math.ceil((n+1)/fe),a=l.length,f>=a)return e;for(s=a=l[f],o=1;a>=10;a/=10)o++;n%=fe,i=n-fe+o}if(r!==void 0&&(a=er(10,o-i-1),u=s/a%10|0,c=t<0||l[f+1]!==void 0||s%a,c=r<4?(u||c)&&(r==0||r==(e.s<0?3:2)):u>5||u==5&&(r==4||c||r==6&&(n>0?i>0?s/er(10,o-i):0:l[f-1])%10&1||r==(e.s<0?8:7))),t<1||!l[0])return c?(a=be(e),l.length=1,t=t-a-1,l[0]=er(10,(fe-t%fe)%fe),e.e=vn(-t/fe)||0):(l.length=1,l[0]=e.e=e.s=0),e;if(n==0?(l.length=f,a=1,f--):(l.length=f+1,a=er(10,fe-n),l[f]=i>0?(s/er(10,o-i)%er(10,i)|0)*a:0),c)for(;;)if(f==0){(l[0]+=a)==Se&&(l[0]=1,++e.e);break}else{if(l[f]+=a,l[f]!=Se)break;l[f--]=0,a=1}for(n=l.length;l[--n]===0;)l.pop();if(he&&(e.e>va||e.e<-va))throw Error(Xp+be(e));return e}function Qx(e,t){var r,n,i,a,o,u,c,s,f,l,p=e.constructor,h=p.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new p(e),he?oe(t,h):t;if(c=e.d,l=t.d,n=t.e,s=e.e,c=c.slice(),o=s-n,o){for(f=o<0,f?(r=c,o=-o,u=l.length):(r=l,n=s,u=c.length),i=Math.max(Math.ceil(h/fe),u)+2,o>i&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for(i=c.length,u=l.length,f=i<u,f&&(u=i),i=0;i<u;i++)if(c[i]!=l[i]){f=c[i]<l[i];break}o=0}for(f&&(r=c,c=l,l=r,t.s=-t.s),u=c.length,i=l.length-u;i>0;--i)c[u++]=0;for(i=l.length;i>o;){if(c[--i]<l[i]){for(a=i;a&&c[--a]===0;)c[a]=Se-1;--c[a],c[i]+=Se}c[i]-=l[i]}for(;c[--u]===0;)c.pop();for(;c[0]===0;c.shift())--n;return c[0]?(t.d=c,t.e=n,he?oe(t,h):t):new p(0)}function fr(e,t,r){var n,i=be(e),a=ct(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+It(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+It(-i-1)+a,r&&(n=r-o)>0&&(a+=It(n))):i>=o?(a+=It(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+It(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=It(n))),e.s<0?"-"+a:a}function Xm(e,t){if(e.length>t)return e.length=t,!0}function eO(e){var t,r,n;function i(a){var o=this;if(!(o instanceof i))return new i(a);if(o.constructor=i,a instanceof i){o.s=a.s,o.e=a.e,o.d=(a=a.d)?a.slice():a;return}if(typeof a=="number"){if(a*0!==0)throw Error(cr+a);if(a>0)o.s=1;else if(a<0)a=-a,o.s=-1;else{o.s=0,o.e=0,o.d=[0];return}if(a===~~a&&a<1e7){o.e=0,o.d=[a];return}return Vm(o,a.toString())}else if(typeof a!="string")throw Error(cr+a);if(a.charCodeAt(0)===45?(a=a.slice(1),o.s=-1):o.s=1,Dj.test(a))Vm(o,a);else throw Error(cr+a)}if(i.prototype=W,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=eO,i.config=i.set=Nj,e===void 0&&(e={}),e)for(n=["precision","rounding","toExpNeg","toExpPos","LN10"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function Nj(e){if(!e||typeof e!="object")throw Error(Ze+"Object expected");var t,r,n,i=["precision",1,dn,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if((n=e[r=i[t]])!==void 0)if(vn(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(cr+r+": "+n);if((n=e[r="LN10"])!==void 0)if(n==Math.LN10)this[r]=new this(n);else throw Error(cr+r+": "+n);return this}var Yp=eO(Rj);Ue=new Yp(1);const ie=Yp;function qj(e){return zj(e)||Fj(e)||Bj(e)||Lj()}function Lj(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Bj(e,t){if(e){if(typeof e=="string")return sf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sf(e,t)}}function Fj(e){if(typeof Symbol!="undefined"&&Symbol.iterator in Object(e))return Array.from(e)}function zj(e){if(Array.isArray(e))return sf(e)}function sf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Wj=function(t){return t},tO={},rO=function(t){return t===tO},Ym=function(t){return function r(){return arguments.length===0||arguments.length===1&&rO(arguments.length<=0?void 0:arguments[0])?r:t.apply(void 0,arguments)}},Uj=function e(t,r){return t===1?r:Ym(function(){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];var o=i.filter(function(u){return u!==tO}).length;return o>=t?r.apply(void 0,i):e(t-o,Ym(function(){for(var u=arguments.length,c=new Array(u),s=0;s<u;s++)c[s]=arguments[s];var f=i.map(function(l){return rO(l)?c.shift():l});return r.apply(void 0,qj(f).concat(c))}))})},mo=function(t){return Uj(t.length,t)},lf=function(t,r){for(var n=[],i=t;i<r;++i)n[i-t]=i;return n},Kj=mo(function(e,t){return Array.isArray(t)?t.map(e):Object.keys(t).map(function(r){return t[r]}).map(e)}),Hj=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];if(!r.length)return Wj;var i=r.reverse(),a=i[0],o=i.slice(1);return function(){return o.reduce(function(u,c){return c(u)},a.apply(void 0,arguments))}},ff=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},nO=function(t){var r=null,n=null;return function(){for(var i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return r&&a.every(function(u,c){return u===r[c]})||(r=a,n=t.apply(void 0,a)),n}};function Gj(e){var t;return e===0?t=1:t=Math.floor(new ie(e).abs().log(10).toNumber())+1,t}function Vj(e,t,r){for(var n=new ie(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}var Xj=mo(function(e,t,r){var n=+e,i=+t;return n+r*(i-n)}),Yj=mo(function(e,t,r){var n=t-+e;return n=n||1/0,(r-e)/n}),Zj=mo(function(e,t,r){var n=t-+e;return n=n||1/0,Math.max(0,Math.min(1,(r-e)/n))});const go={rangeStep:Vj,getDigitCount:Gj,interpolateNumber:Xj,uninterpolateNumber:Yj,uninterpolateTruncation:Zj};function pf(e){return e$(e)||Qj(e)||iO(e)||Jj()}function Jj(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Qj(e){if(typeof Symbol!="undefined"&&Symbol.iterator in Object(e))return Array.from(e)}function e$(e){if(Array.isArray(e))return hf(e)}function Jn(e,t){return n$(e)||r$(e,t)||iO(e,t)||t$()}function t$(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function iO(e,t){if(e){if(typeof e=="string")return hf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hf(e,t)}}function hf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function r$(e,t){if(!(typeof Symbol=="undefined"||!(Symbol.iterator in Object(e)))){var r=[],n=!0,i=!1,a=void 0;try{for(var o=e[Symbol.iterator](),u;!(n=(u=o.next()).done)&&(r.push(u.value),!(t&&r.length===t));n=!0);}catch(c){i=!0,a=c}finally{try{!n&&o.return!=null&&o.return()}finally{if(i)throw a}}return r}}function n$(e){if(Array.isArray(e))return e}function aO(e){var t=Jn(e,2),r=t[0],n=t[1],i=r,a=n;return r>n&&(i=n,a=r),[i,a]}function oO(e,t,r){if(e.lte(0))return new ie(0);var n=go.getDigitCount(e.toNumber()),i=new ie(10).pow(n),a=e.div(i),o=n!==1?.05:.1,u=new ie(Math.ceil(a.div(o).toNumber())).add(r).mul(o),c=u.mul(i);return t?c:new ie(Math.ceil(c))}function i$(e,t,r){var n=1,i=new ie(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new ie(10).pow(go.getDigitCount(e)-1),i=new ie(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new ie(Math.floor(e)))}else e===0?i=new ie(Math.floor((t-1)/2)):r||(i=new ie(Math.floor(e)));var o=Math.floor((t-1)/2),u=Hj(Kj(function(c){return i.add(new ie(c-o).mul(n)).toNumber()}),lf);return u(0,t)}function uO(e,t,r,n){var i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new ie(0),tickMin:new ie(0),tickMax:new ie(0)};var a=oO(new ie(t).sub(e).div(r-1),n,i),o;e<=0&&t>=0?o=new ie(0):(o=new ie(e).add(t).div(2),o=o.sub(new ie(o).mod(a)));var u=Math.ceil(o.sub(e).div(a).toNumber()),c=Math.ceil(new ie(t).sub(o).div(a).toNumber()),s=u+c+1;return s>r?uO(e,t,r,n,i+1):(s<r&&(c=t>0?c+(r-s):c,u=t>0?u:u+(r-s)),{step:a,tickMin:o.sub(new ie(u).mul(a)),tickMax:o.add(new ie(c).mul(a))})}function a$(e){var t=Jn(e,2),r=t[0],n=t[1],i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:6,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=Math.max(i,2),u=aO([r,n]),c=Jn(u,2),s=c[0],f=c[1];if(s===-1/0||f===1/0){var l=f===1/0?[s].concat(pf(lf(0,i-1).map(function(){return 1/0}))):[].concat(pf(lf(0,i-1).map(function(){return-1/0})),[f]);return r>n?ff(l):l}if(s===f)return i$(s,i,a);var p=uO(s,f,o,a),h=p.step,v=p.tickMin,d=p.tickMax,y=go.rangeStep(v,d.add(new ie(.1).mul(h)),h);return r>n?ff(y):y}function o$(e,t){var r=Jn(e,2),n=r[0],i=r[1],a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=aO([n,i]),u=Jn(o,2),c=u[0],s=u[1];if(c===-1/0||s===1/0)return[n,i];if(c===s)return[c];var f=Math.max(t,2),l=oO(new ie(s).sub(c).div(f-1),a,0),p=[].concat(pf(go.rangeStep(new ie(c),new ie(s).sub(new ie(.99).mul(l)),l)),[s]);return n>i?ff(p):p}var u$=nO(a$),c$=nO(o$),s$=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function Fr(e){"@babel/helpers - typeof";return Fr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fr(e)}function ya(){return ya=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ya.apply(this,arguments)}function l$(e,t){return d$(e)||h$(e,t)||p$(e,t)||f$()}function f$(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function p$(e,t){if(e){if(typeof e=="string")return Zm(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Zm(e,t)}}function Zm(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function h$(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function d$(e){if(Array.isArray(e))return e}function v$(e,t){if(e==null)return{};var r=y$(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function y$(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function m$(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function g$(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,lO(n.key),n)}}function b$(e,t,r){return t&&g$(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function x$(e,t,r){return t=ma(t),O$(e,cO()?Reflect.construct(t,r||[],ma(e).constructor):t.apply(e,r))}function O$(e,t){if(t&&(Fr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return w$(e)}function w$(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function cO(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(cO=function(){return!!e})()}function ma(e){return ma=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ma(e)}function _$(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&df(e,t)}function df(e,t){return df=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},df(e,t)}function sO(e,t,r){return t=lO(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function lO(e){var t=A$(e,"string");return Fr(t)=="symbol"?t:t+""}function A$(e,t){if(Fr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Fr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var yn=function(e){function t(){return m$(this,t),x$(this,t,arguments)}return _$(t,e),b$(t,[{key:"render",value:function(){var n=this.props,i=n.offset,a=n.layout,o=n.width,u=n.dataKey,c=n.data,s=n.dataPointFormatter,f=n.xAxis,l=n.yAxis,p=v$(n,s$),h=U(p,!1);this.props.direction==="x"&&f.type!=="number"&&sr();var v=c.map(function(d){var y=s(d,u),b=y.x,g=y.y,x=y.value,w=y.errorVal;if(!w)return null;var m=[],O,_;if(Array.isArray(w)){var S=l$(w,2);O=S[0],_=S[1]}else O=_=w;if(a==="vertical"){var T=f.scale,j=g+i,P=j+o,E=j-o,$=T(x-O),I=T(x+_);m.push({x1:I,y1:P,x2:I,y2:E}),m.push({x1:$,y1:j,x2:I,y2:j}),m.push({x1:$,y1:P,x2:$,y2:E})}else if(a==="horizontal"){var M=l.scale,k=b+i,R=k-o,L=k+o,B=M(x-O),K=M(x+_);m.push({x1:R,y1:K,x2:L,y2:K}),m.push({x1:k,y1:B,x2:k,y2:K}),m.push({x1:R,y1:B,x2:L,y2:B})}return A.createElement(J,ya({className:"recharts-errorBar",key:"bar-".concat(m.map(function(X){return"".concat(X.x1,"-").concat(X.x2,"-").concat(X.y1,"-").concat(X.y2)}))},h),m.map(function(X){return A.createElement("line",ya({},X,{key:"line-".concat(X.x1,"-").concat(X.x2,"-").concat(X.y1,"-").concat(X.y2)}))}))});return A.createElement(J,{className:"recharts-errorBars"},v)}}])}(A.Component);sO(yn,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"});sO(yn,"displayName","ErrorBar");function Qn(e){"@babel/helpers - typeof";return Qn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qn(e)}function Jm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Yt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Jm(Object(r),!0).forEach(function(n){S$(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Jm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function S$(e,t,r){return t=P$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function P$(e){var t=T$(e,"string");return Qn(t)=="symbol"?t:t+""}function T$(e,t){if(Qn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Qn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var fO=function(t){var r=t.children,n=t.formattedGraphicalItems,i=t.legendWidth,a=t.legendContent,o=We(r,$r);if(!o)return null;var u=$r.defaultProps,c=u!==void 0?Yt(Yt({},u),o.props):{},s;return o.props&&o.props.payload?s=o.props&&o.props.payload:a==="children"?s=(n||[]).reduce(function(f,l){var p=l.item,h=l.props,v=h.sectors||h.data||[];return f.concat(v.map(function(d){return{type:o.props.iconType||p.props.legendType,value:d.name,color:d.fill,payload:d}}))},[]):s=(n||[]).map(function(f){var l=f.item,p=l.type.defaultProps,h=p!==void 0?Yt(Yt({},p),l.props):{},v=h.dataKey,d=h.name,y=h.legendType,b=h.hide;return{inactive:b,dataKey:v,type:c.iconType||y||"square",color:Zp(l),value:d||v,payload:h}}),Yt(Yt(Yt({},c),$r.getWithHeight(o,i)),{},{payload:s,item:o})};function ei(e){"@babel/helpers - typeof";return ei=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ei(e)}function Qm(e){return M$(e)||$$(e)||j$(e)||E$()}function E$(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function j$(e,t){if(e){if(typeof e=="string")return vf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return vf(e,t)}}function $$(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function M$(e){if(Array.isArray(e))return vf(e)}function vf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function eg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ye(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?eg(Object(r),!0).forEach(function(n){Ir(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Ir(e,t,r){return t=I$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function I$(e){var t=C$(e,"string");return ei(t)=="symbol"?t:t+""}function C$(e,t){if(ei(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ei(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function pe(e,t,r){return G(e)||G(t)?r:_e(t)?Ke(e,t,r):H(t)?t(e):r}function In(e,t,r,n){var i=Ij(e,function(u){return pe(u,t)});if(r==="number"){var a=i.filter(function(u){return N(u)||parseFloat(u)});return a.length?[yo(a),kt(a)]:[1/0,-1/0]}var o=n?i.filter(function(u){return!G(u)}):i;return o.map(function(u){return _e(u)||u instanceof Date?u:""})}var k$=function(t){var r,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],i=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0,o=-1,u=(r=n==null?void 0:n.length)!==null&&r!==void 0?r:0;if(u<=1)return 0;if(a&&a.axisType==="angleAxis"&&Math.abs(Math.abs(a.range[1]-a.range[0])-360)<=1e-6)for(var c=a.range,s=0;s<u;s++){var f=s>0?i[s-1].coordinate:i[u-1].coordinate,l=i[s].coordinate,p=s>=u-1?i[0].coordinate:i[s+1].coordinate,h=void 0;if(Ie(l-f)!==Ie(p-l)){var v=[];if(Ie(p-l)===Ie(c[1]-c[0])){h=p;var d=l+c[1]-c[0];v[0]=Math.min(d,(d+f)/2),v[1]=Math.max(d,(d+f)/2)}else{h=f;var y=p+c[1]-c[0];v[0]=Math.min(l,(y+l)/2),v[1]=Math.max(l,(y+l)/2)}var b=[Math.min(l,(h+l)/2),Math.max(l,(h+l)/2)];if(t>b[0]&&t<=b[1]||t>=v[0]&&t<=v[1]){o=i[s].index;break}}else{var g=Math.min(f,p),x=Math.max(f,p);if(t>(g+l)/2&&t<=(x+l)/2){o=i[s].index;break}}}else for(var w=0;w<u;w++)if(w===0&&t<=(n[w].coordinate+n[w+1].coordinate)/2||w>0&&w<u-1&&t>(n[w].coordinate+n[w-1].coordinate)/2&&t<=(n[w].coordinate+n[w+1].coordinate)/2||w===u-1&&t>(n[w].coordinate+n[w-1].coordinate)/2){o=n[w].index;break}return o},Zp=function(t){var r,n=t,i=n.type.displayName,a=(r=t.type)!==null&&r!==void 0&&r.defaultProps?ye(ye({},t.type.defaultProps),t.props):t.props,o=a.stroke,u=a.fill,c;switch(i){case"Line":c=o;break;case"Area":case"Radar":c=o&&o!=="none"?o:u;break;default:c=u;break}return c},R$=function(t){var r=t.barSize,n=t.totalSize,i=t.stackGroups,a=i===void 0?{}:i;if(!a)return{};for(var o={},u=Object.keys(a),c=0,s=u.length;c<s;c++)for(var f=a[u[c]].stackGroups,l=Object.keys(f),p=0,h=l.length;p<h;p++){var v=f[l[p]],d=v.items,y=v.cateAxisId,b=d.filter(function(_){return Ot(_.type).indexOf("Bar")>=0});if(b&&b.length){var g=b[0].type.defaultProps,x=g!==void 0?ye(ye({},g),b[0].props):b[0].props,w=x.barSize,m=x[y];o[m]||(o[m]=[]);var O=G(w)?r:w;o[m].push({item:b[0],stackList:b.slice(1),barSize:G(O)?void 0:Ce(O,n,0)})}}return o},D$=function(t){var r=t.barGap,n=t.barCategoryGap,i=t.bandSize,a=t.sizeList,o=a===void 0?[]:a,u=t.maxBarSize,c=o.length;if(c<1)return null;var s=Ce(r,i,0,!0),f,l=[];if(o[0].barSize===+o[0].barSize){var p=!1,h=i/c,v=o.reduce(function(w,m){return w+m.barSize||0},0);v+=(c-1)*s,v>=i&&(v-=(c-1)*s,s=0),v>=i&&h>0&&(p=!0,h*=.9,v=c*h);var d=(i-v)/2>>0,y={offset:d-s,size:0};f=o.reduce(function(w,m){var O={item:m.item,position:{offset:y.offset+y.size+s,size:p?h:m.barSize}},_=[].concat(Qm(w),[O]);return y=_[_.length-1].position,m.stackList&&m.stackList.length&&m.stackList.forEach(function(S){_.push({item:S,position:y})}),_},l)}else{var b=Ce(n,i,0,!0);i-2*b-(c-1)*s<=0&&(s=0);var g=(i-2*b-(c-1)*s)/c;g>1&&(g>>=0);var x=u===+u?Math.min(g,u):g;f=o.reduce(function(w,m,O){var _=[].concat(Qm(w),[{item:m.item,position:{offset:b+(g+s)*O+(g-x)/2,size:x}}]);return m.stackList&&m.stackList.length&&m.stackList.forEach(function(S){_.push({item:S,position:_[_.length-1].position})}),_},l)}return f},N$=function(t,r,n,i){var a=n.children,o=n.width,u=n.margin,c=o-(u.left||0)-(u.right||0),s=fO({children:a,legendWidth:c});if(s){var f=i||{},l=f.width,p=f.height,h=s.align,v=s.verticalAlign,d=s.layout;if((d==="vertical"||d==="horizontal"&&v==="middle")&&h!=="center"&&N(t[h]))return ye(ye({},t),{},Ir({},h,t[h]+(l||0)));if((d==="horizontal"||d==="vertical"&&h==="center")&&v!=="middle"&&N(t[v]))return ye(ye({},t),{},Ir({},v,t[v]+(p||0)))}return t},q$=function(t,r,n){return G(r)?!0:t==="horizontal"?r==="yAxis":t==="vertical"||n==="x"?r==="xAxis":n==="y"?r==="yAxis":!0},pO=function(t,r,n,i,a){var o=r.props.children,u=Re(o,yn).filter(function(s){return q$(i,a,s.props.direction)});if(u&&u.length){var c=u.map(function(s){return s.props.dataKey});return t.reduce(function(s,f){var l=pe(f,n);if(G(l))return s;var p=Array.isArray(l)?[yo(l),kt(l)]:[l,l],h=c.reduce(function(v,d){var y=pe(f,d,0),b=p[0]-Math.abs(Array.isArray(y)?y[0]:y),g=p[1]+Math.abs(Array.isArray(y)?y[1]:y);return[Math.min(b,v[0]),Math.max(g,v[1])]},[1/0,-1/0]);return[Math.min(h[0],s[0]),Math.max(h[1],s[1])]},[1/0,-1/0])}return null},L$=function(t,r,n,i,a){var o=r.map(function(u){return pO(t,u,n,a,i)}).filter(function(u){return!G(u)});return o&&o.length?o.reduce(function(u,c){return[Math.min(u[0],c[0]),Math.max(u[1],c[1])]},[1/0,-1/0]):null},hO=function(t,r,n,i,a){var o=r.map(function(c){var s=c.props.dataKey;return n==="number"&&s&&pO(t,c,s,i)||In(t,s,n,a)});if(n==="number")return o.reduce(function(c,s){return[Math.min(c[0],s[0]),Math.max(c[1],s[1])]},[1/0,-1/0]);var u={};return o.reduce(function(c,s){for(var f=0,l=s.length;f<l;f++)u[s[f]]||(u[s[f]]=!0,c.push(s[f]));return c},[])},dO=function(t,r){return t==="horizontal"&&r==="xAxis"||t==="vertical"&&r==="yAxis"||t==="centric"&&r==="angleAxis"||t==="radial"&&r==="radiusAxis"},vO=function(t,r,n,i){if(i)return t.map(function(c){return c.coordinate});var a,o,u=t.map(function(c){return c.coordinate===r&&(a=!0),c.coordinate===n&&(o=!0),c.coordinate});return a||u.push(r),o||u.push(n),u},xt=function(t,r,n){if(!t)return null;var i=t.scale,a=t.duplicateDomain,o=t.type,u=t.range,c=t.realScaleType==="scaleBand"?i.bandwidth()/2:2,s=(r||n)&&o==="category"&&i.bandwidth?i.bandwidth()/c:0;if(s=t.axisType==="angleAxis"&&(u==null?void 0:u.length)>=2?Ie(u[0]-u[1])*2*s:s,r&&(t.ticks||t.niceTicks)){var f=(t.ticks||t.niceTicks).map(function(l){var p=a?a.indexOf(l):l;return{coordinate:i(p)+s,value:l,offset:s}});return f.filter(function(l){return!fn(l.coordinate)})}return t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(l,p){return{coordinate:i(l)+s,value:l,index:p,offset:s}}):i.ticks&&!n?i.ticks(t.tickCount).map(function(l){return{coordinate:i(l)+s,value:l,offset:s}}):i.domain().map(function(l,p){return{coordinate:i(l)+s,value:a?a[l]:l,index:p,offset:s}})},ol=new WeakMap,Bi=function(t,r){if(typeof r!="function")return t;ol.has(t)||ol.set(t,new WeakMap);var n=ol.get(t);if(n.has(r))return n.get(r);var i=function(){t.apply(void 0,arguments),r.apply(void 0,arguments)};return n.set(r,i),i},yO=function(t,r,n){var i=t.scale,a=t.type,o=t.layout,u=t.axisType;if(i==="auto")return o==="radial"&&u==="radiusAxis"?{scale:Hn(),realScaleType:"band"}:o==="radial"&&u==="angleAxis"?{scale:fa(),realScaleType:"linear"}:a==="category"&&r&&(r.indexOf("LineChart")>=0||r.indexOf("AreaChart")>=0||r.indexOf("ComposedChart")>=0&&!n)?{scale:Mn(),realScaleType:"point"}:a==="category"?{scale:Hn(),realScaleType:"band"}:{scale:fa(),realScaleType:"linear"};if(Oi(i)){var c="scale".concat(Qa(i));return{scale:(Lm[c]||Mn)(),realScaleType:Lm[c]?c:"point"}}return H(i)?{scale:i}:{scale:Mn(),realScaleType:"point"}},tg=1e-4,mO=function(t){var r=t.domain();if(!(!r||r.length<=2)){var n=r.length,i=t.range(),a=Math.min(i[0],i[1])-tg,o=Math.max(i[0],i[1])+tg,u=t(r[0]),c=t(r[n-1]);(u<a||u>o||c<a||c>o)&&t.domain([r[0],r[n-1]])}},B$=function(t,r){if(!t)return null;for(var n=0,i=t.length;n<i;n++)if(t[n].item===r)return t[n].position;return null},F$=function(t,r){if(!r||r.length!==2||!N(r[0])||!N(r[1]))return t;var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]),a=[t[0],t[1]];return(!N(t[0])||t[0]<n)&&(a[0]=n),(!N(t[1])||t[1]>i)&&(a[1]=i),a[0]>i&&(a[0]=i),a[1]<n&&(a[1]=n),a},z$=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0,u=0;u<r;++u){var c=fn(t[u][n][1])?t[u][n][0]:t[u][n][1];c>=0?(t[u][n][0]=a,t[u][n][1]=a+c,a=t[u][n][1]):(t[u][n][0]=o,t[u][n][1]=o+c,o=t[u][n][1])}},W$=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0;o<r;++o){var u=fn(t[o][n][1])?t[o][n][0]:t[o][n][1];u>=0?(t[o][n][0]=a,t[o][n][1]=a+u,a=t[o][n][1]):(t[o][n][0]=0,t[o][n][1]=0)}},U$={sign:z$,expand:IA,none:Cr,silhouette:CA,wiggle:kA,positive:W$},K$=function(t,r,n){var i=r.map(function(u){return u.props.dataKey}),a=U$[n],o=MA().keys(i).value(function(u,c){return+pe(u,c,0)}).order(Ul).offset(a);return o(t)},H$=function(t,r,n,i,a,o){if(!t)return null;var u=o?r.reverse():r,c={},s=u.reduce(function(l,p){var h,v=(h=p.type)!==null&&h!==void 0&&h.defaultProps?ye(ye({},p.type.defaultProps),p.props):p.props,d=v.stackId,y=v.hide;if(y)return l;var b=v[n],g=l[b]||{hasStack:!1,stackGroups:{}};if(_e(d)){var x=g.stackGroups[d]||{numericAxisId:n,cateAxisId:i,items:[]};x.items.push(p),g.hasStack=!0,g.stackGroups[d]=x}else g.stackGroups[zt("_stackId_")]={numericAxisId:n,cateAxisId:i,items:[p]};return ye(ye({},l),{},Ir({},b,g))},c),f={};return Object.keys(s).reduce(function(l,p){var h=s[p];if(h.hasStack){var v={};h.stackGroups=Object.keys(h.stackGroups).reduce(function(d,y){var b=h.stackGroups[y];return ye(ye({},d),{},Ir({},y,{numericAxisId:n,cateAxisId:i,items:b.items,stackedData:K$(t,b.items,a)}))},v)}return ye(ye({},l),{},Ir({},p,h))},f)},gO=function(t,r){var n=r.realScaleType,i=r.type,a=r.tickCount,o=r.originalDomain,u=r.allowDecimals,c=n||r.scale;if(c!=="auto"&&c!=="linear")return null;if(a&&i==="number"&&o&&(o[0]==="auto"||o[1]==="auto")){var s=t.domain();if(!s.length)return null;var f=u$(s,a,u);return t.domain([yo(f),kt(f)]),{niceTicks:f}}if(a&&i==="number"){var l=t.domain(),p=c$(l,a,u);return{niceTicks:p}}return null};function zr(e){var t=e.axis,r=e.ticks,n=e.bandSize,i=e.entry,a=e.index,o=e.dataKey;if(t.type==="category"){if(!t.allowDuplicatedCategory&&t.dataKey&&!G(i[t.dataKey])){var u=Gi(r,"value",i[t.dataKey]);if(u)return u.coordinate+n/2}return r[a]?r[a].coordinate+n/2:null}var c=pe(i,G(o)?t.dataKey:o);return G(c)?null:t.scale(c)}var rg=function(t){var r=t.axis,n=t.ticks,i=t.offset,a=t.bandSize,o=t.entry,u=t.index;if(r.type==="category")return n[u]?n[u].coordinate+i:null;var c=pe(o,r.dataKey,r.domain[u]);return G(c)?null:r.scale(c)-a/2+i},G$=function(t){var r=t.numericAxis,n=r.scale.domain();if(r.type==="number"){var i=Math.min(n[0],n[1]),a=Math.max(n[0],n[1]);return i<=0&&a>=0?0:a<0?a:i}return n[0]},V$=function(t,r){var n,i=(n=t.type)!==null&&n!==void 0&&n.defaultProps?ye(ye({},t.type.defaultProps),t.props):t.props,a=i.stackId;if(_e(a)){var o=r[a];if(o){var u=o.items.indexOf(t);return u>=0?o.stackedData[u]:null}}return null},X$=function(t){return t.reduce(function(r,n){return[yo(n.concat([r[0]]).filter(N)),kt(n.concat([r[1]]).filter(N))]},[1/0,-1/0])},bO=function(t,r,n){return Object.keys(t).reduce(function(i,a){var o=t[a],u=o.stackedData,c=u.reduce(function(s,f){var l=X$(f.slice(r,n+1));return[Math.min(s[0],l[0]),Math.max(s[1],l[1])]},[1/0,-1/0]);return[Math.min(c[0],i[0]),Math.max(c[1],i[1])]},[1/0,-1/0]).map(function(i){return i===1/0||i===-1/0?0:i})},ng=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,ig=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,yf=function(t,r,n){if(H(t))return t(r,n);if(!Array.isArray(t))return r;var i=[];if(N(t[0]))i[0]=n?t[0]:Math.min(t[0],r[0]);else if(ng.test(t[0])){var a=+ng.exec(t[0])[1];i[0]=r[0]-a}else H(t[0])?i[0]=t[0](r[0]):i[0]=r[0];if(N(t[1]))i[1]=n?t[1]:Math.max(t[1],r[1]);else if(ig.test(t[1])){var o=+ig.exec(t[1])[1];i[1]=r[1]+o}else H(t[1])?i[1]=t[1](r[1]):i[1]=r[1];return i},ga=function(t,r,n){if(t&&t.scale&&t.scale.bandwidth){var i=t.scale.bandwidth();if(!n||i>0)return i}if(t&&r&&r.length>=2){for(var a=Sp(r,function(l){return l.coordinate}),o=1/0,u=1,c=a.length;u<c;u++){var s=a[u],f=a[u-1];o=Math.min((s.coordinate||0)-(f.coordinate||0),o)}return o===1/0?0:o}return n?void 0:0},ag=function(t,r,n){return!t||!t.length||Bt(t,Ke(n,"type.defaultProps.domain"))?r:t},xO=function(t,r){var n=t.type.defaultProps?ye(ye({},t.type.defaultProps),t.props):t.props,i=n.dataKey,a=n.name,o=n.unit,u=n.formatter,c=n.tooltipType,s=n.chartType,f=n.hide;return ye(ye({},U(t,!1)),{},{dataKey:i,unit:o,formatter:u,name:a||i,color:Zp(t),value:pe(r,i),type:c,payload:r,chartType:s,hide:f})};function ti(e){"@babel/helpers - typeof";return ti=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ti(e)}function og(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function mt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?og(Object(r),!0).forEach(function(n){OO(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):og(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function OO(e,t,r){return t=Y$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Y$(e){var t=Z$(e,"string");return ti(t)=="symbol"?t:t+""}function Z$(e,t){if(ti(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ti(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function J$(e,t){return rM(e)||tM(e,t)||eM(e,t)||Q$()}function Q$(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function eM(e,t){if(e){if(typeof e=="string")return ug(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ug(e,t)}}function ug(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function tM(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function rM(e){if(Array.isArray(e))return e}var ba=Math.PI/180,nM=function(t){return t*180/Math.PI},se=function(t,r,n,i){return{x:t+Math.cos(-ba*i)*n,y:r+Math.sin(-ba*i)*n}},wO=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(n.left||0)-(n.right||0)),Math.abs(r-(n.top||0)-(n.bottom||0)))/2},iM=function(t,r,n,i,a){var o=t.width,u=t.height,c=t.startAngle,s=t.endAngle,f=Ce(t.cx,o,o/2),l=Ce(t.cy,u,u/2),p=wO(o,u,n),h=Ce(t.innerRadius,p,0),v=Ce(t.outerRadius,p,p*.8),d=Object.keys(r);return d.reduce(function(y,b){var g=r[b],x=g.domain,w=g.reversed,m;if(G(g.range))i==="angleAxis"?m=[c,s]:i==="radiusAxis"&&(m=[h,v]),w&&(m=[m[1],m[0]]);else{m=g.range;var O=m,_=J$(O,2);c=_[0],s=_[1]}var S=yO(g,a),T=S.realScaleType,j=S.scale;j.domain(x).range(m),mO(j);var P=gO(j,mt(mt({},g),{},{realScaleType:T})),E=mt(mt(mt({},g),P),{},{range:m,radius:v,realScaleType:T,scale:j,cx:f,cy:l,innerRadius:h,outerRadius:v,startAngle:c,endAngle:s});return mt(mt({},y),{},OO({},b,E))},{})},aM=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return Math.sqrt(Math.pow(n-a,2)+Math.pow(i-o,2))},oM=function(t,r){var n=t.x,i=t.y,a=r.cx,o=r.cy,u=aM({x:n,y:i},{x:a,y:o});if(u<=0)return{radius:u};var c=(n-a)/u,s=Math.acos(c);return i>o&&(s=2*Math.PI-s),{radius:u,angle:nM(s),angleInRadian:s}},uM=function(t){var r=t.startAngle,n=t.endAngle,i=Math.floor(r/360),a=Math.floor(n/360),o=Math.min(i,a);return{startAngle:r-o*360,endAngle:n-o*360}},cM=function(t,r){var n=r.startAngle,i=r.endAngle,a=Math.floor(n/360),o=Math.floor(i/360),u=Math.min(a,o);return t+u*360},cg=function(t,r){var n=t.x,i=t.y,a=oM({x:n,y:i},r),o=a.radius,u=a.angle,c=r.innerRadius,s=r.outerRadius;if(o<c||o>s)return!1;if(o===0)return!0;var f=uM(r),l=f.startAngle,p=f.endAngle,h=u,v;if(l<=p){for(;h>p;)h-=360;for(;h<l;)h+=360;v=h>=l&&h<=p}else{for(;h>l;)h-=360;for(;h<p;)h+=360;v=h>=p&&h<=l}return v?mt(mt({},r),{},{radius:o,angle:cM(h,r)}):null},_O=function(t){return!q.isValidElement(t)&&!H(t)&&typeof t!="boolean"?t.className:""};function ri(e){"@babel/helpers - typeof";return ri=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ri(e)}var sM=["offset"];function lM(e){return dM(e)||hM(e)||pM(e)||fM()}function fM(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function pM(e,t){if(e){if(typeof e=="string")return mf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return mf(e,t)}}function hM(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function dM(e){if(Array.isArray(e))return mf(e)}function mf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function vM(e,t){if(e==null)return{};var r=yM(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function yM(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function sg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function we(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?sg(Object(r),!0).forEach(function(n){mM(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function mM(e,t,r){return t=gM(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function gM(e){var t=bM(e,"string");return ri(t)=="symbol"?t:t+""}function bM(e,t){if(ri(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ri(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function ni(){return ni=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ni.apply(this,arguments)}var xM=function(t){var r=t.value,n=t.formatter,i=G(t.children)?r:t.children;return H(n)?n(i):i},OM=function(t,r){var n=Ie(r-t),i=Math.min(Math.abs(r-t),360);return n*i},wM=function(t,r,n){var i=t.position,a=t.viewBox,o=t.offset,u=t.className,c=a,s=c.cx,f=c.cy,l=c.innerRadius,p=c.outerRadius,h=c.startAngle,v=c.endAngle,d=c.clockWise,y=(l+p)/2,b=OM(h,v),g=b>=0?1:-1,x,w;i==="insideStart"?(x=h+g*o,w=d):i==="insideEnd"?(x=v-g*o,w=!d):i==="end"&&(x=v+g*o,w=d),w=b<=0?w:!w;var m=se(s,f,y,x),O=se(s,f,y,x+(w?1:-1)*359),_="M".concat(m.x,",").concat(m.y,`
    A`).concat(y,",").concat(y,",0,1,").concat(w?0:1,`,
    `).concat(O.x,",").concat(O.y),S=G(t.id)?zt("recharts-radial-line-"):t.id;return A.createElement("text",ni({},n,{dominantBaseline:"central",className:Z("recharts-radial-bar-label",u)}),A.createElement("defs",null,A.createElement("path",{id:S,d:_})),A.createElement("textPath",{xlinkHref:"#".concat(S)},r))},_M=function(t){var r=t.viewBox,n=t.offset,i=t.position,a=r,o=a.cx,u=a.cy,c=a.innerRadius,s=a.outerRadius,f=a.startAngle,l=a.endAngle,p=(f+l)/2;if(i==="outside"){var h=se(o,u,s+n,p),v=h.x,d=h.y;return{x:v,y:d,textAnchor:v>=o?"start":"end",verticalAnchor:"middle"}}if(i==="center")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"middle"};if(i==="centerTop")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"start"};if(i==="centerBottom")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"end"};var y=(c+s)/2,b=se(o,u,y,p),g=b.x,x=b.y;return{x:g,y:x,textAnchor:"middle",verticalAnchor:"middle"}},AM=function(t){var r=t.viewBox,n=t.parentViewBox,i=t.offset,a=t.position,o=r,u=o.x,c=o.y,s=o.width,f=o.height,l=f>=0?1:-1,p=l*i,h=l>0?"end":"start",v=l>0?"start":"end",d=s>=0?1:-1,y=d*i,b=d>0?"end":"start",g=d>0?"start":"end";if(a==="top"){var x={x:u+s/2,y:c-l*i,textAnchor:"middle",verticalAnchor:h};return we(we({},x),n?{height:Math.max(c-n.y,0),width:s}:{})}if(a==="bottom"){var w={x:u+s/2,y:c+f+p,textAnchor:"middle",verticalAnchor:v};return we(we({},w),n?{height:Math.max(n.y+n.height-(c+f),0),width:s}:{})}if(a==="left"){var m={x:u-y,y:c+f/2,textAnchor:b,verticalAnchor:"middle"};return we(we({},m),n?{width:Math.max(m.x-n.x,0),height:f}:{})}if(a==="right"){var O={x:u+s+y,y:c+f/2,textAnchor:g,verticalAnchor:"middle"};return we(we({},O),n?{width:Math.max(n.x+n.width-O.x,0),height:f}:{})}var _=n?{width:s,height:f}:{};return a==="insideLeft"?we({x:u+y,y:c+f/2,textAnchor:g,verticalAnchor:"middle"},_):a==="insideRight"?we({x:u+s-y,y:c+f/2,textAnchor:b,verticalAnchor:"middle"},_):a==="insideTop"?we({x:u+s/2,y:c+p,textAnchor:"middle",verticalAnchor:v},_):a==="insideBottom"?we({x:u+s/2,y:c+f-p,textAnchor:"middle",verticalAnchor:h},_):a==="insideTopLeft"?we({x:u+y,y:c+p,textAnchor:g,verticalAnchor:v},_):a==="insideTopRight"?we({x:u+s-y,y:c+p,textAnchor:b,verticalAnchor:v},_):a==="insideBottomLeft"?we({x:u+y,y:c+f-p,textAnchor:g,verticalAnchor:h},_):a==="insideBottomRight"?we({x:u+s-y,y:c+f-p,textAnchor:b,verticalAnchor:h},_):ln(a)&&(N(a.x)||rr(a.x))&&(N(a.y)||rr(a.y))?we({x:u+Ce(a.x,s),y:c+Ce(a.y,f),textAnchor:"end",verticalAnchor:"end"},_):we({x:u+s/2,y:c+f/2,textAnchor:"middle",verticalAnchor:"middle"},_)},SM=function(t){return"cx"in t&&N(t.cx)};function Pe(e){var t=e.offset,r=t===void 0?5:t,n=vM(e,sM),i=we({offset:r},n),a=i.viewBox,o=i.position,u=i.value,c=i.children,s=i.content,f=i.className,l=f===void 0?"":f,p=i.textBreakAll;if(!a||G(u)&&G(c)&&!q.isValidElement(s)&&!H(s))return null;if(q.isValidElement(s))return q.cloneElement(s,i);var h;if(H(s)){if(h=q.createElement(s,i),q.isValidElement(h))return h}else h=xM(i);var v=SM(a),d=U(i,!0);if(v&&(o==="insideStart"||o==="insideEnd"||o==="end"))return wM(i,h,d);var y=v?_M(i):AM(i);return A.createElement(lr,ni({className:Z("recharts-label",l)},d,y,{breakAll:p}),h)}Pe.displayName="Label";var AO=function(t){var r=t.cx,n=t.cy,i=t.angle,a=t.startAngle,o=t.endAngle,u=t.r,c=t.radius,s=t.innerRadius,f=t.outerRadius,l=t.x,p=t.y,h=t.top,v=t.left,d=t.width,y=t.height,b=t.clockWise,g=t.labelViewBox;if(g)return g;if(N(d)&&N(y)){if(N(l)&&N(p))return{x:l,y:p,width:d,height:y};if(N(h)&&N(v))return{x:h,y:v,width:d,height:y}}return N(l)&&N(p)?{x:l,y:p,width:0,height:0}:N(r)&&N(n)?{cx:r,cy:n,startAngle:a||i||0,endAngle:o||i||0,innerRadius:s||0,outerRadius:f||c||u||0,clockWise:b}:t.viewBox?t.viewBox:{}},PM=function(t,r){return t?t===!0?A.createElement(Pe,{key:"label-implicit",viewBox:r}):_e(t)?A.createElement(Pe,{key:"label-implicit",viewBox:r,value:t}):q.isValidElement(t)?t.type===Pe?q.cloneElement(t,{key:"label-implicit",viewBox:r}):A.createElement(Pe,{key:"label-implicit",content:t,viewBox:r}):H(t)?A.createElement(Pe,{key:"label-implicit",content:t,viewBox:r}):ln(t)?A.createElement(Pe,ni({viewBox:r},t,{key:"label-implicit"})):null:null},TM=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!t||!t.children&&n&&!t.label)return null;var i=t.children,a=AO(t),o=Re(i,Pe).map(function(c,s){return q.cloneElement(c,{viewBox:r||a,key:"label-".concat(s)})});if(!n)return o;var u=PM(t.label,r||a);return[u].concat(lM(o))};Pe.parseViewBox=AO;Pe.renderCallByParent=TM;var ul,lg;function EM(){if(lg)return ul;lg=1;function e(t){var r=t==null?0:t.length;return r?t[r-1]:void 0}return ul=e,ul}var jM=EM();const $M=ae(jM);function ii(e){"@babel/helpers - typeof";return ii=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ii(e)}var MM=["valueAccessor"],IM=["data","dataKey","clockWise","id","textBreakAll"];function CM(e){return NM(e)||DM(e)||RM(e)||kM()}function kM(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function RM(e,t){if(e){if(typeof e=="string")return gf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return gf(e,t)}}function DM(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function NM(e){if(Array.isArray(e))return gf(e)}function gf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function xa(){return xa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},xa.apply(this,arguments)}function fg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function pg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?fg(Object(r),!0).forEach(function(n){qM(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function qM(e,t,r){return t=LM(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function LM(e){var t=BM(e,"string");return ii(t)=="symbol"?t:t+""}function BM(e,t){if(ii(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ii(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function hg(e,t){if(e==null)return{};var r=FM(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function FM(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var zM=function(t){return Array.isArray(t.value)?$M(t.value):t.value};function ot(e){var t=e.valueAccessor,r=t===void 0?zM:t,n=hg(e,MM),i=n.data,a=n.dataKey,o=n.clockWise,u=n.id,c=n.textBreakAll,s=hg(n,IM);return!i||!i.length?null:A.createElement(J,{className:"recharts-label-list"},i.map(function(f,l){var p=G(a)?r(f,l):pe(f&&f.payload,a),h=G(u)?{}:{id:"".concat(u,"-").concat(l)};return A.createElement(Pe,xa({},U(f,!0),s,h,{parentViewBox:f.parentViewBox,value:p,textBreakAll:c,viewBox:Pe.parseViewBox(G(o)?f:pg(pg({},f),{},{clockWise:o})),key:"label-".concat(l),index:l}))}))}ot.displayName="LabelList";function WM(e,t){return e?e===!0?A.createElement(ot,{key:"labelList-implicit",data:t}):A.isValidElement(e)||H(e)?A.createElement(ot,{key:"labelList-implicit",data:t,content:e}):ln(e)?A.createElement(ot,xa({data:t},e,{key:"labelList-implicit"})):null:null}function UM(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!e||!e.children&&r&&!e.label)return null;var n=e.children,i=Re(n,ot).map(function(o,u){return q.cloneElement(o,{data:t,key:"labelList-".concat(u)})});if(!r)return i;var a=WM(e.label,t);return[a].concat(CM(i))}ot.renderCallByParent=UM;function ai(e){"@babel/helpers - typeof";return ai=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ai(e)}function bf(){return bf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},bf.apply(this,arguments)}function dg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function vg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?dg(Object(r),!0).forEach(function(n){KM(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function KM(e,t,r){return t=HM(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function HM(e){var t=GM(e,"string");return ai(t)=="symbol"?t:t+""}function GM(e,t){if(ai(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ai(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var VM=function(t,r){var n=Ie(r-t),i=Math.min(Math.abs(r-t),359.999);return n*i},Fi=function(t){var r=t.cx,n=t.cy,i=t.radius,a=t.angle,o=t.sign,u=t.isExternal,c=t.cornerRadius,s=t.cornerIsExternal,f=c*(u?1:-1)+i,l=Math.asin(c/f)/ba,p=s?a:a+o*l,h=se(r,n,f,p),v=se(r,n,i,p),d=s?a-o*l:a,y=se(r,n,f*Math.cos(l*ba),d);return{center:h,circleTangency:v,lineTangency:y,theta:l}},SO=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.startAngle,u=t.endAngle,c=VM(o,u),s=o+c,f=se(r,n,a,o),l=se(r,n,a,s),p="M ".concat(f.x,",").concat(f.y,`
    A `).concat(a,",").concat(a,`,0,
    `).concat(+(Math.abs(c)>180),",").concat(+(o>s),`,
    `).concat(l.x,",").concat(l.y,`
  `);if(i>0){var h=se(r,n,i,o),v=se(r,n,i,s);p+="L ".concat(v.x,",").concat(v.y,`
            A `).concat(i,",").concat(i,`,0,
            `).concat(+(Math.abs(c)>180),",").concat(+(o<=s),`,
            `).concat(h.x,",").concat(h.y," Z")}else p+="L ".concat(r,",").concat(n," Z");return p},XM=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.cornerRadius,u=t.forceCornerRadius,c=t.cornerIsExternal,s=t.startAngle,f=t.endAngle,l=Ie(f-s),p=Fi({cx:r,cy:n,radius:a,angle:s,sign:l,cornerRadius:o,cornerIsExternal:c}),h=p.circleTangency,v=p.lineTangency,d=p.theta,y=Fi({cx:r,cy:n,radius:a,angle:f,sign:-l,cornerRadius:o,cornerIsExternal:c}),b=y.circleTangency,g=y.lineTangency,x=y.theta,w=c?Math.abs(s-f):Math.abs(s-f)-d-x;if(w<0)return u?"M ".concat(v.x,",").concat(v.y,`
        a`).concat(o,",").concat(o,",0,0,1,").concat(o*2,`,0
        a`).concat(o,",").concat(o,",0,0,1,").concat(-o*2,`,0
      `):SO({cx:r,cy:n,innerRadius:i,outerRadius:a,startAngle:s,endAngle:f});var m="M ".concat(v.x,",").concat(v.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(h.x,",").concat(h.y,`
    A`).concat(a,",").concat(a,",0,").concat(+(w>180),",").concat(+(l<0),",").concat(b.x,",").concat(b.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(g.x,",").concat(g.y,`
  `);if(i>0){var O=Fi({cx:r,cy:n,radius:i,angle:s,sign:l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),_=O.circleTangency,S=O.lineTangency,T=O.theta,j=Fi({cx:r,cy:n,radius:i,angle:f,sign:-l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),P=j.circleTangency,E=j.lineTangency,$=j.theta,I=c?Math.abs(s-f):Math.abs(s-f)-T-$;if(I<0&&o===0)return"".concat(m,"L").concat(r,",").concat(n,"Z");m+="L".concat(E.x,",").concat(E.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(P.x,",").concat(P.y,`
      A`).concat(i,",").concat(i,",0,").concat(+(I>180),",").concat(+(l>0),",").concat(_.x,",").concat(_.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(S.x,",").concat(S.y,"Z")}else m+="L".concat(r,",").concat(n,"Z");return m},YM={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},PO=function(t){var r=vg(vg({},YM),t),n=r.cx,i=r.cy,a=r.innerRadius,o=r.outerRadius,u=r.cornerRadius,c=r.forceCornerRadius,s=r.cornerIsExternal,f=r.startAngle,l=r.endAngle,p=r.className;if(o<a||f===l)return null;var h=Z("recharts-sector",p),v=o-a,d=Ce(u,v,0,!0),y;return d>0&&Math.abs(f-l)<360?y=XM({cx:n,cy:i,innerRadius:a,outerRadius:o,cornerRadius:Math.min(d,v/2),forceCornerRadius:c,cornerIsExternal:s,startAngle:f,endAngle:l}):y=SO({cx:n,cy:i,innerRadius:a,outerRadius:o,startAngle:f,endAngle:l}),A.createElement("path",bf({},U(r,!0),{className:h,d:y,role:"img"}))};function oi(e){"@babel/helpers - typeof";return oi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},oi(e)}function xf(){return xf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},xf.apply(this,arguments)}function yg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function mg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?yg(Object(r),!0).forEach(function(n){ZM(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ZM(e,t,r){return t=JM(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function JM(e){var t=QM(e,"string");return oi(t)=="symbol"?t:t+""}function QM(e,t){if(oi(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(oi(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var gg={curveBasisClosed:xA,curveBasisOpen:OA,curveBasis:bA,curveBumpX:aA,curveBumpY:oA,curveLinearClosed:wA,curveLinear:to,curveMonotoneX:_A,curveMonotoneY:AA,curveNatural:SA,curveStep:PA,curveStepAfter:EA,curveStepBefore:TA},zi=function(t){return t.x===+t.x&&t.y===+t.y},_n=function(t){return t.x},An=function(t){return t.y},eI=function(t,r){if(H(t))return t;var n="curve".concat(Qa(t));return(n==="curveMonotone"||n==="curveBump")&&r?gg["".concat(n).concat(r==="vertical"?"Y":"X")]:gg[n]||to},tI=function(t){var r=t.type,n=r===void 0?"linear":r,i=t.points,a=i===void 0?[]:i,o=t.baseLine,u=t.layout,c=t.connectNulls,s=c===void 0?!1:c,f=eI(n,u),l=s?a.filter(function(d){return zi(d)}):a,p;if(Array.isArray(o)){var h=s?o.filter(function(d){return zi(d)}):o,v=l.map(function(d,y){return mg(mg({},d),{},{base:h[y]})});return u==="vertical"?p=Ci().y(An).x1(_n).x0(function(d){return d.base.x}):p=Ci().x(_n).y1(An).y0(function(d){return d.base.y}),p.defined(zi).curve(f),p(v)}return u==="vertical"&&N(o)?p=Ci().y(An).x1(_n).x0(o):N(o)?p=Ci().x(_n).y1(An).y0(o):p=A0().x(_n).y(An),p.defined(zi).curve(f),p(l)},qt=function(t){var r=t.className,n=t.points,i=t.path,a=t.pathRef;if((!n||!n.length)&&!i)return null;var o=n&&n.length?tI(t):i;return A.createElement("path",xf({},U(t,!1),Vi(t),{className:Z("recharts-curve",r),d:o,ref:a}))},rI=Object.getOwnPropertyNames,nI=Object.getOwnPropertySymbols,iI=Object.prototype.hasOwnProperty;function bg(e,t){return function(n,i,a){return e(n,i,a)&&t(n,i,a)}}function Wi(e){return function(r,n,i){if(!r||!n||typeof r!="object"||typeof n!="object")return e(r,n,i);var a=i.cache,o=a.get(r),u=a.get(n);if(o&&u)return o===n&&u===r;a.set(r,n),a.set(n,r);var c=e(r,n,i);return a.delete(r),a.delete(n),c}}function xg(e){return rI(e).concat(nI(e))}var aI=Object.hasOwn||function(e,t){return iI.call(e,t)};function yr(e,t){return e===t||!e&&!t&&e!==e&&t!==t}var oI="__v",uI="__o",cI="_owner",Og=Object.getOwnPropertyDescriptor,wg=Object.keys;function sI(e,t,r){var n=e.length;if(t.length!==n)return!1;for(;n-- >0;)if(!r.equals(e[n],t[n],n,n,e,t,r))return!1;return!0}function lI(e,t){return yr(e.getTime(),t.getTime())}function fI(e,t){return e.name===t.name&&e.message===t.message&&e.cause===t.cause&&e.stack===t.stack}function pI(e,t){return e===t}function _g(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i=new Array(n),a=e.entries(),o,u,c=0;(o=a.next())&&!o.done;){for(var s=t.entries(),f=!1,l=0;(u=s.next())&&!u.done;){if(i[l]){l++;continue}var p=o.value,h=u.value;if(r.equals(p[0],h[0],c,l,e,t,r)&&r.equals(p[1],h[1],p[0],h[0],e,t,r)){f=i[l]=!0;break}l++}if(!f)return!1;c++}return!0}var hI=yr;function dI(e,t,r){var n=wg(e),i=n.length;if(wg(t).length!==i)return!1;for(;i-- >0;)if(!TO(e,t,r,n[i]))return!1;return!0}function Sn(e,t,r){var n=xg(e),i=n.length;if(xg(t).length!==i)return!1;for(var a,o,u;i-- >0;)if(a=n[i],!TO(e,t,r,a)||(o=Og(e,a),u=Og(t,a),(o||u)&&(!o||!u||o.configurable!==u.configurable||o.enumerable!==u.enumerable||o.writable!==u.writable)))return!1;return!0}function vI(e,t){return yr(e.valueOf(),t.valueOf())}function yI(e,t){return e.source===t.source&&e.flags===t.flags}function Ag(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i=new Array(n),a=e.values(),o,u;(o=a.next())&&!o.done;){for(var c=t.values(),s=!1,f=0;(u=c.next())&&!u.done;){if(!i[f]&&r.equals(o.value,u.value,o.value,u.value,e,t,r)){s=i[f]=!0;break}f++}if(!s)return!1}return!0}function mI(e,t){var r=e.length;if(t.length!==r)return!1;for(;r-- >0;)if(e[r]!==t[r])return!1;return!0}function gI(e,t){return e.hostname===t.hostname&&e.pathname===t.pathname&&e.protocol===t.protocol&&e.port===t.port&&e.hash===t.hash&&e.username===t.username&&e.password===t.password}function TO(e,t,r,n){return(n===cI||n===uI||n===oI)&&(e.$$typeof||t.$$typeof)?!0:aI(t,n)&&r.equals(e[n],t[n],n,n,e,t,r)}var bI="[object Arguments]",xI="[object Boolean]",OI="[object Date]",wI="[object Error]",_I="[object Map]",AI="[object Number]",SI="[object Object]",PI="[object RegExp]",TI="[object Set]",EI="[object String]",jI="[object URL]",$I=Array.isArray,Sg=typeof ArrayBuffer=="function"&&ArrayBuffer.isView?ArrayBuffer.isView:null,Pg=Object.assign,MI=Object.prototype.toString.call.bind(Object.prototype.toString);function II(e){var t=e.areArraysEqual,r=e.areDatesEqual,n=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,o=e.areNumbersEqual,u=e.areObjectsEqual,c=e.arePrimitiveWrappersEqual,s=e.areRegExpsEqual,f=e.areSetsEqual,l=e.areTypedArraysEqual,p=e.areUrlsEqual;return function(v,d,y){if(v===d)return!0;if(v==null||d==null)return!1;var b=typeof v;if(b!==typeof d)return!1;if(b!=="object")return b==="number"?o(v,d,y):b==="function"?i(v,d,y):!1;var g=v.constructor;if(g!==d.constructor)return!1;if(g===Object)return u(v,d,y);if($I(v))return t(v,d,y);if(Sg!=null&&Sg(v))return l(v,d,y);if(g===Date)return r(v,d,y);if(g===RegExp)return s(v,d,y);if(g===Map)return a(v,d,y);if(g===Set)return f(v,d,y);var x=MI(v);return x===OI?r(v,d,y):x===PI?s(v,d,y):x===_I?a(v,d,y):x===TI?f(v,d,y):x===SI?typeof v.then!="function"&&typeof d.then!="function"&&u(v,d,y):x===jI?p(v,d,y):x===wI?n(v,d,y):x===bI?u(v,d,y):x===xI||x===AI||x===EI?c(v,d,y):!1}}function CI(e){var t=e.circular,r=e.createCustomConfig,n=e.strict,i={areArraysEqual:n?Sn:sI,areDatesEqual:lI,areErrorsEqual:fI,areFunctionsEqual:pI,areMapsEqual:n?bg(_g,Sn):_g,areNumbersEqual:hI,areObjectsEqual:n?Sn:dI,arePrimitiveWrappersEqual:vI,areRegExpsEqual:yI,areSetsEqual:n?bg(Ag,Sn):Ag,areTypedArraysEqual:n?Sn:mI,areUrlsEqual:gI};if(r&&(i=Pg({},i,r(i))),t){var a=Wi(i.areArraysEqual),o=Wi(i.areMapsEqual),u=Wi(i.areObjectsEqual),c=Wi(i.areSetsEqual);i=Pg({},i,{areArraysEqual:a,areMapsEqual:o,areObjectsEqual:u,areSetsEqual:c})}return i}function kI(e){return function(t,r,n,i,a,o,u){return e(t,r,u)}}function RI(e){var t=e.circular,r=e.comparator,n=e.createState,i=e.equals,a=e.strict;if(n)return function(c,s){var f=n(),l=f.cache,p=l===void 0?t?new WeakMap:void 0:l,h=f.meta;return r(c,s,{cache:p,equals:i,meta:h,strict:a})};if(t)return function(c,s){return r(c,s,{cache:new WeakMap,equals:i,meta:void 0,strict:a})};var o={cache:void 0,equals:i,meta:void 0,strict:a};return function(c,s){return r(c,s,o)}}var DI=Kt();Kt({strict:!0});Kt({circular:!0});Kt({circular:!0,strict:!0});Kt({createInternalComparator:function(){return yr}});Kt({strict:!0,createInternalComparator:function(){return yr}});Kt({circular:!0,createInternalComparator:function(){return yr}});Kt({circular:!0,createInternalComparator:function(){return yr},strict:!0});function Kt(e){e===void 0&&(e={});var t=e.circular,r=t===void 0?!1:t,n=e.createInternalComparator,i=e.createState,a=e.strict,o=a===void 0?!1:a,u=CI(e),c=II(u),s=n?n(c):kI(c);return RI({circular:r,comparator:c,createState:i,equals:s,strict:o})}function NI(e){typeof requestAnimationFrame!="undefined"&&requestAnimationFrame(e)}function Tg(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r=-1,n=function i(a){r<0&&(r=a),a-r>t?(e(a),r=-1):NI(i)};requestAnimationFrame(n)}function Of(e){"@babel/helpers - typeof";return Of=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Of(e)}function qI(e){return zI(e)||FI(e)||BI(e)||LI()}function LI(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function BI(e,t){if(e){if(typeof e=="string")return Eg(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Eg(e,t)}}function Eg(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function FI(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function zI(e){if(Array.isArray(e))return e}function WI(){var e={},t=function(){return null},r=!1,n=function i(a){if(!r){if(Array.isArray(a)){if(!a.length)return;var o=a,u=qI(o),c=u[0],s=u.slice(1);if(typeof c=="number"){Tg(i.bind(null,s),c);return}i(c),Tg(i.bind(null,s));return}Of(a)==="object"&&(e=a,t(e)),typeof a=="function"&&a()}};return{stop:function(){r=!0},start:function(a){r=!1,n(a)},subscribe:function(a){return t=a,function(){t=function(){return null}}}}}function ui(e){"@babel/helpers - typeof";return ui=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ui(e)}function jg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function $g(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?jg(Object(r),!0).forEach(function(n){EO(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):jg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function EO(e,t,r){return t=UI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function UI(e){var t=KI(e,"string");return ui(t)==="symbol"?t:String(t)}function KI(e,t){if(ui(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ui(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var HI=function(t,r){return[Object.keys(t),Object.keys(r)].reduce(function(n,i){return n.filter(function(a){return i.includes(a)})})},GI=function(t){return t},VI=function(t){return t.replace(/([A-Z])/g,function(r){return"-".concat(r.toLowerCase())})},Cn=function(t,r){return Object.keys(r).reduce(function(n,i){return $g($g({},n),{},EO({},i,t(i,r[i])))},{})},Mg=function(t,r,n){return t.map(function(i){return"".concat(VI(i)," ").concat(r,"ms ").concat(n)}).join(",")};function XI(e,t){return JI(e)||ZI(e,t)||jO(e,t)||YI()}function YI(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ZI(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function JI(e){if(Array.isArray(e))return e}function QI(e){return rC(e)||tC(e)||jO(e)||eC()}function eC(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function jO(e,t){if(e){if(typeof e=="string")return wf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return wf(e,t)}}function tC(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function rC(e){if(Array.isArray(e))return wf(e)}function wf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Oa=1e-4,$O=function(t,r){return[0,3*t,3*r-6*t,3*t-3*r+1]},MO=function(t,r){return t.map(function(n,i){return n*Math.pow(r,i)}).reduce(function(n,i){return n+i})},Ig=function(t,r){return function(n){var i=$O(t,r);return MO(i,n)}},nC=function(t,r){return function(n){var i=$O(t,r),a=[].concat(QI(i.map(function(o,u){return o*u}).slice(1)),[0]);return MO(a,n)}},Cg=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0],a=r[1],o=r[2],u=r[3];if(r.length===1)switch(r[0]){case"linear":i=0,a=0,o=1,u=1;break;case"ease":i=.25,a=.1,o=.25,u=1;break;case"ease-in":i=.42,a=0,o=1,u=1;break;case"ease-out":i=.42,a=0,o=.58,u=1;break;case"ease-in-out":i=0,a=0,o=.58,u=1;break;default:{var c=r[0].split("(");if(c[0]==="cubic-bezier"&&c[1].split(")")[0].split(",").length===4){var s=c[1].split(")")[0].split(",").map(function(y){return parseFloat(y)}),f=XI(s,4);i=f[0],a=f[1],o=f[2],u=f[3]}}}var l=Ig(i,o),p=Ig(a,u),h=nC(i,o),v=function(b){return b>1?1:b<0?0:b},d=function(b){for(var g=b>1?1:b,x=g,w=0;w<8;++w){var m=l(x)-g,O=h(x);if(Math.abs(m-g)<Oa||O<Oa)return p(x);x=v(x-m/O)}return p(x)};return d.isStepper=!1,d},iC=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=t.stiff,n=r===void 0?100:r,i=t.damping,a=i===void 0?8:i,o=t.dt,u=o===void 0?17:o,c=function(f,l,p){var h=-(f-l)*n,v=p*a,d=p+(h-v)*u/1e3,y=p*u/1e3+f;return Math.abs(y-l)<Oa&&Math.abs(d)<Oa?[l,0]:[y,d]};return c.isStepper=!0,c.dt=u,c},aC=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0];if(typeof i=="string")switch(i){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return Cg(i);case"spring":return iC();default:if(i.split("(")[0]==="cubic-bezier")return Cg(i)}return typeof i=="function"?i:null};function ci(e){"@babel/helpers - typeof";return ci=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ci(e)}function kg(e){return cC(e)||uC(e)||IO(e)||oC()}function oC(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function uC(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function cC(e){if(Array.isArray(e))return Af(e)}function Rg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ee(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Rg(Object(r),!0).forEach(function(n){_f(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Rg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function _f(e,t,r){return t=sC(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function sC(e){var t=lC(e,"string");return ci(t)==="symbol"?t:String(t)}function lC(e,t){if(ci(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ci(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function fC(e,t){return dC(e)||hC(e,t)||IO(e,t)||pC()}function pC(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function IO(e,t){if(e){if(typeof e=="string")return Af(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Af(e,t)}}function Af(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function hC(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function dC(e){if(Array.isArray(e))return e}var wa=function(t,r,n){return t+(r-t)*n},Sf=function(t){var r=t.from,n=t.to;return r!==n},vC=function e(t,r,n){var i=Cn(function(a,o){if(Sf(o)){var u=t(o.from,o.to,o.velocity),c=fC(u,2),s=c[0],f=c[1];return Ee(Ee({},o),{},{from:s,velocity:f})}return o},r);return n<1?Cn(function(a,o){return Sf(o)?Ee(Ee({},o),{},{velocity:wa(o.velocity,i[a].velocity,n),from:wa(o.from,i[a].from,n)}):o},r):e(t,i,n-1)};const yC=function(e,t,r,n,i){var a=HI(e,t),o=a.reduce(function(y,b){return Ee(Ee({},y),{},_f({},b,[e[b],t[b]]))},{}),u=a.reduce(function(y,b){return Ee(Ee({},y),{},_f({},b,{from:e[b],velocity:0,to:t[b]}))},{}),c=-1,s,f,l=function(){return null},p=function(){return Cn(function(b,g){return g.from},u)},h=function(){return!Object.values(u).filter(Sf).length},v=function(b){s||(s=b);var g=b-s,x=g/r.dt;u=vC(r,u,x),i(Ee(Ee(Ee({},e),t),p())),s=b,h()||(c=requestAnimationFrame(l))},d=function(b){f||(f=b);var g=(b-f)/n,x=Cn(function(m,O){return wa.apply(void 0,kg(O).concat([r(g)]))},o);if(i(Ee(Ee(Ee({},e),t),x)),g<1)c=requestAnimationFrame(l);else{var w=Cn(function(m,O){return wa.apply(void 0,kg(O).concat([r(1)]))},o);i(Ee(Ee(Ee({},e),t),w))}};return l=r.isStepper?v:d,function(){return requestAnimationFrame(l),function(){cancelAnimationFrame(c)}}};function Wr(e){"@babel/helpers - typeof";return Wr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Wr(e)}var mC=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function gC(e,t){if(e==null)return{};var r=bC(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function bC(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function cl(e){return _C(e)||wC(e)||OC(e)||xC()}function xC(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function OC(e,t){if(e){if(typeof e=="string")return Pf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Pf(e,t)}}function wC(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function _C(e){if(Array.isArray(e))return Pf(e)}function Pf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Dg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function tt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Dg(Object(r),!0).forEach(function(n){jn(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Dg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function jn(e,t,r){return t=CO(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function AC(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function SC(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,CO(n.key),n)}}function PC(e,t,r){return t&&SC(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function CO(e){var t=TC(e,"string");return Wr(t)==="symbol"?t:String(t)}function TC(e,t){if(Wr(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Wr(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function EC(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Tf(e,t)}function Tf(e,t){return Tf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Tf(e,t)}function jC(e){var t=$C();return function(){var n=_a(e),i;if(t){var a=_a(this).constructor;i=Reflect.construct(n,arguments,a)}else i=n.apply(this,arguments);return Ef(this,i)}}function Ef(e,t){if(t&&(Wr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return jf(e)}function jf(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function $C(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}function _a(e){return _a=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},_a(e)}var Je=function(e){EC(r,e);var t=jC(r);function r(n,i){var a;AC(this,r),a=t.call(this,n,i);var o=a.props,u=o.isActive,c=o.attributeName,s=o.from,f=o.to,l=o.steps,p=o.children,h=o.duration;if(a.handleStyleChange=a.handleStyleChange.bind(jf(a)),a.changeStyle=a.changeStyle.bind(jf(a)),!u||h<=0)return a.state={style:{}},typeof p=="function"&&(a.state={style:f}),Ef(a);if(l&&l.length)a.state={style:l[0].style};else if(s){if(typeof p=="function")return a.state={style:s},Ef(a);a.state={style:c?jn({},c,s):s}}else a.state={style:{}};return a}return PC(r,[{key:"componentDidMount",value:function(){var i=this.props,a=i.isActive,o=i.canBegin;this.mounted=!0,!(!a||!o)&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(i){var a=this.props,o=a.isActive,u=a.canBegin,c=a.attributeName,s=a.shouldReAnimate,f=a.to,l=a.from,p=this.state.style;if(u){if(!o){var h={style:c?jn({},c,f):f};this.state&&p&&(c&&p[c]!==f||!c&&p!==f)&&this.setState(h);return}if(!(DI(i.to,f)&&i.canBegin&&i.isActive)){var v=!i.canBegin||!i.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var d=v||s?l:i.to;if(this.state&&p){var y={style:c?jn({},c,d):d};(c&&p[c]!==d||!c&&p!==d)&&this.setState(y)}this.runAnimation(tt(tt({},this.props),{},{from:d,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var i=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),i&&i()}},{key:"handleStyleChange",value:function(i){this.changeStyle(i)}},{key:"changeStyle",value:function(i){this.mounted&&this.setState({style:i})}},{key:"runJSAnimation",value:function(i){var a=this,o=i.from,u=i.to,c=i.duration,s=i.easing,f=i.begin,l=i.onAnimationEnd,p=i.onAnimationStart,h=yC(o,u,aC(s),c,this.changeStyle),v=function(){a.stopJSAnimation=h()};this.manager.start([p,f,v,c,l])}},{key:"runStepAnimation",value:function(i){var a=this,o=i.steps,u=i.begin,c=i.onAnimationStart,s=o[0],f=s.style,l=s.duration,p=l===void 0?0:l,h=function(d,y,b){if(b===0)return d;var g=y.duration,x=y.easing,w=x===void 0?"ease":x,m=y.style,O=y.properties,_=y.onAnimationEnd,S=b>0?o[b-1]:y,T=O||Object.keys(m);if(typeof w=="function"||w==="spring")return[].concat(cl(d),[a.runJSAnimation.bind(a,{from:S.style,to:m,duration:g,easing:w}),g]);var j=Mg(T,g,w),P=tt(tt(tt({},S.style),m),{},{transition:j});return[].concat(cl(d),[P,g,_]).filter(GI)};return this.manager.start([c].concat(cl(o.reduce(h,[f,Math.max(p,u)])),[i.onAnimationEnd]))}},{key:"runAnimation",value:function(i){this.manager||(this.manager=WI());var a=i.begin,o=i.duration,u=i.attributeName,c=i.to,s=i.easing,f=i.onAnimationStart,l=i.onAnimationEnd,p=i.steps,h=i.children,v=this.manager;if(this.unSubscribe=v.subscribe(this.handleStyleChange),typeof s=="function"||typeof h=="function"||s==="spring"){this.runJSAnimation(i);return}if(p.length>1){this.runStepAnimation(i);return}var d=u?jn({},u,c):c,y=Mg(Object.keys(d),o,s);v.start([f,a,tt(tt({},d),{},{transition:y}),o,l])}},{key:"render",value:function(){var i=this.props,a=i.children;i.begin;var o=i.duration;i.attributeName,i.easing;var u=i.isActive;i.steps,i.from,i.to,i.canBegin,i.onAnimationEnd,i.shouldReAnimate,i.onAnimationReStart;var c=gC(i,mC),s=q.Children.count(a),f=this.state.style;if(typeof a=="function")return a(f);if(!u||s===0||o<=0)return a;var l=function(h){var v=h.props,d=v.style,y=d===void 0?{}:d,b=v.className,g=q.cloneElement(h,tt(tt({},c),{},{style:tt(tt({},y),f),className:b}));return g};return s===1?l(q.Children.only(a)):A.createElement("div",null,q.Children.map(a,function(p){return l(p)}))}}]),r}(q.PureComponent);Je.displayName="Animate";Je.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}};Je.propTypes={from:ne.oneOfType([ne.object,ne.string]),to:ne.oneOfType([ne.object,ne.string]),attributeName:ne.string,duration:ne.number,begin:ne.number,easing:ne.oneOfType([ne.string,ne.func]),steps:ne.arrayOf(ne.shape({duration:ne.number.isRequired,style:ne.object.isRequired,easing:ne.oneOfType([ne.oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),ne.func]),properties:ne.arrayOf("string"),onAnimationEnd:ne.func})),children:ne.oneOfType([ne.node,ne.func]),isActive:ne.bool,canBegin:ne.bool,onAnimationEnd:ne.func,shouldReAnimate:ne.bool,onAnimationStart:ne.func,onAnimationReStart:ne.func};function si(e){"@babel/helpers - typeof";return si=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},si(e)}function Aa(){return Aa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Aa.apply(this,arguments)}function MC(e,t){return RC(e)||kC(e,t)||CC(e,t)||IC()}function IC(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function CC(e,t){if(e){if(typeof e=="string")return Ng(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ng(e,t)}}function Ng(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function kC(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function RC(e){if(Array.isArray(e))return e}function qg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Lg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?qg(Object(r),!0).forEach(function(n){DC(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):qg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function DC(e,t,r){return t=NC(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function NC(e){var t=qC(e,"string");return si(t)=="symbol"?t:t+""}function qC(e,t){if(si(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(si(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Bg=function(t,r,n,i,a){var o=Math.min(Math.abs(n)/2,Math.abs(i)/2),u=i>=0?1:-1,c=n>=0?1:-1,s=i>=0&&n>=0||i<0&&n<0?1:0,f;if(o>0&&a instanceof Array){for(var l=[0,0,0,0],p=0,h=4;p<h;p++)l[p]=a[p]>o?o:a[p];f="M".concat(t,",").concat(r+u*l[0]),l[0]>0&&(f+="A ".concat(l[0],",").concat(l[0],",0,0,").concat(s,",").concat(t+c*l[0],",").concat(r)),f+="L ".concat(t+n-c*l[1],",").concat(r),l[1]>0&&(f+="A ".concat(l[1],",").concat(l[1],",0,0,").concat(s,`,
        `).concat(t+n,",").concat(r+u*l[1])),f+="L ".concat(t+n,",").concat(r+i-u*l[2]),l[2]>0&&(f+="A ".concat(l[2],",").concat(l[2],",0,0,").concat(s,`,
        `).concat(t+n-c*l[2],",").concat(r+i)),f+="L ".concat(t+c*l[3],",").concat(r+i),l[3]>0&&(f+="A ".concat(l[3],",").concat(l[3],",0,0,").concat(s,`,
        `).concat(t,",").concat(r+i-u*l[3])),f+="Z"}else if(o>0&&a===+a&&a>0){var v=Math.min(o,a);f="M ".concat(t,",").concat(r+u*v,`
            A `).concat(v,",").concat(v,",0,0,").concat(s,",").concat(t+c*v,",").concat(r,`
            L `).concat(t+n-c*v,",").concat(r,`
            A `).concat(v,",").concat(v,",0,0,").concat(s,",").concat(t+n,",").concat(r+u*v,`
            L `).concat(t+n,",").concat(r+i-u*v,`
            A `).concat(v,",").concat(v,",0,0,").concat(s,",").concat(t+n-c*v,",").concat(r+i,`
            L `).concat(t+c*v,",").concat(r+i,`
            A `).concat(v,",").concat(v,",0,0,").concat(s,",").concat(t,",").concat(r+i-u*v," Z")}else f="M ".concat(t,",").concat(r," h ").concat(n," v ").concat(i," h ").concat(-n," Z");return f},LC=function(t,r){if(!t||!r)return!1;var n=t.x,i=t.y,a=r.x,o=r.y,u=r.width,c=r.height;if(Math.abs(u)>0&&Math.abs(c)>0){var s=Math.min(a,a+u),f=Math.max(a,a+u),l=Math.min(o,o+c),p=Math.max(o,o+c);return n>=s&&n<=f&&i>=l&&i<=p}return!1},BC={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},Jp=function(t){var r=Lg(Lg({},BC),t),n=q.useRef(),i=q.useState(-1),a=MC(i,2),o=a[0],u=a[1];q.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var w=n.current.getTotalLength();w&&u(w)}catch(m){}},[]);var c=r.x,s=r.y,f=r.width,l=r.height,p=r.radius,h=r.className,v=r.animationEasing,d=r.animationDuration,y=r.animationBegin,b=r.isAnimationActive,g=r.isUpdateAnimationActive;if(c!==+c||s!==+s||f!==+f||l!==+l||f===0||l===0)return null;var x=Z("recharts-rectangle",h);return g?A.createElement(Je,{canBegin:o>0,from:{width:f,height:l,x:c,y:s},to:{width:f,height:l,x:c,y:s},duration:d,animationEasing:v,isActive:g},function(w){var m=w.width,O=w.height,_=w.x,S=w.y;return A.createElement(Je,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:d,isActive:b,easing:v},A.createElement("path",Aa({},U(r,!0),{className:x,d:Bg(_,S,m,O,p),ref:n})))}):A.createElement("path",Aa({},U(r,!0),{className:x,d:Bg(c,s,f,l,p)}))},FC=["points","className","baseLinePoints","connectNulls"];function Sr(){return Sr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Sr.apply(this,arguments)}function zC(e,t){if(e==null)return{};var r=WC(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function WC(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Fg(e){return GC(e)||HC(e)||KC(e)||UC()}function UC(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function KC(e,t){if(e){if(typeof e=="string")return $f(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return $f(e,t)}}function HC(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function GC(e){if(Array.isArray(e))return $f(e)}function $f(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var zg=function(t){return t&&t.x===+t.x&&t.y===+t.y},VC=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],r=[[]];return t.forEach(function(n){zg(n)?r[r.length-1].push(n):r[r.length-1].length>0&&r.push([])}),zg(t[0])&&r[r.length-1].push(t[0]),r[r.length-1].length<=0&&(r=r.slice(0,-1)),r},kn=function(t,r){var n=VC(t);r&&(n=[n.reduce(function(a,o){return[].concat(Fg(a),Fg(o))},[])]);var i=n.map(function(a){return a.reduce(function(o,u,c){return"".concat(o).concat(c===0?"M":"L").concat(u.x,",").concat(u.y)},"")}).join("");return n.length===1?"".concat(i,"Z"):i},XC=function(t,r,n){var i=kn(t,n);return"".concat(i.slice(-1)==="Z"?i.slice(0,-1):i,"L").concat(kn(r.reverse(),n).slice(1))},YC=function(t){var r=t.points,n=t.className,i=t.baseLinePoints,a=t.connectNulls,o=zC(t,FC);if(!r||!r.length)return null;var u=Z("recharts-polygon",n);if(i&&i.length){var c=o.stroke&&o.stroke!=="none",s=XC(r,i,a);return A.createElement("g",{className:u},A.createElement("path",Sr({},U(o,!0),{fill:s.slice(-1)==="Z"?o.fill:"none",stroke:"none",d:s})),c?A.createElement("path",Sr({},U(o,!0),{fill:"none",d:kn(r,a)})):null,c?A.createElement("path",Sr({},U(o,!0),{fill:"none",d:kn(i,a)})):null)}var f=kn(r,a);return A.createElement("path",Sr({},U(o,!0),{fill:f.slice(-1)==="Z"?o.fill:"none",className:u,d:f}))};function Mf(){return Mf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Mf.apply(this,arguments)}var Ti=function(t){var r=t.cx,n=t.cy,i=t.r,a=t.className,o=Z("recharts-dot",a);return r===+r&&n===+n&&i===+i?A.createElement("circle",Mf({},U(t,!1),Vi(t),{className:o,cx:r,cy:n,r:i})):null};function li(e){"@babel/helpers - typeof";return li=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},li(e)}var ZC=["x","y","top","left","width","height","className"];function If(){return If=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},If.apply(this,arguments)}function Wg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function JC(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Wg(Object(r),!0).forEach(function(n){QC(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Wg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function QC(e,t,r){return t=ek(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ek(e){var t=tk(e,"string");return li(t)=="symbol"?t:t+""}function tk(e,t){if(li(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(li(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function rk(e,t){if(e==null)return{};var r=nk(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function nk(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var ik=function(t,r,n,i,a,o){return"M".concat(t,",").concat(a,"v").concat(i,"M").concat(o,",").concat(r,"h").concat(n)},ak=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.top,u=o===void 0?0:o,c=t.left,s=c===void 0?0:c,f=t.width,l=f===void 0?0:f,p=t.height,h=p===void 0?0:p,v=t.className,d=rk(t,ZC),y=JC({x:n,y:a,top:u,left:s,width:l,height:h},d);return!N(n)||!N(a)||!N(l)||!N(h)||!N(u)||!N(s)?null:A.createElement("path",If({},U(y,!0),{className:Z("recharts-cross",v),d:ik(n,a,l,h,u,s)}))},sl,Ug;function ok(){if(Ug)return sl;Ug=1;var e=vo(),t=Vx(),r=dt();function n(i,a){return i&&i.length?e(i,r(a,2),t):void 0}return sl=n,sl}var uk=ok();const ck=ae(uk);var ll,Kg;function sk(){if(Kg)return ll;Kg=1;var e=vo(),t=dt(),r=Xx();function n(i,a){return i&&i.length?e(i,t(a,2),r):void 0}return ll=n,ll}var lk=sk();const fk=ae(lk);var pk=["cx","cy","angle","ticks","axisLine"],hk=["ticks","tick","angle","tickFormatter","stroke"];function Ur(e){"@babel/helpers - typeof";return Ur=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ur(e)}function Rn(){return Rn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Rn.apply(this,arguments)}function Hg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Zt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Hg(Object(r),!0).forEach(function(n){bo(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Hg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Gg(e,t){if(e==null)return{};var r=dk(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function dk(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function vk(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Vg(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,RO(n.key),n)}}function yk(e,t,r){return t&&Vg(e.prototype,t),r&&Vg(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function mk(e,t,r){return t=Sa(t),gk(e,kO()?Reflect.construct(t,r||[],Sa(e).constructor):t.apply(e,r))}function gk(e,t){if(t&&(Ur(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return bk(e)}function bk(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function kO(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(kO=function(){return!!e})()}function Sa(e){return Sa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Sa(e)}function xk(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Cf(e,t)}function Cf(e,t){return Cf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Cf(e,t)}function bo(e,t,r){return t=RO(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function RO(e){var t=Ok(e,"string");return Ur(t)=="symbol"?t:t+""}function Ok(e,t){if(Ur(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ur(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var xo=function(e){function t(){return vk(this,t),mk(this,t,arguments)}return xk(t,e),yk(t,[{key:"getTickValueCoord",value:function(n){var i=n.coordinate,a=this.props,o=a.angle,u=a.cx,c=a.cy;return se(u,c,i,o)}},{key:"getTickTextAnchor",value:function(){var n=this.props.orientation,i;switch(n){case"left":i="end";break;case"right":i="start";break;default:i="middle";break}return i}},{key:"getViewBox",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.angle,u=n.ticks,c=ck(u,function(f){return f.coordinate||0}),s=fk(u,function(f){return f.coordinate||0});return{cx:i,cy:a,startAngle:o,endAngle:o,innerRadius:s.coordinate||0,outerRadius:c.coordinate||0}}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.angle,u=n.ticks,c=n.axisLine,s=Gg(n,pk),f=u.reduce(function(v,d){return[Math.min(v[0],d.coordinate),Math.max(v[1],d.coordinate)]},[1/0,-1/0]),l=se(i,a,f[0],o),p=se(i,a,f[1],o),h=Zt(Zt(Zt({},U(s,!1)),{},{fill:"none"},U(c,!1)),{},{x1:l.x,y1:l.y,x2:p.x,y2:p.y});return A.createElement("line",Rn({className:"recharts-polar-radius-axis-line"},h))}},{key:"renderTicks",value:function(){var n=this,i=this.props,a=i.ticks,o=i.tick,u=i.angle,c=i.tickFormatter,s=i.stroke,f=Gg(i,hk),l=this.getTickTextAnchor(),p=U(f,!1),h=U(o,!1),v=a.map(function(d,y){var b=n.getTickValueCoord(d),g=Zt(Zt(Zt(Zt({textAnchor:l,transform:"rotate(".concat(90-u,", ").concat(b.x,", ").concat(b.y,")")},p),{},{stroke:"none",fill:s},h),{},{index:y},b),{},{payload:d});return A.createElement(J,Rn({className:Z("recharts-polar-radius-axis-tick",_O(o)),key:"tick-".concat(d.coordinate)},Lt(n.props,d,y)),t.renderTickItem(o,g,c?c(d.value,y):d.value))});return A.createElement(J,{className:"recharts-polar-radius-axis-ticks"},v)}},{key:"render",value:function(){var n=this.props,i=n.ticks,a=n.axisLine,o=n.tick;return!i||!i.length?null:A.createElement(J,{className:Z("recharts-polar-radius-axis",this.props.className)},a&&this.renderAxisLine(),o&&this.renderTicks(),Pe.renderCallByParent(this.props,this.getViewBox()))}}],[{key:"renderTickItem",value:function(n,i,a){var o;return A.isValidElement(n)?o=A.cloneElement(n,i):H(n)?o=n(i):o=A.createElement(lr,Rn({},i,{className:"recharts-polar-radius-axis-tick-value"}),a),o}}])}(q.PureComponent);bo(xo,"displayName","PolarRadiusAxis");bo(xo,"axisType","radiusAxis");bo(xo,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});function Kr(e){"@babel/helpers - typeof";return Kr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Kr(e)}function tr(){return tr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},tr.apply(this,arguments)}function Xg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Jt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Xg(Object(r),!0).forEach(function(n){Oo(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Xg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function wk(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Yg(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,NO(n.key),n)}}function _k(e,t,r){return t&&Yg(e.prototype,t),r&&Yg(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Ak(e,t,r){return t=Pa(t),Sk(e,DO()?Reflect.construct(t,r||[],Pa(e).constructor):t.apply(e,r))}function Sk(e,t){if(t&&(Kr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Pk(e)}function Pk(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function DO(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(DO=function(){return!!e})()}function Pa(e){return Pa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Pa(e)}function Tk(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&kf(e,t)}function kf(e,t){return kf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},kf(e,t)}function Oo(e,t,r){return t=NO(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function NO(e){var t=Ek(e,"string");return Kr(t)=="symbol"?t:t+""}function Ek(e,t){if(Kr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Kr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var jk=Math.PI/180,$k=1e-5,wo=function(e){function t(){return wk(this,t),Ak(this,t,arguments)}return Tk(t,e),_k(t,[{key:"getTickLineCoord",value:function(n){var i=this.props,a=i.cx,o=i.cy,u=i.radius,c=i.orientation,s=i.tickSize,f=s||8,l=se(a,o,u,n.coordinate),p=se(a,o,u+(c==="inner"?-1:1)*f,n.coordinate);return{x1:l.x,y1:l.y,x2:p.x,y2:p.y}}},{key:"getTickTextAnchor",value:function(n){var i=this.props.orientation,a=Math.cos(-n.coordinate*jk),o;return a>$k?o=i==="outer"?"start":"end":a<-1e-5?o=i==="outer"?"end":"start":o="middle",o}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.radius,u=n.axisLine,c=n.axisLineType,s=Jt(Jt({},U(this.props,!1)),{},{fill:"none"},U(u,!1));if(c==="circle")return A.createElement(Ti,tr({className:"recharts-polar-angle-axis-line"},s,{cx:i,cy:a,r:o}));var f=this.props.ticks,l=f.map(function(p){return se(i,a,o,p.coordinate)});return A.createElement(YC,tr({className:"recharts-polar-angle-axis-line"},s,{points:l}))}},{key:"renderTicks",value:function(){var n=this,i=this.props,a=i.ticks,o=i.tick,u=i.tickLine,c=i.tickFormatter,s=i.stroke,f=U(this.props,!1),l=U(o,!1),p=Jt(Jt({},f),{},{fill:"none"},U(u,!1)),h=a.map(function(v,d){var y=n.getTickLineCoord(v),b=n.getTickTextAnchor(v),g=Jt(Jt(Jt({textAnchor:b},f),{},{stroke:"none",fill:s},l),{},{index:d,payload:v,x:y.x2,y:y.y2});return A.createElement(J,tr({className:Z("recharts-polar-angle-axis-tick",_O(o)),key:"tick-".concat(v.coordinate)},Lt(n.props,v,d)),u&&A.createElement("line",tr({className:"recharts-polar-angle-axis-tick-line"},p,y)),o&&t.renderTickItem(o,g,c?c(v.value,d):v.value))});return A.createElement(J,{className:"recharts-polar-angle-axis-ticks"},h)}},{key:"render",value:function(){var n=this.props,i=n.ticks,a=n.radius,o=n.axisLine;return a<=0||!i||!i.length?null:A.createElement(J,{className:Z("recharts-polar-angle-axis",this.props.className)},o&&this.renderAxisLine(),this.renderTicks())}}],[{key:"renderTickItem",value:function(n,i,a){var o;return A.isValidElement(n)?o=A.cloneElement(n,i):H(n)?o=n(i):o=A.createElement(lr,tr({},i,{className:"recharts-polar-angle-axis-tick-value"}),a),o}}])}(q.PureComponent);Oo(wo,"displayName","PolarAngleAxis");Oo(wo,"axisType","angleAxis");Oo(wo,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var fl,Zg;function Mk(){if(Zg)return fl;Zg=1;var e=G0(),t=e(Object.getPrototypeOf,Object);return fl=t,fl}var pl,Jg;function Ik(){if(Jg)return pl;Jg=1;var e=Pt(),t=Mk(),r=Tt(),n="[object Object]",i=Function.prototype,a=Object.prototype,o=i.toString,u=a.hasOwnProperty,c=o.call(Object);function s(f){if(!r(f)||e(f)!=n)return!1;var l=t(f);if(l===null)return!0;var p=u.call(l,"constructor")&&l.constructor;return typeof p=="function"&&p instanceof p&&o.call(p)==c}return pl=s,pl}var Ck=Ik();const kk=ae(Ck);var hl,Qg;function Rk(){if(Qg)return hl;Qg=1;var e=Pt(),t=Tt(),r="[object Boolean]";function n(i){return i===!0||i===!1||t(i)&&e(i)==r}return hl=n,hl}var Dk=Rk();const Nk=ae(Dk);function fi(e){"@babel/helpers - typeof";return fi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fi(e)}function Ta(){return Ta=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ta.apply(this,arguments)}function qk(e,t){return zk(e)||Fk(e,t)||Bk(e,t)||Lk()}function Lk(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Bk(e,t){if(e){if(typeof e=="string")return eb(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return eb(e,t)}}function eb(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Fk(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function zk(e){if(Array.isArray(e))return e}function tb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function rb(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?tb(Object(r),!0).forEach(function(n){Wk(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):tb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Wk(e,t,r){return t=Uk(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Uk(e){var t=Kk(e,"string");return fi(t)=="symbol"?t:t+""}function Kk(e,t){if(fi(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(fi(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var nb=function(t,r,n,i,a){var o=n-i,u;return u="M ".concat(t,",").concat(r),u+="L ".concat(t+n,",").concat(r),u+="L ".concat(t+n-o/2,",").concat(r+a),u+="L ".concat(t+n-o/2-i,",").concat(r+a),u+="L ".concat(t,",").concat(r," Z"),u},Hk={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},Gk=function(t){var r=rb(rb({},Hk),t),n=q.useRef(),i=q.useState(-1),a=qk(i,2),o=a[0],u=a[1];q.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var x=n.current.getTotalLength();x&&u(x)}catch(w){}},[]);var c=r.x,s=r.y,f=r.upperWidth,l=r.lowerWidth,p=r.height,h=r.className,v=r.animationEasing,d=r.animationDuration,y=r.animationBegin,b=r.isUpdateAnimationActive;if(c!==+c||s!==+s||f!==+f||l!==+l||p!==+p||f===0&&l===0||p===0)return null;var g=Z("recharts-trapezoid",h);return b?A.createElement(Je,{canBegin:o>0,from:{upperWidth:0,lowerWidth:0,height:p,x:c,y:s},to:{upperWidth:f,lowerWidth:l,height:p,x:c,y:s},duration:d,animationEasing:v,isActive:b},function(x){var w=x.upperWidth,m=x.lowerWidth,O=x.height,_=x.x,S=x.y;return A.createElement(Je,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:d,easing:v},A.createElement("path",Ta({},U(r,!0),{className:g,d:nb(_,S,w,m,O),ref:n})))}):A.createElement("g",null,A.createElement("path",Ta({},U(r,!0),{className:g,d:nb(c,s,f,l,p)})))},Vk=["option","shapeType","propTransformer","activeClassName","isActive"];function pi(e){"@babel/helpers - typeof";return pi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},pi(e)}function Xk(e,t){if(e==null)return{};var r=Yk(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Yk(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function ib(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ea(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ib(Object(r),!0).forEach(function(n){Zk(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ib(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Zk(e,t,r){return t=Jk(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Jk(e){var t=Qk(e,"string");return pi(t)=="symbol"?t:t+""}function Qk(e,t){if(pi(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(pi(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function eR(e,t){return Ea(Ea({},t),e)}function tR(e,t){return e==="symbols"}function ab(e){var t=e.shapeType,r=e.elementProps;switch(t){case"rectangle":return A.createElement(Jp,r);case"trapezoid":return A.createElement(Gk,r);case"sector":return A.createElement(PO,r);case"symbols":if(tR(t))return A.createElement(no,r);break;default:return null}}function rR(e){return q.isValidElement(e)?e.props:e}function ja(e){var t=e.option,r=e.shapeType,n=e.propTransformer,i=n===void 0?eR:n,a=e.activeClassName,o=a===void 0?"recharts-active-shape":a,u=e.isActive,c=Xk(e,Vk),s;if(q.isValidElement(t))s=q.cloneElement(t,Ea(Ea({},c),rR(t)));else if(H(t))s=t(c);else if(kk(t)&&!Nk(t)){var f=i(t,c);s=A.createElement(ab,{shapeType:r,elementProps:f})}else{var l=c;s=A.createElement(ab,{shapeType:r,elementProps:l})}return u?A.createElement(J,{className:o},s):s}function _o(e,t){return t!=null&&"trapezoids"in e.props}function Ao(e,t){return t!=null&&"sectors"in e.props}function hi(e,t){return t!=null&&"points"in e.props}function nR(e,t){var r,n,i=e.x===(t==null||(r=t.labelViewBox)===null||r===void 0?void 0:r.x)||e.x===t.x,a=e.y===(t==null||(n=t.labelViewBox)===null||n===void 0?void 0:n.y)||e.y===t.y;return i&&a}function iR(e,t){var r=e.endAngle===t.endAngle,n=e.startAngle===t.startAngle;return r&&n}function aR(e,t){var r=e.x===t.x,n=e.y===t.y,i=e.z===t.z;return r&&n&&i}function oR(e,t){var r;return _o(e,t)?r=nR:Ao(e,t)?r=iR:hi(e,t)&&(r=aR),r}function uR(e,t){var r;return _o(e,t)?r="trapezoids":Ao(e,t)?r="sectors":hi(e,t)&&(r="points"),r}function cR(e,t){if(_o(e,t)){var r;return(r=t.tooltipPayload)===null||r===void 0||(r=r[0])===null||r===void 0||(r=r.payload)===null||r===void 0?void 0:r.payload}if(Ao(e,t)){var n;return(n=t.tooltipPayload)===null||n===void 0||(n=n[0])===null||n===void 0||(n=n.payload)===null||n===void 0?void 0:n.payload}return hi(e,t)?t.payload:{}}function sR(e){var t=e.activeTooltipItem,r=e.graphicalItem,n=e.itemData,i=uR(r,t),a=cR(r,t),o=n.filter(function(c,s){var f=Bt(a,c),l=r.props[i].filter(function(v){var d=oR(r,t);return d(v,t)}),p=r.props[i].indexOf(l[l.length-1]),h=s===p;return f&&h}),u=n.indexOf(o[o.length-1]);return u}var Hi;function Hr(e){"@babel/helpers - typeof";return Hr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Hr(e)}function Pr(){return Pr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Pr.apply(this,arguments)}function ob(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ue(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ob(Object(r),!0).forEach(function(n){Xe(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ob(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function lR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ub(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,LO(n.key),n)}}function fR(e,t,r){return t&&ub(e.prototype,t),r&&ub(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function pR(e,t,r){return t=$a(t),hR(e,qO()?Reflect.construct(t,r||[],$a(e).constructor):t.apply(e,r))}function hR(e,t){if(t&&(Hr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return dR(e)}function dR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function qO(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(qO=function(){return!!e})()}function $a(e){return $a=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},$a(e)}function vR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Rf(e,t)}function Rf(e,t){return Rf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Rf(e,t)}function Xe(e,t,r){return t=LO(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function LO(e){var t=yR(e,"string");return Hr(t)=="symbol"?t:t+""}function yR(e,t){if(Hr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Hr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Ht=function(e){function t(r){var n;return lR(this,t),n=pR(this,t,[r]),Xe(n,"pieRef",null),Xe(n,"sectorRefs",[]),Xe(n,"id",zt("recharts-pie-")),Xe(n,"handleAnimationEnd",function(){var i=n.props.onAnimationEnd;n.setState({isAnimationFinished:!0}),H(i)&&i()}),Xe(n,"handleAnimationStart",function(){var i=n.props.onAnimationStart;n.setState({isAnimationFinished:!1}),H(i)&&i()}),n.state={isAnimationFinished:!r.isAnimationActive,prevIsAnimationActive:r.isAnimationActive,prevAnimationId:r.animationId,sectorToFocus:0},n}return vR(t,e),fR(t,[{key:"isActiveIndex",value:function(n){var i=this.props.activeIndex;return Array.isArray(i)?i.indexOf(n)!==-1:n===i}},{key:"hasActiveIndex",value:function(){var n=this.props.activeIndex;return Array.isArray(n)?n.length!==0:n||n===0}},{key:"renderLabels",value:function(n){var i=this.props.isAnimationActive;if(i&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.label,u=a.labelLine,c=a.dataKey,s=a.valueKey,f=U(this.props,!1),l=U(o,!1),p=U(u,!1),h=o&&o.offsetRadius||20,v=n.map(function(d,y){var b=(d.startAngle+d.endAngle)/2,g=se(d.cx,d.cy,d.outerRadius+h,b),x=ue(ue(ue(ue({},f),d),{},{stroke:"none"},l),{},{index:y,textAnchor:t.getTextAnchor(g.x,d.cx)},g),w=ue(ue(ue(ue({},f),d),{},{fill:"none",stroke:d.fill},p),{},{index:y,points:[se(d.cx,d.cy,d.outerRadius,b),g]}),m=c;return G(c)&&G(s)?m="value":G(c)&&(m=s),A.createElement(J,{key:"label-".concat(d.startAngle,"-").concat(d.endAngle,"-").concat(d.midAngle,"-").concat(y)},u&&t.renderLabelLineItem(u,w,"line"),t.renderLabelItem(o,x,pe(d,m)))});return A.createElement(J,{className:"recharts-pie-labels"},v)}},{key:"renderSectorsStatically",value:function(n){var i=this,a=this.props,o=a.activeShape,u=a.blendStroke,c=a.inactiveShape;return n.map(function(s,f){if((s==null?void 0:s.startAngle)===0&&(s==null?void 0:s.endAngle)===0&&n.length!==1)return null;var l=i.isActiveIndex(f),p=c&&i.hasActiveIndex()?c:null,h=l?o:p,v=ue(ue({},s),{},{stroke:u?s.fill:s.stroke,tabIndex:-1});return A.createElement(J,Pr({ref:function(y){y&&!i.sectorRefs.includes(y)&&i.sectorRefs.push(y)},tabIndex:-1,className:"recharts-pie-sector"},Lt(i.props,s,f),{key:"sector-".concat(s==null?void 0:s.startAngle,"-").concat(s==null?void 0:s.endAngle,"-").concat(s.midAngle,"-").concat(f)}),A.createElement(ja,Pr({option:h,isActive:l,shapeType:"sector"},v)))})}},{key:"renderSectorsWithAnimation",value:function(){var n=this,i=this.props,a=i.sectors,o=i.isAnimationActive,u=i.animationBegin,c=i.animationDuration,s=i.animationEasing,f=i.animationId,l=this.state,p=l.prevSectors,h=l.prevIsAnimationActive;return A.createElement(Je,{begin:u,duration:c,isActive:o,easing:s,from:{t:0},to:{t:1},key:"pie-".concat(f,"-").concat(h),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(v){var d=v.t,y=[],b=a&&a[0],g=b.startAngle;return a.forEach(function(x,w){var m=p&&p[w],O=w>0?Ke(x,"paddingAngle",0):0;if(m){var _=de(m.endAngle-m.startAngle,x.endAngle-x.startAngle),S=ue(ue({},x),{},{startAngle:g+O,endAngle:g+_(d)+O});y.push(S),g=S.endAngle}else{var T=x.endAngle,j=x.startAngle,P=de(0,T-j),E=P(d),$=ue(ue({},x),{},{startAngle:g+O,endAngle:g+E+O});y.push($),g=$.endAngle}}),A.createElement(J,null,n.renderSectorsStatically(y))})}},{key:"attachKeyboardHandlers",value:function(n){var i=this;n.onkeydown=function(a){if(!a.altKey)switch(a.key){case"ArrowLeft":{var o=++i.state.sectorToFocus%i.sectorRefs.length;i.sectorRefs[o].focus(),i.setState({sectorToFocus:o});break}case"ArrowRight":{var u=--i.state.sectorToFocus<0?i.sectorRefs.length-1:i.state.sectorToFocus%i.sectorRefs.length;i.sectorRefs[u].focus(),i.setState({sectorToFocus:u});break}case"Escape":{i.sectorRefs[i.state.sectorToFocus].blur(),i.setState({sectorToFocus:0});break}}}}},{key:"renderSectors",value:function(){var n=this.props,i=n.sectors,a=n.isAnimationActive,o=this.state.prevSectors;return a&&i&&i.length&&(!o||!Bt(o,i))?this.renderSectorsWithAnimation():this.renderSectorsStatically(i)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var n=this,i=this.props,a=i.hide,o=i.sectors,u=i.className,c=i.label,s=i.cx,f=i.cy,l=i.innerRadius,p=i.outerRadius,h=i.isAnimationActive,v=this.state.isAnimationFinished;if(a||!o||!o.length||!N(s)||!N(f)||!N(l)||!N(p))return null;var d=Z("recharts-pie",u);return A.createElement(J,{tabIndex:this.props.rootTabIndex,className:d,ref:function(b){n.pieRef=b}},this.renderSectors(),c&&this.renderLabels(o),Pe.renderCallByParent(this.props,null,!1),(!h||v)&&ot.renderCallByParent(this.props,o,!1))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return i.prevIsAnimationActive!==n.isAnimationActive?{prevIsAnimationActive:n.isAnimationActive,prevAnimationId:n.animationId,curSectors:n.sectors,prevSectors:[],isAnimationFinished:!0}:n.isAnimationActive&&n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curSectors:n.sectors,prevSectors:i.curSectors,isAnimationFinished:!0}:n.sectors!==i.curSectors?{curSectors:n.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(n,i){return n>i?"start":n<i?"end":"middle"}},{key:"renderLabelLineItem",value:function(n,i,a){if(A.isValidElement(n))return A.cloneElement(n,i);if(H(n))return n(i);var o=Z("recharts-pie-label-line",typeof n!="boolean"?n.className:"");return A.createElement(qt,Pr({},i,{key:a,type:"linear",className:o}))}},{key:"renderLabelItem",value:function(n,i,a){if(A.isValidElement(n))return A.cloneElement(n,i);var o=a;if(H(n)&&(o=n(i),A.isValidElement(o)))return o;var u=Z("recharts-pie-label-text",typeof n!="boolean"&&!H(n)?n.className:"");return A.createElement(lr,Pr({},i,{alignmentBaseline:"middle",className:u}),o)}}])}(q.PureComponent);Hi=Ht;Xe(Ht,"displayName","Pie");Xe(Ht,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!Et.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0});Xe(Ht,"parseDeltaAngle",function(e,t){var r=Ie(t-e),n=Math.min(Math.abs(t-e),360);return r*n});Xe(Ht,"getRealPieData",function(e){var t=e.data,r=e.children,n=U(e,!1),i=Re(r,uo);return t&&t.length?t.map(function(a,o){return ue(ue(ue({payload:a},n),a),i&&i[o]&&i[o].props)}):i&&i.length?i.map(function(a){return ue(ue({},n),a.props)}):[]});Xe(Ht,"parseCoordinateOfPie",function(e,t){var r=t.top,n=t.left,i=t.width,a=t.height,o=wO(i,a),u=n+Ce(e.cx,i,i/2),c=r+Ce(e.cy,a,a/2),s=Ce(e.innerRadius,o,0),f=Ce(e.outerRadius,o,o*.8),l=e.maxRadius||Math.sqrt(i*i+a*a)/2;return{cx:u,cy:c,innerRadius:s,outerRadius:f,maxRadius:l}});Xe(Ht,"getComposedData",function(e){var t=e.item,r=e.offset,n=t.type.defaultProps!==void 0?ue(ue({},t.type.defaultProps),t.props):t.props,i=Hi.getRealPieData(n);if(!i||!i.length)return null;var a=n.cornerRadius,o=n.startAngle,u=n.endAngle,c=n.paddingAngle,s=n.dataKey,f=n.nameKey,l=n.valueKey,p=n.tooltipType,h=Math.abs(n.minAngle),v=Hi.parseCoordinateOfPie(n,r),d=Hi.parseDeltaAngle(o,u),y=Math.abs(d),b=s;G(s)&&G(l)?(at(!1,`Use "dataKey" to specify the value of pie,
      the props "valueKey" will be deprecated in 1.1.0`),b="value"):G(s)&&(at(!1,`Use "dataKey" to specify the value of pie,
      the props "valueKey" will be deprecated in 1.1.0`),b=l);var g=i.filter(function(S){return pe(S,b,0)!==0}).length,x=(y>=360?g:g-1)*c,w=y-g*h-x,m=i.reduce(function(S,T){var j=pe(T,b,0);return S+(N(j)?j:0)},0),O;if(m>0){var _;O=i.map(function(S,T){var j=pe(S,b,0),P=pe(S,f,T),E=(N(j)?j:0)/m,$;T?$=_.endAngle+Ie(d)*c*(j!==0?1:0):$=o;var I=$+Ie(d)*((j!==0?h:0)+E*w),M=($+I)/2,k=(v.innerRadius+v.outerRadius)/2,R=[{name:P,value:j,payload:S,dataKey:b,type:p}],L=se(v.cx,v.cy,k,M);return _=ue(ue(ue({percent:E,cornerRadius:a,name:P,tooltipPayload:R,midAngle:M,middleRadius:k,tooltipPosition:L},S),v),{},{value:pe(S,b),startAngle:$,endAngle:I,payload:S,paddingAngle:Ie(d)*c}),_})}return ue(ue({},v),{},{sectors:O,data:i})});var dl,cb;function mR(){if(cb)return dl;cb=1;var e=Math.ceil,t=Math.max;function r(n,i,a,o){for(var u=-1,c=t(e((i-n)/(a||1)),0),s=Array(c);c--;)s[o?c:++u]=n,n+=a;return s}return dl=r,dl}var vl,sb;function BO(){if(sb)return vl;sb=1;var e=sx(),t=1/0,r=17976931348623157e292;function n(i){if(!i)return i===0?i:0;if(i=e(i),i===t||i===-1/0){var a=i<0?-1:1;return a*r}return i===i?i:0}return vl=n,vl}var yl,lb;function gR(){if(lb)return yl;lb=1;var e=mR(),t=oo(),r=BO();function n(i){return function(a,o,u){return u&&typeof u!="number"&&t(a,o,u)&&(o=u=void 0),a=r(a),o===void 0?(o=a,a=0):o=r(o),u=u===void 0?a<o?1:-1:r(u),e(a,o,u,i)}}return yl=n,yl}var ml,fb;function bR(){if(fb)return ml;fb=1;var e=gR(),t=e();return ml=t,ml}var xR=bR();const Ma=ae(xR);function di(e){"@babel/helpers - typeof";return di=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},di(e)}function pb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function hb(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?pb(Object(r),!0).forEach(function(n){FO(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function FO(e,t,r){return t=OR(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function OR(e){var t=wR(e,"string");return di(t)=="symbol"?t:t+""}function wR(e,t){if(di(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(di(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var _R=["Webkit","Moz","O","ms"],AR=function(t,r){var n=t.replace(/(\w)/,function(a){return a.toUpperCase()}),i=_R.reduce(function(a,o){return hb(hb({},a),{},FO({},o+n,r))},{});return i[t]=r,i};function Gr(e){"@babel/helpers - typeof";return Gr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Gr(e)}function Ia(){return Ia=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ia.apply(this,arguments)}function db(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function gl(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?db(Object(r),!0).forEach(function(n){ze(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):db(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function SR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function vb(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,WO(n.key),n)}}function PR(e,t,r){return t&&vb(e.prototype,t),r&&vb(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function TR(e,t,r){return t=Ca(t),ER(e,zO()?Reflect.construct(t,r||[],Ca(e).constructor):t.apply(e,r))}function ER(e,t){if(t&&(Gr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return jR(e)}function jR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function zO(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(zO=function(){return!!e})()}function Ca(e){return Ca=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ca(e)}function $R(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Df(e,t)}function Df(e,t){return Df=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Df(e,t)}function ze(e,t,r){return t=WO(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function WO(e){var t=MR(e,"string");return Gr(t)=="symbol"?t:t+""}function MR(e,t){if(Gr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Gr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var IR=function(t){var r=t.data,n=t.startIndex,i=t.endIndex,a=t.x,o=t.width,u=t.travellerWidth;if(!r||!r.length)return{};var c=r.length,s=Mn().domain(Ma(0,c)).range([a,a+o-u]),f=s.domain().map(function(l){return s(l)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:s(n),endX:s(i),scale:s,scaleValues:f}},yb=function(t){return t.changedTouches&&!!t.changedTouches.length},Vr=function(e){function t(r){var n;return SR(this,t),n=TR(this,t,[r]),ze(n,"handleDrag",function(i){n.leaveTimer&&(clearTimeout(n.leaveTimer),n.leaveTimer=null),n.state.isTravellerMoving?n.handleTravellerMove(i):n.state.isSlideMoving&&n.handleSlideDrag(i)}),ze(n,"handleTouchMove",function(i){i.changedTouches!=null&&i.changedTouches.length>0&&n.handleDrag(i.changedTouches[0])}),ze(n,"handleDragEnd",function(){n.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var i=n.props,a=i.endIndex,o=i.onDragEnd,u=i.startIndex;o==null||o({endIndex:a,startIndex:u})}),n.detachDragEndListener()}),ze(n,"handleLeaveWrapper",function(){(n.state.isTravellerMoving||n.state.isSlideMoving)&&(n.leaveTimer=window.setTimeout(n.handleDragEnd,n.props.leaveTimeOut))}),ze(n,"handleEnterSlideOrTraveller",function(){n.setState({isTextActive:!0})}),ze(n,"handleLeaveSlideOrTraveller",function(){n.setState({isTextActive:!1})}),ze(n,"handleSlideDragStart",function(i){var a=yb(i)?i.changedTouches[0]:i;n.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:a.pageX}),n.attachDragEndListener()}),n.travellerDragStartHandlers={startX:n.handleTravellerDragStart.bind(n,"startX"),endX:n.handleTravellerDragStart.bind(n,"endX")},n.state={},n}return $R(t,e),PR(t,[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(n){var i=n.startX,a=n.endX,o=this.state.scaleValues,u=this.props,c=u.gap,s=u.data,f=s.length-1,l=Math.min(i,a),p=Math.max(i,a),h=t.getIndexInRange(o,l),v=t.getIndexInRange(o,p);return{startIndex:h-h%c,endIndex:v===f?f:v-v%c}}},{key:"getTextOfTick",value:function(n){var i=this.props,a=i.data,o=i.tickFormatter,u=i.dataKey,c=pe(a[n],u,n);return H(o)?o(c,n):c}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(n){var i=this.state,a=i.slideMoveStartX,o=i.startX,u=i.endX,c=this.props,s=c.x,f=c.width,l=c.travellerWidth,p=c.startIndex,h=c.endIndex,v=c.onChange,d=n.pageX-a;d>0?d=Math.min(d,s+f-l-u,s+f-l-o):d<0&&(d=Math.max(d,s-o,s-u));var y=this.getIndex({startX:o+d,endX:u+d});(y.startIndex!==p||y.endIndex!==h)&&v&&v(y),this.setState({startX:o+d,endX:u+d,slideMoveStartX:n.pageX})}},{key:"handleTravellerDragStart",value:function(n,i){var a=yb(i)?i.changedTouches[0]:i;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:n,brushMoveStartX:a.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(n){var i=this.state,a=i.brushMoveStartX,o=i.movingTravellerId,u=i.endX,c=i.startX,s=this.state[o],f=this.props,l=f.x,p=f.width,h=f.travellerWidth,v=f.onChange,d=f.gap,y=f.data,b={startX:this.state.startX,endX:this.state.endX},g=n.pageX-a;g>0?g=Math.min(g,l+p-h-s):g<0&&(g=Math.max(g,l-s)),b[o]=s+g;var x=this.getIndex(b),w=x.startIndex,m=x.endIndex,O=function(){var S=y.length-1;return o==="startX"&&(u>c?w%d===0:m%d===0)||u<c&&m===S||o==="endX"&&(u>c?m%d===0:w%d===0)||u>c&&m===S};this.setState(ze(ze({},o,s+g),"brushMoveStartX",n.pageX),function(){v&&O()&&v(x)})}},{key:"handleTravellerMoveKeyboard",value:function(n,i){var a=this,o=this.state,u=o.scaleValues,c=o.startX,s=o.endX,f=this.state[i],l=u.indexOf(f);if(l!==-1){var p=l+n;if(!(p===-1||p>=u.length)){var h=u[p];i==="startX"&&h>=s||i==="endX"&&h<=c||this.setState(ze({},i,h),function(){a.props.onChange(a.getIndex({startX:a.state.startX,endX:a.state.endX}))})}}}},{key:"renderBackground",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.fill,s=n.stroke;return A.createElement("rect",{stroke:s,fill:c,x:i,y:a,width:o,height:u})}},{key:"renderPanorama",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.data,s=n.children,f=n.padding,l=q.Children.only(s);return l?A.cloneElement(l,{x:i,y:a,width:o,height:u,margin:f,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(n,i){var a,o,u=this,c=this.props,s=c.y,f=c.travellerWidth,l=c.height,p=c.traveller,h=c.ariaLabel,v=c.data,d=c.startIndex,y=c.endIndex,b=Math.max(n,this.props.x),g=gl(gl({},U(this.props,!1)),{},{x:b,y:s,width:f,height:l}),x=h||"Min value: ".concat((a=v[d])===null||a===void 0?void 0:a.name,", Max value: ").concat((o=v[y])===null||o===void 0?void 0:o.name);return A.createElement(J,{tabIndex:0,role:"slider","aria-label":x,"aria-valuenow":n,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[i],onTouchStart:this.travellerDragStartHandlers[i],onKeyDown:function(m){["ArrowLeft","ArrowRight"].includes(m.key)&&(m.preventDefault(),m.stopPropagation(),u.handleTravellerMoveKeyboard(m.key==="ArrowRight"?1:-1,i))},onFocus:function(){u.setState({isTravellerFocused:!0})},onBlur:function(){u.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},t.renderTraveller(p,g))}},{key:"renderSlide",value:function(n,i){var a=this.props,o=a.y,u=a.height,c=a.stroke,s=a.travellerWidth,f=Math.min(n,i)+s,l=Math.max(Math.abs(i-n)-s,0);return A.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:c,fillOpacity:.2,x:f,y:o,width:l,height:u})}},{key:"renderText",value:function(){var n=this.props,i=n.startIndex,a=n.endIndex,o=n.y,u=n.height,c=n.travellerWidth,s=n.stroke,f=this.state,l=f.startX,p=f.endX,h=5,v={pointerEvents:"none",fill:s};return A.createElement(J,{className:"recharts-brush-texts"},A.createElement(lr,Ia({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,p)-h,y:o+u/2},v),this.getTextOfTick(i)),A.createElement(lr,Ia({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,p)+c+h,y:o+u/2},v),this.getTextOfTick(a)))}},{key:"render",value:function(){var n=this.props,i=n.data,a=n.className,o=n.children,u=n.x,c=n.y,s=n.width,f=n.height,l=n.alwaysShowText,p=this.state,h=p.startX,v=p.endX,d=p.isTextActive,y=p.isSlideMoving,b=p.isTravellerMoving,g=p.isTravellerFocused;if(!i||!i.length||!N(u)||!N(c)||!N(s)||!N(f)||s<=0||f<=0)return null;var x=Z("recharts-brush",a),w=A.Children.count(o)===1,m=AR("userSelect","none");return A.createElement(J,{className:x,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:m},this.renderBackground(),w&&this.renderPanorama(),this.renderSlide(h,v),this.renderTravellerLayer(h,"startX"),this.renderTravellerLayer(v,"endX"),(d||y||b||g||l)&&this.renderText())}}],[{key:"renderDefaultTraveller",value:function(n){var i=n.x,a=n.y,o=n.width,u=n.height,c=n.stroke,s=Math.floor(a+u/2)-1;return A.createElement(A.Fragment,null,A.createElement("rect",{x:i,y:a,width:o,height:u,fill:c,stroke:"none"}),A.createElement("line",{x1:i+1,y1:s,x2:i+o-1,y2:s,fill:"none",stroke:"#fff"}),A.createElement("line",{x1:i+1,y1:s+2,x2:i+o-1,y2:s+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(n,i){var a;return A.isValidElement(n)?a=A.cloneElement(n,i):H(n)?a=n(i):a=t.renderDefaultTraveller(i),a}},{key:"getDerivedStateFromProps",value:function(n,i){var a=n.data,o=n.width,u=n.x,c=n.travellerWidth,s=n.updateId,f=n.startIndex,l=n.endIndex;if(a!==i.prevData||s!==i.prevUpdateId)return gl({prevData:a,prevTravellerWidth:c,prevUpdateId:s,prevX:u,prevWidth:o},a&&a.length?IR({data:a,width:o,x:u,travellerWidth:c,startIndex:f,endIndex:l}):{scale:null,scaleValues:null});if(i.scale&&(o!==i.prevWidth||u!==i.prevX||c!==i.prevTravellerWidth)){i.scale.range([u,u+o-c]);var p=i.scale.domain().map(function(h){return i.scale(h)});return{prevData:a,prevTravellerWidth:c,prevUpdateId:s,prevX:u,prevWidth:o,startX:i.scale(n.startIndex),endX:i.scale(n.endIndex),scaleValues:p}}return null}},{key:"getIndexInRange",value:function(n,i){for(var a=n.length,o=0,u=a-1;u-o>1;){var c=Math.floor((o+u)/2);n[c]>i?u=c:o=c}return i>=n[u]?u:o}}])}(q.PureComponent);ze(Vr,"displayName","Brush");ze(Vr,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var bl,mb;function CR(){if(mb)return bl;mb=1;var e=Ap();function t(r,n){var i;return e(r,function(a,o,u){return i=n(a,o,u),!i}),!!i}return bl=t,bl}var xl,gb;function kR(){if(gb)return xl;gb=1;var e=B0(),t=dt(),r=CR(),n=Le(),i=oo();function a(o,u,c){var s=n(o)?e:r;return c&&i(o,u,c)&&(u=void 0),s(o,t(u,3))}return xl=a,xl}var RR=kR();const DR=ae(RR);var ft=function(t,r){var n=t.alwaysShow,i=t.ifOverflow;return n&&(i="extendDomain"),i===r},Ol,bb;function NR(){if(bb)return Ol;bb=1;var e=ix();function t(r,n,i){n=="__proto__"&&e?e(r,n,{configurable:!0,enumerable:!0,value:i,writable:!0}):r[n]=i}return Ol=t,Ol}var wl,xb;function qR(){if(xb)return wl;xb=1;var e=NR(),t=rx(),r=dt();function n(i,a){var o={};return a=r(a,3),t(i,function(u,c,s){e(o,c,a(u,c,s))}),o}return wl=n,wl}var LR=qR();const BR=ae(LR);var _l,Ob;function FR(){if(Ob)return _l;Ob=1;function e(t,r){for(var n=-1,i=t==null?0:t.length;++n<i;)if(!r(t[n],n,t))return!1;return!0}return _l=e,_l}var Al,wb;function zR(){if(wb)return Al;wb=1;var e=Ap();function t(r,n){var i=!0;return e(r,function(a,o,u){return i=!!n(a,o,u),i}),i}return Al=t,Al}var Sl,_b;function WR(){if(_b)return Sl;_b=1;var e=FR(),t=zR(),r=dt(),n=Le(),i=oo();function a(o,u,c){var s=n(o)?e:t;return c&&i(o,u,c)&&(u=void 0),s(o,r(u,3))}return Sl=a,Sl}var UR=WR();const UO=ae(UR);var KR=["x","y"];function vi(e){"@babel/helpers - typeof";return vi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},vi(e)}function Nf(){return Nf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Nf.apply(this,arguments)}function Ab(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Pn(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ab(Object(r),!0).forEach(function(n){HR(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ab(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function HR(e,t,r){return t=GR(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function GR(e){var t=VR(e,"string");return vi(t)=="symbol"?t:t+""}function VR(e,t){if(vi(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(vi(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function XR(e,t){if(e==null)return{};var r=YR(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function YR(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function ZR(e,t){var r=e.x,n=e.y,i=XR(e,KR),a="".concat(r),o=parseInt(a,10),u="".concat(n),c=parseInt(u,10),s="".concat(t.height||i.height),f=parseInt(s,10),l="".concat(t.width||i.width),p=parseInt(l,10);return Pn(Pn(Pn(Pn(Pn({},t),i),o?{x:o}:{}),c?{y:c}:{}),{},{height:f,width:p,name:t.name,radius:t.radius})}function Sb(e){return A.createElement(ja,Nf({shapeType:"rectangle",propTransformer:ZR,activeClassName:"recharts-active-bar"},e))}var JR=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return function(n,i){if(typeof t=="number")return t;var a=typeof n=="number";return a?t(n,i):(a||sr(),r)}},QR=["value","background"],KO;function Xr(e){"@babel/helpers - typeof";return Xr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Xr(e)}function eD(e,t){if(e==null)return{};var r=tD(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function tD(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function ka(){return ka=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ka.apply(this,arguments)}function Pb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ge(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Pb(Object(r),!0).forEach(function(n){Rt(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Pb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function rD(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Tb(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,GO(n.key),n)}}function nD(e,t,r){return t&&Tb(e.prototype,t),r&&Tb(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function iD(e,t,r){return t=Ra(t),aD(e,HO()?Reflect.construct(t,r||[],Ra(e).constructor):t.apply(e,r))}function aD(e,t){if(t&&(Xr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return oD(e)}function oD(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function HO(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(HO=function(){return!!e})()}function Ra(e){return Ra=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ra(e)}function uD(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&qf(e,t)}function qf(e,t){return qf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},qf(e,t)}function Rt(e,t,r){return t=GO(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function GO(e){var t=cD(e,"string");return Xr(t)=="symbol"?t:t+""}function cD(e,t){if(Xr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Xr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var mr=function(e){function t(){var r;rD(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=iD(this,t,[].concat(i)),Rt(r,"state",{isAnimationFinished:!1}),Rt(r,"id",zt("recharts-bar-")),Rt(r,"handleAnimationEnd",function(){var o=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),o&&o()}),Rt(r,"handleAnimationStart",function(){var o=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),o&&o()}),r}return uD(t,e),nD(t,[{key:"renderRectanglesStatically",value:function(n){var i=this,a=this.props,o=a.shape,u=a.dataKey,c=a.activeIndex,s=a.activeBar,f=U(this.props,!1);return n&&n.map(function(l,p){var h=p===c,v=h?s:o,d=ge(ge(ge({},f),l),{},{isActive:h,option:v,index:p,dataKey:u,onAnimationStart:i.handleAnimationStart,onAnimationEnd:i.handleAnimationEnd});return A.createElement(J,ka({className:"recharts-bar-rectangle"},Lt(i.props,l,p),{key:"rectangle-".concat(l==null?void 0:l.x,"-").concat(l==null?void 0:l.y,"-").concat(l==null?void 0:l.value,"-").concat(p)}),A.createElement(Sb,d))})}},{key:"renderRectanglesWithAnimation",value:function(){var n=this,i=this.props,a=i.data,o=i.layout,u=i.isAnimationActive,c=i.animationBegin,s=i.animationDuration,f=i.animationEasing,l=i.animationId,p=this.state.prevData;return A.createElement(Je,{begin:c,duration:s,isActive:u,easing:f,from:{t:0},to:{t:1},key:"bar-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(h){var v=h.t,d=a.map(function(y,b){var g=p&&p[b];if(g){var x=de(g.x,y.x),w=de(g.y,y.y),m=de(g.width,y.width),O=de(g.height,y.height);return ge(ge({},y),{},{x:x(v),y:w(v),width:m(v),height:O(v)})}if(o==="horizontal"){var _=de(0,y.height),S=_(v);return ge(ge({},y),{},{y:y.y+y.height-S,height:S})}var T=de(0,y.width),j=T(v);return ge(ge({},y),{},{width:j})});return A.createElement(J,null,n.renderRectanglesStatically(d))})}},{key:"renderRectangles",value:function(){var n=this.props,i=n.data,a=n.isAnimationActive,o=this.state.prevData;return a&&i&&i.length&&(!o||!Bt(o,i))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(i)}},{key:"renderBackground",value:function(){var n=this,i=this.props,a=i.data,o=i.dataKey,u=i.activeIndex,c=U(this.props.background,!1);return a.map(function(s,f){s.value;var l=s.background,p=eD(s,QR);if(!l)return null;var h=ge(ge(ge(ge(ge({},p),{},{fill:"#eee"},l),c),Lt(n.props,s,f)),{},{onAnimationStart:n.handleAnimationStart,onAnimationEnd:n.handleAnimationEnd,dataKey:o,index:f,className:"recharts-bar-background-rectangle"});return A.createElement(Sb,ka({key:"background-bar-".concat(f),option:n.props.background,isActive:f===u},h))})}},{key:"renderErrorBar",value:function(n,i){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.data,u=a.xAxis,c=a.yAxis,s=a.layout,f=a.children,l=Re(f,yn);if(!l)return null;var p=s==="vertical"?o[0].height/2:o[0].width/2,h=function(y,b){var g=Array.isArray(y.value)?y.value[1]:y.value;return{x:y.x,y:y.y,value:g,errorVal:pe(y,b)}},v={clipPath:n?"url(#clipPath-".concat(i,")"):null};return A.createElement(J,v,l.map(function(d){return A.cloneElement(d,{key:"error-bar-".concat(i,"-").concat(d.props.dataKey),data:o,xAxis:u,yAxis:c,layout:s,offset:p,dataPointFormatter:h})}))}},{key:"render",value:function(){var n=this.props,i=n.hide,a=n.data,o=n.className,u=n.xAxis,c=n.yAxis,s=n.left,f=n.top,l=n.width,p=n.height,h=n.isAnimationActive,v=n.background,d=n.id;if(i||!a||!a.length)return null;var y=this.state.isAnimationFinished,b=Z("recharts-bar",o),g=u&&u.allowDataOverflow,x=c&&c.allowDataOverflow,w=g||x,m=G(d)?this.id:d;return A.createElement(J,{className:b},g||x?A.createElement("defs",null,A.createElement("clipPath",{id:"clipPath-".concat(m)},A.createElement("rect",{x:g?s:s-l/2,y:x?f:f-p/2,width:g?l:l*2,height:x?p:p*2}))):null,A.createElement(J,{className:"recharts-bar-rectangles",clipPath:w?"url(#clipPath-".concat(m,")"):null},v?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(w,m),(!h||y)&&ot.renderCallByParent(this.props,a))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curData:n.data,prevData:i.curData}:n.data!==i.curData?{curData:n.data}:null}}])}(q.PureComponent);KO=mr;Rt(mr,"displayName","Bar");Rt(mr,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!Et.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"});Rt(mr,"getComposedData",function(e){var t=e.props,r=e.item,n=e.barPosition,i=e.bandSize,a=e.xAxis,o=e.yAxis,u=e.xAxisTicks,c=e.yAxisTicks,s=e.stackedData,f=e.dataStartIndex,l=e.displayedData,p=e.offset,h=B$(n,r);if(!h)return null;var v=t.layout,d=r.type.defaultProps,y=d!==void 0?ge(ge({},d),r.props):r.props,b=y.dataKey,g=y.children,x=y.minPointSize,w=v==="horizontal"?o:a,m=s?w.scale.domain():null,O=G$({numericAxis:w}),_=Re(g,uo),S=l.map(function(T,j){var P,E,$,I,M,k;s?P=F$(s[f+j],m):(P=pe(T,b),Array.isArray(P)||(P=[O,P]));var R=JR(x,KO.defaultProps.minPointSize)(P[1],j);if(v==="horizontal"){var L,B=[o.scale(P[0]),o.scale(P[1])],K=B[0],X=B[1];E=rg({axis:a,ticks:u,bandSize:i,offset:h.offset,entry:T,index:j}),$=(L=X!=null?X:K)!==null&&L!==void 0?L:void 0,I=h.size;var z=K-X;if(M=Number.isNaN(z)?0:z,k={x:E,y:o.y,width:I,height:o.height},Math.abs(R)>0&&Math.abs(M)<Math.abs(R)){var Y=Ie(M||R)*(Math.abs(R)-Math.abs(M));$-=Y,M+=Y}}else{var le=[a.scale(P[0]),a.scale(P[1])],me=le[0],Be=le[1];if(E=me,$=rg({axis:o,ticks:c,bandSize:i,offset:h.offset,entry:T,index:j}),I=Be-me,M=h.size,k={x:a.x,y:$,width:a.width,height:M},Math.abs(R)>0&&Math.abs(I)<Math.abs(R)){var Gt=Ie(I||R)*(Math.abs(R)-Math.abs(I));I+=Gt}}return ge(ge(ge({},T),{},{x:E,y:$,width:I,height:M,value:s?P:P[1],payload:T,background:k},_&&_[j]&&_[j].props),{},{tooltipPayload:[xO(r,T)],tooltipPosition:{x:E+I/2,y:$+M/2}})});return ge({data:S,layout:v},p)});function yi(e){"@babel/helpers - typeof";return yi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},yi(e)}function sD(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Eb(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,VO(n.key),n)}}function lD(e,t,r){return t&&Eb(e.prototype,t),r&&Eb(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function jb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function rt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?jb(Object(r),!0).forEach(function(n){So(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):jb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function So(e,t,r){return t=VO(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function VO(e){var t=fD(e,"string");return yi(t)=="symbol"?t:t+""}function fD(e,t){if(yi(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(yi(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Qp=function(t,r,n,i,a){var o=t.width,u=t.height,c=t.layout,s=t.children,f=Object.keys(r),l={left:n.left,leftMirror:n.left,right:o-n.right,rightMirror:o-n.right,top:n.top,topMirror:n.top,bottom:u-n.bottom,bottomMirror:u-n.bottom},p=!!We(s,mr);return f.reduce(function(h,v){var d=r[v],y=d.orientation,b=d.domain,g=d.padding,x=g===void 0?{}:g,w=d.mirror,m=d.reversed,O="".concat(y).concat(w?"Mirror":""),_,S,T,j,P;if(d.type==="number"&&(d.padding==="gap"||d.padding==="no-gap")){var E=b[1]-b[0],$=1/0,I=d.categoricalDomain.sort();if(I.forEach(function(le,me){me>0&&($=Math.min((le||0)-(I[me-1]||0),$))}),Number.isFinite($)){var M=$/E,k=d.layout==="vertical"?n.height:n.width;if(d.padding==="gap"&&(_=M*k/2),d.padding==="no-gap"){var R=Ce(t.barCategoryGap,M*k),L=M*k/2;_=L-R-(L-R)/k*R}}}i==="xAxis"?S=[n.left+(x.left||0)+(_||0),n.left+n.width-(x.right||0)-(_||0)]:i==="yAxis"?S=c==="horizontal"?[n.top+n.height-(x.bottom||0),n.top+(x.top||0)]:[n.top+(x.top||0)+(_||0),n.top+n.height-(x.bottom||0)-(_||0)]:S=d.range,m&&(S=[S[1],S[0]]);var B=yO(d,a,p),K=B.scale,X=B.realScaleType;K.domain(b).range(S),mO(K);var z=gO(K,rt(rt({},d),{},{realScaleType:X}));i==="xAxis"?(P=y==="top"&&!w||y==="bottom"&&w,T=n.left,j=l[O]-P*d.height):i==="yAxis"&&(P=y==="left"&&!w||y==="right"&&w,T=l[O]-P*d.width,j=n.top);var Y=rt(rt(rt({},d),z),{},{realScaleType:X,x:T,y:j,scale:K,width:i==="xAxis"?n.width:d.width,height:i==="yAxis"?n.height:d.height});return Y.bandSize=ga(Y,z),!d.hide&&i==="xAxis"?l[O]+=(P?-1:1)*Y.height:d.hide||(l[O]+=(P?-1:1)*Y.width),rt(rt({},h),{},So({},v,Y))},{})},XO=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return{x:Math.min(n,a),y:Math.min(i,o),width:Math.abs(a-n),height:Math.abs(o-i)}},pD=function(t){var r=t.x1,n=t.y1,i=t.x2,a=t.y2;return XO({x:r,y:n},{x:i,y:a})},YO=function(){function e(t){sD(this,e),this.scale=t}return lD(e,[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(r){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=n.bandAware,a=n.position;if(r!==void 0){if(a)switch(a){case"start":return this.scale(r);case"middle":{var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+o}case"end":{var u=this.bandwidth?this.bandwidth():0;return this.scale(r)+u}default:return this.scale(r)}if(i){var c=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+c}return this.scale(r)}}},{key:"isInRange",value:function(r){var n=this.range(),i=n[0],a=n[n.length-1];return i<=a?r>=i&&r<=a:r>=a&&r<=i}}],[{key:"create",value:function(r){return new e(r)}}])}();So(YO,"EPS",1e-4);var eh=function(t){var r=Object.keys(t).reduce(function(n,i){return rt(rt({},n),{},So({},i,YO.create(t[i])))},{});return rt(rt({},r),{},{apply:function(i){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=a.bandAware,u=a.position;return BR(i,function(c,s){return r[s].apply(c,{bandAware:o,position:u})})},isInRange:function(i){return UO(i,function(a,o){return r[o].isInRange(a)})}})};function hD(e){return(e%180+180)%180}var dD=function(t){var r=t.width,n=t.height,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=hD(i),o=a*Math.PI/180,u=Math.atan(n/r),c=o>u&&o<Math.PI-u?n/Math.sin(o):r/Math.cos(o);return Math.abs(c)},Pl,$b;function vD(){if($b)return Pl;$b=1;var e=dt(),t=wi(),r=io();function n(i){return function(a,o,u){var c=Object(a);if(!t(a)){var s=e(o,3);a=r(a),o=function(l){return s(c[l],l,c)}}var f=i(a,o,u);return f>-1?c[s?a[f]:f]:void 0}}return Pl=n,Pl}var Tl,Mb;function yD(){if(Mb)return Tl;Mb=1;var e=BO();function t(r){var n=e(r),i=n%1;return n===n?i?n-i:n:0}return Tl=t,Tl}var El,Ib;function mD(){if(Ib)return El;Ib=1;var e=Z0(),t=dt(),r=yD(),n=Math.max;function i(a,o,u){var c=a==null?0:a.length;if(!c)return-1;var s=u==null?0:r(u);return s<0&&(s=n(c+s,0)),e(a,t(o,3),s)}return El=i,El}var jl,Cb;function gD(){if(Cb)return jl;Cb=1;var e=vD(),t=mD(),r=e(t);return jl=r,jl}var bD=gD();const xD=ae(bD);var OD=p0();const wD=ae(OD);var _D=wD(function(e){return{x:e.left,y:e.top,width:e.width,height:e.height}},function(e){return["l",e.left,"t",e.top,"w",e.width,"h",e.height].join("")}),th=q.createContext(void 0),rh=q.createContext(void 0),ZO=q.createContext(void 0),JO=q.createContext({}),QO=q.createContext(void 0),ew=q.createContext(0),tw=q.createContext(0),kb=function(t){var r=t.state,n=r.xAxisMap,i=r.yAxisMap,a=r.offset,o=t.clipPathId,u=t.children,c=t.width,s=t.height,f=_D(a);return A.createElement(th.Provider,{value:n},A.createElement(rh.Provider,{value:i},A.createElement(JO.Provider,{value:a},A.createElement(ZO.Provider,{value:f},A.createElement(QO.Provider,{value:o},A.createElement(ew.Provider,{value:s},A.createElement(tw.Provider,{value:c},u)))))))},AD=function(){return q.useContext(QO)},rw=function(t){var r=q.useContext(th);r==null&&sr();var n=r[t];return n==null&&sr(),n},SD=function(){var t=q.useContext(th);return Ct(t)},PD=function(){var t=q.useContext(rh),r=xD(t,function(n){return UO(n.domain,Number.isFinite)});return r||Ct(t)},nw=function(t){var r=q.useContext(rh);r==null&&sr();var n=r[t];return n==null&&sr(),n},TD=function(){var t=q.useContext(ZO);return t},ED=function(){return q.useContext(JO)},nh=function(){return q.useContext(tw)},ih=function(){return q.useContext(ew)};function Yr(e){"@babel/helpers - typeof";return Yr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Yr(e)}function jD(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function $D(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,aw(n.key),n)}}function MD(e,t,r){return t&&$D(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function ID(e,t,r){return t=Da(t),CD(e,iw()?Reflect.construct(t,r||[],Da(e).constructor):t.apply(e,r))}function CD(e,t){if(t&&(Yr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return kD(e)}function kD(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function iw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(iw=function(){return!!e})()}function Da(e){return Da=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Da(e)}function RD(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Lf(e,t)}function Lf(e,t){return Lf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Lf(e,t)}function Rb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Db(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Rb(Object(r),!0).forEach(function(n){ah(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Rb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ah(e,t,r){return t=aw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function aw(e){var t=DD(e,"string");return Yr(t)=="symbol"?t:t+""}function DD(e,t){if(Yr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Yr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function ND(e,t){return FD(e)||BD(e,t)||LD(e,t)||qD()}function qD(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function LD(e,t){if(e){if(typeof e=="string")return Nb(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Nb(e,t)}}function Nb(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function BD(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function FD(e){if(Array.isArray(e))return e}function Bf(){return Bf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Bf.apply(this,arguments)}var zD=function(t,r){var n;return A.isValidElement(t)?n=A.cloneElement(t,r):H(t)?n=t(r):n=A.createElement("line",Bf({},r,{className:"recharts-reference-line-line"})),n},WD=function(t,r,n,i,a,o,u,c,s){var f=a.x,l=a.y,p=a.width,h=a.height;if(n){var v=s.y,d=t.y.apply(v,{position:o});if(ft(s,"discard")&&!t.y.isInRange(d))return null;var y=[{x:f+p,y:d},{x:f,y:d}];return c==="left"?y.reverse():y}if(r){var b=s.x,g=t.x.apply(b,{position:o});if(ft(s,"discard")&&!t.x.isInRange(g))return null;var x=[{x:g,y:l+h},{x:g,y:l}];return u==="top"?x.reverse():x}if(i){var w=s.segment,m=w.map(function(O){return t.apply(O,{position:o})});return ft(s,"discard")&&DR(m,function(O){return!t.isInRange(O)})?null:m}return null};function UD(e){var t=e.x,r=e.y,n=e.segment,i=e.xAxisId,a=e.yAxisId,o=e.shape,u=e.className,c=e.alwaysShow,s=AD(),f=rw(i),l=nw(a),p=TD();if(!s||!p)return null;at(c===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var h=eh({x:f.scale,y:l.scale}),v=_e(t),d=_e(r),y=n&&n.length===2,b=WD(h,v,d,y,p,e.position,f.orientation,l.orientation,e);if(!b)return null;var g=ND(b,2),x=g[0],w=x.x,m=x.y,O=g[1],_=O.x,S=O.y,T=ft(e,"hidden")?"url(#".concat(s,")"):void 0,j=Db(Db({clipPath:T},U(e,!0)),{},{x1:w,y1:m,x2:_,y2:S});return A.createElement(J,{className:Z("recharts-reference-line",u)},zD(o,j),Pe.renderCallByParent(e,pD({x1:w,y1:m,x2:_,y2:S})))}var oh=function(e){function t(){return jD(this,t),ID(this,t,arguments)}return RD(t,e),MD(t,[{key:"render",value:function(){return A.createElement(UD,this.props)}}])}(A.Component);ah(oh,"displayName","ReferenceLine");ah(oh,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function Ff(){return Ff=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ff.apply(this,arguments)}function Zr(e){"@babel/helpers - typeof";return Zr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Zr(e)}function qb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Lb(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?qb(Object(r),!0).forEach(function(n){Po(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):qb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function KD(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function HD(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,uw(n.key),n)}}function GD(e,t,r){return t&&HD(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function VD(e,t,r){return t=Na(t),XD(e,ow()?Reflect.construct(t,r||[],Na(e).constructor):t.apply(e,r))}function XD(e,t){if(t&&(Zr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return YD(e)}function YD(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ow(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ow=function(){return!!e})()}function Na(e){return Na=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Na(e)}function ZD(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&zf(e,t)}function zf(e,t){return zf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},zf(e,t)}function Po(e,t,r){return t=uw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function uw(e){var t=JD(e,"string");return Zr(t)=="symbol"?t:t+""}function JD(e,t){if(Zr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Zr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var QD=function(t){var r=t.x,n=t.y,i=t.xAxis,a=t.yAxis,o=eh({x:i.scale,y:a.scale}),u=o.apply({x:r,y:n},{bandAware:!0});return ft(t,"discard")&&!o.isInRange(u)?null:u},To=function(e){function t(){return KD(this,t),VD(this,t,arguments)}return ZD(t,e),GD(t,[{key:"render",value:function(){var n=this.props,i=n.x,a=n.y,o=n.r,u=n.alwaysShow,c=n.clipPathId,s=_e(i),f=_e(a);if(at(u===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!s||!f)return null;var l=QD(this.props);if(!l)return null;var p=l.x,h=l.y,v=this.props,d=v.shape,y=v.className,b=ft(this.props,"hidden")?"url(#".concat(c,")"):void 0,g=Lb(Lb({clipPath:b},U(this.props,!0)),{},{cx:p,cy:h});return A.createElement(J,{className:Z("recharts-reference-dot",y)},t.renderDot(d,g),Pe.renderCallByParent(this.props,{x:p-o,y:h-o,width:2*o,height:2*o}))}}])}(A.Component);Po(To,"displayName","ReferenceDot");Po(To,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1});Po(To,"renderDot",function(e,t){var r;return A.isValidElement(e)?r=A.cloneElement(e,t):H(e)?r=e(t):r=A.createElement(Ti,Ff({},t,{cx:t.cx,cy:t.cy,className:"recharts-reference-dot-dot"})),r});function Wf(){return Wf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Wf.apply(this,arguments)}function Jr(e){"@babel/helpers - typeof";return Jr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Jr(e)}function Bb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Fb(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Bb(Object(r),!0).forEach(function(n){Eo(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Bb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function eN(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function tN(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,sw(n.key),n)}}function rN(e,t,r){return t&&tN(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function nN(e,t,r){return t=qa(t),iN(e,cw()?Reflect.construct(t,r||[],qa(e).constructor):t.apply(e,r))}function iN(e,t){if(t&&(Jr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return aN(e)}function aN(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function cw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(cw=function(){return!!e})()}function qa(e){return qa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},qa(e)}function oN(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Uf(e,t)}function Uf(e,t){return Uf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Uf(e,t)}function Eo(e,t,r){return t=sw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function sw(e){var t=uN(e,"string");return Jr(t)=="symbol"?t:t+""}function uN(e,t){if(Jr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Jr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var cN=function(t,r,n,i,a){var o=a.x1,u=a.x2,c=a.y1,s=a.y2,f=a.xAxis,l=a.yAxis;if(!f||!l)return null;var p=eh({x:f.scale,y:l.scale}),h={x:t?p.x.apply(o,{position:"start"}):p.x.rangeMin,y:n?p.y.apply(c,{position:"start"}):p.y.rangeMin},v={x:r?p.x.apply(u,{position:"end"}):p.x.rangeMax,y:i?p.y.apply(s,{position:"end"}):p.y.rangeMax};return ft(a,"discard")&&(!p.isInRange(h)||!p.isInRange(v))?null:XO(h,v)},jo=function(e){function t(){return eN(this,t),nN(this,t,arguments)}return oN(t,e),rN(t,[{key:"render",value:function(){var n=this.props,i=n.x1,a=n.x2,o=n.y1,u=n.y2,c=n.className,s=n.alwaysShow,f=n.clipPathId;at(s===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var l=_e(i),p=_e(a),h=_e(o),v=_e(u),d=this.props.shape;if(!l&&!p&&!h&&!v&&!d)return null;var y=cN(l,p,h,v,this.props);if(!y&&!d)return null;var b=ft(this.props,"hidden")?"url(#".concat(f,")"):void 0;return A.createElement(J,{className:Z("recharts-reference-area",c)},t.renderRect(d,Fb(Fb({clipPath:b},U(this.props,!0)),y)),Pe.renderCallByParent(this.props,y))}}])}(A.Component);Eo(jo,"displayName","ReferenceArea");Eo(jo,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1});Eo(jo,"renderRect",function(e,t){var r;return A.isValidElement(e)?r=A.cloneElement(e,t):H(e)?r=e(t):r=A.createElement(Jp,Wf({},t,{className:"recharts-reference-area-rect"})),r});function lw(e,t,r){if(t<1)return[];if(t===1&&r===void 0)return e;for(var n=[],i=0;i<e.length;i+=t)n.push(e[i]);return n}function sN(e,t,r){var n={width:e.width+t.width,height:e.height+t.height};return dD(n,r)}function lN(e,t,r){var n=r==="width",i=e.x,a=e.y,o=e.width,u=e.height;return t===1?{start:n?i:a,end:n?i+o:a+u}:{start:n?i+o:a+u,end:n?i:a}}function La(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function fN(e,t){return lw(e,t+1)}function pN(e,t,r,n,i){for(var a=(n||[]).slice(),o=t.start,u=t.end,c=0,s=1,f=o,l=function(){var v=n==null?void 0:n[c];if(v===void 0)return{v:lw(n,s)};var d=c,y,b=function(){return y===void 0&&(y=r(v,d)),y},g=v.coordinate,x=c===0||La(e,g,b,f,u);x||(c=0,f=o,s+=1),x&&(f=g+e*(b()/2+i),c+=s)},p;s<=a.length;)if(p=l(),p)return p.v;return[]}function mi(e){"@babel/helpers - typeof";return mi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},mi(e)}function zb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function je(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?zb(Object(r),!0).forEach(function(n){hN(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):zb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function hN(e,t,r){return t=dN(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dN(e){var t=vN(e,"string");return mi(t)=="symbol"?t:t+""}function vN(e,t){if(mi(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(mi(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function yN(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,u=t.start,c=t.end,s=function(p){var h=a[p],v,d=function(){return v===void 0&&(v=r(h,p)),v};if(p===o-1){var y=e*(h.coordinate+e*d()/2-c);a[p]=h=je(je({},h),{},{tickCoord:y>0?h.coordinate-y*e:h.coordinate})}else a[p]=h=je(je({},h),{},{tickCoord:h.coordinate});var b=La(e,h.tickCoord,d,u,c);b&&(c=h.tickCoord-e*(d()/2+i),a[p]=je(je({},h),{},{isShow:!0}))},f=o-1;f>=0;f--)s(f);return a}function mN(e,t,r,n,i,a){var o=(n||[]).slice(),u=o.length,c=t.start,s=t.end;if(a){var f=n[u-1],l=r(f,u-1),p=e*(f.coordinate+e*l/2-s);o[u-1]=f=je(je({},f),{},{tickCoord:p>0?f.coordinate-p*e:f.coordinate});var h=La(e,f.tickCoord,function(){return l},c,s);h&&(s=f.tickCoord-e*(l/2+i),o[u-1]=je(je({},f),{},{isShow:!0}))}for(var v=a?u-1:u,d=function(g){var x=o[g],w,m=function(){return w===void 0&&(w=r(x,g)),w};if(g===0){var O=e*(x.coordinate-e*m()/2-c);o[g]=x=je(je({},x),{},{tickCoord:O<0?x.coordinate-O*e:x.coordinate})}else o[g]=x=je(je({},x),{},{tickCoord:x.coordinate});var _=La(e,x.tickCoord,m,c,s);_&&(c=x.tickCoord+e*(m()/2+i),o[g]=je(je({},x),{},{isShow:!0}))},y=0;y<v;y++)d(y);return o}function uh(e,t,r){var n=e.tick,i=e.ticks,a=e.viewBox,o=e.minTickGap,u=e.orientation,c=e.interval,s=e.tickFormatter,f=e.unit,l=e.angle;if(!i||!i.length||!n)return[];if(N(c)||Et.isSsr)return fN(i,typeof c=="number"&&N(c)?c:0);var p=[],h=u==="top"||u==="bottom"?"width":"height",v=f&&h==="width"?$n(f,{fontSize:t,letterSpacing:r}):{width:0,height:0},d=function(x,w){var m=H(s)?s(x.value,w):x.value;return h==="width"?sN($n(m,{fontSize:t,letterSpacing:r}),v,l):$n(m,{fontSize:t,letterSpacing:r})[h]},y=i.length>=2?Ie(i[1].coordinate-i[0].coordinate):1,b=lN(a,y,h);return c==="equidistantPreserveStart"?pN(y,b,d,i,o):(c==="preserveStart"||c==="preserveStartEnd"?p=mN(y,b,d,i,o,c==="preserveStartEnd"):p=yN(y,b,d,i,o),p.filter(function(g){return g.isShow}))}var gN=["viewBox"],bN=["viewBox"],xN=["ticks"];function Qr(e){"@babel/helpers - typeof";return Qr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qr(e)}function Tr(){return Tr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Tr.apply(this,arguments)}function Wb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Me(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Wb(Object(r),!0).forEach(function(n){ch(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Wb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function $l(e,t){if(e==null)return{};var r=ON(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function ON(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function wN(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Ub(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,pw(n.key),n)}}function _N(e,t,r){return t&&Ub(e.prototype,t),r&&Ub(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function AN(e,t,r){return t=Ba(t),SN(e,fw()?Reflect.construct(t,r||[],Ba(e).constructor):t.apply(e,r))}function SN(e,t){if(t&&(Qr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return PN(e)}function PN(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function fw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fw=function(){return!!e})()}function Ba(e){return Ba=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ba(e)}function TN(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Kf(e,t)}function Kf(e,t){return Kf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Kf(e,t)}function ch(e,t,r){return t=pw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pw(e){var t=EN(e,"string");return Qr(t)=="symbol"?t:t+""}function EN(e,t){if(Qr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Qr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var mn=function(e){function t(r){var n;return wN(this,t),n=AN(this,t,[r]),n.state={fontSize:"",letterSpacing:""},n}return TN(t,e),_N(t,[{key:"shouldComponentUpdate",value:function(n,i){var a=n.viewBox,o=$l(n,gN),u=this.props,c=u.viewBox,s=$l(u,bN);return!jr(a,c)||!jr(o,s)||!jr(i,this.state)}},{key:"componentDidMount",value:function(){var n=this.layerReference;if(n){var i=n.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];i&&this.setState({fontSize:window.getComputedStyle(i).fontSize,letterSpacing:window.getComputedStyle(i).letterSpacing})}}},{key:"getTickLineCoord",value:function(n){var i=this.props,a=i.x,o=i.y,u=i.width,c=i.height,s=i.orientation,f=i.tickSize,l=i.mirror,p=i.tickMargin,h,v,d,y,b,g,x=l?-1:1,w=n.tickSize||f,m=N(n.tickCoord)?n.tickCoord:n.coordinate;switch(s){case"top":h=v=n.coordinate,y=o+ +!l*c,d=y-x*w,g=d-x*p,b=m;break;case"left":d=y=n.coordinate,v=a+ +!l*u,h=v-x*w,b=h-x*p,g=m;break;case"right":d=y=n.coordinate,v=a+ +l*u,h=v+x*w,b=h+x*p,g=m;break;default:h=v=n.coordinate,y=o+ +l*c,d=y+x*w,g=d+x*p,b=m;break}return{line:{x1:h,y1:d,x2:v,y2:y},tick:{x:b,y:g}}}},{key:"getTickTextAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o;switch(i){case"left":o=a?"start":"end";break;case"right":o=a?"end":"start";break;default:o="middle";break}return o}},{key:"getTickVerticalAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o="end";switch(i){case"left":case"right":o="middle";break;case"top":o=a?"start":"end";break;default:o=a?"end":"start";break}return o}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.orientation,s=n.mirror,f=n.axisLine,l=Me(Me(Me({},U(this.props,!1)),U(f,!1)),{},{fill:"none"});if(c==="top"||c==="bottom"){var p=+(c==="top"&&!s||c==="bottom"&&s);l=Me(Me({},l),{},{x1:i,y1:a+p*u,x2:i+o,y2:a+p*u})}else{var h=+(c==="left"&&!s||c==="right"&&s);l=Me(Me({},l),{},{x1:i+h*o,y1:a,x2:i+h*o,y2:a+u})}return A.createElement("line",Tr({},l,{className:Z("recharts-cartesian-axis-line",Ke(f,"className"))}))}},{key:"renderTicks",value:function(n,i,a){var o=this,u=this.props,c=u.tickLine,s=u.stroke,f=u.tick,l=u.tickFormatter,p=u.unit,h=uh(Me(Me({},this.props),{},{ticks:n}),i,a),v=this.getTickTextAnchor(),d=this.getTickVerticalAnchor(),y=U(this.props,!1),b=U(f,!1),g=Me(Me({},y),{},{fill:"none"},U(c,!1)),x=h.map(function(w,m){var O=o.getTickLineCoord(w),_=O.line,S=O.tick,T=Me(Me(Me(Me({textAnchor:v,verticalAnchor:d},y),{},{stroke:"none",fill:s},b),S),{},{index:m,payload:w,visibleTicksCount:h.length,tickFormatter:l});return A.createElement(J,Tr({className:"recharts-cartesian-axis-tick",key:"tick-".concat(w.value,"-").concat(w.coordinate,"-").concat(w.tickCoord)},Lt(o.props,w,m)),c&&A.createElement("line",Tr({},g,_,{className:Z("recharts-cartesian-axis-tick-line",Ke(c,"className"))})),f&&t.renderTickItem(f,T,"".concat(H(l)?l(w.value,m):w.value).concat(p||"")))});return A.createElement("g",{className:"recharts-cartesian-axis-ticks"},x)}},{key:"render",value:function(){var n=this,i=this.props,a=i.axisLine,o=i.width,u=i.height,c=i.ticksGenerator,s=i.className,f=i.hide;if(f)return null;var l=this.props,p=l.ticks,h=$l(l,xN),v=p;return H(c)&&(v=p&&p.length>0?c(this.props):c(h)),o<=0||u<=0||!v||!v.length?null:A.createElement(J,{className:Z("recharts-cartesian-axis",s),ref:function(y){n.layerReference=y}},a&&this.renderAxisLine(),this.renderTicks(v,this.state.fontSize,this.state.letterSpacing),Pe.renderCallByParent(this.props))}}],[{key:"renderTickItem",value:function(n,i,a){var o;return A.isValidElement(n)?o=A.cloneElement(n,i):H(n)?o=n(i):o=A.createElement(lr,Tr({},i,{className:"recharts-cartesian-axis-tick-value"}),a),o}}])}(q.Component);ch(mn,"displayName","CartesianAxis");ch(mn,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var jN=["x1","y1","x2","y2","key"],$N=["offset"];function pr(e){"@babel/helpers - typeof";return pr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},pr(e)}function Kb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function $e(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Kb(Object(r),!0).forEach(function(n){MN(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Kb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function MN(e,t,r){return t=IN(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function IN(e){var t=CN(e,"string");return pr(t)=="symbol"?t:t+""}function CN(e,t){if(pr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(pr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function ar(){return ar=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ar.apply(this,arguments)}function Hb(e,t){if(e==null)return{};var r=kN(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function kN(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var RN=function(t){var r=t.fill;if(!r||r==="none")return null;var n=t.fillOpacity,i=t.x,a=t.y,o=t.width,u=t.height,c=t.ry;return A.createElement("rect",{x:i,y:a,ry:c,width:o,height:u,stroke:"none",fill:r,fillOpacity:n,className:"recharts-cartesian-grid-bg"})};function hw(e,t){var r;if(A.isValidElement(e))r=A.cloneElement(e,t);else if(H(e))r=e(t);else{var n=t.x1,i=t.y1,a=t.x2,o=t.y2,u=t.key,c=Hb(t,jN),s=U(c,!1);s.offset;var f=Hb(s,$N);r=A.createElement("line",ar({},f,{x1:n,y1:i,x2:a,y2:o,fill:"none",key:u}))}return r}function DN(e){var t=e.x,r=e.width,n=e.horizontal,i=n===void 0?!0:n,a=e.horizontalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,c){var s=$e($e({},e),{},{x1:t,y1:u,x2:t+r,y2:u,key:"line-".concat(c),index:c});return hw(i,s)});return A.createElement("g",{className:"recharts-cartesian-grid-horizontal"},o)}function NN(e){var t=e.y,r=e.height,n=e.vertical,i=n===void 0?!0:n,a=e.verticalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,c){var s=$e($e({},e),{},{x1:u,y1:t,x2:u,y2:t+r,key:"line-".concat(c),index:c});return hw(i,s)});return A.createElement("g",{className:"recharts-cartesian-grid-vertical"},o)}function qN(e){var t=e.horizontalFill,r=e.fillOpacity,n=e.x,i=e.y,a=e.width,o=e.height,u=e.horizontalPoints,c=e.horizontal,s=c===void 0?!0:c;if(!s||!t||!t.length)return null;var f=u.map(function(p){return Math.round(p+i-i)}).sort(function(p,h){return p-h});i!==f[0]&&f.unshift(0);var l=f.map(function(p,h){var v=!f[h+1],d=v?i+o-p:f[h+1]-p;if(d<=0)return null;var y=h%t.length;return A.createElement("rect",{key:"react-".concat(h),y:p,x:n,height:d,width:a,stroke:"none",fill:t[y],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return A.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},l)}function LN(e){var t=e.vertical,r=t===void 0?!0:t,n=e.verticalFill,i=e.fillOpacity,a=e.x,o=e.y,u=e.width,c=e.height,s=e.verticalPoints;if(!r||!n||!n.length)return null;var f=s.map(function(p){return Math.round(p+a-a)}).sort(function(p,h){return p-h});a!==f[0]&&f.unshift(0);var l=f.map(function(p,h){var v=!f[h+1],d=v?a+u-p:f[h+1]-p;if(d<=0)return null;var y=h%n.length;return A.createElement("rect",{key:"react-".concat(h),x:p,y:o,width:d,height:c,stroke:"none",fill:n[y],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return A.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},l)}var BN=function(t,r){var n=t.xAxis,i=t.width,a=t.height,o=t.offset;return vO(uh($e($e($e({},mn.defaultProps),n),{},{ticks:xt(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.left,o.left+o.width,r)},FN=function(t,r){var n=t.yAxis,i=t.width,a=t.height,o=t.offset;return vO(uh($e($e($e({},mn.defaultProps),n),{},{ticks:xt(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.top,o.top+o.height,r)},wr={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function zN(e){var t,r,n,i,a,o,u=nh(),c=ih(),s=ED(),f=$e($e({},e),{},{stroke:(t=e.stroke)!==null&&t!==void 0?t:wr.stroke,fill:(r=e.fill)!==null&&r!==void 0?r:wr.fill,horizontal:(n=e.horizontal)!==null&&n!==void 0?n:wr.horizontal,horizontalFill:(i=e.horizontalFill)!==null&&i!==void 0?i:wr.horizontalFill,vertical:(a=e.vertical)!==null&&a!==void 0?a:wr.vertical,verticalFill:(o=e.verticalFill)!==null&&o!==void 0?o:wr.verticalFill,x:N(e.x)?e.x:s.left,y:N(e.y)?e.y:s.top,width:N(e.width)?e.width:s.width,height:N(e.height)?e.height:s.height}),l=f.x,p=f.y,h=f.width,v=f.height,d=f.syncWithTicks,y=f.horizontalValues,b=f.verticalValues,g=SD(),x=PD();if(!N(h)||h<=0||!N(v)||v<=0||!N(l)||l!==+l||!N(p)||p!==+p)return null;var w=f.verticalCoordinatesGenerator||BN,m=f.horizontalCoordinatesGenerator||FN,O=f.horizontalPoints,_=f.verticalPoints;if((!O||!O.length)&&H(m)){var S=y&&y.length,T=m({yAxis:x?$e($e({},x),{},{ticks:S?y:x.ticks}):void 0,width:u,height:c,offset:s},S?!0:d);at(Array.isArray(T),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(pr(T),"]")),Array.isArray(T)&&(O=T)}if((!_||!_.length)&&H(w)){var j=b&&b.length,P=w({xAxis:g?$e($e({},g),{},{ticks:j?b:g.ticks}):void 0,width:u,height:c,offset:s},j?!0:d);at(Array.isArray(P),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(pr(P),"]")),Array.isArray(P)&&(_=P)}return A.createElement("g",{className:"recharts-cartesian-grid"},A.createElement(RN,{fill:f.fill,fillOpacity:f.fillOpacity,x:f.x,y:f.y,width:f.width,height:f.height,ry:f.ry}),A.createElement(DN,ar({},f,{offset:s,horizontalPoints:O,xAxis:g,yAxis:x})),A.createElement(NN,ar({},f,{offset:s,verticalPoints:_,xAxis:g,yAxis:x})),A.createElement(qN,ar({},f,{horizontalPoints:O})),A.createElement(LN,ar({},f,{verticalPoints:_})))}zN.displayName="CartesianGrid";var WN=["type","layout","connectNulls","ref"],UN=["key"];function en(e){"@babel/helpers - typeof";return en=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},en(e)}function Gb(e,t){if(e==null)return{};var r=KN(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function KN(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Dn(){return Dn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Dn.apply(this,arguments)}function Vb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Fe(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Vb(Object(r),!0).forEach(function(n){nt(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Vb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function _r(e){return XN(e)||VN(e)||GN(e)||HN()}function HN(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function GN(e,t){if(e){if(typeof e=="string")return Hf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Hf(e,t)}}function VN(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function XN(e){if(Array.isArray(e))return Hf(e)}function Hf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function YN(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Xb(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,vw(n.key),n)}}function ZN(e,t,r){return t&&Xb(e.prototype,t),r&&Xb(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function JN(e,t,r){return t=Fa(t),QN(e,dw()?Reflect.construct(t,r||[],Fa(e).constructor):t.apply(e,r))}function QN(e,t){if(t&&(en(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return e2(e)}function e2(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function dw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(dw=function(){return!!e})()}function Fa(e){return Fa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Fa(e)}function t2(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Gf(e,t)}function Gf(e,t){return Gf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Gf(e,t)}function nt(e,t,r){return t=vw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function vw(e){var t=r2(e,"string");return en(t)=="symbol"?t:t+""}function r2(e,t){if(en(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(en(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Ei=function(e){function t(){var r;YN(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=JN(this,t,[].concat(i)),nt(r,"state",{isAnimationFinished:!0,totalLength:0}),nt(r,"generateSimpleStrokeDasharray",function(o,u){return"".concat(u,"px ").concat(o-u,"px")}),nt(r,"getStrokeDasharray",function(o,u,c){var s=c.reduce(function(b,g){return b+g});if(!s)return r.generateSimpleStrokeDasharray(u,o);for(var f=Math.floor(o/s),l=o%s,p=u-o,h=[],v=0,d=0;v<c.length;d+=c[v],++v)if(d+c[v]>l){h=[].concat(_r(c.slice(0,v)),[l-d]);break}var y=h.length%2===0?[0,p]:[p];return[].concat(_r(t.repeat(c,f)),_r(h),y).map(function(b){return"".concat(b,"px")}).join(", ")}),nt(r,"id",zt("recharts-line-")),nt(r,"pathRef",function(o){r.mainCurve=o}),nt(r,"handleAnimationEnd",function(){r.setState({isAnimationFinished:!0}),r.props.onAnimationEnd&&r.props.onAnimationEnd()}),nt(r,"handleAnimationStart",function(){r.setState({isAnimationFinished:!1}),r.props.onAnimationStart&&r.props.onAnimationStart()}),r}return t2(t,e),ZN(t,[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var n=this.getTotalLength();this.setState({totalLength:n})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var n=this.getTotalLength();n!==this.state.totalLength&&this.setState({totalLength:n})}}},{key:"getTotalLength",value:function(){var n=this.mainCurve;try{return n&&n.getTotalLength&&n.getTotalLength()||0}catch(i){return 0}}},{key:"renderErrorBar",value:function(n,i){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.points,u=a.xAxis,c=a.yAxis,s=a.layout,f=a.children,l=Re(f,yn);if(!l)return null;var p=function(d,y){return{x:d.x,y:d.y,value:d.value,errorVal:pe(d.payload,y)}},h={clipPath:n?"url(#clipPath-".concat(i,")"):null};return A.createElement(J,h,l.map(function(v){return A.cloneElement(v,{key:"bar-".concat(v.props.dataKey),data:o,xAxis:u,yAxis:c,layout:s,dataPointFormatter:p})}))}},{key:"renderDots",value:function(n,i,a){var o=this.props.isAnimationActive;if(o&&!this.state.isAnimationFinished)return null;var u=this.props,c=u.dot,s=u.points,f=u.dataKey,l=U(this.props,!1),p=U(c,!0),h=s.map(function(d,y){var b=Fe(Fe(Fe({key:"dot-".concat(y),r:3},l),p),{},{index:y,cx:d.x,cy:d.y,value:d.value,dataKey:f,payload:d.payload,points:s});return t.renderDotItem(c,b)}),v={clipPath:n?"url(#clipPath-".concat(i?"":"dots-").concat(a,")"):null};return A.createElement(J,Dn({className:"recharts-line-dots",key:"dots"},v),h)}},{key:"renderCurveStatically",value:function(n,i,a,o){var u=this.props,c=u.type,s=u.layout,f=u.connectNulls;u.ref;var l=Gb(u,WN),p=Fe(Fe(Fe({},U(l,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:i?"url(#clipPath-".concat(a,")"):null,points:n},o),{},{type:c,layout:s,connectNulls:f});return A.createElement(qt,Dn({},p,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(n,i){var a=this,o=this.props,u=o.points,c=o.strokeDasharray,s=o.isAnimationActive,f=o.animationBegin,l=o.animationDuration,p=o.animationEasing,h=o.animationId,v=o.animateNewValues,d=o.width,y=o.height,b=this.state,g=b.prevPoints,x=b.totalLength;return A.createElement(Je,{begin:f,duration:l,isActive:s,easing:p,from:{t:0},to:{t:1},key:"line-".concat(h),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(w){var m=w.t;if(g){var O=g.length/u.length,_=u.map(function(E,$){var I=Math.floor($*O);if(g[I]){var M=g[I],k=de(M.x,E.x),R=de(M.y,E.y);return Fe(Fe({},E),{},{x:k(m),y:R(m)})}if(v){var L=de(d*2,E.x),B=de(y/2,E.y);return Fe(Fe({},E),{},{x:L(m),y:B(m)})}return Fe(Fe({},E),{},{x:E.x,y:E.y})});return a.renderCurveStatically(_,n,i)}var S=de(0,x),T=S(m),j;if(c){var P="".concat(c).split(/[,\s]+/gim).map(function(E){return parseFloat(E)});j=a.getStrokeDasharray(T,x,P)}else j=a.generateSimpleStrokeDasharray(x,T);return a.renderCurveStatically(u,n,i,{strokeDasharray:j})})}},{key:"renderCurve",value:function(n,i){var a=this.props,o=a.points,u=a.isAnimationActive,c=this.state,s=c.prevPoints,f=c.totalLength;return u&&o&&o.length&&(!s&&f>0||!Bt(s,o))?this.renderCurveWithAnimation(n,i):this.renderCurveStatically(o,n,i)}},{key:"render",value:function(){var n,i=this.props,a=i.hide,o=i.dot,u=i.points,c=i.className,s=i.xAxis,f=i.yAxis,l=i.top,p=i.left,h=i.width,v=i.height,d=i.isAnimationActive,y=i.id;if(a||!u||!u.length)return null;var b=this.state.isAnimationFinished,g=u.length===1,x=Z("recharts-line",c),w=s&&s.allowDataOverflow,m=f&&f.allowDataOverflow,O=w||m,_=G(y)?this.id:y,S=(n=U(o,!1))!==null&&n!==void 0?n:{r:3,strokeWidth:2},T=S.r,j=T===void 0?3:T,P=S.strokeWidth,E=P===void 0?2:P,$=m0(o)?o:{},I=$.clipDot,M=I===void 0?!0:I,k=j*2+E;return A.createElement(J,{className:x},w||m?A.createElement("defs",null,A.createElement("clipPath",{id:"clipPath-".concat(_)},A.createElement("rect",{x:w?p:p-h/2,y:m?l:l-v/2,width:w?h:h*2,height:m?v:v*2})),!M&&A.createElement("clipPath",{id:"clipPath-dots-".concat(_)},A.createElement("rect",{x:p-k/2,y:l-k/2,width:h+k,height:v+k}))):null,!g&&this.renderCurve(O,_),this.renderErrorBar(O,_),(g||o)&&this.renderDots(O,M,_),(!d||b)&&ot.renderCallByParent(this.props,u))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curPoints:n.points,prevPoints:i.curPoints}:n.points!==i.curPoints?{curPoints:n.points}:null}},{key:"repeat",value:function(n,i){for(var a=n.length%2!==0?[].concat(_r(n),[0]):n,o=[],u=0;u<i;++u)o=[].concat(_r(o),_r(a));return o}},{key:"renderDotItem",value:function(n,i){var a;if(A.isValidElement(n))a=A.cloneElement(n,i);else if(H(n))a=n(i);else{var o=i.key,u=Gb(i,UN),c=Z("recharts-line-dot",typeof n!="boolean"?n.className:"");a=A.createElement(Ti,Dn({key:o},u,{className:c}))}return a}}])}(q.PureComponent);nt(Ei,"displayName","Line");nt(Ei,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!Et.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1});nt(Ei,"getComposedData",function(e){var t=e.props,r=e.xAxis,n=e.yAxis,i=e.xAxisTicks,a=e.yAxisTicks,o=e.dataKey,u=e.bandSize,c=e.displayedData,s=e.offset,f=t.layout,l=c.map(function(p,h){var v=pe(p,o);return f==="horizontal"?{x:zr({axis:r,ticks:i,bandSize:u,entry:p,index:h}),y:G(v)?null:n.scale(v),value:v,payload:p}:{x:G(v)?null:r.scale(v),y:zr({axis:n,ticks:a,bandSize:u,entry:p,index:h}),value:v,payload:p}});return Fe({points:l,layout:f},s)});var n2=["layout","type","stroke","connectNulls","isRange","ref"],i2=["key"],yw;function tn(e){"@babel/helpers - typeof";return tn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},tn(e)}function mw(e,t){if(e==null)return{};var r=a2(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function a2(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function or(){return or=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},or.apply(this,arguments)}function Yb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Mt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Yb(Object(r),!0).forEach(function(n){st(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Yb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function o2(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Zb(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,bw(n.key),n)}}function u2(e,t,r){return t&&Zb(e.prototype,t),r&&Zb(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function c2(e,t,r){return t=za(t),s2(e,gw()?Reflect.construct(t,r||[],za(e).constructor):t.apply(e,r))}function s2(e,t){if(t&&(tn(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return l2(e)}function l2(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function gw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(gw=function(){return!!e})()}function za(e){return za=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},za(e)}function f2(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Vf(e,t)}function Vf(e,t){return Vf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Vf(e,t)}function st(e,t,r){return t=bw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function bw(e){var t=p2(e,"string");return tn(t)=="symbol"?t:t+""}function p2(e,t){if(tn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(tn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var gr=function(e){function t(){var r;o2(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=c2(this,t,[].concat(i)),st(r,"state",{isAnimationFinished:!0}),st(r,"id",zt("recharts-area-")),st(r,"handleAnimationEnd",function(){var o=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),H(o)&&o()}),st(r,"handleAnimationStart",function(){var o=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),H(o)&&o()}),r}return f2(t,e),u2(t,[{key:"renderDots",value:function(n,i,a){var o=this.props.isAnimationActive,u=this.state.isAnimationFinished;if(o&&!u)return null;var c=this.props,s=c.dot,f=c.points,l=c.dataKey,p=U(this.props,!1),h=U(s,!0),v=f.map(function(y,b){var g=Mt(Mt(Mt({key:"dot-".concat(b),r:3},p),h),{},{index:b,cx:y.x,cy:y.y,dataKey:l,value:y.value,payload:y.payload,points:f});return t.renderDotItem(s,g)}),d={clipPath:n?"url(#clipPath-".concat(i?"":"dots-").concat(a,")"):null};return A.createElement(J,or({className:"recharts-area-dots"},d),v)}},{key:"renderHorizontalRect",value:function(n){var i=this.props,a=i.baseLine,o=i.points,u=i.strokeWidth,c=o[0].x,s=o[o.length-1].x,f=n*Math.abs(c-s),l=kt(o.map(function(p){return p.y||0}));return N(a)&&typeof a=="number"?l=Math.max(a,l):a&&Array.isArray(a)&&a.length&&(l=Math.max(kt(a.map(function(p){return p.y||0})),l)),N(l)?A.createElement("rect",{x:c<s?c:c-f,y:0,width:f,height:Math.floor(l+(u?parseInt("".concat(u),10):1))}):null}},{key:"renderVerticalRect",value:function(n){var i=this.props,a=i.baseLine,o=i.points,u=i.strokeWidth,c=o[0].y,s=o[o.length-1].y,f=n*Math.abs(c-s),l=kt(o.map(function(p){return p.x||0}));return N(a)&&typeof a=="number"?l=Math.max(a,l):a&&Array.isArray(a)&&a.length&&(l=Math.max(kt(a.map(function(p){return p.x||0})),l)),N(l)?A.createElement("rect",{x:0,y:c<s?c:c-f,width:l+(u?parseInt("".concat(u),10):1),height:Math.floor(f)}):null}},{key:"renderClipRect",value:function(n){var i=this.props.layout;return i==="vertical"?this.renderVerticalRect(n):this.renderHorizontalRect(n)}},{key:"renderAreaStatically",value:function(n,i,a,o){var u=this.props,c=u.layout,s=u.type,f=u.stroke,l=u.connectNulls,p=u.isRange;u.ref;var h=mw(u,n2);return A.createElement(J,{clipPath:a?"url(#clipPath-".concat(o,")"):null},A.createElement(qt,or({},U(h,!0),{points:n,connectNulls:l,type:s,baseLine:i,layout:c,stroke:"none",className:"recharts-area-area"})),f!=="none"&&A.createElement(qt,or({},U(this.props,!1),{className:"recharts-area-curve",layout:c,type:s,connectNulls:l,fill:"none",points:n})),f!=="none"&&p&&A.createElement(qt,or({},U(this.props,!1),{className:"recharts-area-curve",layout:c,type:s,connectNulls:l,fill:"none",points:i})))}},{key:"renderAreaWithAnimation",value:function(n,i){var a=this,o=this.props,u=o.points,c=o.baseLine,s=o.isAnimationActive,f=o.animationBegin,l=o.animationDuration,p=o.animationEasing,h=o.animationId,v=this.state,d=v.prevPoints,y=v.prevBaseLine;return A.createElement(Je,{begin:f,duration:l,isActive:s,easing:p,from:{t:0},to:{t:1},key:"area-".concat(h),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(b){var g=b.t;if(d){var x=d.length/u.length,w=u.map(function(S,T){var j=Math.floor(T*x);if(d[j]){var P=d[j],E=de(P.x,S.x),$=de(P.y,S.y);return Mt(Mt({},S),{},{x:E(g),y:$(g)})}return S}),m;if(N(c)&&typeof c=="number"){var O=de(y,c);m=O(g)}else if(G(c)||fn(c)){var _=de(y,0);m=_(g)}else m=c.map(function(S,T){var j=Math.floor(T*x);if(y[j]){var P=y[j],E=de(P.x,S.x),$=de(P.y,S.y);return Mt(Mt({},S),{},{x:E(g),y:$(g)})}return S});return a.renderAreaStatically(w,m,n,i)}return A.createElement(J,null,A.createElement("defs",null,A.createElement("clipPath",{id:"animationClipPath-".concat(i)},a.renderClipRect(g))),A.createElement(J,{clipPath:"url(#animationClipPath-".concat(i,")")},a.renderAreaStatically(u,c,n,i)))})}},{key:"renderArea",value:function(n,i){var a=this.props,o=a.points,u=a.baseLine,c=a.isAnimationActive,s=this.state,f=s.prevPoints,l=s.prevBaseLine,p=s.totalLength;return c&&o&&o.length&&(!f&&p>0||!Bt(f,o)||!Bt(l,u))?this.renderAreaWithAnimation(n,i):this.renderAreaStatically(o,u,n,i)}},{key:"render",value:function(){var n,i=this.props,a=i.hide,o=i.dot,u=i.points,c=i.className,s=i.top,f=i.left,l=i.xAxis,p=i.yAxis,h=i.width,v=i.height,d=i.isAnimationActive,y=i.id;if(a||!u||!u.length)return null;var b=this.state.isAnimationFinished,g=u.length===1,x=Z("recharts-area",c),w=l&&l.allowDataOverflow,m=p&&p.allowDataOverflow,O=w||m,_=G(y)?this.id:y,S=(n=U(o,!1))!==null&&n!==void 0?n:{r:3,strokeWidth:2},T=S.r,j=T===void 0?3:T,P=S.strokeWidth,E=P===void 0?2:P,$=m0(o)?o:{},I=$.clipDot,M=I===void 0?!0:I,k=j*2+E;return A.createElement(J,{className:x},w||m?A.createElement("defs",null,A.createElement("clipPath",{id:"clipPath-".concat(_)},A.createElement("rect",{x:w?f:f-h/2,y:m?s:s-v/2,width:w?h:h*2,height:m?v:v*2})),!M&&A.createElement("clipPath",{id:"clipPath-dots-".concat(_)},A.createElement("rect",{x:f-k/2,y:s-k/2,width:h+k,height:v+k}))):null,g?null:this.renderArea(O,_),(o||g)&&this.renderDots(O,M,_),(!d||b)&&ot.renderCallByParent(this.props,u))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curPoints:n.points,curBaseLine:n.baseLine,prevPoints:i.curPoints,prevBaseLine:i.curBaseLine}:n.points!==i.curPoints||n.baseLine!==i.curBaseLine?{curPoints:n.points,curBaseLine:n.baseLine}:null}}])}(q.PureComponent);yw=gr;st(gr,"displayName","Area");st(gr,"defaultProps",{stroke:"#3182bd",fill:"#3182bd",fillOpacity:.6,xAxisId:0,yAxisId:0,legendType:"line",connectNulls:!1,points:[],dot:!1,activeDot:!0,hide:!1,isAnimationActive:!Et.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"});st(gr,"getBaseValue",function(e,t,r,n){var i=e.layout,a=e.baseValue,o=t.props.baseValue,u=o!=null?o:a;if(N(u)&&typeof u=="number")return u;var c=i==="horizontal"?n:r,s=c.scale.domain();if(c.type==="number"){var f=Math.max(s[0],s[1]),l=Math.min(s[0],s[1]);return u==="dataMin"?l:u==="dataMax"||f<0?f:Math.max(Math.min(s[0],s[1]),0)}return u==="dataMin"?s[0]:u==="dataMax"?s[1]:s[0]});st(gr,"getComposedData",function(e){var t=e.props,r=e.item,n=e.xAxis,i=e.yAxis,a=e.xAxisTicks,o=e.yAxisTicks,u=e.bandSize,c=e.dataKey,s=e.stackedData,f=e.dataStartIndex,l=e.displayedData,p=e.offset,h=t.layout,v=s&&s.length,d=yw.getBaseValue(t,r,n,i),y=h==="horizontal",b=!1,g=l.map(function(w,m){var O;v?O=s[f+m]:(O=pe(w,c),Array.isArray(O)?b=!0:O=[d,O]);var _=O[1]==null||v&&pe(w,c)==null;return y?{x:zr({axis:n,ticks:a,bandSize:u,entry:w,index:m}),y:_?null:i.scale(O[1]),value:O,payload:w}:{x:_?null:n.scale(O[1]),y:zr({axis:i,ticks:o,bandSize:u,entry:w,index:m}),value:O,payload:w}}),x;return v||b?x=g.map(function(w){var m=Array.isArray(w.value)?w.value[0]:null;return y?{x:w.x,y:m!=null&&w.y!=null?i.scale(m):null}:{x:m!=null?n.scale(m):null,y:w.y}}):x=y?i.scale(d):n.scale(d),Mt({points:g,baseLine:x,layout:h,isRange:b},p)});st(gr,"renderDotItem",function(e,t){var r;if(A.isValidElement(e))r=A.cloneElement(e,t);else if(H(e))r=e(t);else{var n=Z("recharts-area-dot",typeof e!="boolean"?e.className:""),i=t.key,a=mw(t,i2);r=A.createElement(Ti,or({},a,{key:i,className:n}))}return r});function rn(e){"@babel/helpers - typeof";return rn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},rn(e)}function h2(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function d2(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ww(n.key),n)}}function v2(e,t,r){return t&&d2(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function y2(e,t,r){return t=Wa(t),m2(e,xw()?Reflect.construct(t,r||[],Wa(e).constructor):t.apply(e,r))}function m2(e,t){if(t&&(rn(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return g2(e)}function g2(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function xw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(xw=function(){return!!e})()}function Wa(e){return Wa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Wa(e)}function b2(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Xf(e,t)}function Xf(e,t){return Xf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Xf(e,t)}function Ow(e,t,r){return t=ww(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ww(e){var t=x2(e,"string");return rn(t)=="symbol"?t:t+""}function x2(e,t){if(rn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(rn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var $o=function(e){function t(){return h2(this,t),y2(this,t,arguments)}return b2(t,e),v2(t,[{key:"render",value:function(){return null}}])}(A.Component);Ow($o,"displayName","ZAxis");Ow($o,"defaultProps",{zAxisId:0,range:[64,64],scale:"auto",type:"number"});var O2=["option","isActive"];function Nn(){return Nn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Nn.apply(this,arguments)}function w2(e,t){if(e==null)return{};var r=_2(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function _2(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function A2(e){var t=e.option,r=e.isActive,n=w2(e,O2);return typeof t=="string"?A.createElement(ja,Nn({option:A.createElement(no,Nn({type:t},n)),isActive:r,shapeType:"symbols"},n)):A.createElement(ja,Nn({option:t,isActive:r,shapeType:"symbols"},n))}function nn(e){"@babel/helpers - typeof";return nn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},nn(e)}function qn(){return qn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},qn.apply(this,arguments)}function Jb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ve(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Jb(Object(r),!0).forEach(function(n){Dt(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Jb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function S2(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Qb(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Aw(n.key),n)}}function P2(e,t,r){return t&&Qb(e.prototype,t),r&&Qb(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function T2(e,t,r){return t=Ua(t),E2(e,_w()?Reflect.construct(t,r||[],Ua(e).constructor):t.apply(e,r))}function E2(e,t){if(t&&(nn(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return j2(e)}function j2(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _w(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(_w=function(){return!!e})()}function Ua(e){return Ua=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ua(e)}function $2(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Yf(e,t)}function Yf(e,t){return Yf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Yf(e,t)}function Dt(e,t,r){return t=Aw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Aw(e){var t=M2(e,"string");return nn(t)=="symbol"?t:t+""}function M2(e,t){if(nn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(nn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Mo=function(e){function t(){var r;S2(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=T2(this,t,[].concat(i)),Dt(r,"state",{isAnimationFinished:!1}),Dt(r,"handleAnimationEnd",function(){r.setState({isAnimationFinished:!0})}),Dt(r,"handleAnimationStart",function(){r.setState({isAnimationFinished:!1})}),Dt(r,"id",zt("recharts-scatter-")),r}return $2(t,e),P2(t,[{key:"renderSymbolsStatically",value:function(n){var i=this,a=this.props,o=a.shape,u=a.activeShape,c=a.activeIndex,s=U(this.props,!1);return n.map(function(f,l){var p=c===l,h=p?u:o,v=Ve(Ve({},s),f);return A.createElement(J,qn({className:"recharts-scatter-symbol",key:"symbol-".concat(f==null?void 0:f.cx,"-").concat(f==null?void 0:f.cy,"-").concat(f==null?void 0:f.size,"-").concat(l)},Lt(i.props,f,l),{role:"img"}),A.createElement(A2,qn({option:h,isActive:p,key:"symbol-".concat(l)},v)))})}},{key:"renderSymbolsWithAnimation",value:function(){var n=this,i=this.props,a=i.points,o=i.isAnimationActive,u=i.animationBegin,c=i.animationDuration,s=i.animationEasing,f=i.animationId,l=this.state.prevPoints;return A.createElement(Je,{begin:u,duration:c,isActive:o,easing:s,from:{t:0},to:{t:1},key:"pie-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(p){var h=p.t,v=a.map(function(d,y){var b=l&&l[y];if(b){var g=de(b.cx,d.cx),x=de(b.cy,d.cy),w=de(b.size,d.size);return Ve(Ve({},d),{},{cx:g(h),cy:x(h),size:w(h)})}var m=de(0,d.size);return Ve(Ve({},d),{},{size:m(h)})});return A.createElement(J,null,n.renderSymbolsStatically(v))})}},{key:"renderSymbols",value:function(){var n=this.props,i=n.points,a=n.isAnimationActive,o=this.state.prevPoints;return a&&i&&i.length&&(!o||!Bt(o,i))?this.renderSymbolsWithAnimation():this.renderSymbolsStatically(i)}},{key:"renderErrorBar",value:function(){var n=this.props.isAnimationActive;if(n&&!this.state.isAnimationFinished)return null;var i=this.props,a=i.points,o=i.xAxis,u=i.yAxis,c=i.children,s=Re(c,yn);return s?s.map(function(f,l){var p=f.props,h=p.direction,v=p.dataKey;return A.cloneElement(f,{key:"".concat(h,"-").concat(v,"-").concat(a[l]),data:a,xAxis:o,yAxis:u,layout:h==="x"?"vertical":"horizontal",dataPointFormatter:function(y,b){return{x:y.cx,y:y.cy,value:h==="x"?+y.node.x:+y.node.y,errorVal:pe(y,b)}}})}):null}},{key:"renderLine",value:function(){var n=this.props,i=n.points,a=n.line,o=n.lineType,u=n.lineJointType,c=U(this.props,!1),s=U(a,!1),f,l;if(o==="joint")f=i.map(function(x){return{x:x.cx,y:x.cy}});else if(o==="fitting"){var p=$_(i),h=p.xmin,v=p.xmax,d=p.a,y=p.b,b=function(w){return d*w+y};f=[{x:h,y:b(h)},{x:v,y:b(v)}]}var g=Ve(Ve(Ve({},c),{},{fill:"none",stroke:c&&c.fill},s),{},{points:f});return A.isValidElement(a)?l=A.cloneElement(a,g):H(a)?l=a(g):l=A.createElement(qt,qn({},g,{type:u})),A.createElement(J,{className:"recharts-scatter-line",key:"recharts-scatter-line"},l)}},{key:"render",value:function(){var n=this.props,i=n.hide,a=n.points,o=n.line,u=n.className,c=n.xAxis,s=n.yAxis,f=n.left,l=n.top,p=n.width,h=n.height,v=n.id,d=n.isAnimationActive;if(i||!a||!a.length)return null;var y=this.state.isAnimationFinished,b=Z("recharts-scatter",u),g=c&&c.allowDataOverflow,x=s&&s.allowDataOverflow,w=g||x,m=G(v)?this.id:v;return A.createElement(J,{className:b,clipPath:w?"url(#clipPath-".concat(m,")"):null},g||x?A.createElement("defs",null,A.createElement("clipPath",{id:"clipPath-".concat(m)},A.createElement("rect",{x:g?f:f-p/2,y:x?l:l-h/2,width:g?p:p*2,height:x?h:h*2}))):null,o&&this.renderLine(),this.renderErrorBar(),A.createElement(J,{key:"recharts-scatter-symbols"},this.renderSymbols()),(!d||y)&&ot.renderCallByParent(this.props,a))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curPoints:n.points,prevPoints:i.curPoints}:n.points!==i.curPoints?{curPoints:n.points}:null}}])}(q.PureComponent);Dt(Mo,"displayName","Scatter");Dt(Mo,"defaultProps",{xAxisId:0,yAxisId:0,zAxisId:0,legendType:"circle",lineType:"joint",lineJointType:"linear",data:[],shape:"circle",hide:!1,isAnimationActive:!Et.isSsr,animationBegin:0,animationDuration:400,animationEasing:"linear"});Dt(Mo,"getComposedData",function(e){var t=e.xAxis,r=e.yAxis,n=e.zAxis,i=e.item,a=e.displayedData,o=e.xAxisTicks,u=e.yAxisTicks,c=e.offset,s=i.props.tooltipType,f=Re(i.props.children,uo),l=G(t.dataKey)?i.props.dataKey:t.dataKey,p=G(r.dataKey)?i.props.dataKey:r.dataKey,h=n&&n.dataKey,v=n?n.range:$o.defaultProps.range,d=v&&v[0],y=t.scale.bandwidth?t.scale.bandwidth():0,b=r.scale.bandwidth?r.scale.bandwidth():0,g=a.map(function(x,w){var m=pe(x,l),O=pe(x,p),_=!G(h)&&pe(x,h)||"-",S=[{name:G(t.dataKey)?i.props.name:t.name||t.dataKey,unit:t.unit||"",value:m,payload:x,dataKey:l,type:s},{name:G(r.dataKey)?i.props.name:r.name||r.dataKey,unit:r.unit||"",value:O,payload:x,dataKey:p,type:s}];_!=="-"&&S.push({name:n.name||n.dataKey,unit:n.unit||"",value:_,payload:x,dataKey:h,type:s});var T=zr({axis:t,ticks:o,bandSize:y,entry:x,index:w,dataKey:l}),j=zr({axis:r,ticks:u,bandSize:b,entry:x,index:w,dataKey:p}),P=_!=="-"?n.scale(_):d,E=Math.sqrt(Math.max(P,0)/Math.PI);return Ve(Ve({},x),{},{cx:T,cy:j,x:T-E,y:j-E,xAxis:t,yAxis:r,zAxis:n,width:2*E,height:2*E,size:P,node:{x:m,y:O,z:_},tooltipPayload:S,tooltipPosition:{x:T,y:j},payload:x},f&&f[w]&&f[w].props)});return Ve({points:g},c)});function an(e){"@babel/helpers - typeof";return an=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},an(e)}function I2(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function C2(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Tw(n.key),n)}}function k2(e,t,r){return t&&C2(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function R2(e,t,r){return t=Ka(t),D2(e,Sw()?Reflect.construct(t,r||[],Ka(e).constructor):t.apply(e,r))}function D2(e,t){if(t&&(an(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return N2(e)}function N2(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Sw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(Sw=function(){return!!e})()}function Ka(e){return Ka=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ka(e)}function q2(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Zf(e,t)}function Zf(e,t){return Zf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Zf(e,t)}function Pw(e,t,r){return t=Tw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Tw(e){var t=L2(e,"string");return an(t)=="symbol"?t:t+""}function L2(e,t){if(an(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(an(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function Jf(){return Jf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Jf.apply(this,arguments)}function B2(e){var t=e.xAxisId,r=nh(),n=ih(),i=rw(t);return i==null?null:A.createElement(mn,Jf({},i,{className:Z("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(o){return xt(o,!0)}}))}var ji=function(e){function t(){return I2(this,t),R2(this,t,arguments)}return q2(t,e),k2(t,[{key:"render",value:function(){return A.createElement(B2,this.props)}}])}(A.Component);Pw(ji,"displayName","XAxis");Pw(ji,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function on(e){"@babel/helpers - typeof";return on=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},on(e)}function F2(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function z2(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,$w(n.key),n)}}function W2(e,t,r){return t&&z2(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function U2(e,t,r){return t=Ha(t),K2(e,Ew()?Reflect.construct(t,r||[],Ha(e).constructor):t.apply(e,r))}function K2(e,t){if(t&&(on(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return H2(e)}function H2(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ew(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(Ew=function(){return!!e})()}function Ha(e){return Ha=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ha(e)}function G2(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Qf(e,t)}function Qf(e,t){return Qf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Qf(e,t)}function jw(e,t,r){return t=$w(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function $w(e){var t=V2(e,"string");return on(t)=="symbol"?t:t+""}function V2(e,t){if(on(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(on(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function ep(){return ep=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ep.apply(this,arguments)}var X2=function(t){var r=t.yAxisId,n=nh(),i=ih(),a=nw(r);return a==null?null:A.createElement(mn,ep({},a,{className:Z("recharts-".concat(a.axisType," ").concat(a.axisType),a.className),viewBox:{x:0,y:0,width:n,height:i},ticksGenerator:function(u){return xt(u,!0)}}))},$i=function(e){function t(){return F2(this,t),U2(this,t,arguments)}return G2(t,e),W2(t,[{key:"render",value:function(){return A.createElement(X2,this.props)}}])}(A.Component);jw($i,"displayName","YAxis");jw($i,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});function e0(e){return Q2(e)||J2(e)||Z2(e)||Y2()}function Y2(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Z2(e,t){if(e){if(typeof e=="string")return tp(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tp(e,t)}}function J2(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Q2(e){if(Array.isArray(e))return tp(e)}function tp(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var rp=function(t,r,n,i,a){var o=Re(t,oh),u=Re(t,To),c=[].concat(e0(o),e0(u)),s=Re(t,jo),f="".concat(i,"Id"),l=i[0],p=r;if(c.length&&(p=c.reduce(function(d,y){if(y.props[f]===n&&ft(y.props,"extendDomain")&&N(y.props[l])){var b=y.props[l];return[Math.min(d[0],b),Math.max(d[1],b)]}return d},p)),s.length){var h="".concat(l,"1"),v="".concat(l,"2");p=s.reduce(function(d,y){if(y.props[f]===n&&ft(y.props,"extendDomain")&&N(y.props[h])&&N(y.props[v])){var b=y.props[h],g=y.props[v];return[Math.min(d[0],b,g),Math.max(d[1],b,g)]}return d},p)}return a&&a.length&&(p=a.reduce(function(d,y){return N(y)?[Math.min(d[0],y),Math.max(d[1],y)]:d},p)),p},Ml={exports:{}},t0;function eq(){return t0||(t0=1,function(e){var t=Object.prototype.hasOwnProperty,r="~";function n(){}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1));function i(c,s,f){this.fn=c,this.context=s,this.once=f||!1}function a(c,s,f,l,p){if(typeof f!="function")throw new TypeError("The listener must be a function");var h=new i(f,l||c,p),v=r?r+s:s;return c._events[v]?c._events[v].fn?c._events[v]=[c._events[v],h]:c._events[v].push(h):(c._events[v]=h,c._eventsCount++),c}function o(c,s){--c._eventsCount===0?c._events=new n:delete c._events[s]}function u(){this._events=new n,this._eventsCount=0}u.prototype.eventNames=function(){var s=[],f,l;if(this._eventsCount===0)return s;for(l in f=this._events)t.call(f,l)&&s.push(r?l.slice(1):l);return Object.getOwnPropertySymbols?s.concat(Object.getOwnPropertySymbols(f)):s},u.prototype.listeners=function(s){var f=r?r+s:s,l=this._events[f];if(!l)return[];if(l.fn)return[l.fn];for(var p=0,h=l.length,v=new Array(h);p<h;p++)v[p]=l[p].fn;return v},u.prototype.listenerCount=function(s){var f=r?r+s:s,l=this._events[f];return l?l.fn?1:l.length:0},u.prototype.emit=function(s,f,l,p,h,v){var d=r?r+s:s;if(!this._events[d])return!1;var y=this._events[d],b=arguments.length,g,x;if(y.fn){switch(y.once&&this.removeListener(s,y.fn,void 0,!0),b){case 1:return y.fn.call(y.context),!0;case 2:return y.fn.call(y.context,f),!0;case 3:return y.fn.call(y.context,f,l),!0;case 4:return y.fn.call(y.context,f,l,p),!0;case 5:return y.fn.call(y.context,f,l,p,h),!0;case 6:return y.fn.call(y.context,f,l,p,h,v),!0}for(x=1,g=new Array(b-1);x<b;x++)g[x-1]=arguments[x];y.fn.apply(y.context,g)}else{var w=y.length,m;for(x=0;x<w;x++)switch(y[x].once&&this.removeListener(s,y[x].fn,void 0,!0),b){case 1:y[x].fn.call(y[x].context);break;case 2:y[x].fn.call(y[x].context,f);break;case 3:y[x].fn.call(y[x].context,f,l);break;case 4:y[x].fn.call(y[x].context,f,l,p);break;default:if(!g)for(m=1,g=new Array(b-1);m<b;m++)g[m-1]=arguments[m];y[x].fn.apply(y[x].context,g)}}return!0},u.prototype.on=function(s,f,l){return a(this,s,f,l,!1)},u.prototype.once=function(s,f,l){return a(this,s,f,l,!0)},u.prototype.removeListener=function(s,f,l,p){var h=r?r+s:s;if(!this._events[h])return this;if(!f)return o(this,h),this;var v=this._events[h];if(v.fn)v.fn===f&&(!p||v.once)&&(!l||v.context===l)&&o(this,h);else{for(var d=0,y=[],b=v.length;d<b;d++)(v[d].fn!==f||p&&!v[d].once||l&&v[d].context!==l)&&y.push(v[d]);y.length?this._events[h]=y.length===1?y[0]:y:o(this,h)}return this},u.prototype.removeAllListeners=function(s){var f;return s?(f=r?r+s:s,this._events[f]&&o(this,f)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,e.exports=u}(Ml)),Ml.exports}var tq=eq();const rq=ae(tq);var Il=new rq,Cl="recharts.syncMouseEvents";function gi(e){"@babel/helpers - typeof";return gi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},gi(e)}function nq(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function iq(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Mw(n.key),n)}}function aq(e,t,r){return t&&iq(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function kl(e,t,r){return t=Mw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Mw(e){var t=oq(e,"string");return gi(t)=="symbol"?t:t+""}function oq(e,t){if(gi(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(gi(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var uq=function(){function e(){nq(this,e),kl(this,"activeIndex",0),kl(this,"coordinateList",[]),kl(this,"layout","horizontal")}return aq(e,[{key:"setDetails",value:function(r){var n,i=r.coordinateList,a=i===void 0?null:i,o=r.container,u=o===void 0?null:o,c=r.layout,s=c===void 0?null:c,f=r.offset,l=f===void 0?null:f,p=r.mouseHandlerCallback,h=p===void 0?null:p;this.coordinateList=(n=a!=null?a:this.coordinateList)!==null&&n!==void 0?n:[],this.container=u!=null?u:this.container,this.layout=s!=null?s:this.layout,this.offset=l!=null?l:this.offset,this.mouseHandlerCallback=h!=null?h:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(r){if(this.coordinateList.length!==0)switch(r.key){case"ArrowRight":{if(this.layout!=="horizontal")return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break}case"ArrowLeft":{if(this.layout!=="horizontal")return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse();break}}}},{key:"setIndex",value:function(r){this.activeIndex=r}},{key:"spoofMouse",value:function(){var r,n;if(this.layout==="horizontal"&&this.coordinateList.length!==0){var i=this.container.getBoundingClientRect(),a=i.x,o=i.y,u=i.height,c=this.coordinateList[this.activeIndex].coordinate,s=((r=window)===null||r===void 0?void 0:r.scrollX)||0,f=((n=window)===null||n===void 0?void 0:n.scrollY)||0,l=a+c+s,p=o+this.offset.top+u/2+f;this.mouseHandlerCallback({pageX:l,pageY:p})}}}])}();function cq(e,t,r){if(r==="number"&&t===!0&&Array.isArray(e)){var n=e==null?void 0:e[0],i=e==null?void 0:e[1];if(n&&i&&N(n)&&N(i))return!0}return!1}function sq(e,t,r,n){var i=n/2;return{stroke:"none",fill:"#ccc",x:e==="horizontal"?t.x-i:r.left+.5,y:e==="horizontal"?r.top+.5:t.y-i,width:e==="horizontal"?n:r.width-1,height:e==="horizontal"?r.height-1:n}}function Iw(e){var t=e.cx,r=e.cy,n=e.radius,i=e.startAngle,a=e.endAngle,o=se(t,r,n,i),u=se(t,r,n,a);return{points:[o,u],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}function lq(e,t,r){var n,i,a,o;if(e==="horizontal")n=t.x,a=n,i=r.top,o=r.top+r.height;else if(e==="vertical")i=t.y,o=i,n=r.left,a=r.left+r.width;else if(t.cx!=null&&t.cy!=null)if(e==="centric"){var u=t.cx,c=t.cy,s=t.innerRadius,f=t.outerRadius,l=t.angle,p=se(u,c,s,l),h=se(u,c,f,l);n=p.x,i=p.y,a=h.x,o=h.y}else return Iw(t);return[{x:n,y:i},{x:a,y:o}]}function bi(e){"@babel/helpers - typeof";return bi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},bi(e)}function r0(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ui(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?r0(Object(r),!0).forEach(function(n){fq(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):r0(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function fq(e,t,r){return t=pq(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pq(e){var t=hq(e,"string");return bi(t)=="symbol"?t:t+""}function hq(e,t){if(bi(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(bi(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function dq(e){var t,r,n=e.element,i=e.tooltipEventType,a=e.isActive,o=e.activeCoordinate,u=e.activePayload,c=e.offset,s=e.activeTooltipIndex,f=e.tooltipAxisBandSize,l=e.layout,p=e.chartName,h=(t=n.props.cursor)!==null&&t!==void 0?t:(r=n.type.defaultProps)===null||r===void 0?void 0:r.cursor;if(!n||!h||!a||!o||p!=="ScatterChart"&&i!=="axis")return null;var v,d=qt;if(p==="ScatterChart")v=o,d=ak;else if(p==="BarChart")v=sq(l,o,c,f),d=Jp;else if(l==="radial"){var y=Iw(o),b=y.cx,g=y.cy,x=y.radius,w=y.startAngle,m=y.endAngle;v={cx:b,cy:g,startAngle:w,endAngle:m,innerRadius:x,outerRadius:x},d=PO}else v={points:lq(l,o,c)},d=qt;var O=Ui(Ui(Ui(Ui({stroke:"#ccc",pointerEvents:"none"},c),v),U(h,!1)),{},{payload:u,payloadIndex:s,className:Z("recharts-tooltip-cursor",h.className)});return q.isValidElement(h)?q.cloneElement(h,O):q.createElement(d,O)}var vq=["item"],yq=["children","className","width","height","style","compact","title","desc"];function un(e){"@babel/helpers - typeof";return un=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},un(e)}function Er(){return Er=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Er.apply(this,arguments)}function n0(e,t){return bq(e)||gq(e,t)||kw(e,t)||mq()}function mq(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function gq(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function bq(e){if(Array.isArray(e))return e}function i0(e,t){if(e==null)return{};var r=xq(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function xq(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Oq(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function wq(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Rw(n.key),n)}}function _q(e,t,r){return t&&wq(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Aq(e,t,r){return t=Ga(t),Sq(e,Cw()?Reflect.construct(t,r||[],Ga(e).constructor):t.apply(e,r))}function Sq(e,t){if(t&&(un(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Pq(e)}function Pq(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Cw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(Cw=function(){return!!e})()}function Ga(e){return Ga=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ga(e)}function Tq(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&np(e,t)}function np(e,t){return np=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},np(e,t)}function cn(e){return $q(e)||jq(e)||kw(e)||Eq()}function Eq(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function kw(e,t){if(e){if(typeof e=="string")return ip(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ip(e,t)}}function jq(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function $q(e){if(Array.isArray(e))return ip(e)}function ip(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function a0(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function C(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?a0(Object(r),!0).forEach(function(n){V(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a0(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function V(e,t,r){return t=Rw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Rw(e){var t=Mq(e,"string");return un(t)=="symbol"?t:t+""}function Mq(e,t){if(un(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(un(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Iq={xAxis:["bottom","top"],yAxis:["left","right"]},Cq={width:"100%",height:"100%"},Dw={x:0,y:0};function Ki(e){return e}var kq=function(t,r){return r==="horizontal"?t.x:r==="vertical"?t.y:r==="centric"?t.angle:t.radius},Rq=function(t,r,n,i){var a=r.find(function(f){return f&&f.index===n});if(a){if(t==="horizontal")return{x:a.coordinate,y:i.y};if(t==="vertical")return{x:i.x,y:a.coordinate};if(t==="centric"){var o=a.coordinate,u=i.radius;return C(C(C({},i),se(i.cx,i.cy,u,o)),{},{angle:o,radius:u})}var c=a.coordinate,s=i.angle;return C(C(C({},i),se(i.cx,i.cy,c,s)),{},{angle:s,radius:c})}return Dw},Io=function(t,r){var n=r.graphicalItems,i=r.dataStartIndex,a=r.dataEndIndex,o=(n!=null?n:[]).reduce(function(u,c){var s=c.props.data;return s&&s.length?[].concat(cn(u),cn(s)):u},[]);return o.length>0?o:t&&t.length&&N(i)&&N(a)?t.slice(i,a+1):[]};function Nw(e){return e==="number"?[0,"auto"]:void 0}var ap=function(t,r,n,i){var a=t.graphicalItems,o=t.tooltipAxis,u=Io(r,t);return n<0||!a||!a.length||n>=u.length?null:a.reduce(function(c,s){var f,l=(f=s.props.data)!==null&&f!==void 0?f:r;l&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=n&&(l=l.slice(t.dataStartIndex,t.dataEndIndex+1));var p;if(o.dataKey&&!o.allowDuplicatedCategory){var h=l===void 0?u:l;p=Gi(h,o.dataKey,i)}else p=l&&l[n]||u[n];return p?[].concat(cn(c),[xO(s,p)]):c},[])},o0=function(t,r,n,i){var a=i||{x:t.chartX,y:t.chartY},o=kq(a,n),u=t.orderedTooltipTicks,c=t.tooltipAxis,s=t.tooltipTicks,f=k$(o,u,s,c);if(f>=0&&s){var l=s[f]&&s[f].value,p=ap(t,r,f,l),h=Rq(n,u,f,a);return{activeTooltipIndex:f,activeLabel:l,activePayload:p,activeCoordinate:h}}return null},Dq=function(t,r){var n=r.axes,i=r.graphicalItems,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.layout,l=t.children,p=t.stackOffset,h=dO(f,a);return n.reduce(function(v,d){var y,b=d.type.defaultProps!==void 0?C(C({},d.type.defaultProps),d.props):d.props,g=b.type,x=b.dataKey,w=b.allowDataOverflow,m=b.allowDuplicatedCategory,O=b.scale,_=b.ticks,S=b.includeHidden,T=b[o];if(v[T])return v;var j=Io(t.data,{graphicalItems:i.filter(function(z){var Y,le=o in z.props?z.props[o]:(Y=z.type.defaultProps)===null||Y===void 0?void 0:Y[o];return le===T}),dataStartIndex:c,dataEndIndex:s}),P=j.length,E,$,I;cq(b.domain,w,g)&&(E=yf(b.domain,null,w),h&&(g==="number"||O!=="auto")&&(I=In(j,x,"category")));var M=Nw(g);if(!E||E.length===0){var k,R=(k=b.domain)!==null&&k!==void 0?k:M;if(x){if(E=In(j,x,g),g==="category"&&h){var L=j_(E);m&&L?($=E,E=Ma(0,P)):m||(E=ag(R,E,d).reduce(function(z,Y){return z.indexOf(Y)>=0?z:[].concat(cn(z),[Y])},[]))}else if(g==="category")m?E=E.filter(function(z){return z!==""&&!G(z)}):E=ag(R,E,d).reduce(function(z,Y){return z.indexOf(Y)>=0||Y===""||G(Y)?z:[].concat(cn(z),[Y])},[]);else if(g==="number"){var B=L$(j,i.filter(function(z){var Y,le,me=o in z.props?z.props[o]:(Y=z.type.defaultProps)===null||Y===void 0?void 0:Y[o],Be="hide"in z.props?z.props.hide:(le=z.type.defaultProps)===null||le===void 0?void 0:le.hide;return me===T&&(S||!Be)}),x,a,f);B&&(E=B)}h&&(g==="number"||O!=="auto")&&(I=In(j,x,"category"))}else h?E=Ma(0,P):u&&u[T]&&u[T].hasStack&&g==="number"?E=p==="expand"?[0,1]:bO(u[T].stackGroups,c,s):E=hO(j,i.filter(function(z){var Y=o in z.props?z.props[o]:z.type.defaultProps[o],le="hide"in z.props?z.props.hide:z.type.defaultProps.hide;return Y===T&&(S||!le)}),g,f,!0);if(g==="number")E=rp(l,E,T,a,_),R&&(E=yf(R,E,w));else if(g==="category"&&R){var K=R,X=E.every(function(z){return K.indexOf(z)>=0});X&&(E=K)}}return C(C({},v),{},V({},T,C(C({},b),{},{axisType:a,domain:E,categoricalDomain:I,duplicateDomain:$,originalDomain:(y=b.domain)!==null&&y!==void 0?y:M,isCategorical:h,layout:f})))},{})},Nq=function(t,r){var n=r.graphicalItems,i=r.Axis,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.layout,l=t.children,p=Io(t.data,{graphicalItems:n,dataStartIndex:c,dataEndIndex:s}),h=p.length,v=dO(f,a),d=-1;return n.reduce(function(y,b){var g=b.type.defaultProps!==void 0?C(C({},b.type.defaultProps),b.props):b.props,x=g[o],w=Nw("number");if(!y[x]){d++;var m;return v?m=Ma(0,h):u&&u[x]&&u[x].hasStack?(m=bO(u[x].stackGroups,c,s),m=rp(l,m,x,a)):(m=yf(w,hO(p,n.filter(function(O){var _,S,T=o in O.props?O.props[o]:(_=O.type.defaultProps)===null||_===void 0?void 0:_[o],j="hide"in O.props?O.props.hide:(S=O.type.defaultProps)===null||S===void 0?void 0:S.hide;return T===x&&!j}),"number",f),i.defaultProps.allowDataOverflow),m=rp(l,m,x,a)),C(C({},y),{},V({},x,C(C({axisType:a},i.defaultProps),{},{hide:!0,orientation:Ke(Iq,"".concat(a,".").concat(d%2),null),domain:m,originalDomain:w,isCategorical:v,layout:f})))}return y},{})},qq=function(t,r){var n=r.axisType,i=n===void 0?"xAxis":n,a=r.AxisComp,o=r.graphicalItems,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.children,l="".concat(i,"Id"),p=Re(f,a),h={};return p&&p.length?h=Dq(t,{axes:p,graphicalItems:o,axisType:i,axisIdKey:l,stackGroups:u,dataStartIndex:c,dataEndIndex:s}):o&&o.length&&(h=Nq(t,{Axis:a,graphicalItems:o,axisType:i,axisIdKey:l,stackGroups:u,dataStartIndex:c,dataEndIndex:s})),h},Lq=function(t){var r=Ct(t),n=xt(r,!1,!0);return{tooltipTicks:n,orderedTooltipTicks:Sp(n,function(i){return i.coordinate}),tooltipAxis:r,tooltipAxisBandSize:ga(r,n)}},u0=function(t){var r=t.children,n=t.defaultShowTooltip,i=We(r,Vr),a=0,o=0;return t.data&&t.data.length!==0&&(o=t.data.length-1),i&&i.props&&(i.props.startIndex>=0&&(a=i.props.startIndex),i.props.endIndex>=0&&(o=i.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:a,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:!!n}},Bq=function(t){return!t||!t.length?!1:t.some(function(r){var n=Ot(r&&r.type);return n&&n.indexOf("Bar")>=0})},c0=function(t){return t==="horizontal"?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:t==="vertical"?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:t==="centric"?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},Fq=function(t,r){var n=t.props,i=t.graphicalItems,a=t.xAxisMap,o=a===void 0?{}:a,u=t.yAxisMap,c=u===void 0?{}:u,s=n.width,f=n.height,l=n.children,p=n.margin||{},h=We(l,Vr),v=We(l,$r),d=Object.keys(c).reduce(function(m,O){var _=c[O],S=_.orientation;return!_.mirror&&!_.hide?C(C({},m),{},V({},S,m[S]+_.width)):m},{left:p.left||0,right:p.right||0}),y=Object.keys(o).reduce(function(m,O){var _=o[O],S=_.orientation;return!_.mirror&&!_.hide?C(C({},m),{},V({},S,Ke(m,"".concat(S))+_.height)):m},{top:p.top||0,bottom:p.bottom||0}),b=C(C({},y),d),g=b.bottom;h&&(b.bottom+=h.props.height||Vr.defaultProps.height),v&&r&&(b=N$(b,i,n,r));var x=s-b.left-b.right,w=f-b.top-b.bottom;return C(C({brushBottom:g},b),{},{width:Math.max(x,0),height:Math.max(w,0)})},zq=function(t,r){if(r==="xAxis")return t[r].width;if(r==="yAxis")return t[r].height},Co=function(t){var r=t.chartName,n=t.GraphicalChild,i=t.defaultTooltipEventType,a=i===void 0?"axis":i,o=t.validateTooltipEventTypes,u=o===void 0?["axis"]:o,c=t.axisComponents,s=t.legendContent,f=t.formatAxisMap,l=t.defaultProps,p=function(b,g){var x=g.graphicalItems,w=g.stackGroups,m=g.offset,O=g.updateId,_=g.dataStartIndex,S=g.dataEndIndex,T=b.barSize,j=b.layout,P=b.barGap,E=b.barCategoryGap,$=b.maxBarSize,I=c0(j),M=I.numericAxisName,k=I.cateAxisName,R=Bq(x),L=[];return x.forEach(function(B,K){var X=Io(b.data,{graphicalItems:[B],dataStartIndex:_,dataEndIndex:S}),z=B.type.defaultProps!==void 0?C(C({},B.type.defaultProps),B.props):B.props,Y=z.dataKey,le=z.maxBarSize,me=z["".concat(M,"Id")],Be=z["".concat(k,"Id")],Gt={},De=c.reduce(function(Vt,Xt){var ko=g["".concat(Xt.axisType,"Map")],sh=z["".concat(Xt.axisType,"Id")];ko&&ko[sh]||Xt.axisType==="zAxis"||sr();var lh=ko[sh];return C(C({},Vt),{},V(V({},Xt.axisType,lh),"".concat(Xt.axisType,"Ticks"),xt(lh)))},Gt),F=De[k],Q=De["".concat(k,"Ticks")],ee=w&&w[me]&&w[me].hasStack&&V$(B,w[me].stackGroups),D=Ot(B.type).indexOf("Bar")>=0,ve=ga(F,Q),te=[],xe=R&&R$({barSize:T,stackGroups:w,totalSize:zq(De,k)});if(D){var Oe,Ne,$t=G(le)?$:le,br=(Oe=(Ne=ga(F,Q,!0))!==null&&Ne!==void 0?Ne:$t)!==null&&Oe!==void 0?Oe:0;te=D$({barGap:P,barCategoryGap:E,bandSize:br!==ve?br:ve,sizeList:xe[Be],maxBarSize:$t}),br!==ve&&(te=te.map(function(Vt){return C(C({},Vt),{},{position:C(C({},Vt.position),{},{offset:Vt.position.offset-br/2})})}))}var Mi=B&&B.type&&B.type.getComposedData;Mi&&L.push({props:C(C({},Mi(C(C({},De),{},{displayedData:X,props:b,dataKey:Y,item:B,bandSize:ve,barPosition:te,offset:m,stackedData:ee,layout:j,dataStartIndex:_,dataEndIndex:S}))),{},V(V(V({key:B.key||"item-".concat(K)},M,De[M]),k,De[k]),"animationId",O)),childIndex:F_(B,b.children),item:B})}),L},h=function(b,g){var x=b.props,w=b.dataStartIndex,m=b.dataEndIndex,O=b.updateId;if(!xd({props:x}))return null;var _=x.children,S=x.layout,T=x.stackOffset,j=x.data,P=x.reverseStackOrder,E=c0(S),$=E.numericAxisName,I=E.cateAxisName,M=Re(_,n),k=H$(j,M,"".concat($,"Id"),"".concat(I,"Id"),T,P),R=c.reduce(function(z,Y){var le="".concat(Y.axisType,"Map");return C(C({},z),{},V({},le,qq(x,C(C({},Y),{},{graphicalItems:M,stackGroups:Y.axisType===$&&k,dataStartIndex:w,dataEndIndex:m}))))},{}),L=Fq(C(C({},R),{},{props:x,graphicalItems:M}),g==null?void 0:g.legendBBox);Object.keys(R).forEach(function(z){R[z]=f(x,R[z],L,z.replace("Map",""),r)});var B=R["".concat(I,"Map")],K=Lq(B),X=p(x,C(C({},R),{},{dataStartIndex:w,dataEndIndex:m,updateId:O,graphicalItems:M,stackGroups:k,offset:L}));return C(C({formattedGraphicalItems:X,graphicalItems:M,offset:L,stackGroups:k},K),R)},v=function(y){function b(g){var x,w,m;return Oq(this,b),m=Aq(this,b,[g]),V(m,"eventEmitterSymbol",Symbol("rechartsEventEmitter")),V(m,"accessibilityManager",new uq),V(m,"handleLegendBBoxUpdate",function(O){if(O){var _=m.state,S=_.dataStartIndex,T=_.dataEndIndex,j=_.updateId;m.setState(C({legendBBox:O},h({props:m.props,dataStartIndex:S,dataEndIndex:T,updateId:j},C(C({},m.state),{},{legendBBox:O}))))}}),V(m,"handleReceiveSyncEvent",function(O,_,S){if(m.props.syncId===O){if(S===m.eventEmitterSymbol&&typeof m.props.syncMethod!="function")return;m.applySyncEvent(_)}}),V(m,"handleBrushChange",function(O){var _=O.startIndex,S=O.endIndex;if(_!==m.state.dataStartIndex||S!==m.state.dataEndIndex){var T=m.state.updateId;m.setState(function(){return C({dataStartIndex:_,dataEndIndex:S},h({props:m.props,dataStartIndex:_,dataEndIndex:S,updateId:T},m.state))}),m.triggerSyncEvent({dataStartIndex:_,dataEndIndex:S})}}),V(m,"handleMouseEnter",function(O){var _=m.getMouseInfo(O);if(_){var S=C(C({},_),{},{isTooltipActive:!0});m.setState(S),m.triggerSyncEvent(S);var T=m.props.onMouseEnter;H(T)&&T(S,O)}}),V(m,"triggeredAfterMouseMove",function(O){var _=m.getMouseInfo(O),S=_?C(C({},_),{},{isTooltipActive:!0}):{isTooltipActive:!1};m.setState(S),m.triggerSyncEvent(S);var T=m.props.onMouseMove;H(T)&&T(S,O)}),V(m,"handleItemMouseEnter",function(O){m.setState(function(){return{isTooltipActive:!0,activeItem:O,activePayload:O.tooltipPayload,activeCoordinate:O.tooltipPosition||{x:O.cx,y:O.cy}}})}),V(m,"handleItemMouseLeave",function(){m.setState(function(){return{isTooltipActive:!1}})}),V(m,"handleMouseMove",function(O){O.persist(),m.throttleTriggeredAfterMouseMove(O)}),V(m,"handleMouseLeave",function(O){m.throttleTriggeredAfterMouseMove.cancel();var _={isTooltipActive:!1};m.setState(_),m.triggerSyncEvent(_);var S=m.props.onMouseLeave;H(S)&&S(_,O)}),V(m,"handleOuterEvent",function(O){var _=B_(O),S=Ke(m.props,"".concat(_));if(_&&H(S)){var T,j;/.*touch.*/i.test(_)?j=m.getMouseInfo(O.changedTouches[0]):j=m.getMouseInfo(O),S((T=j)!==null&&T!==void 0?T:{},O)}}),V(m,"handleClick",function(O){var _=m.getMouseInfo(O);if(_){var S=C(C({},_),{},{isTooltipActive:!0});m.setState(S),m.triggerSyncEvent(S);var T=m.props.onClick;H(T)&&T(S,O)}}),V(m,"handleMouseDown",function(O){var _=m.props.onMouseDown;if(H(_)){var S=m.getMouseInfo(O);_(S,O)}}),V(m,"handleMouseUp",function(O){var _=m.props.onMouseUp;if(H(_)){var S=m.getMouseInfo(O);_(S,O)}}),V(m,"handleTouchMove",function(O){O.changedTouches!=null&&O.changedTouches.length>0&&m.throttleTriggeredAfterMouseMove(O.changedTouches[0])}),V(m,"handleTouchStart",function(O){O.changedTouches!=null&&O.changedTouches.length>0&&m.handleMouseDown(O.changedTouches[0])}),V(m,"handleTouchEnd",function(O){O.changedTouches!=null&&O.changedTouches.length>0&&m.handleMouseUp(O.changedTouches[0])}),V(m,"handleDoubleClick",function(O){var _=m.props.onDoubleClick;if(H(_)){var S=m.getMouseInfo(O);_(S,O)}}),V(m,"handleContextMenu",function(O){var _=m.props.onContextMenu;if(H(_)){var S=m.getMouseInfo(O);_(S,O)}}),V(m,"triggerSyncEvent",function(O){m.props.syncId!==void 0&&Il.emit(Cl,m.props.syncId,O,m.eventEmitterSymbol)}),V(m,"applySyncEvent",function(O){var _=m.props,S=_.layout,T=_.syncMethod,j=m.state.updateId,P=O.dataStartIndex,E=O.dataEndIndex;if(O.dataStartIndex!==void 0||O.dataEndIndex!==void 0)m.setState(C({dataStartIndex:P,dataEndIndex:E},h({props:m.props,dataStartIndex:P,dataEndIndex:E,updateId:j},m.state)));else if(O.activeTooltipIndex!==void 0){var $=O.chartX,I=O.chartY,M=O.activeTooltipIndex,k=m.state,R=k.offset,L=k.tooltipTicks;if(!R)return;if(typeof T=="function")M=T(L,O);else if(T==="value"){M=-1;for(var B=0;B<L.length;B++)if(L[B].value===O.activeLabel){M=B;break}}var K=C(C({},R),{},{x:R.left,y:R.top}),X=Math.min($,K.x+K.width),z=Math.min(I,K.y+K.height),Y=L[M]&&L[M].value,le=ap(m.state,m.props.data,M),me=L[M]?{x:S==="horizontal"?L[M].coordinate:X,y:S==="horizontal"?z:L[M].coordinate}:Dw;m.setState(C(C({},O),{},{activeLabel:Y,activeCoordinate:me,activePayload:le,activeTooltipIndex:M}))}else m.setState(O)}),V(m,"renderCursor",function(O){var _,S=m.state,T=S.isTooltipActive,j=S.activeCoordinate,P=S.activePayload,E=S.offset,$=S.activeTooltipIndex,I=S.tooltipAxisBandSize,M=m.getTooltipEventType(),k=(_=O.props.active)!==null&&_!==void 0?_:T,R=m.props.layout,L=O.key||"_recharts-cursor";return A.createElement(dq,{key:L,activeCoordinate:j,activePayload:P,activeTooltipIndex:$,chartName:r,element:O,isActive:k,layout:R,offset:E,tooltipAxisBandSize:I,tooltipEventType:M})}),V(m,"renderPolarAxis",function(O,_,S){var T=Ke(O,"type.axisType"),j=Ke(m.state,"".concat(T,"Map")),P=O.type.defaultProps,E=P!==void 0?C(C({},P),O.props):O.props,$=j&&j[E["".concat(T,"Id")]];return q.cloneElement(O,C(C({},$),{},{className:Z(T,$.className),key:O.key||"".concat(_,"-").concat(S),ticks:xt($,!0)}))}),V(m,"renderPolarGrid",function(O){var _=O.props,S=_.radialLines,T=_.polarAngles,j=_.polarRadius,P=m.state,E=P.radiusAxisMap,$=P.angleAxisMap,I=Ct(E),M=Ct($),k=M.cx,R=M.cy,L=M.innerRadius,B=M.outerRadius;return q.cloneElement(O,{polarAngles:Array.isArray(T)?T:xt(M,!0).map(function(K){return K.coordinate}),polarRadius:Array.isArray(j)?j:xt(I,!0).map(function(K){return K.coordinate}),cx:k,cy:R,innerRadius:L,outerRadius:B,key:O.key||"polar-grid",radialLines:S})}),V(m,"renderLegend",function(){var O=m.state.formattedGraphicalItems,_=m.props,S=_.children,T=_.width,j=_.height,P=m.props.margin||{},E=T-(P.left||0)-(P.right||0),$=fO({children:S,formattedGraphicalItems:O,legendWidth:E,legendContent:s});if(!$)return null;var I=$.item,M=i0($,vq);return q.cloneElement(I,C(C({},M),{},{chartWidth:T,chartHeight:j,margin:P,onBBoxUpdate:m.handleLegendBBoxUpdate}))}),V(m,"renderTooltip",function(){var O,_=m.props,S=_.children,T=_.accessibilityLayer,j=We(S,yt);if(!j)return null;var P=m.state,E=P.isTooltipActive,$=P.activeCoordinate,I=P.activePayload,M=P.activeLabel,k=P.offset,R=(O=j.props.active)!==null&&O!==void 0?O:E;return q.cloneElement(j,{viewBox:C(C({},k),{},{x:k.left,y:k.top}),active:R,label:M,payload:R?I:[],coordinate:$,accessibilityLayer:T})}),V(m,"renderBrush",function(O){var _=m.props,S=_.margin,T=_.data,j=m.state,P=j.offset,E=j.dataStartIndex,$=j.dataEndIndex,I=j.updateId;return q.cloneElement(O,{key:O.key||"_recharts-brush",onChange:Bi(m.handleBrushChange,O.props.onChange),data:T,x:N(O.props.x)?O.props.x:P.left,y:N(O.props.y)?O.props.y:P.top+P.height+P.brushBottom-(S.bottom||0),width:N(O.props.width)?O.props.width:P.width,startIndex:E,endIndex:$,updateId:"brush-".concat(I)})}),V(m,"renderReferenceElement",function(O,_,S){if(!O)return null;var T=m,j=T.clipPathId,P=m.state,E=P.xAxisMap,$=P.yAxisMap,I=P.offset,M=O.type.defaultProps||{},k=O.props,R=k.xAxisId,L=R===void 0?M.xAxisId:R,B=k.yAxisId,K=B===void 0?M.yAxisId:B;return q.cloneElement(O,{key:O.key||"".concat(_,"-").concat(S),xAxis:E[L],yAxis:$[K],viewBox:{x:I.left,y:I.top,width:I.width,height:I.height},clipPathId:j})}),V(m,"renderActivePoints",function(O){var _=O.item,S=O.activePoint,T=O.basePoint,j=O.childIndex,P=O.isRange,E=[],$=_.props.key,I=_.item.type.defaultProps!==void 0?C(C({},_.item.type.defaultProps),_.item.props):_.item.props,M=I.activeDot,k=I.dataKey,R=C(C({index:j,dataKey:k,cx:S.x,cy:S.y,r:4,fill:Zp(_.item),strokeWidth:2,stroke:"#fff",payload:S.payload,value:S.value},U(M,!1)),Vi(M));return E.push(b.renderActiveDot(M,R,"".concat($,"-activePoint-").concat(j))),T?E.push(b.renderActiveDot(M,C(C({},R),{},{cx:T.x,cy:T.y}),"".concat($,"-basePoint-").concat(j))):P&&E.push(null),E}),V(m,"renderGraphicChild",function(O,_,S){var T=m.filterFormatItem(O,_,S);if(!T)return null;var j=m.getTooltipEventType(),P=m.state,E=P.isTooltipActive,$=P.tooltipAxis,I=P.activeTooltipIndex,M=P.activeLabel,k=m.props.children,R=We(k,yt),L=T.props,B=L.points,K=L.isRange,X=L.baseLine,z=T.item.type.defaultProps!==void 0?C(C({},T.item.type.defaultProps),T.item.props):T.item.props,Y=z.activeDot,le=z.hide,me=z.activeBar,Be=z.activeShape,Gt=!!(!le&&E&&R&&(Y||me||Be)),De={};j!=="axis"&&R&&R.props.trigger==="click"?De={onClick:Bi(m.handleItemMouseEnter,O.props.onClick)}:j!=="axis"&&(De={onMouseLeave:Bi(m.handleItemMouseLeave,O.props.onMouseLeave),onMouseEnter:Bi(m.handleItemMouseEnter,O.props.onMouseEnter)});var F=q.cloneElement(O,C(C({},T.props),De));function Q(Xt){return typeof $.dataKey=="function"?$.dataKey(Xt.payload):null}if(Gt)if(I>=0){var ee,D;if($.dataKey&&!$.allowDuplicatedCategory){var ve=typeof $.dataKey=="function"?Q:"payload.".concat($.dataKey.toString());ee=Gi(B,ve,M),D=K&&X&&Gi(X,ve,M)}else ee=B==null?void 0:B[I],D=K&&X&&X[I];if(Be||me){var te=O.props.activeIndex!==void 0?O.props.activeIndex:I;return[q.cloneElement(O,C(C(C({},T.props),De),{},{activeIndex:te})),null,null]}if(!G(ee))return[F].concat(cn(m.renderActivePoints({item:T,activePoint:ee,basePoint:D,childIndex:I,isRange:K})))}else{var xe,Oe=(xe=m.getItemByXY(m.state.activeCoordinate))!==null&&xe!==void 0?xe:{graphicalItem:F},Ne=Oe.graphicalItem,$t=Ne.item,br=$t===void 0?O:$t,Mi=Ne.childIndex,Vt=C(C(C({},T.props),De),{},{activeIndex:Mi});return[q.cloneElement(br,Vt),null,null]}return K?[F,null,null]:[F,null]}),V(m,"renderCustomized",function(O,_,S){return q.cloneElement(O,C(C({key:"recharts-customized-".concat(S)},m.props),m.state))}),V(m,"renderMap",{CartesianGrid:{handler:Ki,once:!0},ReferenceArea:{handler:m.renderReferenceElement},ReferenceLine:{handler:Ki},ReferenceDot:{handler:m.renderReferenceElement},XAxis:{handler:Ki},YAxis:{handler:Ki},Brush:{handler:m.renderBrush,once:!0},Bar:{handler:m.renderGraphicChild},Line:{handler:m.renderGraphicChild},Area:{handler:m.renderGraphicChild},Radar:{handler:m.renderGraphicChild},RadialBar:{handler:m.renderGraphicChild},Scatter:{handler:m.renderGraphicChild},Pie:{handler:m.renderGraphicChild},Funnel:{handler:m.renderGraphicChild},Tooltip:{handler:m.renderCursor,once:!0},PolarGrid:{handler:m.renderPolarGrid,once:!0},PolarAngleAxis:{handler:m.renderPolarAxis},PolarRadiusAxis:{handler:m.renderPolarAxis},Customized:{handler:m.renderCustomized}}),m.clipPathId="".concat((x=g.id)!==null&&x!==void 0?x:zt("recharts"),"-clip"),m.throttleTriggeredAfterMouseMove=lx(m.triggeredAfterMouseMove,(w=g.throttleDelay)!==null&&w!==void 0?w:1e3/60),m.state={},m}return Tq(b,y),_q(b,[{key:"componentDidMount",value:function(){var x,w;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:(x=this.props.margin.left)!==null&&x!==void 0?x:0,top:(w=this.props.margin.top)!==null&&w!==void 0?w:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var x=this.props,w=x.children,m=x.data,O=x.height,_=x.layout,S=We(w,yt);if(S){var T=S.props.defaultIndex;if(!(typeof T!="number"||T<0||T>this.state.tooltipTicks.length-1)){var j=this.state.tooltipTicks[T]&&this.state.tooltipTicks[T].value,P=ap(this.state,m,T,j),E=this.state.tooltipTicks[T].coordinate,$=(this.state.offset.top+O)/2,I=_==="horizontal",M=I?{x:E,y:$}:{y:E,x:$},k=this.state.formattedGraphicalItems.find(function(L){var B=L.item;return B.type.name==="Scatter"});k&&(M=C(C({},M),k.props.points[T].tooltipPosition),P=k.props.points[T].tooltipPayload);var R={activeTooltipIndex:T,isTooltipActive:!0,activeLabel:j,activePayload:P,activeCoordinate:M};this.setState(R),this.renderCursor(S),this.accessibilityManager.setIndex(T)}}}},{key:"getSnapshotBeforeUpdate",value:function(x,w){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==w.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==x.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==x.margin){var m,O;this.accessibilityManager.setDetails({offset:{left:(m=this.props.margin.left)!==null&&m!==void 0?m:0,top:(O=this.props.margin.top)!==null&&O!==void 0?O:0}})}return null}},{key:"componentDidUpdate",value:function(x){Nl([We(x.children,yt)],[We(this.props.children,yt)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var x=We(this.props.children,yt);if(x&&typeof x.props.shared=="boolean"){var w=x.props.shared?"axis":"item";return u.indexOf(w)>=0?w:a}return a}},{key:"getMouseInfo",value:function(x){if(!this.container)return null;var w=this.container,m=w.getBoundingClientRect(),O=TP(m),_={chartX:Math.round(x.pageX-O.left),chartY:Math.round(x.pageY-O.top)},S=m.width/w.offsetWidth||1,T=this.inRange(_.chartX,_.chartY,S);if(!T)return null;var j=this.state,P=j.xAxisMap,E=j.yAxisMap,$=this.getTooltipEventType(),I=o0(this.state,this.props.data,this.props.layout,T);if($!=="axis"&&P&&E){var M=Ct(P).scale,k=Ct(E).scale,R=M&&M.invert?M.invert(_.chartX):null,L=k&&k.invert?k.invert(_.chartY):null;return C(C({},_),{},{xValue:R,yValue:L},I)}return I?C(C({},_),I):null}},{key:"inRange",value:function(x,w){var m=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,O=this.props.layout,_=x/m,S=w/m;if(O==="horizontal"||O==="vertical"){var T=this.state.offset,j=_>=T.left&&_<=T.left+T.width&&S>=T.top&&S<=T.top+T.height;return j?{x:_,y:S}:null}var P=this.state,E=P.angleAxisMap,$=P.radiusAxisMap;if(E&&$){var I=Ct(E);return cg({x:_,y:S},I)}return null}},{key:"parseEventsOfWrapper",value:function(){var x=this.props.children,w=this.getTooltipEventType(),m=We(x,yt),O={};m&&w==="axis"&&(m.props.trigger==="click"?O={onClick:this.handleClick}:O={onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu});var _=Vi(this.props,this.handleOuterEvent);return C(C({},_),O)}},{key:"addListener",value:function(){Il.on(Cl,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){Il.removeListener(Cl,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(x,w,m){for(var O=this.state.formattedGraphicalItems,_=0,S=O.length;_<S;_++){var T=O[_];if(T.item===x||T.props.key===x.key||w===Ot(T.item.type)&&m===T.childIndex)return T}return null}},{key:"renderClipPath",value:function(){var x=this.clipPathId,w=this.state.offset,m=w.left,O=w.top,_=w.height,S=w.width;return A.createElement("defs",null,A.createElement("clipPath",{id:x},A.createElement("rect",{x:m,y:O,height:_,width:S})))}},{key:"getXScales",value:function(){var x=this.state.xAxisMap;return x?Object.entries(x).reduce(function(w,m){var O=n0(m,2),_=O[0],S=O[1];return C(C({},w),{},V({},_,S.scale))},{}):null}},{key:"getYScales",value:function(){var x=this.state.yAxisMap;return x?Object.entries(x).reduce(function(w,m){var O=n0(m,2),_=O[0],S=O[1];return C(C({},w),{},V({},_,S.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(x){var w;return(w=this.state.xAxisMap)===null||w===void 0||(w=w[x])===null||w===void 0?void 0:w.scale}},{key:"getYScaleByAxisId",value:function(x){var w;return(w=this.state.yAxisMap)===null||w===void 0||(w=w[x])===null||w===void 0?void 0:w.scale}},{key:"getItemByXY",value:function(x){var w=this.state,m=w.formattedGraphicalItems,O=w.activeItem;if(m&&m.length)for(var _=0,S=m.length;_<S;_++){var T=m[_],j=T.props,P=T.item,E=P.type.defaultProps!==void 0?C(C({},P.type.defaultProps),P.props):P.props,$=Ot(P.type);if($==="Bar"){var I=(j.data||[]).find(function(L){return LC(x,L)});if(I)return{graphicalItem:T,payload:I}}else if($==="RadialBar"){var M=(j.data||[]).find(function(L){return cg(x,L)});if(M)return{graphicalItem:T,payload:M}}else if(_o(T,O)||Ao(T,O)||hi(T,O)){var k=sR({graphicalItem:T,activeTooltipItem:O,itemData:E.data}),R=E.activeIndex===void 0?k:E.activeIndex;return{graphicalItem:C(C({},T),{},{childIndex:R}),payload:hi(T,O)?E.data[k]:T.props.data[k]}}}return null}},{key:"render",value:function(){var x=this;if(!xd(this))return null;var w=this.props,m=w.children,O=w.className,_=w.width,S=w.height,T=w.style,j=w.compact,P=w.title,E=w.desc,$=i0(w,yq),I=U($,!1);if(j)return A.createElement(kb,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},A.createElement(Ll,Er({},I,{width:_,height:S,title:P,desc:E}),this.renderClipPath(),wd(m,this.renderMap)));if(this.props.accessibilityLayer){var M,k;I.tabIndex=(M=this.props.tabIndex)!==null&&M!==void 0?M:0,I.role=(k=this.props.role)!==null&&k!==void 0?k:"application",I.onKeyDown=function(L){x.accessibilityManager.keyboardEvent(L)},I.onFocus=function(){x.accessibilityManager.focus()}}var R=this.parseEventsOfWrapper();return A.createElement(kb,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},A.createElement("div",Er({className:Z("recharts-wrapper",O),style:C({position:"relative",cursor:"default",width:_,height:S},T)},R,{ref:function(B){x.container=B}}),A.createElement(Ll,Er({},I,{width:_,height:S,title:P,desc:E,style:Cq}),this.renderClipPath(),wd(m,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}])}(q.Component);V(v,"displayName",r),V(v,"defaultProps",C({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},l)),V(v,"getDerivedStateFromProps",function(y,b){var g=y.dataKey,x=y.data,w=y.children,m=y.width,O=y.height,_=y.layout,S=y.stackOffset,T=y.margin,j=b.dataStartIndex,P=b.dataEndIndex;if(b.updateId===void 0){var E=u0(y);return C(C(C({},E),{},{updateId:0},h(C(C({props:y},E),{},{updateId:0}),b)),{},{prevDataKey:g,prevData:x,prevWidth:m,prevHeight:O,prevLayout:_,prevStackOffset:S,prevMargin:T,prevChildren:w})}if(g!==b.prevDataKey||x!==b.prevData||m!==b.prevWidth||O!==b.prevHeight||_!==b.prevLayout||S!==b.prevStackOffset||!jr(T,b.prevMargin)){var $=u0(y),I={chartX:b.chartX,chartY:b.chartY,isTooltipActive:b.isTooltipActive},M=C(C({},o0(b,x,_)),{},{updateId:b.updateId+1}),k=C(C(C({},$),I),M);return C(C(C({},k),h(C({props:y},k),b)),{},{prevDataKey:g,prevData:x,prevWidth:m,prevHeight:O,prevLayout:_,prevStackOffset:S,prevMargin:T,prevChildren:w})}if(!Nl(w,b.prevChildren)){var R,L,B,K,X=We(w,Vr),z=X&&(R=(L=X.props)===null||L===void 0?void 0:L.startIndex)!==null&&R!==void 0?R:j,Y=X&&(B=(K=X.props)===null||K===void 0?void 0:K.endIndex)!==null&&B!==void 0?B:P,le=z!==j||Y!==P,me=!G(x),Be=me&&!le?b.updateId:b.updateId+1;return C(C({updateId:Be},h(C(C({props:y},b),{},{updateId:Be,dataStartIndex:z,dataEndIndex:Y}),b)),{},{prevChildren:w,dataStartIndex:z,dataEndIndex:Y})}return null}),V(v,"renderActiveDot",function(y,b,g){var x;return q.isValidElement(y)?x=q.cloneElement(y,b):H(y)?x=y(b):x=A.createElement(Ti,b),A.createElement(J,{className:"recharts-active-dot",key:g},x)});var d=q.forwardRef(function(b,g){return A.createElement(v,Er({},b,{ref:g}))});return d.displayName=v.displayName,d},Gq=Co({chartName:"LineChart",GraphicalChild:Ei,axisComponents:[{axisType:"xAxis",AxisComp:ji},{axisType:"yAxis",AxisComp:$i}],formatAxisMap:Qp}),Vq=Co({chartName:"BarChart",GraphicalChild:mr,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:ji},{axisType:"yAxis",AxisComp:$i}],formatAxisMap:Qp}),Xq=Co({chartName:"PieChart",GraphicalChild:Ht,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:wo},{axisType:"radiusAxis",AxisComp:xo}],formatAxisMap:iM,defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}}),Yq=Co({chartName:"ComposedChart",GraphicalChild:[Ei,gr,mr,Mo],axisComponents:[{axisType:"xAxis",AxisComp:ji},{axisType:"yAxis",AxisComp:$i},{axisType:"zAxis",AxisComp:$o}],formatAxisMap:Qp});export{gr as A,mr as B,uo as C,$r as L,Xq as P,Hq as R,yt as T,ji as X,$i as Y,Kq as _,Ht as a,Yq as b,zN as c,Ei as d,Gq as e,Vq as f,sr as i};
