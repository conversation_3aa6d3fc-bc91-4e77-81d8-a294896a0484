<?php
// DEBUGGING: Log the entire $_SERVER array at the very beginning
error_log("Task API (SERVER VARS): " . print_r($_SERVER, true));

// DEBUGGING: Log GET parameters at the very beginning
error_log("Task API (TOP): Checking GET params - action: " . ($_GET['action'] ?? 'NOT SET') . ", publisher_id: " . ($_GET['publisher_id'] ?? 'NOT SET'));

// Göndermeden önce hata ayıklama ve geçersiz/uygun olmayan JSON dönüşünü önleme için
ini_set('display_errors', 0);
error_reporting(E_ALL);

require_once __DIR__ . '/../config/config.php';
$conn = $db;
require_once __DIR__ . '/auth.php';

// Kullanıcı ID'sinden 'name' almak için fonksiyon
function getNameById($db, $userId) {
    try {
        $stmt = $db->prepare("SELECT name FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        return $user ? $user['name'] : null;
    } catch (PDOException $e) {
        error_log("Kullanıcı adı (name) alınırken hata: " . $e->getMessage());
        return null;
    }
}

// Haftalık görevleri getirme sorgusu
function getWeeklyTasks($db, $start_date, $end_date) {
    try {
        // Doğru aralık: başlangıç dahil, bitiş hariç
        $stmt = $db->prepare("
            SELECT *
            FROM gorevler
            WHERE baslangic_tarihi >= ?
              AND baslangic_tarihi < ?
            ORDER BY kullanici_adi, baslangic_tarihi
        ");
        $stmt->execute([
            $start_date, $end_date
        ]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Haftalık görevleri getirme hatası: " . $e->getMessage());
        throw new Exception("Haftalık görevler getirilirken bir hata oluştu");
    }
}

function jsonResponse($data, $status = 200) {
    http_response_code($status);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

// Tüm görevleri getir
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // Eğer kullanici_adi, start_date ve end_date parametreleri varsa, weekly_tasks tablosundan sadece o kullanıcı ve hafta aralığına ait görevleri getir
    if (isset($_GET['kullanici_adi']) && isset($_GET['start_date']) && isset($_GET['end_date'])) {
        $kullanici_adi = $_GET['kullanici_adi'];
        $start_date = $_GET['start_date'];
        $end_date = $_GET['end_date'];

        // Kullanıcı adını normalize et (trim ve küçük harf)
        $kullanici_adi = strtolower(trim($kullanici_adi));

        error_log("Weekly Tasks Query: kullanici_adi=$kullanici_adi, start_date=$start_date, end_date=$end_date");

        $stmt = $db->prepare("
            SELECT *
            FROM weekly_tasks
            WHERE LOWER(TRIM(kullanici_adi)) = ?
              AND DATE(hafta_baslangici) = DATE(?)
            ORDER BY id
        ");
        $stmt->execute([$kullanici_adi, $start_date]);
        $tasks = $stmt->fetchAll(PDO::FETCH_ASSOC);

        error_log("Weekly Tasks Result: Found " . count($tasks) . " tasks for user $kullanici_adi");
        if (count($tasks) > 0) {
            error_log("First task: " . print_r($tasks[0], true));
        }

        jsonResponse($tasks);
        exit;
    }
    try {
        // Token kontrolü olmadan, tüm görevleri getir
        $stmt = $db->query("SELECT * FROM gorevler ORDER BY baslangic_tarihi DESC");
        $tasks = $stmt->fetchAll(PDO::FETCH_ASSOC);
        jsonResponse($tasks);
    } catch (PDOException $e) {
        error_log("Task API - Görev getirme hatası (DB): " . $e->getMessage());
        jsonResponse(['error' => 'Görevler getirilirken bir hata oluştu', 'details' => $e->getMessage()], 500);
    }
}

// Yeni görev oluştur
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Token ile kimlik doğrula ve kullanıcı ID'sini al
    $userId = requireAuthToken();
    // Kullanıcı ID'sinden name'i al
    $userName = getNameById($db, $userId);
    if (!$userName) {
        jsonResponse(['error' => 'Kimliği doğrulanmış kullanıcı adı (name) alınamadı'], 401);
    }

    // Gelen veriyi doğrula (validateRequest fonksiyonu config.php'de)
    $data = validateRequest(['gorev_onerisi', 'gorev_zorlugu', 'aciklama', 'baslangic_tarihi', 'bitis_tarihi']);

    try {
        $stmt = $db->prepare("
            INSERT INTO gorevler (
                kullanici_adi, gorev_onerisi, gorev_zorlugu, aciklama,
                baslangic_tarihi, bitis_tarihi, oncelik, otomatik_atama, durum
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'Beklemede')
        ");

        $stmt->execute([
            $userName,
            $data['gorev_onerisi'],
            $data['gorev_zorlugu'],
            $data['aciklama'],
            $data['baslangic_tarihi'],
            $data['bitis_tarihi'],
            $data['oncelik'] ?? 'Normal',
            $data['otomatik_atama'] ?? 0
        ]);

        $taskId = $db->lastInsertId();

        // Oluşturulan görevi getir (opsiyonel, yanıt için)
        $stmt = $db->prepare("
            SELECT g.*, u.name as atanan_kullanici_adi
            FROM gorevler g
            LEFT JOIN users u ON g.kullanici_adi = u.name
            WHERE g.id = ?
        ");
        $stmt->execute([$taskId]);
        $task = $stmt->fetch(PDO::FETCH_ASSOC);

        jsonResponse($task, 201);
    } catch (PDOException $e) {
        error_log("Görev oluşturma hatası (POST): " . $e->getMessage());
        jsonResponse(['error' => 'Görev oluşturulurken bir hata oluştu'], 500);
    }
}

// Görev güncelle
if ($_SERVER['REQUEST_METHOD'] === 'PUT') {
    // Token ile kimlik doğrula ve kullanıcı ID'sini al
    $userId = requireAuthToken();
    // Kullanıcı ID'sinden name'i al
    $userName = getNameById($db, $userId);
    if (!$userName) {
        jsonResponse(['error' => 'Kimliği doğrulanmış kullanıcı adı (name) alınamadı'], 401);
    }

    // Görev ID'sini al
    $taskId = $_GET['id'] ?? null;
    if (!$taskId) {
        jsonResponse(['error' => 'Görev ID\'si gerekli'], 400);
    }

    // Gelen veriyi doğrula
    $data = validateRequest(['gorev_onerisi', 'gorev_zorlugu', 'aciklama', 'baslangic_tarihi', 'bitis_tarihi', 'durum']);

    try {
        // Görevin sahibi mi kontrol et (kullanici_adi = name eşleşmesi varsayımı)
        $stmt = $db->prepare("SELECT kullanici_adi FROM gorevler WHERE id = ?");
        $stmt->execute([$taskId]);
        $task = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$task) {
            jsonResponse(['error' => 'Görev bulunamadı'], 404);
        }

        if ($task['kullanici_adi'] !== $userName) {
            jsonResponse(['error' => 'Bu görevi güncelleme yetkiniz yok'], 403);
        }

        $stmt = $db->prepare("
            UPDATE gorevler
            SET gorev_onerisi = ?, gorev_zorlugu = ?, aciklama = ?,
                baslangic_tarihi = ?, bitis_tarihi = ?, oncelik = ?,
                otomatik_atama = ?, durum = ?, ilerleme = ?
            WHERE id = ? AND kullanici_adi = ?
        ");

        $stmt->execute([
            $data['gorev_onerisi'],
            $data['gorev_zorlugu'],
            $data['aciklama'],
            $data['baslangic_tarihi'],
            $data['bitis_tarihi'],
            $data['oncelik'] ?? 'Normal',
            $data['otomatik_atama'] ?? 0,
            $data['durum'],
            $data['ilerleme'] ?? 0,
            $taskId,
            $userName
        ]);

        // Güncellenmiş görevi getir
        $stmt = $db->prepare("
            SELECT g.*, u.name as atanan_kullanici_adi
            FROM gorevler g
            LEFT JOIN users u ON g.kullanici_adi = u.name
            WHERE g.id = ?
        ");
        $stmt->execute([$taskId]);
        $updatedTask = $stmt->fetch(PDO::FETCH_ASSOC);

        jsonResponse($updatedTask);
    } catch (PDOException $e) {
        error_log("Görev güncelleme hatası (PUT): " . $e->getMessage());
        jsonResponse(['error' => 'Görev güncellenirken bir hata oluştu'], 500);
    }
}

// Görev sil
if ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
    // Token ile kimlik doğrula ve kullanıcı ID'sini al
    $userId = requireAuthToken();
    // Kullanıcı ID'sinden name'i al
    $userName = getNameById($db, $userId);
    if (!$userName) {
        jsonResponse(['error' => 'Kimliği doğrulanmış kullanıcı adı (name) alınamadı'], 401);
    }

    // Görev ID'sini al
    $taskId = $_GET['id'] ?? null;
    if (!$taskId) {
        jsonResponse(['error' => 'Görev ID\'si gerekli'], 400);
    }

    try {
        // Görevin sahibi mi kontrol et (kullanici_adi = name eşleşmesi varsayımı)
        $stmt = $db->prepare("SELECT kullanici_adi FROM gorevler WHERE id = ?");
        $stmt->execute([$taskId]);
        $task = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$task) {
            jsonResponse(['error' => 'Görev bulunamadı'], 404);
        }

        if ($task['kullanici_adi'] !== $userName) {
            jsonResponse(['error' => 'Bu görevi silme yetkiniz yok'], 403);
        }

        $stmt = $db->prepare("DELETE FROM gorevler WHERE id = ? AND kullanici_adi = ?");
        $stmt->execute([$taskId, $userName]);

        if ($stmt->rowCount() > 0) {
             jsonResponse(['message' => 'Görev başarıyla silindi']);
        } else {
             jsonResponse(['error' => 'Görev silinemedi veya bulunamadı'], 404);
        }

    } catch (PDOException $e) {
        error_log("Görev silme hatası (DELETE): " . $e->getMessage());
        jsonResponse(['error' => 'Görev silinirken bir hata oluştu'], 500);
    }
}