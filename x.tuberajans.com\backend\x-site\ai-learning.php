<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/auth.php';

// CORS ayarları
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json');

// OPTIONS isteğini yanıtla
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Yetkilendirme kontrolü
$user = checkAuth();
if (!$user) {
    http_response_code(401);
    echo json_encode(['error' => 'Yetkisiz erişim']);
    exit();
}

// POST isteği kontrolü
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

// İstek verilerini al
$data = json_decode(file_get_contents('php://input'), true);

// Gerekli alanları kontrol et
if (!isset($data['conversation']) || !isset($data['feedback'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Eksik parametreler']);
    exit();
}

$conversation = $data['conversation'];
$feedback = $data['feedback'];

try {
    // Veritabanı bağlantısı
    $db = getDB();
    
    // Konuşmayı kaydet
    $stmt = $db->prepare("
        INSERT INTO ai_learning_logs (
            user_id,
            conversation,
            feedback,
            created_at
        ) VALUES (
            :user_id,
            :conversation,
            :feedback,
            NOW()
        )
    ");
    
    $stmt->execute([
        ':user_id' => $user['id'],
        ':conversation' => json_encode($conversation),
        ':feedback' => $feedback
    ]);
    
    // Başarılı yanıt döndür
    echo json_encode([
        'success' => true,
        'message' => 'Öğrenme verisi kaydedildi'
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Veritabanı hatası: ' . $e->getMessage()
    ]);
}

// Öğrenme verilerini getir
function getLearningData() {
    try {
        $db = getDB();
        
        $stmt = $db->query("
            SELECT 
                l.*,
                u.name as user_name
            FROM ai_learning_logs l
            JOIN users u ON l.user_id = u.id
            ORDER BY l.created_at DESC
            LIMIT 100
        ");
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        throw new Exception('Öğrenme verileri alınamadı: ' . $e->getMessage());
    }
} 