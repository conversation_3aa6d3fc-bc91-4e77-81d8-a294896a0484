<?php
session_start();

require_once dirname(__DIR__) . '/config/config.php';
require_once '../config/database.php';
require_once '../utils/auth.php';

header("Access-Control-Allow-Origin: https://akademi.tuberajans.com");
header("Access-Control-Allow-Credentials: true");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
header("Content-Type: application/json; charset=utf-8");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'status' => 'error',
        'message' => 'Method not allowed'
    ]);
    exit;
}

try {
    $auth = new Auth($conn);
    $currentUser = $auth->getCurrentUser();

    if (!$currentUser) {
        http_response_code(401);
        echo json_encode([
            'status' => 'error',
            'message' => 'Unauthorized access'
        ]);
        exit;
    }

    // TikTok bağlantısını kaldır
    $stmt = $conn->prepare("
        UPDATE users 
        SET 
            tiktok_open_id = NULL,
            tiktok_union_id = NULL,
            tiktok_username = NULL,
            tiktok_display_name = NULL,
            tiktok_avatar_url = NULL,
            tiktok_bio = NULL,
            is_verified = 0,
            follower_count = NULL,
            following_count = NULL,
            likes_count = NULL,
            video_count = NULL,
            access_token = NULL,
            refresh_token = NULL,
            token_expires_at = NULL,
            tiktok_linked_at = NULL,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
    ");

    $stmt->bind_param('i', $currentUser['id']);

    if ($stmt->execute()) {
        echo json_encode([
            'status' => 'success',
            'message' => 'TikTok bağlantısı başarıyla kaldırıldı'
        ]);
    } else {
        throw new Exception('TikTok bağlantısı kaldırılamadı');
    }

} catch (Exception $e) {
    error_log('TikTok disconnect error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Internal server error',
        'debug' => $e->getMessage()
    ]);
}
?>
