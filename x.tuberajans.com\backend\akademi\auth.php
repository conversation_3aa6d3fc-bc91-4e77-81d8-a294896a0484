<?php
/**
 * Akademi Auth API
 *
 * Bu dosya, akademi.tuberajans.com sitesi için kimlik doğrulama API'sini sağlar.
 * Kullanıcı girişi, çıkışı ve token doğrulama işlemlerini yönetir.
 *
 * Not: Bu API, tuberaja_yayinci_takip veritabanındaki kullanıcıları kullanır.
 * Akademi sitesi için ayrı bir kullanıcı tablosu oluşturmak yerine, mevcut kullanıcıları kullanıyoruz.
 */

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// CORS başlıkları
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
header("Access-Control-Max-Age: 86400");
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(204);
    exit();
}

// Content-Type header'ını ayarla
header('Content-Type: application/json; charset=utf-8');

try {
    require_once __DIR__ . '/../config/config.php';
} catch (Exception $e) {
    error_log("Config dosyası yüklenemedi: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Server configuration error', 'message' => $e->getMessage()]);
    exit;
}

// Session başlat
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Bearer token'ı al
function getBearerToken() {
    $headers = null;
    if (isset($_SERVER['Authorization'])) {
        $headers = trim($_SERVER['Authorization']);
    } elseif (isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers = trim($_SERVER['HTTP_AUTHORIZATION']);
    } elseif (function_exists('apache_request_headers')) {
        $requestHeaders = apache_request_headers();
        $requestHeaders = array_combine(
            array_map('ucwords', array_keys($requestHeaders)),
            array_values($requestHeaders)
        );
        if (isset($requestHeaders['Authorization'])) {
            $headers = trim($requestHeaders['Authorization']);
        }
    }

    // Bearer token'ı ayıkla
    if (!empty($headers)) {
        if (preg_match('/Bearer\s(\S+)/', $headers, $matches)) {
            return $matches[1];
        }
    }
    return null;
}

// Token ile kimlik doğrulaması
function requireAuthToken() {
    $token = getBearerToken();
    if (!$token) {
        http_response_code(401);
        echo json_encode(['error' => 'Unauthorized', 'message' => 'Token required']);
        exit;
    }

    global $db_takip;

    // Veritabanı bağlantısını kontrol et
    if (!isset($db_takip) || !$db_takip) {
        http_response_code(500);
        echo json_encode(['error' => 'Server Error', 'message' => 'Database connection failed']);
        exit;
    }

    // Gerçek veritabanı kontrolü
    try {
        $stmt = $db_takip->prepare("SELECT user_id FROM user_tokens WHERE token = ? AND expires_at > NOW()");
        $stmt->execute([$token]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result && isset($result['user_id'])) {
            return $result['user_id'];
        } else {
            http_response_code(401);
            echo json_encode(['error' => 'Unauthorized', 'message' => 'Invalid or expired token']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Token doğrulama hatası: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Server Error', 'message' => 'Database error: ' . $e->getMessage()]);
        exit;
    }
}

// Auth kontrolü
if (isset($_GET['check'])) {
    $token = getBearerToken();

    if ($token) {
        try {
            $stmt = $db_takip->prepare("
                SELECT u.id, u.name, u.email, u.username, u.role
                FROM users u
                JOIN user_tokens t ON u.id = t.user_id
                WHERE t.token = ? AND t.expires_at > NOW()
            ");
            $stmt->execute([$token]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($user) {
                // Token süresini uzat
                $expiresAt = date('Y-m-d H:i:s', strtotime('+24 hours'));
                $updateStmt = $db_takip->prepare("UPDATE user_tokens SET expires_at = ? WHERE token = ?");
                $updateStmt->execute([$expiresAt, $token]);

                http_response_code(200);
                echo json_encode([
                    'authenticated' => true,
                    'user' => [
                        'id' => $user['id'],
                        'name' => $user['name'],
                        'email' => $user['email'],
                        'username' => $user['username'],
                        'role' => $user['role']
                    ]
                ]);
                exit;
            }
        } catch (PDOException $e) {
            error_log("Veritabanı hatası: " . $e->getMessage());
        }
    }

    // Token geçersizse
    http_response_code(200);
    echo json_encode([
        'authenticated' => false,
        'message' => 'Oturum bulunamadı veya süresi doldu. Lütfen giriş yapın.'
    ]);
    exit;
}

// Debug endpoint - Sadece geliştirme aşamasında kullanılır
if (isset($_GET['debug'])) {
    $debug_info = [
        'server' => $_SERVER,
        'request_method' => $_SERVER['REQUEST_METHOD'],
        'http_origin' => isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : 'not set',
        'remote_addr' => $_SERVER['REMOTE_ADDR'],
        'db_takip' => isset($db_takip) ? 'connected' : 'not connected',
        'db_akademi' => isset($db_akademi) ? 'connected' : 'not connected',
        'php_version' => phpversion(),
        'time' => date('Y-m-d H:i:s')
    ];

    echo json_encode($debug_info);
    exit();
}

// Giriş işlemi
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isset($_GET['action'])) {
    // Gelen JSON verisini al
    $raw_input = file_get_contents('php://input');
    $data = json_decode($raw_input, true);

    // Debug log
    error_log("Login attempt: " . json_encode([
        'input' => $raw_input,
        'parsed_data' => $data,
        'headers' => getallheaders()
    ]));

    if ((!isset($data['email']) && !isset($data['username'])) || !isset($data['password'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Kullanıcı adı/E-posta ve şifre gerekli']);
        exit();
    }

    try {
        // Kullanıcı adı veya e-posta ile giriş yapabilme
        $identifier = isset($data['email']) ? $data['email'] : $data['username'];
        $identifier = strtolower(trim($identifier));

        // E-posta veya kullanıcı adı ile kullanıcıyı bul
        $stmt = $db_takip->prepare("SELECT id, email, username, password, role, name FROM users WHERE LOWER(email) = LOWER(?) OR LOWER(username) = LOWER(?)");
        $stmt->execute([$identifier, $identifier]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($user) {
            // Şifre doğrulama işlemi
            $password_verified = password_verify($data['password'], $user['password']);
            $ip = $_SERVER['REMOTE_ADDR'] ?? '';
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

            if ($password_verified) {
                // Giriş başarılıysa, login_attempts tablosuna kayıt ekle
                $status = 'success';
                $stmtLog = $db_takip->prepare("INSERT INTO login_attempts (email, attempt_time, status, ip_address, user_agent) VALUES (?, NOW(), ?, ?, ?)");
                $stmtLog->execute([$user['email'], $status, $ip, $userAgent]);

                // Token oluştur
                $token = bin2hex(random_bytes(32));
                $expires = date('Y-m-d H:i:s', strtotime('+24 hours'));

                // Önceki token'ları temizle
                $stmtClean = $db_takip->prepare("DELETE FROM user_tokens WHERE user_id = ?");
                $stmtClean->execute([$user['id']]);

                // Yeni token'ı kaydet
                $stmtToken = $db_takip->prepare("INSERT INTO user_tokens (user_id, token, expires_at) VALUES (?, ?, ?)");
                $stmtToken->execute([$user['id'], $token, $expires]);

                http_response_code(200);
                echo json_encode([
                    'success' => true,
                    'token' => $token,
                    'expires_at' => $expires,
                    'user' => [
                        'id' => $user['id'],
                        'email' => $user['email'],
                        'username' => $user['username'],
                        'role' => $user['role'],
                        'name' => $user['name']
                    ]
                ]);
            } else {
                // Giriş başarısızsa, login_attempts tablosuna kayıt ekle
                $status = 'fail';
                $stmtLog = $db_takip->prepare("INSERT INTO login_attempts (email, attempt_time, status, ip_address, user_agent) VALUES (?, NOW(), ?, ?, ?)");
                $stmtLog->execute([$user['email'], $status, $ip, $userAgent]);

                http_response_code(401);
                echo json_encode(['error' => 'Geçersiz kullanıcı adı veya şifre']);
            }
        } else {
            // Kullanıcı bulunamadı
            http_response_code(401);
            echo json_encode(['error' => 'Geçersiz kullanıcı adı veya şifre']);
        }
    } catch (PDOException $e) {
        error_log("Veritabanı hatası: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Sunucu hatası', 'detay' => $e->getMessage()]);
    }
    exit();
}

// Çıkış yap
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_GET['action']) && $_GET['action'] === 'logout') {
    $token = getBearerToken();

    if ($token) {
        try {
            $stmt = $db_takip->prepare("DELETE FROM user_tokens WHERE token = ?");
            $stmt->execute([$token]);

            http_response_code(200);
            echo json_encode(['success' => true, 'message' => 'Çıkış başarılı']);
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Sunucu hatası']);
        }
    } else {
        http_response_code(401);
        echo json_encode(['error' => 'Token gerekli']);
    }
    exit();
}
