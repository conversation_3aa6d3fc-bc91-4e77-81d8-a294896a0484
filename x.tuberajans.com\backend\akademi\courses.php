<?php
/**
 * Kurslar API
 * Bu API kursları listelemek, eklemek, düzenlemek ve silmek için kullanılır
 */

// Hata ayıklama
ini_set('display_errors', 1);
error_reporting(E_ALL);

// CORS ve JSON header'ları
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS, DELETE');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=utf-8');

// OPTIONS istekleri için hızlıca 200 dön
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Ortak config dosyasını dahil et
require_once __DIR__ . '/../config/config.php';

// JSON yanıt fonksiyonu
if (!function_exists('jsonResponse')) {
    function jsonResponse($data, $status = 200) {
        http_response_code($status);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}

// Auth kontrolü (checkAuth fonksiyonu - events.php'deki gibi)
if (!function_exists('checkAuth')) {
    function checkAuth() {
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
        
        if (strpos($authHeader, 'Bearer ') === 0) {
            $token = substr($authHeader, 7);
            if (strlen($token) > 10) { // Basit token kontrolü
                return true;
            }
        }
        return false;
    }
}

// Token doğrulaması - sadece GET istekleri için şimdilik kaldıralım, çünkü kurslar public olabilir
// if ($_SERVER['REQUEST_METHOD'] !== 'OPTIONS') {
//     if (!checkAuth()) {
//         jsonResponse(['success' => false, 'message' => 'Bu işlemi gerçekleştirmek için giriş yapmalısınız.'], 401);
//         exit;
//     }
// }

// İstek methodunu al
$method = $_SERVER['REQUEST_METHOD'];

// Veritabanı bağlantısı - config.php'den $db_akademi değişkenini kullan
try {
    // Eğer $db_akademi değişkeni tanımlı değilse, yeni bir bağlantı oluştur
    if (!isset($db_akademi)) {
        $db = new PDO("mysql:host=$servername;dbname=tuberaja_yayinci_akademi;charset=utf8mb4", $db_username, $db_password);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    } else {
        // Zaten tanımlı olan $db_akademi değişkenini kullan
        $db = $db_akademi;
    }
} catch (PDOException $e) {
    jsonResponse(['success' => false, 'message' => 'Veritabanı bağlantı hatası: ' . $e->getMessage()], 500);
}

// URL parametrelerini al
$action = $_GET['action'] ?? '';

// GET isteği - Kursları Listele
if ($method === 'GET') {
    try {
        // ID parametresi varsa tek bir kursu getir
        if (isset($_GET['id'])) {
            $id = intval($_GET['id']);

            // Kurs bilgilerini al
            $stmt = $db->prepare("SELECT * FROM courses WHERE id = ?");
            $stmt->execute([$id]);
            $course = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($course) {
                jsonResponse(['success' => true, 'data' => $course]);
            } else {
                jsonResponse(['success' => false, 'message' => 'Eğitim bulunamadı.'], 404);
            }
        }
        // Kategori parametresi varsa o kategorideki kursları getir
        else if (isset($_GET['category'])) {
            $category = $_GET['category'];

            $stmt = $db->prepare("
                SELECT * FROM courses
                WHERE category = ? AND status = 'active'
                ORDER BY created_at DESC
            ");
            $stmt->execute([$category]);
            $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);

            jsonResponse(['success' => true, 'data' => $courses]);
        }
        // Hiçbir parametre yoksa tüm kursları listele
        else {
            $query = "SELECT * FROM courses";

            // Sadece aktif kursları getir
            if (isset($_GET['status']) && $_GET['status'] === 'active') {
                $query .= " WHERE status = 'active'";
            }

            // Sıralama
            $query .= " ORDER BY created_at DESC";

            $stmt = $db->prepare($query);
            $stmt->execute();
            $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);

            jsonResponse(['success' => true, 'data' => $courses]);
        }
    } catch (PDOException $e) {
        jsonResponse(['success' => false, 'message' => 'Veritabanı hatası: ' . $e->getMessage()], 500);
    }
}

// POST isteği - Kurs Ekle/Güncelle/Sil
else if ($method === 'POST') {
    // POST istekleri için auth gerekli
    if (!checkAuth()) {
        jsonResponse(['success' => false, 'message' => 'Bu işlemi gerçekleştirmek için giriş yapmalısınız.'], 401);
        exit;
    }

    try {
        // Action'a göre işlem belirle
        switch ($action) {
            case 'add':
    // Kurs Ekle
        $input = json_decode(file_get_contents('php://input'), true);

        // Eğer JSON yoksa $_POST'u kontrol et
        if (!$input) {
            $input = $_POST;
        }

        // Gerekli alanları kontrol et
        if (!isset($input['title']) || !isset($input['description'])) {
                    jsonResponse(['success' => false, 'message' => 'Eksik alanlar: title ve description gerekli.'], 400);
        }

        // Debug için log ekle
        error_log("Eğitim ekleme - Title: " . ($input['title'] ?? 'null') . ", Description: " . ($input['description'] ?? 'null') . ", Content: " . ($input['content'] ?? 'null'));

            // Veritabanı işlemini başlat
            $db->beginTransaction();

            // Görsel yükleme
            $image_path = null;
            if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
                $uploads_dir = '../../uploads/courses/';
                if (!is_dir($uploads_dir)) {
                    mkdir($uploads_dir, 0755, true);
                }

                $tmp_name = $_FILES['image']['tmp_name'];
                $name = basename($_FILES['image']['name']);
                $file_extension = strtolower(pathinfo($name, PATHINFO_EXTENSION));
                $new_file_name = uniqid() . '.' . $file_extension;

                if (move_uploaded_file($tmp_name, $uploads_dir . $new_file_name)) {
                    $image_path = 'uploads/courses/' . $new_file_name;
                }
            }

                // Kursu ekle - basitleştirilmiş yapı
            $stmt = $db->prepare("
                INSERT INTO courses (
                    title, description, content, category, image, icon, featured,
                    created_by, created_at, updated_at, status
                ) VALUES (
                        ?, ?, ?, ?, ?, ?, ?, 1, NOW(), NOW(), ?
                )
            ");

            $stmt->execute([
                    $input['title'],
                    $input['description'],
                    $input['content'] ?? '',
                    $input['category'] ?? 'general',
                    $image_path,
                    $input['icon'] ?? null,
                    isset($input['featured']) ? ($input['featured'] ? 1 : 0) : 0,
                    $input['status'] ?? 'active'
            ]);

            $courseId = $db->lastInsertId();

            // İşlemi tamamla
            $db->commit();

                jsonResponse(['success' => true, 'message' => 'Eğitim başarıyla eklendi.', 'id' => $courseId]);
                break;

            case 'update':
    // Kurs Güncelle
        $input = json_decode(file_get_contents('php://input'), true);

        // Eğer JSON yoksa $_POST'u kontrol et
        if (!$input) {
            $input = $_POST;
        }

        // ID kontrolü
        if (!isset($input['id'])) {
                    jsonResponse(['success' => false, 'message' => 'Eksik alan: id gerekli.'], 400);
        }

        $id = intval($input['id']);

            // Veritabanı işlemini başlat
            $db->beginTransaction();

            // Önce kursun var olup olmadığını kontrol et
            $checkStmt = $db->prepare("SELECT image FROM courses WHERE id = ?");
            $checkStmt->execute([$id]);
            $course = $checkStmt->fetch();

            if (!$course) {
                    jsonResponse(['success' => false, 'message' => 'Eğitim bulunamadı.'], 404);
            }

            // Görsel yükleme
            $image_path = $course['image'] ?? null;
            if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
                $uploads_dir = '../../uploads/courses/';
                if (!is_dir($uploads_dir)) {
                    mkdir($uploads_dir, 0755, true);
                }

                // Eski görseli sil
                if ($image_path && file_exists('../../' . $image_path)) {
                    unlink('../../' . $image_path);
                }

                $tmp_name = $_FILES['image']['tmp_name'];
                $name = basename($_FILES['image']['name']);
                $file_extension = strtolower(pathinfo($name, PATHINFO_EXTENSION));
                $new_file_name = uniqid() . '.' . $file_extension;

                if (move_uploaded_file($tmp_name, $uploads_dir . $new_file_name)) {
                    $image_path = 'uploads/courses/' . $new_file_name;
                }
            }

            // Güncellenecek alanları belirle
            $updateFields = [];
            $params = [];

            $fields = [
                'title', 'description', 'content', 'category', 'icon', 'status'
            ];

            foreach ($fields as $field) {
                if (isset($input[$field])) {
                    $updateFields[] = "$field = :$field";
                    $params[":$field"] = $input[$field];
                }
            }

            // Görsel alanını ekle
            $updateFields[] = "image = :image";
            $params[':image'] = $image_path;

            // Özel işlem gerektiren alanlar
            if (isset($input['featured'])) {
                $updateFields[] = "featured = :featured";
                $params[':featured'] = $input['featured'] ? 1 : 0;
            }

            // Güncellenecek alan yoksa hata döndür
            if (empty($updateFields)) {
                    jsonResponse(['success' => false, 'message' => 'Güncellenecek alan belirtilmedi.'], 400);
            }

            // Güncelleme zamanını ekle
            $updateFields[] = "updated_at = NOW()";

            // Kursu güncelle
            $updateQuery = "UPDATE courses SET " . implode(", ", $updateFields) . " WHERE id = :id";
            $params[':id'] = $id;

            $stmt = $db->prepare($updateQuery);
            $stmt->execute($params);

            // İşlemi tamamla
            $db->commit();

                jsonResponse(['success' => true, 'message' => 'Eğitim başarıyla güncellendi.']);
                break;

            case 'delete':
    // Kurs Sil
        $input = json_decode(file_get_contents('php://input'), true);
        $id = isset($input['id']) ? intval($input['id']) : (isset($_GET['id']) ? intval($_GET['id']) : 0);

        if ($id <= 0) {
                    jsonResponse(['success' => false, 'message' => 'Geçerli bir ID gerekli.'], 400);
        }

            // Veritabanı işlemini başlat
            $db->beginTransaction();

            // Kursun varlığını kontrol et ve görsel bilgisini al
            $checkStmt = $db->prepare("SELECT image FROM courses WHERE id = ?");
            $checkStmt->execute([$id]);
            $course = $checkStmt->fetch();

            if (!$course) {
                    jsonResponse(['success' => false, 'message' => 'Eğitim bulunamadı.'], 404);
            }

            // Görseli sil
            if ($course['image'] && file_exists('../../' . $course['image'])) {
                unlink('../../' . $course['image']);
            }

            // Kursu sil
            $stmt = $db->prepare("DELETE FROM courses WHERE id = ?");
            $stmt->execute([$id]);

            // İşlemi tamamla
            $db->commit();

                jsonResponse(['success' => true, 'message' => 'Eğitim başarıyla silindi.']);
                break;

            default:
                jsonResponse(['success' => false, 'message' => 'Desteklenmeyen action parametresi: ' . $action], 400);
                break;
        }

        } catch (PDOException $e) {
            // Hata durumunda işlemi geri al
            $db->rollBack();

        error_log("Kurs işlem hatası: " . $e->getMessage());
        jsonResponse(['success' => false, 'message' => 'Veritabanı hatası: ' . $e->getMessage()], 500);
    } catch (Exception $e) {
        // Hata durumunda işlemi geri al
        $db->rollBack();

        error_log("Genel hata: " . $e->getMessage());
        jsonResponse(['success' => false, 'message' => 'Bir hata oluştu.'], 500);
    }
}

// Desteklenmeyen istek metodu
else {
    jsonResponse(['success' => false, 'message' => 'Desteklenmeyen HTTP metodu.'], 405);
}
