import React from 'react';
import { Typography, Layout } from 'antd';

const { Title, Paragraph } = Typography;
const { Content } = Layout;

const Privacy: React.FC = () => {
  return (
    <Layout>
      <Content style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
        <Title level={1}>TuberOPS - Privacy Policy</Title>
        
        <Paragraph>
          This Privacy Policy describes how TuberOPS (x.tuberajans.com), an internal tool developed by Tuber Ajans, 
          handles data collected via TikTok APIs.
        </Paragraph>

        <Title level={2}>1. What Data We Access:</Title>
        <Paragraph>
          <ul>
            <li>Public TikTok profile information</li>
            <li>Direct messaging metadata (only with user consent)</li>
            <li>Creator performance metrics (e.g. stream hours, engagement)</li>
          </ul>
        </Paragraph>

        <Title level={2}>2. Why We Access This Data:</Title>
        <Paragraph>
          <ul>
            <li>To support agency creators with performance reports</li>
            <li>To enable better communication between the agency and creators</li>
            <li>To optimize publishing and live strategy support</li>
          </ul>
        </Paragraph>

        <Title level={2}>3. Data Sharing:</Title>
        <Paragraph>
          We do not share any personal data with third parties. All data is processed within the agency environment 
          and limited to authorized users only.
        </Paragraph>

        <Title level={2}>4. User Consent:</Title>
        <Paragraph>
          All data is accessed with the explicit consent of the creator via TikTok OAuth flow.
        </Paragraph>

        <Title level={2}>5. Data Retention:</Title>
        <Paragraph>
          Data is retained only as long as needed for operational purposes. Users may request removal at any time.
        </Paragraph>

        <Title level={2}>6. GDPR & Compliance:</Title>
        <Paragraph>
          We are fully committed to GDPR and applicable data protection laws.
        </Paragraph>

        <div style={{ marginTop: '24px' }}>
          <Paragraph>
            For any concerns, contact: <a href="mailto:<EMAIL>"><EMAIL></a>
          </Paragraph>
          
          <Paragraph>
            Last Updated: {new Date().toLocaleDateString()}
          </Paragraph>
        </div>
      </Content>
    </Layout>
  );
};

export default Privacy; 