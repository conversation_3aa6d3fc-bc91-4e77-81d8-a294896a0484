import{k as t}from"./App-DHTOkUiy.js";import{S as n}from"./index-Bso3pfdw.js";const s=()=>{const e=localStorage.getItem(n.TOKEN_KEY);return e?{Authorization:`Bearer ${e}`}:{}},p=async()=>(await t.get("/backend/x-site/events.php",{headers:s()})).data,d=async()=>(await t.get("/backend/x-site/publishers.php",{headers:s()})).data,h=async e=>(await t.get(`/backend/x-site/event-matches.php?eventId=${e}`,{headers:s()})).data,i=async e=>(await t.post("/backend/x-site/create-event.php",e,{headers:s()})).data,u=async e=>(await t.get(`/backend/x-site/event-detail.php?id=${e}`,{headers:s()})).data,m=async e=>(await t.get(`/backend/x-site/tournament-matches.php?tournamentId=${e}`,{headers:s()})).data,b=async(e,a)=>(await t.post(`/backend/x-site/update-match.php?matchId=${e}`,a,{headers:s()})).data,g=async e=>(await t.delete(`/backend/x-site/delete-event.php?id=${e}`,{headers:s()})).data;export{d as a,h as b,i as c,g as d,m as e,p as f,u as g,b as u};
