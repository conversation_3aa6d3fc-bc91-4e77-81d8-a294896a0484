<?php
// Veritabanı tablosuna video_count ve total_videos sütunlarını ekle

require_once '../config/config.php';

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};dbname={$config['database']};charset=utf8mb4",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    echo "Veritabanına bağlanıldı.\n";

    // tiktok_requests tablosuna video_count sütunu ekle
    try {
        $pdo->exec("ALTER TABLE tiktok_requests ADD COLUMN video_count INT DEFAULT 10 AFTER username");
        echo "✅ tiktok_requests tablosuna video_count sütunu eklendi.\n";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "ℹ️ video_count sütunu zaten mevcut.\n";
        } else {
            throw $e;
        }
    }

    // tiktok_analysis tablosuna total_videos sütunu ekle
    try {
        $pdo->exec("ALTER TABLE tiktok_analysis ADD COLUMN total_videos INT DEFAULT NULL AFTER likes_count");
        echo "✅ tiktok_analysis tablosuna total_videos sütunu eklendi.\n";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "ℹ️ total_videos sütunu zaten mevcut.\n";
        } else {
            throw $e;
        }
    }

    echo "\n🎉 Tüm sütunlar başarıyla eklendi!\n";

} catch (Exception $e) {
    echo "❌ Hata: " . $e->getMessage() . "\n";
}
?>
