<?php
// Basit auth test
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
header("Access-Control-Max-Age: 86400");
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(204);
    exit();
}

header('Content-Type: application/json; charset=utf-8');

// Hızlı test - config olmadan
if (isset($_GET['check'])) {
    echo json_encode([
        'authenticated' => false,
        'message' => 'Test: Auth sistemi çalışıyor ama kullanıcı giriş yapmamış',
        'timestamp' => date('Y-m-d H:i:s'),
        'server_info' => [
            'php_version' => PHP_VERSION,
            'memory_usage' => memory_get_usage(true),
            'request_method' => $_SERVER['REQUEST_METHOD']
        ]
    ]);
    exit;
}

echo json_encode([
    'status' => 'Auth test endpoint çalışıyor',
    'timestamp' => date('Y-m-d H:i:s')
]);
?>
