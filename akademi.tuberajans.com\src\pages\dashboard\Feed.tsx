import React, { useState, useRef, useEffect, useContext } from 'react';
import { motion } from 'framer-motion';
import { FaStream, FaHeart, FaComment, FaEllipsisH, FaSmile, FaTimes, FaPencilAlt, FaTrash } from 'react-icons/fa';
import axios from 'axios';
import { useAuth } from '../../contexts/AuthContext';
import { SidebarContext } from '../../contexts/SidebarContext';
import { useTikTok } from '../../contexts/TikTokContext';

const Feed: React.FC = () => {
  // Development mode kontrolü
  const isDevMode = import.meta.env.VITE_DISABLE_AUTH === 'true' || import.meta.env.DEV;

  // Sidebar context'inden isMobile ve isSidebarOpen değerlerini al
  const { isMobile } = useContext(SidebarContext);

  // AuthContext'i sadece production modunda kullan
  let user: any = null;
  if (!isDevMode) {
    try {
      const authContext = useAuth();
      user = authContext.user;
    } catch (error) {
      console.error('Auth context error in Feed:', error);
      // Fallback: localStorage'dan kullanıcı bilgisini al
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        user = JSON.parse(storedUser);
      }
    }
  } else {
    // Development modunda localStorage'dan kullanıcı bilgisini al
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      user = JSON.parse(storedUser);
    }
  }
  const [postText, setPostText] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [openMenuId, setOpenMenuId] = useState<number | null>(null);
  const [editingPostId, setEditingPostId] = useState<number | null>(null);
  const [editingText, setEditingText] = useState('');
  const [editingPreviewUrl, setEditingPreviewUrl] = useState<string | null>(null);
  const [openCommentId, setOpenCommentId] = useState<number | null>(null);
  const [feedItems, setFeedItems] = useState<any[]>([]);
  const [userInfo, setUserInfo] = useState<any>(null);

  // TikTok Context'ten verileri al
  const { tiktokUser } = useTikTok();

  // Dosya seçme işlevi
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    if (file) {
      // Sadece resim dosyalarını kabul et
      if (!file.type.match('image/(png|jpg|jpeg)')) {
        alert('Lütfen yalnızca PNG, JPG veya JPEG formatında dosya yükleyin.');
        return;
      }

      setSelectedFile(file);

      // Dosya ön izlemesi için URL oluştur
      const fileReader = new FileReader();
      fileReader.onload = () => {
        setPreviewUrl(fileReader.result as string);
      };
      fileReader.readAsDataURL(file);
    }
  };

  // Dosya yükleme butonuna tıklama
  const handleMediaButtonClick = () => {
    fileInputRef.current?.click();
  };

  // Eklenen dosyayı kaldırma
  const handleRemoveFile = () => {
    setSelectedFile(null);
    setPreviewUrl(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Emoji ekleme
  const handleEmojiSelect = (emojiData: { emoji: string }) => {
    if (editingPostId) {
      setEditingText(prevText => prevText + emojiData.emoji);
    } else {
      setPostText(prevText => prevText + emojiData.emoji);
    }
  };

  // Akış verilerini API'den çek
  const fetchFeedItems = async () => {
    try {
      console.log('Akış verileri API çağrısı yapılıyor...');

      // Development modunda mock veri kullan
      if (isDevMode) {
        console.log('Development mode: Mock akış verileri kullanılıyor');

        // Mock akış verileri
        const mockFeedItems = [
          {
            id: 1,
            author: {
              name: tiktokUser?.display_name || userInfo?.name || 'tuberajans',
              avatar: tiktokUser?.avatar_url || userInfo?.profile_image || userInfo?.avatar_url || 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face&auto=format&q=80',
              role: userInfo?.role || 'Ajans Grubu',
            },
            content: 'Yeni TikTok algoritması güncellemesi hakkında detaylı bir analiz hazırladım. Bu değişiklikler içerik üreticileri için büyük fırsatlar sunuyor! 🚀\n\nÖzellikle kısa form videolar için engagement oranları %40 artmış durumda. Stratejilerinizi buna göre güncelleyin.',
            media: 'https://images.unsplash.com/photo-1611162617474-5b21e879e113?w=500&h=300&fit=crop&auto=format&q=80',
            createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 saat önce
            likes: 24,
            comments: 8,
            shares: 3,
            isLiked: false,
          },
          {
            id: 2,
            author: {
              name: 'Ayşe Demir',
              avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face&auto=format&q=80',
              role: 'İçerik Üreticisi',
            },
            content: 'Bu hafta ki canlı yayın performansım gerçekten harika geçti! 3 saatte 1.2K takipçi kazandım 🎉\n\nCanlı yayın ipuçlarımı paylaşmak isteyenler var mı? Yorumlarda belirtin, detaylı bir rehber hazırlayayım.',
            media: null,
            createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 saat önce
            likes: 18,
            comments: 12,
            shares: 2,
            isLiked: true,
          },
          {
            id: 3,
            author: {
              name: 'Mehmet Kaya',
              avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face&auto=format&q=80',
              role: 'Dijital Pazarlama Uzmanı',
            },
            content: 'Hashtag stratejileri konusunda çok güzel bir workshop tamamladık! Katılan herkese teşekkürler 🙏\n\n#TikTokTips #DigitalMarketing #ContentCreator #SocialMedia',
            media: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=500&h=300&fit=crop&auto=format&q=80',
            createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 saat önce
            likes: 31,
            comments: 15,
            shares: 7,
            isLiked: false,
          },
          {
            id: 4,
            author: {
              name: 'Zeynep Özkan',
              avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face&auto=format&q=80',
              role: 'Sosyal Medya Uzmanı',
            },
            content: 'Yeni trend analizi raporumuz hazır! Bu ay en çok izlenen içerik türleri:\n\n1. Eğitici videolar (%35)\n2. Komedi içerikleri (%28)\n3. Dans videoları (%22)\n4. Yemek tarifleri (%15)\n\nSizin en sevdiğiniz kategori hangisi?',
            media: null,
            createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000), // 8 saat önce
            likes: 42,
            comments: 23,
            shares: 11,
            isLiked: true,
          },
          {
            id: 5,
            author: {
              name: 'Can Yılmaz',
              avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face&auto=format&q=80',
              role: 'Video Editörü',
            },
            content: 'Yeni editing setup\'ım tamamlandı! 4K montaj için optimize edilmiş sistem ile çalışma hızım 3 kat arttı 💪\n\nVideo editing konusunda sorularınız varsa çekinmeyin, yardımcı olmaya çalışırım.',
            media: 'https://images.unsplash.com/photo-1547394765-185e1e68f34e?w=500&h=300&fit=crop&auto=format&q=80',
            createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 saat önce
            likes: 19,
            comments: 6,
            shares: 4,
            isLiked: false,
          },
          {
            id: 6,
            author: {
              name: 'Elif Şahin',
              avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face&auto=format&q=80',
              role: 'Marka Ortağı',
            },
            content: 'Geçen ay ki marka iş birliği kampanyamız muhteşem sonuçlar verdi! 🎯\n\n• 2.5M görüntülenme\n• %8.3 engagement rate\n• 15K yeni takipçi\n\nDoğru marka seçimi ne kadar önemli bir kez daha anlaşıldı.',
            media: null,
            createdAt: new Date(Date.now() - 18 * 60 * 60 * 1000), // 18 saat önce
            likes: 67,
            comments: 34,
            shares: 18,
            isLiked: true,
          }
        ];

        setFeedItems(mockFeedItems);
        return;
      }

      // Production modunda gerçek API çağrısı
      const response = await axios.get('/backend/api/api_data.php', {
        params: {
          endpoint: 'feed',
          user_id: user?.id || 1
        },
        timeout: 10000 // 10 saniye timeout
      });

      console.log('API yanıtı:', response.data);

      if (response.data.status === 'success') {
        // API'den gelen verileri formatla
        if (Array.isArray(response.data.data) && response.data.data.length > 0) {
          const formattedFeedItems = response.data.data.map((post: any) => {
            return {
              id: post.id,
              author: {
                name: post.username || 'Kullanıcı',
                avatar: post.profile_image || 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face&auto=format&q=80',
                role: post.role || 'Üye',
              },
              content: post.content,
              media: post.media_url,
              createdAt: new Date(post.created_at),
              likes: post.likes_count || 0,
              comments: post.comments_count || 0,
              shares: 0,
              isLiked: post.is_liked === 1 || post.is_liked === true,
            };
          });

          setFeedItems(formattedFeedItems);
        } else {
          // Veri yoksa boş dizi göster ama hata değil
          console.log('API yanıtında gönderi bulunamadı');
          setFeedItems([]);
        }
      } else {
        console.error('API başarısız yanıt döndü:', response.data);
        alert('Akış verileri alınamadı: ' + (response.data.message || 'Bilinmeyen hata'));
      }
    } catch (err: any) {
      console.error('Akış verileri alınırken hata oluştu:', err);

      // Daha detaylı hata mesajı
      if (err.response) {
        // Sunucu yanıtı ile dönen hata
        console.error('Sunucu yanıtı:', err.response.data);
        console.error('Durum kodu:', err.response.status);
        alert(`Sunucu hatası: ${err.response.status} - ${err.response.data?.message || 'Bilinmeyen hata'}`);
      } else if (err.request) {
        // İstek yapıldı ama yanıt alınamadı
        console.error('İstek yapıldı ama yanıt alınamadı');
        alert('Sunucuya bağlanılamadı. Lütfen internet bağlantınızı kontrol edin.');
      } else {
        // İstek oluşturulurken hata oluştu
        console.error('İstek hatası:', err.message);
        alert(`İstek hatası: ${err.message}`);
      }
    }
  };

  // Kullanıcı bilgilerini getir
  const fetchUserInfo = async () => {
    try {
      // Development modunda mock veri kullan
      if (isDevMode) {
        console.log('Development mode: Mock kullanıcı verileri kullanılıyor (Feed)');

        // Mock kullanıcı verisi
        const mockUserData = {
          id: 1,
          name: 'Ahmet Yılmaz',
          username: 'ahmet_yilmaz',
          email: '<EMAIL>',
          profile_image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face&auto=format&q=80',
          avatar_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face&auto=format&q=80',
          is_verified: true,
          role: 'admin',
          bio: 'TikTok içerik üreticisi ve dijital pazarlama uzmanı. Tuber Akademi\'de eğitmenlik yapıyorum.',
          tiktok_username: '@ahmet_yilmaz_official'
        };

        setUserInfo(mockUserData);
        return;
      }

      // Production modunda gerçek API çağrısı
      const response = await axios.get('/backend/api/user_info.php');
      if (response.data.success) {
        setUserInfo(response.data.data);
      }
    } catch (err) {
      console.error('Kullanıcı bilgileri yüklenirken hata:', err);
    }
  };



  // Sayfa yüklendiğinde akış verilerini ve kullanıcı bilgilerini çek
  useEffect(() => {
    fetchFeedItems();
    fetchUserInfo();
  }, []);

  // Like işlevi
  const handleLike = async (id: number) => {
    try {
      const response = await axios.post('/backend/api/api_data.php', {
        endpoint: 'like_post',
        post_id: id,
        user_id: user?.id || 1
      });

      if (response.data.status === 'success') {
        // UI'ı güncelle
        setFeedItems(prevItems =>
          prevItems.map(item =>
            item.id === id
              ? { ...item, isLiked: !item.isLiked, likes: item.isLiked ? item.likes - 1 : item.likes + 1 }
              : item
          )
        );
      } else {
        console.error('Beğeni işlemi başarısız:', response.data.message);
      }
    } catch (err) {
      console.error('Beğeni işlemi sırasında hata oluştu:', err);
    }
  };

  // Menü açma/kapama
  const toggleMenu = (id: number) => {
    setOpenMenuId(openMenuId === id ? null : id);
  };

  // Yorumları açma/kapama
  const toggleComments = (id: number) => {
    setOpenCommentId(openCommentId === id ? null : id);
  };

  // Düzenleme başlatma
  const handleEdit = (item: any) => {
    // Paylaşım 30 dakikadan eski mi kontrolü
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
    if (item.createdAt < thirtyMinutesAgo) {
      alert('Bu paylaşım yalnızca ilk 30 dakika içinde düzenlenebilir.');
      return;
    }

    setEditingPostId(item.id);
    setEditingText(item.content);
    setEditingPreviewUrl(item.media);
    setOpenMenuId(null);
  };

  // Düzenlemeyi kaydetme
  const handleSaveEdit = () => {
    if (!editingText.trim()) {
      alert('Paylaşım içeriği boş olamaz.');
      return;
    }

    setFeedItems(prevItems =>
      prevItems.map(item =>
        item.id === editingPostId
          ? { ...item, content: editingText, media: editingPreviewUrl }
          : item
      )
    );

    // Düzenleme modunu kapat
    setEditingPostId(null);
    setEditingText('');
    setEditingPreviewUrl(null);
  };

  // Düzenlemeyi iptal etme
  const handleCancelEdit = () => {
    setEditingPostId(null);
    setEditingText('');
    setEditingPreviewUrl(null);
  };

  // Paylaşım silme
  const handleDelete = (id: number) => {
    if (window.confirm('Bu paylaşımı silmek istediğinize emin misiniz?')) {
      setFeedItems(prevItems => prevItems.filter(item => item.id !== id));
    }
    setOpenMenuId(null);
  };

  // Gönderi paylaşma
  const handleShare = async () => {
    if (!postText.trim() && !selectedFile) {
      alert('Lütfen bir metin yazın veya bir medya dosyası ekleyin.');
      return;
    }

    try {
      // Form verisi oluştur
      const formData = new FormData();
      formData.append('endpoint', 'create_post');
      formData.append('user_id', user?.id?.toString() || '1');
      formData.append('content', postText);

      if (selectedFile) {
        formData.append('media', selectedFile);
      }

      const response = await axios.post('/backend/api/api_data.php', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      if (response.data.status === 'success') {
        // Yeni gönderiyi ekle
        const newPost = {
          id: response.data.post_id || Math.max(0, ...feedItems.map(item => item.id)) + 1,
          author: {
            name: tiktokUser?.display_name || userInfo?.name || 'tuberajans',
            avatar: tiktokUser?.avatar_url || userInfo?.profile_image || userInfo?.avatar_url || 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face&auto=format&q=80',
            role: userInfo?.role || 'Ajans Grubu',
          },
          content: postText,
          media: previewUrl,
          createdAt: new Date(),
          likes: 0,
          comments: 0,
          shares: 0,
          isLiked: false,
        };

        setFeedItems([newPost, ...feedItems]);

        // Paylaşım sonrası sıfırlama
        setPostText('');
        setSelectedFile(null);
        setPreviewUrl(null);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      } else {
        alert('Gönderi paylaşılırken bir hata oluştu: ' + response.data.message);
      }
    } catch (err) {
      console.error('Gönderi paylaşılırken hata oluştu:', err);
      alert('Gönderi paylaşılırken bir hata oluştu. Lütfen daha sonra tekrar deneyin.');
    }
  };

  // Paylaşım zamanını formatlama
  const formatPostTime = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffSecs / 60);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) {
      return `${diffDays} gün önce`;
    } else if (diffHours > 0) {
      return `${diffHours} saat önce`;
    } else if (diffMins > 0) {
      return `${diffMins} dakika önce`;
    } else {
      return 'Az önce';
    }
  };

  // Paylaşımın düzenlenebilir olup olmadığını kontrol etme
  const isEditable = (date: Date) => {
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
    return date > thirtyMinutesAgo;
  };

  return (
    <div className="min-h-screen w-full">
      <div className="container" style={{
        maxWidth: isMobile ? '100%' : 'none',
        overflowX: 'hidden'
      }}>
        <div className="flex gap-0 xl:gap-6 w-full">
          {/* Sol Sütun - Ana Akış */}
          <div className="flex-1 w-full">
            {/* Gönderi Oluştur */}
            <div className="bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden mb-6 p-3">
              <div className="flex items-center mb-3 sm:mb-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-700 dark:text-gray-300 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                <h2 className="text-base font-semibold text-gray-800 dark:text-white">Yeni Paylaşım</h2>
              </div>
              <div className="p-3">
                <div className="flex space-x-1.5 sm:space-x-2">
                  <div className="flex-shrink-0">
                    {(tiktokUser?.avatar_url || userInfo?.profile_image || userInfo?.avatar_url) ? (
                      <img
                        src={tiktokUser?.avatar_url || userInfo?.profile_image || userInfo?.avatar_url}
                        alt={tiktokUser?.display_name || userInfo?.name || 'Kullanıcı'}
                        className="w-7 h-7 sm:w-8 sm:h-8 rounded-full object-cover border border-gray-200 dark:border-gray-600"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face&auto=format&q=80';
                        }}
                      />
                    ) : null}
                    <div className={`w-7 h-7 sm:w-8 sm:h-8 rounded-full bg-[#FF3E71]/10 dark:bg-[#FF3E71]/20 flex items-center justify-center text-[#FF3E71] ${(tiktokUser?.avatar_url || userInfo?.profile_image || userInfo?.avatar_url) ? 'hidden' : ''}`}>
                      <span className="font-medium text-xs sm:text-sm">{(tiktokUser?.display_name || userInfo?.name || 'T').charAt(0).toUpperCase()}</span>
                    </div>
                  </div>
                  <div className="flex-1">
                    <div className="relative">
                      <textarea
                        className="w-full p-2 pr-8 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 rounded-lg focus:ring-[#FF3E71] focus:border-[#FF3E71] text-xs sm:text-sm text-gray-700 dark:text-gray-200 placeholder-gray-400 dark:placeholder-gray-500 resize-none"
                        placeholder="Topluluğa bir şeyler paylaş..."
                        rows={2}
                        value={postText}
                        onChange={(e) => setPostText(e.target.value)}
                      ></textarea>
                      <div className="absolute top-3 right-3">
                        <button
                          className="text-gray-400 hover:text-[#FF3E71] transition-colors p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
                          onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                          title="Emoji ekle"
                        >
                          <FaSmile className="h-5 w-5" />
                        </button>

                        {showEmojiPicker && (
                          <div className="absolute top-8 right-0 z-50 shadow-xl border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden bg-white dark:bg-gray-800 p-2 w-[280px]">
                            <div
                              className="fixed inset-0"
                              onClick={() => setShowEmojiPicker(false)}
                            ></div>
                            <div className="relative z-20">
                              <div className="grid grid-cols-8 gap-1">
                                {[
                                  "😀", "😃", "😄", "😁", "😆", "😅", "🤣", "😂",
                                  "🙂", "🙃", "😉", "😊", "😇", "😍", "🥰", "😘",
                                  "😗", "☺️", "😚", "😙", "😋", "😛", "😜", "🤪",
                                  "😝", "🤑", "🤗", "🤭", "🤫", "🤔", "🤐", "🤨",
                                  "😐", "😑", "😶", "😏", "😒", "🙄", "😬", "🤥",
                                  "😌", "😔", "😪", "🤤", "😴", "😷", "🤒", "🤕",
                                  "🤢", "🤮", "🤧", "🥵", "🥶", "🥴", "😵", "🤯",
                                  "🤠", "🥳", "😎", "🤓", "🧐", "😕", "😟", "🙁",
                                  "☹️", "😮", "😯", "😲", "😳", "🥺", "😦", "😧",
                                  "😨", "😰", "😥", "😢", "😭", "😱", "😖", "😣",
                                  "😞", "😓", "😩", "😫", "😤", "😡", "😠", "🤬",
                                  "👍", "👎", "👏", "🙌", "👌", "🤝", "❤️", "👋"
                                ].map((emoji, index) => (
                                  <button
                                    key={index}
                                    className="text-lg hover:bg-gray-100 dark:hover:bg-gray-700 p-1 rounded"
                                    onClick={() => {
                                      handleEmojiSelect({ emoji });
                                      setShowEmojiPicker(false);
                                    }}
                                  >
                                    {emoji}
                                  </button>
                                ))}
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Medya ön izleme */}
                    {previewUrl && (
                      <div className="mt-3 relative">
                        <div className="rounded-lg overflow-hidden relative">
                          <img
                            src={previewUrl}
                            alt="Seçilen görsel"
                            className="max-h-48 w-auto mx-auto object-contain"
                          />
                          <button
                            className="absolute top-2 right-2 bg-gray-800 bg-opacity-50 text-white rounded-full p-1 hover:bg-opacity-70 transition-all"
                            onClick={handleRemoveFile}
                          >
                            <FaTimes size={16} />
                          </button>
                        </div>
                        <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                          {selectedFile?.name} ({(selectedFile?.size ? selectedFile.size / 1024 : 0).toFixed(1)} KB)
                        </div>
                      </div>
                    )}

                    {/* Görünmeyen dosya girişi */}
                    <input
                      type="file"
                      ref={fileInputRef}
                      className="hidden"
                      accept="image/png, image/jpeg, image/jpg"
                      onChange={handleFileSelect}
                    />

                    <div className="mt-3 sm:mt-4 flex justify-between items-center">
                      <div className="flex space-x-1 sm:space-x-2">
                        <button
                          className="inline-flex items-center px-2 sm:px-3 py-1 sm:py-1.5 border border-gray-200 dark:border-gray-600 rounded-full text-xs font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors touch-manipulation"
                          onClick={handleMediaButtonClick}
                        >
                          <svg className="h-3 sm:h-4 w-3 sm:w-4 mr-1 text-[#FF3E71]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                          <span className="hidden sm:inline">Medya</span>
                          <span className="sm:hidden">📷</span>
                        </button>
                      </div>
                      <button
                        className="inline-flex items-center px-3 sm:px-4 py-1.5 sm:py-2 border border-transparent rounded-full shadow-sm text-xs sm:text-sm font-medium text-white bg-gradient-to-r from-[#FF3E71] to-[#FF5F87] hover:from-[#FF5F87] hover:to-[#FF3E71] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF3E71] transition-all duration-300 touch-manipulation"
                        onClick={handleShare}
                      >
                        Paylaş
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* Akış Listesi */}
            <div className="space-y-1 xl:space-y-4">
              {feedItems.map((item, index) => (
                <motion.div
                  key={item.id}
                  className="bg-white dark:bg-[#16151c] rounded-none xl:rounded-lg shadow-sm overflow-hidden p-3 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  {/* Düzenleme Modu */}
                  {editingPostId === item.id ? (
                    <div className="p-4">
                      <div className="flex items-center mb-4">
                        <img
                          src={item.id === 1 ? (tiktokUser?.avatar_url || userInfo?.profile_image || userInfo?.avatar_url || item.author.avatar) : item.author.avatar}
                          alt={item.author.name}
                          className="w-10 h-10 rounded-full object-cover"
                          onError={(e) => {
                            (e.target as HTMLImageElement).src = 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face&auto=format&q=80';
                          }}
                        />
                        <div className="ml-3">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">{item.author.name}</div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">{item.author.role}</div>
                        </div>
                      </div>

                      <textarea
                        className="w-full p-3 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 rounded-lg focus:ring-tuber-pink focus:border-tuber-pink text-sm text-gray-700 dark:text-gray-200 placeholder-gray-400 dark:placeholder-gray-500"
                        rows={3}
                        value={editingText}
                        onChange={(e) => setEditingText(e.target.value)}
                      ></textarea>

                      {editingPreviewUrl && (
                        <div className="mt-3 relative">
                          <div className="rounded-lg overflow-hidden relative">
                            <img
                              src={editingPreviewUrl}
                              alt="Paylaşım görseli"
                              className="max-h-48 w-auto mx-auto object-contain"
                            />
                          </div>
                        </div>
                      )}

                      <div className="mt-3 flex justify-end gap-2">
                        <button
                          className="inline-flex items-center px-3 py-1.5 border border-gray-200 dark:border-gray-600 rounded-full text-xs font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
                          onClick={handleCancelEdit}
                        >
                          İptal
                        </button>
                        <button
                          className="inline-flex items-center px-3 py-1.5 border border-transparent rounded-full shadow-sm text-xs font-medium text-white bg-gradient-to-r from-[#FF3E71] to-[#FF5F87] hover:from-[#FF5F87] hover:to-[#FF3E71] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF3E71] transition-all duration-300"
                          onClick={handleSaveEdit}
                        >
                          Kaydet
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="p-1 sm:p-3 md:p-4">
                      {/* Gönderi Başlığı */}
                      <div className="flex justify-between">
                        <div className="flex items-center">
                          <img
                            src={item.id === 1 ? (tiktokUser?.avatar_url || userInfo?.profile_image || userInfo?.avatar_url || item.author.avatar) : item.author.avatar}
                            alt={item.author.name}
                            className="w-7 h-7 sm:w-8 sm:h-8 md:w-10 md:h-10 rounded-full object-cover"
                            onError={(e) => {
                              (e.target as HTMLImageElement).src = 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face&auto=format&q=80';
                            }}
                          />
                          <div className="ml-1.5 sm:ml-2 md:ml-3">
                            <div className="text-xs sm:text-sm font-medium text-gray-900 dark:text-white">{item.author.name}</div>
                            <div className="text-[10px] sm:text-xs text-gray-500 dark:text-gray-400 flex items-center">
                              <span>{item.author.role}</span>
                              <span className="mx-1">•</span>
                              <span>{formatPostTime(item.createdAt)}</span>
                            </div>
                          </div>
                        </div>
                        <div className="relative">
                          <button
                            className="text-gray-400 dark:text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 p-1"
                            onClick={() => toggleMenu(item.id)}
                            aria-label="Paylaşım seçenekleri"
                          >
                            <FaEllipsisH />
                          </button>

                          {/* Açılır menü */}
                          {openMenuId === item.id && (
                            <div className="absolute right-0 top-8 w-36 bg-white dark:bg-[#1e1d26] rounded-lg shadow-lg z-10 py-1 border border-gray-100 dark:border-gray-700">
                              {isEditable(item.createdAt) ? (
                                <button
                                  className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center"
                                  onClick={() => handleEdit(item)}
                                >
                                  <FaPencilAlt className="mr-2 text-[#FF3E71]" />
                                  Düzenle
                                </button>
                              ) : (
                                <button
                                  className="w-full text-left px-4 py-2 text-sm text-gray-400 dark:text-gray-500 flex items-center cursor-not-allowed"
                                  title="Paylaşımlar yalnızca ilk 30 dakika içinde düzenlenebilir."
                                >
                                  <FaPencilAlt className="mr-2" />
                                  Düzenle
                                </button>
                              )}
                              <button
                                className="w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center"
                                onClick={() => handleDelete(item.id)}
                              >
                                <FaTrash className="mr-2" />
                                Sil
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                      {/* Gönderi İçeriği */}
                      <div className="mt-2 sm:mt-3">
                        <p className="text-xs sm:text-sm md:text-base text-gray-800 dark:text-gray-200 line-clamp-3">{item.content}</p>
                        {item.media && (
                          <div className="mt-2 sm:mt-3 rounded-lg overflow-hidden">
                            <img
                              src={item.media}
                              alt="Post media"
                              className="w-full h-32 sm:h-36 md:h-48 object-cover"
                            />
                          </div>
                        )}
                      </div>
                      {/* Etkileşim Butonları */}
                      <div className="mt-3 sm:mt-4 flex items-center justify-between border-t border-gray-100 dark:border-gray-700 pt-2 sm:pt-3">
                        {/* Beğeni ve Yorum butonları yan yana */}
                        <div className="flex items-center space-x-1 sm:space-x-2">
                          <button
                            className={`flex items-center text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-1.5 rounded-full transition-colors touch-manipulation ${
                              item.isLiked
                                ? 'text-white bg-[#FF3E71] dark:bg-[#FF3E71]'
                                : 'text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600'
                            }`}
                            onClick={() => handleLike(item.id)}
                          >
                            <FaHeart className="mr-1 sm:mr-1.5 text-xs" />
                            <span>{item.likes}</span>
                          </button>
                          <button
                            className="flex items-center text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-1.5 rounded-full text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors touch-manipulation"
                            onClick={() => document.getElementById(`comment-input-${item.id}`)?.focus()}
                          >
                            <FaComment className="mr-1 sm:mr-1.5 text-xs" />
                            <span>{item.comments}</span>
                          </button>
                        </div>
                      </div>

                      {/* Yorumlar Bölümü - Her zaman görünür */}
                      <div className="mt-2 sm:mt-3 border-t border-gray-100 dark:border-gray-700 pt-2 sm:pt-3 px-0 sm:px-2 md:px-3">
                        {/* Örnek yorumlar - Her zaman en fazla 2 yorum göster */}
                        <div className="space-y-2 sm:space-y-3">
                          {item.id === 1 ? (
                            <>
                              {/* İlk 2 yorumu göster */}
                              <div className="flex space-x-1.5 sm:space-x-2">
                                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Kullanıcı" className="w-6 h-6 sm:w-7 sm:h-7 rounded-full flex-shrink-0" />
                                <div className="flex-1 bg-gray-50 dark:bg-gray-800/50 rounded-lg p-1.5 sm:p-2">
                                  <div className="flex justify-between items-start">
                                    <div className="text-[10px] sm:text-xs font-medium text-gray-900 dark:text-white">Mehmet Yılmaz</div>
                                    <div className="text-[9px] sm:text-[10px] text-gray-500">1 saat önce</div>
                                  </div>
                                  <p className="text-[10px] sm:text-xs text-gray-700 dark:text-gray-300 mt-0.5 sm:mt-1">Harika bir strateji! Ben de benzer bir yaklaşım deniyorum.</p>
                                </div>
                              </div>

                              {/* Daha fazla yorum varsa ve açık değilse "Daha fazla yorum gör" butonu göster */}
                              {openCommentId !== item.id && item.comments > 1 ? (
                                <button
                                  className="text-[10px] sm:text-xs text-[#FF3E71] font-medium hover:underline touch-manipulation py-1"
                                  onClick={() => toggleComments(item.id)}
                                >
                                  {item.comments - 1} yorum daha gör...
                                </button>
                              ) : openCommentId === item.id && (
                                <>
                                  <div className="flex space-x-2">
                                    <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Kullanıcı" className="w-7 h-7 rounded-full" />
                                    <div className="flex-1 bg-gray-50 dark:bg-gray-800/50 rounded-lg p-2">
                                      <div className="flex justify-between items-start">
                                        <div className="text-xs font-medium text-gray-900 dark:text-white">Zeynep Kaya</div>
                                        <div className="text-[10px] text-gray-500">45 dakika önce</div>
                                      </div>
                                      <p className="text-xs text-gray-700 dark:text-gray-300 mt-1">Algoritma değişikliklerini nasıl takip ediyorsun? Benim için en zor kısım bu.</p>
                                    </div>
                                  </div>
                                  <div className="flex space-x-2">
                                    <img src="https://randomuser.me/api/portraits/women/22.jpg" alt="Kullanıcı" className="w-7 h-7 rounded-full" />
                                    <div className="flex-1 bg-gray-50 dark:bg-gray-800/50 rounded-lg p-2">
                                      <div className="flex justify-between items-start">
                                        <div className="text-xs font-medium text-gray-900 dark:text-white">Elif Yıldız</div>
                                        <div className="text-[10px] text-gray-500">30 dakika önce</div>
                                      </div>
                                      <p className="text-xs text-gray-700 dark:text-gray-300 mt-1">Paylaştığın için teşekkürler, çok faydalı oldu!</p>
                                    </div>
                                  </div>
                                  <div className="flex space-x-2">
                                    <img src="https://randomuser.me/api/portraits/men/45.jpg" alt="Kullanıcı" className="w-7 h-7 rounded-full" />
                                    <div className="flex-1 bg-gray-50 dark:bg-gray-800/50 rounded-lg p-2">
                                      <div className="flex justify-between items-start">
                                        <div className="text-xs font-medium text-gray-900 dark:text-white">Ali Kaya</div>
                                        <div className="text-[10px] text-gray-500">15 dakika önce</div>
                                      </div>
                                      <p className="text-xs text-gray-700 dark:text-gray-300 mt-1">Hangi içerik türleri senin için en iyi sonuç veriyor?</p>
                                    </div>
                                  </div>
                                  <button
                                    className="text-xs text-gray-500 font-medium hover:underline"
                                    onClick={() => toggleComments(item.id)}
                                  >
                                    Yorumları gizle
                                  </button>
                                </>
                              )}
                            </>
                          ) : item.id === 2 ? (
                            <>
                              <div className="flex space-x-2">
                                <img src="https://randomuser.me/api/portraits/women/12.jpg" alt="Kullanıcı" className="w-7 h-7 rounded-full" />
                                <div className="flex-1 bg-gray-50 dark:bg-gray-800/50 rounded-lg p-2">
                                  <div className="flex justify-between items-start">
                                    <div className="text-xs font-medium text-gray-900 dark:text-white">Ayşe Demir</div>
                                    <div className="text-[10px] text-gray-500">2 saat önce</div>
                                  </div>
                                  <p className="text-xs text-gray-700 dark:text-gray-300 mt-1">Ben de bu eğitimi tamamladım, gerçekten çok faydalıydı!</p>
                                </div>
                              </div>

                              {openCommentId !== item.id && item.comments > 1 ? (
                                <button
                                  className="text-xs text-[#FF3E71] font-medium hover:underline"
                                  onClick={() => toggleComments(item.id)}
                                >
                                  {item.comments - 1} yorum daha gör...
                                </button>
                              ) : openCommentId === item.id && (
                                <>
                                  <div className="flex space-x-2">
                                    <img src="https://randomuser.me/api/portraits/men/28.jpg" alt="Kullanıcı" className="w-7 h-7 rounded-full" />
                                    <div className="flex-1 bg-gray-50 dark:bg-gray-800/50 rounded-lg p-2">
                                      <div className="flex justify-between items-start">
                                        <div className="text-xs font-medium text-gray-900 dark:text-white">Ahmet Yılmaz</div>
                                        <div className="text-[10px] text-gray-500">1 saat önce</div>
                                      </div>
                                      <p className="text-xs text-gray-700 dark:text-gray-300 mt-1">Eğitimden sonra izlenme oranlarında artış oldu mu?</p>
                                    </div>
                                  </div>
                                  <div className="flex space-x-2">
                                    <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Kullanıcı" className="w-7 h-7 rounded-full" />
                                    <div className="flex-1 bg-gray-50 dark:bg-gray-800/50 rounded-lg p-2">
                                      <div className="flex justify-between items-start">
                                        <div className="text-xs font-medium text-gray-900 dark:text-white">Mehmet Yılmaz</div>
                                        <div className="text-[10px] text-gray-500">45 dakika önce</div>
                                      </div>
                                      <p className="text-xs text-gray-700 dark:text-gray-300 mt-1">Hangi bölüm senin için en faydalı oldu?</p>
                                    </div>
                                  </div>
                                  <button
                                    className="text-xs text-gray-500 font-medium hover:underline"
                                    onClick={() => toggleComments(item.id)}
                                  >
                                    Yorumları gizle
                                  </button>
                                </>
                              )}
                            </>
                          ) : item.id === 3 ? (
                            <>
                              {item.comments > 0 ? (
                                <>
                                  <div className="flex space-x-2">
                                    <img src="https://randomuser.me/api/portraits/men/22.jpg" alt="Kullanıcı" className="w-7 h-7 rounded-full" />
                                    <div className="flex-1 bg-gray-50 dark:bg-gray-800/50 rounded-lg p-2">
                                      <div className="flex justify-between items-start">
                                        <div className="text-xs font-medium text-gray-900 dark:text-white">Kemal Özkan</div>
                                        <div className="text-[10px] text-gray-500">3 saat önce</div>
                                      </div>
                                      <p className="text-xs text-gray-700 dark:text-gray-300 mt-1">Kayıt linkini paylaşabilir misin? Kaçırdım yayını.</p>
                                    </div>
                                  </div>

                                  {openCommentId !== item.id && item.comments > 1 ? (
                                    <button
                                      className="text-xs text-[#FF3E71] font-medium hover:underline"
                                      onClick={() => toggleComments(item.id)}
                                    >
                                      {item.comments - 1} yorum daha gör...
                                    </button>
                                  ) : openCommentId === item.id && (
                                    <>
                                      <div className="flex space-x-2">
                                        <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Kullanıcı" className="w-7 h-7 rounded-full" />
                                        <div className="flex-1 bg-gray-50 dark:bg-gray-800/50 rounded-lg p-2">
                                          <div className="flex justify-between items-start">
                                            <div className="text-xs font-medium text-gray-900 dark:text-white">Zeynep Kaya</div>
                                            <div className="text-[10px] text-gray-500">2 saat önce</div>
                                          </div>
                                          <p className="text-xs text-gray-700 dark:text-gray-300 mt-1">İşte link: tiktok.com/akademi/kayit123 - Bir sonraki yayın ne zaman olacak?</p>
                                        </div>
                                      </div>
                                      <div className="flex space-x-2">
                                        <img src="https://randomuser.me/api/portraits/women/28.jpg" alt="Kullanıcı" className="w-7 h-7 rounded-full" />
                                        <div className="flex-1 bg-gray-50 dark:bg-gray-800/50 rounded-lg p-2">
                                          <div className="flex justify-between items-start">
                                            <div className="text-xs font-medium text-gray-900 dark:text-white">Seda Demir</div>
                                            <div className="text-[10px] text-gray-500">1 saat önce</div>
                                          </div>
                                          <p className="text-xs text-gray-700 dark:text-gray-300 mt-1">Çok faydalı bir yayındı, teşekkürler!</p>
                                        </div>
                                      </div>
                                      <button
                                        className="text-xs text-gray-500 font-medium hover:underline"
                                        onClick={() => toggleComments(item.id)}
                                      >
                                        Yorumları gizle
                                      </button>
                                    </>
                                  )}
                                </>
                              ) : (
                                <div className="text-xs text-gray-500 dark:text-gray-400 text-center py-2">Henüz yorum yapılmamış.</div>
                              )}
                            </>
                          ) : (
                            <div className="text-xs text-gray-500 dark:text-gray-400 text-center py-2">Henüz yorum yapılmamış.</div>
                          )}
                        </div>

                        {/* Yorum yapma alanı - Her zaman görünür */}
                        <div className="mt-2 flex space-x-1.5 px-0">
                          {(tiktokUser?.avatar_url || userInfo?.profile_image || userInfo?.avatar_url) ? (
                            <img
                              src={tiktokUser?.avatar_url || userInfo?.profile_image || userInfo?.avatar_url}
                              alt={tiktokUser?.display_name || userInfo?.name || 'Kullanıcı'}
                              className="w-6 h-6 rounded-full object-cover border border-gray-200 dark:border-gray-600"
                              onError={(e) => {
                                (e.target as HTMLImageElement).src = 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face&auto=format&q=80';
                              }}
                            />
                          ) : null}
                          <div className={`w-6 h-6 rounded-full bg-[#FF3E71]/10 dark:bg-[#FF3E71]/20 flex items-center justify-center text-[#FF3E71] ${(tiktokUser?.avatar_url || userInfo?.profile_image || userInfo?.avatar_url) ? 'hidden' : ''}`}>
                            <span className="font-medium text-[10px]">{(tiktokUser?.display_name || userInfo?.name || 'T').charAt(0).toUpperCase()}</span>
                          </div>
                          <div className="flex-1 relative">
                            <input
                              id={`comment-input-${item.id}`}
                              type="text"
                              className="w-full p-1.5 text-[10px] sm:text-xs border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 rounded-full focus:ring-[#FF3E71] focus:border-[#FF3E71] text-gray-700 dark:text-gray-200 placeholder-gray-400 dark:placeholder-gray-500"
                              placeholder="Yorum yaz..."
                            />
                            <button className="absolute right-1.5 top-1/2 -translate-y-1/2 text-[#FF3E71] hover:text-[#FF5F87] transition-colors p-1 hover:bg-[#FF3E71]/10 rounded-full">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                              </svg>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </motion.div>
              ))}

              {feedItems.length === 0 && (
                <div className="text-center py-10 bg-white dark:bg-[#16151c] rounded-lg shadow-sm">
                  <div className="w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-300 rounded-full flex items-center justify-center mb-4">
                    <FaStream size={24} />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">Hiç İçerik Bulunamadı</h3>
                  <p className="mt-1 text-gray-500 dark:text-gray-400">Henüz bu akışta içerik bulunmamaktadır.</p>
                  <button
                    className="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-full shadow-sm text-sm font-medium text-white bg-gradient-to-r from-[#FF3E71] to-[#FF5F87] hover:from-[#FF5F87] hover:to-[#FF3E71] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF3E71] transition-all duration-300"
                  >
                    İlk Paylaşımı Yap
                  </button>
                </div>
              )}
            </div>
          </div>
          {/* Sağ Sütun - Yan Konteynerler - Sadece Desktop'ta Görünür */}
          <div className="hidden xl:block w-80 2xl:w-96 flex-shrink-0 space-y-6 pr-4 2xl:pr-6">
            {/* Faydalı Bağlantılar */}
            {/* ...Faydalı Bağlantılar içeriği... */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Feed;