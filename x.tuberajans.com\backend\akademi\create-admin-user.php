<?php
/**
 * Ad<PERSON> kullanıcı oluşturma script
 */

ini_set('display_errors', 1);
error_reporting(E_ALL);

require_once __DIR__ . '/../config/config.php';

// CORS ayarları
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json');

try {
    // Doğrudan SQL sorgusu ile kullanıcı oluştur
    $db_takip->exec("
        INSERT INTO users (username, email, password, name, role, permissions, status, created_at, updated_at)
        VALUES (
            'admin',
            '<EMAIL>',
            '" . password_hash('Tuber123!', PASSWORD_DEFAULT) . "',
            'Admin',
            'admin',
            '{\"dashboard\":true,\"publishers\":true,\"influencers\":true,\"tasks\":true,\"reports\":true,\"ai_advisor\":true,\"events\":{\"view\":true,\"manage\":true},\"users\":{\"view\":true,\"manage\":true},\"akademi\":{\"dashboard\":true,\"duyurular\":true,\"egitimler\":true,\"destek\":true,\"kullanicilar\":true,\"ayarlar\":true}}',
            'active',
            NOW(),
            NOW()
        )
    ");

    $userId = $db_takip->lastInsertId();

    echo json_encode([
        'success' => true,
        'message' => 'Admin kullanıcısı başarıyla oluşturuldu.',
        'user_id' => $userId,
        'email' => '<EMAIL>',
        'username' => 'admin',
        'password' => 'Tuber123!'
    ]);

} catch (PDOException $e) {
    // Eğer kullanıcı zaten varsa (duplicate key hatası)
    if ($e->getCode() == 23000) {
        try {
            $stmt = $db_takip->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute(['<EMAIL>']);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            echo json_encode([
                'success' => true,
                'message' => 'Admin kullanıcısı zaten mevcut.',
                'user_id' => $user['id'],
                'email' => '<EMAIL>',
                'username' => 'admin',
                'password' => 'Tuber123!'
            ]);
        } catch (PDOException $e2) {
            echo json_encode([
                'success' => false,
                'message' => 'Kullanıcı bilgisi alınırken hata: ' . $e2->getMessage()
            ]);
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Veritabanı hatası: ' . $e->getMessage(),
            'error_code' => $e->getCode()
        ]);
    }
}
