import os
import subprocess
import time
import random
import logging
import sys
import threading
from typing import List, Dict, Optional, Any
from datetime import datetime

from PyQt5.QtCore import QThread, pyqtSignal
from selenium import webdriver
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
import psutil

from constants import config_manager
from utils import kill_chrome_processes, set_thread_status, wait_for_threads_to_finish, chrome_semaphore

logger = logging.getLogger('StatusChecker')

import threading

def close_popup_if_exists(driver: webdriver.Chrome):
    """
    Ekranda beklenmedik bir pop-up (ör. "Anladım" butonu) varsa kapatır.
    Yoksa hata vermeden geçer.
    """
    try:
        close_button = WebDriverWait(driver, 3).until(
            EC.element_to_be_clickable((By.XPATH, "//*[@data-id='work-summary-modal-btn']"))
        )
        close_button.click()
        time.sleep(1)
        logger.info("Pop-up kapatıldı")
    except TimeoutException:
        pass
    except Exception as e:
        logger.warning(f"Pop-up kapatılırken hata: {e}")
        
class StatusCheckerThread(QThread):
    """
    TikTok Backstage üzerinden kullanıcı durumlarını (örneğin 'Uygun', 'Uygun Değil' vb.)
    kontrol eden bir iş parçacığı (QThread). Belirli kullanıcı listesini chunk'lar halinde
    işleyerek veritabanında günceller. Stop sinyali geldiğinde döngü hızla sonlanır.
    """
    logSignal = pyqtSignal(str)       # UI'ye log mesajı gönderir.
    finishedSignal = pyqtSignal(str)  # Thread bittiğinde mesaj iletir.
    progressSignal = pyqtSignal(int)  # İlerleme yüzdesi

    def __init__(self,
                 db_manager: Any,
                 publishers: Optional[List[Dict]] = None,
                 parent: Optional[Any] = None) -> None:
        super().__init__(parent)
        self.logger = logging.getLogger('StatusChecker')
        self.db_manager = db_manager
        self.publishers: List[Dict] = publishers or []
        
        # STANDART Chrome yolları - main.py ile TAMAMEN aynı yolları kullan
        self.driver_path: str = r"C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe"
        self.chrome_profile_path: str = r"C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data"
        self.chrome_profile_directory_name: str = "Profile 1"
        self.chrome_binary_path: str = r"C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe"

        self._running: bool = True
        self.chunk_size: int = 30
        self.retry_count: int = 3
        self.retry_delay: int = 1
        self.rate_limit_delay: int = 1
        self.delay_between_batches: float = random.uniform(2, 4)
        self.driver: Optional[webdriver.Chrome] = None
        
        # Güvenli sinyal gönderimi için
        self.is_finished = False

    def log(self, msg: str, level=logging.INFO, show_in_ui=False) -> None:
        """Basit log sistemi - DUPLIKASYON ÖNLEME"""
        if level == logging.INFO:
            self.logger.info(f"[STATUS_CHECKER] {msg}")
        elif level == logging.WARNING:
            self.logger.warning(f"[STATUS_CHECKER] {msg}")
        elif level == logging.ERROR:
            self.logger.error(f"[STATUS_CHECKER] {msg}")

        # UI sinyali sadece gerektiğinde
        if show_in_ui and hasattr(self, 'logSignal'):
            self.logSignal.emit(msg)

    @staticmethod
    def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
        """
        Verilen listeyi chunk_size boyutunda parçalara ayırır.
        """
        return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]
        
    def run(self) -> None:
        self.log("🔍 StatusChecker thread RUN metodu başladı")
        self.log(f"📊 İşlenecek kullanıcı sayısı: {len(self.publishers) if self.publishers else 0}")
        self.log(f"🔧 Thread ID: {threading.current_thread().ident}")
        self.log(f"🔧 _running durumu: {self._running}")
        self.driver = None

        try:
            self.log("🔧 Try bloğuna girdi")
            # Chrome temizle - BASIT VERSİYON
            self.log("🔄 Chrome temizleniyor...")
            import subprocess

            subprocess.run(['taskkill', '/f', '/im', 'chrome.exe'], capture_output=True, text=True)
            subprocess.run(['taskkill', '/f', '/im', 'chromedriver.exe'], capture_output=True, text=True)
            time.sleep(3)

            self.log("✅ Chrome temizlendi")

            if not self._running:
                return

            if not self.publishers:
                self.log("⚠️ Sorgulanacak yayıncı yok, işlem sonlandırılıyor")

                if not self.is_finished:
                    self.is_finished = True
                    self.finishedSignal.emit("StatusChecker tamamlandı: Yayıncı bulunamadı.")
                return

            self.log(f"✅ {len(self.publishers)} kullanıcı işlenecek")
            self.log(f"📋 İlk 3 kullanıcı: {self.publishers[:3]}")
            
            # Thread durumunu güncelle - artık gerekli değil ama uyumluluk için bırakıyoruz
            # set_thread_status('status_checker', True)
            
            # Chrome'u başlat - TIMEOUT İLE
            self.log("🚀 Chrome WebDriver başlatılıyor...")
            try:
                self.driver = self.setup_driver()
                if not self.driver or not self._running:
                    self.log("❌ Chrome WebDriver başlatılamadı")

                    if not self.is_finished:
                        self.is_finished = True
                        self.finishedSignal.emit("StatusChecker tamamlandı: Chrome başlatılamadı.")
                    return

                self.log("✅ Chrome WebDriver başarıyla başlatıldı")

            except Exception as chrome_error:
                self.log(f"❌ Chrome başlatma hatası: {chrome_error}")

                if not self.is_finished:
                    self.is_finished = True
                    self.finishedSignal.emit(f"StatusChecker tamamlandı: Chrome hatası - {chrome_error}")
                return

            # Backstage portaline git - GELİŞMİŞ HATA YÖNETİMİ
            try:
                if not self._running:
                    return

                self.log("🌐 TikTok Backstage sayfasına gidiliyor...")

                # URL ve zaman aşımı ayarları - DAHA KISA TIMEOUT
                url = "https://live-backstage.tiktok.com/portal"
                self.driver.set_page_load_timeout(60)  # 60 saniye timeout

                # Sayfayı aç
                self.log(f"⏳ URL yükleniyor: {url}")

                try:
                    self.driver.get(url)
                    self.log("✅ Sayfa yükleme komutu gönderildi")
                except Exception as load_error:
                    self.log(f"❌ Sayfa yükleme hatası: {load_error}")
                    raise load_error

                if not self._running:
                    return

                # Sayfanın yüklenmesi için kısa bekleme
                self.log("⏳ Sayfa yüklenmesi bekleniyor (10 saniye)...")
                for i in range(10):
                    if not self._running:
                        return
                    try:
                        # Sayfa durumunu kontrol et
                        ready_state = self.driver.execute_script("return document.readyState")
                        if ready_state == "complete":
                            self.log(f"✅ Sayfa yüklendi (ready_state: {ready_state})")
                            break
                    except:
                        pass
                    time.sleep(1)

                # Sayfanın yüklendiğini doğrula
                if not self._running:
                    return

                current_url = self.driver.current_url
                self.log(f"📍 Mevcut URL: {current_url}")

                # URL kontrolü - daha esnek
                if "tiktok.com" not in current_url:
                    self.log(f"⚠️ TikTok sayfasına yönlendirilemedi: {current_url}")
                    if not self.is_finished:
                        self.is_finished = True
                        self.finishedSignal.emit("StatusChecker tamamlandı: TikTok sayfasına erişilemedi.")
                    return

                self.log(f"✅ TikTok sayfasına erişim sağlandı")

                # Pop-up'ları kapat
                try:
                    if not self._running:
                        return
                    self.log("🔄 Pop-up'lar kontrol ediliyor...")
                    close_popup_if_exists(self.driver)
                    self.log("✅ Pop-up kontrolü tamamlandı")
                except Exception as popup_error:
                    self.log(f"⚠️ Pop-up kapatma hatası: {popup_error}")

            except Exception as e:
                self.log(f"❌ Sayfaya erişim hatası: {e}", level=logging.ERROR)
                if self.driver:
                    try:
                        self.driver.quit()
                    except:
                        pass

                if not self.is_finished:
                    self.is_finished = True
                    self.finishedSignal.emit(f"StatusChecker tamamlanamadı: {e}")
                return
            
            if not self._running:
                return
                
            # Set ise listeye dönüştür
            if isinstance(self.publishers, set):
                self.publishers = list(self.publishers)
                
            # Yayıncı listesini chunk'lara ayır
            chunks = self.chunk_list(self.publishers, self.chunk_size)
            total_chunks = len(chunks)

            for i, chunk in enumerate(chunks, start=1):
                if not self._running:
                    break

                self.log(f"📦 Chunk {i}/{total_chunks} (içinde {len(chunk)} kullanıcı).")
                if not self.open_invite_panel(self.driver):
                    self.log("⚠ Panel açılamadı, bu chunk atlanıyor.")
                    continue
                if not self._running:
                    break

                self.fill_textarea(self.driver, chunk)
                if not self._running:
                    break

                self.click_next_button(self.driver)
                if not self._running:
                    break

                self.parse_and_update(self.driver)
                if not self._running:
                    break

                self.close_panel_with_back(self.driver)
                if not self._running:
                    break

                progress = int((i / total_chunks) * 100)
                self.progressSignal.emit(progress)

                # Chunk bitince kısa bekleme
                for _ in range(int(self.delay_between_batches)):
                    if not self._running:
                        break
                    time.sleep(1)
                if not self._running:
                    break

        except Exception as e:
            self.log(f"⚠ Kritik hata: {e}", level=logging.ERROR)
            self.logger.error("Critical error", exc_info=True)
        finally:
            # Thread durumunu güncelle - artık gerekli değil ama uyumluluk için bırakıyoruz
            # set_thread_status('status_checker', False)

            # WebDriver'ı kapat - AŞAMA SONU
            if self.driver:
                try:
                    self.driver.quit()
                    self.log("🔄 Chrome WebDriver kapatıldı")
                except Exception:
                    # Bağlantı hatalarını sessizce ele al
                    pass

            # Chrome işlemlerini zorla kapat
            try:
                import subprocess
                subprocess.run(['taskkill', '/f', '/im', 'chrome.exe'],
                             capture_output=True, text=True)
                subprocess.run(['taskkill', '/f', '/im', 'chromedriver.exe'],
                             capture_output=True, text=True)
                self.log("🔄 Tüm Chrome işlemleri kapatıldı")
            except:
                pass

            # Kaynakları temizle
            self._running = False
            
            # Güvenli sinyal gönderimi
            if not self.is_finished:
                self.is_finished = True
                try:
                    self.finishedSignal.emit("StatusChecker tamamlandı.")
                    # Log sinyalinin gönderildiğini belirt, ancak bunu en son yap
                    self.log("StatusChecker tamamlandı, ana programa sinyal gönderildi.")
                except Exception as e:
                    self.log(f"Sinyal gönderilirken hata: {e}", level=logging.ERROR)

    def setup_driver(self) -> Optional[webdriver.Chrome]:
        if not self._running:
            return None

        self.log("🚀 Chrome WebDriver kurulumu başlıyor...")

        try:
            # Yol kontrollerini yap
            self.log(f"📁 ChromeDriver yolu: {self.driver_path}")
            self.log(f"📁 Chrome profil yolu: {self.chrome_profile_path}")
            self.log(f"📁 Chrome profil klasörü: {self.chrome_profile_directory_name}")
            self.log(f"📁 Chrome binary yolu: {self.chrome_binary_path}")

            if not os.path.exists(self.driver_path):
                self.log(f"❌ ChromeDriver bulunamadı: {self.driver_path}", level=logging.ERROR)
                return None
            if not os.path.exists(self.chrome_profile_path):
                self.log(f"❌ Chrome profil yolu bulunamadı: {self.chrome_profile_path}", level=logging.ERROR)
                return None
            if not os.path.exists(self.chrome_binary_path):
                self.log(f"❌ Chrome binary bulunamadı: {self.chrome_binary_path}", level=logging.ERROR)
                return None

            self.log("✅ Tüm yollar doğrulandı")

            options = ChromeOptions()

            # Temel profil ayarları
            options.add_argument(f"user-data-dir={self.chrome_profile_path}")
            options.add_argument(f"profile-directory={self.chrome_profile_directory_name}")
            options.binary_location = self.chrome_binary_path

            # GELİŞMİŞ ANTİ-BOT ALGILAMA ÖNLEMELERİ
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_argument("--disable-web-security")
            options.add_argument("--disable-features=VizDisplayCompositor")
            options.add_argument("--disable-ipc-flooding-protection")
            options.add_experimental_option("excludeSwitches", ["enable-automation", "enable-logging"])
            options.add_experimental_option("useAutomationExtension", False)

            # User-Agent ve diğer başlıkları normal tarayıcı gibi ayarla
            options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

            # Performans ve stabilite ayarları
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-gpu")
            options.add_argument("--disable-software-rasterizer")
            options.add_argument("--disable-crash-reporter")
            options.add_argument("--disable-logging")
            options.add_argument("--disable-extensions")
            options.add_argument("--disable-default-apps")
            options.add_argument("--disable-popup-blocking")
            options.add_argument("--disable-notifications")
            options.add_argument("--no-first-run")
            options.add_argument("--no-default-browser-check")
            options.add_argument("--ignore-certificate-errors")
            options.add_argument("--ignore-ssl-errors")
            options.add_argument("--ignore-certificate-errors-spki-list")

            # Pencere ayarları
            options.add_argument("--start-maximized")
            options.add_argument("--window-size=1920,1080")

            # Ek gizlilik ayarları
            options.add_argument("--disable-background-timer-throttling")
            options.add_argument("--disable-backgrounding-occluded-windows")
            options.add_argument("--disable-renderer-backgrounding")
            options.add_argument("--disable-features=TranslateUI")

            # Chrome'un otomatik kapanmasını engelle
            options.add_experimental_option("detach", True)

            # Ek tercihler
            prefs = {
                "profile.default_content_setting_values": {
                    "notifications": 2,
                    "geolocation": 2,
                    "media_stream": 2,
                },
                "profile.managed_default_content_settings": {
                    "images": 1
                }
            }
            options.add_experimental_option("prefs", prefs)

            service = ChromeService(
                executable_path=self.driver_path,
                log_path="chromedriver_status_checker.log"  # ChromeDriver log dosyası
            )

            self.log("🔧 Chrome servisi başlatılıyor...")

            # Chrome'u başlat - hata yönetimi ile
            try:
                driver = webdriver.Chrome(service=service, options=options)
                self.log("✅ Chrome WebDriver başarıyla başlatıldı")

                # Tarayıcının tamamen yüklenmesi için bekle
                time.sleep(3)

            except Exception as chrome_start_error:
                self.log(f"❌ Chrome başlatma hatası: {chrome_start_error}", level=logging.ERROR)

                # Profil çakışması hatası kontrolü
                if "user data directory is already in use" in str(chrome_start_error):
                    self.log("⚠️ Chrome profil çakışması tespit edildi, tekrar deneniyor...", level=logging.WARNING)

                    # Ekstra bekleme ve temizlik
                    time.sleep(10)

                    # Chrome işlemlerini tekrar kapat
                    import subprocess
                    subprocess.run(['taskkill', '/f', '/im', 'chrome.exe'], capture_output=True, text=True)
                    subprocess.run(['taskkill', '/f', '/im', 'chromedriver.exe'], capture_output=True, text=True)
                    time.sleep(5)

                    # Tekrar dene
                    try:
                        driver = webdriver.Chrome(service=service, options=options)
                        self.log("✅ Chrome WebDriver ikinci denemede başlatıldı")
                        time.sleep(3)
                    except Exception as second_error:
                        self.log(f"❌ Chrome ikinci deneme hatası: {second_error}", level=logging.ERROR)
                        raise second_error
                else:
                    raise chrome_start_error

            try:
                # GELİŞMİŞ BOT ALGILAMA ÖNLEMELERİ - JAVASCRIPT
                stealth_script = """
                // Navigator.webdriver özelliğini kaldır
                Object.defineProperty(navigator, 'webdriver', {get: () => undefined});

                // Chrome runtime'ı gizle
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5]
                });

                // Languages ayarla
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['tr-TR', 'tr', 'en-US', 'en']
                });

                // Platform bilgisi
                Object.defineProperty(navigator, 'platform', {
                    get: () => 'Win32'
                });

                // Permissions API'yi gizle
                const originalQuery = window.navigator.permissions.query;
                window.navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                        Promise.resolve({ state: Notification.permission }) :
                        originalQuery(parameters)
                );

                // Chrome object'ini gizle
                delete window.chrome;

                // Console.debug'ı gizle
                console.debug = () => {};
                """

                driver.execute_script(stealth_script)
                self.log("✅ Gelişmiş bot algılama önlemleri uygulandı")

            except Exception as e_mask:
                self.log(f"⚠️ Bot algılama önlemleri uygulanamadı: {e_mask}", level=logging.WARNING)
                
            return driver
            
        except WebDriverException as wde:
            self.log(f"❌ Chrome WebDriverException: {wde}", level=logging.ERROR)
            return None
        except Exception as e:
            self.log(f"❌ Chrome WebDriver başlatılamadı (Genel Hata): {e}", level=logging.ERROR)
            self.logger.error("Chrome setup_driver error", exc_info=True)
            return None

    def open_invite_panel(self, driver: webdriver.Chrome) -> bool:
        """
        Davet panelini açmak için butona tıklar.
        """
        if not self._running:
            return False
        try:
            invite_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable(
                    (By.XPATH, "//span[contains(text(), 'İçerik üreticilerini davet edin')]")
                )
            )
            if not self._running:
                return False
                
            time.sleep(random.uniform(0.5, 1.0))
            if not self._running:
                return False
                
            actions = ActionChains(driver)
            actions.move_to_element(invite_button).pause(random.uniform(0.3, 0.8)).click().perform()
            time.sleep(random.uniform(1, 2))
            
            if not self._running:
                return False
                
            self.log("✅ Panel açıldı (davet butonu).")
            return True
            
        except TimeoutException:
            self.log("❌ Davet butonu bulunamadı.", level=logging.ERROR)
            return False
        except Exception as e:
            self.log(f"❌ Panel açma hatası: {e}", level=logging.ERROR)
            return False

    def fill_textarea(self, driver: webdriver.Chrome, chunk: List[Dict]) -> None:
        """
        Davet panelindeki textarea'ya kullanıcı adlarını yazar.
        """
        if not self._running:
            return
        try:
            textarea = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//textarea[@data-testId='inviteHostTextArea']")
                )
            )
            if not self._running:
                return
                
            driver.execute_script("arguments[0].scrollIntoView(true);", textarea)
            time.sleep(1)
            if not self._running:
                return
                
            actions = ActionChains(driver)
            actions.move_to_element(textarea).click().perform()
            time.sleep(1)
            if not self._running:
                return
                
            actions.key_down(Keys.CONTROL).send_keys('a').key_up(Keys.CONTROL).send_keys(Keys.BACKSPACE).perform()
            time.sleep(1)          
            if not self._running:
                return
                
            all_values = "\n".join([
                user["username"] if isinstance(user, dict) and "username" in user else str(user)
                for user in chunk
            ])
            actions.send_keys_to_element(textarea, all_values).perform()
            self.log(f"✅ {len(chunk)} kullanıcı textarea'ya yazıldı.")
        except Exception as e:
            self.log(f"❌ Textarea'ya kullanıcı ekleme hatası: {e}", level=logging.ERROR)

    def click_next_button(self, driver: webdriver.Chrome) -> None:
        """
        'İleri' butonuna tıklar.
        """
        if not self._running:
            return
        try:
            next_button = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//span[contains(text(), 'İleri')]/ancestor::button"))
            )
            if not self._running:
                return
                
            driver.execute_script("arguments[0].scrollIntoView(true);", next_button)
            time.sleep(random.uniform(0.5, 1.0))
            if not self._running:
                return
                
            actions = ActionChains(driver)
            actions.move_to_element(next_button).pause(random.uniform(0.3, 0.8)).click().perform()
            time.sleep(random.uniform(1, 2))
            if not self._running:
                return
                
            self.log("✅ 'İleri' butonuna tıklandı.")
        except TimeoutException:
            self.log("❌ 'İleri' butonu bulunamadı.", level=logging.ERROR)
        except Exception as e:
            self.log(f"❌ 'İleri' butonuna tıklanamadı: {e}", level=logging.ERROR)

    def parse_and_update(self, driver: webdriver.Chrome) -> None:
        """
        Kullanıcı durumlarını parse edip veritabanını günceller.
        """
        if not self._running:
            return

        try:
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".semi-sidesheet .semi-table-row"))
            )
            if not self._running:
                return
                
            time.sleep(1)
            if not self._running:
                return

            # JavaScript ile verileri tek seferde al
            user_data = driver.execute_script("""
                const rows = document.querySelectorAll('.semi-sidesheet .semi-table-row');
                const data = [];

                for (const row of rows) {
                    const usernameElement = row.querySelector('span.liveplatform-live-copy');
                    const statusElement = row.querySelector('div.semi-tag-content.semi-tag-content-center');
                    const extraStatusElement = row.querySelector('div.liveplatform-status-tag_extra-bottom span');
                    const isElite = row.querySelector('[data-id="invite-pillar-elite"]') !== null;
                    
                    const username = usernameElement ? usernameElement.innerText.trim() : null;
                    let status = statusElement ? statusElement.innerText.trim() : null;
                    const extraStatus = extraStatusElement ? extraStatusElement.innerText.trim() : '';
                    
                    if (status === 'Mevcut') {
                        status = 'Uygun';
                    } else if (status === 'Uygun değil' && extraStatus) {
                        status = extraStatus;
                    }
                    
                    if (isElite) {
                        status += ' Elite';
                    }
                    
                    if (username && status) {
                        data.push([username, status]);
                    }
                }

                return data;
            """)
            
            if not user_data:
                self.log("⚠ Panelde kullanıcı durumu parse edilemedi.", level=logging.WARNING)
                return
                
            # Güncel zamanı al
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # Veritabanı güncelleme sorgusu
            query = """
                INSERT INTO live_data (username, status, sorgu_tarihi) 
                VALUES (%s, %s, %s)
                ON DUPLICATE KEY UPDATE 
                    status = VALUES(status),
                    sorgu_tarihi = VALUES(sorgu_tarihi)
            """
            
            self.log(f"🔍 Toplam kullanıcı sayısı: {len(user_data)}")
            
            # Güncellenecek kayıtları hazırla
            updates = []
            for [user, st] in user_data:
                try:
                    # Temiz veriler
                    clean_user = user.strip()
                    clean_status = st.strip()
                    
                    if clean_user and clean_status:
                        updates.append((clean_user, clean_status, current_time))
                except Exception as e:
                    self.log(f"❌ Kullanıcı işleme hatası: {e}", level=logging.ERROR)

            try:
                # Kayıtları güncelle
                if updates:
                    self.db_manager.execute_query(query, params=updates, many=True)
                    self.log(f"💾 {len(updates)} kaydın durumu güncellendi.")
                else:
                    self.log("⚠ Güncellenecek kayıt bulunamadı!", level=logging.WARNING)
            except Exception as e:
                self.log(f"⚠ Veritabanı güncelleme hatası: {e}", level=logging.ERROR)
        
        except Exception as e:
            self.log(f"⚠ Tablo 10 sn'de yüklenmedi, parse edilemedi.", level=logging.WARNING)
            return
    
    def close_panel_with_back(self, driver: webdriver.Chrome) -> None:
        """
        Davet panelini 'Geri' butonuna tıklayarak kapatır.
        """
        if not self._running:
            return
        try:
            back_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//button[@data-id='invite-host-back']"))
            )
            if not self._running:
                return
                
            time.sleep(random.uniform(0.5, 1.0))
            if not self._running:
                return
                
            actions = ActionChains(driver)
            actions.move_to_element(back_button).pause(random.uniform(0.3, 0.8)).click().perform()
            time.sleep(random.uniform(1, 2))
            if not self._running:
                return
                
            self.log("✅ Geri butonuna basılarak panel kapatıldı.")
        except TimeoutException:
            self.log("⚠ Geri butonu bulunamadı.", level=logging.WARNING)
        except Exception as e:
            self.log(f"❌ Geri butonuna tıklanamadı: {e}", level=logging.ERROR)

    def stop(self) -> None:
        """
        Thread'i durdur - kullanıcı 'Stop' butonuna bastığında veya otomasyon durdurulurken çağrılır.
        Thread döngüsünden çıkmak için _running bayrağı False yapılır.
        """
        self._running = False
        self.log("⏹ StatusChecker durdurma sinyali alındı.")
    
    def cleanup(self) -> None:
        """Thread kaynaklarını temizler"""
        try:
            self._running = False
            
            if self.driver:
                try:
                    self.driver.quit()
                    self.log("✅ Chrome WebDriver kapatıldı.")
                except Exception as e:
                    self.log(f"WebDriver kapatma hatası: {e}", level=logging.ERROR, show_in_ui=False)
                
                self.driver = None
                
            # Chrome işlemlerini sonlandır - diğer thread'leri etkilememek için force=False
            kill_chrome_processes(self.log, force=False)
            
        except Exception as e:
            pass

if __name__ == "__main__":
    # Test modu kodu...
    pass