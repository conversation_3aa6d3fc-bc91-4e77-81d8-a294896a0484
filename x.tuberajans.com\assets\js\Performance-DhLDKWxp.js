import{j as e}from"./reactDnd-CIvPAkL_.js";import{r as o}from"./vendor-CnpYymF8.js";import{V as D}from"./App-DHTOkUiy.js";import{S as Q,a as ee}from"./api-CRj0mnM9.js";import{P as ae,S as y,c as r,T as t,A as se}from"./ThemeStyles-Ct0n9BwN.js";import{k as te,s as ie,K as re}from"./antd-gS---Efz.js";import{c as l}from"./createLucideIcon-DxVmGoQf.js";import{R as ne,T as le,t as A}from"./tr-Bk4hK31u.js";import{X as de}from"./x-BlF-lTk7.js";import{R as $,b as oe,c as T,X as L,Y as f,T as P,L as B,d as H,B as ce,e as me}from"./charts-CXWFy-zF.js";import{f as n,s as xe,a as ye,b as he,c as h}from"./utils-CtuI0RRe.js";import"./index-Bso3pfdw.js";const ge=[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]],W=l("activity",ge);const pe=[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]],ue=l("arrow-down",pe);const fe=[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]],je=l("chevron-left",fe);const ke=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],be=l("chevron-right",ke);const Ne=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],E=l("circle-check-big",Ne);const ve=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]],j=l("clock",ve);const we=[["path",{d:"M2.7 10.3a2.41 2.41 0 0 0 0 3.41l7.59 7.59a2.41 2.41 0 0 0 3.41 0l7.59-7.59a2.41 2.41 0 0 0 0-3.41l-7.59-7.59a2.41 2.41 0 0 0-3.41 0Z",key:"1f1r0c"}]],R=l("diamond",we);const _e=[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]],Ce=l("download",_e);const Se=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]],Me=l("users",Se),Ke=()=>{const[k,Y]=o.useState([]),[b,g]=o.useState(new Date),[p,N]=o.useState(""),[c,G]=o.useState({key:"",direction:"asc"}),[v,w]=o.useState(!1),[i,_]=o.useState(null),C=()=>{const a=xe(b,{weekStartsOn:1}),s=ye(a,7);return{start:n(a,"yyyy-MM-dd"),end:n(s,"yyyy-MM-dd"),startFormatted:n(a,"dd MMM yyyy",{locale:A}),endFormatted:n(s,"dd MMM yyyy",{locale:A})}},u=async()=>{w(!0);try{const{start:a,end:s}=C(),d=await ee(a,s);Y(d.data),D.success("Haftalık veriler başarıyla güncellendi")}catch(a){D.error("Veriler yüklenirken bir hata oluştu")}finally{w(!1)}};o.useEffect(()=>{u()},[b]);const K=()=>{g(a=>he(a))},F=()=>{g(a=>h(a,1))},I=a=>{G(s=>({key:a,direction:s.key===a&&s.direction==="asc"?"desc":"asc"}))},O=()=>{u()},U=a=>{a==="all"?u():N(a)},V=a=>{if(a&&a.length===2){const[s,d]=a;g(s.toDate())}},q=a=>{const s={...a,tasks:[{id:1,title:`${a.canli_yayin_gunu+2} gün yayın yap`,status:"pending",deadline:n(h(new Date,1),"yyyy-MM-dd")},{id:2,title:`${Math.round(a.elmaslar*1.1)} elmas hedefi`,status:"in_progress",deadline:n(h(new Date,1),"yyyy-MM-dd")},{id:3,title:`${Math.round(a.yeni_takipciler*1.1)} yeni takipçi hedefi`,status:"pending",deadline:n(h(new Date,1),"yyyy-MM-dd")}],weeklyPerformance:[{week:"4 hafta önce",streamHours:a.yayin_suresi*.7,diamonds:a.elmaslar*.7,engagement:82},{week:"3 hafta önce",streamHours:a.yayin_suresi*.8,diamonds:a.elmaslar*.8,engagement:86},{week:"2 hafta önce",streamHours:a.yayin_suresi*.9,diamonds:a.elmaslar*.9,engagement:88},{week:"Bu hafta",streamHours:a.yayin_suresi,diamonds:a.elmaslar,engagement:94}]};_(s)},z=a=>{switch(a){case"completed":return"bg-green-100 text-green-800";case"in_progress":return"bg-yellow-100 text-yellow-800";case"pending":return"bg-gray-100 text-gray-800";default:return"bg-gray-100 text-gray-800"}},X=a=>{switch(a){case"completed":return e.jsx(E,{className:"w-4 h-4 text-green-600"});case"in_progress":return e.jsx(j,{className:"w-4 h-4 text-yellow-600"});case"pending":return e.jsx(j,{className:"w-4 h-4 text-gray-600"});default:return null}},S=[...k.filter(a=>a.kullanici_adi.toLowerCase().includes(p.toLowerCase()))].sort((a,s)=>{if(!c.key)return 0;const d=c.direction==="asc"?1:-1,m=a[c.key],x=s[c.key];return typeof m=="string"&&typeof x=="string"?m.localeCompare(x)*d:(m>x?1:-1)*d}),M=a=>{if(!a)return"0sn";const s=Math.floor(a/3600),d=a%3600,m=Math.floor(d/60),x=d%60;return`${s}sa ${m}dk ${x}sn`},{startFormatted:Z,endFormatted:J}=C();return e.jsxs("div",{className:"p-6 performance-dashboard space-y-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx(ae,{children:"Performans İzleme"}),e.jsxs(te,{children:[e.jsx(ie,{defaultValue:"all",style:{width:150},onChange:U,options:[{value:"all",label:"Tüm Yayıncılar"},...Array.from(new Set(k.map(a=>a.kullanici_adi))).map(a=>({value:a,label:a}))]}),e.jsx(re.RangePicker,{onChange:V})]})]}),e.jsx("div",{className:"bg-white shadow rounded-lg",children:e.jsxs("div",{className:"p-6 space-y-4",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-center gap-4 bg-white p-4 rounded-xl",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("button",{onClick:K,className:"p-2 hover:bg-indigo-50 rounded-full transition-colors text-indigo-600",children:e.jsx(je,{className:"w-5 h-5"})}),e.jsxs("span",{className:"text-sm font-medium bg-gradient-to-r from-indigo-500 to-purple-600 bg-clip-text text-transparent",children:[Z," - ",J]}),e.jsx("button",{onClick:F,className:"p-2 hover:bg-indigo-50 rounded-full transition-colors text-indigo-600",children:e.jsx(be,{className:"w-5 h-5"})})]}),e.jsxs("div",{className:"relative",children:[e.jsx(Q,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),e.jsx("input",{type:"text",placeholder:"Kullanıcı adı ara...",value:p,onChange:a=>N(a.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"})]}),e.jsx("div",{className:"flex space-x-3",children:e.jsxs("button",{onClick:O,disabled:v,className:"flex items-center px-4 py-2 bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-lg hover:opacity-90 transition-all shadow-md hover:shadow-lg transform hover:translate-y-[-1px] disabled:opacity-50",children:[e.jsx(Ce,{className:"w-4 h-4 mr-2"}),"Haftalık Verileri Çek"]})})]}),i&&e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[e.jsx(y,{title:"Yayın Süresi",value:M(i.yayin_suresi),icon:e.jsx(j,{className:"w-5 h-5"}),gradient:r.gradients.primary}),e.jsx(y,{title:"Elmaslar",value:i.elmaslar.toLocaleString(),icon:e.jsx(R,{className:"w-5 h-5"}),gradient:r.gradients.warning}),e.jsx(y,{title:"Yeni Takipçiler",value:i.yeni_takipciler.toLocaleString(),icon:e.jsx(Me,{className:"w-5 h-5"}),gradient:r.gradients.success}),e.jsx(y,{title:"Aktivite Puanı",value:i.canli_yayin_gunu+" gün",icon:e.jsx(W,{className:"w-5 h-5"}),gradient:r.gradients.info})]}),e.jsx("div",{className:t.container,children:e.jsxs("table",{className:t.table,children:[e.jsx("thead",{className:t.thead,children:e.jsx("tr",{children:[{key:"kullanici_adi",label:"Kullanıcı Adı"},{key:"canli_yayin_gunu",label:"Canlı Yayın Günü"},{key:"yayin_suresi",label:"Yayın Süresi"},{key:"elmaslar",label:"Elmaslar"},{key:"yeni_takipciler",label:"Yeni Takipçiler"},{key:"aboneler",label:"Aboneler"},{key:"maclar",label:"Maçlar"},{key:"hafta_baslangici",label:"Başlangıç Tarihi"},{key:"hafta_bitisi",label:"Bitiş Tarihi"}].map(a=>e.jsx("th",{onClick:()=>I(a.key),className:`${t.th} cursor-pointer hover:bg-indigo-50`,children:e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("span",{children:a.label}),c.key===a.key&&(c.direction==="asc"?e.jsx(se,{className:"w-4 h-4 text-indigo-500"}):e.jsx(ue,{className:"w-4 h-4 text-indigo-500"}))]})},a.key))})}),e.jsx("tbody",{className:t.tbody,children:v?e.jsx("tr",{children:e.jsx("td",{colSpan:9,className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex justify-center items-center space-x-2",children:[e.jsx(ne,{className:"w-5 h-5 text-indigo-600 animate-spin"}),e.jsx("span",{children:"Yükleniyor..."})]})})}):S.length>0?S.map(a=>e.jsxs("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",onClick:()=>q(a),children:[e.jsx("td",{className:t.td,children:e.jsx("div",{className:"text-sm font-medium text-gray-900",children:a.kullanici_adi})}),e.jsx("td",{className:t.td,children:e.jsxs("div",{className:"text-sm text-gray-500",children:[a.canli_yayin_gunu," gün"]})}),e.jsx("td",{className:t.td,children:e.jsx("div",{className:"text-sm text-gray-500",children:M(a.yayin_suresi)})}),e.jsx("td",{className:t.td,children:e.jsx("div",{className:"text-sm text-gray-500",children:a.elmaslar.toLocaleString()})}),e.jsx("td",{className:t.td,children:e.jsx("div",{className:"text-sm text-gray-500",children:a.yeni_takipciler.toLocaleString()})}),e.jsx("td",{className:t.td,children:e.jsx("div",{className:"text-sm text-gray-500",children:a.aboneler.toLocaleString()})}),e.jsx("td",{className:t.td,children:e.jsx("div",{className:"text-sm text-gray-500",children:a.maclar})}),e.jsx("td",{className:t.td,children:e.jsx("div",{className:"text-sm text-gray-500",children:n(new Date(a.hafta_baslangici),"dd.MM.yyyy")})}),e.jsx("td",{className:t.td,children:e.jsx("div",{className:"text-sm text-gray-500",children:n(new Date(a.hafta_bitisi),"dd.MM.yyyy")})})]},`${a.kullanici_adi}-${a.hafta_baslangici}`)):e.jsx("tr",{children:e.jsx("td",{colSpan:9,className:"px-6 py-4 text-center text-gray-500",children:p?"Arama kriterlerine uygun sonuç bulunamadı":"Bu hafta için veri bulunamadı"})})})]})}),i&&e.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-60 overflow-y-auto h-full w-full z-50 backdrop-blur-sm",children:e.jsxs("div",{className:"relative top-20 mx-auto p-5 border w-4/5 shadow-2xl rounded-xl bg-white",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("h2",{className:"text-xl font-bold bg-gradient-to-r from-indigo-500 to-purple-600 bg-clip-text text-transparent",children:[i.kullanici_adi," - Detaylı Performans"]}),e.jsx("button",{onClick:()=>_(null),className:"text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100",children:e.jsx(de,{className:"w-6 h-6"})})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsx("div",{className:"space-y-6",children:i.weeklyPerformance&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"bg-white p-4 rounded-xl shadow-md",children:[e.jsxs("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[e.jsx(R,{className:"w-5 h-5 mr-2 text-indigo-500"}),"Yayın Süresi ve Elmas İlişkisi"]}),e.jsx("div",{className:"h-64",children:e.jsx($,{width:"100%",height:"100%",children:e.jsxs(oe,{data:i.weeklyPerformance,children:[e.jsxs("defs",{children:[e.jsxs("linearGradient",{id:"colorStreamHours",x1:"0",y1:"0",x2:"0",y2:"1",children:[e.jsx("stop",{offset:"5%",stopColor:r.solid.primary,stopOpacity:.8}),e.jsx("stop",{offset:"95%",stopColor:r.solid.primary,stopOpacity:.1})]}),e.jsxs("linearGradient",{id:"colorDiamonds",x1:"0",y1:"0",x2:"0",y2:"1",children:[e.jsx("stop",{offset:"5%",stopColor:r.solid.warning,stopOpacity:.8}),e.jsx("stop",{offset:"95%",stopColor:r.solid.warning,stopOpacity:.1})]})]}),e.jsx(T,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.jsx(L,{dataKey:"week"}),e.jsx(f,{yAxisId:"left"}),e.jsx(f,{yAxisId:"right",orientation:"right"}),e.jsx(P,{contentStyle:{borderRadius:"8px",border:"none",boxShadow:"0 4px 12px rgba(0,0,0,0.1)"}}),e.jsx(B,{}),e.jsx(H,{yAxisId:"left",type:"monotone",dataKey:"streamHours",name:"Yayın Süresi (Dk)",stroke:r.solid.primary,strokeWidth:2,activeDot:{r:8}}),e.jsx(H,{yAxisId:"right",type:"monotone",dataKey:"diamonds",name:"Elmaslar",stroke:r.solid.warning,strokeWidth:2})]})})})]}),e.jsxs("div",{className:"bg-white p-4 rounded-xl shadow-md",children:[e.jsxs("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[e.jsx(W,{className:"w-5 h-5 mr-2 text-indigo-500"}),"Haftalık Etkileşim Puanı"]}),e.jsx("div",{className:"h-64",children:e.jsx($,{width:"100%",height:"100%",children:e.jsxs(ce,{data:i.weeklyPerformance,children:[e.jsx("defs",{children:e.jsxs("linearGradient",{id:"barGradient",x1:"0",y1:"0",x2:"0",y2:"1",children:[e.jsx("stop",{offset:"0%",stopColor:r.solid.success}),e.jsx("stop",{offset:"100%",stopColor:r.solid.info})]})}),e.jsx(T,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.jsx(L,{dataKey:"week"}),e.jsx(f,{domain:[0,100]}),e.jsx(P,{contentStyle:{borderRadius:"8px",border:"none",boxShadow:"0 4px 12px rgba(0,0,0,0.1)"}}),e.jsx(B,{}),e.jsx(me,{dataKey:"engagement",name:"Etkileşim Puanı",fill:"url(#barGradient)",radius:[4,4,0,0]})]})})})]})]})}),e.jsxs("div",{className:"bg-white p-4 rounded-xl shadow-md",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h3",{className:"text-lg font-medium text-gray-900 flex items-center",children:[e.jsx(E,{className:"w-5 h-5 mr-2 text-indigo-500"}),"Haftalık Görevler"]}),e.jsx(le,{className:"w-5 h-5 text-indigo-600"})]}),e.jsx("div",{className:"space-y-4",children:i.tasks?i.tasks.map(a=>e.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:shadow-md transition-shadow",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[X(a.status),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:a.title}),e.jsxs("p",{className:"text-sm text-gray-500",children:["Bitiş: ",n(new Date(a.deadline),"dd.MM.yyyy")]})]})]}),e.jsx("span",{className:`px-3 py-1 rounded-full text-xs font-medium ${z(a.status)}`,children:a.status==="completed"?"Tamamlandı":a.status==="in_progress"?"Devam Ediyor":"Beklemede"})]},a.id)):e.jsx("div",{className:"text-center text-gray-500 py-4",children:"Bu hafta için görev tanımlanmamış"})})]})]})]})})]})})]})};export{Ke as default};
