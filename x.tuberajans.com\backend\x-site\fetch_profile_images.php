<?php
// TikTok profil resimlerini topluca çekip kaydeden endpoint
ini_set('display_errors', 1);
error_reporting(E_ALL);

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/auth.php';

header('Content-Type: application/json');

function jsonResponse($data, $status = 200) {
    http_response_code($status);
    echo json_encode($data);
    exit;
}

try {
    // Token doğrulama
    $userId = requireAuthToken();

    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        jsonResponse(['success' => false, 'error' => 'Sadece POST isteği destekleniyor.'], 405);
    }

    $input = json_decode(file_get_contents('php://input'), true);
    if (!isset($input['usernames']) || !is_array($input['usernames'])) {
        jsonResponse(['success' => false, 'error' => 'Kullanıcı adı listesi eksik veya hatalı.']);
    }
    $usernames = $input['usernames'];
    $type = (isset($input['type']) && strtolower($input['type']) === 'influencer') ? 'influencer' : 'publisher';
    $results = [];
    if ($type === 'influencer') {
        $uploadDir = __DIR__ . '/../../uploads/influencers/';
        $dbTable = 'influencer_info';
    } else {
        // Publisher'lar için uploads/profile_images klasörünü kullan
        $uploadDir = __DIR__ . '/../../uploads/profile_images/';
        $dbTable = 'publisher_info';
    }
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }

    foreach ($usernames as $username) {
        $username = trim($username);
        if (!$username) continue;
        $tiktokUrl = "https://www.tiktok.com/@$username";
        $profileImage = null;
        $success = false;
        $error = '';
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $tiktokUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');
            curl_setopt($ch, CURLOPT_TIMEOUT, 20);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
            $html = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            if ($httpCode !== 200 || !$html) {
                throw new Exception('TikTok profiline erişilemedi.');
            }
            // JSON içindeki avatarLarger, avatarMedium, avatarThumb alanlarını sırayla dene
            $patterns = [
                '/"avatarLarger":"([^"]+)"/',
                '/"avatarMedium":"([^"]+)"/',
                '/"avatarThumb":"([^"]+)"/'
            ];
            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $html, $matches)) {
                    $profileImage = $matches[1];
                    $profileImage = str_replace(['\\u002F', '\\/'], '/', $profileImage);
                    $profileImage = trim($profileImage, '"');
                    break;
                }
            }
            if (!$profileImage) {
                throw new Exception('Profil resmi bulunamadı.');
            }
            // Resmi indir ve kaydet
            $imgData = @file_get_contents($profileImage);
            if ($imgData === false) throw new Exception('Resim indirilemedi.');

            // Eski dosyaları temizle (publisher için)
            if ($type === 'publisher') {
                $oldFiles = glob($uploadDir . $username . '_*.jpg');
                foreach ($oldFiles as $oldFile) {
                    @unlink($oldFile);
                }
            }

            $filePath = $uploadDir . $username . '.jpg';
            file_put_contents($filePath, $imgData);
            $dbPath = $type === 'influencer'
                ? "/uploads/influencers/{$username}.jpg"
                : "/uploads/profile_images/{$username}.jpg";
            $success = true;
            // Veritabanında profile_image alanını güncelle
            $updateStmt = $db->prepare("UPDATE $dbTable SET profile_image = ? WHERE username = ?");
            $updateStmt->execute([$dbPath, $username]);
        } catch (Exception $ex) {
            $error = $ex->getMessage();
        }
        $results[] = [
            'username' => $username,
            'success' => $success,
            'profile_image' => $success ? $dbPath : null,
            'error' => $error
        ];
    }
    jsonResponse(['success' => true, 'results' => $results]);
} catch (Exception $e) {
    jsonResponse(['success' => false, 'error' => $e->getMessage()]);
}