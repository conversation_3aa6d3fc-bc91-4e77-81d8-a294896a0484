<?php
require_once __DIR__ . '/../config/config.php';

try {
    // Önce mevcut unique constraint'leri kontrol et
    $stmt = $db->prepare("SHOW INDEX FROM weekly_tasks WHERE Non_unique = 0 AND Key_name != 'PRIMARY'");
    $stmt->execute();
    $uniqueIndexes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($uniqueIndexes)) {
        echo "Unique constraint ekleniyor...\n";
        
        // Unique constraint ekle: kullanici_adi + hafta_baslangici + gorev_onerisi
        // Bu sayede aynı kullanıcı, aynı hafta, aynı görev için tekrar kayıt eklenirse güncelleme yapılır
        $alterQuery = "ALTER TABLE weekly_tasks 
                      ADD UNIQUE KEY unique_user_week_task (kullanici_adi, hafta_baslangici, gorev_onerisi(100))";
        
        $stmt = $db->prepare($alterQuery);
        $stmt->execute();
        
        echo "Unique constraint başarıyla eklendi!\n";
        echo "Artık aynı kullanıcı için aynı haftada aynı görev tekrar atanırsa güncelleme yapılacak.\n";
    } else {
        echo "Unique constraint zaten mevcut:\n";
        foreach ($uniqueIndexes as $index) {
            echo "- {$index['Key_name']}: {$index['Column_name']}\n";
        }
    }
    
    // Test için mevcut kayıt sayısını göster
    $stmt = $db->prepare("SELECT COUNT(*) as total FROM weekly_tasks");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "\nMevcut toplam görev sayısı: " . $result['total'] . "\n";
    
} catch (PDOException $e) {
    echo "Hata: " . $e->getMessage() . "\n";
    
    // Eğer duplicate entry hatası varsa, önce duplicate'leri temizle
    if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
        echo "\nDuplicate kayıtlar tespit edildi. Temizleniyor...\n";
        
        // Duplicate kayıtları temizle - en son eklenen kayıtları tut
        $cleanupQuery = "DELETE t1 FROM weekly_tasks t1
                        INNER JOIN weekly_tasks t2 
                        WHERE t1.id < t2.id 
                        AND t1.kullanici_adi = t2.kullanici_adi 
                        AND t1.hafta_baslangici = t2.hafta_baslangici 
                        AND t1.gorev_onerisi = t2.gorev_onerisi";
        
        $stmt = $db->prepare($cleanupQuery);
        $stmt->execute();
        
        echo "Duplicate kayıtlar temizlendi. Unique constraint tekrar deneniyor...\n";
        
        // Unique constraint'i tekrar dene
        try {
            $alterQuery = "ALTER TABLE weekly_tasks 
                          ADD UNIQUE KEY unique_user_week_task (kullanici_adi, hafta_baslangici, gorev_onerisi(100))";
            
            $stmt = $db->prepare($alterQuery);
            $stmt->execute();
            
            echo "Unique constraint başarıyla eklendi!\n";
        } catch (PDOException $e2) {
            echo "Tekrar deneme başarısız: " . $e2->getMessage() . "\n";
        }
    }
}
?>
