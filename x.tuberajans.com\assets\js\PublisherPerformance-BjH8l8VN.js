import{j as n}from"./reactDnd-CIvPAkL_.js";import{o as He,r as y,g as gr}from"./vendor-CnpYymF8.js";import{r as ht,k as yr,s as xr,j as kr}from"./App-DhIV03Gw.js";import{A as X,S as ye}from"./index-CVO3aNyS.js";import{q as Ce,Y as ft,T as kt,y as ke,z as N,J as ca,n as vr,I as pt,i as Q,v as P,U as br,m as Se,s as ge,K as Ma,l as nt,G as Ut,j as wr,V as Ke,p as Tt,W as Ze,x as da,o as Sr}from"./antd-gS---Efz.js";import{R as _r}from"./InfoCircleOutlined-GeNJ5oqS.js";import{R as ua}from"./UploadOutlined-C87fzLVr.js";import{h as C}from"./utils-CtuI0RRe.js";import"./tr-CwGFhkM0.js";import{c as vt}from"./createLucideIcon-DxVmGoQf.js";import{R as jr}from"./MoreOutlined-BeMbOAzl.js";import{P as Ar}from"./plus-DjpIx7VF.js";import{E as $r}from"./eye-C0V96B8t.js";import{R as Je,B as Ct,c as Qe,X as et,Y as Ge,T as tt,e as It,b as Er,L as Yr,d as ha}from"./charts-CXWFy-zF.js";const Dr=[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]],zr=vt("arrow-left",Dr);const Mr=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]],Tr=vt("arrow-right",Mr);const Cr=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],Ir=vt("chevron-down",Cr);const Nr=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],Rr=vt("chevron-up",Nr);var q=function(){return q=Object.assign||function(t){for(var a,r=1,i=arguments.length;r<i;r++){a=arguments[r];for(var s in a)Object.prototype.hasOwnProperty.call(a,s)&&(t[s]=a[s])}return t},q.apply(this,arguments)};function mt(e,t,a){if(a||arguments.length===2)for(var r=0,i=t.length,s;r<i;r++)(s||!(r in t))&&(s||(s=Array.prototype.slice.call(t,0,r)),s[r]=t[r]);return e.concat(s||Array.prototype.slice.call(t))}var T="-ms-",Fe="-moz-",Y="-webkit-",Ta="comm",bt="rule",Vt="decl",Pr="@import",Ca="@keyframes",Or="@layer",Ia=Math.abs,Xt=String.fromCharCode,Bt=Object.assign;function Br(e,t){return O(e,0)^45?(((t<<2^O(e,0))<<2^O(e,1))<<2^O(e,2))<<2^O(e,3):0}function Na(e){return e.trim()}function xe(e,t){return(e=t.exec(e))?e[0]:e}function w(e,t,a){return e.replace(t,a)}function it(e,t,a){return e.indexOf(t,a)}function O(e,t){return e.charCodeAt(t)|0}function Ne(e,t,a){return e.slice(t,a)}function de(e){return e.length}function Ra(e){return e.length}function We(e,t){return t.push(e),e}function Lr(e,t){return e.map(t).join("")}function fa(e,t){return e.filter(function(a){return!xe(a,t)})}var wt=1,Re=1,Pa=0,ae=0,R=0,Le="";function St(e,t,a,r,i,s,o,d){return{value:e,root:t,parent:a,type:r,props:i,children:s,line:wt,column:Re,length:o,return:"",siblings:d}}function _e(e,t){return Bt(St("",null,null,"",null,null,0,e.siblings),e,{length:-e.length},t)}function Te(e){for(;e.root;)e=_e(e.root,{children:[e]});We(e,e.siblings)}function Gr(){return R}function Kr(){return R=ae>0?O(Le,--ae):0,Re--,R===10&&(Re=1,wt--),R}function ie(){return R=ae<Pa?O(Le,ae++):0,Re++,R===10&&(Re=1,wt++),R}function De(){return O(Le,ae)}function st(){return ae}function _t(e,t){return Ne(Le,e,t)}function Lt(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Wr(e){return wt=Re=1,Pa=de(Le=e),ae=0,[]}function Fr(e){return Le="",e}function Nt(e){return Na(_t(ae-1,Gt(e===91?e+2:e===40?e+1:e)))}function Hr(e){for(;(R=De())&&R<33;)ie();return Lt(e)>2||Lt(R)>3?"":" "}function Ur(e,t){for(;--t&&ie()&&!(R<48||R>102||R>57&&R<65||R>70&&R<97););return _t(e,st()+(t<6&&De()==32&&ie()==32))}function Gt(e){for(;ie();)switch(R){case e:return ae;case 34:case 39:e!==34&&e!==39&&Gt(R);break;case 40:e===41&&Gt(e);break;case 92:ie();break}return ae}function Vr(e,t){for(;ie()&&e+R!==57;)if(e+R===84&&De()===47)break;return"/*"+_t(t,ae-1)+"*"+Xt(e===47?e:ie())}function Xr(e){for(;!Lt(De());)ie();return _t(e,ae)}function qr(e){return Fr(ot("",null,null,null,[""],e=Wr(e),0,[0],e))}function ot(e,t,a,r,i,s,o,d,f){for(var u=0,c=0,h=o,m=0,g=0,b=0,S=1,A=1,k=1,x=0,E="",D=i,z=s,_=r,v=E;A;)switch(b=x,x=ie()){case 40:if(b!=108&&O(v,h-1)==58){it(v+=w(Nt(x),"&","&\f"),"&\f",Ia(u?d[u-1]:0))!=-1&&(k=-1);break}case 34:case 39:case 91:v+=Nt(x);break;case 9:case 10:case 13:case 32:v+=Hr(b);break;case 92:v+=Ur(st()-1,7);continue;case 47:switch(De()){case 42:case 47:We(Zr(Vr(ie(),st()),t,a,f),f);break;default:v+="/"}break;case 123*S:d[u++]=de(v)*k;case 125*S:case 59:case 0:switch(x){case 0:case 125:A=0;case 59+c:k==-1&&(v=w(v,/\f/g,"")),g>0&&de(v)-h&&We(g>32?ma(v+";",r,a,h-1,f):ma(w(v," ","")+";",r,a,h-2,f),f);break;case 59:v+=";";default:if(We(_=pa(v,t,a,u,c,i,d,E,D=[],z=[],h,s),s),x===123)if(c===0)ot(v,t,_,_,D,s,h,d,z);else switch(m===99&&O(v,3)===110?100:m){case 100:case 108:case 109:case 115:ot(e,_,_,r&&We(pa(e,_,_,0,0,i,d,E,i,D=[],h,z),z),i,z,h,d,r?D:z);break;default:ot(v,_,_,_,[""],z,0,d,z)}}u=c=g=0,S=k=1,E=v="",h=o;break;case 58:h=1+de(v),g=b;default:if(S<1){if(x==123)--S;else if(x==125&&S++==0&&Kr()==125)continue}switch(v+=Xt(x),x*S){case 38:k=c>0?1:(v+="\f",-1);break;case 44:d[u++]=(de(v)-1)*k,k=1;break;case 64:De()===45&&(v+=Nt(ie())),m=De(),c=h=de(E=v+=Xr(st())),x++;break;case 45:b===45&&de(v)==2&&(S=0)}}return s}function pa(e,t,a,r,i,s,o,d,f,u,c,h){for(var m=i-1,g=i===0?s:[""],b=Ra(g),S=0,A=0,k=0;S<r;++S)for(var x=0,E=Ne(e,m+1,m=Ia(A=o[S])),D=e;x<b;++x)(D=Na(A>0?g[x]+" "+E:w(E,/&\f/g,g[x])))&&(f[k++]=D);return St(e,t,a,i===0?bt:d,f,u,c,h)}function Zr(e,t,a,r){return St(e,t,a,Ta,Xt(Gr()),Ne(e,2,-2),0,r)}function ma(e,t,a,r,i){return St(e,t,a,Vt,Ne(e,0,r),Ne(e,r+1,-1),r,i)}function Oa(e,t,a){switch(Br(e,t)){case 5103:return Y+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return Y+e+e;case 4789:return Fe+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return Y+e+Fe+e+T+e+e;case 5936:switch(O(e,t+11)){case 114:return Y+e+T+w(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return Y+e+T+w(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return Y+e+T+w(e,/[svh]\w+-[tblr]{2}/,"lr")+e}case 6828:case 4268:case 2903:return Y+e+T+e+e;case 6165:return Y+e+T+"flex-"+e+e;case 5187:return Y+e+w(e,/(\w+).+(:[^]+)/,Y+"box-$1$2"+T+"flex-$1$2")+e;case 5443:return Y+e+T+"flex-item-"+w(e,/flex-|-self/g,"")+(xe(e,/flex-|baseline/)?"":T+"grid-row-"+w(e,/flex-|-self/g,""))+e;case 4675:return Y+e+T+"flex-line-pack"+w(e,/align-content|flex-|-self/g,"")+e;case 5548:return Y+e+T+w(e,"shrink","negative")+e;case 5292:return Y+e+T+w(e,"basis","preferred-size")+e;case 6060:return Y+"box-"+w(e,"-grow","")+Y+e+T+w(e,"grow","positive")+e;case 4554:return Y+w(e,/([^-])(transform)/g,"$1"+Y+"$2")+e;case 6187:return w(w(w(e,/(zoom-|grab)/,Y+"$1"),/(image-set)/,Y+"$1"),e,"")+e;case 5495:case 3959:return w(e,/(image-set\([^]*)/,Y+"$1$`$1");case 4968:return w(w(e,/(.+:)(flex-)?(.*)/,Y+"box-pack:$3"+T+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+Y+e+e;case 4200:if(!xe(e,/flex-|baseline/))return T+"grid-column-align"+Ne(e,t)+e;break;case 2592:case 3360:return T+w(e,"template-","")+e;case 4384:case 3616:return a&&a.some(function(r,i){return t=i,xe(r.props,/grid-\w+-end/)})?~it(e+(a=a[t].value),"span",0)?e:T+w(e,"-start","")+e+T+"grid-row-span:"+(~it(a,"span",0)?xe(a,/\d+/):+xe(a,/\d+/)-+xe(e,/\d+/))+";":T+w(e,"-start","")+e;case 4896:case 4128:return a&&a.some(function(r){return xe(r.props,/grid-\w+-start/)})?e:T+w(w(e,"-end","-span"),"span ","")+e;case 4095:case 3583:case 4068:case 2532:return w(e,/(.+)-inline(.+)/,Y+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(de(e)-1-t>6)switch(O(e,t+1)){case 109:if(O(e,t+4)!==45)break;case 102:return w(e,/(.+:)(.+)-([^]+)/,"$1"+Y+"$2-$3$1"+Fe+(O(e,t+3)==108?"$3":"$2-$3"))+e;case 115:return~it(e,"stretch",0)?Oa(w(e,"stretch","fill-available"),t,a)+e:e}break;case 5152:case 5920:return w(e,/(.+?):(\d+)(\s*\/\s*(span)?\s*(\d+))?(.*)/,function(r,i,s,o,d,f,u){return T+i+":"+s+u+(o?T+i+"-span:"+(d?f:+f-+s)+u:"")+e});case 4949:if(O(e,t+6)===121)return w(e,":",":"+Y)+e;break;case 6444:switch(O(e,O(e,14)===45?18:11)){case 120:return w(e,/(.+:)([^;\s!]+)(;|(\s+)?!.+)?/,"$1"+Y+(O(e,14)===45?"inline-":"")+"box$3$1"+Y+"$2$3$1"+T+"$2box$3")+e;case 100:return w(e,":",":"+T)+e}break;case 5719:case 2647:case 2135:case 3927:case 2391:return w(e,"scroll-","scroll-snap-")+e}return e}function gt(e,t){for(var a="",r=0;r<e.length;r++)a+=t(e[r],r,e,t)||"";return a}function Jr(e,t,a,r){switch(e.type){case Or:if(e.children.length)break;case Pr:case Vt:return e.return=e.return||e.value;case Ta:return"";case Ca:return e.return=e.value+"{"+gt(e.children,r)+"}";case bt:if(!de(e.value=e.props.join(",")))return""}return de(a=gt(e.children,r))?e.return=e.value+"{"+a+"}":""}function Qr(e){var t=Ra(e);return function(a,r,i,s){for(var o="",d=0;d<t;d++)o+=e[d](a,r,i,s)||"";return o}}function en(e){return function(t){t.root||(t=t.return)&&e(t)}}function tn(e,t,a,r){if(e.length>-1&&!e.return)switch(e.type){case Vt:e.return=Oa(e.value,e.length,a);return;case Ca:return gt([_e(e,{value:w(e.value,"@","@"+Y)})],r);case bt:if(e.length)return Lr(a=e.props,function(i){switch(xe(i,r=/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":Te(_e(e,{props:[w(i,/:(read-\w+)/,":"+Fe+"$1")]})),Te(_e(e,{props:[i]})),Bt(e,{props:fa(a,r)});break;case"::placeholder":Te(_e(e,{props:[w(i,/:(plac\w+)/,":"+Y+"input-$1")]})),Te(_e(e,{props:[w(i,/:(plac\w+)/,":"+Fe+"$1")]})),Te(_e(e,{props:[w(i,/:(plac\w+)/,T+"input-$1")]})),Te(_e(e,{props:[i]})),Bt(e,{props:fa(a,r)});break}return""})}}var an={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},J={},Pe=typeof process!="undefined"&&J!==void 0&&(J.REACT_APP_SC_ATTR||J.SC_ATTR)||"data-styled",Ba="active",La="data-styled-version",jt="6.1.17",qt=`/*!sc*/
`,yt=typeof window!="undefined"&&"HTMLElement"in window,rn=!!(typeof SC_DISABLE_SPEEDY=="boolean"?SC_DISABLE_SPEEDY:typeof process!="undefined"&&J!==void 0&&J.REACT_APP_SC_DISABLE_SPEEDY!==void 0&&J.REACT_APP_SC_DISABLE_SPEEDY!==""?J.REACT_APP_SC_DISABLE_SPEEDY!=="false"&&J.REACT_APP_SC_DISABLE_SPEEDY:typeof process!="undefined"&&J!==void 0&&J.SC_DISABLE_SPEEDY!==void 0&&J.SC_DISABLE_SPEEDY!==""&&J.SC_DISABLE_SPEEDY!=="false"&&J.SC_DISABLE_SPEEDY),At=Object.freeze([]),Oe=Object.freeze({});function nn(e,t,a){return a===void 0&&(a=Oe),e.theme!==a.theme&&e.theme||t||a.theme}var Ga=new Set(["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","tr","track","u","ul","use","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","marker","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"]),sn=/[!"#$%&'()*+,./:;<=>?@[\\\]^`{|}~-]+/g,on=/(^-|-$)/g;function ga(e){return e.replace(sn,"-").replace(on,"")}var ln=/(a)(d)/gi,at=52,ya=function(e){return String.fromCharCode(e+(e>25?39:97))};function Kt(e){var t,a="";for(t=Math.abs(e);t>at;t=t/at|0)a=ya(t%at)+a;return(ya(t%at)+a).replace(ln,"$1-$2")}var Rt,Ka=5381,Ie=function(e,t){for(var a=t.length;a;)e=33*e^t.charCodeAt(--a);return e},Wa=function(e){return Ie(Ka,e)};function cn(e){return Kt(Wa(e)>>>0)}function dn(e){return e.displayName||e.name||"Component"}function Pt(e){return typeof e=="string"&&!0}var Fa=typeof Symbol=="function"&&Symbol.for,Ha=Fa?Symbol.for("react.memo"):60115,un=Fa?Symbol.for("react.forward_ref"):60112,hn={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},fn={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},Ua={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},pn=((Rt={})[un]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},Rt[Ha]=Ua,Rt);function xa(e){return("type"in(t=e)&&t.type.$$typeof)===Ha?Ua:"$$typeof"in e?pn[e.$$typeof]:hn;var t}var mn=Object.defineProperty,gn=Object.getOwnPropertyNames,ka=Object.getOwnPropertySymbols,yn=Object.getOwnPropertyDescriptor,xn=Object.getPrototypeOf,va=Object.prototype;function Va(e,t,a){if(typeof t!="string"){if(va){var r=xn(t);r&&r!==va&&Va(e,r,a)}var i=gn(t);ka&&(i=i.concat(ka(t)));for(var s=xa(e),o=xa(t),d=0;d<i.length;++d){var f=i[d];if(!(f in fn||a&&a[f]||o&&f in o||s&&f in s)){var u=yn(t,f);try{mn(e,f,u)}catch(c){}}}}return e}function Be(e){return typeof e=="function"}function Zt(e){return typeof e=="object"&&"styledComponentId"in e}function Ye(e,t){return e&&t?"".concat(e," ").concat(t):e||t||""}function ba(e,t){if(e.length===0)return"";for(var a=e[0],r=1;r<e.length;r++)a+=e[r];return a}function Ue(e){return e!==null&&typeof e=="object"&&e.constructor.name===Object.name&&!("props"in e&&e.$$typeof)}function Wt(e,t,a){if(a===void 0&&(a=!1),!a&&!Ue(e)&&!Array.isArray(e))return t;if(Array.isArray(t))for(var r=0;r<t.length;r++)e[r]=Wt(e[r],t[r]);else if(Ue(t))for(var r in t)e[r]=Wt(e[r],t[r]);return e}function Jt(e,t){Object.defineProperty(e,"toString",{value:t})}function Ve(e){for(var t=[],a=1;a<arguments.length;a++)t[a-1]=arguments[a];return new Error("An error occurred. See https://github.com/styled-components/styled-components/blob/main/packages/styled-components/src/utils/errors.md#".concat(e," for more information.").concat(t.length>0?" Args: ".concat(t.join(", ")):""))}var kn=function(){function e(t){this.groupSizes=new Uint32Array(512),this.length=512,this.tag=t}return e.prototype.indexOfGroup=function(t){for(var a=0,r=0;r<t;r++)a+=this.groupSizes[r];return a},e.prototype.insertRules=function(t,a){if(t>=this.groupSizes.length){for(var r=this.groupSizes,i=r.length,s=i;t>=s;)if((s<<=1)<0)throw Ve(16,"".concat(t));this.groupSizes=new Uint32Array(s),this.groupSizes.set(r),this.length=s;for(var o=i;o<s;o++)this.groupSizes[o]=0}for(var d=this.indexOfGroup(t+1),f=(o=0,a.length);o<f;o++)this.tag.insertRule(d,a[o])&&(this.groupSizes[t]++,d++)},e.prototype.clearGroup=function(t){if(t<this.length){var a=this.groupSizes[t],r=this.indexOfGroup(t),i=r+a;this.groupSizes[t]=0;for(var s=r;s<i;s++)this.tag.deleteRule(r)}},e.prototype.getGroup=function(t){var a="";if(t>=this.length||this.groupSizes[t]===0)return a;for(var r=this.groupSizes[t],i=this.indexOfGroup(t),s=i+r,o=i;o<s;o++)a+="".concat(this.tag.getRule(o)).concat(qt);return a},e}(),lt=new Map,xt=new Map,ct=1,rt=function(e){if(lt.has(e))return lt.get(e);for(;xt.has(ct);)ct++;var t=ct++;return lt.set(e,t),xt.set(t,e),t},vn=function(e,t){ct=t+1,lt.set(e,t),xt.set(t,e)},bn="style[".concat(Pe,"][").concat(La,'="').concat(jt,'"]'),wn=new RegExp("^".concat(Pe,'\\.g(\\d+)\\[id="([\\w\\d-]+)"\\].*?"([^"]*)')),Sn=function(e,t,a){for(var r,i=a.split(","),s=0,o=i.length;s<o;s++)(r=i[s])&&e.registerName(t,r)},_n=function(e,t){for(var a,r=((a=t.textContent)!==null&&a!==void 0?a:"").split(qt),i=[],s=0,o=r.length;s<o;s++){var d=r[s].trim();if(d){var f=d.match(wn);if(f){var u=0|parseInt(f[1],10),c=f[2];u!==0&&(vn(c,u),Sn(e,c,f[3]),e.getTag().insertRules(u,i)),i.length=0}else i.push(d)}}},wa=function(e){for(var t=document.querySelectorAll(bn),a=0,r=t.length;a<r;a++){var i=t[a];i&&i.getAttribute(Pe)!==Ba&&(_n(e,i),i.parentNode&&i.parentNode.removeChild(i))}};function jn(){return typeof __webpack_nonce__!="undefined"?__webpack_nonce__:null}var Xa=function(e){var t=document.head,a=e||t,r=document.createElement("style"),i=function(d){var f=Array.from(d.querySelectorAll("style[".concat(Pe,"]")));return f[f.length-1]}(a),s=i!==void 0?i.nextSibling:null;r.setAttribute(Pe,Ba),r.setAttribute(La,jt);var o=jn();return o&&r.setAttribute("nonce",o),a.insertBefore(r,s),r},An=function(){function e(t){this.element=Xa(t),this.element.appendChild(document.createTextNode("")),this.sheet=function(a){if(a.sheet)return a.sheet;for(var r=document.styleSheets,i=0,s=r.length;i<s;i++){var o=r[i];if(o.ownerNode===a)return o}throw Ve(17)}(this.element),this.length=0}return e.prototype.insertRule=function(t,a){try{return this.sheet.insertRule(a,t),this.length++,!0}catch(r){return!1}},e.prototype.deleteRule=function(t){this.sheet.deleteRule(t),this.length--},e.prototype.getRule=function(t){var a=this.sheet.cssRules[t];return a&&a.cssText?a.cssText:""},e}(),$n=function(){function e(t){this.element=Xa(t),this.nodes=this.element.childNodes,this.length=0}return e.prototype.insertRule=function(t,a){if(t<=this.length&&t>=0){var r=document.createTextNode(a);return this.element.insertBefore(r,this.nodes[t]||null),this.length++,!0}return!1},e.prototype.deleteRule=function(t){this.element.removeChild(this.nodes[t]),this.length--},e.prototype.getRule=function(t){return t<this.length?this.nodes[t].textContent:""},e}(),En=function(){function e(t){this.rules=[],this.length=0}return e.prototype.insertRule=function(t,a){return t<=this.length&&(this.rules.splice(t,0,a),this.length++,!0)},e.prototype.deleteRule=function(t){this.rules.splice(t,1),this.length--},e.prototype.getRule=function(t){return t<this.length?this.rules[t]:""},e}(),Sa=yt,Yn={isServer:!yt,useCSSOMInjection:!rn},qa=function(){function e(t,a,r){t===void 0&&(t=Oe),a===void 0&&(a={});var i=this;this.options=q(q({},Yn),t),this.gs=a,this.names=new Map(r),this.server=!!t.isServer,!this.server&&yt&&Sa&&(Sa=!1,wa(this)),Jt(this,function(){return function(s){for(var o=s.getTag(),d=o.length,f="",u=function(h){var m=function(k){return xt.get(k)}(h);if(m===void 0)return"continue";var g=s.names.get(m),b=o.getGroup(h);if(g===void 0||!g.size||b.length===0)return"continue";var S="".concat(Pe,".g").concat(h,'[id="').concat(m,'"]'),A="";g!==void 0&&g.forEach(function(k){k.length>0&&(A+="".concat(k,","))}),f+="".concat(b).concat(S,'{content:"').concat(A,'"}').concat(qt)},c=0;c<d;c++)u(c);return f}(i)})}return e.registerId=function(t){return rt(t)},e.prototype.rehydrate=function(){!this.server&&yt&&wa(this)},e.prototype.reconstructWithOptions=function(t,a){return a===void 0&&(a=!0),new e(q(q({},this.options),t),this.gs,a&&this.names||void 0)},e.prototype.allocateGSInstance=function(t){return this.gs[t]=(this.gs[t]||0)+1},e.prototype.getTag=function(){return this.tag||(this.tag=(t=function(a){var r=a.useCSSOMInjection,i=a.target;return a.isServer?new En(i):r?new An(i):new $n(i)}(this.options),new kn(t)));var t},e.prototype.hasNameForId=function(t,a){return this.names.has(t)&&this.names.get(t).has(a)},e.prototype.registerName=function(t,a){if(rt(t),this.names.has(t))this.names.get(t).add(a);else{var r=new Set;r.add(a),this.names.set(t,r)}},e.prototype.insertRules=function(t,a,r){this.registerName(t,a),this.getTag().insertRules(rt(t),r)},e.prototype.clearNames=function(t){this.names.has(t)&&this.names.get(t).clear()},e.prototype.clearRules=function(t){this.getTag().clearGroup(rt(t)),this.clearNames(t)},e.prototype.clearTag=function(){this.tag=void 0},e}(),Dn=/&/g,zn=/^\s*\/\/.*$/gm;function Za(e,t){return e.map(function(a){return a.type==="rule"&&(a.value="".concat(t," ").concat(a.value),a.value=a.value.replaceAll(",",",".concat(t," ")),a.props=a.props.map(function(r){return"".concat(t," ").concat(r)})),Array.isArray(a.children)&&a.type!=="@keyframes"&&(a.children=Za(a.children,t)),a})}function Mn(e){var t,a,r,i=Oe,s=i.options,o=s===void 0?Oe:s,d=i.plugins,f=d===void 0?At:d,u=function(m,g,b){return b.startsWith(a)&&b.endsWith(a)&&b.replaceAll(a,"").length>0?".".concat(t):m},c=f.slice();c.push(function(m){m.type===bt&&m.value.includes("&")&&(m.props[0]=m.props[0].replace(Dn,a).replace(r,u))}),o.prefix&&c.push(tn),c.push(Jr);var h=function(m,g,b,S){g===void 0&&(g=""),b===void 0&&(b=""),S===void 0&&(S="&"),t=S,a=g,r=new RegExp("\\".concat(a,"\\b"),"g");var A=m.replace(zn,""),k=qr(b||g?"".concat(b," ").concat(g," { ").concat(A," }"):A);o.namespace&&(k=Za(k,o.namespace));var x=[];return gt(k,Qr(c.concat(en(function(E){return x.push(E)})))),x};return h.hash=f.length?f.reduce(function(m,g){return g.name||Ve(15),Ie(m,g.name)},Ka).toString():"",h}var Tn=new qa,Ft=Mn(),Ja=He.createContext({shouldForwardProp:void 0,styleSheet:Tn,stylis:Ft});Ja.Consumer;He.createContext(void 0);function _a(){return y.useContext(Ja)}var Cn=function(){function e(t,a){var r=this;this.inject=function(i,s){s===void 0&&(s=Ft);var o=r.name+s.hash;i.hasNameForId(r.id,o)||i.insertRules(r.id,o,s(r.rules,o,"@keyframes"))},this.name=t,this.id="sc-keyframes-".concat(t),this.rules=a,Jt(this,function(){throw Ve(12,String(r.name))})}return e.prototype.getName=function(t){return t===void 0&&(t=Ft),this.name+t.hash},e}(),In=function(e){return e>="A"&&e<="Z"};function ja(e){for(var t="",a=0;a<e.length;a++){var r=e[a];if(a===1&&r==="-"&&e[0]==="-")return e;In(r)?t+="-"+r.toLowerCase():t+=r}return t.startsWith("ms-")?"-"+t:t}var Qa=function(e){return e==null||e===!1||e===""},er=function(e){var t,a,r=[];for(var i in e){var s=e[i];e.hasOwnProperty(i)&&!Qa(s)&&(Array.isArray(s)&&s.isCss||Be(s)?r.push("".concat(ja(i),":"),s,";"):Ue(s)?r.push.apply(r,mt(mt(["".concat(i," {")],er(s),!1),["}"],!1)):r.push("".concat(ja(i),": ").concat((t=i,(a=s)==null||typeof a=="boolean"||a===""?"":typeof a!="number"||a===0||t in an||t.startsWith("--")?String(a).trim():"".concat(a,"px")),";")))}return r};function ze(e,t,a,r){if(Qa(e))return[];if(Zt(e))return[".".concat(e.styledComponentId)];if(Be(e)){if(!Be(s=e)||s.prototype&&s.prototype.isReactComponent||!t)return[e];var i=e(t);return ze(i,t,a,r)}var s;return e instanceof Cn?a?(e.inject(a,r),[e.getName(r)]):[e]:Ue(e)?er(e):Array.isArray(e)?Array.prototype.concat.apply(At,e.map(function(o){return ze(o,t,a,r)})):[e.toString()]}function Nn(e){for(var t=0;t<e.length;t+=1){var a=e[t];if(Be(a)&&!Zt(a))return!1}return!0}var Rn=Wa(jt),Pn=function(){function e(t,a,r){this.rules=t,this.staticRulesId="",this.isStatic=(r===void 0||r.isStatic)&&Nn(t),this.componentId=a,this.baseHash=Ie(Rn,a),this.baseStyle=r,qa.registerId(a)}return e.prototype.generateAndInjectStyles=function(t,a,r){var i=this.baseStyle?this.baseStyle.generateAndInjectStyles(t,a,r):"";if(this.isStatic&&!r.hash)if(this.staticRulesId&&a.hasNameForId(this.componentId,this.staticRulesId))i=Ye(i,this.staticRulesId);else{var s=ba(ze(this.rules,t,a,r)),o=Kt(Ie(this.baseHash,s)>>>0);if(!a.hasNameForId(this.componentId,o)){var d=r(s,".".concat(o),void 0,this.componentId);a.insertRules(this.componentId,o,d)}i=Ye(i,o),this.staticRulesId=o}else{for(var f=Ie(this.baseHash,r.hash),u="",c=0;c<this.rules.length;c++){var h=this.rules[c];if(typeof h=="string")u+=h;else if(h){var m=ba(ze(h,t,a,r));f=Ie(f,m+c),u+=m}}if(u){var g=Kt(f>>>0);a.hasNameForId(this.componentId,g)||a.insertRules(this.componentId,g,r(u,".".concat(g),void 0,this.componentId)),i=Ye(i,g)}}return i},e}(),tr=He.createContext(void 0);tr.Consumer;var Ot={};function On(e,t,a){var r=Zt(e),i=e,s=!Pt(e),o=t.attrs,d=o===void 0?At:o,f=t.componentId,u=f===void 0?function(D,z){var _=typeof D!="string"?"sc":ga(D);Ot[_]=(Ot[_]||0)+1;var v="".concat(_,"-").concat(cn(jt+_+Ot[_]));return z?"".concat(z,"-").concat(v):v}(t.displayName,t.parentComponentId):f,c=t.displayName,h=c===void 0?function(D){return Pt(D)?"styled.".concat(D):"Styled(".concat(dn(D),")")}(e):c,m=t.displayName&&t.componentId?"".concat(ga(t.displayName),"-").concat(t.componentId):t.componentId||u,g=r&&i.attrs?i.attrs.concat(d).filter(Boolean):d,b=t.shouldForwardProp;if(r&&i.shouldForwardProp){var S=i.shouldForwardProp;if(t.shouldForwardProp){var A=t.shouldForwardProp;b=function(D,z){return S(D,z)&&A(D,z)}}else b=S}var k=new Pn(a,m,r?i.componentStyle:void 0);function x(D,z){return function(_,v,ee){var $=_.attrs,j=_.componentStyle,K=_.defaultProps,se=_.foldedComponentIds,je=_.styledComponentId,Me=_.target,Ae=He.useContext(tr),ue=_a(),ve=_.shouldForwardProp||ue.shouldForwardProp,$e=nn(v,Ae,K)||Oe,W=function(be,pe,we){for(var ne,B=q(q({},pe),{className:void 0,theme:we}),me=0;me<be.length;me+=1){var le=Be(ne=be[me])?ne(B):ne;for(var H in le)B[H]=H==="className"?Ye(B[H],le[H]):H==="style"?q(q({},B[H]),le[H]):le[H]}return pe.className&&(B.className=Ye(B.className,pe.className)),B}($,v,$e),he=W.as||Me,fe={};for(var F in W)W[F]===void 0||F[0]==="$"||F==="as"||F==="theme"&&W.theme===$e||(F==="forwardedAs"?fe.as=W.forwardedAs:ve&&!ve(F,he)||(fe[F]=W[F]));var oe=function(be,pe){var we=_a(),ne=be.generateAndInjectStyles(pe,we.styleSheet,we.stylis);return ne}(j,W),re=Ye(se,je);return oe&&(re+=" "+oe),W.className&&(re+=" "+W.className),fe[Pt(he)&&!Ga.has(he)?"class":"className"]=re,ee&&(fe.ref=ee),y.createElement(he,fe)}(E,D,z)}x.displayName=h;var E=He.forwardRef(x);return E.attrs=g,E.componentStyle=k,E.displayName=h,E.shouldForwardProp=b,E.foldedComponentIds=r?Ye(i.foldedComponentIds,i.styledComponentId):"",E.styledComponentId=m,E.target=r?i.target:e,Object.defineProperty(E,"defaultProps",{get:function(){return this._foldedDefaultProps},set:function(D){this._foldedDefaultProps=r?function(z){for(var _=[],v=1;v<arguments.length;v++)_[v-1]=arguments[v];for(var ee=0,$=_;ee<$.length;ee++)Wt(z,$[ee],!0);return z}({},i.defaultProps,D):D}}),Jt(E,function(){return".".concat(E.styledComponentId)}),s&&Va(E,e,{attrs:!0,componentStyle:!0,displayName:!0,foldedComponentIds:!0,shouldForwardProp:!0,styledComponentId:!0,target:!0}),E}function Aa(e,t){for(var a=[e[0]],r=0,i=t.length;r<i;r+=1)a.push(t[r],e[r+1]);return a}var $a=function(e){return Object.assign(e,{isCss:!0})};function Bn(e){for(var t=[],a=1;a<arguments.length;a++)t[a-1]=arguments[a];if(Be(e)||Ue(e))return $a(ze(Aa(At,mt([e],t,!0))));var r=e;return t.length===0&&r.length===1&&typeof r[0]=="string"?ze(r):$a(ze(Aa(r,t)))}function Ht(e,t,a){if(a===void 0&&(a=Oe),!t)throw Ve(1,t);var r=function(i){for(var s=[],o=1;o<arguments.length;o++)s[o-1]=arguments[o];return e(t,a,Bn.apply(void 0,mt([i],s,!1)))};return r.attrs=function(i){return Ht(e,t,q(q({},a),{attrs:Array.prototype.concat(a.attrs,i).filter(Boolean)}))},r.withConfig=function(i){return Ht(e,t,q(q({},a),i))},r}var ar=function(e){return Ht(On,e)},$t=ar;Ga.forEach(function(e){$t[e]=ar(e)});const{TextArea:Ea}=pt,{Text:Ln,Paragraph:Si}=kt,Gn=$t.div`
  margin-top: 8px; 
  border: 1px solid #e0e0e0;
  border-radius: 6px; 
  padding: 0; /* Dış padding yok */
  background-color: #f0f2f5; /* E-posta arka planı gibi */
  height: ${e=>e.mode==="single"?"320px":"380px"}; /* Tek mod için daha küçük yükseklik */
  overflow-y: auto;

  /* Ana e-posta içeriği alanı */
  .email-body-wrapper {
    font-family: Arial, sans-serif !important; 
    font-size: 14px;
    line-height: 1.6 !important; 
    color: #333333 !important;
    background-color: #ffffff;
    padding: 20px;
    max-width: 600px; /* E-posta genişliğini sınırla */
    margin: 0 auto; /* Üst/alt margin kaldırıldı */
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    /* Min height ekleyerek kısa içeriklerde de container'ı doldurmasını sağlayabiliriz */
    min-height: 100%; 
    box-sizing: border-box; /* Padding'i min-height'a dahil et */
  }

  /* Başlık Bölümü Stili (Python'a benzer) */
  .email-header {
      background-color: #3B82F6 !important; 
      color: white !important; 
      padding: 16px !important; /* Padding'i azalttık */
      border-radius: 8px 8px 0 0 !important; /* Üst köşeler yuvarlak */
      margin: -20px -20px 20px -20px; /* Margin'i azalttık */
  }
  .email-header h3 {
      margin: 0 !important; 
      color: white !important;
      border-bottom: none !important;
      padding-bottom: 0 !important;
      font-size: 20px !important; /* Font boyutunu azalttık */
   }
  .email-header p {
       color: white !important; 
       margin: 5px 0 0 0 !important;
       font-size: 13px !important;
       opacity: 0.9;
   }

  // Tekil mod için h4 başlıklara daha az margin
  h4 {
    margin: 0 0 10px 0; /* Margin'i azalttık */
    font-size: 16px; /* Font boyutunu azalttık */
    color: #2c2c2c;
    border-bottom: 1px solid #e5e7eb; /* Daha belirgin sınır */
    padding-bottom: 8px; /* Padding'i azalttık */
    font-weight: 600;
  }

  p { margin-bottom: 10px; } /* Margin'i azalttık */
  ul { list-style: none; padding-left: 0; margin: 0 0 10px 0; } /* Margin'i azalttık */
  li {
      position: relative; 
      margin-bottom: 12px; 
      line-height: 1.5;
      span.task-emoji {
           margin-right: 10px; 
           display: inline-block; 
           width: 18px; /* Genişlik ayarı */
           text-align: center; 
      }
  }
  .guidelines-section p::before { content: '⚠️'; margin-right: 8px; }

  /* Metrik Kartları (Python'a benzer, flexbox ile) */
  .metrics-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 10px; /* Kartlar arası boşluğu azalttık */
      margin-bottom: 15px; /* Alt kenar boşluğunu azalttık */
  }
  .metric-card {
      background-color: #ffffff;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      padding: 12px; /* Padding'i azalttık */
      flex: 0 0 31%; 
      box-shadow: 0 1px 3px rgba(0,0,0,0.04);
      text-align: center;
      box-sizing: border-box; /* Padding ve border'ı hesaba kat */
  }
   .metric-card h5 {
       margin: 0 0 5px 0; /* Alt margin'i azalttık */
       font-size: 12px; /* Font boyutunu azalttık */
       color: #6b7280; /* Gri başlık */
       font-weight: 500;
   }
  .metric-value {
       font-size: 18px; /* Font boyutunu azalttık */
       font-weight: 600;
       color: #1f2937;
       word-wrap: break-word; /* Uzun metinler için */
   }
   /* Mobil için metrik kartları */
   @media screen and (max-width: 600px) {
       .metric-card {
           flex: 1 1 calc(50% - 10px); /* Mobilde 2'li sıra */
       }
   }
    @media screen and (max-width: 400px) {
       .metric-card {
           flex: 1 1 100%; /* Çok küçük ekranda tek sıra */
       }
   }

  /* Özel Not Banner */
  .custom-note-banner { /* Stiller aynı */ 
       background-color: #e0f2fe; border: 1px solid #7dd3fc; color: #075985; padding: 15px 20px; margin: 25px 0; border-radius: 6px; display: flex; align-items: center; 
   }
   .custom-note-banner svg { margin-right: 12px; width: 20px; height: 20px; stroke-width: 2.5; }
   .custom-note-banner p { margin: 0; }

  /* İpuçları ve Kurallar Bölümü */
   .tips-section, .guidelines-section {
       background-color: #f9fafb; /* Biraz farklı arkaplan */
       border: 1px solid #f3f4f6;
       border-radius: 8px;
       padding: 20px;
       margin-bottom: 25px;
       font-size: 0.95em;
   }
   .tips-section h4, .guidelines-section h4 { margin-top: 0; }
   .tips-section li { margin-bottom: 10px; }
   .guidelines-section p { margin-bottom: 10px; }
   .tips-section strong, .guidelines-section strong { color: #1f2937; }

  /* Footer */
  .footer { /* Stiller aynı */ 
      background-color: transparent; border: none; margin-top: 35px; padding-top: 20px; border-top: 1px solid #e0e0e0; font-size: 12px; color: #999999; text-align: center; 
  }
  .footer p { margin-bottom: 6px; }
  .footer a { color: #777777; text-decoration: none; }
  .footer a:hover { text-decoration: underline; }
  
  .placeholder-text { color: #a0a0a0; font-style: italic; }

  /* .tip stili güncellendi (daha açık yeşil tonlar) */
  .tip {
      background-color: #ecfdf5 !important; /* Daha açık yeşil (Tailwind green-50) */
      padding: 12px 15px !important;
      border-left: 4px solid #22c55e !important; /* Orta yeşil (Tailwind green-500) */
      margin-bottom: 10px !important;
      color: #065f46 !important; /* Koyu yeşil yazı (Tailwind green-800) */
      font-size: 0.95em;
      border-radius: 0 4px 4px 0; /* Sağ köşeler hafif yuvarlak */
  }
  .tip strong { color: #065f46; font-weight: 600; }

  /* .guideline stili güncellendi (sarı tonlar) */
  .guideline {
      background-color: #fffbeb !important; /* Açık sarı */
      padding: 12px 15px !important;
      border-left: 4px solid #d97706 !important; /* Sarı/Amber */
      margin-bottom: 10px !important;
      color: #7c4a03 !important; /* Koyu sarı/kahve yazı */
      border-radius: 0 4px 4px 0;
  }
  .guideline p { margin-bottom: 5px; font-size: 0.95em; }
  .guideline p:last-child { margin-bottom: 0; }
  .guideline strong { color: #7c4a03; font-weight: 600; }

  /* Section içindeki h4'ün stilini de kontrol et */
  .section h4 {
       color: #000000 !important;
       margin: 0 0 15px 0; /* h4 margin tekrar ayarlandı */
       font-size: 18px;
       border-bottom: 1px solid #e5e7eb;
       padding-bottom: 10px;
       font-weight: 600;
  }
`,Kn=$t(Q)`
  &.ant-btn-primary:not([disabled]):not(.ant-btn-disabled) {
    color: white !important; /* Her zaman beyaz metin rengi */
    background-color: #1890ff !important; /* Ant Design'ın varsayılan mavi tonu */
    border-color: #1890ff !important;
    
    &:hover,
    &:focus,
    &:active {
      color: white !important; /* Hover, focus ve active durumlarında metin rengini beyaz yap */
      background-color: #40a9ff !important; /* Hover durumunda daha açık mavi */
      border-color: #40a9ff !important;
    }
    /* Yüklenme durumunda da metni beyaz yapmaya çalışalım */
    &.ant-btn-loading {
        color: white !important;
        opacity: 0.8; /* Yüklenirken biraz soluk görünebilir */
    }
  }
`,Wn=$t.div`
    padding: 5px 8px;
    cursor: pointer;
    border-radius: 4px;
    margin-bottom: 2px;
    background-color: ${e=>e.isSelected?"#e6f7ff":"transparent"};
    border: 1px solid ${e=>e.isSelected?"#91d5ff":"transparent"};
    
    &:hover {
        background-color: #f0f8ff;
    }
`,Fn=e=>{if(e===void 0||e<0)return"N/A";const t=Math.floor(e/3600),a=Math.floor(e%3600/60);let r="";return t>0&&(r+=`${t} saat `),a>0&&(r+=`${a} dakika`),r.trim()||"0 dakika"},Ya=({isOpen:e,onClose:t,onSubmit:a,publishers:r,mode:i="bulk",selectedWeekRange:s,nextWeekRange:o})=>{const[d,f]=y.useState(""),[u,c]=y.useState(!1),[h,m]=y.useState([]),[g,b]=y.useState(!0),[S,A]=y.useState(""),[k,x]=y.useState(null);y.useEffect(()=>{if(e)if(i==="bulk"&&r.length>0){const $=r.map(j=>j.id);m($),b(!0),x(r[0])}else i==="single"&&r.length===1?(x(r[0]),m([r[0].id])):(x(null),m([]),b(!1));else f(""),m([]),b(i==="bulk"),x(null),c(!1)},[e,r,i]);const E=y.useCallback($=>{if(i!=="bulk")return;const j=$.target.checked;b(j);const K=j?r.map(se=>se.id):[];m(K),x(j&&r.length>0?r[0]:null)},[r,i]),D=y.useCallback(($,j)=>{if(i!=="bulk")return;let K;j?K=[...h,$]:K=h.filter(se=>se!==$),m(K),b(K.length===r.length&&r.length>0)},[h,r,i]),z=y.useCallback($=>{i==="bulk"&&x($)},[i]),_=async()=>{const $=i==="single"&&r.length===1?[r[0].id]:h;if($.length===0){P.error("Lütfen en az bir alıcı seçin");return}const j=i==="single"&&k?`Haftalık Rapor ve Görevleriniz (${k.username})`:"Haftalık Rapor ve Görevleriniz";c(!0);try{await a({subject:j,customNote:d,publisherIds:$}),c(!1),f(""),t(),P.success(`${$.length} kişiye e-posta gönderime isteği başarıyla alındı (simülasyon).`)}catch(K){c(!1),P.error("E-postaları gönderme başarısız oldu")}};y.useEffect(()=>{A((j=>{var K,se,je,Me,Ae,ue,ve,$e,W;const he=(j==null?void 0:j.username)||"Değerli Yayıncımız",fe=new Date().getFullYear();let F=j==null?void 0:j.hafta_baslangici,oe=j==null?void 0:j.hafta_bitisi;if(F&&!oe){const U=new Date(F+"T00:00:00"),ce=new Date(U);ce.setDate(U.getDate()+7),oe=ce.toISOString().slice(0,10)}let re="";if(i==="bulk"&&s)re=s;else if(F&&oe)try{const U=new Date(F+"T00:00:00"),ce=new Date(oe+"T00:00:00");!isNaN(U.getTime())&&!isNaN(ce.getTime())?re=`${U.toLocaleDateString("tr-TR")} - ${ce.toLocaleDateString("tr-TR")}`:re="Tarih Bilgisi Yok"}catch(U){re="Tarih Bilgisi Yok"}else re="Tarih Bilgisi Yok";const be=`
        <div class="email-header">
            <h3 style="margin-bottom: 2px;">Haftalık Performans Raporu</h3>
            <div style="font-size: 13px; color: #e0e7ef; margin-top: 0; font-weight: normal;">Kullanıcı adı: ${(j==null?void 0:j.username)||"N/A"}</div>
        </div>`,pe=`<p>Merhaba ${he},</p><p>Geçtiğimiz haftanın performans özetini ve yeni haftanın önerilen görevlerini aşağıda bulabilirsin.</p>`,we=d?`<div class="custom-note-banner">
                 <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-info"><circle cx="12" cy="12" r="10"/><path d="M12 16v-4"/><path d="M12 8h.01"/></svg>
                 <p>${d.replace(/\n/g,"<br/>")}</p>
               </div>`:"";let ne="<h4>📊 Geçen Haftanın Özeti</h4>";j?ne+=`
              <div class="metrics-grid">
                <div class="metric-card"><h5>📅 Aktif Gün</h5><div class="metric-value">${(K=j.live_days)!=null?K:"N/A"}</div></div>
                <div class="metric-card"><h5>⏱️ Toplam Süre</h5><div class="metric-value">${Fn(j.total_duration)}</div></div>
                <div class="metric-card"><h5>💎 Toplam Elmas</h5><div class="metric-value">${(je=(se=j.total_diamonds)==null?void 0:se.toLocaleString())!=null?je:"N/A"}</div></div>
                <div class="metric-card"><h5>👥 Yeni Takipçi</h5><div class="metric-value">${(Ae=(Me=j.total_followers)==null?void 0:Me.toLocaleString())!=null?Ae:"N/A"}</div></div>
                <div class="metric-card"><h5>⭐ Yeni Abone</h5><div class="metric-value">${(ve=(ue=j.total_subscribers)==null?void 0:ue.toLocaleString())!=null?ve:"N/A"}</div></div>
                <div class="metric-card"><h5>⚔️ PK Maçı</h5><div class="metric-value">${(W=($e=j.total_matches)==null?void 0:$e.toLocaleString())!=null?W:"N/A"}</div></div>
              </div>
            `:ne+='<div class="section-box"><p class="placeholder-text">[Genel metrik özeti veya tek bir yayıncı seçildiğinde detaylar burada görünecek...]</p></div>';let B="<h4>Gelecek Hafta Görevleri</h4>";const me=j==null?void 0:j.next_week_tasks,le={stream_frequency:'<svg width="20" height="20" fill="none" stroke="#3B82F6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24" style="margin-right:7px;vertical-align:middle"><rect x="3" y="4" width="18" height="18" rx="2"/><path d="M16 2v4M8 2v4M3 10h18"/></svg>',stream_duration:'<svg width="20" height="20" fill="none" stroke="#06b6d4" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24" style="margin-right:7px;vertical-align:middle"><circle cx="12" cy="12" r="10"/><path d="M12 6v6l4 2"/></svg>',follower_gain:'<svg width="20" height="20" fill="none" stroke="#10b981" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24" style="margin-right:7px;vertical-align:middle"><path d="M17 21v-2a4 4 0 0 0-4-4H7a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M23 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/></svg>',diamond_gain:'<svg width="20" height="20" fill="none" stroke="#06b6d4" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24" style="margin-right:7px;vertical-align:middle"><polygon points="6 3 18 3 22 9 12 21 2 9 6 3"/></svg>',subscriber_gain:'<svg width="20" height="20" fill="none" stroke="#f59e42" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24" style="margin-right:7px;vertical-align:middle"><polygon points="12 2 15 8.5 22 9.3 17 14.1 18.2 21 12 17.8 5.8 21 7 14.1 2 9.3 9 8.5 12 2"/></svg>',pk_battle:'<svg width="20" height="20" fill="none" stroke="#f59e42" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24" style="margin-right:7px;vertical-align:middle"><path d="M14 9l6 6M20 9l-6 6"/><path d="M9 5l-7 7 7 7"/></svg>',content_variety:'<svg width="20" height="20" fill="none" stroke="#a21caf" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24" style="margin-right:7px;vertical-align:middle"><circle cx="12" cy="12" r="10"/><path d="M8 15s1.5-2 4-2 4 2 4 2"/><path d="M9 9h.01M15 9h.01"/></svg>',consistent_timing:'<svg width="20" height="20" fill="none" stroke="#64748b" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24" style="margin-right:7px;vertical-align:middle"><circle cx="12" cy="12" r="10"/><path d="M12 6v6l4 2"/></svg>',default:'<svg width="20" height="20" fill="none" stroke="#64748b" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24" style="margin-right:7px;vertical-align:middle"><circle cx="12" cy="12" r="10"/><path d="M12 8v4l3 3"/></svg>'};me&&me.length>0?(B+='<ol style="margin: 0 0 10px 0; padding: 0; color: #222; font-size: 1.08em; line-height: 1.7;">',me.forEach(U=>{const ce=U.task_type&&Object.prototype.hasOwnProperty.call(le,U.task_type)?U.task_type:"default";let Dt=le[ce];B+=`<li style="margin-bottom: 7px; font-family: 'Segoe UI', 'Arial', sans-serif; display: flex; align-items: center;">${Dt}<span>${U.gorev_onerisi||"Görev"}</span></li>`}),B+="</ol>"):B+='<div style="background: #f1f5f9; border: 1px solid #e0e7ef; border-radius: 6px; padding: 14px; color: #64748b; font-size: 14px; margin-bottom: 10px;">Bu yayıncı için gelecek hafta görevi atanmamış veya bulunamadı.</div>';const H=`
            <div class="section">
                <h4>İyileştirme Önerileri</h4>
                <div class="tip">
                    <strong>Yayın Süresi:</strong> Yayınlarınızı her seferinde en az 60 dakika sürdürmeniz TikTok algoritması tarafından daha fazla önerilmenizi sağlar.
                </div>
                <div class="tip">
                    <strong>Etkileşim:</strong> Yayın sırasında izleyicilerle aktif etkileşim, elmas kazanımınızı önemli ölçüde artırır.
                </div>
                <div class="tip">
                    <strong>Düzenlilik:</strong> Her gün aynı saatlerde yayın yapmak takipçi sayınızı artırır ve algoritma tarafından daha fazla önerilmenizi sağlar.
                </div>
             </div>
        `,Et=`
            <div class="section">
                <h4>Topluluk Kuralları Hatırlatması</h4>
                <div class="guideline">
                    <p><strong>Topluluk Etiketleri:</strong> Herhangi bir topluluk etiketinin önüne negatif bir ön ek kullanılması durumunda TikTok hesabınıza ban, ihlal veya kısıtlama verebilir ve bu türdeki ban ve ihlaller ajans tarafından dahi kaldırılamaz.</p>
                </div>
                <div class="guideline">
                    <p><strong>Dil Kullanımı:</strong> Şaka amaçlı bile olsa argo veya hakaret içeren ifadelerden uzak durun.</p>
                    <p>TikTok, bu tür içerikleri tespit ettiğinde bildirim göndermeden hesabınızı arka planda puanlayan bir sisteme sahiptir.</p>
                    <p>Bu puanlama düştüğünde etkileşimleriniz ciddi oranda azalır ve TikTok algoritması sizi önermemeye başlar.</p>
                </div>
            </div>
        `,Yt='<p style="margin-top: 30px;">',Z=`
        <div class='footer'>
             <p>Bu e-posta Tuber Ajans tarafından otomatik olarak gönderilmiştir.</p>
             <p>&copy; ${fe} Tuber Ajans. Tüm hakları saklıdır.</p>
            <p>
                <a href='#' style='color: #777777; text-decoration: none;'>Gizlilik Politikası</a> |
                <a href='#' style='color: #777777; text-decoration: none;'>Kullanım Koşulları</a> |
                <a href='#' style='color: #777777; text-decoration: none;'>Abonelikten Çık</a>
            </p>
        </div>`;return`
         <div class="email-body-wrapper">
            ${be} 
            ${pe}
            ${we} 
            ${ne}
            ${B}
            ${H} 
            ${Et} 
            ${Yt}
            ${Z}
        </div>
        `})(k))},[d,k,i,s,o]);const v=i==="single"?"E-posta Gönder":"Toplu E-posta Gönder",ee=i==="single"?800:900;return n.jsxs(Ce,{title:n.jsxs(n.Fragment,{children:[n.jsx(ht,{style:{marginRight:8}})," ",v]}),open:e,onCancel:t,width:ee,footer:[n.jsx(Q,{onClick:t,disabled:u,children:"İptal"},"cancel"),n.jsx(Kn,{type:"primary",loading:u,onClick:_,disabled:u||i==="bulk"&&h.length===0,children:"Gönder"},"send")],bodyStyle:{padding:"16px",maxHeight:i==="single"?"700px":"calc(100vh - 200px)",overflowY:i==="single"?"visible":"auto"},children:[i==="single"&&k&&n.jsx(ft,{message:n.jsxs("strong",{children:["Alıcı: ",k.isim_soyisim||k.username]}),description:n.jsxs(Ln,{type:"secondary",children:["E-posta: ",k.mail||"Belirtilmemiş"]}),type:"info",showIcon:!0,icon:n.jsx(ht,{}),style:{marginBottom:"20px"}}),n.jsxs(ke,{gutter:24,children:[i==="bulk"&&n.jsxs(N,{span:10,children:[n.jsxs("div",{style:{marginBottom:"16px"},children:[n.jsx("label",{style:{display:"block",marginBottom:"8px",fontWeight:"bold"},children:"Alıcılar (Önizleme için Tıkla):"}),n.jsxs("div",{style:{height:"200px",overflowY:"auto",border:"1px solid #d9d9d9",borderRadius:"4px",padding:"8px",background:"#fff"},children:[n.jsxs(ca,{checked:g,onChange:E,style:{marginBottom:"8px",fontWeight:"bold"},disabled:r.length===0,children:["Tümünü Seç (",r.length," Yayıncı)"]}),n.jsx(vr,{style:{margin:"8px 0"}}),r.map($=>n.jsxs(Wn,{onClick:()=>z($),isSelected:(k==null?void 0:k.id)===$.id,children:[n.jsx(ca,{style:{marginRight:"8px"},checked:h.includes($.id),onChange:j=>D($.id,j.target.checked),onClick:j=>j.stopPropagation()}),$.isim_soyisim||$.username,n.jsxs("span",{style:{fontSize:"0.85em",color:"#888"},children:["(",$.mail||"E-posta yok",")"]})]},$.id)),r.length===0&&n.jsx("span",{className:"placeholder-text",children:"Yüklenecek yayıncı yok."})]})]}),n.jsxs("p",{style:{marginBottom:"16px",fontWeight:"500"},children:["Seçilen Alıcı Sayısı: ",h.length]}),n.jsxs("div",{children:[n.jsx("label",{style:{display:"block",marginBottom:"4px"},children:"Özel Notlar (Opsiyonel):"}),n.jsx(Ea,{value:d,onChange:$=>f($.target.value),placeholder:"Otomatik mesaja eklemek istediğiniz özel notları buraya yazın...",rows:6})]})]}),i==="single"&&n.jsx(N,{span:4,children:n.jsxs("div",{children:[n.jsx("label",{style:{display:"block",marginBottom:"4px"},children:"Özel Notlar (Opsiyonel):"}),n.jsx(Ea,{value:d,onChange:$=>f($.target.value),placeholder:"Otomatik mesaja eklemek istediğiniz özel notları buraya yazın...",rows:5})]})}),n.jsxs(N,{span:i==="single"?20:14,children:[n.jsx("label",{style:{display:"block",marginBottom:"8px",fontWeight:"bold"},children:"E-posta Önizleme:"}),k?n.jsx(Gn,{mode:i,children:n.jsx("div",{dangerouslySetInnerHTML:{__html:S}})}):n.jsx(ft,{message:"Önizleme için bir yayıncı seçin (veya veri yükleniyor).",type:"warning",showIcon:!0})]})]})]})},{Text:te,Paragraph:Da}=kt,Hn=({isOpen:e,onClose:t,onUploadSuccess:a})=>{const[r,i]=y.useState([]),[s,o]=y.useState(!1),d=async()=>{var u,c,h,m;const g=(u=r[0])==null?void 0:u.originFileObj;if(!g){P.error("Lütfen bir dosya seçin.");return}const b=new FormData;b.append("file",g),o(!0);const S=P.loading("Dosya yükleniyor ve işleniyor...",0);try{const A=await yr.post("/X/api/upload-weekly-data",b,{headers:{"Content-Type":"multipart/form-data"}});if(P.destroy(S),(c=A.data)!=null&&c.success){i([]);const k=A.data.message||"Dosya başarıyla yüklendi ve işlendi.";P.success(k),A.data.errors&&A.data.errors.length>0&&P.warning(`${A.data.errors.length} satırda hata bulundu. Detaylar için konsolu kontrol edin.`),t(),a&&a()}else P.error(A.data.message||"Dosya işlenirken bir hata oluştu.")}catch(A){P.destroy(S);let k="Dosya yüklenirken bir sunucu hatası oluştu.";(m=(h=A.response)==null?void 0:h.data)!=null&&m.message?k=A.response.data.message:A.message&&(k=A.message),P.error(k)}finally{o(!1)}},f={onRemove:u=>{i(c=>{const h=c.findIndex(g=>g.uid===u.uid);if(h===-1)return c;const m=c.slice();return m.splice(h,1),m})},beforeUpload:u=>(i([u]),!1),fileList:r,accept:".xlsx, .xls",maxCount:1};return n.jsxs(Ce,{title:n.jsxs(n.Fragment,{children:[n.jsx(ua,{style:{marginRight:8}})," Haftalık Performans Verisi Yükle"]}),open:e,onCancel:t,footer:[n.jsx(Q,{onClick:t,disabled:s,children:"İptal"},"cancel"),n.jsx(Q,{type:"primary",onClick:d,loading:s,disabled:r.length===0||s,children:s?"Yükleniyor...":"Yükle ve İşle"},"upload")],width:600,children:[n.jsx(Da,{children:"Bu araç, TikTok Live Backstage'den dışa aktardığınız haftalık yayıncı performans verilerini içeren Excel dosyasını sisteme yüklemenizi sağlar."}),n.jsx(ft,{message:"Dosya Formatı ve Gereksinimler",description:n.jsx(kt,{children:n.jsxs("ul",{children:[n.jsxs("li",{children:["Lütfen ",n.jsx(te,{strong:!0,children:".xlsx"})," veya ",n.jsx(te,{strong:!0,children:".xls"})," formatında bir dosya yükleyin."]}),n.jsx("li",{children:"Dosyanın ilk satırı başlıkları içermelidir."}),n.jsxs("li",{children:["Gerekli sütunlar: ",n.jsx(te,{code:!0,children:"Veri Dönemi"}),", ",n.jsx(te,{code:!0,children:"İçerik Üreticisinin Kullanıcı Adı"}),"."]}),n.jsxs("li",{children:["Diğer okunacak sütunlar: ",n.jsx(te,{code:!0,children:"Elmaslar"}),", ",n.jsx(te,{code:!0,children:"Canlı Yayın Süresi"}),", ",n.jsx(te,{code:!0,children:"Geçerli CANLI Yayın yapılan gün"}),", ",n.jsx(te,{code:!0,children:"Maçlar"}),", ",n.jsx(te,{code:!0,children:"Aboneler"}),", ",n.jsx(te,{code:!0,children:"Yeni Takipçiler"})," (varsa)."]}),n.jsxs("li",{children:["'Veri Dönemi' ",n.jsx(te,{strong:!0,children:"GG.AA.YYYY - GG.AA.YYYY"})," formatında olmalıdır."]})]})}),type:"info",showIcon:!0,icon:n.jsx(_r,{}),style:{marginBottom:20}}),n.jsx(Da,{strong:!0,children:"Yüklenecek Dosya:"}),n.jsxs(br,{...f,children:[n.jsx(Q,{icon:n.jsx(ua,{}),disabled:s||r.length>0,children:r.length>0?"Dosya Seçildi":"Excel Dosyası Seç"}),r.length>0&&n.jsxs(te,{style:{marginLeft:10},type:"secondary",children:["(",r[0].name,")"]})]})]})},Un=({form:e,onClose:t,onSubmit:a,initialData:r,publishers:i})=>{y.useEffect(()=>{r?e.setFieldsValue({...r,bitis_tarihi:r.bitis_tarihi?C(r.bitis_tarihi):null,atanan_kullanici_id:r.atanan_kullanici_id||null}):e.resetFields()},[r,e]);const s=o=>{const d={...r,...o,baslangic_tarihi:o.baslangic_tarihi?C(o.baslangic_tarihi).toISOString():new Date().toISOString(),bitis_tarihi:o.bitis_tarihi?C(o.bitis_tarihi).toISOString():null};a(d)};return n.jsxs(Se,{form:e,layout:"vertical",onFinish:s,children:[n.jsxs(ke,{gutter:16,children:[n.jsx(N,{span:12,children:n.jsx(Se.Item,{name:"gorev_onerisi",label:"Görev Başlığı",rules:[{required:!0,message:"Lütfen görev başlığı girin"}],children:n.jsx(pt,{placeholder:"Görev başlığı"})})}),n.jsx(N,{span:12,children:n.jsx(Se.Item,{name:"atanan_kullanici_id",label:"Atanan Yayıncı",children:n.jsx(ge,{showSearch:!0,allowClear:!0,placeholder:"Yayıncı Seçin",filterOption:(o,d)=>{var f;return((f=d==null?void 0:d.label)!=null?f:"").toLowerCase().includes(o.toLowerCase())},options:i.map(o=>({value:o.id,label:`${o.isim_soyisim} (@${o.username})`}))})})})]}),n.jsx(Se.Item,{name:"aciklama",label:"Açıklama",children:n.jsx(pt.TextArea,{rows:3,placeholder:"Görev açıklaması (opsiyonel)"})}),n.jsxs(ke,{gutter:16,children:[n.jsx(N,{span:12,children:n.jsx(Se.Item,{name:"oncelik",label:"Öncelik",initialValue:"Orta",children:n.jsxs(ge,{placeholder:"Öncelik Seçin",children:[n.jsx(ge.Option,{value:"Düşük",children:"Düşük"}),n.jsx(ge.Option,{value:"Orta",children:"Orta"}),n.jsx(ge.Option,{value:"Yüksek",children:"Yüksek"})]})})}),n.jsx(N,{span:12,children:n.jsx(Se.Item,{name:"bitis_tarihi",label:"Bitiş Tarihi",children:n.jsx(Ma,{style:{width:"100%"},format:"DD.MM.YYYY",placeholder:"Tarih seçin"})})})]}),n.jsx(Se.Item,{name:"gorev_zorlugu",label:"Görev Zorluğu",initialValue:"Orta",children:n.jsxs(ge,{placeholder:"Zorluk Seçin",children:[n.jsx(ge.Option,{value:"Kolay",children:"Kolay"}),n.jsx(ge.Option,{value:"Orta",children:"Orta"}),n.jsx(ge.Option,{value:"Zor",children:"Zor"})]})})]})};var dt={exports:{}},Vn=dt.exports,za;function Xn(){return za||(za=1,function(e,t){(function(a,r){e.exports=r()})(Vn,function(){var a="day";return function(r,i,s){var o=function(u){return u.add(4-u.isoWeekday(),a)},d=i.prototype;d.isoWeekYear=function(){return o(this).year()},d.isoWeek=function(u){if(!this.$utils().u(u))return this.add(7*(u-this.isoWeek()),a);var c,h,m,g,b=o(this),S=(c=this.isoWeekYear(),h=this.$u,m=(h?s.utc:s)().year(c).startOf("year"),g=4-m.isoWeekday(),m.isoWeekday()>4&&(g+=7),m.add(g,a));return b.diff(S,"week")+1},d.isoWeekday=function(u){return this.$utils().u(u)?this.day()||7:this.day(this.day()%7?u:u-7)};var f=d.startOf;d.startOf=function(u,c){var h=this.$utils(),m=!!h.u(c)||c;return h.p(u)==="isoweek"?m?this.date(this.date()-(this.isoWeekday()-1)).startOf("day"):this.date(this.date()-1-(this.isoWeekday()-1)+7).endOf("day"):f.bind(this)(u,c)}}})}(dt)),dt.exports}var qn=Xn();const Zn=gr(qn);C.locale("tr");Ut.locale("tr");Ut.extend(Zn);const ut={1:"Ocak",2:"Şubat",3:"Mart",4:"Nisan",5:"Mayıs",6:"Haziran",7:"Temmuz",8:"Ağustos",9:"Eylül",10:"Ekim",11:"Kasım",12:"Aralık"},Jn=e=>{const t=C(e),a=C(t).day(1);t.day()===0&&a.add(1,"day");const r=C(a).add(7,"days"),i=a.format("D"),s=r.format("D");if(a.format("M")===r.format("M")){const o=ut[a.format("M")];return`${i}-${s} ${o}`}else{const o=ut[a.format("M")],d=ut[r.format("M")];return`${i} ${o} - ${s} ${d}`}},Qn=e=>{switch(e==null?void 0:e.toLowerCase()){case"kolay":case"low":return"green";case"orta":case"medium":return"blue";case"zor":case"high":return"red";default:return"default"}},ei=e=>{if(!e)return"0 dk";const t=Math.floor(e/3600),a=Math.floor(e%3600/60);return t>0?`${t} sa ${a} dk`:`${a} dk`},ti=e=>{const t=Math.floor(e/3600),a=Math.floor(e%3600/60),r=e%60;return`${t}s ${a}dk ${r}sn`},ai=e=>{const t=e.format("YYYY-MM-DD"),a=C(e).add(7,"days").format("YYYY-MM-DD");return{start:t,end:a}},ri=async(e,t=4)=>{try{const r=await(await fetch(`${X.X_SITE_BASE_URL}/weekly_archive.php?username=${e}&count=${t}`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem(ye.TOKEN_KEY)}`}})).json();return r.success&&Array.isArray(r.data)?{data:r.data}:{data:[]}}catch(a){return{data:[]}}};function ni(e){if(!e)return"";const t=C.isMoment(e)?e:C(e.format("YYYY-MM-DD")),a=t.clone(),r=t.clone().add(7,"days"),i=["Ocak","Şubat","Mart","Nisan","Mayıs","Haziran","Temmuz","Ağustos","Eylül","Ekim","Kasım","Aralık"],s=`${a.date()} ${i[a.month()]}`,o=`${r.date()} ${i[r.month()]}`;return`${s} - ${o}`}function ii(e){if(!e)return"";const t=C.isMoment(e)?e:C(e.format("YYYY-MM-DD")),a=t.clone().add(7,"days"),r=t.clone().add(13,"days"),i=["Ocak","Şubat","Mart","Nisan","Mayıs","Haziran","Temmuz","Ağustos","Eylül","Ekim","Kasım","Aralık"],s=`${a.date()} ${i[a.month()]}`,o=`${r.date()} ${i[r.month()]}`;return`${s} - ${o}`}function si(){var e,t;const[a,r]=y.useState([]),[i,s]=y.useState(!1),[o,d]=y.useState(!1),[f,u]=y.useState(!1),[c,h]=y.useState(!1),[m,g]=y.useState([]),[b,S]=y.useState(null),[A,k]=y.useState(!1),[x,E]=y.useState(null),[D,z]=y.useState(!1),[_]=y.useState([]),[v]=Se.useForm(),[ee,$]=y.useState(!0),[j,K]=y.useState(!1),[se,je]=y.useState([]),[Me,Ae]=y.useState(!1),[ue,ve]=y.useState(""),[$e,W]=y.useState(!1),[he,fe]=y.useState(null),[F,oe]=y.useState(!1),[re,be]=y.useState([]),[pe,we]=y.useState(10),[ne,B]=y.useState(!1),[me,le]=y.useState(!1),H=l=>{const p=C(l).day(1);return l.day()===0?C(l).add(1,"day"):p},Et=()=>{const l=C();return H(l).subtract(7,"days")},Yt=()=>C().clone().day(1).subtract(1,"week"),[Z,U]=y.useState(Yt),ce=l=>{const p=C(Z.format("YYYY-MM-DD")),M=l==="prev"?p.subtract(7,"days"):p.add(7,"days");U(H(M)),g([]),Xe(H(M).format("YYYY-MM-DD"))},Dt=l=>{if(!l)return;g([]);const p=C(l.format("YYYY-MM-DD")),M=H(p);U(M),Xe(M.format("YYYY-MM-DD"))},rr=async()=>{s(!0);try{const{start:l,end:p}=ai(C(Z.format("YYYY-MM-DD"))),M=await fetch(`${X.X_SITE_BASE_URL}${X.ENDPOINTS.WEEKLY_TASKS}?start_date=${l}&end_date=${p}`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem(ye.TOKEN_KEY)}`},cache:"no-store"});if(!M.ok)throw new Error(`Error fetching tasks: ${M.statusText}`);const I=await M.json();let L=[];if(I.success&&Array.isArray(I.data)?(L=I.data,I.week_info&&S(I.week_info)):Array.isArray(I)?L=I:L=[],L.length>0){const Ee=L.map(V=>({...V,key:V.id.toString(),gorev_onerisi:V.gorev_onerisi,gorev_zorlugu:V.gorev_zorlugu,tamamlandi:Number(V.tamamlandi||0),puan:Number(V.puan||0),ilerleme:Number(V.ilerleme||0),baslangic_tarihi:V.hafta_baslangici||V.baslangic_tarihi,bitis_tarihi:V.hafta_bitisi||V.bitis_tarihi,durum:V.durum||"beklemede"}));r(Ee)}else r([])}catch(l){P.error("Görevler yüklenirken bir hata oluştu!"),r([])}finally{s(!1)}};y.useEffect(()=>{const l=Et();U(l),Xe(l.format("YYYY-MM-DD"))},[]);const Qt=l=>l?a.filter(p=>p.kullanici_adi===l.kullanici_adi):[],nr=l=>{switch(l==null?void 0:l.toLowerCase()){case"tamamlandı":case"completed":return"success";case"devam ediyor":case"in progress":return"processing";case"beklemede":case"pending":return"warning";case"iptal edildi":case"cancelled":return"error";default:return"default"}},ea=[{title:"Kullanıcı Adı",dataIndex:"kullanici_adi",key:"kullanici_adi",fixed:"left",width:150,sorter:(l,p)=>l.kullanici_adi.localeCompare(p.kullanici_adi)},{title:"Canlı Yayın Günü",dataIndex:"canli_yayin_gunu",key:"canli_yayin_gunu",width:100,align:"center",sorter:(l,p)=>(l.canli_yayin_gunu||0)-(p.canli_yayin_gunu||0)},{title:"Yayın Süresi",dataIndex:"yayin_suresi",key:"yayin_suresi",width:150,align:"center",render:l=>ei(l),sorter:(l,p)=>(l.yayin_suresi||0)-(p.yayin_suresi||0)},{title:"Elmaslar",dataIndex:"elmaslar",key:"elmaslar",width:100,align:"center",render:l=>(l||0).toLocaleString(),sorter:(l,p)=>(l.elmaslar||0)-(p.elmaslar||0)},{title:"Takipçiler",dataIndex:"yeni_takipciler",key:"yeni_takipciler",width:100,align:"center",render:l=>(l||0).toLocaleString(),sorter:(l,p)=>(l.yeni_takipciler||0)-(p.yeni_takipciler||0)},{title:"Aboneler",dataIndex:"aboneler",key:"aboneler",width:100,align:"center",render:l=>(l||0).toLocaleString(),sorter:(l,p)=>(l.aboneler||0)-(p.aboneler||0)},{title:"Maçlar",dataIndex:"maclar",key:"maclar",width:100,align:"center",render:l=>(l||0).toLocaleString(),sorter:(l,p)=>(l.maclar||0)-(p.maclar||0)},{title:"Görevler",key:"actions",width:120,align:"center",render:(l,p)=>n.jsx(Q,{type:"default",size:"middle",icon:n.jsx($r,{size:16,className:"mr-1"}),onClick:()=>ir(p),style:{width:"100%",display:"flex",justifyContent:"center",alignItems:"center",backgroundColor:"#1677ff",color:"white",border:"none"},className:"hover:bg-blue-700 transition-colors",children:"Detaylar"})}],ir=async l=>{E(l),K(!0),Ae(!0);try{const p=C(l.hafta_baslangici),M=p.clone().add(7,"days"),I=p.format("YYYY-MM-DD"),L=M.format("YYYY-MM-DD"),Ee=`${X.X_SITE_BASE_URL}/tasks.php?kullanici_adi=${encodeURIComponent(l.kullanici_adi)}&start_date=${I}&end_date=${L}`,zt=await(await fetch(Ee,{headers:{Authorization:`Bearer ${localStorage.getItem(ye.TOKEN_KEY)}`}})).json(),qe=Array.isArray(zt)?zt.slice(0,6):[];je(qe),l.hafta_bitisi=M.format("YYYY-MM-DD")}catch(p){je([])}finally{Ae(!1)}},sr=()=>n.jsx(Ce,{open:j,onCancel:()=>K(!1),footer:null,title:x?`${x.kullanici_adi} - ${C(x.hafta_baslangici).format("DD/MM/YYYY")} - ${C(x.hafta_baslangici).add(7,"days").format("DD/MM/YYYY")}`:"Görev Detayları",width:700,children:Me?n.jsx(Ke,{}):n.jsx(Tt,{dataSource:se,rowKey:"id",pagination:!1,columns:[{title:"Görev",dataIndex:"gorev_onerisi",key:"gorev_onerisi"},{title:"Zorluk",dataIndex:"gorev_zorlugu",key:"gorev_zorlugu",render:l=>n.jsx(da,{color:Qn(l),children:l})},{title:"Durum",dataIndex:"durum",key:"durum",render:l=>n.jsx(da,{color:nr(l),children:l})},{title:"Puan",dataIndex:"puan",key:"puan"}],size:"small"})}),or=async()=>{le(!0);try{const l=Z.format("YYYY-MM-DD"),p=Z.clone().add(7,"days").format("YYYY-MM-DD"),I=await(await fetch(`${X.X_SITE_BASE_URL}/auto-assign-tasks.php`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem(ye.TOKEN_KEY)}`},body:JSON.stringify({start_date:l,end_date:p})})).json();I.success?(P.success(`${I.success_count||0} kullanıcıya görev atandı!`),rr(),Xe(l)):P.error(I.message||"Görev atama başarısız!")}catch(l){P.error("Görev atama sırasında hata oluştu!")}finally{le(!1),B(!1)}},lr=async()=>{try{const l=await fetch(`${X.X_SITE_BASE_URL}/fetch_weekly_data.php`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem(ye.TOKEN_KEY)}`},body:JSON.stringify({startDate:Z.format("YYYY-MM-DD")})});if(!l.ok)throw new Error("Haftalık veriler yüklenemedi");const p=await l.json();if(p.success)return P.success("Haftalık veri çekme işlemi başlatıldı!"),p.data;throw new Error(p.message||"Haftalık veriler yüklenemedi")}catch(l){throw P.error("Haftalık veri çekme hatası: "+((l==null?void 0:l.message)||l)),l}};y.useEffect(()=>{window.location.pathname==="/tasks"&&(document.title="Görevler | X Paneli")},[]);const Xe=async l=>{g([]),k(!0);try{const p=`${X.ENDPOINTS.WEEKLY_ARCHIVE}?start_date=${l}`,M=`${X.X_SITE_BASE_URL}/${p.replace(/^\//,"")}`,L=await(await fetch(M,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem(ye.TOKEN_KEY)}`}})).json();L.success&&Array.isArray(L.data)?(g(L.data),S(L.week_info||null)):(g([]),S(null))}catch(p){g([]),S(null)}finally{k(!1)}},ta=y.useMemo(()=>{let l=m;return ue&&(l=l.filter(p=>{var M;return(M=p.kullanici_adi)==null?void 0:M.toLowerCase().includes(ue.toLowerCase())})),[...l].sort((p,M)=>(M.elmaslar||0)-(p.elmaslar||0))},[m,ue]),cr=async()=>{oe(!0);try{const l=Z.format("YYYY-MM-DD"),p=`${X.X_SITE_BASE_URL}/${X.ENDPOINTS.WEEKLY_ARCHIVE.replace(/^\//,"")}?start_date=${l}`,I=await(await fetch(p,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem(ye.TOKEN_KEY)}`}})).json(),L=Array.isArray(I.data)?I.data:[],Ee=C(l).add(7,"days").format("YYYY-MM-DD"),V=`${X.X_SITE_BASE_URL}/${X.ENDPOINTS.WEEKLY_TASKS.replace(/^\//,"")}?start_date=${l}&end_date=${Ee}`,qe=await(await fetch(V,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem(ye.TOKEN_KEY)}`}})).json(),dr=Array.isArray(qe)?qe:[],ur=L.map(G=>G.kullanici_adi).filter(Boolean),hr=`${X.X_SITE_BASE_URL}/${X.ENDPOINTS.PUBLISHERS.replace(/^\//,"")}?usernames=${encodeURIComponent(ur.join(","))}`,aa=await(await fetch(hr,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem(ye.TOKEN_KEY)}`}})).json(),fr=Array.isArray(aa.data)?aa.data:[],pr=L.map(G=>{var ra,na,ia,sa,oa;const mr=dr.filter(Mt=>Mt.kullanici_adi===G.kullanici_adi),la=fr.find(Mt=>Mt.username===G.kullanici_adi)||{};return{id:G.id||G.kullanici_adi,username:G.kullanici_adi,isim_soyisim:la.isim_soyisim||G.kullanici_adi,mail:la.mail||"",live_days:G.canli_yayin_gunu,total_duration:(ra=G.yayin_suresi)!=null?ra:void 0,total_diamonds:(na=G.elmaslar)!=null?na:void 0,total_followers:(ia=G.yeni_takipciler)!=null?ia:void 0,total_subscribers:(sa=G.aboneler)!=null?sa:void 0,total_matches:(oa=G.maclar)!=null?oa:void 0,hafta_baslangici:G.hafta_baslangici,hafta_bitisi:G.hafta_bitisi,next_week_tasks:mr}});be(pr),u(!0)}catch(l){P.error("Toplu mail verileri yüklenemedi")}finally{oe(!1)}};return n.jsxs("div",{className:"w-full m-0 p-0",children:[window.location.pathname==="/tasks"&&!1,n.jsx(nt,{className:"w-full m-0",bodyStyle:{padding:"12px 12px 0 12px"},style:{marginBottom:0},children:n.jsxs(ke,{gutter:[16,16],align:"middle",style:{marginBottom:8},children:[n.jsx(N,{xs:24,md:14,style:{display:"flex",alignItems:"center"},children:n.jsxs("div",{style:{display:"flex",alignItems:"center",width:"100%"},children:[n.jsx("div",{style:{marginRight:12},children:n.jsxs("div",{className:"flex border border-gray-300 rounded-md overflow-hidden",children:[n.jsx(Q,{icon:n.jsx(zr,{size:16}),onClick:()=>ce("prev"),style:{height:"32px",border:"none",borderRight:"1px solid #e8e8e8",borderRadius:0,padding:"0 10px"},className:"flex items-center justify-center"}),n.jsx(Ma,{picker:"week",onChange:Dt,value:Ut(Z.format("YYYY-MM-DD")),style:{width:"130px",height:"32px",border:"none",borderRadius:0},format:()=>{const l=Z.clone().day(1),p=l.clone().add(7,"days"),M=l.format("D"),I=p.format("D"),L=l.format("M"),Ee=ut[L];return`${M}-${I} ${Ee}`}}),n.jsx(Q,{icon:n.jsx(Tr,{size:16}),onClick:()=>ce("next"),style:{height:"32px",border:"none",borderLeft:"1px solid #e8e8e8",borderRadius:0,padding:"0 10px"},className:"flex items-center justify-center"})]})}),n.jsx("div",{style:{width:160},children:n.jsx(pt.Search,{placeholder:"Kullanıcı Adı Ara",allowClear:!0,value:ue,onChange:l=>ve(l.target.value),style:{height:32}})})]})}),n.jsxs(N,{xs:24,md:10,style:{display:"flex",justifyContent:"flex-end",alignItems:"center"},children:[n.jsx(Q,{icon:n.jsx(xr,{style:{fontSize:"16px"}}),onClick:()=>B(!0),className:"flex items-center",style:{height:"32px",marginRight:8},size:"middle",children:"Otomatik Ata"}),n.jsx(Q,{icon:n.jsx(ht,{style:{fontSize:"16px"}}),onClick:cr,className:"flex items-center",style:{height:"32px",marginRight:8},size:"middle",children:"Toplu Mail Gönder"}),n.jsx(wr,{menu:{items:[{key:"fetch",label:"Haftalık Verileri Çek",onClick:lr},{key:"upload",label:"Haftalık Veri Yükle",onClick:()=>h(!0)},{key:"manual",label:"Manuel Görev Ekle",onClick:()=>d(!0)}]},trigger:["click"],placement:"bottomRight",children:n.jsx(Q,{icon:n.jsx(jr,{style:{fontSize:22}}),style:{height:"32px",border:"none",boxShadow:"none",background:"transparent"}})})]})]})}),A?n.jsx(Ke,{tip:"Yükleniyor..."}):ta.length>0?n.jsx(nt,{className:"mb-4",children:n.jsx(Tt,{bordered:!0,columns:ea,dataSource:ta,rowKey:"id",expandable:{expandedRowRender:l=>n.jsx(oi,{username:l.kullanici_adi,publishers:_,onSendMail:p=>{fe(p),W(!0)}}),expandRowByClick:!0,expandIcon:({expanded:l,onExpand:p,record:M})=>l?n.jsx(Rr,{style:{cursor:"pointer"},onClick:I=>p(M,I)}):n.jsx(Ir,{style:{cursor:"pointer"},onClick:I=>p(M,I)})},pagination:{pageSize:pe,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:(l,p)=>we(p)},loading:A,scroll:{x:"max-content"}})}):n.jsx("div",{className:"pl-6",children:ee&&n.jsx(ft,{message:"Veri Bulunamadı",description:"Seçilen hafta için yayıncı performans verisi bulunamadı.",type:"info",showIcon:!0,closable:!0,onClose:()=>$(!1),className:"mb-4",style:{overflow:"hidden",margin:"0",marginRight:"24px"}})}),sr(),n.jsx(Ce,{title:`${(x==null?void 0:x.kullanici_adi)||""} - Görev Detayları`,open:D,onCancel:()=>z(!1),footer:null,width:"90%",style:{maxWidth:"1000px"},styles:{body:{padding:"12px"}},children:i?n.jsx(Ke,{tip:"Görevler yükleniyor..."}):n.jsxs(n.Fragment,{children:[n.jsx(ke,{gutter:[16,16],className:"mb-4",children:n.jsx(N,{span:24,children:n.jsx(nt,{title:`${(b==null?void 0:b.week_name)||Jn(Z.format("YYYY-MM-DD"))} Performans Özeti`,size:"small",children:n.jsxs(ke,{gutter:[16,16],children:[n.jsx(N,{span:6,children:n.jsx(Ze,{title:"Canlı Yayın Günü",value:(x==null?void 0:x.canli_yayin_gunu)||0,valueStyle:{color:"#1677ff"}})}),n.jsx(N,{span:6,children:n.jsx(Ze,{title:"Yayın Süresi",value:ti((x==null?void 0:x.yayin_suresi)||0),valueStyle:{color:"#1677ff"}})}),n.jsx(N,{span:6,children:n.jsx(Ze,{title:"Elmaslar",value:((e=x==null?void 0:x.elmaslar)==null?void 0:e.toLocaleString("tr-TR"))||0,valueStyle:{color:"#1677ff"}})}),n.jsx(N,{span:6,children:n.jsx(Ze,{title:"Yeni Takipçiler",value:((t=x==null?void 0:x.yeni_takipciler)==null?void 0:t.toLocaleString("tr-TR"))||0,valueStyle:{color:"#1677ff"}})})]})})})}),n.jsx(ke,{className:"mb-3",children:n.jsx(N,{span:24,children:n.jsx(kt.Title,{level:5,children:"Haftalık Görevler"})})}),Qt(x).length>0?n.jsx(Tt,{columns:ea,dataSource:Qt(x),rowKey:"id",pagination:!1,scroll:void 0,bordered:!0,size:"middle",style:{width:"100%"}}):n.jsxs("div",{className:"text-center py-4",children:[n.jsx("p",{children:"Bu yayıncıya henüz görev atanmamış."}),n.jsx(Q,{type:"primary",icon:n.jsx(Ar,{}),onClick:()=>{z(!1),d(!0)},className:"mt-4",style:{backgroundColor:"#1677ff",display:"inline-flex",alignItems:"center",justifyContent:"center",height:"36px",paddingLeft:"15px",paddingRight:"15px"},children:"Görev Ekle"})]})]})}),n.jsx(Ce,{title:"Manuel Görev Ekle",open:o,onCancel:()=>d(!1),footer:null,destroyOnClose:!0,children:n.jsx(Un,{onSubmit:l=>{d(!1)},onClose:()=>d(!1),publishers:_,form:v})}),n.jsx(Ya,{isOpen:f,onClose:()=>u(!1),onSubmit:l=>Promise.resolve(void 0),publishers:re,mode:"bulk",selectedWeekRange:ni(Z),nextWeekRange:ii(Z)}),n.jsx(Hn,{isOpen:c,onClose:()=>h(!1)}),n.jsx(Ya,{isOpen:$e,onClose:()=>W(!1),onSubmit:l=>Promise.resolve(void 0),publishers:he?[he]:[],mode:"single"}),F&&n.jsx(Ke,{tip:"Mail verileri yükleniyor..."}),n.jsx(Ce,{open:ne,onCancel:()=>B(!1),onOk:or,confirmLoading:me,okText:"Evet",cancelText:"Hayır",title:"Tüm Kullanıcılara Otomatik Görev Ata",children:"Bu işlem, seçili hafta için tüm kullanıcılara kişiselleştirilmiş görevler atayacak. Devam etmek istiyor musunuz?"})]})}const oi=({username:e,publishers:t,onSendMail:a})=>{const[r,i]=y.useState([]),[s,o]=y.useState(!1),d=t.find(c=>c.username===e),f=()=>{var c,h,m,g,b,S,A,k,x,E,D,z,_,v;if(!d)return;const ee={...d,hafta_baslangici:(c=r[0])==null?void 0:c.hafta_baslangici,hafta_bitisi:(h=r[0])==null?void 0:h.hafta_bitisi,live_days:(g=(m=r[0])==null?void 0:m.canli_yayin_gunu)!=null?g:void 0,total_duration:(S=(b=r[0])==null?void 0:b.yayin_suresi)!=null?S:void 0,total_diamonds:(k=(A=r[0])==null?void 0:A.elmaslar)!=null?k:void 0,total_followers:(E=(x=r[0])==null?void 0:x.yeni_takipciler)!=null?E:void 0,total_subscribers:(z=(D=r[0])==null?void 0:D.aboneler)!=null?z:void 0,total_matches:(v=(_=r[0])==null?void 0:_.maclar)!=null?v:void 0};a(ee)};y.useEffect(()=>{(async()=>{o(!0);try{const h=await ri(e,4);i(h.data)}catch(h){i([])}finally{o(!1)}})()},[e]);const u=y.useMemo(()=>r.map(c=>({...c,yayin_suresi_dakika:c.yayin_suresi?Math.round(c.yayin_suresi/60):0,yeni_takipciler:c.yeni_takipciler||0,elmaslar:c.elmaslar||0,aboneler:c.aboneler||0,maclar:c.maclar||0,weekLabel:c.hafta_baslangici?C(c.hafta_baslangici).format("D MMM"):""})).reverse(),[r]);return s?n.jsx(Ke,{}):r.length===0?n.jsx("div",{className:"text-center text-gray-500 dark:text-gray-400 py-4",children:"Kullanıcı için geçmiş hafta verisi bulunamadı."}):n.jsxs("div",{children:[n.jsx(ke,{gutter:16,justify:"end",className:"mb-2",children:n.jsx(N,{children:n.jsx(Q,{icon:n.jsx(ht,{}),onClick:f,disabled:!d,children:"Mail Gönder"})})}),n.jsxs(ke,{gutter:[16,16],children:[n.jsxs(N,{xs:24,md:12,lg:6,children:[n.jsx("h4",{className:"text-sm font-semibold mb-2 text-center dark:text-gray-300",children:"Son 4 Hafta Yayın Süresi (dk)"}),n.jsx(Je,{width:"100%",height:200,children:n.jsxs(Ct,{data:u,children:[n.jsx(Qe,{strokeDasharray:"3 3"}),n.jsx(et,{dataKey:"weekLabel",fontSize:10}),n.jsx(Ge,{fontSize:10}),n.jsx(tt,{formatter:c=>`${c} dk`,labelFormatter:c=>`Hafta: ${c}`}),n.jsx(It,{dataKey:"yayin_suresi_dakika",fill:"#1890ff",name:"Yayın Süresi"})]})})]}),n.jsxs(N,{xs:24,md:12,lg:6,children:[n.jsx("h4",{className:"text-sm font-semibold mb-2 text-center dark:text-gray-300",children:"Son 4 Hafta Elmas"}),n.jsx(Je,{width:"100%",height:200,children:n.jsxs(Ct,{data:u,children:[n.jsx(Qe,{strokeDasharray:"3 3"}),n.jsx(et,{dataKey:"weekLabel",fontSize:10}),n.jsx(Ge,{fontSize:10}),n.jsx(tt,{formatter:c=>c.toLocaleString(),labelFormatter:c=>`Hafta: ${c}`}),n.jsx(It,{dataKey:"elmaslar",fill:"#faad14",name:"Elmas"})]})})]}),n.jsxs(N,{xs:24,md:12,lg:6,children:[n.jsx("h4",{className:"text-sm font-semibold mb-2 text-center dark:text-gray-300",children:"Son 4 Hafta Yeni Takipçi"}),n.jsx(Je,{width:"100%",height:200,children:n.jsxs(Ct,{data:u,children:[n.jsx(Qe,{strokeDasharray:"3 3"}),n.jsx(et,{dataKey:"weekLabel",fontSize:10}),n.jsx(Ge,{fontSize:10}),n.jsx(tt,{formatter:c=>c.toLocaleString(),labelFormatter:c=>`Hafta: ${c}`}),n.jsx(It,{dataKey:"yeni_takipciler",fill:"#52c41a",name:"Takipçi"})]})})]}),n.jsxs(N,{xs:24,md:12,lg:6,children:[n.jsx("h4",{className:"text-sm font-semibold mb-2 text-center dark:text-gray-300",children:"Son 4 Hafta Abone & Maç"}),n.jsx(Je,{width:"100%",height:200,children:n.jsxs(Er,{data:u,children:[n.jsx(Qe,{strokeDasharray:"3 3"}),n.jsx(et,{dataKey:"weekLabel",fontSize:10}),n.jsx(Ge,{yAxisId:"left",fontSize:10}),n.jsx(Ge,{yAxisId:"right",orientation:"right",fontSize:10}),n.jsx(tt,{formatter:(c,h)=>[`${c.toLocaleString()} ${h==="aboneler"?"Abone":"Maç"}`,h],labelFormatter:c=>`Hafta: ${c}`}),n.jsx(Yr,{verticalAlign:"top",height:30,iconSize:10,wrapperStyle:{fontSize:"10px"}}),n.jsx(ha,{yAxisId:"left",type:"monotone",dataKey:"aboneler",stroke:"#1890ff",strokeWidth:2,name:"Aboneler"}),n.jsx(ha,{yAxisId:"right",type:"monotone",dataKey:"maclar",stroke:"#f5222d",strokeWidth:2,name:"Maçlar"})]})})]})]})]})},{TabPane:_i}=Sr,ji=()=>{const{darkMode:e}=kr(),[t,a]=y.useState("tasks"),[r,i]=y.useState(!1);return y.useEffect(()=>{document.title="Yayıncı Performans | X Paneli"},[]),n.jsx("div",{className:"w-full h-full m-0 p-0",children:n.jsx(nt,{bordered:!1,className:`w-full overflow-hidden shadow-md ${e?"bg-gray-800 text-white":"bg-white"}`,bodyStyle:{padding:0,margin:0},style:{width:"100%",margin:0,borderRadius:0},children:n.jsx(si,{})})})};export{ji as default};
