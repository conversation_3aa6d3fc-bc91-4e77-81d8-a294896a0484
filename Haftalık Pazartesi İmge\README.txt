===== TikTok Haftalık Ajans Görev Takip Sistemi =====

Bu program, TikTok içerik üreticilerinin haftalık görevlerini takip etmek için kullanılır.
Görevler:
1. Günlük minimum 1 saat yayın açma durumu
2. Haftada 4 video paylaşımı
3. <PERSON><PERSON>s içi etkinliklere katılım
4. Topluluk kurallarına uygun yayın durumu

=========== KULLANIM ===========

Program iki ana bölümden oluşur:
1. Backstage verilerinin çekilmesi (yayın açma, ihlal durumları)
2. Video sayılarının çekilmesi (kullanıcı profillerinden)

Tüm işlemleri çalıştırmak:
python tiktok_daily_live_tracker.py

Sadece backstage verilerini çekmek:
python tiktok_daily_live_tracker.py --sadece-backstage

Sadece video sayılarını kontrol etmek:
python tiktok_daily_live_tracker.py --sadece-videolar

Belli bir kullanıcıdan başlayarak video kontrolü yapmak:
python tiktok_daily_live_tracker.py --sadece-videolar --baslangic-indeksi 64

Özel kullanıcı listesi ile çalıştırmak:
python tiktok_daily_live_tracker.py --kullanici-listesi kullanicilar.txt

Özel tarih aralığında çalıştırmak:
python tiktok_daily_live_tracker.py --tarih-baslangic 01.05.2024 --tarih-bitis 07.05.2024

=========== KALINAN YERDEN DEVAM ETME ===========

Program her bir aşama tamamlandığında son durumu "son_durum.json" dosyasına kaydeder.
Bu sayede işlem herhangi bir nedenle yarıda kesilirse, kaldığınız yerden devam edebilirsiniz.

Örnek: Video kontrol işlemi 63. kullanıcıda kaldıysa:
python tiktok_daily_live_tracker.py --sadece-videolar --baslangic-indeksi 64

=========== DİKKAT EDİLECEK HUSUSLAR ===========

1. TikTok'a ilk kez girişte manuel olarak giriş yapmanız istenecektir.
2. CAPTCHA çıktığında program otomatik olarak bekleyecek, siz çözdükten sonra devam edecektir.
3. Video kontrol işlemi uzun sürebilir, bu nedenle işlemi bölümlere ayırmak için baslangic-indeksi parametresini kullanabilirsiniz.
4. Profil sayfalarını kontrol ederken çok sık istek atarsanız, TikTok tarafından geçici olarak engellenebilirsiniz. Bu durumda beklemek gerekebilir.

=========== GEREKSINIMLER ===========

- Python 3.6 veya üzeri
- Google Chrome tarayıcısı
- chromedriver.exe (Chrome sürümünüze uygun olmalı)
- Gerekli Python kütüphaneleri: selenium, pandas, openpyxl

Program ilk çalıştırıldığında chrome profilini oluşturacak ve TikTok'a giriş yapmanızı isteyecektir. 