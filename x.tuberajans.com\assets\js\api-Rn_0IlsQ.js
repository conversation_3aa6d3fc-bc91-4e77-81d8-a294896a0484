import{c as h}from"./createLucideIcon-DxVmGoQf.js";import{A as u}from"./index-CVO3aNyS.js";const f=[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]],I=h("search",f),E=u.X_SITE_BASE_URL,p=()=>{var e;try{const t=localStorage.getItem("user");return t&&(e=JSON.parse(t).token)!=null?e:null}catch(t){return null}},d=async(e,t={})=>{try{const r=`${E}${e.startsWith("/")?e:"/"+e}`;t.method==="POST"&&t.body;const a=p(),n={"Content-Type":"application/json",Accept:"application/json"};a&&(n.Authorization=`Bearer ${a}`),t.headers&&Object.entries(t.headers).forEach(([s,l])=>{n[s]=l});const c={method:t.method||"GET",headers:n,body:t.body,credentials:"same-origin"},o=await fetch(r,c);if(!o.ok){const s=o.headers.get("content-type");let l="";try{if(s&&s.includes("application/json")){const i=await o.json();l=i.error||i.message||(typeof i=="string"?i:JSON.stringify(i))}else l=await o.text()||"Hata detayı yok"}catch(i){l="Hata yanıtı alınamadı"}throw new Error(`API request failed: Error: HTTP error! status: ${o.status} - ${l}`)}const y=o.headers.get("content-type");return y&&y.includes("application/json")?await o.json():await o.text()}catch(r){if(e.includes("auth"))throw r;return e.includes("reports")||e.includes("publishers")||e.includes("influencers")?{data:[]}:{}}};async function S(e,t){const r=`${u.ENDPOINTS.WEEKLY_ARCHIVE}?start_date=${e}&end_date=${t}`;try{return await d(r)}catch(a){return{data:[]}}}async function T(){try{const e=u.ENDPOINTS.INFLUENCERS;return await d(e,{method:"GET"})}catch(e){return{data:[]}}}async function w(e,t){try{const r=u.ENDPOINTS.INFLUENCERS;if(!e)throw new Error("Güncelleme için ID gereklidir");const a=Object.entries(t).filter(([n,c])=>c!=null).reduce((n,[c,o])=>({...n,[c]:o}),{});return a.id=e,d(r,{method:"PUT",body:JSON.stringify({action:"update",data:a})})}catch(r){throw r}}async function g(e){try{if(!e.username||!e.email)throw new Error("Kullanıcı adı ve email zorunludur");const t=u.ENDPOINTS.INFLUENCERS,r=Object.entries(e).filter(([a,n])=>n!=null&&n!=="").reduce((a,[n,c])=>({...a,[n]:c}),{});return d(t,{method:"POST",body:JSON.stringify({action:"create",data:r})})}catch(t){throw t}}async function O(e){try{const t=u.ENDPOINTS.INFLUENCERS;if(!e)throw new Error("Silme işlemi için ID gereklidir");return d(t,{method:"DELETE",body:JSON.stringify({action:"delete",id:e})})}catch(t){throw t}}async function D(e,t,r){return d("/influencer-email.php",{method:"POST",body:JSON.stringify({emails:e,subject:t,content:r})})}export{I as S,S as a,g as c,O as d,T as g,D as s,w as u};
