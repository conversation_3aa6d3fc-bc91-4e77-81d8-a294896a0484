<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// CORS başlıkları
header("Access-Control-Allow-Origin: https://x.tuberajans.com");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
header("Access-Control-Allow-Credentials: true");
header("Access-Control-Max-Age: 86400");

// Content-Type header'ını ayarla
header('Content-Type: application/json; charset=utf-8');

// OPTIONS isteklerini hemen yanıtla
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(204);
    exit();
}

try {
    require_once __DIR__ . '/../config/config.php';
    error_log("Auth.php: Config dosyası başarıyla yüklendi");
} catch (Exception $e) {
    error_log("Config dosyası yüklenemedi: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Server configuration error', 'message' => $e->getMessage(), 'file' => __FILE__, 'line' => __LINE__]);
    exit;
}

// Session başlat
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// İzinleri normalize eden fonksiyon
function normalizePermissions($permissions) {
    if (isset($permissions['site'])) {
        $permissions['site_yonetimi'] = $permissions['site'];
        unset($permissions['site']);
    }
    if (isset($permissions['etsy'])) {
        $permissions['etsy_operasyonu'] = $permissions['etsy'];
        unset($permissions['etsy']);
    }
    return $permissions;
}

// Kullanıcı izinlerini almak için yardımcı fonksiyon
function getUserPermissions($userId) {
    global $db_takip;
    try {
        $stmt = $db_takip->prepare("SELECT permissions, role FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($row && !empty($row['permissions'])) {
            $permissions = json_decode($row['permissions'], true);
            if (is_array($permissions)) {
                return normalizePermissions($permissions);
            }
        }
        // Eğer permissions yoksa, role bazlı default döndür
        $role = $row['role'] ?? null;
        if ($role === 'admin') {
            return [
                'dashboard' => true,
                'publishers' => true,
                'influencers' => true,
                'tasks' => true,
                'reports' => true,
                'ai_advisor' => true,
                'events' => [
                    'view' => true,
                    'manage' => true,
                    'ai_advisor' => true,
                    'pk_matcher' => true
                ],
                'users' => [
                    'view' => true,
                    'manage' => true
                ],
                'performance' => true,
                'tournament' => true,
                'akademi' => [
                    'dashboard' => true,
                    'duyurular' => true,
                    'egitimler' => true,
                    'destek' => true,
                    'kullanicilar' => true,
                    'ai' => true,
                    'ayarlar' => true
                ],
                'site_yonetimi' => [
                    'anasayfa' => true,
                    'yayincilar' => true,
                    'basvurular' => true,
                    'iletisim' => true,
                    'geriarama' => true,
                    'toplanti' => true,
                    'sms' => true,
                    'blog' => true,
                    'ayarlar' => true
                ],
                'etsy_operasyonu' => [
                    'dashboard' => true,
                    'tasarim' => true,
                    'urunler' => true,
                    'ayarlar' => true
                ],
                'whatsapp' => [
                    'view' => true,
                    'manage' => true
                ],
                'yayinci_kesfi' => [
                    'view' => true,
                    'manage' => true
                ],
                'business_discovery' => [
                    'fetch' => true,
                    'list' => true,
                    'stats' => true,
                    'settings' => true
                ]
            ];
        } else if ($role === 'editor') {
            return [
                'dashboard' => true,
                'publishers' => true,
                'influencers' => true,
                'tasks' => true,
                'reports' => true,
                'ai_advisor' => false,
                'events' => [
                    'view' => true,
                    'manage' => true,
                    'ai_advisor' => false,
                    'pk_matcher' => false
                ],
                'users' => [
                    'view' => false,
                    'manage' => false
                ],
                'performance' => true,
                'tournament' => false,
                'akademi' => [
                    'dashboard' => true,
                    'duyurular' => true,
                    'egitimler' => true,
                    'destek' => true,
                    'kullanicilar' => true,
                    'ai' => true,
                    'ayarlar' => true
                ],
                'site_yonetimi' => [
                    'anasayfa' => true,
                    'yayincilar' => true,
                    'basvurular' => true,
                    'iletisim' => true,
                    'geriarama' => true,
                    'toplanti' => true,
                    'sms' => true,
                    'blog' => true,
                    'ayarlar' => true
                ],
                'etsy_operasyonu' => [
                    'dashboard' => true,
                    'tasarim' => true,
                    'urunler' => true,
                    'ayarlar' => true
                ],
                'whatsapp' => [
                    'view' => true,
                    'manage' => false
                ],
                'yayinci_kesfi' => [
                    'view' => true,
                    'manage' => false
                ],
                'business_discovery' => [
                    'fetch' => true,
                    'list' => true,
                    'stats' => true,
                    'settings' => true
                ]
            ];
        } else if ($role === 'viewer') {
            return [
                'dashboard' => true,
                'publishers' => true,
                'influencers' => false,
                'tasks' => false,
                'reports' => true,
                'ai_advisor' => false,
                'events' => [
                    'view' => true,
                    'manage' => false,
                    'ai_advisor' => false,
                    'pk_matcher' => false
                ],
                'users' => [
                    'view' => false,
                    'manage' => false
                ],
                'performance' => false,
                'tournament' => false,
                'akademi' => [
                    'dashboard' => false,
                    'duyurular' => false,
                    'egitimler' => false,
                    'destek' => false,
                    'kullanicilar' => false,
                    'ai' => false,
                    'ayarlar' => false
                ],
                'site_yonetimi' => [
                    'anasayfa' => false,
                    'yayincilar' => false,
                    'basvurular' => false,
                    'iletisim' => false,
                    'geriarama' => false,
                    'toplanti' => false,
                    'sms' => false,
                    'blog' => false,
                    'ayarlar' => false
                ],
                'etsy_operasyonu' => [
                    'dashboard' => false,
                    'tasarim' => false,
                    'urunler' => false,
                    'ayarlar' => false
                ],
                'whatsapp' => [
                    'view' => false,
                    'manage' => false
                ],
                'yayinci_kesfi' => [
                    'view' => false,
                    'manage' => false
                ],
                'business_discovery' => [
                    'fetch' => false,
                    'list' => false,
                    'stats' => false,
                    'settings' => false
                ]
            ];
        }
        // Hiçbiri değilse minimum default
        return [ 'dashboard' => true ];
    } catch (PDOException $e) {
        return [ 'dashboard' => true ];
    }
}

// Auth kontrolü
if (isset($_GET['check'])) {
    error_log("Auth.php: Oturum kontrolü isteği alındı");

    // Hızlı yanıt için timeout ayarla
    set_time_limit(10); // 10 saniye max

    // Veritabanı bağlantısını kontrol et
    if (!isset($db_takip) || !$db_takip) {
        error_log("Auth.php: db_takip bağlantısı bulunamadı");
        http_response_code(200);
        echo json_encode([
            'authenticated' => false,
            'message' => 'Veritabanı bağlantısı bulunamadı. Lütfen giriş yapın.',
            'debug' => 'db_takip connection missing'
        ]);
        exit;
    }

    $token = getBearerToken();
    error_log("Auth.php: Token: " . ($token ? substr($token, 0, 10) . "..." : "Yok"));

    if ($token) {
        try {
            error_log("Auth.php: Token ile kullanıcı aranıyor...");
            $stmt = $db_takip->prepare("
                SELECT u.id, u.name, u.email, u.role
                FROM users u
                JOIN user_tokens t ON u.id = t.user_id
                WHERE t.token = ? AND t.expires_at > NOW()
            ");
            $stmt->execute([$token]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            error_log("Auth.php: Kullanıcı bulundu: " . ($user ? "Evet (ID: " . $user['id'] . ")" : "Hayır"));

            if ($user) {
                // Kullanıcı izinlerini al
                $permissions = getUserPermissions($user['id']);

                // Token süresini uzat
                $expiresAt = date('Y-m-d H:i:s', strtotime('+24 hours'));
                $updateStmt = $db_takip->prepare("UPDATE user_tokens SET expires_at = ? WHERE token = ?");
                $updateStmt->execute([$expiresAt, $token]);

                // file_put_contents($logFile, date('Y-m-d H:i:s') . " - Oturum kontrolü başarılı: " . $user['id'] . "\n", FILE_APPEND);
                http_response_code(200);
                echo json_encode([
                    'authenticated' => true,
                    'user' => [
                        'id' => $user['id'],
                        'name' => $user['name'],
                        'email' => $user['email'],
                        'role' => $user['role'],
                        'permissions' => $permissions
                    ]
                ]);
                exit;
            }
        } catch (PDOException $e) {
            error_log("Auth.php: Veritabanı hatası: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Database error', 'message' => $e->getMessage()]);
            exit;
        }
    }

    // Token geçersizse
    http_response_code(200);
    echo json_encode([
        'authenticated' => false,
        'message' => 'Oturum bulunamadı veya süresi doldu. Lütfen giriş yapın.'
    ]);
    exit;
}

// Giriş işlemi
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isset($_GET['action']) && basename($_SERVER['SCRIPT_NAME']) === 'auth.php') {
    // file_put_contents($logFile, date('Y-m-d H:i:s') . " - Giriş isteği alındı\n", FILE_APPEND);

    // Gelen JSON verisini al
    $raw_input = file_get_contents('php://input');
    $data = json_decode($raw_input, true);

    if (!isset($data['email']) || !isset($data['password'])) {
        // file_put_contents($logFile, date('Y-m-d H:i:s') . " - Eksik alanlar\n", FILE_APPEND);
        http_response_code(400);
        echo json_encode(['error' => 'E-posta ve şifre gerekli']);
        exit();
    }

    try {
        // E-posta adresini lowercase'e çevirelim (case-insensitive karşılaştırma için)
        $email = strtolower(trim($data['email']));
        // file_put_contents($logFile, date('Y-m-d H:i:s') . " - Giriş denemesi: $email\n", FILE_APPEND);

        $stmt = $db_takip->prepare("SELECT id, email, password, role, name FROM users WHERE LOWER(email) = LOWER(?)");
        $stmt->execute([$email]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($user) {
            // Şifre doğrulama işlemi
            $password_verified = password_verify($data['password'], $user['password']);
            $ip = $_SERVER['REMOTE_ADDR'] ?? '';
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            if ($password_verified) {
                // Giriş başarılıysa, login_attempts tablosuna kayıt ekle
                $status = 'success';
                $stmtLog = $db_takip->prepare("INSERT INTO login_attempts (email, attempt_time, status, ip_address, user_agent) VALUES (?, NOW(), ?, ?, ?)");
                $stmtLog->execute([$email, $status, $ip, $userAgent]);

                // Token oluştur
                $token = bin2hex(random_bytes(32));
                $expires = date('Y-m-d H:i:s', strtotime('+24 hours'));

                // Önce eski tokenları temizle
                try {
                    $cleanStmt = $db_takip->prepare("DELETE FROM user_tokens WHERE user_id = ?");
                    $cleanStmt->execute([$user['id']]);
                    // file_put_contents($logFile, date('Y-m-d H:i:s') . " - Eski tokenlar temizlendi: " . $user['id'] . "\n", FILE_APPEND);
                } catch (PDOException $e) {
                    // file_put_contents($logFile, date('Y-m-d H:i:s') . " - Eski token temizleme hatası: " . $e->getMessage() . "\n", FILE_APPEND);
                    // Hata olsa bile devam et, yeni token kaydedilebilir
                }

                // Veritabanına token'ı kaydet
                $stmt = $db_takip->prepare("INSERT INTO user_tokens (user_id, token, expires_at) VALUES (?, ?, ?)");
                $success = $stmt->execute([$user['id'], $token, $expires]);

                if (!$success) {
                    // file_put_contents($logFile, date('Y-m-d H:i:s') . " - Token kaydetme hatası: " . implode(", ", $stmt->errorInfo()) . "\n", FILE_APPEND);
                    http_response_code(500);
                    echo json_encode(['error' => 'Token kaydedilemedi']);
                    exit();
                }

                // file_put_contents($logFile, date('Y-m-d H:i:s') . " - Token oluşturuldu ve kaydedildi: " . substr($token, 0, 10) . "...\n", FILE_APPEND);

                // Süresi geçmiş tokenları temizle (bakım)
                try {
                    $db_takip->exec("DELETE FROM user_tokens WHERE expires_at < NOW()");
                } catch (PDOException $e) {
                    // file_put_contents($logFile, date('Y-m-d H:i:s') . " - Süresi geçmiş tokenları temizleme hatası: " . $e->getMessage() . "\n", FILE_APPEND);
                }

                // Kullanıcı izinlerini al
                $permissions = getUserPermissions($user['id']);

                // file_put_contents($logFile, date('Y-m-d H:i:s') . " - Giriş başarılı: " . $user['id'] . "\n", FILE_APPEND);
                http_response_code(200);
                echo json_encode([
                    'success' => true,
                    'token' => $token,
                    'expires_at' => $expires,
                    'user' => [
                        'id' => $user['id'],
                        'email' => $user['email'],
                        'role' => $user['role'],
                        'name' => $user['name'],
                        'permissions' => $permissions
                    ]
                ]);
            } else {
                // Giriş başarısızsa, login_attempts tablosuna kayıt ekle
                $status = 'fail';
                $stmtLog = $db_takip->prepare("INSERT INTO login_attempts (email, attempt_time, status, ip_address, user_agent) VALUES (?, NOW(), ?, ?, ?)");
                $stmtLog->execute([$email, $status, $ip, $userAgent]);
                // file_put_contents($logFile, date('Y-m-d H:i:s') . " - Geçersiz şifre\n", FILE_APPEND);
                http_response_code(401);
                echo json_encode(['error' => 'Geçersiz kullanıcı adı veya şifre']);
            }
        } else {
            // Giriş başarısızsa, login_attempts tablosuna kayıt ekle
            $ip = $_SERVER['REMOTE_ADDR'] ?? '';
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            $status = 'fail';
            $stmtLog = $db_takip->prepare("INSERT INTO login_attempts (email, attempt_time, status, ip_address, user_agent) VALUES (?, NOW(), ?, ?, ?)");
            $stmtLog->execute([$email, $status, $ip, $userAgent]);
            // file_put_contents($logFile, date('Y-m-d H:i:s') . " - Kullanıcı bulunamadı\n", FILE_APPEND);
            http_response_code(401);
            echo json_encode(['error' => 'Geçersiz kullanıcı adı veya şifre']);
        }
    } catch (PDOException $e) {
        // file_put_contents($logFile, date('Y-m-d H:i:s') . " - Veritabanı hatası: " . $e->getMessage() . "\n", FILE_APPEND);
        http_response_code(500);
        echo json_encode(['error' => 'Sunucu hatası', 'detay' => $e->getMessage()]);
    }
    exit();
}

// Çıkış yap
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_GET['action']) && $_GET['action'] === 'logout') {
    // file_put_contents($logFile, date('Y-m-d H:i:s') . " - Çıkış isteği alındı\n", FILE_APPEND);

    $token = getBearerToken();

    if ($token) {
        try {
            $stmt = $db_takip->prepare("DELETE FROM user_tokens WHERE token = ?");
            $stmt->execute([$token]);

            // file_put_contents($logFile, date('Y-m-d H:i:s') . " - Çıkış başarılı\n", FILE_APPEND);
            http_response_code(200);
            echo json_encode(['success' => true, 'message' => 'Çıkış başarılı']);
        } catch (PDOException $e) {
            // file_put_contents($logFile, date('Y-m-d H:i:s') . " - Veritabanı hatası: " . $e->getMessage() . "\n", FILE_APPEND);
            http_response_code(500);
            echo json_encode(['error' => 'Sunucu hatası']);
        }
    } else {
        // file_put_contents($logFile, date('Y-m-d H:i:s') . " - Token bulunamadı\n", FILE_APPEND);
        http_response_code(401);
        echo json_encode(['error' => 'Token gerekli']);
    }
    exit();
}

// Bearer token'ı header'dan çeken yardımcı fonksiyon
function getBearerToken() {
    $headers = null;
    if (isset($_SERVER['Authorization'])) {
        $headers = trim($_SERVER["Authorization"]);
    } else if (isset($_SERVER['HTTP_AUTHORIZATION'])) { // Nginx veya fast CGI
        $headers = trim($_SERVER["HTTP_AUTHORIZATION"]);
    } else if (isset($_SERVER['REDIRECT_HTTP_AUTHORIZATION'])) {
        $headers = trim($_SERVER["REDIRECT_HTTP_AUTHORIZATION"]);
    } elseif (function_exists('apache_request_headers')) {
        $requestHeaders = apache_request_headers();
        foreach ($requestHeaders as $key => $value) {
            if (strtolower($key) === 'authorization') {
                $headers = trim($value);
                break;
            }
        }
    }
    // Bearer token'ı ayıkla
    if (!empty($headers)) {
        if (preg_match('/Bearer\s(\S+)/', $headers, $matches)) {
            return $matches[1];
        }
    }
    return null;
}

// Token ile kimlik doğrulaması
function requireAuthToken() {
    $token = getBearerToken();
    if (!$token) {
        http_response_code(401);
        echo json_encode(['error' => 'Unauthorized', 'message' => 'Token required']);
        exit;
    }

    global $db_takip;

    // Veritabanı bağlantısını kontrol et
    if (!isset($db_takip) || !$db_takip) {
        http_response_code(500);
        echo json_encode(['error' => 'Server Error', 'message' => 'Database connection failed']);
        exit;
    }

    // Gerçek veritabanı kontrolü
    try {
        $stmt = $db_takip->prepare("SELECT user_id FROM user_tokens WHERE token = ? AND expires_at > NOW()");
        $stmt->execute([$token]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result && isset($result['user_id'])) {
            return $result['user_id'];
        } else {
            http_response_code(401);
            echo json_encode(['error' => 'Unauthorized', 'message' => 'Invalid or expired token']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Token doğrulama hatası: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Server Error', 'message' => 'Database error: ' . $e->getMessage()]);
        exit;
    }
}