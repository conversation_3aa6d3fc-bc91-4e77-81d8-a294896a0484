<?php
require_once __DIR__ . '/../config/config.php';

function handleError($e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
    exit;
}

$conn = getDBConnection();

header('Content-Type: application/json; charset=utf-8');

// GET isteği - Bildirimleri listele
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {
        $stmt = $conn->prepare("SELECT id, type, data, created_at, is_read FROM notifications ORDER BY created_at DESC");
        $stmt->execute();
        $dbNotifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $notifications = [];
        foreach ($dbNotifications as $notification) {
            $data = json_decode($notification['data'], true);
            $title = '';
            $message = '';
            $source = $notification['type'];
            switch ($source) {
                case 'site.basvuru':
                    $title = 'Yeni Yayıncı Başvurusu';
                    $tiktok = $data['tiktok_username'] ?? '';
                    $name = $data['name'] ?? '';
                    $message = "'{$name}' isimli kişiden yeni bir yayıncı başvurusu alındı. @{$tiktok}";
                    break;
                case 'site.iletisim':
                    $title = 'Yeni İletişim Talebi';
                    $name = $data['adsoyad'] ?? '';
                    $message = "{$name} kişisinden yeni bir iletişim talebi alındı.";
                    break;
                case 'site.geriarama':
                    $title = 'Yeni Geri Arama Talebi';
                    $name = $data['adsoyad'] ?? '';
                    $phone = $data['telefon'] ?? '';
                    $message = "{$name} ({$phone}) kişisinden yeni bir geri arama talebi alındı.";
                    break;
                case 'site.toplanti':
                    $title = 'Yeni Online Toplantı Talebi';
                    $name = $data['adsoyad'] ?? '';
                    $konu = $data['konu'] ?? '';
                    $message = "{$name} kişisinden '{$konu}' konulu yeni bir toplantı talebi alındı.";
                    break;
                default:
                    $title = isset($data['title']) ? $data['title'] : 'Yeni Bildirim';
                    $message = isset($data['message']) ? $data['message'] : 'Yeni bir bildirim alındı.';
                    break;
            }
            $notifications[] = [
                'id' => (string)$notification['id'],
                'title' => $title,
                'message' => $message,
                'time' => date('c', strtotime($notification['created_at'])),
                'read' => (bool)$notification['is_read'],
                'type' => 'info',
                'source' => $source,
                'sourceId' => isset($data['id']) ? (string)$data['id'] : null
            ];
        }
        echo json_encode(["data" => $notifications]);
        return;
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(["error" => $e->getMessage()]);
        return;
    }
}

// POST isteği - Yeni bildirim ekle
else if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $data = json_decode(file_get_contents('php://input'), true);
        
        $sql = "INSERT INTO notifications (
            user_id, mesaj, tip
        ) VALUES (
            :user_id, :mesaj, :tip
        )";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            ':user_id' => $data['user_id'],
            ':mesaj' => $data['mesaj'],
            ':tip' => $data['tip']
        ]);
        
        $notificationId = $conn->lastInsertId();
        
        echo json_encode([
            'message' => 'Bildirim başarıyla oluşturuldu',
            'id' => $notificationId
        ]);
    } catch(PDOException $e) {
        handleError($e);
    }
}

// PUT isteği - Bildirim güncelle (okundu olarak işaretle)
else if ($_SERVER['REQUEST_METHOD'] === 'PUT') {
    try {
        $data = json_decode(file_get_contents('php://input'), true);
        
        $sql = "UPDATE notifications SET okundu = :okundu WHERE id = :id";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            ':id' => $data['id'],
            ':okundu' => $data['okundu']
        ]);
        
        echo json_encode(['message' => 'Bildirim başarıyla güncellendi']);
    } catch(PDOException $e) {
        handleError($e);
    }
}

// DELETE isteği - Bildirim sil
else if ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
    try {
        $id = $_GET['id'];
        
        $sql = "DELETE FROM notifications WHERE id = :id";
        $stmt = $conn->prepare($sql);
        $stmt->execute([':id' => $id]);
        
        echo json_encode(['message' => 'Bildirim başarıyla silindi']);
    } catch(PDOException $e) {
        handleError($e);
    }
}