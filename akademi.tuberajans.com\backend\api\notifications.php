<?php
/**
 * Bildirimler API
 *
 * Bu dosya, akademi.tuberajans.com sitesi için bildirim API'sini sağlar.
 * Bildirim oluşturma, listeleme, okundu olarak işaretleme işlemlerini yönetir.
 */

// Doğrudan erişimi engelle
if (count(get_included_files()) == 1) {
    // Bu dosya doğrudan çalıştırılıyor, JSON yanıtı döndür
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'status' => 'error',
        'message' => 'API endpoint is working but should be accessed through proper channels',
        'time' => date('Y-m-d H:i:s')
    ]);
    exit;
}

// Veritabanı bağlantısını al
require_once __DIR__ . '/../config/config.php';

// CORS başlıkları
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
header("Content-Type: application/json; charset=utf-8");

// OPTIONS isteği ise hemen yanıt ver
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// İstek metoduna göre işlem yap
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        // Bildirimleri listele
        getNotifications();
        break;
    case 'POST':
        // Yeni bildirim oluştur
        createNotification();
        break;
    case 'PUT':
        // Bildirimi okundu olarak işaretle
        markAsRead();
        break;
    case 'DELETE':
        // Bildirimi sil (isteğe bağlı)
        deleteNotification();
        break;
    default:
        http_response_code(405);
        echo json_encode([
            'status' => 'error',
            'message' => 'Method not allowed'
        ]);
        break;
}

/**
 * Bildirimleri listele
 */
function getNotifications() {
    global $db;

    // Kullanıcı kimliğini al (auth sisteminden)
    $user_id = getUserId();

    // Parametreleri al
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
    $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
    $unread_only = isset($_GET['unread']) && $_GET['unread'] === 'true';

    try {
        // SQL sorgusu
        $sql = "SELECT * FROM notifications WHERE user_id = :user_id";

        // Sadece okunmamış bildirimleri istiyorsa
        if ($unread_only) {
            $sql .= " AND `read` = 0";
        }

        // Sıralama ve limit
        $sql .= " ORDER BY created_at DESC LIMIT :limit OFFSET :offset";

        $stmt = $db->prepare($sql);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Okunmamış bildirim sayısını al
        $countStmt = $db->prepare("SELECT COUNT(*) FROM notifications WHERE user_id = :user_id AND `read` = 0");
        $countStmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $countStmt->execute();
        $unreadCount = $countStmt->fetchColumn();

        echo json_encode([
            'status' => 'success',
            'data' => $notifications,
            'unread_count' => $unreadCount,
            'total' => count($notifications),
            'limit' => $limit,
            'offset' => $offset
        ]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'status' => 'error',
            'message' => 'Bildirimler alınırken hata oluştu: ' . $e->getMessage()
        ]);
    }
}

/**
 * Yeni bildirim oluştur
 */
function createNotification() {
    global $db;

    // JSON verisini al
    $data = json_decode(file_get_contents('php://input'), true);

    // Gerekli alanları kontrol et
    if (!isset($data['user_id']) || !isset($data['title']) || !isset($data['message']) || !isset($data['type'])) {
        http_response_code(400);
        echo json_encode([
            'status' => 'error',
            'message' => 'Eksik parametreler: user_id, title, message ve type gerekli'
        ]);
        return;
    }

    // Type değerini kontrol et ve dönüştür
    $type = $data['type'];
    // Tip dönüşümü: announcement, support, course, event, system -> info, success, warning, error
    if ($type === 'announcement') {
        $type = 'info';
    } elseif ($type === 'support') {
        $type = 'success';
    } elseif ($type === 'course') {
        $type = 'info';
    } elseif ($type === 'event') {
        $type = 'info';
    } elseif ($type === 'system') {
        $type = 'warning';
    }

    try {
        $sql = "INSERT INTO notifications (
            user_id, title, message, type, `read`, created_at, source, source_id, link
        ) VALUES (
            :user_id, :title, :message, :type, 0, NOW(), :source, :source_id, :link
        )";

        $stmt = $db->prepare($sql);
        $stmt->bindParam(':user_id', $data['user_id'], PDO::PARAM_INT);
        $stmt->bindParam(':title', $data['title'], PDO::PARAM_STR);
        $stmt->bindParam(':message', $data['message'], PDO::PARAM_STR);
        $stmt->bindParam(':type', $type, PDO::PARAM_STR);
        $stmt->bindParam(':source', $data['source'] ?? null, PDO::PARAM_STR);
        $stmt->bindParam(':source_id', $data['source_id'] ?? null, PDO::PARAM_STR);
        $stmt->bindParam(':link', $data['link'] ?? null, PDO::PARAM_STR);
        $stmt->execute();

        $notification_id = $db->lastInsertId();

        echo json_encode([
            'status' => 'success',
            'message' => 'Bildirim başarıyla oluşturuldu',
            'id' => $notification_id
        ]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'status' => 'error',
            'message' => 'Bildirim oluşturulurken hata oluştu: ' . $e->getMessage()
        ]);
    }
}

/**
 * Bildirimi okundu olarak işaretle
 */
function markAsRead() {
    global $db;

    // JSON verisini al
    $data = json_decode(file_get_contents('php://input'), true);

    // Bildirim ID'sini kontrol et
    if (!isset($data['id'])) {
        http_response_code(400);
        echo json_encode([
            'status' => 'error',
            'message' => 'Bildirim ID gerekli'
        ]);
        return;
    }

    // Tüm bildirimleri okundu olarak işaretleme kontrolü
    $markAll = isset($data['mark_all']) && $data['mark_all'] === true;
    $user_id = getUserId();

    try {
        if ($markAll) {
            // Tüm bildirimleri okundu olarak işaretle
            $sql = "UPDATE notifications SET `read` = 1 WHERE user_id = :user_id";
            $stmt = $db->prepare($sql);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->execute();

            echo json_encode([
                'status' => 'success',
                'message' => 'Tüm bildirimler okundu olarak işaretlendi'
            ]);
        } else {
            // Tek bir bildirimi okundu olarak işaretle
            $sql = "UPDATE notifications SET `read` = 1 WHERE id = :id AND user_id = :user_id";
            $stmt = $db->prepare($sql);
            $stmt->bindParam(':id', $data['id'], PDO::PARAM_INT);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                echo json_encode([
                    'status' => 'success',
                    'message' => 'Bildirim okundu olarak işaretlendi'
                ]);
            } else {
                http_response_code(404);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Bildirim bulunamadı veya bu kullanıcıya ait değil'
                ]);
            }
        }
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'status' => 'error',
            'message' => 'Bildirim güncellenirken hata oluştu: ' . $e->getMessage()
        ]);
    }
}

/**
 * Bildirimi sil (isteğe bağlı)
 */
function deleteNotification() {
    global $db;

    // JSON verisini al
    $data = json_decode(file_get_contents('php://input'), true);

    // Bildirim ID'sini kontrol et
    if (!isset($data['id'])) {
        http_response_code(400);
        echo json_encode([
            'status' => 'error',
            'message' => 'Bildirim ID gerekli'
        ]);
        return;
    }

    $user_id = getUserId();

    try {
        $sql = "DELETE FROM notifications WHERE id = :id AND user_id = :user_id";
        $stmt = $db->prepare($sql);
        $stmt->bindParam(':id', $data['id'], PDO::PARAM_INT);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            echo json_encode([
                'status' => 'success',
                'message' => 'Bildirim silindi'
            ]);
        } else {
            http_response_code(404);
            echo json_encode([
                'status' => 'error',
                'message' => 'Bildirim bulunamadı veya bu kullanıcıya ait değil'
            ]);
        }
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'status' => 'error',
            'message' => 'Bildirim silinirken hata oluştu: ' . $e->getMessage()
        ]);
    }
}

/**
 * Kullanıcı kimliğini al
 * Not: Gerçek uygulamada bu, auth sisteminden alınmalıdır
 */
function getUserId() {
    // Şu an için test amaçlı sabit bir değer döndürüyoruz
    // Gerçek uygulamada, oturum veya token'dan kullanıcı kimliği alınmalıdır
    return 1; // Örnek kullanıcı ID'si
}
