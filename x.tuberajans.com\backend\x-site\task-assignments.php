<?php
require_once __DIR__ . '/../config/config.php';

$conn = getDBConnection();

// GET isteği - Görev atamalarını listele
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {
        $sql = "SELECT ta.*, t.gorev_onerisi, u.username as yayinci_adi 
                FROM task_assignments ta
                INNER JOIN tasks t ON ta.task_id = t.id
                INNER JOIN users u ON ta.user_id = u.id";
        
        // Yayıncı ID'si ile filtreleme
        if (isset($_GET['publisherId'])) {
            $sql .= " WHERE ta.user_id = :publisherId";
            $stmt = $conn->prepare($sql);
            $stmt->bindParam(':publisherId', $_GET['publisherId']);
        }
        // Hafta başlangıcı ile filtreleme
        else if (isset($_GET['weekStart'])) {
            $sql .= " WHERE ta.hafta_baslangici = :weekStart";
            $stmt = $conn->prepare($sql);
            $stmt->bindParam(':weekStart', $_GET['weekStart']);
        }
        else {
            $stmt = $conn->prepare($sql);
        }
        
        $stmt->execute();
        $assignments = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode(['data' => $assignments]);
    } catch(PDOException $e) {
        handleError($e);
    }
}

// POST isteği - Yeni görev ataması yap
else if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Toplu atama için
        if (isset($data['assignments']) && is_array($data['assignments'])) {
            $sql = "INSERT INTO task_assignments (
                task_id, user_id, hafta_baslangici, hafta_bitisi, durumu
            ) VALUES (
                :task_id, :user_id, :hafta_baslangici, :hafta_bitisi, :durumu
            )";
            
            $stmt = $conn->prepare($sql);
            $insertedIds = [];
            
            foreach ($data['assignments'] as $assignment) {
                $stmt->execute([
                    ':task_id' => $assignment['task_id'],
                    ':user_id' => $assignment['user_id'],
                    ':hafta_baslangici' => $assignment['hafta_baslangici'],
                    ':hafta_bitisi' => $assignment['hafta_bitisi'],
                    ':durumu' => $assignment['durumu'] ?? 'açık'
                ]);
                
                $insertedIds[] = $conn->lastInsertId();
            }
            
            echo json_encode([
                'message' => 'Görev atamaları başarıyla oluşturuldu',
                'ids' => $insertedIds
            ]);
        }
        // Tekil atama için
        else {
            $sql = "INSERT INTO task_assignments (
                task_id, user_id, hafta_baslangici, hafta_bitisi, durumu
            ) VALUES (
                :task_id, :user_id, :hafta_baslangici, :hafta_bitisi, :durumu
            )";
            
            $stmt = $conn->prepare($sql);
            $stmt->execute([
                ':task_id' => $data['task_id'],
                ':user_id' => $data['user_id'],
                ':hafta_baslangici' => $data['hafta_baslangici'],
                ':hafta_bitisi' => $data['hafta_bitisi'],
                ':durumu' => $data['durumu'] ?? 'açık'
            ]);
            
            $assignmentId = $conn->lastInsertId();
            
            echo json_encode([
                'message' => 'Görev ataması başarıyla oluşturuldu',
                'id' => $assignmentId
            ]);
        }
    } catch(PDOException $e) {
        handleError($e);
    }
}

// PUT isteği - Görev ataması güncelle
else if ($_SERVER['REQUEST_METHOD'] === 'PUT') {
    try {
        $data = json_decode(file_get_contents('php://input'), true);
        
        $sql = "UPDATE task_assignments SET 
            durumu = :durumu,
            ilerleme = :ilerleme,
            tamamlanma_tarihi = :tamamlanma_tarihi
            WHERE id = :id";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            ':id' => $data['id'],
            ':durumu' => $data['durumu'],
            ':ilerleme' => $data['ilerleme'] ?? 0,
            ':tamamlanma_tarihi' => $data['tamamlanma_tarihi'] ?? null
        ]);
        
        echo json_encode(['message' => 'Görev ataması başarıyla güncellendi']);
    } catch(PDOException $e) {
        handleError($e);
    }
}

// DELETE isteği - Görev ataması sil
else if ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
    try {
        $id = $_GET['id'];
        
        $sql = "DELETE FROM task_assignments WHERE id = :id";
        $stmt = $conn->prepare($sql);
        $stmt->execute([':id' => $id]);
        
        echo json_encode(['message' => 'Görev ataması başarıyla silindi']);
    } catch(PDOException $e) {
        handleError($e);
    }
}
?> 