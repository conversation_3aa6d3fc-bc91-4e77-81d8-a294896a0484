@tailwind base;
@tailwind components;
@tailwind utilities;

/* Karanlık mod değişimleri için geçiş efektleri */
body {
  @apply transition-colors duration-200 ease-in-out;
  @apply bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100;
}

/* <PERSON><PERSON>er karanlık mod uyarlamaları */
.dark .bg-white {
  @apply bg-gray-800 text-gray-100;
}

.dark .border {
  @apply border-gray-700;
}

.dark .text-gray-900 {
  @apply text-white;
}

.dark input,
.dark select,
.dark textarea {
  @apply bg-gray-700 border-gray-600 placeholder-gray-400;
}

.dark .shadow {
  @apply shadow-gray-900/10;
}

.dark .shadow-sm {
  @apply shadow-gray-900/5;
}

/* Burada mevcutta var olan diğer stiller devam eder */ 