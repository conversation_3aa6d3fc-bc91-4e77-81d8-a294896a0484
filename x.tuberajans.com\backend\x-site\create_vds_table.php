<?php
/**
 * VDS status tablosunu oluştur
 */

header('Content-Type: text/html; charset=utf-8');

try {
    // social_media_analytics veritabanına bağlan
    $db_social = new PDO("mysql:host=**************;dbname=social_media_analytics;charset=utf8mb4", 'root', 'Bebek845396!');
    $db_social->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $db_social->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    
    echo "<h2>🔗 Veritabanına bağlanıldı</h2>";
    
    // VDS status tablosunu oluştur
    $create_vds_table = "
    CREATE TABLE IF NOT EXISTS tiktok_vds_status (
        id INT AUTO_INCREMENT PRIMARY KEY,
        vds_name VARCHAR(100) NOT NULL UNIQUE,
        status ENUM('idle', 'busy', 'offline') DEFAULT 'offline',
        current_request_id INT NULL,
        last_heartbeat TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_status (status),
        INDEX idx_heartbeat (last_heartbeat)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db_social->exec($create_vds_table);
    echo "<p>✅ tiktok_vds_status tablosu oluşturuldu</p>";
    
    // Varsayılan VDS kaydını ekle
    $stmt = $db_social->prepare("INSERT IGNORE INTO tiktok_vds_status (vds_name, status) VALUES (?, ?)");
    $stmt->execute(['VDS-TikTok-01', 'offline']);
    echo "<p>✅ Varsayılan VDS kaydı eklendi</p>";
    
    // Tabloları kontrol et
    echo "<h3>📊 Mevcut Tablolar:</h3>";
    $stmt = $db_social->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>✅ {$table}</li>";
    }
    echo "</ul>";
    
    // VDS durumunu göster
    echo "<h3>🖥️ VDS Durumu:</h3>";
    $stmt = $db_social->prepare("SELECT * FROM tiktok_vds_status");
    $stmt->execute();
    $vds_status = $stmt->fetchAll();
    
    if (empty($vds_status)) {
        echo "<p>❌ Hiç VDS bulunamadı</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>VDS Adı</th><th>Durum</th><th>Son Heartbeat</th><th>Mevcut İstek</th></tr>";
        foreach ($vds_status as $vds) {
            echo "<tr>";
            echo "<td>{$vds['id']}</td>";
            echo "<td>{$vds['vds_name']}</td>";
            echo "<td>{$vds['status']}</td>";
            echo "<td>{$vds['last_heartbeat']}</td>";
            echo "<td>{$vds['current_request_id']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h4>🎉 VDS tablosu hazır!</h4>";
    echo "<p><a href='clear_requests_web.php' style='background: #52c41a; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🧹 Şimdi Temizlik Yap</a></p>";
    
} catch (Exception $e) {
    echo "<h3>❌ Hata: " . $e->getMessage() . "</h3>";
}
?>
