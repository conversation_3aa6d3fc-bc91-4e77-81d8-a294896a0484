<?php
/**
 * Connection Manager ve Rate Limiting Sistemi
 * Backend bağlantı sorunlarını çözmek için geliştirildi
 */

class ConnectionManager {
    private static $instance = null;
    private $connections = [];
    private $maxConnections = 10;
    private $connectionTimeout = 30;
    
    private function __construct() {
        // Singleton pattern
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Veritabanı bağlantısı al (pool'dan)
     */
    public function getConnection($dbname = 'tuberaja_yayinci_takip') {
        $key = md5($dbname);
        
        // Mevcut bağlantı var mı kontrol et
        if (isset($this->connections[$key]) && $this->isConnectionAlive($this->connections[$key])) {
            return $this->connections[$key];
        }
        
        // Yeni bağlantı oluştur
        try {
            require_once __DIR__ . '/../config/config.php';
            
            $pdo_options = [
                PDO::ATTR_TIMEOUT => $this->connectionTimeout,
                PDO::ATTR_PERSISTENT => false,
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ];
            
            $pdo = new PDO("mysql:host=**************;dbname=$dbname;charset=utf8mb4", 'root', 'Bebek845396!', $pdo_options);
            
            $this->connections[$key] = $pdo;
            return $pdo;
            
        } catch (PDOException $e) {
            error_log("Connection Manager PDO Error: " . $e->getMessage());
            throw new Exception("Veritabanı bağlantısı kurulamadı: " . $e->getMessage());
        }
    }
    
    /**
     * Bağlantının canlı olup olmadığını kontrol et
     */
    private function isConnectionAlive($pdo) {
        try {
            $pdo->query('SELECT 1');
            return true;
        } catch (PDOException $e) {
            return false;
        }
    }
    
    /**
     * Tüm bağlantıları kapat
     */
    public function closeAllConnections() {
        $this->connections = [];
    }
}

class RateLimiter {
    private $db;
    
    public function __construct() {
        $this->db = ConnectionManager::getInstance()->getConnection();
    }
    
    /**
     * Rate limiting kontrol et
     * @param string $ip IP adresi
     * @param int $limit Dakikada maksimum istek sayısı
     * @param int $window Zaman penceresi (saniye)
     * @return bool İzin verilip verilmediği
     */
    public function checkLimit($ip, $limit = 60, $window = 60) {
        try {
            // Eski kayıtları temizle
            $this->db->prepare("DELETE FROM request_limits WHERE window_start < DATE_SUB(NOW(), INTERVAL ? SECOND)")
                     ->execute([$window]);
            
            // Mevcut IP için kayıt kontrol et
            $stmt = $this->db->prepare("SELECT request_count FROM request_limits WHERE ip_address = ?");
            $stmt->execute([$ip]);
            $result = $stmt->fetch();
            
            if ($result) {
                if ($result['request_count'] >= $limit) {
                    return false; // Limit aşıldı
                }
                
                // Sayacı artır
                $this->db->prepare("UPDATE request_limits SET request_count = request_count + 1 WHERE ip_address = ?")
                         ->execute([$ip]);
            } else {
                // Yeni kayıt oluştur
                $this->db->prepare("INSERT INTO request_limits (ip_address, request_count) VALUES (?, 1)")
                         ->execute([$ip]);
            }
            
            return true;
            
        } catch (PDOException $e) {
            error_log("Rate Limiter Error: " . $e->getMessage());
            return true; // Hata durumunda izin ver
        }
    }
}

class CacheManager {
    private $db;
    
    public function __construct() {
        $this->db = ConnectionManager::getInstance()->getConnection();
    }
    
    /**
     * Cache'den veri al
     */
    public function get($key) {
        try {
            $stmt = $this->db->prepare("SELECT cache_data FROM api_cache WHERE cache_key = ? AND expires_at > NOW()");
            $stmt->execute([$key]);
            $result = $stmt->fetch();
            
            if ($result) {
                return json_decode($result['cache_data'], true);
            }
            
            return null;
            
        } catch (PDOException $e) {
            error_log("Cache Manager Get Error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Cache'e veri kaydet
     */
    public function set($key, $data, $ttl = 3600) {
        try {
            $expiresAt = date('Y-m-d H:i:s', time() + $ttl);
            
            $stmt = $this->db->prepare("
                INSERT INTO api_cache (cache_key, cache_data, expires_at) 
                VALUES (?, ?, ?) 
                ON DUPLICATE KEY UPDATE 
                cache_data = VALUES(cache_data), 
                expires_at = VALUES(expires_at)
            ");
            
            $stmt->execute([$key, json_encode($data), $expiresAt]);
            return true;
            
        } catch (PDOException $e) {
            error_log("Cache Manager Set Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Süresi dolmuş cache'leri temizle
     */
    public function cleanup() {
        try {
            $this->db->prepare("DELETE FROM api_cache WHERE expires_at < NOW()")->execute();
        } catch (PDOException $e) {
            error_log("Cache Cleanup Error: " . $e->getMessage());
        }
    }
}

class PerformanceMonitor {
    private $db;
    private $startTime;
    private $startMemory;
    private $queryCount = 0;
    
    public function __construct() {
        $this->db = ConnectionManager::getInstance()->getConnection();
        $this->startTime = microtime(true);
        $this->startMemory = memory_get_usage();
    }
    
    /**
     * Query sayısını artır
     */
    public function incrementQueryCount() {
        $this->queryCount++;
    }
    
    /**
     * Performance log'u kaydet
     */
    public function log($endpoint) {
        try {
            $executionTime = (microtime(true) - $this->startTime) * 1000; // milisaniye
            $memoryUsage = memory_get_usage() - $this->startMemory;
            
            $stmt = $this->db->prepare("
                INSERT INTO performance_logs (endpoint, execution_time, memory_usage, query_count) 
                VALUES (?, ?, ?, ?)
            ");
            
            $stmt->execute([$endpoint, $executionTime, $memoryUsage, $this->queryCount]);
            
        } catch (PDOException $e) {
            error_log("Performance Monitor Error: " . $e->getMessage());
        }
    }
}

/**
 * Global helper fonksiyonlar
 */
function getDbConnection($dbname = 'tuberaja_yayinci_takip') {
    return ConnectionManager::getInstance()->getConnection($dbname);
}

function checkRateLimit($limit = 60, $window = 60) {
    $rateLimiter = new RateLimiter();
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    
    if (!$rateLimiter->checkLimit($ip, $limit, $window)) {
        http_response_code(429);
        header('Content-Type: application/json');
        echo json_encode([
            'error' => 'Rate limit exceeded',
            'message' => 'Çok fazla istek gönderiyorsunuz. Lütfen bekleyin.',
            'retry_after' => $window
        ]);
        exit;
    }
}

function getCachedData($key) {
    $cacheManager = new CacheManager();
    return $cacheManager->get($key);
}

function setCachedData($key, $data, $ttl = 3600) {
    $cacheManager = new CacheManager();
    return $cacheManager->set($key, $data, $ttl);
}
?> 