import sys
import logging
import time
import traceback
import threading
import os
from database_manager import DatabaseManager
from scraper_thread import ScraperThread
from status_checker import StatusCheckerThread
from message_sender_thread import MessageSenderThread
from constants import config_manager
from utils import kill_chrome_processes
from datetime import datetime

# Hata ayıklama için geliştirilmiş loglama
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("app.log")
    ]
)

logger = logging.getLogger(__name__)

class MainApp:
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.cycle_running = False
        self.scraper_thread = None
        self.status_checker_thread = None
        self.message_sender_thread = None
        self.duration = 5  # Varsayılan döngü süresi (dakika)
        
        # Chrome için doğru yollar - güncellenmiş Chrome
        self.chrome_binary_path = r"C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe"
        self.chrome_profile_path = r"C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data"
        self.chrome_profile_directory = "Profile 1"
        self.headless = False
        
        # ChromeDriver yolu - tam yol olarak belirt
        self.chromedriver_path = r"C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe"
        
        # Thread yönetimi için değişkenler
        self.active_thread = None  # Şu anda aktif olan thread'i takip et
        
        # Thread sonuçları için güvenli veri deposu
        self.thread_results = {}
        self.thread_results_lock = threading.Lock()
        
        # Monitor için değişkenler
        self.thread_monitor = None
        self.monitor_running = False
        
        self.message_mode = 'both'  # Varsayılan: hem uygun hem uygun elite

    def set_duration(self, duration):
        self.duration = duration

    def set_headless(self, headless):
        self.headless = headless

    def set_message_mode(self, mode):
        self.message_mode = mode or 'both'

    def start_automation(self):
        """Otomasyon döngüsünü başlatır"""
        try:
            if not self.cycle_running:
                self.cycle_running = True
                
                # Thread monitörünü başlat
                self.start_thread_monitor()
                
                # İlk olarak scraper'ı başlat
                self.start_scraper_phase()
                return True
            else:
                logger.info("Otomasyon zaten çalışıyor")
                return False
        except Exception as e:
            logger.error(f"Otomasyon başlatma hatası: {e}")
            logger.error(traceback.format_exc())
            return False

    def start_thread_monitor(self):
        """Thread durumunu izleyen bir monitör başlatır"""
        def monitor_thread():
            self.monitor_running = True
            
            while self.monitor_running and self.cycle_running:
                try:
                    # Scraper thread'i kontrol et
                    if self.active_thread == 'scraper' and self.scraper_thread and not self.scraper_thread.isRunning():
                        
                        # Aktif thread'i hemen güncelle
                        self.active_thread = None
                        
                        # Veritabanından scraper döngü süresi içinde eklenen "Bekleniyor" statüsündeki kullanıcıları çek
                        try:
                            # Scraper döngü süresi kadar geriye giderek "Bekleniyor" statüsündeki kullanıcıları çek
                            recent_usernames = self.db_manager.execute_query(
                                f"""
                                SELECT username, timestamp, status 
                                FROM live_data 
                                WHERE status='Bekleniyor' 
                                AND timestamp >= DATE_SUB(NOW(), INTERVAL {self.duration} MINUTE)
                                ORDER BY timestamp DESC
                                """
                            )
                            
                            if recent_usernames:
                                usernames = [user.get('username') for user in recent_usernames]
                                logger.info(f"MONİTÖR: Veritabanından alınan son {self.duration} dakikadaki kullanıcı sayısı: {len(usernames)}")
                                logger.info(f"MONİTÖR: Örnek kayıtlar: {recent_usernames[:3]}")
                            else:
                                usernames = []
                               
                        except Exception as e:
                            logger.error(f"Veritabanından kullanıcı çekme hatası: {e}")
                            usernames = []
    
                        
                        # Daha uzun bir bekleme süresi
                        time.sleep(3)
                        
                        # Status checker'a geç
                        if usernames and self.cycle_running:
                            logger.info(f"MONİTÖR: Status checker için {len(usernames)} kullanıcı hazırlandı")
                            self.start_status_checker_phase(usernames)
                        elif self.cycle_running:
                            
                            self.start_scraper_phase()
                    
                    # Status checker thread'i kontrol et 
                    elif self.active_thread == 'status_checker' and self.status_checker_thread and not self.status_checker_thread.isRunning():
                        logger.info("!!! MONİTÖR: Status checker thread tamamlandı !!!")
                        
                        # Aktif thread'i hemen güncelle
                        self.active_thread = None
                        logger.info("MONİTÖR: active_thread None olarak ayarlandı")
                        
                        # Daha uzun bir bekleme süresi
                        time.sleep(3)
                        
                        # Message sender'a geç - uygun kullanıcıları tekrar kontrol et
                        if self.cycle_running:
                            logger.info("MONİTÖR: Uygun kullanıcılar kontrol ediliyor...")
                            try:
                                users = self.db_manager.execute_query(f"""
                                    SELECT
                                        id, username, status, message_log, timestamp, viewer_count, link, sorgu_tarihi
                                    FROM live_data
                                    WHERE status='Uygun'
                                    AND sorgu_tarihi >= DATE_SUB(NOW(), INTERVAL {self.duration} MINUTE)
                                """)
                                
                                if users and len(users) > 0:
                                    logger.info(f"MONİTÖR: {len(users)} uygun kullanıcı bulundu, message sender başlatılıyor...")
                                    logger.info("MONİTÖR: Message sender başlatılıyor...")
                                    self.start_message_sender_phase()
                                else:
                                    logger.info("MONİTÖR: Uygun kullanıcı bulunamadı, scraper'a geri dönülüyor...")
                                    self.start_scraper_phase()
                            except Exception as db_err:
                                logger.error(f"Veritabanı sorgusu sırasında hata: {db_err}")
                                logger.info("MONİTÖR: Hata nedeniyle scraper'a geri dönülüyor...")
                                self.start_scraper_phase()
                    
                    # Message sender thread'i kontrol et
                    elif self.active_thread == 'message_sender' and self.message_sender_thread and not self.message_sender_thread.isRunning():
                        logger.info("!!! MONİTÖR: Message sender thread tamamlandı !!!")
                        
                        # Aktif thread'i hemen güncelle
                        self.active_thread = None
                        logger.info("MONİTÖR: active_thread None olarak ayarlandı")
                        
                        # Daha uzun bir bekleme süresi
                        time.sleep(3)
                        
                        # Döngüyü tekrar başlat
                        if self.cycle_running:
                            logger.info("MONİTÖR: Tam döngü tamamlandı, scraper'ı tekrar başlatıyorum")
                            self.start_scraper_phase()
                except Exception as e:
                    logger.error(f"Monitör hatası: {e}")
                    logger.error(traceback.format_exc())
                    # Hata durumunda bir sonraki adım
                    time.sleep(5)  # Hata durumunda biraz daha uzun bekle
                    if self.cycle_running and self.active_thread is None:
                        logger.info("MONİTÖR: Hata sonrası scraper'ı tekrar başlatıyorum")
                        self.start_scraper_phase()
                    
                # Her 1 saniyede bir kontrol et
                time.sleep(1)
                
            logger.info("Thread monitörü durduruldu")
        
        # Monitör thread'i başlat
        self.thread_monitor = threading.Thread(target=monitor_thread)
        self.thread_monitor.daemon = True  # Ana program sonlandığında bu thread de sonlanır
        self.thread_monitor.start()

    def stop_automation(self):
        """Otomasyon döngüsünü durdurur"""
        if self.cycle_running:
            self.cycle_running = False
            self.monitor_running = False  # Monitörü de durdur
            logger.info("Otomasyon durdurma sinyali gönderildi")
            
            # Aktif thread'leri durdur
            if self.scraper_thread and hasattr(self.scraper_thread, 'stop'):
                self.scraper_thread.stop()
                logger.info("Scraper thread durduruldu")
                
            if self.status_checker_thread and hasattr(self.status_checker_thread, 'stop'):
                self.status_checker_thread.stop()
                logger.info("Status checker thread durduruldu")
                
            if self.message_sender_thread and hasattr(self.message_sender_thread, 'stop'):
                self.message_sender_thread.stop()
                logger.info("Message sender thread durduruldu")
                
            logger.info("Tüm thread'ler durduruldu")
        else:
            logger.info("Otomasyon zaten durdurulmuş durumda")

    def start_scraper_phase(self):
        """Scraper aşamasını başlatır"""
        if not self.cycle_running:
            logger.info("Döngü durduruldu, scraper başlatılmıyor")
            return
            
        # Önceki thread temizliği
        if self.scraper_thread:
            try:
                if self.scraper_thread.isRunning():
                    logger.info("Önceki scraper thread hala çalışıyor, durduruluyor...")
                    self.scraper_thread.stop()
                    self.scraper_thread.wait(3000)  # 3 saniye bekle
                
                self.scraper_thread.deleteLater()
                logger.info("Önceki scraper thread temizlendi")
            except Exception as e:
                logger.error(f"Scraper thread temizlenirken hata: {e}")
                
            self.scraper_thread = None
                
        # Thread'i oluştur
        try:
            self.scraper_thread = ScraperThread(
                db_manager=self.db_manager,
                duration=self.duration,
                chrome_binary_path=self.chrome_binary_path,
                chrome_profile_path=self.chrome_profile_path,
                chrome_profile_directory=self.chrome_profile_directory,
                headless=self.headless,
                parent=None
            )
            
            # FinishedSignal bağlantısını kur
            try:
                self.scraper_thread.finishedSignal.connect(self.handle_scraper_finished)
            except Exception as e:
                logger.error(f"Sinyal bağlantı hatası: {e}")
            
            # Thread'i başlat
            self.active_thread = 'scraper'
            self.scraper_thread.start()
            
        except Exception as e:
            logger.error(f"Scraper thread oluşturulurken hata: {e}")
            logger.error(traceback.format_exc())
            self.cycle_running = False

    def start_status_checker_phase(self, users):
        """Status checker aşamasını başlatır"""
        if not self.cycle_running:
            logger.info("Döngü durduruldu, status checker başlatılmıyor")
            return           
        
        # Önceki thread temizliği
        if self.status_checker_thread:
            try:
                if self.status_checker_thread.isRunning():
                    logger.info("Önceki status checker thread hala çalışıyor, durduruluyor...")
                    self.status_checker_thread.stop()
                    self.status_checker_thread.wait(3000)  # 3 saniye bekle
                
                self.status_checker_thread.deleteLater()
            except Exception as e:
                logger.error(f"Status checker thread temizlenirken hata: {e}")
                
            self.status_checker_thread = None
            
        # Yeni thread oluştur
        try:
            self.status_checker_thread = StatusCheckerThread(
                db_manager=self.db_manager,
                publishers=users,
                parent=None
            )
            
            # Sinyali bağla
            self.status_checker_thread.finishedSignal.connect(self.handle_status_checker_finished)
            
            # Thread'i başlat
            self.active_thread = 'status_checker'
            self.status_checker_thread.start()
            
        except Exception as e:
            logger.error(f"Status checker thread oluşturulurken hata: {e}")
            logger.error(traceback.format_exc())
            self.cycle_running = False

    def start_message_sender_phase(self):
        """Message sender aşamasını başlatır"""
        if not self.cycle_running:
            logger.info("Döngü durduruldu, message sender başlatılmıyor")
            return          
        
        # Uygun kullanıcıları veritabanından çek (message_mode'a göre)
        try:
            if self.message_mode == 'none':
                logger.info("Mesaj gönderme modu 'none', mesaj gönderilmeyecek. Scraper'a geçiliyor.")
                self.start_scraper_phase()
                return
            elif self.message_mode == 'suitable':
                status_filter = "status='Uygun'"
            elif self.message_mode == 'elite':
                status_filter = "status='Uygun Elite'"
            else:
                status_filter = "(status='Uygun' OR status='Uygun Elite')"

            # Sadece son döngüdeki uygun kullanıcıları al (scraper duration'ı kadar geriye git)
            users = self.db_manager.execute_query(f"""
                SELECT
                    id,
                    username,
                    status,
                    message_log,
                    timestamp,
                    viewer_count,
                    link,
                    sorgu_tarihi
                FROM live_data
                WHERE {status_filter}
                AND message_log IS NULL
                AND sorgu_tarihi >= DATE_SUB(NOW(), INTERVAL {self.duration} MINUTE)
                ORDER BY
                    CASE
                        WHEN status='Uygun Elite' THEN 1
                        WHEN status='Uygun' THEN 2
                    END,
                    sorgu_tarihi DESC
            """)

            if not users:
                logger.info("Mesaj gönderilecek kullanıcı bulunamadı, scraper'a geri dönülüyor")
                time.sleep(1)
                self.start_scraper_phase()
                return             
            
        except Exception as e:
            logger.error(f"Veritabanı sorgusu sırasında hata: {e}")
            self.start_scraper_phase()
            return
        
        # Önceki thread temizliği
        if self.message_sender_thread:
            try:
                if self.message_sender_thread.isRunning():
                    logger.info("Önceki message sender thread durduruluyor...")
                    self.message_sender_thread.stop()
                    self.message_sender_thread.wait(3000)
                
                self.message_sender_thread.deleteLater()
            except Exception as e:
                logger.error(f"Message sender thread temizlenirken hata: {e}")
                
            self.message_sender_thread = None
            
        # Yeni thread oluştur
        try:
            self.message_sender_thread = MessageSenderThread(
                db_manager=self.db_manager,
                users=users,
                parent=None
            )
            
            self.message_sender_thread.finishedSignal.connect(self.handle_message_sender_finished)
            
            self.active_thread = 'message_sender'
            self.message_sender_thread.start()
            
        except Exception as e:
            logger.error(f"Message sender thread oluşturulurken hata: {e}")
            self.cycle_running = False

    def handle_scraper_finished(self, usernames):
        """
        Scraper tamamlandığında çağrılır - sinyalden
        """
        try:
            logger.info("!!! HANDLE_SCRAPER_FINISHED ÇAĞRILDI (sinyal) !!!")
            logger.info(f"[SCRAPER TAMAMLANDI] Bulunan kullanıcılar: {usernames}")
            # Sinyaller güvenilir değil, monitor zaten doğrudan veritabanından okuyacak
            logger.info(f"Sinyal ile gelen kullanıcı sayısı: {len(usernames)}")
        except Exception as e:
            logger.error(f"Scraper tamamlama işleyicisinde hata: {e}")
            logger.error(traceback.format_exc())

    def handle_status_checker_finished(self, message):
        """
        Status checker thread tamamlandığında çağrılır - sinyalden
        """
        try:
            logger.info("!!! HANDLE_STATUS_CHECKER_FINISHED ÇAĞRILDI (sinyal) !!!")
            logger.info(f"[STATUS CHECKER TAMAMLANDI] Mesaj: {message}")
            # Burada istersen UI güncellemesi veya yeni bir aşama başlatabilirsin
        except Exception as e:
            logger.error(f"Status checker tamamlama işleyicisinde hata: {e}")
            logger.error(traceback.format_exc())

    def handle_message_sender_finished(self, message):
        """
        Message sender thread tamamlandığında çağrılır - sinyalden
        """
        try:
            logger.info("!!! HANDLE_MESSAGE_SENDER_FINISHED ÇAĞRILDI (sinyal) !!!")
            logger.info(f"[MESSAGE SENDER TAMAMLANDI] Mesaj: {message}")
            # Burada istersen UI güncellemesi veya yeni bir aşama başlatabilirsin
        except Exception as e:
            logger.error(f"Message sender tamamlama işleyicisinde hata: {e}")
            logger.error(traceback.format_exc())

if __name__ == "__main__":
    print("Main.py başlatıldı")
    print(f"Argümanlar: {sys.argv}")
    try:
        if len(sys.argv) > 1:
            cmd = sys.argv[1]
            duration = 5
            headless = False
            if cmd == "start":
                if len(sys.argv) > 2:
                    try:
                        duration = int(sys.argv[2])
                    except ValueError:
                        pass
                if len(sys.argv) > 3 and sys.argv[3].lower() == "headless":
                    headless = True
                
                main_app = MainApp()
                main_app.set_duration(duration)
                main_app.set_headless(headless)
                success = main_app.start_automation()
                
                if success:
                    # Ana döngü
                    try:
                        while True:
                            time.sleep(1)
                            # Döngü durdurulduysa çık
                            if not main_app.cycle_running:
                                break
                    except KeyboardInterrupt:
                        main_app.stop_automation()
                    print("Otomasyon elle durduruldu.")
                else:
                    print("Program başlatılamadı!")
                    
            elif cmd == "stop":
                main_app = MainApp()
                main_app.stop_automation()
                print("Otomasyon durduruldu.")
            else:
                print("Kullanım: python main.py [start <süre(dk)> [headless]|stop]")
        else:
            print("Kullanım: python main.py [start <süre(dk)> [headless]|stop]")
    except Exception as e:
        logger.error(f"Ana program hatası: {e}")
        logger.error(traceback.format_exc())
        print(f"HATA: {e}")
        print("Hataları görmek için app.log dosyasını kontrol edin.")