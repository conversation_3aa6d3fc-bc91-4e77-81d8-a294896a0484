import sys
import logging
import time
import traceback
import threading
import subprocess
from database_manager import DatabaseManager
from scraper_thread import ScraperThread
from status_checker import StatusCheckerThread
from message_sender_thread import MessageSenderThread

# Basit loglama - DUPLIKASYON ÖNLEME
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.FileHandler("app.log")
    ]
)

logger = logging.getLogger(__name__)

class MainApp:
    """Basitleştirilmiş MainApp - SIRALI ÇALIŞMA"""

    def __init__(self):
        self.db_manager = DatabaseManager()
        self.cycle_running = False

        # Thread'ler - sadece bir tane aktif olacak
        self.current_thread = None
        self.current_phase = None  # 'scraper', 'status_checker', 'message_sender'

        # Ayarlar
        self.duration = 1  # Varsayılan döngü süresi (dakika)
        self.headless = False
        self.message_mode = 'both'

        # STANDART Chrome yolları - TÜM THREAD'LERDE AYNI
        self.chrome_binary_path = r"C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe"
        self.chrome_profile_path = r"C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data"
        self.chrome_profile_directory = "Profile 1"
        self.chromedriver_path = r"C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe"

        # Sıralı çalışma için
        self.phase_lock = threading.Lock()
        self.monitor_thread = None
        self.monitor_running = False

    def set_duration(self, duration):
        self.duration = duration

    def set_headless(self, headless):
        self.headless = headless

    def set_message_mode(self, mode):
        self.message_mode = mode or 'both'

    def force_close_chrome(self):
        """Chrome işlemlerini zorla kapatır"""
        try:
            import subprocess

            logger.info("🔄 Chrome kapatılıyor...")

            # Tüm Chrome işlemlerini kapat
            subprocess.run(['taskkill', '/f', '/im', 'chrome.exe'],
                         capture_output=True, text=True)
            subprocess.run(['taskkill', '/f', '/im', 'chromedriver.exe'],
                         capture_output=True, text=True)

            time.sleep(3)  # Kapanma için bekle
            logger.info("✅ Chrome kapatıldı")

        except Exception as e:
            logger.warning(f"⚠️ Chrome kapatma hatası: {e}")

    def start_automation(self):
        """Otomasyon döngüsünü başlatır - BASITLEŞTIRILMIŞ"""
        try:
            if self.cycle_running:
                logger.info("⚠️ Otomasyon zaten çalışıyor")
                return False

            self.cycle_running = True
            logger.info("🚀 Otomasyon döngüsü başlatılıyor...")

            # Monitör thread'ini başlat
            self.start_monitor()

            # İlk aşama: Scraper
            self.start_scraper()

            return True

        except Exception as e:
            logger.error(f"❌ Otomasyon başlatma hatası: {e}")
            self.cycle_running = False
            return False

    def start_monitor(self):
        """Basitleştirilmiş monitör - SIRALI ÇALIŞMA + TIMEOUT KONTROLÜ"""
        def monitor_loop():
            self.monitor_running = True
            logger.info("🔍 Monitör başlatıldı")

            last_activity_time = time.time()
            timeout_seconds = 180  # 3 dakika timeout (Status Checker için)

            while self.monitor_running and self.cycle_running:
                try:
                    with self.phase_lock:
                        current_time = time.time()

                        # Mevcut thread'in durumunu kontrol et - HER İKİ THREAD TÜRÜ İÇİN
                        thread_running = False
                        if self.current_thread:
                            # QThread (ScraperThread) için
                            if hasattr(self.current_thread, 'isRunning'):
                                thread_running = self.current_thread.isRunning()
                            # Normal Python Thread (StatusCheckerThread) için
                            elif hasattr(self.current_thread, 'is_alive'):
                                thread_running = self.current_thread.is_alive()

                        if self.current_thread and not thread_running:
                            phase = self.current_phase
                            logger.info(f"🏁 {phase} tamamlandı")

                            # Chrome'u kapat - AŞAMA SONU
                            self.force_close_chrome()

                            # Thread'i temizle
                            self.current_thread = None
                            self.current_phase = None

                            # Aktivite zamanını güncelle
                            last_activity_time = current_time

                            # Aşamalar arası bekleme
                            time.sleep(3)

                            # Sonraki aşamaya geç
                            if phase == 'scraper':
                                self.handle_scraper_completed()
                            elif phase == 'status_checker':
                                self.handle_status_checker_completed()
                            elif phase == 'message_sender':
                                self.handle_message_sender_completed()

                        # TIMEOUT KONTROLÜ - Thread takılmış mı? - HER İKİ THREAD TÜRÜ İÇİN
                        elif self.current_thread and thread_running:
                            if current_time - last_activity_time > timeout_seconds:
                                logger.warning(f"⚠️ {self.current_phase} thread {timeout_seconds} saniyedir yanıt vermiyor, yeniden başlatılıyor...")

                                # Thread'i zorla durdur
                                try:
                                    if hasattr(self.current_thread, 'stop'):
                                        self.current_thread.stop()
                                    self.current_thread.terminate()
                                except:
                                    pass

                                # Chrome'u zorla kapat
                                self.force_close_chrome()

                                # Thread'i temizle
                                self.current_thread = None
                                self.current_phase = None

                                # Scraper'ı yeniden başlat
                                logger.info("🔄 Timeout nedeniyle scraper yeniden başlatılıyor")
                                time.sleep(5)
                                self.start_scraper()

                                # Aktivite zamanını sıfırla
                                last_activity_time = current_time

                except Exception as e:
                    logger.error(f"❌ Monitör hatası: {e}")
                    time.sleep(5)

                time.sleep(2)  # Kontrol aralığı

            logger.info("🔍 Monitör durduruldu")

        # Monitör thread'ini başlat
        self.monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        self.monitor_thread.start()

    def stop_automation(self):
        """Otomasyon döngüsünü durdurur"""
        if self.cycle_running:
            logger.info("⏹️ Otomasyon durduruluyor...")

            self.cycle_running = False
            self.monitor_running = False

            # Aktif thread'i durdur
            if self.current_thread and hasattr(self.current_thread, 'stop'):
                self.current_thread.stop()

            # Chrome'u temizle
            self.force_close_chrome()

            logger.info("✅ Otomasyon durduruldu")
        else:
            logger.info("ℹ️ Otomasyon zaten durdurulmuş")

    def handle_scraper_completed(self):
        """Scraper tamamlandığında çağrılır"""
        if not self.cycle_running:
            return

        try:
            # Veritabanından "Bekleniyor" kullanıcıları al
            users = self.db_manager.execute_query(f"""
                SELECT username FROM live_data
                WHERE status='Bekleniyor'
                AND timestamp >= DATE_SUB(NOW(), INTERVAL {self.duration} MINUTE)
            """)

            if users:
                usernames = [user['username'] for user in users]
                logger.info(f"2️⃣ Status Checker başlatılıyor ({len(usernames)} kullanıcı)")
                self.start_status_checker(usernames)
            else:
                logger.info("🔄 Kullanıcı bulunamadı, scraper tekrar başlatılıyor")
                self.start_scraper()

        except Exception as e:
            logger.error(f"❌ Scraper tamamlama hatası: {e}")
            self.start_scraper()

    def handle_status_checker_completed(self):
        """Status checker tamamlandığında çağrılır"""
        if not self.cycle_running:
            return

        try:
            # Uygun kullanıcıları al
            users = self.db_manager.execute_query(f"""
                SELECT id, username, status FROM live_data
                WHERE (status='Uygun' OR status='Uygun Elite')
                AND message_log IS NULL
                AND sorgu_tarihi >= DATE_SUB(NOW(), INTERVAL {self.duration} MINUTE)
            """)

            if users:
                logger.info(f"3️⃣ Message Sender başlatılıyor ({len(users)} kullanıcı)")
                self.start_message_sender(users)
            else:
                logger.info("🔄 Uygun kullanıcı yok, scraper tekrar başlatılıyor")
                self.start_scraper()

        except Exception as e:
            logger.error(f"❌ Status checker tamamlama hatası: {e}")
            self.start_scraper()

    def handle_message_sender_completed(self):
        """Message sender tamamlandığında çağrılır"""
        if not self.cycle_running:
            return

        logger.info("🔄 Döngü tamamlandı, scraper tekrar başlatılıyor")
        time.sleep(2)  # Kısa bekleme
        self.start_scraper()

    def start_scraper(self):
        """Scraper thread'ini başlatır"""
        if not self.cycle_running:
            return

        with self.phase_lock:
            if self.current_thread is not None:
                logger.warning("⚠️ Başka thread aktif, scraper bekliyor")
                return

            try:
                logger.info(f"1️⃣ Scraper başlatılıyor (süre: {self.duration} dk)")

                self.current_thread = ScraperThread(
                    db_manager=self.db_manager,
                    duration=self.duration,
                    chrome_binary_path=self.chrome_binary_path,
                    chrome_profile_path=self.chrome_profile_path,
                    chrome_profile_directory=self.chrome_profile_directory,
                    headless=self.headless,
                    parent=None
                )

                self.current_phase = 'scraper'
                self.current_thread.start()

            except Exception as e:
                logger.error(f"❌ Scraper başlatma hatası: {e}")
                self.current_thread = None
                self.current_phase = None

    def start_status_checker(self, usernames):
        """Status checker thread'ini başlatır - NORMAL PYTHON THREAD"""
        if not self.cycle_running:
            logger.warning("⚠️ Döngü durduruldu, status checker başlatılmıyor")
            return

        with self.phase_lock:
            if self.current_thread is not None:
                logger.warning("⚠️ Başka thread aktif, status checker bekliyor")
                return

            try:
                logger.info(f"2️⃣ Status Checker başlatılıyor ({len(usernames)} kullanıcı)")
                logger.info(f"📋 Kullanıcılar: {usernames}")

                # Kullanıcı listesini uygun formata çevir
                publishers = [{"username": username} for username in usernames]
                logger.info(f"📋 Publishers formatı: {publishers}")

                # NORMAL PYTHON THREAD OLARAK ÇALIŞTIR
                def run_status_checker():
                    try:
                        logger.info("🔧 Status Checker thread fonksiyonu başladı")

                        # StatusCheckerThread oluştur ama QThread olarak değil
                        status_checker = StatusCheckerThread(
                            db_manager=self.db_manager,
                            publishers=publishers,
                            parent=None
                        )

                        logger.info("🚀 Status Checker run() metodu çağrılıyor...")
                        # Direkt run() metodunu çağır (QThread.start() değil)
                        status_checker.run()
                        logger.info("✅ Status Checker tamamlandı")

                    except Exception as e:
                        logger.error(f"❌ Status Checker thread hatası: {e}")
                        import traceback
                        logger.error(f"❌ Hata detayı: {traceback.format_exc()}")

                # Normal Python thread olarak başlat
                self.current_thread = threading.Thread(target=run_status_checker, daemon=True)
                self.current_phase = 'status_checker'

                logger.info("🚀 Status Checker thread başlatılıyor...")
                self.current_thread.start()
                logger.info("✅ Status Checker thread başlatıldı")

            except Exception as e:
                logger.error(f"❌ Status checker başlatma hatası: {e}")
                import traceback
                logger.error(f"❌ Hata detayı: {traceback.format_exc()}")
                self.current_thread = None
                self.current_phase = None

                # Hata durumunda scraper'a geri dön
                logger.info("🔄 Hata nedeniyle scraper'a geri dönülüyor")
                self.start_scraper()

    def start_message_sender(self, users):
        """Message sender thread'ini başlatır"""
        if not self.cycle_running:
            return

        with self.phase_lock:
            if self.current_thread is not None:
                logger.warning("⚠️ Başka thread aktif, message sender bekliyor")
                return

            try:
                # Mesaj gönderme modunu kontrol et
                if self.message_mode == 'none':
                    logger.info("ℹ️ Mesaj gönderme kapalı, scraper'a geçiliyor")
                    self.start_scraper()
                    return

                logger.info(f"3️⃣ Message Sender başlatılıyor ({len(users)} kullanıcı)")

                self.current_thread = MessageSenderThread(
                    db_manager=self.db_manager,
                    users=users,
                    parent=None
                )

                self.current_phase = 'message_sender'
                self.current_thread.start()

            except Exception as e:
                logger.error(f"❌ Message sender başlatma hatası: {e}")
                self.current_thread = None
                self.current_phase = None

# Gereksiz sinyal işleyicileri kaldırıldı - monitör sistemi kullanılıyor

if __name__ == "__main__":
    print("🚀 TikTok Otomasyon Sistemi - Test Modu")
    print(f"📋 Argümanlar: {sys.argv}")

    try:
        if len(sys.argv) > 1:
            cmd = sys.argv[1]
            duration = 1  # Varsayılan 1 dakika
            headless = False

            if cmd == "start":
                if len(sys.argv) > 2:
                    try:
                        duration = int(sys.argv[2])
                    except ValueError:
                        pass
                if len(sys.argv) > 3 and sys.argv[3].lower() == "headless":
                    headless = True

                print(f"⏱️ Süre: {duration} dakika, Headless: {headless}")

                main_app = MainApp()
                main_app.set_duration(duration)
                main_app.set_headless(headless)

                success = main_app.start_automation()

                if success:
                    print("✅ Otomasyon başlatıldı")
                    try:
                        while main_app.cycle_running:
                            time.sleep(1)
                    except KeyboardInterrupt:
                        print("⏹️ Kullanıcı tarafından durduruldu")
                        main_app.stop_automation()
                else:
                    print("❌ Otomasyon başlatılamadı")

            elif cmd == "stop":
                print("⏹️ Durdurma komutu (test modunda çalışmaz)")
            else:
                print("📖 Kullanım: python main.py [start <süre(dk)> [headless]|stop]")
        else:
            print("📖 Kullanım: python main.py [start <süre(dk)> [headless]|stop]")

    except Exception as e:
        logger.error(f"❌ Ana program hatası: {e}")
        print(f"❌ HATA: {e}")
        print("📋 Detaylar için app.log dosyasını kontrol edin")