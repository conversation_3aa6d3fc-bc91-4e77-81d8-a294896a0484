<?php
// Hata ayıklama
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/dashboard_errors.log');

// CORS başlıkları
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization');
header('Content-Type: application/json; charset=utf-8');

// OPTIONS istekleri için
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(204);
    exit;
}

try {
    require_once __DIR__ . '/../config/config.php';
    $pdo = $db_takip;

    // Yayınc<PERSON> başvuruları
    $stmt = $pdo->query("SELECT COUNT(*) as toplam FROM sitebasvurular");
    $toplamBasvuru = (int)($stmt->fetch(PDO::FETCH_ASSOC)['toplam'] ?? 0);
    $stmt = $pdo->query("SELECT COUNT(*) as okunmamis FROM sitebasvurular WHERE isRead = 0");
    $okunmamisBasvuru = (int)($stmt->fetch(PDO::FETCH_ASSOC)['okunmamis'] ?? 0);

    // Online toplantı talepleri
    $stmt = $pdo->query("SELECT COUNT(*) as toplam FROM onlinetoplantitalep");
    $toplamToplanti = (int)($stmt->fetch(PDO::FETCH_ASSOC)['toplam'] ?? 0);
    $stmt = $pdo->query("SELECT COUNT(*) as okunmamis FROM onlinetoplantitalep WHERE isRead = 0");
    $okunmamisToplanti = (int)($stmt->fetch(PDO::FETCH_ASSOC)['okunmamis'] ?? 0);

    // Geri arama talepleri
    $stmt = $pdo->query("SELECT COUNT(*) as toplam FROM geriaramatalepleri");
    $toplamGeriArama = (int)($stmt->fetch(PDO::FETCH_ASSOC)['toplam'] ?? 0);
    $stmt = $pdo->query("SELECT COUNT(*) as okunmamis FROM geriaramatalepleri WHERE isRead = 0");
    $okunmamisGeriArama = (int)($stmt->fetch(PDO::FETCH_ASSOC)['okunmamis'] ?? 0);

    // İletişim talepleri
    $stmt = $pdo->query("SELECT COUNT(*) as toplam FROM iletisimtalepleri");
    $toplamIletisim = (int)($stmt->fetch(PDO::FETCH_ASSOC)['toplam'] ?? 0);
    $stmt = $pdo->query("SELECT COUNT(*) as okunmamis FROM iletisimtalepleri WHERE isRead = 0");
    $okunmamisIletisim = (int)($stmt->fetch(PDO::FETCH_ASSOC)['okunmamis'] ?? 0);

    // Onaylanan başvuru sayısı
    $stmt = $pdo->query("SELECT COUNT(*) as onaylanan FROM sitebasvurular WHERE approve = 1");
    $onaylananBasvuru = (int)($stmt->fetch(PDO::FETCH_ASSOC)['onaylanan'] ?? 0);

    // Bekleyen başvuru sayısı
    $stmt = $pdo->query("SELECT COUNT(*) as bekleyen FROM sitebasvurular WHERE approve = 0 AND isReject = 0");
    $bekleyenTalep = (int)($stmt->fetch(PDO::FETCH_ASSOC)['bekleyen'] ?? 0);

    // Toplantı talebi (örnek: approveTic = 1 olanlar)
    $stmt = $pdo->query("SELECT COUNT(*) as toplanti FROM sitebasvurular WHERE approveTic = 1");
    $toplantiTalebi = (int)($stmt->fetch(PDO::FETCH_ASSOC)['toplanti'] ?? 0);

    // Geri arama talebi (örnek: isFinal = 1 olanlar)
    $stmt = $pdo->query("SELECT COUNT(*) as geriarama FROM sitebasvurular WHERE isFinal = 1");
    $geriaramaTalebi = (int)($stmt->fetch(PDO::FETCH_ASSOC)['geriarama'] ?? 0);

    // İletişim talebi (örnek: isRead = 0 olanlar)
    $iletisimTalebi = (int)($iletisimTalebi ?? 0);

    // Son başvurular (son 5 başvuru)
    $stmt = $pdo->query("SELECT id, name, tiktok_username, phone, created_at, isRead,
        CASE 
            WHEN approve = 1 THEN 'Onaylandı'
            WHEN isReject = 1 THEN 'Reddedildi'
            ELSE 'Yeni'
        END as status
        FROM sitebasvurular ORDER BY created_at DESC LIMIT 5");
    $recentApplications = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $response = [
        'success' => true,
        'stats' => [
            'okunmamisBasvuru' => $okunmamisBasvuru,
            'toplamBasvuru' => $toplamBasvuru,
            'okunmamisToplanti' => $okunmamisToplanti,
            'toplamToplanti' => $toplamToplanti,
            'okunmamisGeriArama' => $okunmamisGeriArama,
            'toplamGeriArama' => $toplamGeriArama,
            'okunmamisIletisim' => $okunmamisIletisim,
            'toplamIletisim' => $toplamIletisim,
            'basvuruSayisi' => $bekleyenTalep,
            'onaylananBasvuru' => $onaylananBasvuru,
            'toplantıTalebi' => $toplantiTalebi,
            'geriaramaTalebi' => $geriaramaTalebi,
            'iletisimTalebi' => $iletisimTalebi,
            'bekleyenTalep' => $bekleyenTalep
        ],
        'recentApplications' => $recentApplications
    ];
    echo json_encode($response);
    exit;

} catch (Exception $e) {
    error_log("Dashboard API genel hata: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Dashboard verisi alınamadı', 'error' => $e->getMessage()]);
    exit;
} 