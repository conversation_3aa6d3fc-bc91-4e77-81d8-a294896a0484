<?php
// Hata ayıklama - tüm hataları göster
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// CORS için gerekli headerlar
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

// Hata loglama
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/ai_generate_errors.log');

// Tampon temizleme
if (ob_get_level()) {
    ob_end_clean();
}

// OPTIONS isteği için erken yanıt
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header("HTTP/1.1 200 OK");
    exit;
}

// POST isteği değilse hata ver
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        "status" => "error",
        "message" => "Sadece POST istekleri kabul edilir"
    ]);
    exit;
}

// İstek verisini al
$rawData = file_get_contents('php://input');
$data = json_decode($rawData, true);

// İstek verilerini logla
error_log("[" . date('d-M-Y H:i:s') . "] AI generate.php API çağrıldı");
error_log("[" . date('d-M-Y H:i:s') . "] Kategori: " . ($data['category'] ?? 'Belirtilmemiş'));

// İstek parametrelerini kontrol et
if (!isset($data['prompt']) || !isset($data['category'])) {
    echo json_encode([
        "status" => "error",
        "message" => "Gerekli parametreler eksik (prompt, category)"
    ]);
    exit;
}

// Kategori tipine göre mock yanıt oluştur
$prompt = $data['prompt'];
$category = $data['category'];
$response = "";

if ($category === 'pk_matching') {
    $response = '[
        {
            "yayinci1_id": "testuser1", 
            "yayinci2_id": "testuser2",
            "optimal_saat": "20:00"
        },
        {
            "yayinci1_id": "testuser3", 
            "yayinci2_id": "testuser4",
            "optimal_saat": "21:00"
        },
        {
            "yayinci1_id": "testuser5", 
            "yayinci2_id": "testuser6",
            "optimal_saat": "22:00"
        }
    ]';
} else if ($category === 'event_planning') {
    $response = 'Etkinlik planlaması için öneriler:
    
    1. Haftanın en aktif saatleri olan Pazartesi-Çarşamba-Cuma günleri 20:00-22:00 arası etkinlik düzenleyin
    2. Etkinlikten 3 gün önce duyuru yapın ve 1 gün önce hatırlatma gönderin
    3. Kazananlara özel ödüller belirleyin: elmas bonusu, özel rozet veya dijital hediyeler
    4. Etkinlikten sonra performans özeti paylaşarak katılımcıları takdir edin';
} else if ($category === 'whatsapp_message') {
    $response = '🎯 *TuberAjans TikTok Etkinliği* 🎯

    Değerli Yayıncılarımız,
    
    Yeni heyecan verici etkinliğimizi sizlerle paylaşmaktan mutluluk duyarız! 🥳
    
    📱 *Etkinlik Detayları:*
    - 🚀 Büyük PK Turnuvası
    - 📅 7-14 Nisan 2023
    - 🏆 Özel ödüller ve bonuslar
    
    Katılım ve detaylar için yayıncı temsilcinizle iletişime geçebilirsiniz.
    
    Bol şanslar ve bol elmaslar! ✨';
} else {
    $response = 'Bu bir test yanıtıdır. Gerçek AI servisi şu anda bakım modundadır.
    
    İsteğiniz alındı ve işlendi. Kategoriye özel yanıt üretilemedi.
    
    İyi günler dileriz.';
}

// API yanıtını hazırla
$apiResponse = [
    "status" => "success",
    "message" => "Mock AI yanıtı oluşturuldu",
    "response" => $response,
    "model" => "mock-model-v1",
    "processing_time" => rand(1, 4) . "." . rand(100, 999) . " saniye"
];

// JSON olarak döndür
echo json_encode($apiResponse, JSON_UNESCAPED_UNICODE);
?> 