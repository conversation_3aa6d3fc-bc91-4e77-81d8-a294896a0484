<?php
require_once __DIR__ . '/../config/config.php';

// Tüm atamaları getir (sadece admin)
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $userId = requireAdmin();
    
    try {
        $stmt = $db->prepare("
            SELECT a.*, t.title as task_title, u.username as assigned_to_name 
            FROM task_assignments a
            JOIN tasks t ON a.task_id = t.id
            JOIN users u ON a.assigned_to = u.id
            ORDER BY a.created_at DESC
        ");
        $stmt->execute();
        $assignments = $stmt->fetchAll();
        
        jsonResponse($assignments);
    } catch (PDOException $e) {
        jsonResponse(['error' => 'Atamalar getirilirken bir hata oluştu'], 500);
    }
}

// Yeni atama oluştur (sadece admin)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $userId = requireAdmin();
    $data = validateRequest(['task_id', 'assigned_to']);
    
    try {
        // Görev var mı kontrol et
        $stmt = $db->prepare("SELECT id FROM tasks WHERE id = ?");
        $stmt->execute([$data['task_id']]);
        if (!$stmt->fetch()) {
            jsonResponse(['error' => 'Görev bulunamadı'], 404);
        }
        
        // Kullanıcı var mı kontrol et
        $stmt = $db->prepare("SELECT id FROM users WHERE id = ?");
        $stmt->execute([$data['assigned_to']]);
        if (!$stmt->fetch()) {
            jsonResponse(['error' => 'Kullanıcı bulunamadı'], 404);
        }
        
        // Atama zaten var mı kontrol et
        $stmt = $db->prepare("
            SELECT id FROM task_assignments 
            WHERE task_id = ? AND assigned_to = ?
        ");
        $stmt->execute([$data['task_id'], $data['assigned_to']]);
        if ($stmt->fetch()) {
            jsonResponse(['error' => 'Bu görev zaten bu kullanıcıya atanmış'], 400);
        }
        
        // Atamayı oluştur
        $stmt = $db->prepare("
            INSERT INTO task_assignments (task_id, assigned_to, assigned_by) 
            VALUES (?, ?, ?)
        ");
        
        $stmt->execute([
            $data['task_id'],
            $data['assigned_to'],
            $userId
        ]);
        
        $assignmentId = $db->lastInsertId();
        
        // Oluşturulan atamayı getir
        $stmt = $db->prepare("
            SELECT a.*, t.title as task_title, u.username as assigned_to_name 
            FROM task_assignments a
            JOIN tasks t ON a.task_id = t.id
            JOIN users u ON a.assigned_to = u.id
            WHERE a.id = ?
        ");
        $stmt->execute([$assignmentId]);
        $assignment = $stmt->fetch();
        
        jsonResponse($assignment, 201);
    } catch (PDOException $e) {
        jsonResponse(['error' => 'Atama oluşturulurken bir hata oluştu'], 500);
    }
}

// Atama güncelle (sadece admin)
if ($_SERVER['REQUEST_METHOD'] === 'PUT') {
    $userId = requireAdmin();
    $assignmentId = $_GET['id'] ?? null;
    
    if (!$assignmentId) {
        jsonResponse(['error' => 'Atama ID\'si gerekli'], 400);
    }
    
    $data = validateRequest(['status']);
    
    try {
        // Atama var mı kontrol et
        $stmt = $db->prepare("SELECT id FROM task_assignments WHERE id = ?");
        $stmt->execute([$assignmentId]);
        if (!$stmt->fetch()) {
            jsonResponse(['error' => 'Atama bulunamadı'], 404);
        }
        
        // Atamayı güncelle
        $stmt = $db->prepare("
            UPDATE task_assignments 
            SET status = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ");
        
        $stmt->execute([
            $data['status'],
            $assignmentId
        ]);
        
        // Güncellenmiş atamayı getir
        $stmt = $db->prepare("
            SELECT a.*, t.title as task_title, u.username as assigned_to_name 
            FROM task_assignments a
            JOIN tasks t ON a.task_id = t.id
            JOIN users u ON a.assigned_to = u.id
            WHERE a.id = ?
        ");
        $stmt->execute([$assignmentId]);
        $assignment = $stmt->fetch();
        
        jsonResponse($assignment);
    } catch (PDOException $e) {
        jsonResponse(['error' => 'Atama güncellenirken bir hata oluştu'], 500);
    }
}

// Atama sil (sadece admin)
if ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
    $userId = requireAdmin();
    $assignmentId = $_GET['id'] ?? null;
    
    if (!$assignmentId) {
        jsonResponse(['error' => 'Atama ID\'si gerekli'], 400);
    }
    
    try {
        // Atama var mı kontrol et
        $stmt = $db->prepare("SELECT id FROM task_assignments WHERE id = ?");
        $stmt->execute([$assignmentId]);
        if (!$stmt->fetch()) {
            jsonResponse(['error' => 'Atama bulunamadı'], 404);
        }
        
        // Atamayı sil
        $stmt = $db->prepare("DELETE FROM task_assignments WHERE id = ?");
        $stmt->execute([$assignmentId]);
        
        jsonResponse(['message' => 'Atama başarıyla silindi']);
    } catch (PDOException $e) {
        jsonResponse(['error' => 'Atama silinirken bir hata oluştu'], 500);
    }
} 