#!/bin/bash

# TikTok VDS İşleyicisi Başlatma Script'i

echo "🚀 TikTok VDS İşleyicisi başlatılıyor..."

# Klasöre git
cd "$(dirname "$0")"

# Python sanal ortamını kontrol et (varsa)
if [ -d "venv" ]; then
    echo "📦 Python sanal ortamı etkinleştiriliyor..."
    source venv/bin/activate
fi

# Gerekli paketlerin kurulu olup olmadığını kontrol et
echo "🔍 Python paketleri kontrol ediliyor..."
python3 -c "import selenium, mysql.connector" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ Gerekli Python paketleri bulunamadı!"
    echo "📥 Paketler kuruluyor..."
    pip3 install -r requirements.txt
fi

# ChromeDriver'ın var olup olmadığını kontrol et
if [ ! -f "chromedriver" ]; then
    echo "❌ ChromeDriver bulunamadı!"
    echo "📥 ChromeDriver indiriliyor..."
    
    # Chrome versiyonunu al
    CHROME_VERSION=$(google-chrome --version | grep -oP '\d+\.\d+\.\d+')
    echo "🔍 Chrome versiyonu: $CHROME_VERSION"
    
    # ChromeDriver'ı indir
    wget -O chromedriver.zip "https://chromedriver.storage.googleapis.com/LATEST_RELEASE_${CHROME_VERSION%.*}/chromedriver_linux64.zip"
    unzip chromedriver.zip
    chmod +x chromedriver
    rm chromedriver.zip
    
    echo "✅ ChromeDriver indirildi"
fi

# Config dosyasını kontrol et
if [ ! -f "config.py" ]; then
    echo "❌ config.py dosyası bulunamadı!"
    echo "📝 Lütfen config.py dosyasını oluşturun ve veritabanı bilgilerini girin."
    exit 1
fi

# Log klasörünü oluştur
mkdir -p logs

echo "✅ Tüm kontroller tamamlandı"
echo "🎯 TikTok VDS İşleyicisi başlatılıyor..."

# Ana script'i çalıştır
python3 vds-tiktok-processor.py
