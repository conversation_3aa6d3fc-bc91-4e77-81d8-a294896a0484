import React from 'react';
import { FaLock, FaUsers, FaGraduationCap, FaCalendarAlt, FaBullhorn, FaRss, FaClipboardList } from 'react-icons/fa';

interface RestrictedPageProps {
  pageType: 'announcements' | 'courses' | 'events' | 'feed' | 'requests';
}

const RestrictedPage: React.FC<RestrictedPageProps> = ({ pageType }) => {
  const pageConfig = {
    announcements: {
      title: 'Duyurular',
      icon: <FaBullhorn className="w-16 h-16 text-pink-400" />,
      description: 'Tuber Ajans\'ın önemli duyurularını ve güncellemelerini takip edin.',
      features: [
        '<PERSON><PERSON><PERSON> duyuruları ve güncellemeler',
        'Önemli etkinlik bilgilendirmeleri', 
        'Marka iş birliği fırsatları',
        'Platform güncellemeleri'
      ]
    },
    courses: {
      title: 'Eğitimler',
      icon: <FaGraduationCap className="w-16 h-16 text-blue-400" />,
      description: 'Profesyonel içerik üretimi ve sosyal medya stratejileri eğitimleri.',
      features: [
        'TikTok içerik üretimi teknikleri',
        'Viral olma stratejileri',
        'Monetizasyon yöntemleri',
        'Canlı yayın teknikleri'
      ]
    },
    events: {
      title: 'Etkinlikler',
      icon: <FaCalendarAlt className="w-16 h-16 text-purple-400" />,
      description: 'Ajans etkinlikleri, workshop\'lar ve networking fırsatları.',
      features: [
        'Aylık workshop\'lar',
        'Networking etkinlikleri',
        'Marka tanıtım etkinlikleri',
        'Özel davetli toplantılar'
      ]
    },
    feed: {
      title: 'Akış',
      icon: <FaRss className="w-16 h-16 text-green-400" />,
      description: 'Ajans içi güncel gelişmeler ve yayıncı aktiviteleri.',
      features: [
        'Yayıncı başarı hikayeleri',
        'Güncel trend analizleri',
        'Performans güncellemeleri',
        'Topluluk paylaşımları'
      ]
    },
    requests: {
      title: 'Taleplerim',
      icon: <FaClipboardList className="w-16 h-16 text-orange-400" />,
      description: 'Destek talepleri ve ajans hizmetleri başvuruları.',
      features: [
        'Teknik destek talepleri',
        'Marka iş birliği başvuruları',
        'Eğitim talepleri',
        'Genel destek hizmetleri'
      ]
    }
  };

  const config = pageConfig[pageType];

  const handleApplyClick = () => {
    window.open('https://www.tiktok.com/t/ZS2AxxAxT/', '_blank');
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-[#0d0c11] flex items-center justify-center p-4">
      <div className="max-w-2xl w-full">
        <div className="bg-white dark:bg-[#16151c] rounded-2xl shadow-xl overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-pink-500 to-purple-600 px-8 py-6">
            <div className="flex items-center justify-center mb-4">
              {config.icon}
            </div>
            <h1 className="text-3xl font-bold text-white text-center mb-2">
              {config.title}
            </h1>
            <p className="text-pink-100 text-center">
              Ajans Üyelerine Özel İçerik
            </p>
          </div>

          {/* Content */}
          <div className="p-8">
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-gray-100 dark:bg-gray-800 rounded-full mb-4">
                <FaLock className="w-8 h-8 text-gray-400" />
              </div>
              <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">
                Bu Sayfa Ajans Üyelerine Özeldir
              </h2>
              <p className="text-gray-600 dark:text-gray-300 text-lg leading-relaxed">
                {config.description}
              </p>
            </div>

            {/* Features */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4 text-center">
                Bu Bölümde Neler Var?
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {config.features.map((feature, index) => (
                  <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="w-2 h-2 bg-pink-500 rounded-full flex-shrink-0"></div>
                    <span className="text-gray-700 dark:text-gray-300 text-sm">{feature}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* CTA Section */}
            <div className="bg-gradient-to-r from-pink-50 to-purple-50 dark:from-pink-900/20 dark:to-purple-900/20 rounded-xl p-6 border border-pink-200 dark:border-pink-700">
              <div className="text-center">
                <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-2">
                  Tuber Ajans Ailesine Katılın!
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6">
                  Profesyonel içerik üreticisi olarak kariyerinizi bir üst seviyeye taşıyın.
                </p>
                
                <button
                  onClick={handleApplyClick}
                  className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-pink-500 to-purple-600 text-white font-semibold rounded-xl hover:from-pink-600 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                >
                  <FaUsers className="w-5 h-5 mr-2" />
                  Ajansa Başvur
                </button>
                
                <div className="mt-6 pt-6 border-t border-pink-200 dark:border-pink-700">
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">
                    <strong>İletişim Bilgileri:</strong>
                  </p>
                  <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                    <p>📧 E-posta: <EMAIL></p>
                    <p>📞 Telefon: +90 530 915 71 88</p>
                    <p>🌐 Website: tuberajans.com</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RestrictedPage;
