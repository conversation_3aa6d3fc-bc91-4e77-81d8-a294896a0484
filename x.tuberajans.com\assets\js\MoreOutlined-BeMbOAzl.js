import{r as o}from"./vendor-CnpYymF8.js";import{I as s}from"./App-DhIV03Gw.js";var i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M456 231a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"more",theme:"outlined"};function n(){return n=Object.assign?Object.assign.bind():function(r){for(var t=1;t<arguments.length;t++){var e=arguments[t];for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&(r[a]=e[a])}return r},n.apply(this,arguments)}const c=(r,t)=>o.createElement(s,n({},r,{ref:t,icon:i})),l=o.forwardRef(c);export{l as R};
