<?php
require_once '../config/config.php';

try {
    // TikTok Profilleri Tablosu
    $sql = "CREATE TABLE IF NOT EXISTS tiktok_profiles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VA<PERSON>HA<PERSON>(100) NOT NULL UNIQUE,
        nickname <PERSON><PERSON><PERSON><PERSON>(255),
        bio TEXT,
        avatar_url TEXT,
        followers_count BIGINT DEFAULT 0,
        following_count BIGINT DEFAULT 0,
        likes_count BIGINT DEFAULT 0,
        video_count INT DEFAULT 0,
        verified BOOLEAN DEFAULT FALSE,
        is_private BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_username (username),
        INDEX idx_updated_at (updated_at)
    )";
    $pdo->exec($sql);
    echo "✓ tiktok_profiles tablosu oluşturuldu\n";

    // TikTok Profil Analiz Geçmişi
    $sql = "CREATE TABLE IF NOT EXISTS tiktok_profile_history (
        id INT AUTO_INCREMENT PRIMARY KEY,
        profile_id INT NOT NULL,
        followers_count BIGINT DEFAULT 0,
        following_count BIGINT DEFAULT 0,
        likes_count BIGINT DEFAULT 0,
        video_count INT DEFAULT 0,
        total_views BIGINT DEFAULT 0,
        total_engagement BIGINT DEFAULT 0,
        engagement_rate DECIMAL(5,2) DEFAULT 0,
        analysis_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (profile_id) REFERENCES tiktok_profiles(id) ON DELETE CASCADE,
        INDEX idx_profile_date (profile_id, analysis_date)
    )";
    $pdo->exec($sql);
    echo "✓ tiktok_profile_history tablosu oluşturuldu\n";

    // TikTok Videoları Tablosu
    $sql = "CREATE TABLE IF NOT EXISTS tiktok_videos (
        id INT AUTO_INCREMENT PRIMARY KEY,
        profile_id INT NOT NULL,
        video_id VARCHAR(100) NOT NULL,
        video_url TEXT,
        description TEXT,
        thumbnail_url TEXT,
        views_count BIGINT DEFAULT 0,
        likes_count BIGINT DEFAULT 0,
        comments_count BIGINT DEFAULT 0,
        shares_count BIGINT DEFAULT 0,
        saves_count BIGINT DEFAULT 0,
        engagement_rate DECIMAL(5,2) DEFAULT 0,
        published_date TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (profile_id) REFERENCES tiktok_profiles(id) ON DELETE CASCADE,
        UNIQUE KEY unique_video (profile_id, video_id),
        INDEX idx_profile_published (profile_id, published_date),
        INDEX idx_engagement (engagement_rate DESC)
    )";
    $pdo->exec($sql);
    echo "✓ tiktok_videos tablosu oluşturuldu\n";

    // TikTok Hashtag'ler Tablosu
    $sql = "CREATE TABLE IF NOT EXISTS tiktok_hashtags (
        id INT AUTO_INCREMENT PRIMARY KEY,
        hashtag VARCHAR(100) NOT NULL UNIQUE,
        usage_count INT DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_hashtag (hashtag),
        INDEX idx_usage_count (usage_count DESC)
    )";
    $pdo->exec($sql);
    echo "✓ tiktok_hashtags tablosu oluşturuldu\n";

    // Video-Hashtag İlişki Tablosu
    $sql = "CREATE TABLE IF NOT EXISTS tiktok_video_hashtags (
        id INT AUTO_INCREMENT PRIMARY KEY,
        video_id INT NOT NULL,
        hashtag_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (video_id) REFERENCES tiktok_videos(id) ON DELETE CASCADE,
        FOREIGN KEY (hashtag_id) REFERENCES tiktok_hashtags(id) ON DELETE CASCADE,
        UNIQUE KEY unique_video_hashtag (video_id, hashtag_id)
    )";
    $pdo->exec($sql);
    echo "✓ tiktok_video_hashtags tablosu oluşturuldu\n";

    // Analiz İstekleri Tablosu (VDS ile Web Sitesi arası iletişim)
    $sql = "CREATE TABLE IF NOT EXISTS tiktok_analysis_requests (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(100) NOT NULL,
        ip_address VARCHAR(45),
        user_id INT NULL,
        status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
        error_message TEXT NULL,
        processing_time INT NULL,
        result_data LONGTEXT NULL, -- JSON formatında sonuç verisi
        progress_percentage INT DEFAULT 0, -- İlerleme yüzdesi
        current_step VARCHAR(255) NULL, -- Hangi adımda olduğu
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP NULL,
        started_at TIMESTAMP NULL,
        INDEX idx_username_date (username, created_at),
        INDEX idx_ip_date (ip_address, created_at),
        INDEX idx_status (status),
        INDEX idx_pending (status, created_at)
    )";
    $pdo->exec($sql);
    echo "✓ tiktok_analysis_requests tablosu oluşturuldu\n";

    // VDS Durum Tablosu (VDS'nin çalışıp çalışmadığını takip için)
    $sql = "CREATE TABLE IF NOT EXISTS tiktok_vds_status (
        id INT AUTO_INCREMENT PRIMARY KEY,
        vds_name VARCHAR(100) NOT NULL UNIQUE,
        status ENUM('online', 'offline', 'busy') DEFAULT 'offline',
        last_ping TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        current_request_id INT NULL,
        total_processed INT DEFAULT 0,
        total_errors INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_status (status),
        INDEX idx_last_ping (last_ping)
    )";
    $pdo->exec($sql);
    echo "✓ tiktok_vds_status tablosu oluşturuldu\n";

    // TikTok İstekleri Tablosu (VDS ile iletişim için)
    $sql = "CREATE TABLE IF NOT EXISTS tiktok_requests (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(100) NOT NULL,
        status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
        current_step VARCHAR(255) NULL,
        progress INT DEFAULT 0,
        error_message TEXT NULL,
        result_id INT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_status (status),
        INDEX idx_username (username),
        INDEX idx_created_at (created_at)
    )";
    $pdo->exec($sql);
    echo "✓ tiktok_requests tablosu oluşturuldu\n";

    // TikTok Analiz Sonuçları Tablosu
    $sql = "CREATE TABLE IF NOT EXISTS tiktok_analysis (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(100) NOT NULL,
        nickname VARCHAR(255),
        bio TEXT,
        avatar_url TEXT,
        followers_text VARCHAR(50),
        followers_count BIGINT DEFAULT 0,
        following_text VARCHAR(50),
        following_count BIGINT DEFAULT 0,
        likes_text VARCHAR(50),
        likes_count BIGINT DEFAULT 0,
        videos_data LONGTEXT,
        total_views BIGINT DEFAULT 0,
        total_video_likes BIGINT DEFAULT 0,
        total_comments BIGINT DEFAULT 0,
        total_shares BIGINT DEFAULT 0,
        total_saves BIGINT DEFAULT 0,
        total_engagement BIGINT DEFAULT 0,
        engagement_rate DECIMAL(5,2) DEFAULT 0,
        average_views BIGINT DEFAULT 0,
        average_likes BIGINT DEFAULT 0,
        average_comments BIGINT DEFAULT 0,
        average_shares BIGINT DEFAULT 0,
        average_saves BIGINT DEFAULT 0,
        most_used_hashtags LONGTEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_username (username),
        INDEX idx_created_at (created_at)
    )";
    $pdo->exec($sql);
    echo "✓ tiktok_analysis tablosu oluşturuldu\n";

    echo "\n🎉 Tüm TikTok analizi tabloları başarıyla oluşturuldu!\n";
    echo "\nKullanım:\n";
    echo "1. ChromeDriver'ı indirin ve /drivers/ klasörüne koyun\n";
    echo "2. Composer ile Selenium WebDriver kurun: composer require php-webdriver/webdriver\n";
    echo "3. TikTok profil analizi artık gerçek verilerle çalışacak\n";

} catch (Exception $e) {
    echo "❌ Hata: " . $e->getMessage() . "\n";
}
?>
