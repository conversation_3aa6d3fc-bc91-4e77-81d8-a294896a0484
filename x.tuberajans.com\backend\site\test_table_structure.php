<?php
// <PERSON><PERSON><PERSON> ya<PERSON><PERSON>ını kontrol et
require_once __DIR__ . '/../config/config.php';

try {
    $pdo = $db_takip;
    
    // Tablo var mı kontrol et
    $stmt = $pdo->query("SHOW TABLES LIKE 'sitebasvurular'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        echo "❌ sitebasvurular tablosu bulunamadı!\n";
        
        // Tablo oluştur
        $createSQL = "CREATE TABLE `sitebasvurular` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(100) NOT NULL,
            `surname` varchar(100) DEFAULT NULL,
            `phone` varchar(20) DEFAULT NULL,
            `mail` varchar(150) DEFAULT NULL,
            `tiktok_username` varchar(100) NOT NULL,
            `instagram_username` varchar(100) DEFAULT NULL,
            `yayin_suresi` varchar(50) DEFAULT NULL,
            `follower_range` varchar(50) DEFAULT NULL,
            `deneyim` text,
            `approve` tinyint(1) DEFAULT '0',
            `approveTic` tinyint(1) DEFAULT '0',
            `ip` varchar(50) DEFAULT NULL,
            `unixts` bigint(20) DEFAULT NULL,
            `isRead` tinyint(1) DEFAULT '0',
            `isAcademy` tinyint(1) DEFAULT '0',
            `isFinal` tinyint(1) DEFAULT '0',
            `isReject` tinyint(1) DEFAULT '0',
            `smsRed` tinyint(1) DEFAULT '0',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci";
        
        $pdo->exec($createSQL);
        echo "✅ sitebasvurular tablosu oluşturuldu!\n";
    } else {
        echo "✅ sitebasvurular tablosu mevcut\n";
    }
    
    // Tablo yapısını göster
    $stmt = $pdo->query("DESCRIBE sitebasvurular");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\n📋 Tablo Yapısı:\n";
    foreach ($columns as $column) {
        echo "- {$column['Field']} ({$column['Type']}) - {$column['Null']} - {$column['Default']}\n";
    }
    
    // Kayıt sayısını göster
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM sitebasvurular");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "\n📊 Toplam kayıt sayısı: $count\n";
    
} catch (Exception $e) {
    echo "❌ Hata: " . $e->getMessage() . "\n";
}
?>
