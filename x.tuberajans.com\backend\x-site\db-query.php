<?php

// Öncelikle buffer'ı temizle
if (ob_get_level()) ob_end_clean();
ob_start();

header('Content-Type: application/json; charset=utf-8');

// CORS başlıkları
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

error_reporting(E_ALL);
ini_set('display_errors', 0); // Hataları log'a yazalım, ekrana değil
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/ai_query_errors.log');

// Config dosyasını dahil et (veritabanı bağlantısı için)
require_once __DIR__ . '/../../../config/config.php';

// OPTIONS isteklerini hemen yanıtla
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Content-Type: application/json; charset=utf-8');
    http_response_code(200);
    exit;
}

// Kesin olarak Content-Type ayarla
header('Content-Type: application/json; charset=utf-8');

// Giriş üstü detaylı loglama
$log_prefix = '[AI-DB-Query] ';
error_log($log_prefix . "İstek alındı: " . $_SERVER['REQUEST_METHOD']);

// Sadece POST isteklerini kabul et
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    error_log($log_prefix . "Hatalı method: " . $_SERVER['REQUEST_METHOD']);
    http_response_code(405);
    echo json_encode(['error' => 'Sadece POST metodu desteklenmektedir']);
    exit();
}

// Gelen JSON verisini al
$input_data = file_get_contents('php://input');
error_log($log_prefix . "Ham gelen veri (ilk 100 karakter): " . substr($input_data, 0, 100));
$received_data = json_decode($input_data, true);

// JSON formatını kontrol et
if (json_last_error() !== JSON_ERROR_NONE) {
    error_log($log_prefix . "Geçersiz JSON formatı: " . json_last_error_msg() . ", Ham veri: " . $input_data);
    http_response_code(400);
    echo json_encode(['error' => 'Geçersiz JSON formatı: ' . json_last_error_msg()]);
    exit();
}

// Gerekli parametreleri kontrol et
if (!isset($received_data['query']) || empty($received_data['query'])) {
    error_log($log_prefix . "Eksik parametre: query");
    http_response_code(400);
    echo json_encode(['error' => 'Eksik parametre: query']);
    exit();
}

// Sorguyu al
$query = $received_data['query'];
error_log($log_prefix . "Sorgu: " . $query);

// İzin verilen tablolar listesi (güvenlik için sadece belirli tablolara erişime izin ver)
$allowed_tables = [
    'yayinci', 'performans', 'weekly_archive', 'weekly_tasks', 'gorevler',
    'etkinlikler', 'etkinlik_katilimcilar', 'pk_eslesmeleri', 'publisher_info', 
    'influencer_info', 'users'
];

// Yalnızca SELECT sorgularına izin ver
if (!preg_match('/^\s*SELECT\s+/i', $query)) {
    error_log($log_prefix . "Sadece SELECT sorgularına izin verilir");
    http_response_code(403);
    echo json_encode(['error' => 'Sadece SELECT sorgularına izin verilir']);
    exit();
}

// Sorgunun izin verilen tablolarda olup olmadığını kontrol et
$is_allowed = false;
foreach ($allowed_tables as $table) {
    if (preg_match('/\bFROM\s+' . $table . '\b/i', $query)) {
        $is_allowed = true;
        break;
    }
}

if (!$is_allowed) {
    error_log($log_prefix . "Sorgu izin verilen tablolarda değil");
    http_response_code(403);
    echo json_encode(['error' => 'Sorgu izin verilen tablolarda değil']);
    exit();
}

// Parametreleri güvenli şekilde al
$params = isset($received_data['params']) ? $received_data['params'] : [];
error_log($log_prefix . "Parametreler: " . json_encode($params));

try {
    // Sorguyu çalıştır
    $stmt = $db->prepare($query);
    $stmt->execute($params);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    error_log($log_prefix . "Sorgu başarıyla çalıştırıldı, " . count($results) . " sonuç döndü");
    
    // Sonuçları döndür
    echo json_encode([
        'success' => true,
        'data' => $results,
        'count' => count($results)
    ]);
} catch (PDOException $e) {
    error_log($log_prefix . "Sorgu hatası: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'error' => 'Veritabanı hatası: ' . $e->getMessage(),
        'success' => false
    ]);
}
?> 