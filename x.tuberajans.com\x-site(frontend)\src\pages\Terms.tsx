import React from 'react';
import { Typography, Layout } from 'antd';

const { Title, Paragraph } = Typography;
const { Content } = Layout;

const Terms: React.FC = () => {
  return (
    <Layout>
      <Content style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
        <Title level={1}>TuberOPS - Terms of Service</Title>
        
        <Paragraph>
          This document outlines the Terms of Service ("Terms") for the internal use of the TuberOPS platform, 
          located at x.tuberajans.com. This platform is designed exclusively for authorized personnel of Tuber Ajans.
        </Paragraph>

        <Title level={2}>1. Purpose:</Title>
        <Paragraph>
          TuberOPS is developed to manage TikTok live streamers, monitor weekly performance, 
          and facilitate communication via TikTok direct messages.
        </Paragraph>

        <Title level={2}>2. Access & Usage:</Title>
        <Paragraph>
          Access to this platform is restricted to agency administrators and assigned staff. 
          Unauthorized access is strictly prohibited.
        </Paragraph>

        <Title level={2}>3. Data Usage:</Title>
        <Paragraph>
          Data accessed through TikTok APIs (such as user metrics and direct messages) will be used solely 
          for performance analysis, content development, and internal support. No data will be shared with third parties.
        </Paragraph>

        <Title level={2}>4. Limitation of Use:</Title>
        <Paragraph>
          Users may not attempt to reverse engineer, resell, or expose any part of the system externally.
        </Paragraph>

        <Title level={2}>5. Changes:</Title>
        <Paragraph>
          Tuber Ajans reserves the right to update these Terms at any time. 
          Continued use of the platform after updates constitutes acceptance.
        </Paragraph>

        <div style={{ marginTop: '24px' }}>
          <Paragraph>
            If you have any questions, please contact: <a href="mailto:<EMAIL>"><EMAIL></a>
          </Paragraph>
          
          <Paragraph>
            Last Updated: {new Date().toLocaleDateString()}
          </Paragraph>
        </div>
      </Content>
    </Layout>
  );
};

export default Terms; 