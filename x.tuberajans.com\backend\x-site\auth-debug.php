﻿<?php
// Comprehensive Auth Debug Tool
header('Content-Type: application/json; charset=utf-8');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(204);
    exit();
}

$debug_info = [
    'status' => 'ok',
    'timestamp' => date('Y-m-d H:i:s'),
    'method' => $_SERVER['REQUEST_METHOD'],
    'headers' => getallheaders(),
    'server_info' => [
        'HTTP_ORIGIN' => $_SERVER['HTTP_ORIGIN'] ?? 'not set',
        'REQUEST_URI' => $_SERVER['REQUEST_URI'] ?? 'not set',
        'REMOTE_ADDR' => $_SERVER['REMOTE_ADDR'] ?? 'not set',
        'HTTP_USER_AGENT' => $_SERVER['HTTP_USER_AGENT'] ?? 'not set'
    ]
];

// Test database connection
try {
    require_once __DIR__ . '/../config/config.php';
    $debug_info['database']['status'] = 'Connected';
    
    // Test main database
    $stmt = $db_takip->query("SELECT COUNT(*) as user_count FROM users");
    $result = $stmt->fetch();
    $debug_info['database']['user_count'] = $result['user_count'];
    
    // Test user_tokens table
    $stmt = $db_takip->query("SELECT COUNT(*) as token_count FROM user_tokens WHERE expires_at > NOW()");
    $result = $stmt->fetch();
    $debug_info['database']['active_tokens'] = $result['token_count'];
    
} catch (Exception $e) {
    $debug_info['database']['status'] = 'Error';
    $debug_info['database']['error'] = $e->getMessage();
}

// Test POST data if available
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $raw_input = file_get_contents('php://input');
    $debug_info['post_data'] = [
        'raw' => $raw_input,
        'parsed' => json_decode($raw_input, true)
    ];
}

echo json_encode($debug_info, JSON_PRETTY_PRINT);
?>
