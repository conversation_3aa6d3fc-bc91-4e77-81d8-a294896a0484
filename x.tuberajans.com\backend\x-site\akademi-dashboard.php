<?php
/**
 * Akademi Dashboard API
 * 
 * Gerçek veritabanı verisiyle
 */

// Akademi Dashboard API - Gerçek veritabanı verisiyle
require_once '../../config/config.php';

// CORS için header ayarları
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json");

// OPTIONS isteği için erken yanıt ver
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Gelen istek için parametre kontrolü
$timeRange = isset($_GET['range']) ? $_GET['range'] : 'week';

try {
    // Toplam kullanıcı
    $stmt = $pdo->query("SELECT COUNT(*) FROM tuberaja_yayinci_akademi.users");
    $totalUsers = (int)$stmt->fetchColumn();

    // Aktif kullanıcılar (ör: status='active' veya 'approved')
    $stmt = $pdo->query("SELECT COUNT(*) FROM tuberaja_yayinci_akademi.users WHERE status='active' OR status='approved'");
    $activeUsers = (int)$stmt->fetchColumn();

    // Toplam kurs (lessons tablosu)
    $stmt = $pdo->query("SELECT COUNT(*) FROM tuberaja_yayinci_akademi.lessons");
    $totalCourses = (int)$stmt->fetchColumn();

    // Toplam ders (lesson_categories tablosu)
    $stmt = $pdo->query("SELECT COUNT(*) FROM tuberaja_yayinci_akademi.lesson_categories");
    $totalLessons = (int)$stmt->fetchColumn();

    // Açık destek talepleri
    $stmt = $pdo->query("SELECT COUNT(*) FROM tuberaja_yayinci_akademi.support_tickets WHERE status='open' OR status='in_progress' OR status='waiting'");
    $supportTickets = (int)$stmt->fetchColumn();

    // AI sorguları (örnek: ai_conversations tablosu varsa)
    $aiQueries = 0;
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM tuberaja_yayinci_akademi.ai_conversations");
        $aiQueries = (int)$stmt->fetchColumn();
    } catch (Exception $e) {}

    // Son kayıt olan kullanıcılar
    $stmt = $pdo->query("SELECT id, username AS name, email, created_at AS registrationDate, updated_at AS lastLogin FROM tuberaja_yayinci_akademi.users ORDER BY created_at DESC LIMIT 4");
    $recentUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Son destek talepleri
    $stmt = $pdo->query("SELECT id, subject AS title, status, priority, created_at AS createdAt FROM tuberaja_yayinci_akademi.support_tickets ORDER BY created_at DESC LIMIT 3");
    $recentTickets = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $stats = [
        'totalUsers' => $totalUsers,
        'activeUsers' => $activeUsers,
        'totalCourses' => $totalCourses,
        'totalLessons' => $totalLessons,
        'supportTickets' => $supportTickets,
        'aiQueries' => $aiQueries
    ];

    $responseData = [
        'success' => true,
        'timeRange' => $timeRange,
        'stats' => $stats,
        'recentUsers' => $recentUsers,
        'recentTickets' => $recentTickets
    ];
    echo json_encode($responseData);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Dashboard verisi alınamadı', 'detail' => $e->getMessage()]);
} 