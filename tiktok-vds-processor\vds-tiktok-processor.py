#!/usr/bin/env python3
"""
TikTok Analiz İ<PERSON> - VDS Tarafı
Bu script VDS sunucusunda çalışır ve veritabanındaki pending iste<PERSON><PERSON> <PERSON><PERSON>.
"""

import time
import json
import logging
import mysql.connector
import os
import random
import pickle
import re
from collections import Counter
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.action_chains import ActionChains
import re

# Config dosyasını import et
try:
    from config import (DB_CONFIG, CHROME_DRIVER_PATH, VDS_NAME, LOG_FILE,
                       MAX_VIDEOS_PER_PROFILE, REQUEST_TIMEOUT, POLL_INTERVAL,
                       RETRY_INTERVAL, CHROME_OPTIONS, CHROME_PREFS,
                       CHROME_BINARY_PATH, CHROME_PROFILE_PATH, CHROME_PROFILE_DIRECTORY)
except ImportError:
    print("HATA: config.py dosyası bulunamadı!")
    print("Lütfen config.py dosyasını düzenleyip veritabanı bilgilerini girin.")
    exit(1)

# Log klasörünü oluştur
os.makedirs(os.path.dirname(LOG_FILE), exist_ok=True)

# Logging ayarları
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TikTokVDSProcessor:
    def __init__(self):
        # Config'den ayarları al
        self.db_config = DB_CONFIG
        self.chrome_driver_path = CHROME_DRIVER_PATH
        self.vds_name = VDS_NAME
        self.max_videos = MAX_VIDEOS_PER_PROFILE

        self.db = None
        self.driver = None
        self.cookies_file = "tiktok_cookies.pkl"  # Cookies dosyası

        logger.info(f"TikTok VDS İşleyicisi başlatılıyor: {self.vds_name}")

    def connect_database(self):
        """Veritabanına bağlan"""
        try:
            self.db = mysql.connector.connect(**self.db_config)
            logger.info(f"Veritabanına bağlandı: {self.db_config['host']}")
            return True
        except Exception as e:
            logger.error(f"Veritabanı bağlantı hatası: {e}")
            return False

    def update_vds_status(self, status, current_request_id=None):
        """VDS durumunu güncelle"""
        try:
            cursor = self.db.cursor()
            cursor.execute("""
                INSERT INTO vds_status (vds_name, status, current_request_id, last_ping)
                VALUES (%s, %s, %s, NOW())
                ON DUPLICATE KEY UPDATE
                status = VALUES(status),
                current_request_id = VALUES(current_request_id),
                last_ping = NOW(),
                total_processed = total_processed + CASE WHEN %s = 'online' AND status = 'busy' THEN 1 ELSE 0 END
            """, (self.vds_name, status, current_request_id, status))
            self.db.commit()
        except Exception as e:
            logger.error(f"VDS durum güncelleme hatası: {e}")

    def get_pending_request(self):
        """Bekleyen isteği al"""
        try:
            cursor = self.db.cursor(dictionary=True)
            cursor.execute("""
                SELECT * FROM tiktok_requests
                WHERE status = 'pending'
                ORDER BY created_at ASC
                LIMIT 1
            """)
            result = cursor.fetchone()
            if result and 'video_count' not in result:
                result['video_count'] = 10  # Default value if column doesn't exist
            return result
        except Exception as e:
            logger.error(f"Bekleyen istek alma hatası: {e}")
            return None

    def update_request_status(self, request_id, status, progress=None, current_step=None, error_message=None):
        """İstek durumunu güncelle"""
        try:
            cursor = self.db.cursor()

            if status == 'processing':
                cursor.execute("""
                    UPDATE tiktok_requests
                    SET status = %s, progress = %s, current_step = %s,
                        started_at = COALESCE(started_at, NOW())
                    WHERE id = %s
                """, (status, progress or 0, current_step, request_id))
            elif status == 'completed':
                cursor.execute("""
                    UPDATE tiktok_requests
                    SET status = %s, progress = 100, completed_at = NOW()
                    WHERE id = %s
                """, (status, request_id))
            elif status == 'failed':
                cursor.execute("""
                    UPDATE tiktok_requests
                    SET status = %s, error_message = %s, completed_at = NOW()
                    WHERE id = %s
                """, (status, error_message, request_id))

            self.db.commit()
            logger.info(f"İstek {request_id} durumu güncellendi: {status}")

        except Exception as e:
            logger.error(f"İstek durum güncelleme hatası: {e}")

    def save_analysis_result(self, request_id, profile_data):
        """Analiz sonucunu veritabanına kaydet"""
        try:
            cursor = self.db.cursor()

            # tiktok_analysis tablosuna kaydet
            cursor.execute("""
                INSERT INTO tiktok_analysis (
                    username, nickname, bio, avatar_url,
                    followers_text, followers_count, following_text, following_count,
                    likes_text, likes_count, total_videos, videos_data,
                    total_views, total_video_likes, total_comments, total_shares, total_saves,
                    total_engagement, engagement_rate,
                    average_views, average_likes, average_comments, average_shares, average_saves,
                    most_used_hashtags, created_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
            """, (
                profile_data['username'],
                profile_data.get('nickname', ''),
                profile_data.get('bio', ''),
                profile_data.get('avatar_url', ''),
                profile_data.get('followers', '0'),
                profile_data.get('followers_count', 0),
                profile_data.get('following', '0'),
                profile_data.get('following_count', 0),
                profile_data.get('likes', '0'),
                profile_data.get('likes_count', 0),
                profile_data.get('total_videos', 0),
                json.dumps(profile_data.get('videos', []), ensure_ascii=False),
                profile_data.get('total_views', 0),
                profile_data.get('total_likes', 0),
                profile_data.get('total_comments', 0),
                profile_data.get('total_shares', 0),
                profile_data.get('total_saves', 0),
                profile_data.get('total_engagement', 0),
                profile_data.get('total_engagement_rate', 0),
                profile_data.get('average_views', 0),
                profile_data.get('average_likes', 0),
                profile_data.get('average_comments', 0),
                profile_data.get('average_shares', 0),
                profile_data.get('average_saves', 0),
                json.dumps(profile_data.get('most_used_hashtags', []), ensure_ascii=False)
            ))

            analysis_id = cursor.lastrowid

            # tiktok_requests tablosunda result_id'yi güncelle
            cursor.execute("""
                UPDATE tiktok_requests
                SET result_id = %s
                WHERE id = %s
            """, (analysis_id, request_id))

            self.db.commit()
            logger.info(f"Analiz sonucu kaydedildi: Analysis ID {analysis_id}")
            return analysis_id

        except Exception as e:
            logger.error(f"Analiz sonucu kaydetme hatası: {e}")
            return None

    def setup_chrome_driver(self):
        """Chrome WebDriver'ı ayarla (Ultra Anti-Detection + CAPTCHA Bypass)"""
        try:
            chrome_options = Options()

            # Chrome profil ayarları (Yayıncı Avı ile aynı)
            chrome_options.add_argument(f"--user-data-dir={CHROME_PROFILE_PATH}")
            chrome_options.add_argument(f"--profile-directory={CHROME_PROFILE_DIRECTORY}")
            chrome_options.binary_location = CHROME_BINARY_PATH

            # Config'den seçenekleri ekle
            for option in CHROME_OPTIONS:
                chrome_options.add_argument(option)

            # Ultra anti-detection seçenekleri
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_argument("--disable-extensions-file-access-check")
            chrome_options.add_argument("--disable-extensions-http-throttling")
            chrome_options.add_argument("--disable-ipc-flooding-protection")
            chrome_options.add_argument("--disable-renderer-backgrounding")
            chrome_options.add_argument("--disable-backgrounding-occluded-windows")
            chrome_options.add_argument("--disable-features=TranslateUI")
            chrome_options.add_argument("--disable-features=VizDisplayCompositor")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu-sandbox")
            chrome_options.add_argument("--disable-software-rasterizer")
            chrome_options.add_argument("--disable-background-timer-throttling")
            chrome_options.add_argument("--disable-backgrounding-occluded-windows")
            chrome_options.add_argument("--disable-renderer-backgrounding")
            chrome_options.add_argument("--disable-field-trial-config")
            chrome_options.add_argument("--disable-back-forward-cache")
            chrome_options.add_argument("--disable-hang-monitor")
            chrome_options.add_argument("--disable-prompt-on-repost")
            chrome_options.add_argument("--disable-sync")
            chrome_options.add_argument("--disable-translate")
            chrome_options.add_argument("--disable-web-security")
            chrome_options.add_argument("--disable-features=VizDisplayCompositor,VizHitTestSurfaceLayer")
            chrome_options.add_argument("--disable-component-extensions-with-background-pages")
            chrome_options.add_argument("--disable-default-apps")
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-plugins")
            chrome_options.add_argument("--disable-plugins-discovery")
            chrome_options.add_argument("--disable-preconnect")
            chrome_options.add_argument("--disable-print-preview")
            chrome_options.add_argument("--disable-setuid-sandbox")
            chrome_options.add_argument("--disable-speech-api")
            chrome_options.add_argument("--disable-file-system")
            chrome_options.add_argument("--disable-presentation-api")
            chrome_options.add_argument("--disable-permissions-api")
            chrome_options.add_argument("--disable-new-zip-unpacker")
            chrome_options.add_argument("--disable-media-session-api")

            # User-Agent spoofing
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

            # Otomasyon tespitini engelleme
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation", "enable-logging"])
            chrome_options.add_experimental_option("useAutomationExtension", False)

            # Ultra gelişmiş prefs ayarları
            enhanced_prefs = {
                **CHROME_PREFS,
                'profile.default_content_setting_values.notifications': 2,
                'profile.default_content_settings.popups': 0,
                'profile.managed_default_content_settings.images': 1,
                'profile.default_content_setting_values.media_stream': 2,
                'profile.content_settings.exceptions.automatic_downloads.*.setting': 1,
                'profile.default_content_setting_values.geolocation': 2,
                'profile.default_content_setting_values.camera': 2,
                'profile.default_content_setting_values.microphone': 2,
                'profile.default_content_setting_values.plugins': 1,
                'profile.content_settings.plugin_whitelist.adobe-flash-player': 1,
                'profile.content_settings.exceptions.plugins.*,*.per_resource.adobe-flash-player': 1,
                'profile.default_content_setting_values.cookies': 1,
                'profile.block_third_party_cookies': False,
                'profile.cookie_controls_mode': 0,
                'webkit.webprefs.fonts.standard.Zyyy': 'Arial',
                'webkit.webprefs.fonts.fixed.Zyyy': 'Consolas',
                'webkit.webprefs.fonts.serif.Zyyy': 'Times New Roman',
                'webkit.webprefs.fonts.sansserif.Zyyy': 'Arial',
                'webkit.webprefs.fonts.cursive.Zyyy': 'Script',
                'webkit.webprefs.fonts.fantasy.Zyyy': 'Impact',
                'webkit.webprefs.default_font_size': 16,
                'webkit.webprefs.default_fixed_font_size': 13,
                'webkit.webprefs.minimum_font_size': 0,
                'webkit.webprefs.minimum_logical_font_size': 6,
                'webkit.webprefs.default_encoding': 'UTF-8'
            }
            chrome_options.add_experimental_option('prefs', enhanced_prefs)

            # Service
            service = Service(self.chrome_driver_path)

            # WebDriver oluştur
            self.driver = webdriver.Chrome(service=service, options=chrome_options)

            # Ultra gelişmiş anti-detection scriptleri
            self.driver.execute_script("""
                // Navigator özelliklerini tamamen değiştir
                Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
                Object.defineProperty(navigator, 'plugins', {
                    get: () => {
                        return [
                            {name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer'},
                            {name: 'Chrome PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai'},
                            {name: 'Native Client', filename: 'internal-nacl-plugin'},
                            {name: 'Chromium PDF Plugin', filename: 'internal-pdf-viewer'},
                            {name: 'Shockwave Flash', filename: 'pepflashplayer.dll'}
                        ];
                    }
                });
                Object.defineProperty(navigator, 'mimeTypes', {
                    get: () => {
                        return [
                            {type: 'application/pdf', suffixes: 'pdf', description: 'Portable Document Format'},
                            {type: 'application/x-google-chrome-pdf', suffixes: 'pdf', description: 'Portable Document Format'},
                            {type: 'application/x-nacl', suffixes: '', description: 'Native Client Executable'},
                            {type: 'application/x-pnacl', suffixes: '', description: 'Portable Native Client Executable'}
                        ];
                    }
                });
                Object.defineProperty(navigator, 'languages', {get: () => ['tr-TR', 'tr', 'en-US', 'en']});
                Object.defineProperty(navigator, 'platform', {get: () => 'Win32'});
                Object.defineProperty(navigator, 'hardwareConcurrency', {get: () => 8});
                Object.defineProperty(navigator, 'deviceMemory', {get: () => 8});
                Object.defineProperty(navigator, 'maxTouchPoints', {get: () => 0});
                Object.defineProperty(navigator, 'vendor', {get: () => 'Google Inc.'});
                Object.defineProperty(navigator, 'vendorSub', {get: () => ''});
                Object.defineProperty(navigator, 'productSub', {get: () => '20030107'});
                Object.defineProperty(navigator, 'cookieEnabled', {get: () => true});
                Object.defineProperty(navigator, 'onLine', {get: () => true});
                Object.defineProperty(navigator, 'doNotTrack', {get: () => null});

                // Chrome runtime'ı tamamen gizle
                if (window.chrome) {
                    delete window.chrome.runtime;
                    delete window.chrome.loadTimes;
                    delete window.chrome.csi;
                    delete window.chrome.app;
                }

                // Permissions API'yi değiştir
                if (navigator.permissions) {
                    navigator.permissions.query = (function(original) {
                        return function(parameters) {
                            if (parameters.name === 'notifications') {
                                return Promise.resolve({ state: Notification.permission });
                            }
                            return original.apply(this, arguments);
                        };
                    })(navigator.permissions.query);
                }

                // WebGL parmak izini değiştir
                const getParameter = WebGLRenderingContext.prototype.getParameter;
                WebGLRenderingContext.prototype.getParameter = function(parameter) {
                    if (parameter === 37445) return 'Intel Inc.';
                    if (parameter === 37446) return 'Intel(R) HD Graphics 620';
                    if (parameter === 7936) return 'WebGL 1.0 (OpenGL ES 2.0 Chromium)';
                    if (parameter === 7937) return 'WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)';
                    return getParameter.apply(this, arguments);
                };

                // Canvas parmak izini değiştir
                const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
                HTMLCanvasElement.prototype.toDataURL = function(type) {
                    if (type === 'image/png') {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');
                        ctx.fillStyle = 'rgb(255, 255, 255)';
                        ctx.fillRect(0, 0, 1, 1);
                        return canvas.toDataURL();
                    }
                    return originalToDataURL.apply(this, arguments);
                };

                // AudioContext parmak izini değiştir
                if (window.AudioContext) {
                    const originalCreateAnalyser = AudioContext.prototype.createAnalyser;
                    AudioContext.prototype.createAnalyser = function() {
                        const analyser = originalCreateAnalyser.apply(this, arguments);
                        const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;
                        analyser.getFloatFrequencyData = function(array) {
                            originalGetFloatFrequencyData.apply(this, arguments);
                            for (let i = 0; i < array.length; i++) {
                                array[i] = array[i] + Math.random() * 0.0001;
                            }
                        };
                        return analyser;
                    };
                }

                // Screen özelliklerini değiştir
                Object.defineProperty(screen, 'colorDepth', {get: () => 24});
                Object.defineProperty(screen, 'pixelDepth', {get: () => 24});

                // Date.prototype.getTimezoneOffset değiştir
                const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
                Date.prototype.getTimezoneOffset = function() {
                    return -180; // Türkiye saat dilimi
                };

                // Console.debug'ı gizle
                console.debug = () => {};

                // Selenium özelliklerini gizle
                delete window.document.$cdc_asdjflasutopfhvcZLmcfl_;
                delete window.document.$chrome_asyncScriptInfo;
                delete window.document.__$webdriverAsyncExecutor;
                delete window.document.__webdriverFunc;
                delete window.document.webdriver;
                delete window.document.__webdriver_script_fn;
                delete window.document.__driver_evaluate;
                delete window.document.__webdriver_evaluate;
                delete window.document.__selenium_evaluate;
                delete window.document.__fxdriver_evaluate;
                delete window.document.__driver_unwrapped;
                delete window.document.__webdriver_unwrapped;
                delete window.document.__selenium_unwrapped;
                delete window.document.__fxdriver_unwrapped;
                delete window.document._Selenium_IDE_Recorder;
                delete window.document._selenium;
                delete window.document.calledSelenium;
                delete window.document.$webdriverCommand;
                delete window.document.$webdriverAsyncExecutor;

                // Mouse ve keyboard eventlerini simüle et
                ['mousedown', 'mouseup', 'mousemove', 'keydown', 'keyup'].forEach(eventType => {
                    document.addEventListener(eventType, () => {}, true);
                });
            """)

            self.driver.set_page_load_timeout(30)
            logger.info("Chrome WebDriver başlatıldı (Ultra Anti-Detection + CAPTCHA Bypass)")
            return True

        except Exception as e:
            logger.error(f"Chrome WebDriver başlatma hatası: {e}")
            return False

    def save_cookies(self):
        """Cookies'leri dosyaya kaydet"""
        try:
            cookies = self.driver.get_cookies()
            with open(self.cookies_file, 'wb') as file:
                pickle.dump(cookies, file)
            logger.info(f"Cookies kaydedildi: {len(cookies)} adet cookie")
            return True
        except Exception as e:
            logger.error(f"Cookies kaydetme hatası: {e}")
            return False

    def load_cookies(self):
        """Cookies'leri dosyadan yükle"""
        try:
            if not os.path.exists(self.cookies_file):
                logger.info("Cookies dosyası bulunamadı, yeni session başlatılıyor")
                return False

            with open(self.cookies_file, 'rb') as file:
                cookies = pickle.load(file)

            # Önce TikTok ana sayfasına git
            self.driver.get("https://www.tiktok.com")
            time.sleep(2)

            # Cookies'leri ekle
            for cookie in cookies:
                try:
                    # Geçersiz cookie alanlarını temizle
                    if 'expiry' in cookie:
                        cookie['expiry'] = int(cookie['expiry'])

                    # Sadece TikTok domain'i için cookies ekle
                    if 'tiktok.com' in cookie.get('domain', ''):
                        self.driver.add_cookie(cookie)
                except Exception as cookie_error:
                    logger.warning(f"Cookie ekleme hatası: {cookie_error}")
                    continue

            logger.info(f"Cookies yüklendi: {len(cookies)} adet cookie")

            # Sayfayı yenile (cookies'ler aktif olsun)
            self.driver.refresh()
            time.sleep(2)

            return True

        except Exception as e:
            logger.error(f"Cookies yükleme hatası: {e}")
            return False

    def check_login_status(self):
        """Giriş durumunu kontrol et"""
        try:
            # Profil menüsü veya giriş yapmış kullanıcı elementlerini ara
            login_indicators = [
                '[data-e2e="nav-profile"]',
                '.avatar-anchor',
                '[data-e2e="profile-icon"]',
                '.tiktok-avatar',
                '.user-avatar'
            ]

            for selector in login_indicators:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed():
                        logger.info("Kullanıcı giriş yapmış durumda")
                        return True
                except:
                    continue

            # Giriş yap butonu var mı kontrol et
            login_buttons = [
                '[data-e2e="top-login-button"]',
                '.login-button',
                'button[data-e2e="login-button"]'
            ]

            for selector in login_buttons:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed():
                        logger.info("Kullanıcı giriş yapmamış")
                        return False
                except:
                    continue

            # Sayfa içeriğine bakarak kontrol et
            page_source = self.driver.page_source.lower()
            if 'log in' in page_source or 'sign up' in page_source:
                logger.info("Sayfa içeriğinde giriş yapmamış kullanıcı tespiti")
                return False

            logger.info("Giriş durumu belirsiz, devam ediliyor")
            return True

        except Exception as e:
            logger.warning(f"Giriş durumu kontrol hatası: {e}")
            return True  # Hata durumunda devam et

    def extract_hashtags_from_text(self, text):
        """Metinden hashtag'leri çıkar"""
        if not text:
            return []

        # Türkçe karakterleri de destekleyen hashtag pattern'i
        hashtag_pattern = r'#([a-zA-ZğüşıöçĞÜŞİÖÇ0-9_]+)'
        hashtags = re.findall(hashtag_pattern, text.lower())

        # Çok kısa hashtag'leri filtrele (2 karakterden az)
        filtered_hashtags = [tag for tag in hashtags if len(tag) >= 2]

        return filtered_hashtags

    def format_date_turkish(self, date_string):
        """Tarihi Türkçe formata dönüştürür (gün/ay/yıl)"""
        from datetime import datetime, timedelta

        months_en = {
            'January': '01', 'February': '02', 'March': '03', 'April': '04',
            'May': '05', 'June': '06', 'July': '07', 'August': '08',
            'September': '09', 'October': '10', 'November': '11', 'December': '12'
        }

        months_en_short = {
            'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04',
            'May': '05', 'Jun': '06', 'Jul': '07', 'Aug': '08',
            'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
        }

        # Zaten gün/ay/yıl formatındaysa döndür
        if re.match(r'\d{1,2}/\d{1,2}/\d{4}', date_string):
            return date_string

        # 2023-12-25 formatını dönüştür
        if re.match(r'\d{4}-\d{1,2}-\d{1,2}', date_string):
            y, m, d = date_string.split('-')
            return f"{d}/{m}/{y}"

        # Dec 25, 2023 formatını dönüştür
        month_match = re.search(r'(\w+)\s+(\d{1,2}),\s*(\d{4})', date_string)
        if month_match:
            month, day, year = month_match.groups()
            if month in months_en:
                month_num = months_en[month]
            elif month in months_en_short:
                month_num = months_en_short[month]
            else:
                month_num = month
            return f"{day}/{month_num}/{year}"

        # Relatif tarihleri dönüştür (3 days ago, 2 g önce -> bugünden önceki tarih)
        if 'ago' in date_string.lower() or 'önce' in date_string.lower():
            now = datetime.now()

            # Türkçe kısaltmalar (2 g önce, 3 s önce, 1 d önce, 5 h önce)
            if re.search(r'\d+\s*[smhd]\s*önce', date_string.lower()):
                match = re.search(r'(\d+)\s*([smhd])\s*önce', date_string.lower())
                if match:
                    number = int(match.group(1))
                    unit = match.group(2)

                    if unit == 's':  # saniye
                        target_date = now - timedelta(seconds=number)
                    elif unit == 'm':  # dakika
                        target_date = now - timedelta(minutes=number)
                    elif unit == 'h':  # saat
                        target_date = now - timedelta(hours=number)
                    elif unit == 'd':  # gün
                        target_date = now - timedelta(days=number)
                    else:
                        target_date = now

                    return target_date.strftime('%d/%m/%Y')

            # İngilizce kısaltmalar (2h ago, 3d ago, 1w ago)
            elif re.search(r'\d+\s*[smhdw]\s*ago', date_string.lower()):
                match = re.search(r'(\d+)\s*([smhdw])\s*ago', date_string.lower())
                if match:
                    number = int(match.group(1))
                    unit = match.group(2)

                    if unit == 's':  # seconds
                        target_date = now - timedelta(seconds=number)
                    elif unit == 'm':  # minutes
                        target_date = now - timedelta(minutes=number)
                    elif unit == 'h':  # hours
                        target_date = now - timedelta(hours=number)
                    elif unit == 'd':  # days
                        target_date = now - timedelta(days=number)
                    elif unit == 'w':  # weeks
                        target_date = now - timedelta(weeks=number)
                    else:
                        target_date = now

                    return target_date.strftime('%d/%m/%Y')

            # Uzun formatlar
            elif 'minute' in date_string or 'dakika' in date_string:
                match = re.search(r'(\d+)', date_string)
                if match:
                    minutes = int(match.group(1))
                    target_date = now - timedelta(minutes=minutes)
                    return target_date.strftime('%d/%m/%Y')

            elif 'hour' in date_string or 'saat' in date_string:
                match = re.search(r'(\d+)', date_string)
                if match:
                    hours = int(match.group(1))
                    target_date = now - timedelta(hours=hours)
                    return target_date.strftime('%d/%m/%Y')

            elif 'day' in date_string or 'gün' in date_string:
                match = re.search(r'(\d+)', date_string)
                if match:
                    days = int(match.group(1))
                    target_date = now - timedelta(days=days)
                    return target_date.strftime('%d/%m/%Y')

            elif 'week' in date_string or 'hafta' in date_string:
                match = re.search(r'(\d+)', date_string)
                if match:
                    weeks = int(match.group(1))
                    target_date = now - timedelta(weeks=weeks)
                    return target_date.strftime('%d/%m/%Y')

            elif 'month' in date_string or 'ay' in date_string:
                match = re.search(r'(\d+)', date_string)
                if match:
                    months = int(match.group(1))
                    target_date = now - timedelta(days=months*30)
                    return target_date.strftime('%d/%m/%Y')

            elif 'year' in date_string or 'yıl' in date_string:
                match = re.search(r'(\d+)', date_string)
                if match:
                    years = int(match.group(1))
                    target_date = now - timedelta(days=years*365)
                    return target_date.strftime('%d/%m/%Y')

        # Kısa tarih formatları (4-8 -> 08.04.2025, yani 8 Nisan)
        if re.match(r'^\d{1,2}-\d{1,2}$', date_string.strip()):
            parts = date_string.strip().split('-')
            if len(parts) == 2:
                month = int(parts[0])  # İlk rakam ay (4 = Nisan)
                day = int(parts[1])    # İkinci rakam gün (8 = 8. gün)
                current_year = datetime.now().year

                # Geçerli ay/gün kontrolü
                if 1 <= month <= 12 and 1 <= day <= 31:
                    try:
                        # Bu yılı dene
                        target_date = datetime(current_year, month, day)
                        # Eğer gelecekte ise geçen yıl olabilir
                        if target_date > datetime.now():
                            target_date = datetime(current_year - 1, month, day)
                        return target_date.strftime('%d.%m.%Y')  # Türkçe format (08.04.2025)
                    except ValueError:
                        pass

        return date_string  # Dönüştürülemezse orijinal halini döndür

    def extract_video_date(self):
        """Video yayın tarihini çeker"""
        try:
            published_date = None

            # 1. JavaScript ile SIGI_STATE'den tarih bilgisini al (en güvenilir yöntem)
            try:
                js_result = self.driver.execute_script("""
                    try {
                        // SIGI_STATE'den video verisini al
                        const sigiState = document.querySelector('#SIGI_STATE');
                        if (sigiState && sigiState.textContent) {
                            const data = JSON.parse(sigiState.textContent);

                            // Video ID'sini URL'den çıkar
                            const videoId = window.location.pathname.split('/').pop();

                            // ItemModule'den video verisini bul
                            if (data.ItemModule && data.ItemModule[videoId]) {
                                const video = data.ItemModule[videoId];
                                if (video.createTime) {
                                    const date = new Date(video.createTime * 1000);
                                    return date.getDate().toString().padStart(2, '0') + '/' +
                                           (date.getMonth() + 1).toString().padStart(2, '0') + '/' +
                                           date.getFullYear();
                                }
                            }

                            // VideoModule'den de dene
                            if (data.VideoModule && data.VideoModule[videoId]) {
                                const video = data.VideoModule[videoId];
                                if (video.createTime) {
                                    const date = new Date(video.createTime * 1000);
                                    return date.getDate().toString().padStart(2, '0') + '/' +
                                           (date.getMonth() + 1).toString().padStart(2, '0') + '/' +
                                           date.getFullYear();
                                }
                            }
                        }

                        // window.__UNIVERSAL_DATA_FOR_REHYDRATION__ alternatifi
                        if (window.__UNIVERSAL_DATA_FOR_REHYDRATION__) {
                            const data = window.__UNIVERSAL_DATA_FOR_REHYDRATION__;
                            const videoId = window.location.pathname.split('/').pop();

                            if (data.default && data.default.ItemModule && data.default.ItemModule[videoId]) {
                                const video = data.default.ItemModule[videoId];
                                if (video.createTime) {
                                    const date = new Date(video.createTime * 1000);
                                    return date.getDate().toString().padStart(2, '0') + '/' +
                                           (date.getMonth() + 1).toString().padStart(2, '0') + '/' +
                                           date.getFullYear();
                                }
                            }
                        }

                        return null;
                    } catch (e) {
                        console.log('JS tarih çekme hatası:', e);
                        return null;
                    }
                """)

                if js_result:
                    published_date = js_result
                    logger.info(f"✅ JavaScript'ten video tarihi bulundu: {published_date}")
                else:
                    logger.warning("⚠️ JavaScript'ten video tarihi bulunamadı")

            except Exception as e:
                logger.warning(f"JavaScript tarih çekme hatası: {e}")

            # 2. Eğer JavaScript'ten alamadıysak, DOM selektörlerini dene
            if not published_date:
                logger.info("🔍 DOM selektörleriyle tarih aranıyor...")

                # Güncel TikTok selektörleri (ekran görüntülerinden)
                date_selectors = [
                    # Meta veriler
                    "meta[property='og:video:release_date']",
                    "meta[name='twitter:data2']",
                    "meta[property='video:release_date']",

                    # Kullanıcı adı yanındaki tarih bilgisi (ekran görüntüsünden)
                    "[data-e2e='browser-nickname'] + span",
                    "[data-e2e='browser-nickname'] ~ span",

                    # Video açıklama altındaki tarih
                    "[data-e2e='video-desc'] + div span",
                    "[data-e2e='video-desc'] ~ div span",

                    # Profil bilgileri alanındaki tarih
                    ".css-1qb94tb-DivAuthorContainer span",
                    ".css-1s2k5gx-DivInfoContainer span",
                    ".css-j6dmhd-StyledDivContainer span",

                    # Video meta bilgileri
                    ".css-1w7cv3q-SpanOtherInfos span",
                    ".css-1iysm4m-SpanOtherInfos span",

                    # Zaman elementleri
                    "time",
                    "[datetime]",

                    # Genel tarih selektörleri (ekran görüntüsünden)
                    "span:contains('önce')",
                    "span:contains('ago')",
                    "span:contains('gün')",
                    "span:contains('saat')",
                    "span:contains('dakika')",
                    "span:contains(' g önce')",  # "2 g önce" formatı
                    "span:contains(' s önce')",  # "30 s önce" formatı
                    "span:contains(' h önce')",  # "5 h önce" formatı
                    "span:contains(' d önce')",  # "1 d önce" formatı

                    # Video sayfasındaki tarih elementleri (ekran görüntüsünden)
                    ".tiktok-1ejylhp-DivContainer span",  # Video açıklama container'ı
                    ".tiktok-1s2k5gx-DivInfoContainer span",  # Video info container'ı
                    "[data-e2e='video-author-container'] span",  # Video yazar container'ı
                    ".video-info-detail span",  # Video detay bilgileri

                    # Video info container'ları
                    ".video-timestamp",
                    ".video-date",
                    ".publish-date",
                    ".upload-date"
                ]

                for selector in date_selectors:
                    try:
                        if selector.startswith('meta'):
                            # Meta elementler için
                            meta_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                            date_content = meta_element.get_attribute("content")
                            if date_content and date_content.strip():
                                published_date = date_content.strip()
                                logger.info(f"✅ Meta'dan tarih bulundu ({selector}): {published_date}")
                                break
                        else:
                            # Diğer elementler için
                            elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                            for element in elements:
                                date_text = element.text.strip()

                                # Tarih formatlarını kontrol et
                                if date_text and self.is_valid_date_text(date_text):
                                    published_date = date_text
                                    logger.info(f"✅ DOM'dan tarih bulundu ({selector}): {published_date}")
                                    break

                            if published_date:
                                break

                    except Exception as e:
                        continue

            # 3. Eğer hala bulamadıysak, sayfa kaynağından regex ile ara
            if not published_date:
                logger.info("🔍 Sayfa kaynağından regex ile tarih aranıyor...")
                try:
                    page_source = self.driver.page_source

                    # createTime pattern'leri
                    patterns = [
                        r'"createTime":(\d+)',
                        r'"createTime"\s*:\s*(\d+)',
                        r'createTime:(\d+)',
                        r'"uploadDate":"([^"]+)"',
                        r'"datePublished":"([^"]+)"'
                    ]

                    for pattern in patterns:
                        matches = re.findall(pattern, page_source)
                        if matches:
                            timestamp_or_date = matches[0]

                            # Unix timestamp ise dönüştür
                            if timestamp_or_date.isdigit() and len(timestamp_or_date) >= 10:
                                timestamp = int(timestamp_or_date)
                                if timestamp > 1000000000:  # Geçerli timestamp kontrolü
                                    from datetime import datetime
                                    date_obj = datetime.fromtimestamp(timestamp)
                                    published_date = date_obj.strftime('%d/%m/%Y')
                                    logger.info(f"✅ Regex'ten timestamp bulundu: {published_date}")
                                    break
                            else:
                                # ISO date string ise dönüştür
                                published_date = self.format_date_turkish(timestamp_or_date)
                                logger.info(f"✅ Regex'ten tarih string bulundu: {published_date}")
                                break

                except Exception as e:
                    logger.warning(f"Regex tarih arama hatası: {e}")

            # 4. Son çare: bugünün tarihi
            if not published_date:
                from datetime import datetime
                published_date = datetime.now().strftime('%d/%m/%Y')
                logger.warning(f"⚠️ Video tarihi bulunamadı, bugünün tarihi kullanılıyor: {published_date}")
            else:
                # Tarihi Türkçe formata dönüştür
                published_date = self.format_date_turkish(published_date)
                logger.info(f"✅ Final video tarihi: {published_date}")

            return published_date

        except Exception as e:
            logger.error(f"❌ Video tarih çekme genel hatası: {e}")
            from datetime import datetime
            return datetime.now().strftime('%d/%m/%Y')

    def is_valid_date_text(self, text):
        """Verilen metnin geçerli bir tarih metni olup olmadığını kontrol eder"""
        if not text or len(text) < 2:  # "4-8" gibi kısa formatlar için
            return False

        # Tarih formatlarını kontrol et
        date_patterns = [
            r'\d{4}-\d{1,2}-\d{1,2}',  # 2023-12-25
            r'\d{1,2}/\d{1,2}/\d{4}',  # 12/25/2023
            r'\d{1,2}\.\d{1,2}\.\d{4}',  # 25.12.2023
            r'^\d{1,2}-\d{1,2}$',  # 4-8 (kısa tarih formatı)
            r'\d+\s*[smhdw]\s*ago',  # 3h ago, 2d ago, 1w ago
            r'\d+\s*[smhd]\s*önce',  # 2 g önce, 3 s önce, 1 d önce, 5 h önce
            r'\d+\s*g\s*önce',  # 2 g önce (özel olarak)
            r'\d+\s*s\s*önce',  # 30 s önce (özel olarak)
            r'\d+\s*h\s*önce',  # 5 h önce (özel olarak)
            r'\d+\s*d\s*önce',  # 1 d önce (özel olarak)
            r'\d+\s?(saniye|dakika|saat|gün|hafta|ay|yıl|second|minute|hour|day|week|month|year)',
        ]

        for pattern in date_patterns:
            if re.search(pattern, text.lower()):
                return True

        # Relatif tarih kelimeleri
        relative_keywords = ['ago', 'since', 'önce', 'yıl', 'ay', 'gün', 'saat', 'dakika']
        return any(keyword in text.lower() for keyword in relative_keywords)

    def get_video_description(self, video_element):
        """Video açıklamasını çek"""
        try:
            # Video açıklama selectors'ları
            description_selectors = [
                '[data-e2e="video-desc"]',
                '.video-meta-caption',
                '.tt-video-meta-caption',
                '.video-description',
                '[data-e2e="browse-video-desc"]',
                '.tiktok-j2a19u-SpanText'
            ]

            for selector in description_selectors:
                try:
                    desc_element = video_element.find_element(By.CSS_SELECTOR, selector)
                    if desc_element.text.strip():
                        return desc_element.text.strip()
                except:
                    continue

            return ""

        except Exception as e:
            logger.warning(f"Video açıklama çekme hatası: {e}")
            return ""

    def format_number(self, text):
        """Sayı formatını çevir (1.2M -> 1200000)"""
        if not text:
            return 0

        text = text.upper().strip()
        number = 0

        try:
            if 'M' in text:
                number = float(text.replace('M', '')) * 1000000
            elif 'K' in text:
                number = float(text.replace('K', '')) * 1000
            else:
                number = float(text)
            return int(number)
        except:
            return 0

    def format_number_display(self, number):
        """Sayıyı görüntüleme formatına çevir"""
        if number >= 1000000:
            return f"{number/1000000:.1f}M"
        elif number >= 1000:
            return f"{number/1000:.1f}K"
        return str(number)

    def simulate_human_behavior(self):
        """İnsan davranışını simüle et"""
        try:
            # Rastgele mouse hareketleri
            actions = ActionChains(self.driver)

            # Sayfada rastgele noktalara mouse hareket et
            for _ in range(3):
                x = random.randint(100, 800)
                y = random.randint(100, 600)
                actions.move_by_offset(x, y)
                actions.pause(random.uniform(0.5, 1.5))

            actions.perform()

            # Rastgele scroll
            scroll_amount = random.randint(100, 300)
            self.driver.execute_script(f"window.scrollBy(0, {scroll_amount})")
            time.sleep(random.uniform(1, 2))

            # Geri scroll
            self.driver.execute_script(f"window.scrollBy(0, -{scroll_amount//2})")
            time.sleep(random.uniform(0.5, 1))

        except Exception as e:
            logger.warning(f"İnsan davranışı simülasyonu hatası: {e}")

    def bypass_captcha_advanced(self):
        """Gelişmiş CAPTCHA bypass"""
        try:
            # CAPTCHA elementlerini kontrol et
            captcha_selectors = [
                '[data-testid="captcha"]',
                '.captcha-container',
                '#captcha',
                '.verify-container',
                '.challenge-container',
                '[class*="captcha"]',
                '[class*="verify"]',
                '[class*="challenge"]',
                'iframe[src*="captcha"]',
                'iframe[src*="recaptcha"]',
                '.tiktok-verify',
                '.verify-wrap',
                '.secsdk-captcha-wrapper'
            ]

            captcha_found = False
            for selector in captcha_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed():
                        captcha_found = True
                        logger.warning(f"CAPTCHA tespit edildi: {selector}")
                        break
                except:
                    continue

            # Sayfa içeriğinde CAPTCHA kelimelerini ara
            verification_texts = ["verify", "captcha", "puzzle", "slide", "drag", "human", "doğrula", "robot", "challenge"]
            page_source = self.driver.page_source.lower()

            if any(text in page_source for text in verification_texts):
                captcha_found = True
                logger.warning("Sayfa içeriğinde CAPTCHA/doğrulama metni tespit edildi")

            if captcha_found:
                logger.info("CAPTCHA bypass stratejisi uygulanıyor...")

                # Strateji 1: İnsan davranışı simüle et
                self.simulate_human_behavior()

                # Strateji 2: Sayfayı yenile
                logger.info("Sayfa yenileniyor...")
                self.driver.refresh()
                time.sleep(random.uniform(3, 5))

                # Strateji 3: Geri git ve tekrar gel
                logger.info("Geri gidip tekrar geliniyor...")
                self.driver.back()
                time.sleep(random.uniform(2, 3))
                self.driver.forward()
                time.sleep(random.uniform(2, 3))

                # Strateji 4: Ana sayfaya git ve tekrar gel
                current_url = self.driver.current_url
                logger.info("Ana sayfaya gidip tekrar geliniyor...")
                self.driver.get("https://www.tiktok.com")
                time.sleep(random.uniform(3, 5))
                self.simulate_human_behavior()
                self.driver.get(current_url)
                time.sleep(random.uniform(3, 5))

                # Tekrar kontrol et
                captcha_still_exists = False
                for selector in captcha_selectors:
                    try:
                        element = self.driver.find_element(By.CSS_SELECTOR, selector)
                        if element.is_displayed():
                            captcha_still_exists = True
                            break
                    except:
                        continue

                page_source = self.driver.page_source.lower()
                if any(text in page_source for text in verification_texts):
                    captcha_still_exists = True

                if captcha_still_exists:
                    logger.warning("CAPTCHA hala mevcut, manuel müdahale gerekebilir")
                    return False
                else:
                    logger.info("CAPTCHA bypass başarılı!")
                    return True

            return True

        except Exception as e:
            logger.error(f"CAPTCHA bypass hatası: {e}")
            return False

    def process_tiktok_profile(self, request_id, username, video_count=10):
        """TikTok profilini analiz et"""
        try:
            # Video sayısını ayarla
            self.max_videos = video_count
            logger.info(f"Analiz edilecek video sayısı: {video_count}")

            self.update_request_status(request_id, 'processing', 5, 'Anti-detection sistemi hazırlanıyor...')

            # Cookies'leri yükle (önceki session'dan)
            logger.info("Kaydedilmiş cookies yükleniyor...")
            cookies_loaded = self.load_cookies()

            # Direkt kullanıcı profiline git (daha hızlı)
            profile_url = f"https://www.tiktok.com/@{username}"
            logger.info(f"🎯 Direkt profil sayfasına gidiliyor: {profile_url}")

            self.update_request_status(request_id, 'processing', 10, f'TikTok profiline gidiliyor: @{username}')

            # Profil sayfasına git
            self.driver.get(profile_url)

            # Kısa bekleme
            time.sleep(random.uniform(2, 3))

            # Cookies varsa yükle
            if cookies_loaded:
                logger.info("✅ Cookies yüklendi")
                # Sayfayı yenile ki cookies etkili olsun
                self.driver.refresh()
                time.sleep(random.uniform(1, 2))

            # Minimal insan davranışı simülasyonu (sadece gerekli)
            logger.info("🤖 Minimal anti-detection...")
            self.driver.execute_script("""
                // Basit mouse hareket simülasyonu
                document.dispatchEvent(new MouseEvent('mousemove', {
                    clientX: Math.random() * window.innerWidth,
                    clientY: Math.random() * window.innerHeight
                }));

                // Scroll simülasyonu
                window.scrollBy(0, Math.random() * 100);
            """)
            time.sleep(1)

            # CAPTCHA kontrolü (daha akıllı)
            logger.info("🔍 CAPTCHA kontrolü...")

            # Daha spesifik CAPTCHA selectors'ları
            captcha_selectors = [
                "[data-testid='captcha']",
                ".captcha-container",
                "#captcha",
                ".verify-container",
                ".puzzle-container",
                "[class*='captcha']",
                "[id*='captcha']"
            ]

            captcha_found = False
            for selector in captcha_selectors:
                try:
                    captcha_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if captcha_element.is_displayed():
                        captcha_found = True
                        logger.warning(f"⚠️ CAPTCHA element bulundu: {selector}")
                        break
                except:
                    continue

            # Eğer element bulunamadıysa, URL kontrolü yap
            if not captcha_found:
                current_url = self.driver.current_url.lower()
                if 'captcha' in current_url or 'verify' in current_url:
                    captcha_found = True
                    logger.warning(f"⚠️ CAPTCHA URL tespit edildi: {current_url}")

            # Eğer element bulunamadıysa, sayfa başlığı kontrolü
            if not captcha_found:
                try:
                    page_title = self.driver.title.lower()
                    if 'captcha' in page_title or 'verify' in page_title or 'challenge' in page_title:
                        captcha_found = True
                        logger.warning(f"⚠️ CAPTCHA başlık tespit edildi: {page_title}")
                except:
                    pass

            if captcha_found:
                logger.warning("⚠️ CAPTCHA tespit edildi!")
                self.update_request_status(request_id, 'processing', 15, 'CAPTCHA doğrulaması gerekli - VDS\'de manuel çözün')

                # CAPTCHA varsa 30 saniye bekle
                for i in range(3):
                    time.sleep(10)

                    # Tekrar kontrol et
                    captcha_still_exists = False
                    for selector in captcha_selectors:
                        try:
                            captcha_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                            if captcha_element.is_displayed():
                                captcha_still_exists = True
                                break
                        except:
                            continue

                    if not captcha_still_exists:
                        logger.info(f"✅ CAPTCHA çözüldü! ({(i+1)*10} saniye sonra)")
                        break
                    else:
                        logger.info(f"⏳ CAPTCHA hala mevcut, {30-(i+1)*10} saniye daha bekleniyor...")

                # Son kontrol
                final_captcha_check = False
                for selector in captcha_selectors:
                    try:
                        captcha_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                        if captcha_element.is_displayed():
                            final_captcha_check = True
                            break
                    except:
                        continue

                if final_captcha_check:
                    raise Exception("CAPTCHA doğrulaması tamamlanamadı - VDS'de manuel olarak çözün")
            else:
                logger.info("✅ CAPTCHA yok, devam ediliyor")

            # Sayfa yüklensin
            wait = WebDriverWait(self.driver, 15)
            logger.info("📄 Profil sayfası yüklenmesi bekleniyor...")

            try:
                wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, '[data-e2e="user-post-item"], .tiktok-x6y88p-DivItemContainerV2')))
                logger.info("Profil sayfası yüklendi")
            except:
                # CAPTCHA kontrolü
                verification_texts = ["verify", "captcha", "puzzle", "slide", "drag", "human", "doğrula"]
                page_source = self.driver.page_source.lower()

                if any(text in page_source for text in verification_texts):
                    logger.warning("CAPTCHA/Doğrulama ekranı tespit edildi")
                    self.update_request_status(request_id, 'processing', 15, 'CAPTCHA doğrulaması bekleniyor... (60 saniye)')

                    # 60 saniye bekle (kullanıcı manuel doğrulama yapabilir)
                    logger.info("CAPTCHA tespit edildi - 60 saniye manuel çözüm bekleniyor...")

                    # Her 10 saniyede bir kontrol et
                    for i in range(6):
                        time.sleep(10)
                        try:
                            # CAPTCHA hala var mı kontrol et
                            current_page = self.driver.page_source.lower()
                            if not any(text in current_page for text in verification_texts):
                                logger.info(f"CAPTCHA çözüldü! ({(i+1)*10} saniye sonra)")
                                break
                            else:
                                remaining = 60 - (i+1)*10
                                self.update_request_status(request_id, 'processing', 15, f'CAPTCHA doğrulaması bekleniyor... ({remaining} saniye kaldı)')
                                logger.info(f"CAPTCHA hala mevcut, {remaining} saniye daha bekleniyor...")
                        except:
                            pass

                    try:
                        wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, '[data-e2e="user-post-item"], .tiktok-x6y88p-DivItemContainerV2')))
                        logger.info("CAPTCHA sonrası profil sayfası yüklendi")

                        # CAPTCHA çözüldükten sonra cookies'leri kaydet
                        logger.info("CAPTCHA çözüldü, cookies kaydediliyor...")
                        self.save_cookies()

                    except:
                        raise Exception('CAPTCHA doğrulaması tamamlanamadı veya profil erişilemez')
                else:
                    raise Exception('Profil bulunamadı, gizli veya erişilemez')

            self.update_request_status(request_id, 'processing', 25, 'Profil bilgileri çekiliyor...')

            # Profil bilgilerini çek
            profile_data = {'username': username}

            # Nickname
            try:
                nickname_element = self.driver.find_element(By.CSS_SELECTOR, 'h1[data-e2e="user-title"], .tiktok-qpyus6-H1ShareTitle')
                profile_data['nickname'] = nickname_element.text.strip()
            except:
                profile_data['nickname'] = username

            # Bio
            try:
                bio_element = self.driver.find_element(By.CSS_SELECTOR, 'h2[data-e2e="user-bio"], .tiktok-1n8z7yd-H2ShareDesc')
                profile_data['bio'] = bio_element.text.strip()
            except:
                profile_data['bio'] = ''

            # Takipçi, takip, beğeni
            try:
                followers_element = self.driver.find_element(By.CSS_SELECTOR, '[data-e2e="followers-count"]')
                profile_data['followers'] = followers_element.text.strip()
                profile_data['followers_count'] = self.format_number(profile_data['followers'])
            except:
                profile_data['followers'] = '0'
                profile_data['followers_count'] = 0

            try:
                following_element = self.driver.find_element(By.CSS_SELECTOR, '[data-e2e="following-count"]')
                profile_data['following'] = following_element.text.strip()
                profile_data['following_count'] = self.format_number(profile_data['following'])
            except:
                profile_data['following'] = '0'
                profile_data['following_count'] = 0

            try:
                likes_element = self.driver.find_element(By.CSS_SELECTOR, '[data-e2e="likes-count"]')
                profile_data['likes'] = likes_element.text.strip()
                profile_data['likes_count'] = self.format_number(profile_data['likes'])
            except:
                profile_data['likes'] = '0'
                profile_data['likes_count'] = 0

            # Avatar
            try:
                avatar_element = self.driver.find_element(By.CSS_SELECTOR, '[data-e2e="user-avatar"] img, .tiktok-1zpj2q-ImgAvatar')
                profile_data['avatar_url'] = avatar_element.get_attribute('src')
            except:
                profile_data['avatar_url'] = f"https://via.placeholder.com/150x150/4ECDC4/FFFFFF?text={username[:2].upper()}"

            # Toplam video sayısını çek
            try:
                total_videos = 0

                # Önce video kartlarını say
                video_cards = self.driver.find_elements(By.CSS_SELECTOR, '[data-e2e="user-post-item"], .tiktok-x6y88p-DivItemContainerV2')
                if video_cards:
                    total_videos = len(video_cards)
                    logger.info(f"Video kartlarından toplam video sayısı: {total_videos}")

                # Eğer video kartları yeterli değilse, JavaScript ile daha fazla video yükle
                if total_videos < 50:  # Daha fazla video varsa yükle
                    try:
                        # Sayfayı aşağı kaydır
                        for i in range(3):
                            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                            time.sleep(2)

                        # Tekrar video kartlarını say
                        video_cards = self.driver.find_elements(By.CSS_SELECTOR, '[data-e2e="user-post-item"], .tiktok-x6y88p-DivItemContainerV2')
                        total_videos = len(video_cards)
                        logger.info(f"Scroll sonrası toplam video sayısı: {total_videos}")
                    except:
                        pass

                # JavaScript ile SIGI_STATE'den video sayısını almaya çalış
                if total_videos == 0:
                    try:
                        js_total_videos = self.driver.execute_script("""
                            try {
                                const app = document.querySelector('#SIGI_STATE');
                                if (app && app.textContent) {
                                    const data = JSON.parse(app.textContent);
                                    const userModule = data.UserModule;
                                    if (userModule) {
                                        for (let key in userModule.users) {
                                            const user = userModule.users[key];
                                            if (user.stats && user.stats.videoCount) {
                                                return user.stats.videoCount;
                                            }
                                        }
                                    }
                                }
                            } catch (e) {}
                            return 0;
                        """)
                        if js_total_videos and js_total_videos > 0:
                            total_videos = js_total_videos
                            logger.info(f"JavaScript'ten toplam video sayısı: {total_videos}")
                    except:
                        pass

                profile_data['total_videos'] = total_videos
                logger.info(f"Toplam video sayısı: {total_videos}")

            except Exception as e:
                logger.warning(f"Toplam video sayısı çekilemedi: {e}")
                profile_data['total_videos'] = 0

            self.update_request_status(request_id, 'processing', 50, 'Video listesi çekiliyor...')

            # Video kartlarını bul
            video_cards = self.driver.find_elements(By.CSS_SELECTOR, '[data-e2e="user-post-item"], .tiktok-x6y88p-DivItemContainerV2')

            if not video_cards:
                raise Exception('Video bulunamadı. Profil gizli olabilir.')

            # Sayfayı aşağı kaydır (tüm videoların yüklenmesi için)
            self.driver.execute_script("window.scrollBy(0, 700)")
            time.sleep(2)

            # İlk N videoyu analiz et
            max_videos = min(self.max_videos, len(video_cards))
            videos = []
            total_views = 0
            total_likes = 0
            total_comments = 0
            total_shares = 0
            total_saves = 0

            # Önce video URL'lerini ve thumbnail'larını topla
            video_urls = []
            for idx, card in enumerate(video_cards[:max_videos]):
                try:
                    link_element = card.find_element(By.TAG_NAME, 'a')
                    video_url = link_element.get_attribute('href')

                    views_element = card.find_element(By.TAG_NAME, 'strong')
                    views_text = views_element.text.strip()
                    views_count = self.format_number(views_text)

                    # Ana sayfadaki video kartından thumbnail'ı çek (TikTokProfilAnalizi yöntemi)
                    thumbnail_url = None
                    try:
                        # TikTok'un video thumbnail yapısı - Gelişmiş selectors
                        thumbnail_selectors = [
                            "img.tiktok-1itcwxg-ImgPoster",
                            "img[mode='0']",
                            "img[mode='1']",
                            "img[mode='2']",
                            "img[class*='ImgPoster']",
                            "img[src*='tiktokcdn']",
                            "img[src*='muscdn']",
                            "img[src*='bytedance']",
                            "img[src*='image']",
                            ".tiktok-1jxhpnd-ImgPoster",
                            "[data-e2e='user-post-item'] img",
                            ".tiktok-x6y88p-DivItemContainerV2 img",
                            "img[alt*='video']",
                            "img[loading='lazy']",
                            "img[decoding='async']"
                        ]

                        for selector in thumbnail_selectors:
                            try:
                                thumbnail_elements = card.find_elements(By.CSS_SELECTOR, selector)
                                for thumbnail_element in thumbnail_elements:
                                    thumbnail_url = thumbnail_element.get_attribute("src")
                                    if thumbnail_url and thumbnail_url.startswith('http') and ('tiktok' in thumbnail_url or 'mus' in thumbnail_url or 'bytedance' in thumbnail_url or 'image' in thumbnail_url):
                                        logger.info(f"✅ Video {idx+1} ana sayfa thumbnail bulundu: {thumbnail_url[:60]}...")
                                        break
                                if thumbnail_url:
                                    break
                            except:
                                continue

                        # Eğer hala bulunamadıysa, tüm img elementlerini kontrol et
                        if not thumbnail_url:
                            try:
                                all_imgs = card.find_elements(By.TAG_NAME, "img")
                                for img in all_imgs:
                                    img_src = img.get_attribute("src")
                                    if img_src and img_src.startswith('http') and ('tiktok' in img_src or 'mus' in img_src or 'bytedance' in img_src or 'image' in img_src):
                                        thumbnail_url = img_src
                                        logger.info(f"✅ Video {idx+1} ana sayfa thumbnail (genel arama): {thumbnail_url[:60]}...")
                                        break
                            except:
                                pass

                    except Exception as e:
                        logger.warning(f"Video {idx+1} ana sayfa thumbnail çekilemedi: {e}")

                    video_urls.append({
                        'url': video_url,
                        'views_text': views_text,
                        'views_count': views_count,
                        'thumbnail_from_main': thumbnail_url  # Ana sayfadan çekilen thumbnail
                    })
                except Exception as e:
                    logger.warning(f"Video {idx+1} URL'si alınamadı: {e}")
                    continue

            # Her video için detaylı analiz
            for idx, video_info in enumerate(video_urls):
                try:
                    progress = 50 + (idx + 1) * (40 / len(video_urls))
                    self.update_request_status(request_id, 'processing', int(progress), f'Video {idx+1}/{len(video_urls)} detayları çekiliyor...')

                    # Video sayfasına git
                    logger.info(f"Video sayfasına gidiliyor: {video_info['url']}")
                    self.driver.get(video_info['url'])

                    # Video sayfasının yüklenmesini bekle
                    try:
                        WebDriverWait(self.driver, 10).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, ".tiktok-1hskgat-DivPlayerContainer, video"))
                        )
                    except:
                        logger.warning(f"Video {idx+1} sayfası yüklenemedi")
                        continue

                    # Videoları sustur
                    self.driver.execute_script("""
                        const videos = document.querySelectorAll('video');
                        for (let video of videos) {
                            video.muted = true;
                            video.volume = 0;
                        }
                    """)

                    time.sleep(3)  # Sayfa yüklensin

                    # Video thumbnail URL'sini çek - Optimize edilmiş versiyon
                    thumbnail_url = None

                    try:
                        # 1. Önce ana sayfadan çekilen thumbnail'ı kullan (en hızlı ve güvenilir)
                        if video_info.get('thumbnail_from_main'):
                            thumbnail_url = video_info['thumbnail_from_main']
                            logger.info(f"✅ Video {idx+1} ana sayfadan önceden çekilen thumbnail kullanılıyor: {thumbnail_url[:60]}...")

                        # 2. Poster attribute genelde video URL'si veriyor, resim URL'si değil - atla
                        # if not thumbnail_url:
                        #     video_elements = self.driver.find_elements(By.TAG_NAME, "video")
                        #     for video_elem in video_elements:
                        #         poster_url = video_elem.get_attribute('poster')
                        #         if poster_url and poster_url.startswith('http'):
                        #             thumbnail_url = poster_url
                        #             logger.info(f"✅ Video {idx+1} poster thumbnail bulundu: {poster_url[:60]}...")
                        #             break

                        # 3. Gelişmiş img element arama (sadece ana sayfadan thumbnail alınamadıysa)
                        if not thumbnail_url:
                            img_selectors = [
                                "img[src*='tiktokcdn']",
                                "img[src*='muscdn']",
                                "img[src*='bytedance']",
                                "img[src*='image']",
                                ".tiktok-1itcwxg-ImgPoster",
                                "img[class*='ImgPoster']",
                                "img[class*='poster']",
                                "[data-e2e='video-player'] img",
                                ".tiktok-1hskgat-DivPlayerContainer img",
                                "img[mode]",  # TikTok'un mode attribute'u
                                "img[alt*='video']"
                            ]

                            for selector in img_selectors:
                                try:
                                    img_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                                    for img_elem in img_elements:
                                        img_url = img_elem.get_attribute('src')
                                        if img_url and img_url.startswith('http') and ('tiktok' in img_url or 'mus' in img_url or 'bytedance' in img_url or 'image' in img_url):
                                            thumbnail_url = img_url
                                            logger.info(f"✅ Video {idx+1} img thumbnail bulundu: {img_url[:60]}...")
                                            break
                                    if thumbnail_url:
                                        break
                                except:
                                    continue

                        # 4. Sayfa kaynağından gelişmiş regex arama (son çare)
                        if not thumbnail_url:
                            try:
                                page_source = self.driver.page_source
                                # TikTokProfilAnalizi'ndan gelişmiş pattern'ler
                                patterns = [
                                    r'"poster":"([^"]*(?:tiktokcdn|muscdn|bytedance)[^"]*)"',
                                    r'"cover":"([^"]*(?:tiktokcdn|muscdn|bytedance)[^"]*)"',
                                    r'"dynamicCover":"([^"]*(?:tiktokcdn|muscdn|bytedance)[^"]*)"',
                                    r'"originCover":"([^"]*(?:tiktokcdn|muscdn|bytedance)[^"]*)"',
                                    r'poster="([^"]*(?:tiktokcdn|muscdn|bytedance)[^"]*)"',
                                    r'src="([^"]*(?:tiktokcdn|muscdn|bytedance)[^"]*image[^"]*)"',
                                    r'"url":"([^"]*(?:tiktokcdn|muscdn|bytedance)[^"]*image[^"]*)"',
                                    r'"thumbUrl":"([^"]*(?:tiktokcdn|muscdn|bytedance)[^"]*)"',
                                    r'"imageUrl":"([^"]*(?:tiktokcdn|muscdn|bytedance)[^"]*)"'
                                ]

                                for pattern in patterns:
                                    matches = re.findall(pattern, page_source)
                                    if matches:
                                        found_url = matches[0].replace('\\u002F', '/').replace('\\/', '/')
                                        if found_url.startswith('http'):
                                            thumbnail_url = found_url
                                            logger.info(f"✅ Video {idx+1} regex thumbnail bulundu: {found_url[:60]}...")
                                            break

                            except Exception as regex_e:
                                logger.warning(f"Regex thumbnail arama hatası: {regex_e}")

                        # 5. Son çare: TikTok temalı placeholder'lar
                        if not thumbnail_url:
                            # TikTok temalı gradient placeholder'lar
                            tiktok_placeholders = [
                                f"https://via.placeholder.com/300x400/FE2C55/FFFFFF?text=Video+{idx+1}",
                                f"https://via.placeholder.com/300x400/25F4EE/000000?text=Video+{idx+1}",
                                f"https://via.placeholder.com/300x400/FF0050/FFFFFF?text=Video+{idx+1}",
                                f"https://via.placeholder.com/300x400/00F2EA/000000?text=Video+{idx+1}",
                                f"https://via.placeholder.com/300x400/161823/FFFFFF?text=Video+{idx+1}",
                                f"https://via.placeholder.com/300x400/69C9D0/000000?text=Video+{idx+1}",
                                f"https://via.placeholder.com/300x400/EE1D52/FFFFFF?text=Video+{idx+1}",
                                f"https://via.placeholder.com/300x400/010A43/FFFFFF?text=Video+{idx+1}",
                                f"https://via.placeholder.com/300x400/FF6B6B/FFFFFF?text=Video+{idx+1}",
                                f"https://via.placeholder.com/300x400/4ECDC4/000000?text=Video+{idx+1}"
                            ]
                            thumbnail_url = tiktok_placeholders[idx % len(tiktok_placeholders)]
                            logger.warning(f"Video {idx+1} için gerçek thumbnail bulunamadı, TikTok temalı placeholder kullanılıyor")

                    except Exception as e:
                        logger.warning(f"Video {idx+1} thumbnail çekme hatası: {e}")
                        # Hata durumunda TikTok temalı placeholder
                        thumbnail_url = f"https://via.placeholder.com/300x400/FE2C55/FFFFFF?text=Video+{idx+1}"

                    # Video ID
                    video_id = re.search(r'/video/(\d+)', video_info['url'])
                    video_id = video_id.group(1) if video_id else f'video_{idx+1}'

                    # Gerçek etkileşim verilerini çek
                    likes_count = 0
                    comments_count = 0
                    shares_count = 0
                    saves_count = 0

                    # Beğeni sayısı
                    try:
                        like_selectors = [
                            "[data-e2e='like-count']",
                            "[data-e2e='browse-like-count']",
                            ".tiktok-1n8iyy3-StrongText",
                            ".tiktok-1xikrbg-StrongText"
                        ]
                        for selector in like_selectors:
                            try:
                                like_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                                if like_element.text.strip():
                                    likes_text = like_element.text.strip()
                                    likes_count = self.format_number(likes_text)
                                    logger.info(f"Video {idx+1} beğeni: {likes_text}")
                                    break
                            except:
                                continue
                    except Exception as e:
                        logger.warning(f"Video {idx+1} beğeni sayısı çekilemedi: {e}")

                    # Yorum sayısı
                    try:
                        comment_selectors = [
                            "[data-e2e='comment-count']",
                            "[data-e2e='browse-comment-count']"
                        ]
                        for selector in comment_selectors:
                            try:
                                comment_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                                if comment_element.text.strip():
                                    comments_text = comment_element.text.strip()
                                    comments_count = self.format_number(comments_text)
                                    logger.info(f"Video {idx+1} yorum: {comments_text}")
                                    break
                            except:
                                continue
                    except Exception as e:
                        logger.warning(f"Video {idx+1} yorum sayısı çekilemedi: {e}")

                    # Paylaşım sayısı (ekran görüntüsünden)
                    try:
                        share_selectors = [
                            "[data-e2e='share-count']",
                            "[data-e2e='browse-share-count']",
                            "strong[data-e2e='share-count']"
                        ]
                        for selector in share_selectors:
                            try:
                                share_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                                if share_element.text.strip():
                                    shares_text = share_element.text.strip()
                                    shares_count = self.format_number(shares_text)
                                    logger.info(f"Video {idx+1} paylaşım: {shares_text}")
                                    break
                            except:
                                continue
                        else:
                            # Eğer bulunamazsa tahmin et
                            shares_count = int(likes_count * 0.02) if likes_count > 0 else 0
                    except Exception as e:
                        logger.warning(f"Video {idx+1} paylaşım sayısı çekilemedi: {e}")
                        shares_count = int(likes_count * 0.02) if likes_count > 0 else 0

                    # Kaydetme sayısı (ekran görüntüsünden)
                    try:
                        save_selectors = [
                            "[data-e2e='undefined-count']",  # Ekran görüntüsünden
                            "[data-e2e='save-count']",
                            "[data-e2e='browse-save-count']",
                            "strong[data-e2e='undefined-count']"
                        ]
                        for selector in save_selectors:
                            try:
                                save_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                                if save_element.text.strip():
                                    saves_text = save_element.text.strip()
                                    saves_count = self.format_number(saves_text)
                                    logger.info(f"Video {idx+1} kaydetme: {saves_text}")
                                    break
                            except:
                                continue
                        else:
                            # Eğer bulunamazsa tahmin et
                            saves_count = int(likes_count * 0.01) if likes_count > 0 else 0
                    except Exception as e:
                        logger.warning(f"Video {idx+1} kaydetme sayısı çekilemedi: {e}")
                        saves_count = int(likes_count * 0.01) if likes_count > 0 else 0

                    # Video açıklamasını çek
                    description = ""
                    hashtags = []
                    try:
                        # Video açıklama selectors'ları
                        description_selectors = [
                            '[data-e2e="video-desc"]',
                            '[data-e2e="browse-video-desc"]',
                            '.video-meta-caption',
                            '.tt-video-meta-caption',
                            '.video-description',
                            '.tiktok-j2a19u-SpanText',
                            '.tiktok-1ejylhp-DivContainer'
                        ]

                        for selector in description_selectors:
                            try:
                                desc_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                                if desc_element.text.strip():
                                    description = desc_element.text.strip()
                                    # Hashtag'leri çıkar
                                    hashtags = self.extract_hashtags_from_text(description)
                                    logger.info(f"Video {idx+1} açıklama bulundu: {description[:50]}...")
                                    if hashtags:
                                        logger.info(f"Video {idx+1} hashtag'ler: {hashtags}")
                                    break
                            except:
                                continue

                        if not description:
                            description = f"Video {idx+1}"
                            logger.warning(f"Video {idx+1} açıklama bulunamadı")

                    except Exception as e:
                        logger.warning(f"Video {idx+1} açıklama çekme hatası: {e}")
                        description = f"Video {idx+1}"

                    engagement = likes_count + comments_count + shares_count + saves_count
                    engagement_rate = round((engagement / video_info['views_count']) * 100, 1) if video_info['views_count'] > 0 else 0

                    video_data = {
                        'id': video_id,
                        'url': video_info['url'],
                        'views': video_info['views_text'],
                        'views_count': video_info['views_count'],
                        'likes': self.format_number_display(likes_count),
                        'likes_count': likes_count,
                        'comments': self.format_number_display(comments_count),
                        'comments_count': comments_count,
                        'shares': self.format_number_display(shares_count),
                        'shares_count': shares_count,
                        'saves': self.format_number_display(saves_count),
                        'saves_count': saves_count,
                        'published_date': self.extract_video_date(),
                        'description': description,
                        'hashtags': hashtags,
                        'engagement_rate': engagement_rate,
                        'thumbnail': thumbnail_url
                    }

                    videos.append(video_data)

                    total_views += video_info['views_count']
                    total_likes += likes_count
                    total_comments += comments_count
                    total_shares += shares_count
                    total_saves += saves_count

                    logger.info(f"Video {idx+1} analizi tamamlandı: {likes_count} beğeni, {comments_count} yorum, {shares_count} paylaşım, {saves_count} kaydetme")

                except Exception as e:
                    logger.warning(f"Video {idx+1} işlenirken hata: {e}")
                    continue

            # Toplam değerleri hesapla
            total_engagement = total_likes + total_comments + total_shares + total_saves
            total_engagement_rate = round((total_engagement / total_views) * 100, 1) if total_views > 0 else 0
            video_count = len(videos)

            # Hashtag analizi - gerçek verilerden
            all_hashtags = []
            for video in videos:
                if 'hashtags' in video and video['hashtags']:
                    all_hashtags.extend(video['hashtags'])

            # En çok kullanılan hashtag'leri hesapla
            most_used_hashtags = []
            if all_hashtags:
                from collections import Counter
                hashtag_counts = Counter(all_hashtags)
                most_used_hashtags = hashtag_counts.most_common(10)
                logger.info(f"En çok kullanılan hashtag'ler: {most_used_hashtags}")
            else:
                logger.warning("Hiç hashtag bulunamadı")

            profile_data.update({
                'videos': videos,
                'total_views': total_views,
                'total_likes': total_likes,
                'total_comments': total_comments,
                'total_shares': total_shares,
                'total_saves': total_saves,
                'total_engagement': total_engagement,
                'total_engagement_rate': total_engagement_rate,
                'average_views': round(total_views / video_count) if video_count > 0 else 0,
                'average_likes': round(total_likes / video_count) if video_count > 0 else 0,
                'average_comments': round(total_comments / video_count) if video_count > 0 else 0,
                'average_shares': round(total_shares / video_count) if video_count > 0 else 0,
                'average_saves': round(total_saves / video_count) if video_count > 0 else 0,
                'most_used_hashtags': most_used_hashtags
            })

            self.update_request_status(request_id, 'processing', 95, 'Sonuçlar kaydediliyor...')

            # Veritabanına kaydet
            analysis_id = self.save_analysis_result(request_id, profile_data)

            if analysis_id:
                self.update_request_status(request_id, 'completed')
                logger.info(f"Analiz tamamlandı: {username} (Request: {request_id}, Analysis: {analysis_id})")

                # Başarılı analiz sonrası cookies'leri kaydet
                logger.info("Analiz tamamlandı, güncel cookies kaydediliyor...")
                self.save_cookies()

            else:
                raise Exception('Analiz sonucu kaydedilemedi')

        except Exception as e:
            error_msg = str(e)
            logger.error(f"Profil analiz hatası ({username}): {error_msg}")
            self.update_request_status(request_id, 'failed', error_message=error_msg)

    def run(self):
        """Ana döngü"""
        logger.info(f"TikTok VDS İşleyicisi başlatıldı: {self.vds_name}")

        while True:
            try:
                # Veritabanına bağlan
                if not self.db or not self.db.is_connected():
                    if not self.connect_database():
                        logger.warning(f"Veritabanı bağlantısı kurulamadı, {RETRY_INTERVAL} saniye bekleniyor...")
                        time.sleep(RETRY_INTERVAL)
                        continue

                # VDS durumunu güncelle
                self.update_vds_status('online')

                # Bekleyen istek var mı kontrol et
                request = self.get_pending_request()

                if request:
                    logger.info(f"Yeni istek bulundu: @{request['username']} (ID: {request['id']})")

                    # VDS durumunu busy yap
                    self.update_vds_status('busy', request['id'])

                    # Chrome driver'ı başlat
                    if not self.driver:
                        if not self.setup_chrome_driver():
                            self.update_request_status(request['id'], 'failed', error_message='Chrome WebDriver başlatılamadı')
                            continue

                    # Profili analiz et
                    video_count = request.get('video_count', 10)
                    self.process_tiktok_profile(request['id'], request['username'], video_count)

                    # Driver'ı kapat
                    if self.driver:
                        try:
                            self.driver.quit()
                            self.driver = None
                        except:
                            pass

                    # VDS durumunu tekrar online yap
                    self.update_vds_status('online')

                else:
                    # Bekleyen istek yok
                    logger.debug(f"Bekleyen istek yok, {POLL_INTERVAL} saniye bekleniyor...")
                    time.sleep(POLL_INTERVAL)

            except KeyboardInterrupt:
                logger.info("İşleyici kullanıcı tarafından durduruldu")
                break
            except Exception as e:
                logger.error(f"Ana döngü hatası: {e}")
                time.sleep(RETRY_INTERVAL)

        # Temizlik
        logger.info("Temizlik yapılıyor...")

        if self.driver:
            try:
                self.driver.quit()
            except:
                pass

        if self.db:
            try:
                self.update_vds_status('offline')
                self.db.close()
            except:
                pass

        logger.info("TikTok VDS İşleyicisi durduruldu")

if __name__ == '__main__':
    processor = TikTokVDSProcessor()
    processor.run()
