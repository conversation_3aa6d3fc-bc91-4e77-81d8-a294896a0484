import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaTimes, FaCheck } from 'react-icons/fa';

interface ForgotPasswordModalProps {
  isOpen: boolean;
  onClose: () => void;
  darkMode: boolean;
}

const ForgotPasswordModal: React.FC<ForgotPasswordModalProps> = ({ isOpen, onClose, darkMode }) => {
  const [email, setEmail] = useState('');
  const [step, setStep] = useState(1); // 1: Email giriş, 2: Başarılı
  const [loading, setLoading] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    // Şifre sıfırlama simülasyonu
    setTimeout(() => {
      setLoading(false);
      setStep(2); // Başarılı adıma geç
    }, 1500);
  };

  const modalVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: { opacity: 1, scale: 1, transition: { duration: 0.2 } },
    exit: { opacity: 0, scale: 0.95, transition: { duration: 0.2 } }
  };

  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
    exit: { opacity: 0 }
  };

  const handleClose = () => {
    // Modal'ı kapat ve step'i sıfırla
    onClose();
    setTimeout(() => {
      setStep(1);
      setEmail('');
    }, 300);
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 z-50 flex items-center justify-center p-4"
        initial="hidden"
        animate="visible"
        exit="exit"
        variants={backdropVariants}
      >
        {/* Arka plan overlay */}
        <div 
          className="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm" 
          onClick={handleClose}
        />

        {/* Modal içeriği */}
        <motion.div
          className={`relative ${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-xl shadow-xl max-w-md w-full p-6 overflow-hidden`}
          variants={modalVariants}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Kapatma butonu */}
          <button 
            onClick={handleClose}
            className={`absolute top-4 right-4 ${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-800'} focus:outline-none`}
          >
            <FaTimes className="h-5 w-5" />
          </button>

          {step === 1 ? (
            // Adım 1: Email girişi
            <>
              <div className="text-center mb-6">
                <div className={`text-sm font-medium ${darkMode ? 'text-tuber-pink' : 'text-tuber-pink'} uppercase tracking-wider`}>
                  ŞİFRE SIFIRLAMA
                </div>
                <h2 className={`text-xl font-bold mt-1 ${darkMode ? 'text-white' : 'text-gray-800'}`}>
                  Şifrenizi mi unuttunuz?
                </h2>
                <p className={`mt-2 text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  E-posta adresinizi girin, size şifre sıfırlama bağlantısı göndereceğiz.
                </p>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label htmlFor="email" className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    E-posta Adresi
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <svg xmlns="http://www.w3.org/2000/svg" className={`h-5 w-5 ${darkMode ? 'text-gray-500' : 'text-gray-400'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                      </svg>
                    </div>
                    <input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                      className={`block w-full pl-10 pr-3 py-3 rounded-lg text-sm focus:outline-none focus:ring-2 ${
                        darkMode 
                          ? 'bg-gray-700 border-gray-600 text-white focus:ring-tuber-pink' 
                          : 'bg-gray-50 border-gray-200 text-gray-900 focus:ring-tuber-pink'
                      }`}
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="pt-2">
                  <motion.button
                    type="submit"
                    disabled={loading}
                    className={`w-full flex items-center justify-center py-3 px-4 rounded-lg shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                      darkMode
                        ? 'bg-tuber-pink text-white hover:bg-tuber-pink/90 focus:ring-tuber-pink'
                        : 'bg-tuber-pink text-white hover:bg-tuber-pink/90 focus:ring-tuber-pink'
                    } transition-all duration-200`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {loading ? (
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    ) : 'Sıfırlama Bağlantısı Gönder'}
                  </motion.button>
                </div>
              </form>
            </>
          ) : (
            // Adım 2: Başarılı mesaj
            <div className="text-center py-4">
              <div className={`mx-auto flex items-center justify-center h-12 w-12 rounded-full ${darkMode ? 'bg-green-900' : 'bg-green-100'} mb-4`}>
                <FaCheck className={`h-6 w-6 ${darkMode ? 'text-green-400' : 'text-green-600'}`} />
              </div>
              <h2 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'} mb-2`}>
                E-posta Gönderildi
              </h2>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-6`}>
                {email} adresine şifre sıfırlama bağlantısını gönderdik. Lütfen e-posta kutunuzu kontrol edin.
              </p>
              <motion.button
                onClick={handleClose}
                className={`w-full flex items-center justify-center py-3 px-4 rounded-lg shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                  darkMode
                    ? 'bg-tuber-pink text-white hover:bg-tuber-pink/90 focus:ring-tuber-pink'
                    : 'bg-tuber-pink text-white hover:bg-tuber-pink/90 focus:ring-tuber-pink'
                } transition-all duration-200`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                Tamam
              </motion.button>
            </div>
          )}
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default ForgotPasswordModal;