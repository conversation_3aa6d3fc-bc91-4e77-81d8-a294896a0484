<?php
header('Content-Type: application/json; charset=utf-8');

$logFile = __DIR__ . '/whatsapp_incoming_log.txt';
$lines = file_exists($logFile) ? file($logFile) : [];
$contacts = [];
$unique = [];
foreach ($lines as $line) {
    $json = trim(substr($line, 27)); // <PERSON><PERSON>h kısmını atla
    $data = json_decode($json, true);
    if ($data && isset($data['entry'][0]['changes'][0]['value']['messages'][0]['from'])) {
        $from = $data['entry'][0]['changes'][0]['value']['messages'][0]['from'];
        $name = $data['entry'][0]['changes'][0]['value']['contacts'][0]['profile']['name'] ?? $from;
        if (!isset($unique[$from])) {
            $contacts[] = [
                'id' => $from,
                'name' => $name,
                'phone' => '+' . $from
            ];
            $unique[$from] = true;
        }
    }
}
echo json_encode(['contacts' => $contacts]); 