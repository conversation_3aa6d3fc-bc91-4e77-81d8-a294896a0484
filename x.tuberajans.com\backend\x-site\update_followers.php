<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/auth.php';

header('Content-Type: application/json');

// TikTok takipçi sayısını çeken fonksiyon (test.php'den alınmıştır)
function getTikTokFollowerCount($username) {
    $username = trim(str_replace('@', '', $username));
    if (empty($username)) {
        return [
            'success' => false,
            'error' => 'Kullanıcı adı boş olamaz'
        ];
    }
    $tiktokUrl = "https://www.tiktok.com/@{$username}";
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $tiktokUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        CURLOPT_ENCODING => 'gzip, deflate',
        CURLOPT_TIMEOUT => 20
    ]);
    $html = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    if ($httpCode !== 200 || empty($html)) {
        return [
            'success' => false,
            'error' => "TikTok profiline erişim başarısız oldu (HTTP Kodu: $httpCode)"
        ];
    }
    $followerCount = null;
    if (preg_match('/"followerCount"\s*:\s*(\d+)/', $html, $matches)) {
        $followerCount = $matches[1];
    } elseif (preg_match('/<strong[^>]*>(\d+(?:\.\d+)?[KMB]?)<\/strong>(?:.*?)takipçi/si', $html, $matches)) {
        $followerCount = $matches[1];
    }
    if ($followerCount !== null) {
        return [
            'success' => true,
            'follower_count' => $followerCount
        ];
    } else {
        return [
            'success' => false,
            'error' => 'Takipçi sayısı bulunamadı.'
        ];
    }
}

try {
    $userId = requireAuthToken();
    $input = json_decode(file_get_contents('php://input'), true);
    if (!isset($input['usernames']) || !is_array($input['usernames'])) {
        echo json_encode(['success' => false, 'error' => 'Kullanıcı adı listesi eksik veya hatalı.']);
        exit;
    }
    $usernames = $input['usernames'];
    $results = [];
    foreach ($usernames as $username) {
        $username = trim($username);
        if (!$username) continue;
        $result = getTikTokFollowerCount($username);
        if ($result['success']) {
            // Veritabanında güncelle
            $stmt = $db->prepare("UPDATE influencer_info SET followers = ? WHERE username = ?");
            $stmt->execute([$result['follower_count'], $username]);
            $results[] = [
                'username' => $username,
                'followers' => $result['follower_count'],
                'success' => true
            ];
        } else {
            $results[] = [
                'username' => $username,
                'error' => $result['error'],
                'success' => false
            ];
        }
    }
    echo json_encode(['success' => true, 'results' => $results]);
} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
} 