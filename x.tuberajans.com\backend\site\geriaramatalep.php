<?php
header('Content-Type: application/json; charset=utf-8');
require_once __DIR__ . '/../config/config.php';

$pdo = $db;

$action = isset($_GET['action']) ? $_GET['action'] : 'list';

if ($action === 'list') {
    try {
        $stmt = $pdo->query('SELECT * FROM geriaramatalepleri ORDER BY id DESC');
        $data = array_map(function($row) {
            return [
                'id' => $row['id'],
                'adsoyad' => $row['fname'],
                'telefon' => $row['phone'],
                'tiktok' => $row['tiktok_username'],
                'zaman' => $row['unixts'],
                'isRead' => $row['isRead']
            ];
        }, $stmt->fetchAll());
        echo json_encode(['success' => true, 'data' => $data]);
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'message' => 'Veriler alınamadı', 'error' => $e->getMessage()]);
    }
    exit;
}

if ($action === 'okundu') {
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    if ($id > 0) {
        try {
            $stmt = $pdo->prepare('UPDATE geriaramatalepleri SET isRead=1 WHERE id=?');
            $stmt->execute([$id]);
            echo json_encode(['success' => true]);
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'message' => 'Okundu olarak işaretlenemedi', 'error' => $e->getMessage()]);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Geçersiz ID']);
    }
    exit;
}

if ($action === 'delete') {
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    if ($id > 0) {
        try {
            $stmt = $pdo->prepare('DELETE FROM geriaramatalepleri WHERE id=?');
            $stmt->execute([$id]);
            echo json_encode(['success' => true]);
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'message' => 'Silinemedi', 'error' => $e->getMessage()]);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Geçersiz ID']);
    }
    exit;
}

if ($action === 'count') {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM geriaramatalepleri WHERE isRead='0'");
        $row = $stmt->fetch();
        echo json_encode(['success' => true, 'count' => intval($row['count'])]);
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'message' => 'Sayı alınamadı', 'error' => $e->getMessage()]);
    }
    exit;
}

echo json_encode(['success' => false, 'message' => 'Geçersiz action']);
exit; 