#!/usr/bin/env python3
"""
Veritabanı Bağlantı Test Script'i
Bu script VDS'den web sitesi veritabanına bağlantıyı test eder.
"""

import mysql.connector
from config import DB_CONFIG

def test_database_connection():
    """Veritabanı bağlantısını test et"""
    print("🔍 Veritabanı bağlantısı test ediliyor...")
    print(f"📍 Host: {DB_CONFIG['host']}")
    print(f"📍 Database: {DB_CONFIG['database']}")
    print(f"📍 User: {DB_CONFIG['user']}")
    print()
    
    try:
        # Bağlantıyı dene
        db = mysql.connector.connect(**DB_CONFIG)
        cursor = db.cursor()
        
        print("✅ Veritabanı bağlantısı başarılı!")
        
        # Tabloları kontrol et
        print("\n📋 Tablolar kontrol ediliyor...")
        
        tables_to_check = [
            'tiktok_analysis',
            'tiktok_requests', 
            'vds_status'
        ]
        
        for table in tables_to_check:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"✅ {table}: {count} kayıt")
            except Exception as e:
                print(f"❌ {table}: Hata - {e}")
        
        # Test isteği oluştur
        print("\n🧪 Test isteği oluşturuluyor...")
        try:
            cursor.execute("""
                INSERT INTO tiktok_requests (username, ip_address, status, current_step, progress)
                VALUES ('test_user', '127.0.0.1', 'pending', 'Test isteği', 0)
            """)
            db.commit()
            request_id = cursor.lastrowid
            print(f"✅ Test isteği oluşturuldu: ID {request_id}")
            
            # Test isteğini sil
            cursor.execute("DELETE FROM tiktok_requests WHERE id = %s", (request_id,))
            db.commit()
            print("🗑️ Test isteği silindi")
            
        except Exception as e:
            print(f"❌ Test isteği hatası: {e}")
        
        # VDS durumunu güncelle
        print("\n📡 VDS durumu güncelleniyor...")
        try:
            cursor.execute("""
                INSERT INTO vds_status (vds_name, status, last_ping)
                VALUES ('VDS-TEST', 'online', NOW())
                ON DUPLICATE KEY UPDATE
                status = VALUES(status),
                last_ping = NOW()
            """)
            db.commit()
            print("✅ VDS durumu güncellendi")
            
        except Exception as e:
            print(f"❌ VDS durum güncelleme hatası: {e}")
        
        db.close()
        print("\n🎉 Tüm testler başarılı! VDS hazır.")
        return True
        
    except mysql.connector.Error as e:
        print(f"❌ Veritabanı hatası: {e}")
        print("\n💡 Çözüm önerileri:")
        print("1. config.py dosyasındaki IP adresini kontrol edin")
        print("2. MySQL şifresini kontrol edin")
        print("3. Firewall ayarlarını kontrol edin")
        print("4. MySQL servisinin çalıştığını kontrol edin")
        return False
        
    except Exception as e:
        print(f"❌ Genel hata: {e}")
        return False

if __name__ == '__main__':
    print("🧪 TikTok VDS Bağlantı Testi")
    print("=" * 40)
    
    success = test_database_connection()
    
    if success:
        print("\n✅ Test başarılı! Ana script'i çalıştırabilirsiniz:")
        print("   python3 vds-tiktok-processor.py")
    else:
        print("\n❌ Test başarısız! Lütfen ayarları kontrol edin.")
        print("   nano config.py")
