<?php
// Direct test of weekly_tasks.php logic

// Mock superglobals
$_SERVER['REQUEST_METHOD'] = 'GET';
$_GET['kullanici_adi'] = '61.rahab.61';
$_GET['start_date'] = '2025-05-19';
$_GET['end_date'] = '2025-05-26';

// Mock token for testing
$_SERVER['HTTP_AUTHORIZATION'] = 'Bearer test-token';

// Capture output
ob_start();

try {
    include 'x-site/weekly_tasks.php';
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}

$output = ob_get_clean();

echo "=== API OUTPUT ===\n";
echo $output . "\n";

// Try to parse JSON
$data = json_decode($output, true);
if ($data) {
    echo "\n=== PARSED DATA ===\n";
    
    if (isset($data['data'])) {
        echo "Görev Sayısı: " . count($data['data']) . "\n";
        
        foreach ($data['data'] as $index => $task) {
            echo "\nGörev " . ($index + 1) . ":\n";
            echo "  Açıklama: " . $task['gorev_onerisi'] . "\n";
            echo "  Zorluk: " . $task['gorev_zorlugu'] . "\n";
            echo "  Durum: " . $task['durum'] . "\n";
            echo "  Tamamlandı: " . ($task['tamamlandi'] ? 'EVET' : 'HAYIR') . "\n";
            if (isset($task['auto_completed'])) {
                echo "  Otomatik Kontrol: " . ($task['auto_completed'] ? 'EVET' : 'HAYIR') . "\n";
            }
            echo "  Puan: " . $task['puan'] . "\n";
        }
    }
    
    if (isset($data['performance'])) {
        echo "\n=== PERFORMANS VERİLERİ ===\n";
        if ($data['performance']) {
            foreach ($data['performance'] as $key => $value) {
                echo "  $key: $value\n";
            }
        } else {
            echo "  Performans verisi bulunamadı\n";
        }
    }
    
    if (isset($data['debug_info'])) {
        echo "\n=== DEBUG BİLGİLERİ ===\n";
        foreach ($data['debug_info'] as $key => $value) {
            echo "  $key: " . ($value ? 'true' : 'false') . "\n";
        }
    }
}
?> 