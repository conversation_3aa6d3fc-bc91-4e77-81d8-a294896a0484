<?php
header('Content-Type: application/json; charset=utf-8');

require_once __DIR__ . '/../config/config.php';

try {
    $tables = [];
    $stmt = $db->query('SHOW TABLES');
    $tableNames = $stmt->fetchAll(PDO::FETCH_NUM);
    foreach ($tableNames as $row) {
        $table = $row[0];
        $columnsStmt = $db->query("SHOW COLUMNS FROM `$table`");
        $columns = $columnsStmt->fetchAll(PDO::FETCH_ASSOC);
        $tables[$table] = array_map(function($col) {
            return [
                'Field' => $col['Field'],
                'Type' => $col['Type'],
                'Null' => $col['Null'],
                'Key' => $col['Key'],
                'Default' => $col['Default'],
                'Extra' => $col['Extra']
            ];
        }, $columns);
    }
    echo json_encode(['success' => true, 'tables' => $tables], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
} 