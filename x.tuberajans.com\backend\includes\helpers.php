<?php
/**
 * Helpers.php - Ortak kullanılan yardımcı fonksiyonlar
 * 
 * Bu dosya, tüm backend dosyaları tarafından kullanılacak ortak fonksiyonları içerir.
 * Kod tekrarını önlemek ve bakımı kolaylaştırmak için tasarlanmıştır.
 */

// JSON yanıt gönderen fonksiyon
if (!function_exists('jsonResponse')) {
    function jsonResponse($data, $status = 200) {
        http_response_code($status);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}

// CORS ayarlarını yapılandıran fonksiyon
if (!function_exists('configureCORS')) {
    function configureCORS() {
        // CORS ayarları
        header("Access-Control-Allow-Origin: *");
        header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
        header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
        
        // OPTIONS isteği varsa hızlıca cevap ver
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit();
        }
    }
}

// Gelen JSON verilerini doğrulayan ve işleyen fonksiyon
if (!function_exists('validateRequest')) {
    function validateRequest($required_fields = []) {
        $raw_input = file_get_contents('php://input');
        $data = json_decode($raw_input, true);
        
        if ($data === null && json_last_error() !== JSON_ERROR_NONE) {
            jsonResponse(['error' => 'Geçersiz JSON verisi'], 400);
        }
        
        // Gerekli alanları kontrol et
        foreach ($required_fields as $field) {
            if (!isset($data[$field])) {
                jsonResponse(['error' => "Eksik alan: $field"], 400);
            }
        }
        
        return $data;
    }
}

// XSS koruması
if (!function_exists('xss_clean')) {
    function xss_clean($data) {
        return htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    }
}

// Güvenli input temizleme
if (!function_exists('clean_input')) {
    function clean_input($data) {
        $data = trim($data);
        $data = stripslashes($data);
        $data = htmlspecialchars($data);
        return $data;
    }
}
?> 