import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaU<PERSON>, FaLock, FaEye, FaEyeSlash, FaSignInAlt } from 'react-icons/fa';
import { SiTiktok } from 'react-icons/si';
import axios from 'axios';

const Login: React.FC = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setError('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    if (!formData.username || !formData.password) {
      setError('Lütfen tüm alanları doldurun.');
      setLoading(false);
      return;
    }

    try {
      const response = await axios.post('/backend/api/login.php', {
        username: formData.username,
        password: formData.password
      });

      if (response.data.status === 'success') {
        // Kullanıcı bilgilerini localStorage'a kaydet
        if (response.data.user) {
          localStorage.setItem('user', JSON.stringify(response.data.user));
        }

        // Başarılı giriş - dashboard'a yönlendir
        navigate('/dashboard');
      } else {
        setError(response.data.message || 'Giriş işlemi başarısız.');
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Giriş işlemi sırasında hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const handleTikTokLogin = () => {
    const clientKey = 'awfw8k9nim1e8dmu';
    const redirectUri = encodeURIComponent('https://akademi.tuberajans.com/backend/api/tiktok-callback.php');
    const state = Math.random().toString(36).substring(2, 15);
    localStorage.setItem('tiktok_oauth_state', state);
    const scope = 'user.info.basic,user.info.profile,user.info.stats,video.list';
    const url = `https://www.tiktok.com/v2/auth/authorize/?client_key=${clientKey}&response_type=code&scope=${scope}&redirect_uri=${redirectUri}&state=${state}`;
    window.location.href = url;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-md w-full space-y-8"
      >
        {/* Logo ve Başlık */}
        <div className="text-center">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            className="mx-auto h-16 w-16 bg-gradient-to-r from-[#FF3E71] to-[#FF5F87] rounded-full flex items-center justify-center mb-4"
          >
            <FaSignInAlt className="h-8 w-8 text-white" />
          </motion.div>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
            Giriş Yapın
          </h2>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            Tuber Akademi hesabınıza giriş yapın
          </p>
        </div>

        {/* Giriş Formu */}
        <motion.form
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="mt-8 space-y-6 bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg"
          onSubmit={handleSubmit}
        >
          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 px-4 py-3 rounded-lg text-sm">
              {error}
            </div>
          )}

          <div className="space-y-4">
            {/* Kullanıcı Adı / E-posta */}
            <div>
              <label htmlFor="username" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Kullanıcı Adı veya E-posta
              </label>
              <div className="relative">
                <FaUser className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  id="username"
                  name="username"
                  type="text"
                  required
                  value={formData.username}
                  onChange={handleInputChange}
                  className="pl-10 w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#FF3E71] focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="Kullanıcı adınız veya e-posta"
                />
              </div>
            </div>

            {/* Şifre */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Şifre
              </label>
              <div className="relative">
                <FaLock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  required
                  value={formData.password}
                  onChange={handleInputChange}
                  className="pl-10 pr-10 w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#FF3E71] focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="Şifreniz"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <FaEyeSlash /> : <FaEye />}
                </button>
              </div>
            </div>
          </div>

          {/* Giriş Butonu */}
          <button
            type="submit"
            disabled={loading}
            className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-[#FF3E71] to-[#FF5F87] hover:from-[#FF5F87] hover:to-[#FF3E71] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF3E71] disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
          >
            {loading ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Giriş Yapılıyor...
              </div>
            ) : (
              'Giriş Yap'
            )}
          </button>

          {/* Ayırıcı */}
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300 dark:border-gray-600" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white dark:bg-gray-800 text-gray-500">veya</span>
            </div>
          </div>

          {/* TikTok ile Giriş */}
          <button
            type="button"
            onClick={handleTikTokLogin}
            className="w-full flex justify-center items-center py-3 px-4 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF3E71] transition-all duration-200"
          >
            <SiTiktok className="w-5 h-5 mr-2" />
            TikTok ile Giriş Yap
          </button>

          {/* Kayıt Linki */}
          <div className="text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Hesabınız yok mu?{' '}
              <Link
                to="/register"
                className="font-medium text-[#FF3E71] hover:text-[#FF5F87] transition-colors"
              >
                Kayıt Olun
              </Link>
            </p>
          </div>

          {/* Şifremi Unuttum */}
          <div className="text-center">
            <Link
              to="/forgot-password"
              className="text-sm text-gray-500 hover:text-[#FF3E71] transition-colors"
            >
              Şifremi Unuttum
            </Link>
          </div>
        </motion.form>
      </motion.div>
    </div>
  );
};

export default Login;
