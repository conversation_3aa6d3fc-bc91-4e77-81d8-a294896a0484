2025-06-16 13:08:09,634 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:761) (automation_worker.py:251)
2025-06-16 13:08:09,647 [INFO] __main__ - 📁 Çalışma dizini: C:\Users\<USER>\Desktop\Tuber X-Akademi (automation_worker.py:252)
2025-06-16 13:08:09,692 [INFO] __main__ - 🐍 Python versiyonu: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)] (automation_worker.py:253)
2025-06-16 13:08:10,247 [INFO] __main__ - ✅ params alanı LONGTEXT'e g<PERSON> (automation_worker.py:502) (automation_worker.py:182)
2025-06-16 13:08:10,918 [INFO] __main__ - ✅ <PERSON>stem hazır - komut<PERSON> bekleniyor (automation_worker.py:647) (automation_worker.py:195)
2025-06-16 13:08:10,921 [INFO] __main__ - 📨 Yeni komut bulundu: start (ID: 348) (automation_worker.py:570) (automation_worker.py:210)
2025-06-16 13:08:11,104 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:761) (automation_worker.py:97)
2025-06-16 13:08:11,115 [INFO] __main__ - 1️⃣ Scraper başlatılıyor (süre: 1 dakika) (main.py:296) (automation_worker.py:98)
2025-06-16 13:08:11,214 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dakika) (main.py:291)
2025-06-16 13:08:11,266 [INFO] __main__ - ✅ Komut başarıyla işlendi: start (ID: 348) (automation_worker.py:585) (automation_worker.py:109)
2025-06-16 13:08:11,289 [INFO] scraper_thread - 🔄 Chrome işlemleri temizleniyor... (scraper_thread.py:291)
2025-06-16 13:08:13,330 [INFO] __main__ - 📨 Yeni komut bulundu: start (ID: 349) (automation_worker.py:570) (automation_worker.py:210)
2025-06-16 13:08:13,380 [INFO] __main__ - ⚠️ Otomasyon zaten çalışıyor, yeni başlatma isteği reddediliyor. (automation_worker.py:91)
2025-06-16 13:08:13,579 [INFO] scraper_thread - ✅ Chrome işlemleri temizlendi (scraper_thread.py:291)
2025-06-16 13:08:13,580 [ERROR] scraper_thread - Chrome başlatma hatası: Message: Unable to locate or obtain driver for chrome; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location
 (scraper_thread.py:295)
2025-06-16 13:08:13,581 [ERROR] scraper_thread - Chrome başlatılamadı! İşlem sonlandırılıyor. (scraper_thread.py:295)
2025-06-16 13:08:14,282 [INFO] main - 🏁 Scraper thread tamamlandı (main.py:123)
2025-06-16 13:08:14,282 [INFO] main - 🔄 Chrome işlemleri kapatılıyor... (main.py:76)
2025-06-16 13:08:16,531 [INFO] main - ✅ Chrome işlemleri kapatıldı (main.py:88)
2025-06-16 13:08:21,547 [INFO] main - 🔄 Kullanıcı bulunamadı, scraper'a geri dönülüyor (main.py:163)
2025-06-16 13:08:39,775 [INFO] __main__ - 📨 Yeni komut bulundu: start (ID: 350) (automation_worker.py:570) (automation_worker.py:210)
2025-06-16 13:08:39,779 [INFO] __main__ - ⚠️ Otomasyon zaten çalışıyor, yeni başlatma isteği reddediliyor. (automation_worker.py:91)
2025-06-16 13:17:45,242 [INFO] __main__ - ⏹️ Program kullanıcı tarafından durduruldu. (automation_worker.py:256)
2025-06-16 13:17:45,243 [INFO] __main__ - 🧹 Önceki MainApp instance temizleniyor... (automation_worker.py:64)
2025-06-16 13:17:45,244 [INFO] main - Otomasyon durdurma sinyali gönderildi (main.py:243)
2025-06-16 13:17:45,244 [INFO] main - Scraper thread durduruldu (main.py:248)
2025-06-16 13:17:45,244 [INFO] main - Tüm thread'ler durduruldu (main.py:258)
2025-06-16 13:17:45,524 [INFO] __main__ - 🔚 Program sonlandırılıyor... (automation_worker.py:263)
