import React, { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useTikTok } from '../../contexts/TikTokContext';
import { motion, AnimatePresence } from 'framer-motion';
import axios from 'axios';
import {
  Bars3Icon,
  BellIcon,
  UserCircleIcon,
  ArrowRightOnRectangleIcon,
  SunIcon,
  MoonIcon
} from '@heroicons/react/24/outline';
import { BellAlertIcon } from '@heroicons/react/24/outline';
import { FaBars, FaBell, FaUser } from 'react-icons/fa';

interface Notification {
  id: number;
  title: string;
  message: string;
  time: string;
  read: boolean;
  type: string;
  source?: string;
  source_id?: string;
  link?: string;
}

export interface TopbarProps {
  onLogout: () => void;
  toggleSidebar: () => void;
  isSidebarOpen: boolean;
  username: string;
}

interface UserInfo {
  id: number;
  name: string;
  username?: string;
  email: string;
  profile_image?: string;
  avatar_url?: string | null;
  is_verified?: boolean;
  role: string;
}

const Topbar: React.FC<TopbarProps> = ({
  onLogout,
  toggleSidebar,
  isSidebarOpen,
  username
}) => {
  // Development mode kontrolü
  const isDevMode = import.meta.env.VITE_DISABLE_AUTH === 'true' || import.meta.env.DEV;

  // AuthContext'i sadece production modunda kullan
  let logout: any = null;
  if (!isDevMode) {
    try {
      const authContext = useAuth();
      logout = authContext.logout;
    } catch (error) {
      console.error('Auth context error in Topbar:', error);
      // Fallback: basit logout fonksiyonu
      logout = () => {
        localStorage.removeItem('user');
        localStorage.removeItem('token');
        return Promise.resolve();
      };
    }
  } else {
    // Development modunda basit logout fonksiyonu
    logout = () => {
      localStorage.removeItem('user');
      localStorage.removeItem('token');
      return Promise.resolve();
    };
  }

  const [profileMenuOpen, setProfileMenuOpen] = useState(false);
  const [notificationsOpen, setNotificationsOpen] = useState(false);
  const [darkMode, setDarkMode] = useState(localStorage.getItem('darkMode') === 'true');
  const profileMenuRef = useRef<HTMLDivElement>(null);
  const notificationsRef = useRef<HTMLDivElement>(null);

  // Profil menüsünü dışına tıklanınca kapat
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (profileMenuRef.current && !profileMenuRef.current.contains(event.target as Node)) {
        setProfileMenuOpen(false);
      }
      if (notificationsRef.current && !notificationsRef.current.contains(event.target as Node)) {
        setNotificationsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Tema değişimi için useEffect
  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add('dark');
      localStorage.setItem('darkMode', 'true');
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('darkMode', 'false');
    }
  }, [darkMode]);

  // Tema değişim işlevi
  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
  };

  // Bildirimler için state
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const unreadCount = notifications.filter(n => !n.read).length;

  // Kullanıcı bilgileri için state
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [userLoading, setUserLoading] = useState(false); // Başlangıçta false yap

  // TikTok Context'ten verileri al
  const { tiktokUser } = useTikTok();

  // Bildirimleri ve kullanıcı bilgilerini API'den çek - sadece bir kez
  useEffect(() => {
    fetchNotifications();
    fetchUserInfo();
  }, []); // Dependency array boş - sadece mount'ta çalışsın

  // TikTok giriş sonrası kullanıcı bilgilerini yenile
  useEffect(() => {
    if (refreshTrigger) {
      fetchUserInfo();
      // TikTok Context zaten kendi cache'ini yönetiyor, gereksiz çağrı yapma
      // refreshTikTokUser();
    }
  }, [refreshTrigger]); // Dependency array'den refreshTikTokUser'ı kaldır

  // Bildirimleri getir
  const fetchNotifications = async () => {
    setLoading(true);
    setError(null);
    try {
      // Development modunda mock veri kullan
      if (isDevMode) {
        console.log('Development mode: Mock bildirim verileri kullanılıyor');

        const mockNotifications = [
          {
            id: 1,
            title: 'Yeni Eğitim Eklendi',
            message: 'TikTok İçerik Üretimi Temelleri eğitimi eklendi.',
            time: '2 saat önce',
            read: false,
            type: 'info',
            source: 'new_course',
            source_id: "1",
            link: '/dashboard/courses/1'
          },
          {
            id: 2,
            title: 'Etkinlik Hatırlatması',
            message: 'TikTok Trendleri Workshop yarın başlıyor.',
            time: '1 gün önce',
            read: true,
            type: 'warning',
            source: 'new_event',
            source_id: "1",
            link: '/dashboard/events'
          }
        ];

        setNotifications(mockNotifications);
        setLoading(false);
        return;
      }

      // Production modunda gerçek API çağrısı
      const userId = 1; // Örnek kullanıcı ID'si

      const response = await axios.get('/backend/api/api_data.php', {
        params: {
          endpoint: 'notifications',
          user_id: userId,
          limit: 10
        }
      });

      if (response.data.status === 'success') {
        // API'den gelen verileri formatla
        const formattedNotifications = response.data.data.map((item: any) => ({
          id: item.id,
          title: item.title,
          message: item.message,
          time: formatTimeAgo(new Date(item.created_at)),
          read: item.read === 1,
          type: item.type,
          source: item.source,
          source_id: item.source_id,
          link: item.link
        }));

        setNotifications(formattedNotifications);
      } else {
        setError('Bildirimler alınamadı');
        console.error('Bildirimler alınamadı:', response.data.message);
      }
    } catch (err) {
      setError('Bildirimler yüklenirken bir hata oluştu');
      console.error('Bildirimler yüklenirken hata:', err);
    } finally {
      setLoading(false);
    }
  };

  // Kullanıcı bilgilerini getir - cache ile
  const fetchUserInfo = async () => {
    try {
      // Cache kontrolü - son 60 saniye içinde çağrı yapıldıysa skip et
      const lastFetchTime = localStorage.getItem('user_info_last_fetch');
      const now = Date.now();

      if (lastFetchTime && (now - parseInt(lastFetchTime)) < 60000) {
        console.log('Topbar: User info cache aktif, API çağrısı atlanıyor');
        return;
      }

      setUserLoading(true);
      // Development modunda mock veri kullan
      const isDevMode = import.meta.env.VITE_DISABLE_AUTH === 'true' || import.meta.env.DEV;

      if (isDevMode) {
        console.log('Development mode: Mock kullanıcı verileri kullanılıyor (Topbar)');

        // localStorage'dan kullanıcı bilgilerini al
        const savedUser = localStorage.getItem('user');
        if (savedUser) {
          const parsedUser = JSON.parse(savedUser);
          const mockUserData = {
            id: parsedUser.id || 1,
            name: parsedUser.name || parsedUser.username || 'Ahmet Yılmaz',
            username: parsedUser.username || 'ahmet_yilmaz',
            email: parsedUser.email || '<EMAIL>',
            avatar_url: parsedUser.avatar_url || 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face&auto=format&q=80',
            is_verified: parsedUser.is_verified || true,
            role: parsedUser.role || 'admin'
          };
          setUserInfo(mockUserData);
        } else {
          // Fallback mock kullanıcı verisi
          const mockUserData = {
            id: 1,
            name: 'Ahmet Yılmaz',
            username: 'ahmet_yilmaz',
            email: '<EMAIL>',
            avatar_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face&auto=format&q=80',
            is_verified: true,
            role: 'admin'
          };
          setUserInfo(mockUserData);
        }

        setUserLoading(false);
        return;
      }

      // Production modunda gerçek API çağrısı
      const response = await axios.get('/backend/api/user_info.php');

      // Cache zamanını güncelle
      localStorage.setItem('user_info_last_fetch', now.toString());

      if (response.data.success) {
        setUserInfo(response.data.data);
      } else {
        console.error('Kullanıcı bilgileri alınamadı:', response.data.message);
        // API başarısız olduğunda mock veri kullan
        setMockUserData();
      }
    } catch (err: any) {
      console.error('Kullanıcı bilgileri yüklenirken hata:', err);

      // Hata türüne göre işlem yap
      if (axios.isAxiosError(err)) {
        if (err.code === 'ECONNABORTED' || err.message?.includes('timeout')) {
          console.log('Topbar: Timeout hatası - mock veri kullan');
        } else if (err.response?.status === 404) {
          console.log('Topbar: Kullanıcı bulunamadı - mock veri kullan');
        } else {
          console.log('Topbar: API hatası - mock veri kullan');
        }
      } else {
        console.log('Topbar: Network dışı hata - mock veri kullan');
      }

      // Hata durumunda mock veri kullan
      setMockUserData();
    } finally {
      setUserLoading(false);
    }
  };

  // Mock veri ayarlama fonksiyonu
  const setMockUserData = () => {
    const mockUserData = {
      id: 1,
      name: 'Demo Kullanıcı',
      username: 'demo_user',
      email: '<EMAIL>',
      avatar_url: '',
      is_verified: false,
      role: 'user'
    };
    setUserInfo(mockUserData);
    setUserLoading(false);
  };

  // Tek bir bildirimi okundu yap
  const markAsRead = async (id: number) => {
    try {
      const response = await axios.put('/backend/api/api_data.php?endpoint=notifications', {
        id: id
      });

      if (response.data.status === 'success') {
        setNotifications(notifications.map(n =>
          n.id === id ? { ...n, read: true } : n
        ));
      }
    } catch (err) {
      console.error('Bildirim okundu olarak işaretlenirken hata:', err);
    }
  };

  // Tüm bildirimleri okundu yap
  const markAllAsRead = async () => {
    try {
      const response = await axios.put('/backend/api/api_data.php?endpoint=notifications', {
        mark_all: true
      });

      if (response.data.status === 'success') {
        setNotifications(notifications.map(n => ({ ...n, read: true })));
      }
    } catch (err) {
      console.error('Tüm bildirimler okundu olarak işaretlenirken hata:', err);
    }
  };

  // Zaman formatı için yardımcı fonksiyon
  const formatTimeAgo = (date: Date): string => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);

    if (diffSec < 60) {
      return `${diffSec} saniye önce`;
    } else if (diffMin < 60) {
      return `${diffMin} dakika önce`;
    } else if (diffHour < 24) {
      return `${diffHour} saat önce`;
    } else if (diffDay < 30) {
      return `${diffDay} gün önce`;
    } else {
      return date.toLocaleDateString('tr-TR');
    }
  };

  return (
    <header className="bg-black dark:bg-black border-b border-gray-800 h-16 flex items-center px-4 shadow-sm z-20 sticky top-0 transition-colors duration-200">
      <button
        onClick={toggleSidebar}
        className="p-2 mr-3 focus:outline-none rounded-lg transition-all text-gray-300"
        aria-label="Toggle sidebar"
      >
        <FaBars className="w-6 h-6" />
      </button>
      {/* Logo */}
      <img
        src="/images/logotuber1.png"
        alt="Tuber X Akademi Logo"
        className="h-8 w-auto rounded-lg select-none mr-4"
        style={{objectFit: 'contain'}}
      />
      <div className="ml-auto flex items-center space-x-2 pr-4">
        {/* Tema Değiştirme Butonu */}
        <button
          onClick={toggleDarkMode}
          className="p-2 text-gray-500 dark:text-gray-300 hover:text-tuber-pink dark:hover:text-tuber-pink hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg focus:outline-none transition-all"
          aria-label={darkMode ? 'Light mode' : 'Dark mode'}
        >
          {darkMode ? <SunIcon className="h-5 w-5" /> : <MoonIcon className="h-5 w-5" />}
        </button>

        {/* Bildirim Butonu */}
        <div className="relative" ref={notificationsRef}>
          <button
            onClick={() => setNotificationsOpen(!notificationsOpen)}
            className="relative p-2 text-gray-300 hover:text-tuber-pink hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg focus:outline-none transition-all"
            aria-label="Notifications"
          >
            {unreadCount > 0 ? (
              <BellAlertIcon className="h-5 w-5" />
            ) : (
              <BellIcon className="h-5 w-5" />
            )}

            {unreadCount > 0 && (
              <span className="absolute top-0 right-0 h-4 w-4 rounded-full bg-red-500 flex items-center justify-center text-xs text-white font-medium">
                {unreadCount}
              </span>
            )}
          </button>

          {/* Bildirimler Dropdown */}
          <AnimatePresence>
            {notificationsOpen && (
              <motion.div
                initial={{ opacity: 0, y: 10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: 10, scale: 0.95 }}
                transition={{ type: "spring", duration: 0.2, stiffness: 500, damping: 30 }}
                className="absolute right-0 mt-2 w-80 rounded-xl bg-white dark:bg-gray-800 shadow-lg ring-1 ring-black/5 dark:ring-white/10 overflow-hidden z-50"
              >
                <div className="p-3 border-b border-gray-100 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 flex items-center justify-between">
                  <h3 className="font-medium text-sm text-gray-900 dark:text-white">Bildirimler</h3>
                  {unreadCount > 0 && (
                    <button onClick={markAllAsRead} className="text-xs text-tuber-pink dark:text-pink-400 hover:underline font-medium">Tümünü Okundu Olarak İşaretle</button>
                  )}
                </div>

                <div className="max-h-[320px] overflow-y-auto">
                  {loading ? (
                    <div className="p-6 text-center">
                      <p className="text-sm text-gray-500 dark:text-gray-400">Bildirimler yükleniyor...</p>
                    </div>
                  ) : error ? (
                    <div className="p-6 text-center">
                      <p className="text-sm text-red-500 dark:text-red-400">{error}</p>
                    </div>
                  ) : notifications.length > 0 ? (
                    <div className="divide-y divide-gray-100 dark:divide-gray-700">
                      {notifications.map(notification => (
                        <div
                          key={notification.id}
                          className={`p-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${!notification.read ? 'bg-pink-50/50 dark:bg-pink-900/10' : ''}`}
                          onClick={() => {
                            markAsRead(notification.id);
                            if (notification.link) {
                              window.location.href = notification.link;
                            }
                          }}
                          style={{ cursor: 'pointer' }}
                        >
                          <div className="flex items-start">
                            <div className={`mt-0.5 w-2 h-2 rounded-full flex-shrink-0 ${!notification.read ? 'bg-tuber-pink' : 'bg-gray-300 dark:bg-gray-600'}`}></div>
                            <div className="ml-3 flex-1">
                              <div className="text-sm font-medium text-gray-900 dark:text-white">{notification.title}</div>
                              <p className="text-xs text-gray-600 dark:text-gray-300 mt-0.5">{notification.message}</p>
                              <div className="flex items-center mt-1">
                                <span className="text-xs text-gray-500 dark:text-gray-400">{notification.time}</span>
                                {notification.type && (
                                  <span className={`ml-2 text-xs px-1.5 py-0.5 rounded-full ${
                                    notification.type === 'info' ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300' :
                                    notification.type === 'success' ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300' :
                                    notification.type === 'warning' ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300' :
                                    notification.type === 'error' ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300' :
                                    'bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-300'
                                  }`}>
                                    {notification.source === 'new_event' ? 'Etkinlik' :
                                     notification.source === 'new_course' ? 'Eğitim' :
                                     notification.source === 'ticket_reply' ? 'Destek' :
                                     notification.type === 'info' ? 'Bilgi' :
                                     notification.type === 'success' ? 'Başarılı' :
                                     notification.type === 'warning' ? 'Uyarı' :
                                     notification.type === 'error' ? 'Hata' :
                                     notification.type}
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="p-6 text-center">
                      <div className="w-12 h-12 rounded-full bg-gray-100 dark:bg-gray-700 mx-auto flex items-center justify-center mb-3">
                        <BellIcon className="h-6 w-6 text-gray-400 dark:text-gray-500" />
                      </div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Henüz bildiriminiz yok</p>
                    </div>
                  )}
                </div>

                {notifications.length > 0 && (
                  <div className="p-2 border-t border-gray-100 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 text-center">
                    <button className="text-xs font-medium text-tuber-pink dark:text-pink-400 hover:underline" onClick={() => window.location.href='/dashboard/notifications'}>
                      Tüm bildirimleri görüntüle
                    </button>
                  </div>
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Profil Menüsü */}
        <div className="relative" ref={profileMenuRef}>
          <button
            onClick={() => setProfileMenuOpen(!profileMenuOpen)}
            className="flex items-center focus:outline-none"
            aria-label="Open user menu"
          >
            <div className="relative">
              {userLoading ? (
                <div className="w-7 h-7 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
              ) : (tiktokUser?.avatar_url || userInfo?.avatar_url) ? (
                <img
                  src={(tiktokUser?.avatar_url || userInfo?.avatar_url) || ''}
                  alt={tiktokUser?.display_name || userInfo?.name}
                  className="w-7 h-7 rounded-full object-cover shadow-md transition-all duration-200 hover:shadow-lg border-2 border-gray-300 dark:border-gray-600"
                  onError={(e) => {
                    // Avatar yüklenemezse, tutarlı profil resmini kullan
                    (e.target as HTMLImageElement).src = 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face&auto=format&q=80';
                  }}
                />
              ) : (
                <div
                  className="w-7 h-7 rounded-full bg-gradient-to-r from-tuber-pink to-tuber-purple flex items-center justify-center text-white font-medium shadow-md transition-all duration-200 hover:shadow-lg"
                >
                  {(() => {
                    const displayName = username || userInfo?.username || tiktokUser?.username || userInfo?.name || tiktokUser?.display_name || 'U';
                    return displayName.substring(0, 1).toUpperCase();
                  })()}
                </div>
              )}
            </div>
          </button>

          {/* Açılır Profil Menüsü */}
          <AnimatePresence>
            {profileMenuOpen && (
              <motion.div
                initial={{ opacity: 0, y: 10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: 10, scale: 0.95 }}
                transition={{ type: "spring", duration: 0.2, stiffness: 500, damping: 30 }}
                className="absolute right-0 mt-2 w-60 rounded-xl bg-white dark:bg-gray-800 shadow-lg ring-1 ring-black/5 dark:ring-white/10 overflow-hidden z-50"
              >
                <div className="py-1">
                  <Link
                    to="/dashboard/profile"
                    className="flex items-center px-4 py-2.5 text-sm text-gray-700 dark:text-gray-200 hover:bg-pink-50 dark:hover:bg-gray-700 hover:text-tuber-pink dark:hover:text-tuber-pink transition-colors"
                  >
                    <UserCircleIcon className="mr-3 h-5 w-5 text-gray-500 dark:text-gray-400" />
                    Profilim
                  </Link>
                  <hr className="my-1 border-gray-100 dark:border-gray-700" />
                  <button
                    onClick={async () => {
                      await logout();
                      onLogout();
                    }}
                    className="flex w-full items-center px-4 py-2.5 text-sm text-gray-700 dark:text-gray-200 hover:bg-pink-50 dark:hover:bg-gray-700 hover:text-red-600 dark:hover:text-red-400 transition-colors text-left"
                  >
                    <ArrowRightOnRectangleIcon className="mr-3 h-5 w-5 text-gray-500 dark:text-gray-400" />
                    Çıkış Yap
                  </button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </header>
  );
};

export default Topbar;