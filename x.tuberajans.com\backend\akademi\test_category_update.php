<?php
/**
 * <PERSON><PERSON><PERSON> güncelleme test sayfası
 */

// CORS için header ayarları
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, DELETE, PUT, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json; charset=UTF-8");

// Config dosyasını dahil et
require_once __DIR__ . '/../config/config.php';

try {
    $db = new PDO("mysql:host=$servername;dbname=tuberaja_yayinci_akademi;charset=utf8mb4", $db_username, $db_password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo json_encode(['error' => 'Veritabanı bağlantı hatası: ' . $e->getMessage()]);
    exit;
}

// Mevcut etkinlikleri listele
echo "<h2>Mevcut Etkinlikler:</h2>";
$stmt = $db->prepare("SELECT id, title, category FROM events ORDER BY id");
$stmt->execute();
$events = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>ID</th><th>Başlık</th><th>Kategori</th></tr>";
foreach ($events as $event) {
    echo "<tr>";
    echo "<td>" . htmlspecialchars($event['id']) . "</td>";
    echo "<td>" . htmlspecialchars($event['title']) . "</td>";
    echo "<td>" . htmlspecialchars($event['category']) . "</td>";
    echo "</tr>";
}
echo "</table>";

// Kategori listesi
echo "<h2>Mevcut Kategoriler:</h2>";
$stmt = $db->prepare("
    SELECT 
        category,
        COUNT(*) as event_count
    FROM events 
    WHERE category IS NOT NULL AND category != '' 
    GROUP BY category 
    ORDER BY event_count DESC
");
$stmt->execute();
$categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<table border='1' style='border-collapse: collapse; width: 50%;'>";
echo "<tr><th>Kategori</th><th>Etkinlik Sayısı</th></tr>";
foreach ($categories as $cat) {
    echo "<tr>";
    echo "<td>" . htmlspecialchars($cat['category']) . "</td>";
    echo "<td>" . htmlspecialchars($cat['event_count']) . "</td>";
    echo "</tr>";
}
echo "</table>";

// Test formu
echo "<h2>Kategori Güncelleme Testi:</h2>";
echo "<form method='POST'>";
echo "<label>Eski Kategori: <input type='text' name='old_category' placeholder='Örn: webinar' required></label><br><br>";
echo "<label>Yeni Kategori: <input type='text' name='new_category' placeholder='Örn: Webinar' required></label><br><br>";
echo "<input type='submit' value='Kategoriyi Güncelle'>";
echo "</form>";

// Form işleme
if ($_POST) {
    $old_category = $_POST['old_category'] ?? '';
    $new_category = $_POST['new_category'] ?? '';
    
    if (!empty($old_category) && !empty($new_category)) {
        echo "<h3>Güncelleme İşlemi:</h3>";
        
        // Önce kaç etkinlik etkileneceğini kontrol et
        $check_stmt = $db->prepare("SELECT COUNT(*) FROM events WHERE category = ?");
        $check_stmt->execute([$old_category]);
        $count_before = $check_stmt->fetchColumn();
        echo "<p>Güncellenecek etkinlik sayısı: " . $count_before . "</p>";
        
        if ($count_before > 0) {
            // Güncelleme yap
            $stmt = $db->prepare("UPDATE events SET category = ?, updated_at = NOW() WHERE category = ?");
            $stmt->execute([$new_category, $old_category]);
            
            $affected_rows = $stmt->rowCount();
            echo "<p style='color: green;'>Başarılı! " . $affected_rows . " etkinlik güncellendi.</p>";
            
            // Güncelleme sonrası kontrol
            $check_stmt2 = $db->prepare("SELECT COUNT(*) FROM events WHERE category = ?");
            $check_stmt2->execute([$new_category]);
            $count_after = $check_stmt2->fetchColumn();
            echo "<p>Güncelleme sonrası yeni kategorideki etkinlik sayısı: " . $count_after . "</p>";
            
            echo "<p><a href=''>Sayfayı Yenile</a></p>";
        } else {
            echo "<p style='color: red;'>Hata: '" . htmlspecialchars($old_category) . "' kategorisinde etkinlik bulunamadı.</p>";
        }
    }
}
?>
