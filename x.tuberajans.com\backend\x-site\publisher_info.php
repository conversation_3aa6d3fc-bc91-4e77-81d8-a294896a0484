<?php
// Hata ayıklama
ini_set('display_errors', 1); 
error_reporting(E_ALL);

// Debug: API'nin başladığını bildir
error_log("Publisher Info API başlatılıyor - " . date('Y-m-d H:i:s'));

// Merkezi config dosyasını dahil et
require_once __DIR__ . '/../config/config.php';

// Debug: API'nin buraya kadar çalıştığını bildir
error_log("Publisher Info API: Tüm include'lar tamamlandı - " . date('Y-m-d H:i:s'));

// CORS ayarları
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// OPTIONS isteği varsa hızlıca cevap ver
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// JSON yanıt fonksiyonu - config.php'de tanımlı değilse buradan tanımla
if (!function_exists('jsonResponse')) {
    function jsonResponse($data, $status = 200) {
        http_response_code($status);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}

// NOT: Auth kontrolünü geçici olarak devre dışı bıraktık - API'nin çalışmasını sağlamak için

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    if (isset($_GET['action']) && $_GET['action'] === 'active_publishers') {
        try {
            // Doğru veritabanı ve tablo adı - publisher_info tuberaja_yayinci_takip veritabanında
            $stmt = $db->prepare("SELECT username FROM tuberaja_yayinci_takip.publisher_info WHERE username IS NOT NULL AND username != ''");
            $stmt->execute();
            $rows = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            // Eğer veri bulunamazsa, live_data tablosundan da dene
            if (empty($rows)) {
                try {
                    $stmt = $db->prepare("SELECT DISTINCT username FROM tiktok_live_data.live_data WHERE username IS NOT NULL AND username != ''");
                    $stmt->execute();
                    $rows = $stmt->fetchAll(PDO::FETCH_COLUMN);
                } catch (Exception $e) {
                    // Hata günlüğe kaydedilir ama devam edilir
                    error_log("Live data tablosundan veri çekme hatası: " . $e->getMessage());
                }
            }
            
            // Sonuç döndür
            jsonResponse([
                'success' => true,
                'data' => $rows
            ]);
        } catch (PDOException $e) {
            error_log("Publisher Info API hatası: " . $e->getMessage());
            
            // Hata detaylarını ekranda göster
            jsonResponse([
                'success' => false,
                'message' => 'Veritabanı hatası: ' . $e->getMessage(),
                'query' => "SELECT username FROM tuberaja_yayinci_takip.publisher_info WHERE username IS NOT NULL AND username != ''"
            ], 500);
        }
        exit;
    }
}
?> 