<?php
/**
 * <PERSON><PERSON><PERSON>im Tetikleyicileri
 *
 * <PERSON><PERSON> dosya, akademi.tuberajans.com sitesinde çeşitli olaylar gerçekleştiğinde
 * bildirim oluşturmak için kullanılan fonksiyonları içerir.
 */

// Doğrudan erişimi engelle
if (count(get_included_files()) == 1) {
    // Bu dosya doğrudan çalıştırılıyor, JSON yanıtı döndür
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'status' => 'error',
        'message' => 'This file should not be accessed directly',
        'time' => date('Y-m-d H:i:s')
    ]);
    exit;
}

// Veritabanı bağlantısını al
require_once __DIR__ . '/../config/config.php';

/**
 * Yeni duyuru eklendiğinde bildirim oluştur
 *
 * @param int $announcement_id Duyuru ID
 * @param string $title Duyuru başlığı
 * @param string $content Duyuru içeriği
 * @param string $type Duyuru tipi
 * @param int $created_by Duyuruyu oluşturan kullanıcı ID
 * @return bool İşlem başarılı mı?
 */
function createAnnouncementNotification($announcement_id, $title, $content, $type, $created_by) {
    global $db;

    try {
        // Tüm aktif kullanıcıları al
        $userStmt = $db->prepare("SELECT id FROM users WHERE status = 'active'");
        $userStmt->execute();
        $users = $userStmt->fetchAll(PDO::FETCH_ASSOC);

        // Her kullanıcı için bildirim oluştur
        foreach ($users as $user) {
            // Duyuruyu oluşturan kullanıcıya bildirim gönderme
            if ($user['id'] == $created_by) {
                continue;
            }

            $message = mb_substr(strip_tags($content), 0, 150) . '...';
            $link = "/dashboard/announcements/" . $announcement_id;

            $sql = "INSERT INTO notifications (
                user_id, title, message, type, `read`, created_at, source, source_id, link
            ) VALUES (
                :user_id, :title, :message, 'info', 0, NOW(), :source, :source_id, :link
            )";

            $stmt = $db->prepare($sql);
            $stmt->bindParam(':user_id', $user['id'], PDO::PARAM_INT);
            $stmt->bindParam(':title', $title, PDO::PARAM_STR);
            $stmt->bindParam(':message', $message, PDO::PARAM_STR);
            $stmt->bindParam(':source', $type, PDO::PARAM_STR);
            $stmt->bindParam(':source_id', $announcement_id, PDO::PARAM_STR);
            $stmt->bindParam(':link', $link, PDO::PARAM_STR);
            $stmt->execute();
        }

        return true;
    } catch (PDOException $e) {
        error_log('Duyuru bildirimi oluşturulurken hata: ' . $e->getMessage());
        return false;
    }
}

/**
 * Destek talebi yanıtlandığında bildirim oluştur
 *
 * @param int $ticket_id Destek talebi ID
 * @param int $user_id Destek talebini oluşturan kullanıcı ID
 * @param string $subject Destek talebi konusu
 * @param int $replied_by Yanıtlayan kullanıcı ID
 * @return bool İşlem başarılı mı?
 */
function createSupportTicketReplyNotification($ticket_id, $user_id, $subject, $replied_by) {
    global $db;

    try {
        // Yanıtlayan kullanıcı bilgilerini al
        $userStmt = $db->prepare("SELECT name FROM users WHERE id = :id");
        $userStmt->bindParam(':id', $replied_by, PDO::PARAM_INT);
        $userStmt->execute();
        $user = $userStmt->fetch(PDO::FETCH_ASSOC);

        $replier_name = $user ? $user['name'] : 'Bir yönetici';
        $title = 'Destek Talebinize Yanıt Verildi';
        $message = "\"$subject\" konulu destek talebinize $replier_name tarafından yanıt verildi.";
        $link = "/dashboard/support/" . $ticket_id;

        $sql = "INSERT INTO notifications (
            user_id, title, message, type, `read`, created_at, source, source_id, link
        ) VALUES (
            :user_id, :title, :message, 'success', 0, NOW(), 'ticket_reply', :source_id, :link
        )";

        $stmt = $db->prepare($sql);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->bindParam(':title', $title, PDO::PARAM_STR);
        $stmt->bindParam(':message', $message, PDO::PARAM_STR);
        $stmt->bindParam(':source_id', $ticket_id, PDO::PARAM_STR);
        $stmt->bindParam(':link', $link, PDO::PARAM_STR);
        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        error_log('Destek talebi bildirimi oluşturulurken hata: ' . $e->getMessage());
        return false;
    }
}

/**
 * Yeni eğitim eklendiğinde bildirim oluştur
 *
 * @param int $course_id Eğitim ID
 * @param string $title Eğitim başlığı
 * @param string $description Eğitim açıklaması
 * @param int $created_by Eğitimi oluşturan kullanıcı ID
 * @return bool İşlem başarılı mı?
 */
function createCourseNotification($course_id, $title, $description, $created_by) {
    global $db;

    try {
        // Tüm aktif kullanıcıları al
        $userStmt = $db->prepare("SELECT id FROM users WHERE status = 'active'");
        $userStmt->execute();
        $users = $userStmt->fetchAll(PDO::FETCH_ASSOC);

        // Her kullanıcı için bildirim oluştur
        foreach ($users as $user) {
            // Eğitimi oluşturan kullanıcıya bildirim gönderme
            if ($user['id'] == $created_by) {
                continue;
            }

            $message = mb_substr(strip_tags($description), 0, 150) . '...';
            $notificationTitle = 'Yeni Eğitim Eklendi';
            $link = "/dashboard/courses/" . $course_id;

            $sql = "INSERT INTO notifications (
                user_id, title, message, type, `read`, created_at, source, source_id, link
            ) VALUES (
                :user_id, :title, :message, 'info', 0, NOW(), 'new_course', :source_id, :link
            )";

            $stmt = $db->prepare($sql);
            $stmt->bindParam(':user_id', $user['id'], PDO::PARAM_INT);
            $stmt->bindParam(':title', $notificationTitle, PDO::PARAM_STR);
            $stmt->bindParam(':message', "\"$title\" başlıklı yeni bir eğitim eklendi. $message", PDO::PARAM_STR);
            $stmt->bindParam(':source_id', $course_id, PDO::PARAM_STR);
            $stmt->bindParam(':link', $link, PDO::PARAM_STR);
            $stmt->execute();
        }

        return true;
    } catch (PDOException $e) {
        error_log('Eğitim bildirimi oluşturulurken hata: ' . $e->getMessage());
        return false;
    }
}

/**
 * Yeni etkinlik eklendiğinde bildirim oluştur
 *
 * @param int $event_id Etkinlik ID
 * @param string $title Etkinlik başlığı
 * @param string $description Etkinlik açıklaması
 * @param string $start_date Etkinlik başlangıç tarihi
 * @param int $created_by Etkinliği oluşturan kullanıcı ID
 * @return bool İşlem başarılı mı?
 */
function createEventNotification($event_id, $title, $description, $start_date, $created_by) {
    global $db;

    try {
        // Tüm aktif kullanıcıları al
        $userStmt = $db->prepare("SELECT id FROM users WHERE status = 'active'");
        $userStmt->execute();
        $users = $userStmt->fetchAll(PDO::FETCH_ASSOC);

        // Etkinlik tarihini formatla
        $event_date = date('d.m.Y H:i', strtotime($start_date));

        // Her kullanıcı için bildirim oluştur
        foreach ($users as $user) {
            // Etkinliği oluşturan kullanıcıya bildirim gönderme
            if ($user['id'] == $created_by) {
                continue;
            }

            $message = mb_substr(strip_tags($description), 0, 150) . '...';
            $notificationTitle = 'Yeni Etkinlik Eklendi';
            $link = "/dashboard/events/" . $event_id;

            $sql = "INSERT INTO notifications (
                user_id, title, message, type, `read`, created_at, source, source_id, link
            ) VALUES (
                :user_id, :title, :message, 'info', 0, NOW(), 'new_event', :source_id, :link
            )";

            $stmt = $db->prepare($sql);
            $stmt->bindParam(':user_id', $user['id'], PDO::PARAM_INT);
            $stmt->bindParam(':title', $notificationTitle, PDO::PARAM_STR);
            $stmt->bindParam(':message', "\"$title\" başlıklı yeni bir etkinlik eklendi. Tarih: $event_date. $message", PDO::PARAM_STR);
            $stmt->bindParam(':source_id', $event_id, PDO::PARAM_STR);
            $stmt->bindParam(':link', $link, PDO::PARAM_STR);
            $stmt->execute();
        }

        return true;
    } catch (PDOException $e) {
        error_log('Etkinlik bildirimi oluşturulurken hata: ' . $e->getMessage());
        return false;
    }
}
