import{j as e}from"./reactDnd-uQSTYBkW.js";import{n,T as d,$ as m,q as i,o as a,I as r,p as s,k as l}from"./antd-BfejY-CV.js";import{O as c,P as o}from"./App-7woMCAzq.js";import"./vendor-CnpYymF8.js";import"./index-n-wHKc0W.js";import"./utils-CtuI0RRe.js";import"./charts-6B1FLgFz.js";const{Title:h,Paragraph:A}=d,{TabPane:t}=i,P=()=>e.jsx("div",{className:"p-4",children:e.jsxs(n,{children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx(c,{style:{fontSize:24,marginRight:12}}),e.jsx(h,{level:3,style:{margin:0},children:"ETSY Ayarları"})]}),e.jsx(m,{message:"Sayfayı Geliştirme",description:"Bu sayfa şu anda geliştirme aşamasındadır. ETSY hesap ayarları, API anahtarları ve diğer yapılandırmalar burada yapılacaktır.",type:"info",showIcon:!0,className:"mb-4"}),e.jsxs(i,{defaultActiveKey:"1",children:[e.jsx(t,{tab:"Genel Ayarlar",children:e.jsxs(a,{layout:"vertical",children:[e.jsx(a.Item,{label:"Mağaza Adı",name:"storeName",children:e.jsx(r,{placeholder:"ETSY mağaza adınız",disabled:!0})}),e.jsx(a.Item,{label:"Otomatik Yenileme Sıklığı",name:"refreshInterval",children:e.jsx(r,{placeholder:"Saat cinsinden (ör: 2)",disabled:!0})}),e.jsx(s,{}),e.jsx(a.Item,{children:e.jsx(l,{type:"primary",disabled:!0,children:"Kaydet"})})]})},"1"),e.jsx(t,{tab:"API Ayarları",children:e.jsxs(a,{layout:"vertical",children:[e.jsx(a.Item,{label:"ETSY API Anahtarı",name:"apiKey",children:e.jsx(r.Password,{placeholder:"ETSY API anahtarınız",disabled:!0})}),e.jsx(a.Item,{label:"API Gizli Anahtarı",name:"apiSecret",children:e.jsx(r.Password,{placeholder:"ETSY API gizli anahtarınız",disabled:!0})}),e.jsx(a.Item,{label:"Yetkilendirme URL",name:"authUrl",children:e.jsx(r,{placeholder:"https://www.etsy.com/oauth/...",disabled:!0})}),e.jsx(s,{}),e.jsx(a.Item,{children:e.jsx(l,{type:"primary",icon:e.jsx(o,{}),disabled:!0,children:"API Bağlantısını Test Et"})})]})},"2"),e.jsx(t,{tab:"Midjourney Ayarları",children:e.jsxs(a,{layout:"vertical",children:[e.jsx(a.Item,{label:"Midjourney API Anahtarı",name:"midjourneyKey",children:e.jsx(r.Password,{placeholder:"Midjourney API anahtarınız",disabled:!0})}),e.jsx(a.Item,{label:"Günlük Tasarım Limiti",name:"designLimit",children:e.jsx(r,{placeholder:"8",disabled:!0})}),e.jsx(s,{}),e.jsx(a.Item,{children:e.jsx(l,{type:"primary",disabled:!0,children:"Kaydet"})})]})},"3")]})]})});export{P as default};
