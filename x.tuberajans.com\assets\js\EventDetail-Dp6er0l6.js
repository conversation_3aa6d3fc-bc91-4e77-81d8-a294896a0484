import{j as a}from"./reactDnd-uQSTYBkW.js";import{k as S,r as t,u as Y}from"./vendor-CnpYymF8.js";import{h as m}from"./utils-CtuI0RRe.js";import{b as c,C as B,h as g}from"./App-BJoNc_gH.js";import{P as K}from"./ThemeStyles-D7U6Bfn4.js";import{g as A}from"./event-api-DZWXwSIr.js";import{o,x as d,X as P,j as u,k as x,q as v,n as j,a7 as s,z,B as R,T as H,Y as l,a0 as h,t as C,I as G}from"./antd-BfejY-CV.js";import{R as L}from"./LeftOutlined-BAcoapJd.js";import"./index-gEPTqZB-.js";import"./charts-6B1FLgFz.js";import"./createLucideIcon-DxVmGoQf.js";const{TabPane:f}=v,{Title:b,Text:ea,Paragraph:y}=H,{TextArea:F}=G,sa=()=>{var p;const{eventId:r}=S(),[e,E]=t.useState(null),[M,k]=t.useState(!0),[_,n]=t.useState(!1),[T]=o.useForm(),I=Y();t.useEffect(()=>{r&&N()},[r]);const N=async()=>{k(!0);try{const i=await A(r);E(i)}catch(i){d.error("Etkinlik detayları alınamadı")}finally{k(!1)}},D=async i=>{try{d.success("WhatsApp mesajları başarıyla gönderildi!"),n(!1)}catch(O){d.error("Mesajlar gönderilemedi")}};return M?a.jsxs("div",{className:"text-center py-20",children:[a.jsx(P,{size:"large"}),a.jsx("p",{className:"mt-4",children:"Etkinlik detayları yükleniyor..."})]}):e?a.jsxs("div",{className:"p-6",children:[a.jsxs("div",{className:"flex justify-between items-center mb-6",children:[a.jsx(K,{children:(e==null?void 0:e.etkinlik_adi)||"Etkinlik Detayı"}),a.jsx(x,{icon:a.jsx(L,{}),onClick:()=>I("/event-management"),children:"Geri Dön"})]}),a.jsxs(v,{defaultActiveKey:"1",className:"event-tabs",children:[a.jsxs(f,{tab:a.jsxs("span",{children:[a.jsx(c,{})," Genel Bilgiler"]}),children:[a.jsxs(j,{className:"mb-6",children:[a.jsxs(s,{title:"Etkinlik Bilgileri",bordered:!0,column:{xxl:4,xl:3,lg:3,md:2,sm:1,xs:1},children:[a.jsx(s.Item,{label:"Etkinlik Türü",children:a.jsx(z,{color:e.etkinlik_tipi==="lider_tablosu"?"blue":"orange",children:e.etkinlik_tipi==="lider_tablosu"?"Lider Tablosu":"PK Turnuvası"})}),a.jsx(s.Item,{label:"Başlangıç Tarihi",children:m(e.baslangic_tarihi).format("DD MMMM YYYY, HH:mm")}),a.jsx(s.Item,{label:"Bitiş Tarihi",children:m(e.bitis_tarihi).format("DD MMMM YYYY, HH:mm")}),a.jsx(s.Item,{label:"Durum",children:a.jsx(R,{status:e.durum==="aktif"?"processing":"default",text:e.durum==="aktif"?"Aktif":"Tamamlandı"})}),a.jsx(s.Item,{label:"Katılım Şekli",children:e.kayit_uygunlugu==="otomatik"?"Otomatik Atama":e.kayit_uygunlugu==="kayit"?"Kayıt Olma":"Sadece Davetli"}),e.etkinlik_tipi==="lider_tablosu"&&a.jsx(s.Item,{label:"Puanlama Sistemi",children:e.puan_sistemi==="elmas"?"Elmas Bazlı":e.puan_sistemi==="yayin_suresi"?"Yayın Süresi Bazlı":e.puan_sistemi==="takipci"?"Takipçi Artışı Bazlı":"Karma Sistem"}),a.jsx(s.Item,{label:"Oluşturulma Tarihi",span:2,children:m(e.olusturulma_tarihi).format("DD MMMM YYYY, HH:mm")})]}),e.aciklama&&a.jsxs("div",{className:"mt-6",children:[a.jsx(b,{level:5,children:"Etkinlik Açıklaması"}),a.jsx(y,{children:e.aciklama})]}),e.kurallar&&a.jsxs("div",{className:"mt-6",children:[a.jsx(b,{level:5,children:"Etkinlik Kuralları"}),a.jsx(y,{children:e.kurallar})]})]}),a.jsx(j,{title:"Etkinlik İstatistikleri",className:"mb-6",children:a.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[a.jsx(l,{title:"Katılımcı Sayısı",value:((p=e.participants)==null?void 0:p.length)||0,prefix:a.jsx(c,{})}),a.jsx(l,{title:"Toplam Elmas",value:e.total_diamonds||0,valueStyle:{color:"#3f8600"}}),a.jsx(l,{title:"Tamamlanan Yayın",value:e.total_broadcasts||0,suffix:"saat"}),a.jsx(l,{title:"Ortalama Etkileşim",value:e.avg_engagement||0,prefix:a.jsx(B,{})})]})})]},"1"),a.jsx(f,{tab:a.jsxs("span",{children:[a.jsx(c,{})," Katılımcılar"]}),children:a.jsx(j,{children:e.participants&&e.participants.length>0?a.jsx(h,{itemLayout:"horizontal",dataSource:e.participants,renderItem:i=>a.jsx(h.Item,{children:a.jsx(h.Item.Meta,{title:i.isim_soyisim,description:`@${i.username}`})})}):a.jsx(u,{description:"Katılımcı bulunamadı"})})},"2")]}),a.jsx(C,{title:a.jsxs("div",{className:"flex items-center",children:[a.jsx(g,{className:"text-green-600 mr-2"}),a.jsx("span",{children:"WhatsApp Mesajı Gönder"})]}),open:_,onCancel:()=>n(!1),footer:null,children:a.jsxs(o,{form:T,layout:"vertical",onFinish:D,children:[a.jsx(o.Item,{name:"message",label:"Mesaj İçeriği",rules:[{required:!0,message:"Mesaj içeriği boş olamaz"}],children:a.jsx(F,{rows:12})}),a.jsxs("div",{className:"flex justify-end",children:[a.jsx(x,{onClick:()=>n(!1),className:"mr-2",children:"İptal"}),a.jsx(x,{type:"primary",htmlType:"submit",icon:a.jsx(g,{}),className:"bg-green-600 hover:bg-green-700",children:"Tüm Katılımcılara Gönder"})]})]})})]}):a.jsx(u,{description:"Etkinlik bulunamadı",image:u.PRESENTED_IMAGE_SIMPLE})};export{sa as default};
