<?php
// Yapılandırma dosyasını dahil et
require_once __DIR__ . '/../config/config.php';

// Bildirim tetikleyicilerini dahil et
require_once __DIR__ . '/notification_triggers.php';

// CORS başlıkları
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Origin, Content-Type, Accept, Authorization');

// OPTIONS isteği için erken yanıt
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(204);
    exit;
}

// Veritabanı bağlantısını al
$db = getDB();

// İstek parametrelerini al
$method = $_SERVER['REQUEST_METHOD'];

// POST veya GET isteğinden action parametresini al
if ($method === 'POST') {
    $data = json_decode(file_get_contents('php://input'), true);
    $action = isset($data['action']) ? $data['action'] : '';
} else {
    $action = isset($_GET['action']) ? $_GET['action'] : '';
}

// Action'a göre işlem yap
switch ($action) {
    case 'list':
        listTickets($db);
        break;
    case 'detail':
        getTicketDetail($db);
        break;
    case 'create':
        createTicket($db);
        break;
    case 'reply':
        addReply($db);
        break;
    case 'update':
        updateTicket($db);
        break;
    default:
        http_response_code(400);
        echo json_encode([
            'status' => 'error',
            'message' => 'Geçersiz action parametresi: ' . $action
        ]);
        break;
}

// Destek taleplerini listele
function listTickets($db) {
    $user_id = isset($_GET['user_id']) ? $_GET['user_id'] : null;

    if (!$user_id) {
        http_response_code(400);
        echo json_encode([
            'status' => 'error',
            'message' => 'Kullanıcı ID parametresi gerekli'
        ]);
        return;
    }

    try {
        $query = "SELECT * FROM support_tickets WHERE user_id = :user_id ORDER BY created_at DESC";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        $tickets = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode([
            'status' => 'success',
            'data' => $tickets
        ]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'status' => 'error',
            'message' => 'Destek talepleri alınırken hata oluştu: ' . $e->getMessage()
        ]);
    }
}

// Destek talebi detayını getir
function getTicketDetail($db) {
    $ticket_id = isset($_GET['ticket_id']) ? $_GET['ticket_id'] : null;

    if (!$ticket_id) {
        http_response_code(400);
        echo json_encode([
            'status' => 'error',
            'message' => 'Talep ID parametresi gerekli'
        ]);
        return;
    }

    try {
        // Talep bilgilerini al
        $ticketQuery = "SELECT * FROM support_tickets WHERE id = :ticket_id";
        $ticketStmt = $db->prepare($ticketQuery);
        $ticketStmt->bindParam(':ticket_id', $ticket_id);
        $ticketStmt->execute();
        $ticket = $ticketStmt->fetch(PDO::FETCH_ASSOC);

        if (!$ticket) {
            http_response_code(404);
            echo json_encode([
                'status' => 'error',
                'message' => 'Destek talebi bulunamadı'
            ]);
            return;
        }

        // Yanıtları al
        $repliesQuery = "SELECT r.*, u.username
                        FROM support_replies r
                        LEFT JOIN users u ON r.user_id = u.id
                        WHERE r.ticket_id = :ticket_id
                        ORDER BY r.created_at ASC";
        $repliesStmt = $db->prepare($repliesQuery);
        $repliesStmt->bindParam(':ticket_id', $ticket_id);
        $repliesStmt->execute();
        $replies = $repliesStmt->fetchAll(PDO::FETCH_ASSOC);

        // Talep ve yanıtları birleştir
        $ticket['replies'] = $replies;

        echo json_encode([
            'status' => 'success',
            'data' => $ticket
        ]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'status' => 'error',
            'message' => 'Destek talebi detayı alınırken hata oluştu: ' . $e->getMessage()
        ]);
    }
}

// Yeni destek talebi oluştur
function createTicket($db) {
    $data = json_decode(file_get_contents('php://input'), true);

    $user_id = isset($data['user_id']) ? $data['user_id'] : null;
    $subject = isset($data['subject']) ? $data['subject'] : null;
    $message = isset($data['message']) ? $data['message'] : null;
    $category = isset($data['category']) ? $data['category'] : 'general';
    $priority = isset($data['priority']) ? $data['priority'] : 'medium';

    if (!$user_id || !$subject || !$message) {
        http_response_code(400);
        echo json_encode([
            'status' => 'error',
            'message' => 'Eksik parametreler: user_id, subject ve message gerekli'
        ]);
        return;
    }

    try {
        $query = "INSERT INTO support_tickets (user_id, subject, message, category, status, priority, created_at, updated_at)
                 VALUES (:user_id, :subject, :message, :category, 'open', :priority, NOW(), NOW())";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':subject', $subject);
        $stmt->bindParam(':message', $message);
        $stmt->bindParam(':category', $category);
        $stmt->bindParam(':priority', $priority);
        $stmt->execute();

        $ticket_id = $db->lastInsertId();

        // Sistem yanıtı ekle
        $systemMessage = "Talebiniz alınmıştır. En kısa sürede işleme alınacaktır.";
        $systemQuery = "INSERT INTO support_replies (ticket_id, user_id, message, is_admin, created_at)
                       VALUES (:ticket_id, 1, :message, 1, NOW())";
        $systemStmt = $db->prepare($systemQuery);
        $systemStmt->bindParam(':ticket_id', $ticket_id);
        $systemStmt->bindParam(':message', $systemMessage);
        $systemStmt->execute();

        echo json_encode([
            'status' => 'success',
            'message' => 'Destek talebi başarıyla oluşturuldu',
            'ticket_id' => $ticket_id
        ]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'status' => 'error',
            'message' => 'Destek talebi oluşturulurken hata oluştu: ' . $e->getMessage()
        ]);
    }
}

// Destek talebine yanıt ekle
function addReply($db) {
    $data = json_decode(file_get_contents('php://input'), true);

    $ticket_id = isset($data['ticket_id']) ? $data['ticket_id'] : null;
    $user_id = isset($data['user_id']) ? $data['user_id'] : null;
    $message = isset($data['message']) ? $data['message'] : null;
    $is_admin = isset($data['is_admin']) ? $data['is_admin'] : 0;

    if (!$ticket_id || !$user_id || !$message) {
        http_response_code(400);
        echo json_encode([
            'status' => 'error',
            'message' => 'Eksik parametreler: ticket_id, user_id ve message gerekli'
        ]);
        return;
    }

    try {
        // Talep var mı kontrol et
        $checkQuery = "SELECT t.*, u.username, u.name as user_name
                      FROM support_tickets t
                      LEFT JOIN users u ON t.user_id = u.id
                      WHERE t.id = :ticket_id";
        $checkStmt = $db->prepare($checkQuery);
        $checkStmt->bindParam(':ticket_id', $ticket_id);
        $checkStmt->execute();
        $ticket = $checkStmt->fetch(PDO::FETCH_ASSOC);

        if (!$ticket) {
            http_response_code(404);
            echo json_encode([
                'status' => 'error',
                'message' => 'Destek talebi bulunamadı'
            ]);
            return;
        }

        // Talep kapalıysa hata döndür
        if ($ticket['status'] === 'closed') {
            http_response_code(400);
            echo json_encode([
                'status' => 'error',
                'message' => 'Bu destek talebi kapatılmış, yanıt eklenemez'
            ]);
            return;
        }

        // Yanıt ekle
        $replyQuery = "INSERT INTO support_replies (ticket_id, user_id, message, is_admin, created_at)
                      VALUES (:ticket_id, :user_id, :message, :is_admin, NOW())";
        $replyStmt = $db->prepare($replyQuery);
        $replyStmt->bindParam(':ticket_id', $ticket_id);
        $replyStmt->bindParam(':user_id', $user_id);
        $replyStmt->bindParam(':message', $message);
        $replyStmt->bindParam(':is_admin', $is_admin);
        $replyStmt->execute();

        // Talebi güncelle
        $updateQuery = "UPDATE support_tickets
                       SET updated_at = NOW(),
                           last_reply_at = NOW(),
                           last_reply_by = :user_id
                       WHERE id = :ticket_id";
        $updateStmt = $db->prepare($updateQuery);
        $updateStmt->bindParam(':user_id', $user_id);
        $updateStmt->bindParam(':ticket_id', $ticket_id);
        $updateStmt->execute();

        // Eğer yanıtı veren admin ise ve talep sahibi farklı bir kullanıcı ise bildirim oluştur
        if ($is_admin && $ticket['user_id'] != $user_id) {
            // Yanıtlayan kullanıcı bilgilerini al
            $userQuery = "SELECT name FROM users WHERE id = :user_id";
            $userStmt = $db->prepare($userQuery);
            $userStmt->bindParam(':user_id', $user_id);
            $userStmt->execute();
            $user = $userStmt->fetch(PDO::FETCH_ASSOC);

            // Bildirim oluştur
            createSupportTicketReplyNotification(
                $ticket_id,
                $ticket['user_id'],
                $ticket['subject'],
                $user_id
            );
        }

        echo json_encode([
            'status' => 'success',
            'message' => 'Yanıt başarıyla eklendi'
        ]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'status' => 'error',
            'message' => 'Yanıt eklenirken hata oluştu: ' . $e->getMessage()
        ]);
    }
}

// Destek talebini güncelle
function updateTicket($db) {
    $data = json_decode(file_get_contents('php://input'), true);

    $ticket_id = isset($data['ticket_id']) ? $data['ticket_id'] : null;
    $status = isset($data['status']) ? $data['status'] : null;
    $priority = isset($data['priority']) ? $data['priority'] : null;

    if (!$ticket_id || (!$status && !$priority)) {
        http_response_code(400);
        echo json_encode([
            'status' => 'error',
            'message' => 'Eksik parametreler: ticket_id ve status veya priority gerekli'
        ]);
        return;
    }

    try {
        $updateFields = [];
        $params = [':ticket_id' => $ticket_id];

        if ($status) {
            $updateFields[] = "status = :status";
            $params[':status'] = $status;
        }

        if ($priority) {
            $updateFields[] = "priority = :priority";
            $params[':priority'] = $priority;
        }

        $updateFields[] = "updated_at = NOW()";

        $query = "UPDATE support_tickets SET " . implode(', ', $updateFields) . " WHERE id = :ticket_id";
        $stmt = $db->prepare($query);

        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->execute();

        echo json_encode([
            'status' => 'success',
            'message' => 'Destek talebi başarıyla güncellendi'
        ]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'status' => 'error',
            'message' => 'Destek talebi güncellenirken hata oluştu: ' . $e->getMessage()
        ]);
    }
}