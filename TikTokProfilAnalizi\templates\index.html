<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TikTok Profil Analiz Aracı</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/plotly.js-dist-min@2.20.0/plotly.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <style>
        :root {
            --primary-color: #FE2C55;
            --secondary-color: #25F4EE;
            --dark-bg: #121212;
            --card-bg: #1f1f1f;
            --text-color: #ffffff;
            --text-secondary: rgba(255, 255, 255, 0.7);
            --border-color: #333333;
            --success-color: #73D13D;
            --warning-color: #FFC53D;
            --info-color: #1890FF;
            --danger-color: #FF4D4F;
            --gradient-primary: linear-gradient(45deg, #FE2C55, #FE6C85);
            --gradient-secondary: linear-gradient(45deg, #25F4EE, #55F4FF);
            --gradient-dark: linear-gradient(135deg, #1f1f1f, #2a2a2a);
            --input-bg: #2a2a2a;
        }
        
        [data-theme="light"] {
            --dark-bg: #f5f5f5;
            --card-bg: #ffffff;
            --text-color: #333333;
            --text-secondary: rgba(0, 0, 0, 0.7);
            --border-color: #dddddd;
            --gradient-dark: linear-gradient(135deg, #ffffff, #f5f5f5);
            --input-bg: #f9f9f9;
        }
        
        body {
            background: var(--gradient-dark);
            color: var(--text-color);
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            transition: all 0.3s ease;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .main-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px 0;
        }
        
        .container-fluid {
            padding: 0 40px;
            max-width: 1600px;
            margin: 0 auto;
            width: 100%;
        }
        
        .card {
            background: var(--card-bg);
            border: none;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }
        
        .card-header {
            background: rgba(0, 0, 0, 0.05);
            border-bottom: 1px solid var(--border-color);
            font-weight: 600;
            padding: 18px 25px;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            gap: 10px;
            border-radius: 15px 15px 0 0 !important;
        }
        
        .card-body {
            padding: 25px;
        }
        
        .form-container {
            padding: 30px;
            border-radius: 15px;
            background: var(--card-bg);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            margin-bottom: 40px;
        }
        
        .input-group {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        
        .input-group-text {
            background: var(--input-bg);
            color: var(--primary-color);
            border: none;
            font-weight: bold;
            font-size: 1.2rem;
            padding: 15px 20px;
        }
        
        .form-control {
            background: var(--input-bg);
            border: none;
            color: var(--text-color);
            padding: 15px 20px;
            font-size: 1.1rem;
            height: auto;
        }
        
        .form-control:focus {
            background: var(--input-bg);
            color: var(--text-color);
            box-shadow: 0 0 0 2px var(--primary-color);
        }
        
        .form-text {
            color: var(--text-secondary);
            margin-top: 8px;
            font-size: 0.9rem;
        }
        
        .btn-analyze {
            padding: 15px 30px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1.1rem;
            background: var(--gradient-primary);
            border: none;
            color: white;
            transition: all 0.3s ease;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .btn-analyze:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(254, 44, 85, 0.3);
        }
        
        .profile-card {
            background: var(--gradient-primary);
            padding: 3px;
            border-radius: 20px;
            margin-bottom: 30px;
        }
        
        .profile-card-inner {
            background: var(--card-bg);
            border-radius: 17px;
            padding: 30px;
        }
        
        .avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid var(--secondary-color);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .profile-stats {
            display: flex;
            justify-content: space-around;
            margin-top: 30px;
            text-align: center;
            gap: 20px;
        }
        
        .stat-item {
            background: rgba(0, 0, 0, 0.05);
            padding: 15px;
            border-radius: 15px;
            flex: 1;
            transition: all 0.3s ease;
        }
        
        .stat-item:hover {
            background: rgba(254, 44, 85, 0.1);
        }
        
        .stat-value {
            font-size: 1.8rem;
            font-weight: bold;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .metric-card {
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            height: 100%;
            color: #fff;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
            opacity: 0;
            transition: all 0.3s ease;
        }
        
        .metric-card:hover::before {
            opacity: 1;
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        
        .metric-card.primary {
            background: var(--gradient-primary);
        }
        
        .metric-card.secondary {
            background: var(--gradient-secondary);
        }
        
        .metric-card.success {
            background: linear-gradient(135deg, #73D13D 0%, #52c41a 100%);
        }
        
        .metric-card.warning {
            background: linear-gradient(135deg, #FFC53D 0%, #faad14 100%);
        }
        
        .metric-card.info {
            background: linear-gradient(135deg, #1890FF 0%, #096dd9 100%);
        }
        
        .metric-card.danger {
            background: linear-gradient(135deg, #FF4D4F 0%, #f5222d 100%);
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin: 15px 0;
        }
        
        .metric-label {
            font-size: 0.9rem;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .video-thumbnail-wrapper {
            position: relative;
            width: 100%;
            padding-top: 133.33%;
            overflow: hidden;
            border-radius: 15px;
            margin-bottom: 15px;
            background: var(--gradient-dark);
        }
        
        .video-thumbnail {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .video-thumbnail:hover {
            transform: scale(1.05);
        }
        
        .engagement-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 0.85rem;
            font-weight: bold;
            margin-top: 10px;
            background: rgba(0, 0, 0, 0.1);
        }
        
        .engagement-badge.high {
            background: linear-gradient(135deg, rgba(115, 209, 61, 0.2), rgba(82, 196, 26, 0.2));
            color: var(--success-color);
        }
        
        .engagement-badge.medium {
            background: linear-gradient(135deg, rgba(255, 197, 61, 0.2), rgba(250, 173, 20, 0.2));
            color: var(--warning-color);
        }
        
        .engagement-badge.low {
            background: linear-gradient(135deg, rgba(255, 77, 79, 0.2), rgba(245, 34, 45, 0.2));
            color: var(--danger-color);
        }
        
        .hashtag-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 0.9rem;
            margin: 5px;
            background: linear-gradient(135deg, rgba(24, 144, 255, 0.2), rgba(9, 109, 217, 0.2));
            color: var(--info-color);
            transition: all 0.3s ease;
        }
        
        .hashtag-badge:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 10px rgba(24, 144, 255, 0.2);
        }
        
        .social-share {
            display: flex;
            justify-content: center;
            margin-top: 20px;
            gap: 20px;
        }
        
        .social-share a {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            text-align: center;
            color: white;
            transition: all 0.3s ease;
            font-size: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
            text-decoration: none;
            border: none;
        }
        
        .social-share a:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        }
        
        .social-share .facebook {
            background: linear-gradient(135deg, #3b5998, #4267B2);
        }
        
        .social-share .twitter {
            background: linear-gradient(135deg, #1da1f2, #1a91da);
        }
        
        .social-share .whatsapp {
            background: linear-gradient(135deg, #25d366, #128c7e);
        }
        
        .social-share .telegram {
            background: linear-gradient(135deg, #0088cc, #0077b3);
        }
        
        .social-share-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 25px;
            color: var(--text-color);
            text-align: center;
        }
        
        .theme-switch {
            display: flex;
            align-items: center;
            margin-left: 20px;
        }
        
        .theme-switch input[type="checkbox"] {
            display: none;
        }
        
        .theme-switch label {
            cursor: pointer;
            width: 60px;
            height: 30px;
            background: var(--gradient-dark);
            display: block;
            border-radius: 30px;
            position: relative;
            margin-bottom: 0;
            box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.2);
        }
        
        .theme-switch label:after {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            width: 24px;
            height: 24px;
            background: #fff;
            border-radius: 24px;
            transition: 0.3s;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }
        
        .theme-switch input:checked + label {
            background: var(--gradient-primary);
        }
        
        .theme-switch input:checked + label:after {
            left: calc(100% - 3px);
            transform: translateX(-100%);
        }
        
        .export-buttons {
            margin-top: 25px;
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn-export {
            background: linear-gradient(135deg, var(--success-color), #52c41a);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
        }
        
        .btn-export:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(115, 209, 61, 0.3);
        }
        
        .plot-container {
            height: 600px;
            width: 100%;
            border-radius: 15px;
            overflow: hidden;
            background: var(--card-bg);
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .plot-container.large {
            height: 800px;
        }
        
        .footer {
            margin-top: auto;
            padding: 30px 0;
            text-align: center;
            font-size: 0.9rem;
            color: var(--text-secondary);
            background: rgba(0, 0, 0, 0.05);
            border-top: 1px solid var(--border-color);
        }
        
        .recent-posts-heading {
            font-size: 1.8rem;
            font-weight: 700;
            margin: 40px 0 30px;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .post-metrics {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin-top: 20px;
        }
        
        .post-metric {
            text-align: center;
            padding: 10px;
            background: rgba(0, 0, 0, 0.05);
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .post-metric:hover {
            background: rgba(254, 44, 85, 0.1);
        }
        
        .post-metric-value {
            font-size: 1.3rem;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .post-metric-label {
            font-size: 0.8rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin: 40px 0;
            padding: 30px;
            background: var(--card-bg);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .spinner-border {
            width: 3rem;
            height: 3rem;
            color: var(--primary-color);
        }
        
        #error-message {
            display: none;
            color: var(--danger-color);
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: rgba(254, 44, 85, 0.1);
            border-radius: 15px;
            border: 1px solid rgba(254, 44, 85, 0.2);
        }
        
        .input-group .form-control {
            border-radius: 0 10px 10px 0;
            border-left: none;
        }
        
        @media (max-width: 1200px) {
            .container-fluid {
                padding: 0 20px;
            }
        }
        
        @media (max-width: 768px) {
            .profile-stats {
                flex-direction: column;
            }
            
            .post-metrics {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        /* Yeni stil - Ana sayfa için */
        .welcome-container {
            text-align: center;
            max-width: 900px;
            margin: 0 auto;
            padding: 60px 20px;
        }
        
        .welcome-title {
            font-size: 2.5rem;
            margin-bottom: 20px;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 2px 10px rgba(254, 44, 85, 0.2);
        }
        
        .welcome-subtitle {
            font-size: 1.2rem;
            margin-bottom: 50px;
            color: var(--text-secondary);
            max-width: 700px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .features-container {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
            margin-top: 50px;
        }
        
        .feature-item {
            flex: 0 0 calc(33.33% - 30px);
            max-width: 280px;
            margin-bottom: 30px;
            padding: 30px 25px;
            background: var(--card-bg);
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .feature-icon-container {
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, rgba(254, 44, 85, 0.1), rgba(255, 106, 137, 0.1));
            border-radius: 50%;
            margin-bottom: 20px;
        }
        
        .feature-icon {
            font-size: 2.2rem;
            color: var(--primary-color);
        }
        
        .feature-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .feature-item p {
            color: var(--text-secondary);
            line-height: 1.5;
        }
        
        .feature-item:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }
        
        .form-label {
            font-weight: 500;
            margin-bottom: 10px;
        }
        
        /* Yükleme göstergesi */
        .loading-container {
            display: none;
            text-align: center;
            max-width: 850px;
            margin: 50px auto;
            padding: 60px 30px;
            background: var(--card-bg);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
            z-index: 1;
        }
        
        .loading-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(254, 44, 85, 0.05) 0%, rgba(37, 244, 238, 0.05) 100%);
            z-index: -1;
        }
        
        .loading-container h3 {
            font-size: 1.8rem;
            margin: 20px 0 15px;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .loading-container p {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }
        
        .spinner {
            width: 80px;
            height: 80px;
            border: 5px solid rgba(254, 44, 85, 0.1);
            border-top-color: var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 25px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        @media (max-width: 992px) {
            .features-container {
                gap: 20px;
            }
            
            .feature-item {
                flex: 0 0 calc(50% - 20px);
                max-width: 100%;
            }
        }
        
        @media (max-width: 576px) {
            .feature-item {
                flex: 0 0 100%;
            }
            
            .welcome-title {
                font-size: 2rem;
            }
        }
        
        /* Karanlık tema iyileştirmeleri */
        [data-theme="dark"] .feature-item {
            background: rgba(255, 255, 255, 0.05);
        }
        
        [data-theme="dark"] .feature-title {
            color: #ffffff;
        }
        
        [data-theme="dark"] .feature-item p {
            color: rgba(255, 255, 255, 0.85);
        }
        
        [data-theme="dark"] .welcome-subtitle {
            color: rgba(255, 255, 255, 0.85);
        }
        
        [data-theme="dark"] .form-label {
            color: #ffffff;
        }
        
        /* Responsive düzenlemeler */
        @media (max-width: 767px) {
            .search-button-container {
                margin-left: 0;
                margin-top: 15px;
                align-self: auto;
                margin-bottom: 0;
            }
        }
        
        /* Sonuçları başlangıçta gizle - Eklendi */
        #results-container {
            display: none;
        }
        
        /* Buton düzeltmeleri */
        .form-group {
            margin-bottom: 0;
        }
        
        /* Input ve buton hizalama düzeltmesi */
        .search-form-container {
            display: flex;
            align-items: center;
        }
        
        .search-input-container {
            flex: 1;
        }
        
        .search-button-container {
            margin-left: 15px;
            align-self: flex-end;
            margin-bottom: 10px; /* Form metninin yüksekliğine göre ayarlama */
        }
        
        /* Arama formu düzenlemeleri */
        .search-container {
            max-width: 850px;
            margin: 50px auto;
            padding: 30px;
            background: rgba(31, 31, 31, 0.6);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .search-title {
            text-align: center;
            margin-bottom: 30px;
            font-size: 1.8rem;
            font-weight: 600;
            color: var(--text-color);
        }
        
        .search-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .form-row {
            display: flex;
            align-items: flex-end;
            gap: 15px;
        }
        
        .input-container {
            flex: 1;
        }
        
        .button-container {
            width: 180px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 10px;
            font-weight: 500;
            color: var(--text-color);
        }
        
        .search-input-group {
            display: flex;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .input-prefix {
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--primary-color);
            color: white;
            padding: 0 20px;
            font-weight: bold;
            font-size: 1.2rem;
        }
        
        .search-input {
            flex: 1;
            border: none;
            padding: 15px 20px;
            font-size: 1.1rem;
            background: var(--input-bg);
            color: var(--text-color);
        }
        
        .search-input:focus {
            outline: none;
            box-shadow: 0 0 0 2px var(--primary-color);
        }
        
        .search-button {
            height: 56px;
            background: var(--gradient-primary);
            border: none;
            border-radius: 15px;
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 0 30px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(254, 44, 85, 0.3);
            width: 100%;
        }
        
        .search-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(254, 44, 85, 0.4);
        }
        
        .search-helper {
            text-align: center;
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-top: 10px;
        }
        
        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .button-container {
                width: 100%;
                margin-top: 10px;
            }
            
            .search-container {
                margin: 30px 15px;
                padding: 20px;
            }
        }
        
        /* TikTok benzeri arama formu stilleri */
        .tiktok-search-container {
            max-width: 650px;
            margin: 10px auto 60px;
        }
        
        .tiktok-search-title {
            font-size: 2.2rem;
            margin-bottom: 40px;
            text-align: center;
            font-weight: 700;
            background: linear-gradient(to right, var(--primary-color), #ff6a88);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 2px 10px rgba(254, 44, 85, 0.2);
        }
        
        .tiktok-search-box {
            display: flex;
            background: rgba(22, 24, 35, 0.12);
            border-radius: 92px;
            overflow: hidden;
            border: none;
            height: 52px;
        }
        
        [data-theme="dark"] .tiktok-search-box {
            background: rgba(255, 255, 255, 0.12);
        }
        
        [data-theme="light"] .tiktok-search-box {
            background: rgba(22, 24, 35, 0.06);
            border: 1px solid rgba(22, 24, 35, 0.12);
        }
        
        .tiktok-input-wrapper {
            display: flex;
            align-items: center;
            flex: 1;
            height: 100%;
            padding-left: 16px;
        }
        
        .tiktok-input-prefix {
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            font-size: 1.2rem;
            font-weight: bold;
            margin-right: 8px;
        }
        
        .tiktok-input {
            flex: 1;
            height: 100%;
            border: none;
            outline: none;
            background: transparent;
            color: var(--text-color);
            font-size: 1rem;
            padding: 0 16px 0 0;
        }
        
        .tiktok-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
        
        [data-theme="light"] .tiktok-input::placeholder {
            color: rgba(22, 24, 35, 0.34);
        }
        
        .tiktok-search-button {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            background-color: var(--primary-color);
            color: white;
            font-weight: 600;
            font-size: 0.95rem;
            padding: 0 25px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            gap: 8px;
        }
        
        .tiktok-search-button:hover {
            background-color: #d42548;
        }
        
        .tiktok-search-helper {
            margin-top: 12px;
            text-align: center;
            color: var(--text-secondary);
            font-size: 0.85rem;
        }
        
        .tiktok-search-icon {
            font-size: 1rem;
        }
        
        /* Özellik kartları geliştirme */
        .feature-cards {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 25px;
            margin-top: 60px;
            margin-bottom: 40px;
        }
        
        .feature-card {
            flex: 0 0 280px;
            background: rgba(255, 255, 255, 0.04);
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: all 0.3s ease;
            padding: 30px 25px;
        }
        
        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        
        .feature-icon-wrapper {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(45deg, var(--primary-color), #ff6a88);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 25px;
        }
        
        .feature-icon {
            color: white;
            font-size: 32px;
        }
        
        .feature-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: white;
        }
        
        .feature-description {
            color: var(--text-secondary);
            line-height: 1.5;
            font-size: 0.95rem;
        }
        
        [data-theme="light"] .feature-card {
            background: white;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
        }
        
        [data-theme="light"] .feature-title {
            color: #333;
        }
        
        @media (max-width: 768px) {
            .tiktok-search-container {
                padding: 0 20px;
            }
            
            .feature-cards {
                padding: 0 20px;
                gap: 15px;
            }
            
            .feature-card {
                flex: 0 0 100%;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="container-fluid">
            <!-- Yenilenmiş TikTok benzeri arama formu -->
            <div class="tiktok-search-container">
                <h1 class="tiktok-search-title">TikTok Profil Analizi</h1>
                
                            <form id="analyze-form">
                    <div class="tiktok-search-box">
                        <div class="tiktok-input-wrapper">
                            <div class="tiktok-input-prefix">@</div>
                            <input type="text" class="tiktok-input" id="username" name="username" placeholder="kullaniciadi" required>
                                    </div>
                        <button type="submit" class="tiktok-search-button">
                            <i class="fas fa-chart-line tiktok-search-icon"></i> Analiz Et
                        </button>
                                </div>
                    <div class="tiktok-search-helper">
                        Analiz etmek istediğiniz TikTok kullanıcı adını girin
                    </div>
                            </form>
                        </div>
            
            <!-- Özellik kartları -->
            <div id="feature-cards" class="feature-cards">
                <div class="feature-card">
                    <div class="feature-icon-wrapper">
                        <i class="fas fa-chart-bar feature-icon"></i>
                    </div>
                    <h3 class="feature-title">Kapsamlı Analiz</h3>
                    <p class="feature-description">Profil etkileşim oranları, popüler içerikler ve performans istatistikleri</p>
                    </div>
                    
                <div class="feature-card">
                    <div class="feature-icon-wrapper">
                        <i class="fas fa-chart-line feature-icon"></i>
                        </div>
                    <h3 class="feature-title">Trend Analizi</h3>
                    <p class="feature-description">Zaman içindeki performans değişimlerini takip edin</p>
                    </div>
                    
                <div class="feature-card">
                    <div class="feature-icon-wrapper">
                        <i class="fas fa-download feature-icon"></i>
                    </div>
                    <h3 class="feature-title">Verileri Dışa Aktarın</h3>
                    <p class="feature-description">Analiz sonuçlarını PDF veya Excel olarak kaydedin</p>
                </div>
            </div>
            
            <!-- Yükleniyor göstergesi -->
            <div id="loading-container" class="loading-container">
                <div class="spinner"></div>
                <h3>Profil Analiz Ediliyor...</h3>
                <p>Bu işlem birkaç dakika sürebilir. Lütfen bekleyin.</p>
            </div>
            
            <!-- Analiz Sonuçları Bölümü - Başlangıçta gizli -->
            <div id="results-container">
                <div class="row">
                    <div class="col-12">
                        <div class="profile-card">
                            <div class="profile-card-inner">
                                <div class="row align-items-center">
                                    <div class="col-md-3 text-center">
                                        <img id="profile-avatar" src="" alt="Profil Fotoğrafı" class="avatar">
                                    </div>
                                    <div class="col-md-9">
                                        <h3 id="profile-nickname" class="mb-1"></h3>
                                        <p id="profile-username" class="text-muted mb-3"></p>
                                        <p id="profile-bio" class="mb-0"></p>
                                        <div class="profile-stats">
                                            <div class="stat-item">
                                                <div id="profile-followers" class="stat-value"></div>
                                                <div class="stat-label">Takipçi</div>
                                            </div>
                                            <div class="stat-item">
                                                <div id="profile-following" class="stat-value"></div>
                                                <div class="stat-label">Takip</div>
                                            </div>
                                            <div class="stat-item">
                                                <div id="profile-likes" class="stat-value"></div>
                                                <div class="stat-label">Beğeni</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Toplam Performans Metrikleri -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-chart-line me-2"></i> Toplam Performans Metrikleri
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <div class="metric-card primary">
                                            <div class="metric-label">Toplam İzlenme</div>
                                            <div id="total-views" class="metric-value">0</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <div class="metric-card secondary">
                                            <div class="metric-label">Toplam Etkileşim</div>
                                            <div id="total-engagement" class="metric-value">0</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <div class="metric-card info">
                                            <div class="metric-label">Toplam Etkileşim Oranı</div>
                                            <div id="total-engagement-rate" class="metric-value">0%</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3 mb-3">
                                        <div class="metric-card warning">
                                            <div class="metric-label">Ortalama İzlenme</div>
                                            <div id="average-views" class="metric-value">0</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="metric-card danger">
                                            <div class="metric-label">Ortalama Beğeni</div>
                                            <div id="average-likes" class="metric-value">0</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="metric-card success">
                                            <div class="metric-label">Ortalama Yorum</div>
                                            <div id="average-comments" class="metric-value">0</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="metric-card secondary">
                                            <div class="metric-label">Ortalama Paylaşım</div>
                                            <div id="average-shares" class="metric-value">0</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="export-buttons">
                                    <button class="btn-export" onclick="exportToCSV()">
                                        <i class="fas fa-file-csv"></i> CSV İndir
                                    </button>
                                    <button class="btn-export" onclick="exportToExcel()">
                                        <i class="fas fa-file-excel"></i> Excel İndir
                                    </button>
                                    <button class="btn-export" onclick="exportToPDF()">
                                        <i class="fas fa-file-pdf"></i> PDF İndir
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Video Görüntülenme Grafiği -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-chart-bar me-2"></i> Video Görüntülenmeleri
                            </div>
                            <div class="card-body">
                                <div id="views-timeline" class="plot-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- En Popüler Videolar -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-crown me-2"></i> En Popüler Videolar
                            </div>
                            <div class="card-body">
                                <div id="popular-videos-engagement" class="plot-container large"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Hashtag Analizi -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-hashtag me-2"></i> Hashtag Analizi
                            </div>
                            <div class="card-body">
                                <div id="hashtag-list" class="mt-3">
                                    <!-- Burada en çok kullanılan hashtag'ler listelenecek -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Son Videoları -->
                <h2 class="recent-posts-heading">
                    <i class="fas fa-video"></i> Son Videolar
                </h2>
                
                <div id="recent-videos" class="row">
                    <!-- Burada son videolar listelenecek -->
                </div>
                
                <!-- Paylaş -->
                <div class="row">
                    <div class="col-12">
                        <div class="card mt-4">
                            <div class="card-body py-4">
                                <h4 class="social-share-title">Bu Analizi Paylaş</h4>
                                <div class="social-share">
                                    <a href="#" class="facebook" onclick="shareOnFacebook(); return false;" title="Facebook'ta Paylaş">
                                        <i class="fab fa-facebook-f"></i>
                                    </a>
                                    <a href="#" class="twitter" onclick="shareOnTwitter(); return false;" title="Twitter'da Paylaş">
                                        <i class="fab fa-twitter"></i>
                                    </a>
                                    <a href="#" class="whatsapp" onclick="shareOnWhatsApp(); return false;" title="WhatsApp'ta Paylaş">
                                        <i class="fab fa-whatsapp"></i>
                                    </a>
                                    <a href="#" class="telegram" onclick="shareOnTelegram(); return false;" title="Telegram'da Paylaş">
                                        <i class="fab fa-telegram-plane"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p class="mb-2">TikTok Profil Analiz Aracı &copy; 2025</p>
        <p class="mb-0">Not: Bu uygulama eğitim amaçlıdır ve TikTok'un resmi bir ürünü değildir.</p>
    </div>
    
    <script>
        $(document).ready(function() {
            // Tema değiştirme
            $("#theme-toggle").click(function() {
                const newTheme = $("body").attr("data-theme") === "light" ? "dark" : "light";
                $("body").attr("data-theme", newTheme);
                $(this).html(newTheme === "light" ? '<i class="fas fa-moon"></i>' : '<i class="fas fa-sun"></i>');
                localStorage.setItem("theme", newTheme);
            });
            
            // Sayfa yüklendiğinde tema kontrolü
            const savedTheme = localStorage.getItem("theme") || "dark";
            $("body").attr("data-theme", savedTheme);
            $("#theme-toggle").html(savedTheme === "light" ? '<i class="fas fa-moon"></i>' : '<i class="fas fa-sun"></i>');
            
            // Form gönderimi
            $("#analyze-form").submit(function(e) {
                e.preventDefault();
                
                // Yükleniyor göstergesi
                $("#results-container").hide();
                $("#feature-cards").hide();
                $("#loading-container").show();
                
                // Kullanıcı adını al
                var username = $("#username").val().trim();
                
                // API'ye istek gönder
                $.ajax({
                    url: "/analyze",
                    type: "POST",
                    data: { username: username },
                    success: function(response) {
                        // Yükleme göstergesini gizle
                        $("#loading-container").hide();
                        
                        if (response.error) {
                            // Hata durumunda
                            alert("Hata: " + response.error);
                            $("#feature-cards").show(); // Hata durumunda özellik kartlarını tekrar göster
                        } else {
                            // Başarılı durumda sonuçları göster
                            $("#results-container").show();
                            renderResults(response);
                        }
                    },
                    error: function() {
                        $("#loading-container").hide();
                        $("#feature-cards").show(); // Hata durumunda özellik kartlarını tekrar göster
                        alert("Bir hata oluştu. Lütfen tekrar deneyin.");
                    }
                });
            });
            
            // Sonuçları renderla
            function renderResults(data) {
                    // Profil bilgilerini güncelle
                $("#profile-nickname").text(data.profile.nickname || '');
                $("#profile-username").text('@' + data.profile.username);
                $("#profile-bio").text(data.profile.bio || '');
                $("#profile-followers").text(data.profile.followers || '0');
                $("#profile-following").text(data.profile.following || '0');
                $("#profile-likes").text(data.profile.likes || '0');
                
                const avatarElement = $("#profile-avatar");
                if (data.profile.avatar_url) {
                    avatarElement.attr('src', data.profile.avatar_url);
                } else {
                    avatarElement.attr('src', 'https://via.placeholder.com/100/25F4EE/FFFFFF?text=Profil');
                }
                    
                    // Performans metriklerini güncelle
                $("#total-views").text(formatNumber(data.profile.total_views || 0));
                $("#total-engagement").text(formatNumber(data.profile.total_engagement || 0));
                $("#total-engagement-rate").text((data.profile.total_engagement_rate || 0) + '%');
                $("#average-views").text(formatNumber(data.profile.average_views || 0));
                $("#average-likes").text(formatNumber(data.profile.average_likes || 0));
                $("#average-comments").text(formatNumber(data.profile.average_comments || 0));
                $("#average-shares").text(formatNumber(data.profile.average_shares || 0));
                    
                    // Grafikleri güncelle
                        updateGraphs(data.graphs);
                    
                    // Video içeriklerini güncelle
                    if (data.profile.videos) {
                        updateVideos(data.profile.videos);
                    }
                    
                    // Hashtag'leri güncelle
                    if (data.profile.most_used_hashtags) {
                        updateHashtags(data.profile.most_used_hashtags);
                    }
            }
            
            // Sayı formatla
            function formatNumber(number) {
                if (number >= 1000000) {
                    return (number / 1000000).toFixed(1) + 'M';
                } else if (number >= 1000) {
                    return (number / 1000).toFixed(1) + 'K';
            } else {
                    return number.toString();
                }
            }
            
            // Grafikleri güncelleme
        function updateGraphs(graphs) {
                if (graphs && graphs.views_timeline) {
                    try {
                        const viewsData = JSON.parse(graphs.views_timeline);
                        Plotly.newPlot('views-timeline', viewsData.data, viewsData.layout, {
                            displayModeBar: false,
                            responsive: true
                        });
                    } catch (e) {
                        console.error("Görüntülenme grafiği yüklenirken hata:", e);
                    }
                }
                
                if (graphs && graphs.popular_videos_engagement) {
                    try {
                        const engagementData = JSON.parse(graphs.popular_videos_engagement);
                        Plotly.newPlot('popular-videos-engagement', engagementData.data, engagementData.layout, {
                            displayModeBar: false,
                            responsive: true
                        });
                    } catch (e) {
                        console.error("Etkileşim grafiği yüklenirken hata:", e);
                    }
            }
        }
        
            // Videoları güncelleme
        function updateVideos(videos) {
                const container = $("#recent-videos");
                container.empty();
            
            if (!videos || videos.length === 0) {
                    container.html('<div class="col-12 text-center mt-4"><p>Video bulunamadı.</p></div>');
                return;
            }
            
            videos.forEach((video, idx) => {
                const videoCol = document.createElement('div');
                videoCol.className = 'col-md-4 mb-4';
                
                const description = video.description || `Video ${idx+1}`;
                const dateText = video.published_date || 'Tarih bilinmiyor';
                const videoId = video.id || '';
                const thumbnailUrl = video.thumbnail || `https://via.placeholder.com/300x400/FE2C55/FFFFFF?text=Video%20${idx+1}`;
                
                let engagementClass = 'low';
                const engagementRate = video.engagement_rate || 0;
                
                if (engagementRate >= 5) {
                    engagementClass = 'high';
                } else if (engagementRate >= 2) {
                    engagementClass = 'medium';
                }
                
                    videoCol.innerHTML = `
                    <div class="card h-100">
                            <div class="video-thumbnail-wrapper">
                                <img src="${thumbnailUrl}" class="video-thumbnail" alt="Video ${idx+1}" loading="lazy">
                            </div>
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title text-truncate">${description}</h5>
                            <p class="card-text text-muted mb-1">ID: ${videoId ? videoId.slice(-6) : 'N/A'}</p>
                            <p class="card-text text-muted">${dateText}</p>
                            <div class="post-metrics mt-auto">
                                <div class="post-metric">
                                    <div class="post-metric-value">${video.views || '0'}</div>
                                    <div class="post-metric-label">İzlenme</div>
                                </div>
                                <div class="post-metric">
                                    <div class="post-metric-value">${video.likes || '0'}</div>
                                    <div class="post-metric-label">Beğeni</div>
                                </div>
                                <div class="post-metric">
                                    <div class="post-metric-value">${video.comments || '0'}</div>
                                    <div class="post-metric-label">Yorum</div>
                                </div>
                                <div class="post-metric">
                                    <div class="post-metric-value">${video.shares || '0'}</div>
                                    <div class="post-metric-label">Paylaşım</div>
                                </div>
                            </div>
                            <div class="text-center mt-3">
                                <span class="engagement-badge ${engagementClass}">
                                    <i class="fas fa-bolt me-1"></i> %${engagementRate} Etkileşim
                                </span>
                            </div>
                        </div>
                    </div>
                `;
                
                    container.append(videoCol);
            });
        }
        
            // Hashtagleri güncelleme
        function updateHashtags(hashtags) {
                const hashtagList = $("#hashtag-list");
                hashtagList.empty();
            
            if (!hashtags || hashtags.length === 0) {
                    hashtagList.html('<p class="text-center">Hashtag bilgisi bulunamadı.</p>');
                return;
            }
            
                hashtags.forEach(([tag, count]) => {
                    const badge = $(`<span class="hashtag-badge">#${tag} (${count})</span>`);
                    hashtagList.append(badge);
                });
            }
            
            // Sosyal medya paylaşım fonksiyonları
            function shareOnFacebook() {
                if (!$("#profile-username").text()) return;
                
                const username = $("#profile-username").text();
                const followers = $("#profile-followers").text();
                const views = $("#total-views").text();
                const engagementRate = $("#total-engagement-rate").text();
                
                const text = `${username} TikTok profil analizi:\n\n` +
                            `👥 Takipçi: ${followers}\n` +
                            `👁️ Görüntülenme: ${views}\n` +
                            `❤️ Etkileşim Oranı: ${engagementRate}\n\n` +
                            `Detaylı analiz için:`;
                
                const url = window.location.href;
                const shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}&quote=${encodeURIComponent(text)}`;
                window.open(shareUrl, '_blank', 'width=600,height=400');
            }
            
            function shareOnTwitter() {
                if (!$("#profile-username").text()) return;
                
                const username = $("#profile-username").text();
                const followers = $("#profile-followers").text();
                const views = $("#total-views").text();
                const engagementRate = $("#total-engagement-rate").text();
                
                const text = `${username} TikTok analizi:\n\n` +
                            `👥 ${followers} takipçi\n` +
                            `👁️ ${views} görüntülenme\n` +
                            `❤️ ${engagementRate} etkileşim\n\n` +
                            `#TikTok #Analiz`;
                
                const url = window.location.href;
                const shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`;
                window.open(shareUrl, '_blank', 'width=600,height=400');
            }
            
            function shareOnWhatsApp() {
                if (!$("#profile-username").text()) return;
                
                const username = $("#profile-username").text();
                const followers = $("#profile-followers").text();
                const views = $("#total-views").text();
                const engagementRate = $("#total-engagement-rate").text();
                
                const text = `*${username}* TikTok profil analizi:\n\n` +
                            `👥 *Takipçi:* ${followers}\n` +
                            `👁️ *Görüntülenme:* ${views}\n` +
                            `❤️ *Etkileşim Oranı:* ${engagementRate}\n\n` +
                            `Detaylı analiz için: ${window.location.href}`;
                
                const shareUrl = `https://wa.me/?text=${encodeURIComponent(text)}`;
                window.open(shareUrl, '_blank');
            }
            
            function shareOnTelegram() {
                if (!$("#profile-username").text()) return;
                
                const username = $("#profile-username").text();
                const followers = $("#profile-followers").text();
                const views = $("#total-views").text();
                const engagementRate = $("#total-engagement-rate").text();
                
                const text = `${username} TikTok profil analizi:\n\n` +
                            `👥 Takipçi: ${followers}\n` +
                            `👁️ Görüntülenme: ${views}\n` +
                            `❤️ Etkileşim Oranı: ${engagementRate}\n\n` +
                            `Detaylı analiz için:`;
                
                const url = window.location.href;
                const shareUrl = `https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`;
                window.open(shareUrl, '_blank');
            }
            
            // Dışa aktarma fonksiyonları
            function exportToCSV() {
                if (!$("#profile-username").text()) {
                    alert("İndirilecek veri bulunamadı. Lütfen önce bir profil analiz edin.");
                    return;
                }
                
                const username = $("#profile-username").text().replace('@', '');
                const followers = $("#profile-followers").text();
                const following = $("#profile-following").text();
                const likes = $("#profile-likes").text();
                const total_views = $("#total-views").text();
                const total_engagement = $("#total-engagement").text();
                const total_engagement_rate = $("#total-engagement-rate").text();
                const average_views = $("#average-views").text();
                const average_likes = $("#average-likes").text();
                const average_comments = $("#average-comments").text();
                const average_shares = $("#average-shares").text();
                
                let csvContent = "data:text/csv;charset=utf-8,";
                
                // Profil özet bilgileri
                csvContent += "PROFİL ÖZET BİLGİLERİ\n";
                csvContent += "Kullanıcı Adı,Takipçi,Takip,Beğeni,Toplam İzlenme,Toplam Etkileşim,Etkileşim Oranı\n";
                csvContent += `${username},${followers},${following},${likes},${total_views},${total_engagement},${total_engagement_rate}\n\n`;
                
                // Ortalama değerler
                csvContent += "ORTALAMA DEĞERLER\n";
                csvContent += "Ortalama İzlenme,Ortalama Beğeni,Ortalama Yorum,Ortalama Paylaşım\n";
                csvContent += `${average_views},${average_likes},${average_comments},${average_shares}\n\n`;
            
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
                link.setAttribute("download", `tiktok_analiz_${username}_${new Date().toISOString().split('T')[0]}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        
        function exportToExcel() {
                if (!$("#profile-username").text()) {
                    alert("İndirilecek veri bulunamadı. Lütfen önce bir profil analiz edin.");
                    return;
                }
                
                // Profil verileri
                const username = $("#profile-username").text().replace('@', '');
            const profileData = [{
                    'Kullanıcı Adı': username,
                    'Takipçi': $("#profile-followers").text(),
                    'Takip': $("#profile-following").text(),
                    'Beğeni': $("#profile-likes").text(),
                    'Toplam İzlenme': $("#total-views").text(),
                    'Toplam Etkileşim': $("#total-engagement").text(),
                    'Etkileşim Oranı': $("#total-engagement-rate").text(),
                    'Ortalama İzlenme': $("#average-views").text(),
                    'Ortalama Beğeni': $("#average-likes").text(),
                    'Ortalama Yorum': $("#average-comments").text(),
                    'Ortalama Paylaşım': $("#average-shares").text()
                }];
            
            // Excel workbook oluştur
            const wb = XLSX.utils.book_new();
            const wsProfile = XLSX.utils.json_to_sheet(profileData);
            XLSX.utils.book_append_sheet(wb, wsProfile, "Profil Özeti");
            
            // Excel dosyasını indir
                XLSX.writeFile(wb, `tiktok_analiz_${username}_${new Date().toISOString().split('T')[0]}.xlsx`);
        }
        
        function exportToPDF() {
                if (!$("#profile-username").text()) {
                    alert("İndirilecek veri bulunamadı. Lütfen önce bir profil analiz edin.");
                    return;
                }
            
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();
                
                // Profil verileri
                const username = $("#profile-username").text();
                const nickname = $("#profile-nickname").text();
                const bio = $("#profile-bio").text();
                const followers = $("#profile-followers").text();
                const following = $("#profile-following").text();
                const likes = $("#profile-likes").text();
                const total_views = $("#total-views").text();
                const total_engagement = $("#total-engagement").text();
                const total_engagement_rate = $("#total-engagement-rate").text();
                const average_views = $("#average-views").text();
                const average_likes = $("#average-likes").text();
                const average_comments = $("#average-comments").text();
                const average_shares = $("#average-shares").text();
                
                // PDF başlık
            doc.setFontSize(20);
            doc.setTextColor(254, 44, 85); // TikTok kırmızısı
                doc.text('TikTok Profil Analiz Raporu', 105, 20, { align: 'center' });
            
            // Profil bilgileri
            doc.setFontSize(16);
            doc.setTextColor(0);
            doc.text('Profil Bilgileri', 14, 35);
            
            doc.setFontSize(12);
            doc.setTextColor(60);
                doc.text(`Kullanıcı Adı: ${username}`, 14, 45);
                if (nickname) doc.text(`Profil Adı: ${nickname}`, 14, 52);
                if (bio) {
                    const bioLines = doc.splitTextToSize(bio, 180);
                    let yPos = 59;
                    bioLines.forEach(line => {
                        doc.text(`Bio: ${line}`, 14, yPos);
                        yPos += 7;
                    });
                }
            
            // Temel metrikler
            doc.setFontSize(14);
            doc.setTextColor(0);
            doc.text('Temel Metrikler', 14, 75);
            
            const metrics = [
                    ['Takipçi', followers],
                    ['Takip', following],
                    ['Beğeni', likes],
                    ['Toplam İzlenme', total_views],
                    ['Toplam Etkileşim', total_engagement],
                    ['Etkileşim Oranı', total_engagement_rate]
            ];
            
            doc.autoTable({
                startY: 80,
                head: [['Metrik', 'Değer']],
                body: metrics,
                theme: 'striped',
                headStyles: { fillColor: [254, 44, 85] },
                styles: { fontSize: 10 }
            });
            
            // Ortalama değerler
            doc.setFontSize(14);
            doc.text('Ortalama Değerler', 14, doc.lastAutoTable.finalY + 15);
            
            const averages = [
                    ['Ortalama İzlenme', average_views],
                    ['Ortalama Beğeni', average_likes],
                    ['Ortalama Yorum', average_comments],
                    ['Ortalama Paylaşım', average_shares]
            ];
            
            doc.autoTable({
                startY: doc.lastAutoTable.finalY + 20,
                head: [['Metrik', 'Değer']],
                body: averages,
                theme: 'striped',
                headStyles: { fillColor: [254, 44, 85] },
                styles: { fontSize: 10 }
            });
            
            // Rapor bilgileri
            const now = new Date();
            doc.setFontSize(10);
            doc.setTextColor(100);
            doc.text(`Rapor Tarihi: ${now.toLocaleDateString('tr-TR')} ${now.toLocaleTimeString('tr-TR')}`, 14, doc.internal.pageSize.getHeight() - 10);
            
            // PDF'i indir
                doc.save(`tiktok_analiz_${username.replace('@', '')}_${now.toISOString().split('T')[0]}.pdf`);
            }
        });
    </script>
</body>
</html>
