import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom/client'
import { BrowserRouter as Router } from 'react-router-dom'
import App from './App.tsx'
import './index.css'

// Production'da console.error'ları filtrele
if (import.meta.env.PROD) {
  const originalError = console.error;
  console.error = (...args) => {
    // Browser extension hatalarını filtrele
    const message = String(args[0]);
    if (message.includes('browser is not defined') || 
        message.includes('checkPageManual') ||
        message.includes('overlays.js') ||
        message.includes('content.js') ||
        message.includes('ReferenceError: browser is not defined')) {
      return; // Bu hataları production'da gösterme
    }
    originalError(...args);
  };

  // Global error handler
  window.addEventListener('error', (event) => {
    const message = event.message || '';
    if (message.includes('browser is not defined') || 
        message.includes('checkPageManual') ||
        message.includes('overlays.js') ||
        message.includes('content.js')) {
      event.preventDefault();
      return false;
    }
  });

  // Unhandled promise rejection handler
  window.addEventListener('unhandledrejection', (event) => {
    const message = String(event.reason);
    if (message.includes('browser is not defined') || 
        message.includes('checkPageManual') ||
        message.includes('overlays.js') ||
        message.includes('content.js')) {
      event.preventDefault();
      return false;
    }
  });
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <Router>
      <App />
    </Router>
  </React.StrictMode>,
)