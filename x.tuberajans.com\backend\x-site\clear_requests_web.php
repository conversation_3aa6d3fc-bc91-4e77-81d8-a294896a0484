<?php
/**
 * TikTok isteklerini web üzerinden temizleme
 */

header('Content-Type: text/html; charset=utf-8');

try {
    // social_media_analytics veritabanına bağlan
    $db_social = new PDO("mysql:host=**************;dbname=social_media_analytics;charset=utf8mb4", 'root', 'Bebek845396!');
    $db_social->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $db_social->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

    echo "<h2>🔗 Veritabanına bağlanıldı</h2>";

    // Mevcut istekleri göster
    echo "<h3>📊 Mevcut İstekler:</h3>";

    $stmt = $db_social->prepare("SELECT id, username, status, created_at, current_step FROM tiktok_requests ORDER BY created_at DESC LIMIT 20");
    $stmt->execute();
    $requests = $stmt->fetchAll();

    if (empty($requests)) {
        echo "<p>✅ Hiç istek bulunamadı</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Kullanıcı</th><th>Durum</th><th>Tarih</th><th>Adım</th></tr>";
        foreach ($requests as $request) {
            echo "<tr>";
            echo "<td>{$request['id']}</td>";
            echo "<td>@{$request['username']}</td>";
            echo "<td>{$request['status']}</td>";
            echo "<td>{$request['created_at']}</td>";
            echo "<td>{$request['current_step']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    // İstatistikler
    $stmt = $db_social->prepare("SELECT status, COUNT(*) as count FROM tiktok_requests GROUP BY status");
    $stmt->execute();
    $stats = $stmt->fetchAll();

    echo "<h3>📈 İstek İstatistikleri:</h3>";
    if (!empty($stats)) {
        echo "<ul>";
        foreach ($stats as $stat) {
            echo "<li>{$stat['status']}: {$stat['count']} adet</li>";
        }
        echo "</ul>";
    }

    // VDS durumunu kontrol et
    $stmt = $db_social->prepare("SELECT * FROM vds_status");
    $stmt->execute();
    $vds_status = $stmt->fetchAll();

    echo "<h3>🖥️ VDS Durumu:</h3>";
    if (empty($vds_status)) {
        echo "<p>❌ Hiç VDS bulunamadı</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>VDS Adı</th><th>Durum</th><th>Son Ping</th><th>Mevcut Görev</th><th>Mevcut İstek</th><th>Bugün İşlenen</th><th>Toplam İşlenen</th></tr>";
        foreach ($vds_status as $vds) {
            echo "<tr>";
            echo "<td>{$vds['id']}</td>";
            echo "<td>{$vds['vds_name']}</td>";
            echo "<td>{$vds['status']}</td>";
            echo "<td>{$vds['last_ping']}</td>";
            echo "<td>{$vds['current_task']}</td>";
            echo "<td>{$vds['current_request_id']}</td>";
            echo "<td>{$vds['processed_today']}</td>";
            echo "<td>{$vds['total_processed']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    // Temizlik işlemi
    if (isset($_GET['action']) && $_GET['action'] === 'clean') {
        echo "<h3>🧹 Otomatik Temizlik Başlatılıyor...</h3>";

        // 1. Eski istekleri sil (24 saatten eski)
        $stmt = $db_social->prepare("DELETE FROM tiktok_requests WHERE created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)");
        $stmt->execute();
        $deleted_old = $stmt->rowCount();
        echo "<p>✅ {$deleted_old} adet eski istek silindi (24 saatten eski)</p>";

        // 2. Takılı kalmış processing istekleri sil (30 dakikadan eski)
        $stmt = $db_social->prepare("DELETE FROM tiktok_requests WHERE status = 'processing' AND updated_at < DATE_SUB(NOW(), INTERVAL 30 MINUTE)");
        $stmt->execute();
        $deleted_stuck = $stmt->rowCount();
        echo "<p>✅ {$deleted_stuck} adet takılı kalmış istek silindi (30 dakikadan eski processing)</p>";

        // 3. Başarısız istekleri sil
        $stmt = $db_social->prepare("DELETE FROM tiktok_requests WHERE status = 'failed'");
        $stmt->execute();
        $deleted_failed = $stmt->rowCount();
        echo "<p>✅ {$deleted_failed} adet başarısız istek silindi</p>";

        // 4. VDS durumunu sıfırla
        $stmt = $db_social->prepare("UPDATE vds_status SET status = 'online', current_request_id = NULL, current_task = NULL WHERE status != 'offline'");
        $stmt->execute();
        $updated_vds = $stmt->rowCount();
        echo "<p>✅ {$updated_vds} adet VDS durumu sıfırlandı</p>";

        $total_deleted = $deleted_old + $deleted_stuck + $deleted_failed;
        echo "<h4>🎯 Toplam {$total_deleted} istek temizlendi!</h4>";

        echo "<p><a href='clear_requests_web.php'>🔄 Sayfayı Yenile</a></p>";
    }

    // Tüm istekleri silme işlemi
    if (isset($_GET['action']) && $_GET['action'] === 'clear_all') {
        echo "<h3>🧹 TÜM İSTEKLER SİLİNİYOR...</h3>";

        $stmt = $db_social->prepare("DELETE FROM tiktok_requests");
        $stmt->execute();
        $deleted = $stmt->rowCount();
        echo "<p>✅ {$deleted} adet istek silindi</p>";

        // Auto increment'i sıfırla
        $db_social->exec("ALTER TABLE tiktok_requests AUTO_INCREMENT = 1");
        echo "<p>✅ ID sayacı sıfırlandı</p>";

        // VDS durumunu sıfırla
        $stmt = $db_social->prepare("UPDATE vds_status SET status = 'online', current_request_id = NULL, current_task = NULL");
        $stmt->execute();
        $updated_vds = $stmt->rowCount();
        echo "<p>✅ {$updated_vds} adet VDS durumu sıfırlandı</p>";

        echo "<p><a href='clear_requests_web.php'>🔄 Sayfayı Yenile</a></p>";
    }

    // Temizlik butonları
    if (!isset($_GET['action'])) {
        echo "<h3>🧹 Temizlik Seçenekleri:</h3>";
        echo "<p><a href='clear_requests_web.php?action=clean' style='background: #52c41a; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🧹 Otomatik Temizlik (Önerilen)</a></p>";
        echo "<p><a href='clear_requests_web.php?action=clear_all' style='background: #ff4d4f; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;' onclick='return confirm(\"TÜM İSTEKLER SİLİNECEK! Emin misiniz?\")'>🗑️ TÜM İSTEKLERİ SİL</a></p>";
    }

    echo "<h4>🎉 İşlem tamamlandı!</h4>";

} catch (Exception $e) {
    echo "<h3>❌ Hata: " . $e->getMessage() . "</h3>";
}
?>
