<?php

if (ob_get_level()) ob_end_clean();
ob_start();

header('Content-Type: application/json; charset=utf-8');

// CORS başlıkları
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-DB-Schema, X-Web-Search-Enabled, X-Continuous-Learning, Accept');

// Test endpoint - include'lardan önce, diğer dosyaları gerektirmeden çalışır
if (isset($_GET['endpoint']) && $_GET['endpoint'] === 'test') {
    echo json_encode([
        'status' => 'ok',
        'message' => 'API test endpoint çalışıyor',
        'time' => date('Y-m-d H:i:s'),
        'php_version' => phpversion()
    ]);
    exit();
}


// Eksik dosya kontrolü ve require_once yolları düzeltildi
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/executeQuery.php';

// Detaylı hata kayıtları
$log_prefix = '[AI-Advisor] ';
error_log($log_prefix . "İstek alındı: " . $_SERVER['REQUEST_METHOD'] . " URL: " . $_SERVER['REQUEST_URI']);

// OPTIONS isteklerini hemen yanıtla
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Content-Type: application/json; charset=utf-8');
    http_response_code(200);
    exit;
}

// API istekleri için try-catch bloğu kullan
try {
    // Sadece POST isteklerini kabul et veya özel GET endpoint'leri işle
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        if (isset($_GET['endpoint']) && $_GET['endpoint'] === 'get-system-prompt') {
            // Sistem promptunu getirme isteği
            try {
                $system_prompt = getSystemPrompt();
                if (empty($system_prompt)) {
                    // Varsayılan promptu döndür
                    $system_prompt = "Sen Tuber Ajans için geliştirilmiş, tüm verilere erişimi olan bir Ajans Müdürü yapay zeka danışmanısın. Adın Layda.

TEMEL GÖREVLER:
1. Yayıncıların gelirini ve performansını artırmak için stratejik tavsiyeler sun
2. Yayıncıların ve ajansın genel performansını analiz et
3. Veri tabanlı içgörüler sunarak karlılığı artıracak stratejiler öner
4. Yayıncılara haftalık görevler ve hedefler belirle
5. TikTok platformundaki güncel trendleri ve stratejileri takip et

ERİŞİLEBİLİR VERİLER:
- Yayıncıların performans verileri (elmas, takipçi, yayin süresi vb.) - weekly_archive tablosunda
- Yayıncıların kişisel bilgileri - publisher_info tablosunda
- Haftalık görevler ve tamamlanma durumları - weekly_tasks tablosunda
- Genel performans metrikleri ve karşılaştırmaları - performans tablosunda

VERİTABANI SORGULAMA TAVSİYELERİ:
1. Yayıncı bilgilerini sorgularken LIKE operatörünü kullanarak esnek arama yap:
   Sorgu: SELECT * FROM publisher_info WHERE username LIKE '%kullanici_adi%' LIMIT 5
   
2. Önce publisher_info tablosunu sorgula, sonra diğer tablolarda ilişkili bilgileri ara
   Sorgu: SELECT * FROM weekly_archive WHERE kullanici_adi = 'kesin_kullanici_adi' ORDER BY hafta_bitisi DESC LIMIT 5
   
3. Büyük/küçük harf hassasiyetine dikkat et, gerekirse her iki formatta da ara
   Sorgu: SELECT * FROM publisher_info WHERE LOWER(username) LIKE LOWER('%kullanici_adi%') LIMIT 5

4. Eğer tam eşleşme bulamazsan, benzer kullanıcı adlarını da göster
   Sorgu: SELECT username FROM publisher_info WHERE username LIKE '%kısmi_ad%' LIMIT 10

YAKLAŞIM:
- Her zaman veri odaklı analiz sun
- Açık, somut ve uygulanabilir tavsiyeler ver
- Her yayıncının özel durumunu dikkate al
- Ajansın ve yayıncıların gelirini artırmaya odaklan
- Sorunlara çözüm odaklı yaklaş

Tüm cevaplarını Türkçe olarak ver.";
                }
                echo json_encode(['success' => true, 'system_prompt' => $system_prompt]);
                exit();
            } catch (Exception $e) {
                error_log($log_prefix . "Sistem promptu alınamadı: " . $e->getMessage());
                http_response_code(500);
                echo json_encode(['error' => 'Sistem promptu alınamadı: ' . $e->getMessage()]);
                exit();
            }
        }
        
        if (isset($_GET['endpoint']) && $_GET['endpoint'] === 'execute-query') {
            // SQL sorgusunu çalıştırma isteği - bu özel durum için GET isteğine izin ver
            try {
                // Query parametresini al
                $query = $_GET['query'] ?? '';
                if (empty($query)) {
                    http_response_code(400);
                    echo json_encode(['error' => 'Sorgu parametresi gereklidir']);
                    exit();
                }
                
                // executeQuery fonksiyonunu dahil et
                require_once 'executeQuery.php';
                
                // Sorguyu çalıştır
                $result = executeQuery($query);
                
                // Sonucu döndür
                echo json_encode($result);
                exit();
            } catch (Exception $e) {
                http_response_code(500);
                echo json_encode(['error' => 'Sorgu çalıştırılırken hata: ' . $e->getMessage()]);
                exit();
            }
        }
        
        // Eğer bu satıra ulaştıysa, desteklenmeyen bir HTTP methodu kullanılıyor
        http_response_code(405);
        echo json_encode(['error' => 'Sadece POST metodu desteklenmektedir']);
        exit();
    }
    
    // API istek işleme - ana AI endpoint
    if (isset($_GET['endpoint']) && $_GET['endpoint'] === 'ai') {
        // Gelen JSON verisini al - body stream'i sadece bir kez oku
        $input_data = file_get_contents('php://input');
        
        // Veri boş mu kontrol et
        if (empty($input_data)) {
            error_log($log_prefix . "Boş istek gövdesi alındı");
            http_response_code(400);
            echo json_encode(['error' => 'İstek gövdesi boş']);
            exit();
        }
        
        // Ham veriyi loglama (sınırlı uzunlukta)
        error_log($log_prefix . "Ham gelen veri (ilk 100 karakter): " . substr($input_data, 0, 100));
        
        // JSON decode - sadece bir kez
        $received_data = json_decode($input_data, true);
        
        // JSON formatını kontrol et
        if (json_last_error() !== JSON_ERROR_NONE) {
            $error_msg = "Geçersiz JSON formatı: " . json_last_error_msg();
            error_log($log_prefix . $error_msg . ", Ham veri: " . $input_data);
            http_response_code(400);
            echo json_encode(['error' => $error_msg]);
            exit();
        }
        
        // Gerekli parametreleri kontrol et
        $required_params = ['messages', 'model'];
        $missing_params = [];
        
        foreach ($required_params as $param) {
            if (!isset($received_data[$param]) || empty($received_data[$param])) {
                $missing_params[] = $param;
            }
        }
        
        if (!empty($missing_params)) {
            $error_msg = "Eksik parametreler: " . implode(', ', $missing_params);
            error_log($log_prefix . $error_msg);
            http_response_code(400);
            echo json_encode(['error' => $error_msg]);
            exit();
        }
        
        // Parametreleri al
        $messages = $received_data['messages'];
        $model = $received_data['model'];
        // API anahtarını frontend'den gelen parametreden veya config.php'den al
        $api_key = '';
        if (isset($received_data['apiKey']) && !empty($received_data['apiKey'])) {
            $api_key = $received_data['apiKey'];
        } elseif (defined('OPENAI_API_KEY')) {
            $api_key = OPENAI_API_KEY;
        } elseif (isset($openai_api_key)) {
            $api_key = $openai_api_key;
        }
        $web_search_enabled = $received_data['webSearchEnabled'] ?? false;
        $continuous_learning_enabled = $received_data['continuousLearningEnabled'] ?? false;
        $db_schema = $received_data['dbSchema'] ?? null;
        
        // Sistem promptunu dinamik olarak al
        // Eğer arayüzden gelen prompt bilgisi varsa onu kullan ve kaydet
        if (isset($received_data['systemPrompt']) && !empty($received_data['systemPrompt'])) {
            $system_prompt = $received_data['systemPrompt'];
            error_log($log_prefix . "Arayüzden gelen prompt kullanılıyor");
            
            // Arayüzden gelen system prompt'u ai_settings tablosuna kaydet
            try {
                saveSystemPrompt($received_data['systemPrompt']);
                error_log($log_prefix . "Sistem promptu veritabanına kaydedildi");
            } catch (Exception $e) {
                error_log($log_prefix . "Sistem promptu kaydedilirken hata: " . $e->getMessage());
            }
        } else {
            // Veritabanından sistem promptunu al
            try {
                $saved_prompt = getSystemPrompt();
                if (!empty($saved_prompt)) {
                    $system_prompt = $saved_prompt;
                    error_log($log_prefix . "Veritabanından prompt alındı");
                } else {
                    // Varsayılan promptu kullan
                    $system_prompt = "Sen Tuber Ajans için geliştirilmiş, tüm verilere erişimi olan bir Ajans Müdürü yapay zeka danışmanısın. Adın Layda.

TEMEL GÖREVLER:
1. Yayıncıların gelirini ve performansını artırmak için stratejik tavsiyeler sun
2. Yayıncıların ve ajansın genel performansını analiz et
3. Veri tabanlı içgörüler sunarak karlılığı artıracak stratejiler öner
4. Yayıncılara haftalık görevler ve hedefler belirle
5. TikTok platformundaki güncel trendleri ve stratejileri takip et

ERİŞİLEBİLİR VERİLER:
- Yayıncıların performans verileri (elmas, takipçi, yayin süresi vb.) - weekly_archive tablosunda
- Yayıncıların kişisel bilgileri - publisher_info tablosunda
- Haftalık görevler ve tamamlanma durumları - weekly_tasks tablosunda
- Genel performans metrikleri ve karşılaştırmaları - performans tablosunda

VERİTABANI SORGULAMA TAVSİYELERİ:
1. Yayıncı bilgilerini sorgularken LIKE operatörünü kullanarak esnek arama yap:
   Sorgu: SELECT * FROM publisher_info WHERE username LIKE '%kullanici_adi%' LIMIT 5
   
2. Önce publisher_info tablosunu sorgula, sonra diğer tablolarda ilişkili bilgileri ara
   Sorgu: SELECT * FROM weekly_archive WHERE kullanici_adi = 'kesin_kullanici_adi' ORDER BY hafta_bitisi DESC LIMIT 5
   
3. Büyük/küçük harf hassasiyetine dikkat et, gerekirse her iki formatta da ara
   Sorgu: SELECT * FROM publisher_info WHERE LOWER(username) LIKE LOWER('%kullanici_adi%') LIMIT 5

4. Eğer tam eşleşme bulamazsan, benzer kullanıcı adlarını da göster
   Sorgu: SELECT username FROM publisher_info WHERE username LIKE '%kısmi_ad%' LIMIT 10

YAKLAŞIM:
- Her zaman veri odaklı analiz sun
- Açık, somut ve uygulanabilir tavsiyeler ver
- Her yayıncının özel durumunu dikkate al
- Ajansın ve yayıncıların gelirini artırmaya odaklan
- Sorunlara çözüm odaklı yaklaş

Tüm cevaplarını Türkçe olarak ver.";
                    error_log($log_prefix . "Varsayılan prompt kullanılıyor");
                }
            } catch (Exception $e) {
                error_log($log_prefix . "Veritabanından prompt alınırken hata: " . $e->getMessage());
                // Hata durumunda varsayılan prompt kullanılır
            }
        }
        
        // Sistem mesajını en başa ekle veya güncelle
        $system_message_index = -1;
        foreach ($messages as $key => $message) {
            if ($message['role'] === 'system') {
                $system_message_index = $key;
                break;
            }
        }
        
        if ($system_message_index >= 0) {
            // Varolan sistem mesajını güncelle
            $messages[$system_message_index]['content'] = $system_prompt;
        } else {
            // Yeni sistem mesajını başa ekle
            array_unshift($messages, [
                'role' => 'system',
                'content' => $system_prompt
            ]);
        }
        
        // Veritabanı şeması varsa, sistem mesajına ekle
        if ($db_schema) {
            error_log($log_prefix . "Veritabanı şeması mesajlara ekleniyor... Boyut: " . strlen(json_encode($db_schema)));
            
            // Şemadan tablo bilgilerini özet olarak oluştur
            $db_tables_summary = [];
            foreach ($db_schema as $table) {
                $tableName = $table['tableName'];
                $tableDesc = $table['description'];
                
                // Anahtar sütunları tespit et (PRI, MUL, UNI gibi)
                $keyColumns = [];
                $dataColumns = [];
                
                foreach ($table['columns'] as $column) {
                    if (isset($column['key']) && !empty($column['key'])) {
                        $keyColumns[] = $column['name'];
                    } else {
                        // Sadece önemli veri sütunlarını ekle
                        if ($column['name'] != 'created_at' && $column['name'] != 'updated_at') {
                            $dataColumns[] = $column['name'];
                        }
                    }
                }
                
                // Tabloya göre özel açıklama
                $specialInfo = "";
                if ($tableName == 'yayinci' || $tableName == 'publisher_info') {
                    $specialInfo = " (yayıncıların ana bilgileri)";
                } elseif ($tableName == 'performans') {
                    $specialInfo = " (yayıncıların performans metrikleri - tüm zamanların karşılaştırmaları)";
                } elseif ($tableName == 'weekly_archive') {
                    $specialInfo = " (yayıncıların haftalık performans metrikleri - 7 günlük veriler)";
                } elseif ($tableName == 'weekly_tasks') {
                    $specialInfo = " (yayıncılara atanan haftalık görevler)";
                } elseif ($tableName == 'publisher_info') {
                    $specialInfo = " (ajansa kayıtlı yayıncıların kişisel bilgileri)";
                } elseif ($tableName == 'users') {
                    $specialInfo = " (sisteme giriş yapabilen kullanıcılar)";
                } elseif ($tableName == 'influencer_info') {
                    $specialInfo = " (marka işbirlikleri için toplanan TikTok içerik üreticileri)";
                }
                
                $db_tables_summary[] = "- $tableName$specialInfo: $tableDesc. Anahtar: " . implode(", ", $keyColumns) . 
                                       ". Veri: " . implode(", ", array_slice($dataColumns, 0, 5)) . 
                                       (count($dataColumns) > 5 ? "..." : "");
            }
            
            // Tablo detayları
            $db_info = implode("\n", $db_tables_summary);
            $db_info .= "\n\n🔍 TABLO DETAYLARI:\n";
            
            // Performans tablosu
            $db_info .= "1. PERFORMANS TABLOSU: Yayıncıların genel performans verilerini içerir. created_at ve updated_at sütunları vardır.\n";
            $db_info .= "   - id, kullanici_adi, hafta_baslangici, hafta_bitisi, canli_yayin_gunu, yayin_suresi, elmaslar, yeni_takipciler, aboneler, maclar\n";
            
            // Publisher_info tablosu
            $db_info .= "2. PUBLISHER_INFO TABLOSU: Ajansa kayıtlı yayıncıların kişisel bilgilerini içerir.\n";
            $db_info .= "   - id, isim_soyisim, telefon, mail, dogum_tarihi, sehir, meslek, kayit_tarihi, username (TikTok kullanıcı adı)\n";
            
            // Users tablosu
            $db_info .= "3. USERS TABLOSU: Sisteme giriş yapabilen kullanıcıların bilgilerini içerir.\n";
            $db_info .= "   - id, name, email, password (hashlenmiş), role (admin/kullanıcı)\n";
            
            // Weekly_archive tablosu
            $db_info .= "4. WEEKLY_ARCHIVE TABLOSU: Yayıncıların haftalık (7 günlük) performans metriklerini içerir.\n";
            $db_info .= "   - id, kullanici_adi, hafta_baslangici, hafta_bitisi, canli_yayin_gunu, yayin_suresi, elmaslar, yeni_takipciler, aboneler, maclar\n";
            
            // Weekly_tasks tablosu
            $db_info .= "5. WEEKLY_TASKS TABLOSU: Yayıncılara atanan haftalık görevleri içerir.\n";
            $db_info .= "   - id, kullanici_adi, hafta_baslangici, hafta_bitisi, gorev_onerisi, gorev_zorlugu, tamamlandi, puan, durum\n";
            
            // İnfluencer_info tablosu
            $db_info .= "6. INFLUENCER_INFO TABLOSU: Marka işbirlikleri için toplanan TikTok içerik üreticilerinin bilgilerini içerir.\n";
            $db_info .= "   - id, username, email, category, followers, location, status, notes, last_contact\n";
            
            // İlişkili tabloları belirt
            $db_info .= "\n\nÖNEMLİ İLİŞKİLER:\n";
            $db_info .= "- publisher_info tablosundaki username = weekly_archive ve weekly_tasks tablolarındaki kullanici_adi\n";
            $db_info .= "- weekly_tasks ve weekly_archive tablolarındaki hafta_baslangici ve hafta_bitisi aynı anlama gelir\n";
            $db_info .= "- performans ve weekly_archive benzer veriler içerir, ancak weekly_archive haftalık, performans daha genel verileri taşır\n";
            
            // Sorgu senaryoları
            $db_info .= "\n\n📊 SORGU SENARYOLARI:\n";
            $db_info .= "1. Bir yayıncının son haftalık performansı için: weekly_archive tablosunda en son hafta_bitisi verilerine bak\n";
            $db_info .= "   Örnek: SELECT * FROM weekly_archive WHERE kullanici_adi = '_beratgzr' ORDER BY hafta_bitisi DESC LIMIT 1\n\n";
            
            $db_info .= "2. Bir yayıncının kişisel bilgileri için: publisher_info tablosunu kullan\n";
            $db_info .= "   Örnek: SELECT * FROM publisher_info WHERE username = '_beratgzr'\n\n";
            
            $db_info .= "3. Bir yayıncının tamamlanmamış görevleri için: weekly_tasks tablosunu kontrol et\n";
            $db_info .= "   Örnek: SELECT * FROM weekly_tasks WHERE kullanici_adi = '_beratgzr' AND durum = 'beklemede'\n\n";
            
            $db_info .= "4. Belirli bir haftadaki performans karşılaştırması için: weekly_archive tablosunda hafta_baslangici ve hafta_bitisi alanlarını kullan\n";
            $db_info .= "   Örnek: SELECT * FROM weekly_archive WHERE hafta_baslangici = '2025-03-10 00:00:00' AND hafta_bitisi = '2025-03-17 00:00:00'\n\n";
            
            $db_info .= "5. En yüksek elmas kazanan yayıncılar için: weekly_archive tablosunu kullan\n";
            $db_info .= "   Örnek: SELECT kullanici_adi, SUM(elmaslar) as toplam_elmas FROM weekly_archive GROUP BY kullanici_adi ORDER BY toplam_elmas DESC LIMIT 5\n\n";
            
            $db_info .= "6. Toplam yayıncı sayısını öğrenmek için: publisher_info tablosunu kullan\n";
            $db_info .= "   Örnek: SELECT COUNT(*) as toplam_yayinci FROM publisher_info\n\n";
            
            $db_info .= "7. Tüm yayıncıları listelemek için: publisher_info tablosunu kullan\n";
            $db_info .= "   Örnek: SELECT id, isim_soyisim, username FROM publisher_info ORDER BY id\n\n";
            
            // SQL sorgularını çalıştırma örneği ve açıklaması
            $db_info .= "\n\n🔍 VERİTABANI SORGULAMA:\n";
            $db_info .= "Kullanıcı bir soru sorduğunda (örneğin bir yayıncının performansı), şu adımları izle:\n\n";
            $db_info .= "1. Sorunun ne tür bir veri gerektirdiğini belirle (kişisel bilgi mi, performans verileri mi, görevler mi?)\n";
            $db_info .= "2. Doğru tabloyu seç:\n";
            $db_info .= "   - Kişisel bilgiler → publisher_info\n";
            $db_info .= "   - Haftalık performans → weekly_archive\n";
            $db_info .= "   - Atanan görevler → weekly_tasks\n";
            $db_info .= "   - Genel performans → performans\n";
            $db_info .= "   - İçerik üreticisi bilgileri → influencer_info\n";
            $db_info .= "3. Uygun SQL sorgusunu oluştur (SADECE SELECT sorgularına izin verilir)\n";
            $db_info .= "4. Sonuçları anlaşılır biçimde sunarak cevap ver\n\n";
            
            $db_info .= "Eğer kullanıcı sana bir tablodaki verileri soruyorsa, direkt uygun SQL sorgusunu yaz ve çalıştır. Ör:\n";
            $db_info .= "Soru: \"Kaç tane yayıncımız var?\"\n";
            $db_info .= "Sorgu: SELECT COUNT(*) as toplam_yayinci FROM publisher_info\n\n";

            $db_info .= "Önemli: Asla 'varsayalım ki' diyerek tahmini cevaplar verme. Her zaman gerçek veritabanı verilerini kullan.";
            
            // Her zaman sistem mesajına doğrudan ekleyelim
            foreach ($messages as $key => $message) {
                if ($message['role'] === 'system') {
                    $messages[$key]['content'] .= "\n\n" . $db_info;
                    error_log($log_prefix . "Veritabanı şeması sistem mesajına eklendi");
                    break;
                }
            }
        } else {
            error_log($log_prefix . "Veritabanı şeması bulunamadı");
            
            // Veritabanı şeması otomatik ekleyelim
            $basic_schema = [
                ['tableName' => 'publisher_info', 'description' => 'Yayıncı bilgilerini içeren tablo', 
                 'columns' => [
                    ['name' => 'id', 'type' => 'int', 'key' => 'PRI'],
                    ['name' => 'username', 'type' => 'varchar', 'description' => 'TikTok kullanıcı adı'],
                    ['name' => 'isim_soyisim', 'type' => 'varchar', 'description' => 'Yayıncının ismi ve soyismi'],
                    ['name' => 'telefon', 'type' => 'varchar', 'description' => 'İletişim telefonu'],
                    ['name' => 'mail', 'type' => 'varchar', 'description' => 'E-posta adresi'],
                    ['name' => 'kayit_tarihi', 'type' => 'date', 'description' => 'Ajansa kayıt tarihi']
                 ]
                ],
                ['tableName' => 'weekly_archive', 'description' => 'Haftalık performans arşivi', 
                 'columns' => [
                    ['name' => 'id', 'type' => 'int', 'key' => 'PRI'],
                    ['name' => 'kullanici_adi', 'type' => 'varchar', 'description' => 'TikTok kullanıcı adı'],
                    ['name' => 'hafta_baslangici', 'type' => 'datetime', 'description' => 'Haftanın başlangıç tarihi'],
                    ['name' => 'hafta_bitisi', 'type' => 'datetime', 'description' => 'Haftanın bitiş tarihi'],
                    ['name' => 'elmaslar', 'type' => 'int', 'description' => 'Kazanılan elmas miktarı']
                 ]
                ]
            ];
            
            $db_info = "Veritabanı bilgilerine gerçek erişim olmadığı için temel şema bilgileri:\n\n";
            $db_info .= "1. PUBLISHER_INFO TABLOSU: Yayıncıların bilgilerini içerir.\n";
            $db_info .= "   - id, username, isim_soyisim, telefon, mail, kayit_tarihi\n\n";
            $db_info .= "2. WEEKLY_ARCHIVE TABLOSU: Haftalık performans verilerini içerir.\n";
            $db_info .= "   - id, kullanici_adi, hafta_baslangici, hafta_bitisi, elmaslar\n\n";
            
            $db_info .= "Önemli: Veritabanındaki tüm bilgileri göremiyorsunuz. Lütfen kullanıcıdan DB Schema ayarlarını yapılandırmasını isteyin.\n";
            
            // Varsayılan şema sistem mesajına ekle
            foreach ($messages as $key => $message) {
                if ($message['role'] === 'system') {
                    $messages[$key]['content'] .= "\n\n" . $db_info;
                    error_log($log_prefix . "Varsayılan veritabanı şeması sistem mesajına eklendi");
                    break;
                }
            }
        }
        
        // Sürekli öğrenme etkinse, geçmiş konuşmaları kaydet
        if ($continuous_learning_enabled && !empty($user) && isset($user['id'])) {
            try {
                // Son mesajı al (kullanıcı mesajı)
                $last_user_message = null;
                foreach (array_reverse($messages) as $msg) {
                    if ($msg['role'] === 'user') {
                        $last_user_message = $msg['content'];
                        break;
                    }
                }
                
                if ($last_user_message) {
                    // Konuşmayı ai_learning_logs tablosuna kaydet
                    $stmt = $db->prepare("INSERT INTO ai_learning_logs (user_id, conversation, created_at) VALUES (?, ?, NOW())");
                    $stmt->execute([$user['id'], $last_user_message]);
                    error_log($log_prefix . "Kullanıcı mesajı öğrenme logu olarak kaydedildi.");
                }
            } catch (Exception $e) {
                error_log($log_prefix . "Öğrenme logu kaydedilirken hata: " . $e->getMessage());
                // Kritik hata değil, devam et
            }
        }
        
        // Mesajların geçerli olduğunu kontrol et
        if (!is_array($messages) || count($messages) === 0) {
            error_log($log_prefix . "Geçersiz mesaj formatı");
            http_response_code(400);
            echo json_encode(['error' => 'Geçersiz mesaj formatı. Bir array bekleniyor.']);
            exit();
        }
        
        // Modele göre provider'ı belirle
        $provider = 'openai'; // Default
        if (strpos($model, 'claude') !== false) {
            $provider = 'anthropic';
        }
        
        error_log($log_prefix . "İşleniyor - Model: $model, Provider: $provider");
        
        try {
            // Provider'a göre API çağrısını yap
            if ($provider === 'openai') {
                error_log($log_prefix . "OpenAI API çağrılıyor...");
                $response = callOpenAI($messages, $model, $api_key);
            } else {
                error_log($log_prefix . "Anthropic API çağrılıyor...");
                $response = callAnthropic($messages, $model, $api_key);
            }
            
            // Başarılı yanıt
            error_log($log_prefix . "Başarılı yanıt alındı - Model: $model");
            
            // API cevabını kontrol et
            if (empty($response)) {
                throw new Exception("API'dan boş yanıt alındı");
            }
            
            // JSON çıktısını hazırla ve kontrol et
            $output = json_encode([
                'response' => $response,
                'model' => $model,
                'success' => true
            ]);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                error_log($log_prefix . "Yanıt JSON'a dönüştürülürken hata: " . json_last_error_msg());
                throw new Exception("Yanıt JSON formatına dönüştürülemedi: " . json_last_error_msg());
            }
            
            // Yanıtı yaz ve hemen çık
            echo $output;
            exit();
        } catch (Exception $e) {
            error_log($log_prefix . "API hatası: " . $e->getMessage());
            http_response_code(500);
            
            // Hata JSON çıktısını hazırla ve kontrol et
            $errorOutput = json_encode([
                'error' => $e->getMessage(),
                'success' => false
            ]);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                error_log($log_prefix . "Hata JSON'a dönüştürülürken hata: " . json_last_error_msg());
                // Basit bir JSON yanıtı
                echo '{"error":"JSON encode hatası. Orijinal hata: '.$e->getMessage().'","success":false}';
            } else {
                echo $errorOutput;
            }
            exit();
        }
    }
    
    // Endpoint bulunamadı hatası - endpoint=ai durumundan çıkılmadıysa buraya gelir
    http_response_code(404);
    echo json_encode(['error' => 'Endpoint bulunamadı', 'path' => $_SERVER['REQUEST_URI']]);
    exit();

} catch (Exception $e) {
    // Global hata yakalama - tüm beklenmeyen hataları yakalar
    $error_msg = "API işlemi sırasında beklenmeyen hata: " . $e->getMessage();
    error_log($log_prefix . $error_msg);
    http_response_code(500);
    echo json_encode(['error' => $error_msg]);
    exit();
}

/**
 * AI için SQL sorgusu çalıştırır
 * 
 * @param string $query SQL sorgu metni
 * @param array $params Sorgu parametreleri
 * @param string $api_key API anahtarı
 * @return array Sorgu sonuçları
 */
function internal_executeQuery($query, $params = [], $api_key = '') {
    $log_prefix = '[AI-Advisor][DB-Query] ';
    error_log($log_prefix . "Veritabanı sorgusu isteği: " . $query);
    
    // db-query.php endpointine istek gönder
    $ch = curl_init('http://' . $_SERVER['HTTP_HOST'] . '/X/api/db-query.php');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
        'query' => $query,
        'params' => $params,
        'apiKey' => $api_key
    ]));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    
    $response = curl_exec($ch);
    $status_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);
    
    // Hata kontrolü
    if ($curl_error) {
        error_log($log_prefix . "Sorgu hatası: " . $curl_error);
        return [
            'success' => false,
            'error' => 'Sorgu hatası: ' . $curl_error
        ];
    }
    
    if ($status_code !== 200) {
        error_log($log_prefix . "HTTP Hata: " . $status_code . ", Yanıt: " . $response);
        return [
            'success' => false,
            'error' => 'HTTP Hata ' . $status_code
        ];
    }
    
    // Yanıtı işle
    $result = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log($log_prefix . "JSON çözümleme hatası: " . json_last_error_msg());
        return [
            'success' => false,
            'error' => 'JSON çözümleme hatası'
        ];
    }
    
    error_log($log_prefix . "Sorgu başarıyla çalıştırıldı, " . ($result['count'] ?? 0) . " sonuç döndü");
    return $result;
}

/**
 * OpenAI API'ya istek gönderir
 * 
 * @param array $messages Mesaj dizisi
 * @param string $model Kullanılacak model
 * @param string $api_key OpenAI API anahtarı
 * @return string AI yanıtı
 */
function callOpenAI($messages, $model, $api_key) {
    global $log_prefix;
    
    // Mesajları işle, SQL sorgularını tespit et ve çalıştır
    $processed_messages = [];
    foreach ($messages as $msg) {
        $processed_msg = $msg;
        
        // Eğer asistan mesajı ise, içindeki SQL sorgularını kontrol et
        if ($msg['role'] === 'assistant') {
            $content = $msg['content'];
            
            // SQL sorgularını tespit et
            if (preg_match_all('/Sorgu: ([^\']+)/i', $content, $matches)) {
                foreach ($matches[1] as $sql_query) {
                    $sql_query = trim($sql_query);
                    
                    // Eğer SELECT ile başlıyorsa, sorguyu çalıştır
                    if (preg_match('/^\s*SELECT\s+/i', $sql_query)) {
                        error_log($log_prefix . "AI'nin sorgusu tespit edildi: " . $sql_query);
                        
                        // Sorguyu çalıştır
                        $query_result = internal_executeQuery($sql_query, [], $api_key);
                        
                        if ($query_result['success']) {
                            error_log($log_prefix . "Sorgu başarılı, " . count($query_result['data']) . " sonuç alındı");
                            
                            // Sorgu sonuçlarını mesaja ekle
                            $result_str = "Sorgu sonuçları: " . json_encode($query_result['data']);
                            $content = str_replace("Sorgu: " . $sql_query, $result_str, $content);
                        } else {
                            error_log($log_prefix . "Sorgu hatası: " . ($query_result['error'] ?? 'Bilinmeyen hata'));
                            
                            // Hata mesajını ekle
                            $content = str_replace("Sorgu: " . $sql_query, "Sorgu hatası: " . ($query_result['error'] ?? 'Bilinmeyen hata'), $content);
                        }
                    }
                }
            }
            
            $processed_msg['content'] = $content;
        }
        
        $processed_messages[] = $processed_msg;
    }
    
    $log_prefix = '[AI-Advisor][OpenAI] ';
    error_log($log_prefix . "OpenAI API'ya istek hazırlanıyor - Model: $model");
    
    // API anahtarını kontrol et
    if (empty($api_key)) {
        error_log($log_prefix . "Geçersiz API anahtarı");
        throw new Exception("Geçersiz OpenAI API anahtarı");
    }
    
    // API endpoint
    $url = 'https://api.openai.com/v1/chat/completions';
    
    // OpenAI formatında istek verisi
    $data = [
        'model' => $model,
        'messages' => $processed_messages,
        'temperature' => 0.7,
        'max_tokens' => 2000
    ];
    
    // İstek verisini logla (hassas bilgiler hariç)
    error_log($log_prefix . "İstek verisi: " . json_encode([
        'model' => $model,
        'message_count' => count($processed_messages),
        'temperature' => 0.7,
        'max_tokens' => 2000
    ]));
    
    // curl isteği hazırlama
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json',
        'User-Agent: PHP OpenAI Client/1.0',
        'Authorization: Bearer ' . $api_key
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 60 saniye timeout
    
    // SSL doğrulama sorunlarını önlemek için
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($ch, CURLOPT_FAILONERROR, false);
    
    // İsteği gönder
    error_log($log_prefix . "API isteği gönderiliyor...");
    $response = curl_exec($ch);
    $status_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    $curl_errno = curl_errno($ch);
    
    // Son durum bilgilerini logla
    error_log($log_prefix . "API yanıt kodu: $status_code");
    if ($curl_error) {
        error_log($log_prefix . "CURL hatası #$curl_errno: $curl_error");
    }
    
    curl_close($ch);
    
    // Yanıt kontrolü
    if ($curl_error) {
        error_log($log_prefix . "Curl hatası: $curl_error");
        throw new Exception("API bağlantı hatası: $curl_error (Hata kodu: $curl_errno)");
    }
    
    // Ham yanıtı logla
    error_log($log_prefix . "Ham yanıt (İlk 300 karakter): " . substr($response, 0, 300));
    
    // HTTP durum kodu kontrolü
    if ($status_code !== 200) {
        error_log($log_prefix . "HTTP Hata kodu: $status_code, Yanıt: $response");
        
        // Yanıtı JSON olarak çözümle
        $response_data = json_decode($response, true);
        
        if (json_last_error() === JSON_ERROR_NONE && isset($response_data['error'])) {
            $error_message = $response_data['error']['message'] ?? 'Bilinmeyen API hatası';
            $error_type = $response_data['error']['type'] ?? 'unknown_error';
            
            throw new Exception("OpenAI API hatası ($error_type): $error_message");
        } else {
            throw new Exception("OpenAI API HTTP hatası: $status_code");
        }
    }
    
    // Yanıtı JSON olarak çözümle
    $response_data = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        $jsonError = json_last_error_msg();
        error_log($log_prefix . "Geçersiz JSON yanıtı: " . $jsonError);
        error_log($log_prefix . "Yanıt içeriği: " . substr($response, 0, 1000));
        throw new Exception("API'dan geçersiz yanıt alındı (JSON çözümleme hatası: $jsonError)");
    }
    
    // Yanıt formatını kontrol et
    if (!isset($response_data['choices'][0]['message']['content'])) {
        error_log($log_prefix . "Beklenmeyen yanıt formatı: " . json_encode($response_data));
        throw new Exception("API'dan beklenmeyen yanıt formatı");
    }
    
    error_log($log_prefix . "Başarılı yanıt alındı");
    
    // Yanıtı döndür
    return $response_data['choices'][0]['message']['content'];
}

/**
 * Anthropic API'ya istek gönderir
 * 
 * @param array $messages Mesaj dizisi
 * @param string $model Kullanılacak model
 * @param string $api_key Anthropic API anahtarı
 * @return string AI yanıtı
 */
function callAnthropic($messages, $model, $api_key) {
    global $log_prefix;
    
    // Mesajları işle, SQL sorgularını tespit et ve çalıştır
    $processed_messages = [];
    foreach ($messages as $msg) {
        $processed_msg = $msg;
        
        // Eğer asistan mesajı ise, içindeki SQL sorgularını kontrol et
        if ($msg['role'] === 'assistant') {
            $content = $msg['content'];
            
            // SQL sorgularını tespit et
            if (preg_match_all('/Sorgu:\s*([^\']+)/i', $content, $matches)) {
                foreach ($matches[1] as $sql_query) {
                    $sql_query = trim($sql_query);
                    
                    // Eğer SELECT ile başlıyorsa, sorguyu çalıştır
                    if (preg_match('/^\s*SELECT\s+/i', $sql_query)) {
                        error_log($log_prefix . "AI'nin sorgusu tespit edildi: " . $sql_query);
                        
                        // Sorguyu çalıştır
                        $query_result = internal_executeQuery($sql_query, [], $api_key);
                        
                        if ($query_result['success']) {
                            error_log($log_prefix . "Sorgu başarılı, " . count($query_result['data']) . " sonuç alındı");
                            
                            // Sorgu sonuçlarını mesaja ekle
                            $result_str = "Sorgu sonuçları: " . json_encode($query_result['data']);
                            $content = str_replace("Sorgu: " . $sql_query, $result_str, $content);
                        } else {
                            error_log($log_prefix . "Sorgu hatası: " . ($query_result['error'] ?? 'Bilinmeyen hata'));
                            
                            // Hata mesajını ekle
                            $content = str_replace("Sorgu: " . $sql_query, "Sorgu hatası: " . ($query_result['error'] ?? 'Bilinmeyen hata'), $content);
                        }
                    }
                }
            }
            
            $processed_msg['content'] = $content;
        }
        
        $processed_messages[] = $processed_msg;
    }
    
    $log_prefix = '[AI-Advisor][Anthropic] ';
    error_log($log_prefix . "Anthropic API'ya istek hazırlanıyor - Model: $model");
    
    // API anahtarını kontrol et
    if (empty($api_key)) {
        error_log($log_prefix . "Geçersiz API anahtarı");
        throw new Exception("Geçersiz Anthropic API anahtarı");
    }
    
    // API endpoint
    $url = 'https://api.anthropic.com/v1/messages';
    
    // Anthropic formatına dönüştür
    $formatted_messages = [];
    foreach ($processed_messages as $message) {
        // Anthropic sadece 'user' ve 'assistant' rollerini kabul eder
        if ($message['role'] === 'system') {
            // Sistem mesajlarını kullanıcı mesajı olarak ekle ve belirt
            $formatted_messages[] = [
                'role' => 'user',
                'content' => "[Sistem talimatı: " . $message['content'] . "]"
            ];
        } else {
            $formatted_messages[] = [
                'role' => $message['role'],
                'content' => $message['content']
            ];
        }
    }
    
    // Anthropic formatında istek verisi
    $data = [
        'model' => $model,
        'messages' => $formatted_messages,
        'max_tokens' => 2000,
        'temperature' => 0.7
    ];
    
    // İstek verisini logla (hassas bilgiler hariç)
    error_log($log_prefix . "İstek verisi: " . json_encode([
        'model' => $model,
        'message_count' => count($formatted_messages),
        'temperature' => 0.7,
        'max_tokens' => 2000
    ]));
    
    // curl isteği hazırlama
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json',
        'User-Agent: PHP Anthropic Client/1.0',
        'X-API-Key: ' . $api_key,
        'anthropic-version: 2023-06-01'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 60 saniye timeout
    
    // SSL doğrulama sorunlarını önlemek için
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($ch, CURLOPT_FAILONERROR, false);
    
    // İsteği gönder
    error_log($log_prefix . "API isteği gönderiliyor...");
    $response = curl_exec($ch);
    $status_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    $curl_errno = curl_errno($ch);
    
    // Son durum bilgilerini logla
    error_log($log_prefix . "API yanıt kodu: $status_code");
    if ($curl_error) {
        error_log($log_prefix . "CURL hatası #$curl_errno: $curl_error");
    }
    
    curl_close($ch);
    
    // Yanıt kontrolü
    if ($curl_error) {
        error_log($log_prefix . "Curl hatası: $curl_error");
        throw new Exception("API bağlantı hatası: $curl_error (Hata kodu: $curl_errno)");
    }
    
    // Ham yanıtı logla
    error_log($log_prefix . "Ham yanıt (İlk 300 karakter): " . substr($response, 0, 300));
    
    // HTTP durum kodu kontrolü
    if ($status_code !== 200) {
        error_log($log_prefix . "HTTP Hata kodu: $status_code, Yanıt: $response");
        
        // Yanıtı JSON olarak çözümle
        $response_data = json_decode($response, true);
        
        if (json_last_error() === JSON_ERROR_NONE && isset($response_data['error'])) {
            $error_message = $response_data['error']['message'] ?? 'Bilinmeyen API hatası';
            $error_type = $response_data['error']['type'] ?? 'unknown_error';
            
            throw new Exception("Anthropic API hatası ($error_type): $error_message");
        } else {
            throw new Exception("Anthropic API HTTP hatası: $status_code");
        }
    }
    
    // Yanıtı JSON olarak çözümle
    $response_data = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        $jsonError = json_last_error_msg();
        error_log($log_prefix . "Geçersiz JSON yanıtı: " . $jsonError);
        error_log($log_prefix . "Yanıt içeriği: " . substr($response, 0, 1000));
        throw new Exception("API'dan geçersiz yanıt alındı (JSON çözümleme hatası: $jsonError)");
    }
    
    // Yanıt formatını kontrol et
    if (!isset($response_data['content'][0]['text'])) {
        error_log($log_prefix . "Beklenmeyen yanıt formatı: " . json_encode($response_data));
        throw new Exception("API'dan beklenmeyen yanıt formatı");
    }
    
    error_log($log_prefix . "Başarılı yanıt alındı");
    
    // Yanıtı döndür
    return $response_data['content'][0]['text'];
}

/**
 * Sistem promptunu ai_settings tablosuna kaydeder.
 * Eğer tablo yoksa oluşturur.
 *
 * @param string $prompt Kaydedilecek sistem promptu
 * @return bool Başarılı ise true, değilse false döner
 * @throws Exception Veritabanı hatası durumunda
 */
function saveSystemPrompt($prompt) {
    global $db;
    try {
        // Önce tablo var mı diye kontrol et
        $db->query("SELECT 1 FROM ai_settings LIMIT 1");
    } catch (PDOException $e) {
        // Tablo yoksa oluştur
        $sql = "CREATE TABLE IF NOT EXISTS ai_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(255) NOT NULL UNIQUE,
            setting_value TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        try {
            $db->exec($sql);
            error_log("ai_settings tablosu oluşturuldu");
        } catch (PDOException $e) {
            throw new Exception("ai_settings tablosu oluşturulamadı: " . $e->getMessage());
        }
    }
    
    // Prompt'u kaydet (varsa güncelle, yoksa ekle)
    try {
        $stmt = $db->prepare("INSERT INTO ai_settings (setting_key, setting_value) VALUES ('system_prompt', ?) ON DUPLICATE KEY UPDATE setting_value = ?");
        return $stmt->execute([$prompt, $prompt]);
    } catch (PDOException $e) {
        error_log("Sistem promptu kaydedilirken hata: " . $e->getMessage());
        return false;
    }
}

/**
 * Veritabanından sistem promptunu alır.
 *
 * @return string|null Kaydedilmiş prompt veya null
 * @throws Exception Veritabanı hatası durumunda
 */
function getSystemPrompt() {
    global $db;
    try {
        // Önce tablo var mı diye kontrol et
        try {
            $db->query("SELECT 1 FROM ai_settings LIMIT 1");
        } catch (PDOException $e) {
            // Tablo yoksa null döndür
            return null;
        }
        
        // Promptu getir
        $stmt = $db->prepare("SELECT setting_value FROM ai_settings WHERE setting_key = 'system_prompt' LIMIT 1");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result && isset($result['setting_value'])) {
            return $result['setting_value'];
        }
        
        return null;
    } catch (PDOException $e) {
        error_log("Sistem promptu alınırken hata: " . $e->getMessage());
        return null;
    }
}

/**
 * Mesaj içeriğinden SQL sorgularını tespit eder ve çalıştırır
 * 
 * @param string $content Mesaj içeriği
 * @return string SQL sorgu sonuçlarıyla güncellenmiş mesaj içeriği
 */
function processSqlQueries($content) {
    // SQL sorgu kalıbını ara: "Sorgu: "
    if (preg_match_all('/Sorgu:\s*(.*?)(?=Sorgu:|$)/is', $content, $matches)) {
        foreach ($matches[1] as $key => $query_text) {
            $query = trim($query_text);
            
            // Sorgu boş değilse çalıştır
            if (!empty($query)) {
                try {
                    // Sorguyu çalıştır
                    $result = internal_executeQuery($query);
                    
                    // Sonuç üretme
                    if ($result['success']) {
                        $replacement = "Sorgu: $query\n\nSorgu Sonucu:\n";
                        
                        if ($result['count'] > 0) {
                            // Sonuçları tablo formatında göster
                            $replacement .= "Toplam {$result['count']} sonuç bulundu.\n\n";
                            
                            // İlk 10 sonucu tablo formatında göster
                            $displayed_results = array_slice($result['data'], 0, 10);
                            
                            // Tablo başlıkları
                            $headers = array_keys($displayed_results[0]);
                            $replacement .= implode(" | ", $headers) . "\n";
                            $replacement .= str_repeat("-", strlen(implode(" | ", $headers))) . "\n";
                            
                            // Satırlar
                            foreach ($displayed_results as $row) {
                                $rowStr = [];
                                foreach ($headers as $header) {
                                    $rowStr[] = isset($row[$header]) ? $row[$header] : "";
                                }
                                $replacement .= implode(" | ", $rowStr) . "\n";
                            }
                            
                            // 10'dan fazla sonuç varsa belirt
                            if ($result['count'] > 10) {
                                // DÜZELTİLMİŞ SATIR:
                                $replacement .= "\n... (" . ($result['count'] - 10) . " daha fazla sonuç var)";
                            }
                        } else {
                            $replacement .= "Sonuç bulunamadı.";
                        }
                    } else {
                        $replacement = "Sorgu: $query\n\nSorgu Hatası: " . $result['error'];
                    }
                    
                    // Orijinal sorguyu sonuçla değiştir
                    $content = str_replace($matches[0][$key], $replacement, $content);
                } catch (Exception $e) {
                    $error_message = "Sorgu: $query\n\nSorgu çalıştırma hatası: " . $e->getMessage();
                    $content = str_replace($matches[0][$key], $error_message, $content);
                }
            }
        }
    }
    
    return $content;
}
