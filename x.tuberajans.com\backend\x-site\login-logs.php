<?php
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/../config/config.php';

try {
    $stmt = $db->query("SELECT 
        id, 
        id AS userId, 
        email AS userName, 
        '' AS ipAddress, 
        'success' AS status, 
        attempt_time AS timestamp, 
        '' AS userAgent, 
        '' AS details 
        FROM login_attempts ORDER BY attempt_time DESC LIMIT 100");
    $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    if (ob_get_length()) ob_clean();
    echo json_encode(['success' => true, 'logs' => $logs]);
} catch (PDOException $e) {
    if (ob_get_length()) ob_clean();
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
} 