<?php
/**
 * Destek Talepleri API  
 * Bu API destek taleplerini yönetmek için kullanılır
 */

// Hata ayıklama
ini_set('display_errors', 1);
error_reporting(E_ALL);

// CORS ve JSON header'ları
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS, DELETE');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=utf-8');

// OPTIONS istekleri için hızlıca 200 dön
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Ortak config dosyasını dahil et
require_once __DIR__ . '/../config/config.php';

// JSON yanıt fonksiyonu
if (!function_exists('jsonResponse')) {
    function jsonResponse($data, $status = 200) {
        http_response_code($status);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}

// Auth kontrolü (checkAuth fonksiyonu - diğer API'ler gibi)
if (!function_exists('checkAuth')) {
    function checkAuth() {
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
        
        if (strpos($authHeader, 'Bearer ') === 0) {
            $token = substr($authHeader, 7);
            if (strlen($token) > 10) { // Basit token kontrolü
                return true;
            }
        }
        return false;
    }
}

// Config dosyasından $db_akademi değişkeni zaten tanımlı
// Veritabanı bağlantısını kontrol et
if (!isset($db_akademi)) {
    jsonResponse(['success' => false, 'message' => 'Veritabanı bağlantısı bulunamadı'], 500);
}

// İstek metodunu al
$method = $_SERVER['REQUEST_METHOD'];
$action = isset($_GET['action']) ? $_GET['action'] : '';
$type = isset($_GET['type']) ? $_GET['type'] : 'tickets'; // tickets, replies, categories

// POST verilerini al
if ($method === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
}

// Debug log
error_log("Support API - Method: $method, Action: $action, Type: $type");
if (isset($input)) {
    error_log("Support API - Input: " . json_encode($input));
}

// Test verisi ekleme endpoint'i
if ($action === 'add_test_data') {
    // POST istekleri için auth gerekli
    if (!checkAuth()) {
        jsonResponse(['success' => false, 'message' => 'Bu işlemi gerçekleştirmek için giriş yapmalısınız.'], 401);
    }

    try {
        // Test destek talepleri ekle
        $testTickets = [
            [
                'subject' => 'TikTok hesabım askıya alındı',
                'message' => 'Merhaba, TikTok hesabım sebepsiz yere askıya alındı. Nasıl geri açtırabilirim?',
                'category' => 'account',
                'status' => 'open',
                'priority' => 'high'
            ],
            [
                'subject' => 'Video yükleme hatası',
                'message' => 'Video yüklemeye çalışırken sürekli hata alıyorum. Çözüm önerebilir misiniz?',
                'category' => 'technical',
                'status' => 'open',
                'priority' => 'medium'
            ],
            [
                'subject' => 'Ödeme sorunu',
                'message' => 'Premium özellikler için ödeme yaptım ama hesabıma yansımadı.',
                'category' => 'payment',
                'status' => 'open',
                'priority' => 'urgent'
            ],
            [
                'subject' => 'İçerik politikaları hakkında',
                'message' => 'Hangi tür içerikler yasak? Detaylı bilgi alabilir miyim?',
                'category' => 'content',
                'status' => 'closed',
                'priority' => 'low'
            ]
        ];

        foreach ($testTickets as $ticket) {
            $stmt = $db_akademi->prepare("
                INSERT INTO support_tickets (user_id, subject, message, category, status, priority, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
            ");
            $stmt->execute([
                1, // Varsayılan admin user_id
                $ticket['subject'],
                $ticket['message'],
                $ticket['category'],
                $ticket['status'],
                $ticket['priority']
            ]);
        }

        jsonResponse(['success' => true, 'message' => 'Test verileri başarıyla eklendi']);
    } catch (PDOException $e) {
        error_log("Support API - Add test data error: " . $e->getMessage());
        jsonResponse(['success' => false, 'message' => 'Test verisi ekleme hatası: ' . $e->getMessage()], 500);
    }
}

// ==================== TICKETS ====================
if ($type === 'tickets') {

    if ($method === 'GET' && $action === 'list') {
        // Talepleri Listele - GET istekleri public (herkes görebilir)
        try {
            $stmt = $db_akademi->query("
                SELECT
                    t.id,
                    t.user_id,
                    u.username,
                    t.subject,
                    t.message,
                    t.category,
                    t.status,
                    t.priority,
                    t.created_at,
                    t.updated_at,
                    (SELECT COUNT(*) FROM support_replies WHERE ticket_id = t.id) as replies_count
                FROM
                    support_tickets t
                LEFT JOIN
                    users u ON t.user_id = u.id
                ORDER BY
                    t.created_at DESC
            ");
            $data = $stmt->fetchAll(PDO::FETCH_ASSOC);

            jsonResponse(['success' => true, 'data' => $data]);
        } catch (PDOException $e) {
            error_log("Support API - Ticket list error: " . $e->getMessage());
            jsonResponse(['success' => false, 'message' => 'Veritabanı hatası: ' . $e->getMessage()], 500);
        }
    }

    else if ($method === 'GET' && $action === 'count_open') {
        // Açık Talep Sayısı - GET istekleri public
        try {
            error_log("Support API - Count_open action - DB connection test");

            $stmt = $db_akademi->query("SELECT COUNT(*) as count FROM support_tickets WHERE status = 'open'");
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $count = isset($row['count']) ? intval($row['count']) : 0;

            error_log("Support API - Count_open result: " . $count);
            jsonResponse(['success' => true, 'count' => $count]);
        } catch (PDOException $e) {
            error_log("Support API - Count_open PDO error: " . $e->getMessage());
            jsonResponse(['success' => false, 'message' => 'Veritabanı hatası: ' . $e->getMessage()], 500);
        }
    }

    else if ($method === 'POST' && $action === 'update_status') {
        // Durum Güncelle - POST istekleri için auth gerekli
        if (!checkAuth()) {
            jsonResponse(['success' => false, 'message' => 'Bu işlemi gerçekleştirmek için giriş yapmalısınız.'], 401);
        }

        if (!isset($input['ticket_id']) || !isset($input['status'])) {
            jsonResponse(['success' => false, 'message' => 'Eksik alanlar: ticket_id ve status gerekli.'], 400);
        }

        try {
            $stmt = $db_akademi->prepare("UPDATE support_tickets SET status = ?, priority = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$input['status'], $input['priority'] ?? 'medium', $input['ticket_id']]);

            jsonResponse(['success' => true, 'message' => 'Talep durumu güncellendi.']);
        } catch (PDOException $e) {
            error_log("Support API - Update status error: " . $e->getMessage());
            jsonResponse(['success' => false, 'message' => 'Veritabanı hatası: ' . $e->getMessage()], 500);
        }
    }

    else if ($method === 'POST' && $action === 'close') {
        // Talebi Kapat - POST istekleri için auth gerekli
        if (!checkAuth()) {
            jsonResponse(['success' => false, 'message' => 'Bu işlemi gerçekleştirmek için giriş yapmalısınız.'], 401);
        }

        if (!isset($input['ticket_id'])) {
            jsonResponse(['success' => false, 'message' => 'Eksik alan: ticket_id gerekli.'], 400);
        }

        try {
            $stmt = $db_akademi->prepare("UPDATE support_tickets SET status = 'closed', updated_at = NOW() WHERE id = ?");
            $stmt->execute([$input['ticket_id']]);

            jsonResponse(['success' => true, 'message' => 'Talep kapatıldı.']);
        } catch (PDOException $e) {
            error_log("Support API - Close ticket error: " . $e->getMessage());
            jsonResponse(['success' => false, 'message' => 'Veritabanı hatası: ' . $e->getMessage()], 500);
        }
    }

    else if ($method === 'POST' && $action === 'delete') {
        // Talebi Sil - POST istekleri için auth gerekli
        if (!checkAuth()) {
            jsonResponse(['success' => false, 'message' => 'Bu işlemi gerçekleştirmek için giriş yapmalısınız.'], 401);
        }

        if (!isset($input['id'])) {
            jsonResponse(['success' => false, 'message' => 'Eksik alan: id gerekli.'], 400);
        }

        try {
            // Önce yanıtları sil
            $stmt = $db_akademi->prepare("DELETE FROM support_replies WHERE ticket_id = ?");
            $stmt->execute([$input['id']]);

            // Sonra talebi sil
            $stmt = $db_akademi->prepare("DELETE FROM support_tickets WHERE id = ?");
            $stmt->execute([$input['id']]);

            jsonResponse(['success' => true, 'message' => 'Talep silindi.']);
        } catch (PDOException $e) {
            error_log("Support API - Delete ticket error: " . $e->getMessage());
            jsonResponse(['success' => false, 'message' => 'Veritabanı hatası: ' . $e->getMessage()], 500);
        }
    }
}

// ==================== REPLIES ====================
else if ($type === 'replies') {

    if ($method === 'GET' && $action === 'list') {
        // Yanıtları Listele - GET istekleri public
        $ticket_id = isset($_GET['ticket_id']) ? intval($_GET['ticket_id']) : 0;
        if ($ticket_id <= 0) {
            jsonResponse(['success' => false, 'message' => 'Geçersiz talep ID.'], 400);
        }

        try {
            $stmt = $db_akademi->prepare("
                SELECT
                    r.id,
                    r.ticket_id,
                    r.user_id,
                    u.username,
                    r.message,
                    r.is_admin,
                    r.created_at
                FROM
                    support_replies r
                LEFT JOIN
                    users u ON r.user_id = u.id
                WHERE
                    r.ticket_id = ?
                ORDER BY
                    r.created_at ASC
            ");
            $stmt->execute([$ticket_id]);
            $data = $stmt->fetchAll(PDO::FETCH_ASSOC);

            jsonResponse(['success' => true, 'data' => $data]);
        } catch (PDOException $e) {
            error_log("Support API - Replies list error: " . $e->getMessage());
            jsonResponse(['success' => false, 'message' => 'Veritabanı hatası: ' . $e->getMessage()], 500);
        }
    }

    else if ($method === 'POST' && $action === 'add') {
        // Yanıt Ekle - POST istekleri için auth gerekli
        if (!checkAuth()) {
            jsonResponse(['success' => false, 'message' => 'Bu işlemi gerçekleştirmek için giriş yapmalısınız.'], 401);
        }

        if (!isset($input['ticket_id']) || !isset($input['message'])) {
            jsonResponse(['success' => false, 'message' => 'Eksik alanlar: ticket_id ve message gerekli.'], 400);
        }

        try {
            $stmt = $db_akademi->prepare("
                INSERT INTO support_replies (ticket_id, user_id, message, is_admin, created_at)
                VALUES (?, ?, ?, ?, NOW())
            ");
            $stmt->execute([
                $input['ticket_id'],
                $input['user_id'] ?? 1, // Varsayılan admin user_id
                $input['message'],
                $input['is_admin'] ?? 1 // Varsayılan admin yanıtı
            ]);

            // Talebi güncelle
            $stmt = $db_akademi->prepare("UPDATE support_tickets SET updated_at = NOW() WHERE id = ?");
            $stmt->execute([$input['ticket_id']]);

            jsonResponse(['success' => true, 'message' => 'Yanıt eklendi.']);
        } catch (PDOException $e) {
            error_log("Support API - Add reply error: " . $e->getMessage());
            jsonResponse(['success' => false, 'message' => 'Veritabanı hatası: ' . $e->getMessage()], 500);
        }
    }
}

// ==================== CATEGORIES ====================
else if ($type === 'categories') {

    if ($method === 'GET' && $action === 'list') {
        // Kategorileri Listele - GET istekleri public
        try {
            // Veritabanından mevcut kategorileri al
            $stmt = $db_akademi->query("
                SELECT
                    category,
                    COUNT(*) as ticket_count,
                    COUNT(CASE WHEN status = 'open' THEN 1 END) as open_count,
                    COUNT(CASE WHEN status = 'closed' THEN 1 END) as closed_count
                FROM 
                    support_tickets
                WHERE 
                    category IS NOT NULL AND category != ''
                GROUP BY 
                    category
                ORDER BY 
                    ticket_count DESC
            ");
            $dbCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Varsayılan kategoriler
            $defaultCategories = [
                ['value' => 'account', 'name' => 'Hesap Yönetimi', 'color' => 'blue'],
                ['value' => 'technical', 'name' => 'Teknik Sorun', 'color' => 'red'],
                ['value' => 'content', 'name' => 'İçerik', 'color' => 'orange'],
                ['value' => 'payment', 'name' => 'Ödeme', 'color' => 'green'],
                ['value' => 'other', 'name' => 'Diğer', 'color' => 'purple']
            ];

            // Veritabanında bulunan kategorileri varsayılan kategorilerle eşleştir
            $allCategories = [];
            foreach ($defaultCategories as $defaultCat) {
                $dbCat = array_filter($dbCategories, function($cat) use ($defaultCat) {
                    return $cat['category'] === $defaultCat['value'];
                });

                if (!empty($dbCat)) {
                    $dbCat = array_values($dbCat)[0];
                    $allCategories[] = [
                        'value' => $defaultCat['value'],
                        'name' => $defaultCat['name'],
                        'color' => $defaultCat['color'],
                        'ticket_count' => (int)$dbCat['ticket_count'],
                        'open_count' => (int)$dbCat['open_count'],
                        'closed_count' => (int)$dbCat['closed_count']
                    ];
                } else {
                    $allCategories[] = [
                        'value' => $defaultCat['value'],
                        'name' => $defaultCat['name'],
                        'color' => $defaultCat['color'],
                        'ticket_count' => 0,
                        'open_count' => 0,
                        'closed_count' => 0
                    ];
                }
            }

            // Veritabanında var ama varsayılanlarda olmayan kategorileri ekle
            foreach ($dbCategories as $dbCat) {
                $existsInDefault = array_filter($defaultCategories, function($defaultCat) use ($dbCat) {
                    return $defaultCat['value'] === $dbCat['category'];
                });

                if (empty($existsInDefault)) {
                    $allCategories[] = [
                        'value' => $dbCat['category'],
                        'name' => ucfirst($dbCat['category']),
                        'color' => 'default',
                        'ticket_count' => (int)$dbCat['ticket_count'],
                        'open_count' => (int)$dbCat['open_count'],
                        'closed_count' => (int)$dbCat['closed_count']
                    ];
                }
            }

            jsonResponse(['success' => true, 'data' => $allCategories]);
        } catch (PDOException $e) {
            error_log("Support API - Categories list error: " . $e->getMessage());
            jsonResponse(['success' => false, 'message' => 'Veritabanı hatası: ' . $e->getMessage()], 500);
        }
    }

    else if ($method === 'POST') {
        // Kategori İşlemleri - POST istekleri için auth gerekli
        if (!checkAuth()) {
            jsonResponse(['success' => false, 'message' => 'Bu işlemi gerçekleştirmek için giriş yapmalısınız.'], 401);
        }

        if ($action === 'add') {
            // Kategori Ekleme
            if (!isset($input['name']) || !isset($input['value'])) {
                jsonResponse(['success' => false, 'message' => 'Eksik alanlar: name ve value gerekli.'], 400);
            }

            try {
                // Kategori zaten var mı kontrol et
                $stmt = $db_akademi->prepare("SELECT COUNT(*) FROM support_tickets WHERE category = ?");
                $stmt->execute([$input['value']]);
                $exists = $stmt->fetchColumn() > 0;

                if ($exists) {
                    jsonResponse(['success' => false, 'message' => 'Bu kategori zaten mevcut.'], 400);
                }

                jsonResponse(['success' => true, 'message' => 'Kategori başarıyla eklendi.']);
            } catch (PDOException $e) {
                error_log("Support API - Add category error: " . $e->getMessage());
                jsonResponse(['success' => false, 'message' => 'Veritabanı hatası: ' . $e->getMessage()], 500);
            }
        }

        else if ($action === 'update') {
            // Kategori Güncelleme
            if (!isset($input['old_value']) || !isset($input['new_value'])) {
                jsonResponse(['success' => false, 'message' => 'Eksik alanlar: old_value ve new_value gerekli.'], 400);
        }

        try {
            $stmt = $db_akademi->prepare("UPDATE support_tickets SET category = ? WHERE category = ?");
            $stmt->execute([$input['new_value'], $input['old_value']]);

                jsonResponse(['success' => true, 'message' => 'Kategori başarıyla güncellendi.']);
        } catch (PDOException $e) {
                error_log("Support API - Update category error: " . $e->getMessage());
                jsonResponse(['success' => false, 'message' => 'Veritabanı hatası: ' . $e->getMessage()], 500);
        }
    }

        else if ($action === 'delete') {
            // Kategori Silme
        if (!isset($input['value'])) {
                jsonResponse(['success' => false, 'message' => 'Eksik alan: value gerekli.'], 400);
        }

        try {
                // Bu kategorideki talepleri 'other' kategorisine taşı
            $stmt = $db_akademi->prepare("UPDATE support_tickets SET category = 'other' WHERE category = ?");
            $stmt->execute([$input['value']]);

                jsonResponse(['success' => true, 'message' => 'Kategori silindi ve talepler diğer kategorisine taşındı.']);
        } catch (PDOException $e) {
                error_log("Support API - Delete category error: " . $e->getMessage());
                jsonResponse(['success' => false, 'message' => 'Veritabanı hatası: ' . $e->getMessage()], 500);
            }
        }

        else {
            jsonResponse(['success' => false, 'message' => 'Desteklenmeyen action parametresi: ' . $action], 400);
        }
    }
}

// Desteklenmeyen istek metodu veya type
else {
    jsonResponse(['success' => false, 'message' => 'Desteklenmeyen istek parametreleri.'], 400);
}
?>
