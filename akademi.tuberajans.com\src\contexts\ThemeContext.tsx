// src/contexts/ThemeContext.tsx
import React, { createContext, useContext, useState, useEffect } from 'react';

type ThemeContextType = {
  darkMode: boolean;
  toggleDarkMode: () => void;
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  // localStorage'dan dark mode değerini oku (veya varsayılan olarak false)
  const [darkMode, setDarkMode] = useState(() => {
    try {
      const saved = localStorage.getItem('darkMode');
      return saved ? JSON.parse(saved) : false;
    } catch (error) {
      console.error('Dark mode değeri okunurken hata oluştu:', error);
      return false;
    }
  });

  useEffect(() => {
    try {
      // localStorage'a kaydet
      localStorage.setItem('darkMode', JSON.stringify(darkMode));
      
      // DOM'u güncelle
      if (darkMode) {
        document.documentElement.classList.add('dark');
        document.body.style.backgroundColor = '#0f172a'; // Koyu arka plan
      } else {
        document.documentElement.classList.remove('dark');
        document.body.style.backgroundColor = '#f3f4f6'; // Açık arka plan
      }
    } catch (error) {
      console.error('Dark mode değeri uygulanırken hata oluştu:', error);
    }
  }, [darkMode]);

  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
  };

  // Context değerleri
  const value = {
    darkMode,
    toggleDarkMode
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
