-- TikTok Profil Analizi Veritabanı Tabloları

-- 1. TikTok Profilleri Tablosu
CREATE TABLE IF NOT EXISTS tiktok_profiles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(100) NOT NULL UNIQUE,
    nickname <PERSON><PERSON><PERSON><PERSON>(255),
    bio TEXT,
    avatar_url TEXT,
    followers_count BIGINT DEFAULT 0,
    following_count BIGINT DEFAULT 0,
    likes_count BIGINT DEFAULT 0,
    video_count INT DEFAULT 0,
    verified BOOLEAN DEFAULT FALSE,
    is_private BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_updated_at (updated_at)
);

-- 2. TikTok Profil Analiz Geçmişi
CREATE TABLE IF NOT EXISTS tiktok_profile_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    profile_id INT NOT NULL,
    followers_count BIGINT DEFAULT 0,
    following_count BIGINT DEFAULT 0,
    likes_count BIGINT DEFAULT 0,
    video_count INT DEFAULT 0,
    total_views BIGINT DEFAULT 0,
    total_engagement BIGINT DEFAULT 0,
    engagement_rate DECIMAL(5,2) DEFAULT 0,
    analysis_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (profile_id) REFERENCES tiktok_profiles(id) ON DELETE CASCADE,
    INDEX idx_profile_date (profile_id, analysis_date)
);

-- 3. TikTok Videoları Tablosu
CREATE TABLE IF NOT EXISTS tiktok_videos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    profile_id INT NOT NULL,
    video_id VARCHAR(100) NOT NULL,
    video_url TEXT,
    description TEXT,
    thumbnail_url TEXT,
    views_count BIGINT DEFAULT 0,
    likes_count BIGINT DEFAULT 0,
    comments_count BIGINT DEFAULT 0,
    shares_count BIGINT DEFAULT 0,
    saves_count BIGINT DEFAULT 0,
    engagement_rate DECIMAL(5,2) DEFAULT 0,
    published_date TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (profile_id) REFERENCES tiktok_profiles(id) ON DELETE CASCADE,
    UNIQUE KEY unique_video (profile_id, video_id),
    INDEX idx_profile_published (profile_id, published_date),
    INDEX idx_engagement (engagement_rate DESC)
);

-- 4. TikTok Hashtag'ler Tablosu
CREATE TABLE IF NOT EXISTS tiktok_hashtags (
    id INT AUTO_INCREMENT PRIMARY KEY,
    hashtag VARCHAR(100) NOT NULL UNIQUE,
    usage_count INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_hashtag (hashtag),
    INDEX idx_usage_count (usage_count DESC)
);

-- 5. Video-Hashtag İlişki Tablosu
CREATE TABLE IF NOT EXISTS tiktok_video_hashtags (
    id INT AUTO_INCREMENT PRIMARY KEY,
    video_id INT NOT NULL,
    hashtag_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (video_id) REFERENCES tiktok_videos(id) ON DELETE CASCADE,
    FOREIGN KEY (hashtag_id) REFERENCES tiktok_hashtags(id) ON DELETE CASCADE,
    UNIQUE KEY unique_video_hashtag (video_id, hashtag_id)
);

-- 6. Analiz İstekleri Tablosu (Rate limiting ve tracking için)
CREATE TABLE IF NOT EXISTS tiktok_analysis_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(100) NOT NULL,
    ip_address VARCHAR(45),
    user_id INT NULL,
    status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    error_message TEXT NULL,
    processing_time INT NULL, -- saniye cinsinden
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    INDEX idx_username_date (username, created_at),
    INDEX idx_ip_date (ip_address, created_at),
    INDEX idx_status (status)
);

-- 7. TikTok API Kullanım İstatistikleri
CREATE TABLE IF NOT EXISTS tiktok_api_usage (
    id INT AUTO_INCREMENT PRIMARY KEY,
    api_endpoint VARCHAR(255),
    request_count INT DEFAULT 1,
    success_count INT DEFAULT 0,
    error_count INT DEFAULT 0,
    avg_response_time DECIMAL(8,2) DEFAULT 0,
    date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_endpoint_date (api_endpoint, date),
    INDEX idx_date (date)
);

-- 8. Profil Büyüme Analizi View'i
CREATE OR REPLACE VIEW tiktok_profile_growth AS
SELECT 
    p.id,
    p.username,
    p.nickname,
    p.followers_count as current_followers,
    p.likes_count as current_likes,
    LAG(h.followers_count) OVER (PARTITION BY p.id ORDER BY h.analysis_date) as prev_followers,
    LAG(h.likes_count) OVER (PARTITION BY p.id ORDER BY h.analysis_date) as prev_likes,
    h.analysis_date,
    CASE 
        WHEN LAG(h.followers_count) OVER (PARTITION BY p.id ORDER BY h.analysis_date) > 0 
        THEN ROUND(((h.followers_count - LAG(h.followers_count) OVER (PARTITION BY p.id ORDER BY h.analysis_date)) / LAG(h.followers_count) OVER (PARTITION BY p.id ORDER BY h.analysis_date)) * 100, 2)
        ELSE 0 
    END as follower_growth_rate,
    CASE 
        WHEN LAG(h.likes_count) OVER (PARTITION BY p.id ORDER BY h.analysis_date) > 0 
        THEN ROUND(((h.likes_count - LAG(h.likes_count) OVER (PARTITION BY p.id ORDER BY h.analysis_date)) / LAG(h.likes_count) OVER (PARTITION BY p.id ORDER BY h.analysis_date)) * 100, 2)
        ELSE 0 
    END as likes_growth_rate
FROM tiktok_profiles p
JOIN tiktok_profile_history h ON p.id = h.profile_id
ORDER BY p.id, h.analysis_date;

-- 9. En Popüler Hashtag'ler View'i
CREATE OR REPLACE VIEW popular_hashtags AS
SELECT 
    h.hashtag,
    h.usage_count,
    COUNT(DISTINCT vh.video_id) as video_count,
    AVG(v.engagement_rate) as avg_engagement_rate,
    SUM(v.views_count) as total_views
FROM tiktok_hashtags h
JOIN tiktok_video_hashtags vh ON h.id = vh.hashtag_id
JOIN tiktok_videos v ON vh.video_id = v.id
GROUP BY h.id, h.hashtag, h.usage_count
ORDER BY h.usage_count DESC, avg_engagement_rate DESC;

-- 10. Profil Performans Özeti View'i
CREATE OR REPLACE VIEW tiktok_profile_summary AS
SELECT 
    p.id,
    p.username,
    p.nickname,
    p.followers_count,
    p.following_count,
    p.likes_count,
    COUNT(v.id) as total_videos,
    COALESCE(SUM(v.views_count), 0) as total_views,
    COALESCE(SUM(v.likes_count), 0) as total_video_likes,
    COALESCE(SUM(v.comments_count), 0) as total_comments,
    COALESCE(SUM(v.shares_count), 0) as total_shares,
    COALESCE(AVG(v.engagement_rate), 0) as avg_engagement_rate,
    COALESCE(AVG(v.views_count), 0) as avg_views_per_video,
    MAX(v.published_date) as last_video_date,
    p.updated_at as last_analysis_date
FROM tiktok_profiles p
LEFT JOIN tiktok_videos v ON p.id = v.profile_id
GROUP BY p.id, p.username, p.nickname, p.followers_count, p.following_count, p.likes_count, p.updated_at;
