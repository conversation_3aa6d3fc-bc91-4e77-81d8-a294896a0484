<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/auth.php';

header('Content-Type: application/json; charset=utf-8');

$userId = requireAuthToken();
if (!$userId) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$eventId = $_GET['eventId'] ?? null;
if (!$eventId) {
    echo json_encode([]);
    exit;
}

// PK eşleşmeleri tablosundan, kullanıcı adlarıyla birlikte çekiyoruz
$sql = "SELECT 
    pk.*, 
    y1.username AS yayinci1_username_full, 
    y2.username AS yayinci2_username_full, 
    kazanan.username AS kazanan_username_full
FROM pk_eslesmeleri pk
LEFT JOIN publisher_info y1 ON pk.yayinci1_username = y1.username
LEFT JOIN publisher_info y2 ON pk.yayinci2_username = y2.username
LEFT JOIN publisher_info kazanan ON pk.kazanan_username = kazanan.username
WHERE pk.etkinlik_id = ?";
$stmt = $db->prepare($sql);
$stmt->execute([$eventId]);
$matches = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo json_encode($matches);
exit; 