{"name": "akademi-tuberajans", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "typecheck": "tsc --noEmit"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@heroicons/react": "^2.0.18", "axios": "^1.6.2", "framer-motion": "^10.16.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "react-icons": "^4.12.0", "react-router-dom": "^6.20.0"}, "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^5.0.0"}}