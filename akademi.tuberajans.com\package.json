{"name": "akademi-tuberajans", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.3.4", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-toast": "^1.2.13", "axios": "^1.6.7", "clsx": "^2.1.1", "emoji-picker-react": "^4.12.2", "framer-motion": "^11.0.8", "lucide-react": "^0.344.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.51.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.0.1", "react-router-dom": "^6.22.2", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.11.24", "@types/react": "^18.2.61", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.3.3", "vite": "^5.1.4"}}