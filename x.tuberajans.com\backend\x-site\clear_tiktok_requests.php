<?php
/**
 * TikTok isteklerini temizleme scripti
 */

require_once '../config/config.php';

try {
    // social_media_analytics veritabanına bağlan
    $db_social = new PDO("mysql:host=**************;dbname=social_media_analytics;charset=utf8mb4", 'root', 'Bebek845396!');
    $db_social->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $db_social->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

    echo "🔗 Veritabanına bağlanıldı\n\n";

    // Mevcut istekleri göster
    echo "📊 Mevcut İstekler:\n";
    echo "==================\n";

    $stmt = $db_social->prepare("SELECT id, username, status, created_at, current_step FROM tiktok_requests ORDER BY created_at DESC LIMIT 20");
    $stmt->execute();
    $requests = $stmt->fetchAll();

    if (empty($requests)) {
        echo "✅ Hiç istek bulunamadı\n";
    } else {
        foreach ($requests as $request) {
            echo "ID: {$request['id']} | @{$request['username']} | {$request['status']} | {$request['created_at']} | {$request['current_step']}\n";
        }
    }

    echo "\n";

    // İstatistikler
    $stmt = $db_social->prepare("SELECT status, COUNT(*) as count FROM tiktok_requests GROUP BY status");
    $stmt->execute();
    $stats = $stmt->fetchAll();

    echo "📈 İstek İstatistikleri:\n";
    echo "=======================\n";
    foreach ($stats as $stat) {
        echo "{$stat['status']}: {$stat['count']} adet\n";
    }

    echo "\n";

    // VDS durumunu kontrol et
    $stmt = $db_social->prepare("SELECT * FROM tiktok_vds_status");
    $stmt->execute();
    $vds_status = $stmt->fetchAll();

    echo "🖥️ VDS Durumu:\n";
    echo "===============\n";
    if (empty($vds_status)) {
        echo "❌ Hiç VDS bulunamadı\n";
    } else {
        foreach ($vds_status as $vds) {
            echo "VDS: {$vds['vds_name']} | Status: {$vds['status']} | Son Heartbeat: {$vds['last_heartbeat']}\n";
        }
    }

    echo "\n";

    // Otomatik temizlik yap
    echo "🧹 Otomatik Temizlik Başlatılıyor...\n";
    echo "====================================\n";

    // 1. Eski istekleri sil (24 saatten eski)
    $stmt = $db_social->prepare("DELETE FROM tiktok_requests WHERE created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    $stmt->execute();
    $deleted_old = $stmt->rowCount();
    echo "✅ {$deleted_old} adet eski istek silindi (24 saatten eski)\n";

    // 2. Takılı kalmış processing istekleri sil (30 dakikadan eski)
    $stmt = $db_social->prepare("DELETE FROM tiktok_requests WHERE status = 'processing' AND updated_at < DATE_SUB(NOW(), INTERVAL 30 MINUTE)");
    $stmt->execute();
    $deleted_stuck = $stmt->rowCount();
    echo "✅ {$deleted_stuck} adet takılı kalmış istek silindi (30 dakikadan eski processing)\n";

    // 3. Başarısız istekleri sil
    $stmt = $db_social->prepare("DELETE FROM tiktok_requests WHERE status = 'failed'");
    $stmt->execute();
    $deleted_failed = $stmt->rowCount();
    echo "✅ {$deleted_failed} adet başarısız istek silindi\n";

    // 4. VDS durumunu sıfırla
    $stmt = $db_social->prepare("UPDATE tiktok_vds_status SET status = 'idle', current_request_id = NULL WHERE status != 'offline'");
    $stmt->execute();
    $updated_vds = $stmt->rowCount();
    echo "✅ {$updated_vds} adet VDS durumu sıfırlandı\n";

    // 5. Kalan istekleri göster
    echo "\n📊 Temizlik Sonrası Durum:\n";
    echo "==========================\n";

    $stmt = $db_social->prepare("SELECT status, COUNT(*) as count FROM tiktok_requests GROUP BY status");
    $stmt->execute();
    $final_stats = $stmt->fetchAll();

    if (empty($final_stats)) {
        echo "✅ Hiç istek kalmadı - sistem temiz!\n";
    } else {
        foreach ($final_stats as $stat) {
            echo "{$stat['status']}: {$stat['count']} adet\n";
        }
    }

    $total_deleted = $deleted_old + $deleted_stuck + $deleted_failed;
    echo "\n🎯 Toplam {$total_deleted} istek temizlendi!\n";

    echo "\n🎉 İşlem tamamlandı!\n";

} catch (Exception $e) {
    echo "❌ Hata: " . $e->getMessage() . "\n";
}
?>
