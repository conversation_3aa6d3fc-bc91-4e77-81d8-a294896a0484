<?php
// Test başvuru ekleme
require_once __DIR__ . '/../config/config.php';

// Test verisi
$testData = [
    'name' => 'Test Yayıncı',
    'surname' => 'Test Soyad',
    'phone' => '05551234567',
    'mail' => '<EMAIL>',
    'tiktok_username' => 'testyayinci',
    'instagram_username' => 'testyayinci_ig',
    'yayin_suresi' => 'Haftada 3-4 gün',
    'follower_range' => '10K-50K',
    'deneyim' => 'Test deneyim açıklaması',
    'approve' => 1,
    'approveTic' => 0,
    'ip' => '127.0.0.1',
    'unixts' => time(),
    'isRead' => 0,
    'isAcademy' => 0,
    'isFinal' => 0,
    'isReject' => 0,
    'smsRed' => 0
];

try {
    $pdo = $db_takip;
    
    echo "🧪 Test başvuru ekleniyor...\n";
    
    // Veritabanına ekle
    $stmt = $pdo->prepare("
        INSERT INTO sitebasvurular 
        (name, surname, phone, mail, tiktok_username, instagram_username, yayin_suresi, 
         follower_range, deneyim, approve, approveTic, ip, unixts, isRead, isAcademy, 
         isFinal, isReject, smsRed, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
    ");
    
    $stmt->execute([
        $testData['name'], $testData['surname'], $testData['phone'], $testData['mail'], 
        $testData['tiktok_username'], $testData['instagram_username'],
        $testData['yayin_suresi'], $testData['follower_range'], $testData['deneyim'], 
        $testData['approve'], $testData['approveTic'], $testData['ip'],
        $testData['unixts'], $testData['isRead'], $testData['isAcademy'], 
        $testData['isFinal'], $testData['isReject'], $testData['smsRed']
    ]);
    
    $newId = $pdo->lastInsertId();
    
    echo "✅ Test başvuru başarıyla eklendi! ID: $newId\n";
    
    // Eklenen kaydı kontrol et
    $stmt = $pdo->prepare("SELECT * FROM sitebasvurular WHERE id = ?");
    $stmt->execute([$newId]);
    $record = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "\n📋 Eklenen kayıt:\n";
    foreach ($record as $key => $value) {
        echo "- $key: $value\n";
    }
    
    // Toplam kayıt sayısını göster
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM sitebasvurular");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "\n📊 Yeni toplam kayıt sayısı: $count\n";
    
} catch (Exception $e) {
    echo "❌ Hata: " . $e->getMessage() . "\n";
}
?>
