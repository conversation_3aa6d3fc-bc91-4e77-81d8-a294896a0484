# TikTok VDS İşleyicisi

Bu klasör VDS sunucusunda çalışacak TikTok profil analizi işleyicisini içerir.

## 📁 Dosyalar

- `vds-tiktok-processor.py` - Ana Python script'i
- `config.py` - Konfigürasyon dosyası (düzenlenmeli!)
- `requirements.txt` - <PERSON> paket gereksinimleri
- `start.sh` - <PERSON> ba<PERSON> script'i
- `install.sh` - <PERSON><PERSON>ati<PERSON> kurulum script'i
- `README.md` - Bu dosya

## 🚀 H<PERSON>zl<PERSON> Kurulum (Önerilen)

### 1. Dosyaları VDS'ye Kopyala
```bash
# Tüm klasörü VDS masaüstüne kopyala
scp -r tiktok-vds-processor/ user@vds-ip:~/Desktop/
```

### 2. VDS'de Otomatik Kurulum
```bash
cd ~/Desktop/tiktok-vds-processor/
sudo bash install.sh
```

### 3. Konfigürasyonu Düzenle
```bash
sudo nano /home/<USER>/config.py
```

**Düzenlenmesi gereken değerler:**
- `WEB_SITESI_SUNUCU_IP` → Web sitesi sunucusunun IP adresi
- `VERITABANI_SIFRESI` → MySQL root şifresi

### 4. Servisi Başlat
```bash
sudo systemctl start tiktok-analyzer
sudo systemctl status tiktok-analyzer
```

## 🔧 Manuel Kurulum

### 1. Sistem Gereksinimleri
```bash
# Ubuntu/Debian için
sudo apt update
sudo apt install python3 python3-pip google-chrome-stable

# Python paketleri
pip3 install -r requirements.txt
```

### 2. ChromeDriver İndir
```bash
# Chrome versiyonunu kontrol et
google-chrome --version

# Uygun ChromeDriver'ı indir
wget https://chromedriver.storage.googleapis.com/LATEST_RELEASE
# ... (detaylar install.sh'de)
```

### 3. Konfigürasyon
`config.py` dosyasını düzenle:
```python
DB_CONFIG = {
    'host': '*************',  # Web sitesi IP'si
    'database': 'social_media_analytics',
    'user': 'root',
    'password': 'your_password',  # MySQL şifresi
    'charset': 'utf8mb4',
    'port': 3306
}
```

### 4. Çalıştır
```bash
python3 vds-tiktok-processor.py
```

## 📊 Monitoring

### Log Dosyası
```bash
tail -f /home/<USER>/logs/tiktok_processor.log
```

### Servis Durumu
```bash
sudo systemctl status tiktok-analyzer
sudo journalctl -u tiktok-analyzer -f
```

### VDS Durumu (Veritabanından)
```sql
SELECT * FROM vds_status ORDER BY last_ping DESC;
```

## 🔄 Nasıl Çalışır

1. **Veritabanı Kontrolü**: Script sürekli `tiktok_requests` tablosunu kontrol eder
2. **İstek Alma**: `pending` durumundaki istekleri alır
3. **Selenium**: Chrome ile TikTok profiline gider
4. **Veri Çekme**: Profil bilgileri ve video verilerini çeker
5. **Kaydetme**: Sonuçları `tiktok_analysis` tablosuna kaydeder
6. **Durum Güncelleme**: İstek durumunu `completed` yapar

## 🛠️ Sorun Giderme

### ChromeDriver Hatası
```bash
# ChromeDriver'ı yeniden indir
cd /home/<USER>/
rm chromedriver
bash start.sh  # Otomatik indirir
```

### Veritabanı Bağlantı Hatası
```bash
# Bağlantıyı test et
mysql -h WEB_SITESI_IP -u root -p social_media_analytics
```

### Selenium Hatası
```bash
# Chrome'u güncelle
sudo apt update && sudo apt upgrade google-chrome-stable
```

## 📈 Performans

- **İşlem Süresi**: Profil başına ~30-60 saniye
- **Bellek Kullanımı**: ~200-500 MB
- **CPU Kullanımı**: Orta seviye
- **Ağ Trafiği**: Video başına ~1-2 MB

## 🔒 Güvenlik

- Script headless modda çalışır (görünmez)
- Anti-detection teknikleri kullanır
- Rate limiting ile TikTok'u yormaz
- Hata durumunda otomatik yeniden başlar

## 📞 Destek

Sorun yaşarsanız:
1. Log dosyasını kontrol edin
2. Servis durumunu kontrol edin  
3. Veritabanı bağlantısını test edin
4. ChromeDriver versiyonunu kontrol edin
