import{u as s,j as e}from"./index-DYSQ_-oc.js";const t=()=>{const{darkMode:a}=s();return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex justify-between items-center",children:e.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Görev İlerleme Ta<PERSON>bi"})}),e.jsx("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg p-6",children:e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Görev ilerleme takibi özelliği yakında eklenecektir."})})]})};export{t as default};
