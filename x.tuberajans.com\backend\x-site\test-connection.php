<?php
// Test dosyası - Sunucu bağlantısını kontrol et
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// OPTIONS isteği için
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Veritabanı bağlantısını test et
require_once __DIR__ . '/../config/config.php';

$db_status = 'Bağlantı yok';
if (isset($db_takip) && $db_takip) {
    try {
        $stmt = $db_takip->query("SELECT 1");
        $db_status = 'Bağlantı başarılı';
    } catch (Exception $e) {
        $db_status = 'Bağlantı hatası: ' . $e->getMessage();
    }
}

echo json_encode([
    'status' => 'success',
    'message' => 'Sunucu çalışıyor!',
    'server_ip' => $_SERVER['SERVER_ADDR'] ?? 'Bilinmiyor',
    'client_ip' => $_SERVER['REMOTE_ADDR'] ?? 'Bilinmiyor',
    'timestamp' => date('Y-m-d H:i:s'),
    'server_name' => $_SERVER['SERVER_NAME'] ?? 'Bilinmiyor',
    'database_status' => $db_status,
    'php_version' => PHP_VERSION
]);
?>
