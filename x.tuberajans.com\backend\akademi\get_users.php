<?php
// Akademi kullanıcılarını listeleyen endpoint

require_once __DIR__ . '/../config/config.php';

// CORS ayarları
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

header('Content-Type: application/json');

try {
    // Filtreleme için koşullar ve parametreler
    $conditions = [];
    $params = [];

    if (!empty($_GET['role'])) {
        $conditions[] = 'role = ?';
        $params[] = $_GET['role'];
    }
    if (!empty($_GET['status'])) {
        $conditions[] = 'status = ?';
        $params[] = $_GET['status'];
    }
    if (!empty($_GET['search'])) {
        $conditions[] = '(username LIKE ? OR email LIKE ?)';
        $params[] = '%' . $_GET['search'] . '%';
        $params[] = '%' . $_GET['search'] . '%';
    }

    $query = "SELECT id, username, email, role, status, created_at, updated_at FROM users";
    if ($conditions) {
        $query .= " WHERE " . implode(' AND ', $conditions);
    }
    $query .= " ORDER BY created_at DESC, id DESC";
    $stmt = $db->prepare($query);
    $stmt->execute($params);
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $total = count($users);
    echo json_encode([
        'success' => true,
        'data' => $users,
        'pagination' => [
            'page' => 1,
            'limit' => $total,
            'total' => $total,
            'total_pages' => 1
        ]
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Kullanıcılar alınamadı', 'detail' => $e->getMessage()]);
}