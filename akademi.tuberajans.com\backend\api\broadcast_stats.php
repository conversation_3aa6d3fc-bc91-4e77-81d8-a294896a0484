<?php
require_once dirname(__DIR__) . '/config/config.php';

if (!isset($conn)) {
    die(json_encode([
        'status' => 'error',
        'message' => 'Database connection not available'
    ]));
}

// CORS başlıkları
header("Access-Control-Allow-Origin: https://akademi.tuberajans.com");
header("Access-Control-Allow-Credentials: true");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
header("Content-Type: application/json; charset=utf-8");

// OPTIONS isteği kontrolü
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Sadece GET isteklerini kabul et
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'status' => 'error',
        'message' => 'Method not allowed. Only GET requests are accepted.'
    ]);
    exit;
}

try {
    if (!isset($auth)) {
        $auth = new Auth($conn);
    }
    
    $currentUser = $auth->getCurrentUser();

    if (!$currentUser) {
        http_response_code(401);
        echo json_encode([
            'status' => 'error',
            'message' => 'Unauthorized access'
        ]);
        exit;
    }

    // TikTok bağlantısını kontrol et
    if (empty($currentUser['tiktok_open_id'])) {
        http_response_code(400);
        echo json_encode([
            'status' => 'error',
            'message' => 'No TikTok account linked'
        ]);
        exit;
    }

    // İstatistikleri getir
    $stmt = $conn->prepare("
        SELECT 
            broadcast_id,
            start_time,
            end_time,
            total_viewers,
            new_followers,
            likes_count,
            total_comments,
            diamonds_count,
            top_viewer_count,
            created_at
        FROM broadcast_logs
        WHERE user_id = ?
        ORDER BY start_time DESC
        LIMIT 10
    ");

    $stmt->bind_param('s', $currentUser['id']);
    
    if (!$stmt->execute()) {
        throw new Exception('Failed to fetch broadcast stats');
    }

    $result = $stmt->get_result();
    $stats = [];

    while ($row = $result->fetch_assoc()) {
        $stats[] = [
            'broadcast_id' => $row['broadcast_id'],
            'start_time' => $row['start_time'],
            'end_time' => $row['end_time'],
            'duration' => strtotime($row['end_time']) - strtotime($row['start_time']),
            'total_viewers' => (int)$row['total_viewers'],
            'new_followers' => (int)$row['new_followers'],
            'likes_count' => (int)$row['likes_count'],
            'total_comments' => (int)$row['total_comments'],
            'diamonds_count' => (int)$row['diamonds_count'],
            'top_viewer_count' => (int)$row['top_viewer_count'],
            'created_at' => $row['created_at']
        ];
    }

    // Toplam istatistikleri hesapla
    $totalStats = [
        'total_broadcasts' => count($stats),
        'total_viewers' => 0,
        'total_new_followers' => 0,
        'total_likes' => 0,
        'total_comments' => 0,
        'total_diamonds' => 0,
        'total_duration' => 0,
        'average_viewers' => 0,
        'average_duration' => 0
    ];

    foreach ($stats as $stat) {
        $totalStats['total_viewers'] += $stat['total_viewers'];
        $totalStats['total_new_followers'] += $stat['new_followers'];
        $totalStats['total_likes'] += $stat['likes_count'];
        $totalStats['total_comments'] += $stat['total_comments'];
        $totalStats['total_diamonds'] += $stat['diamonds_count'];
        $totalStats['total_duration'] += $stat['duration'];
    }

    if (count($stats) > 0) {
        $totalStats['average_viewers'] = round($totalStats['total_viewers'] / count($stats));
        $totalStats['average_duration'] = round($totalStats['total_duration'] / count($stats));
    }

    echo json_encode([
        'status' => 'success',
        'stats' => $stats,
        'total_stats' => $totalStats
    ]);

} catch (Exception $e) {
    error_log('Broadcast stats error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Internal server error',
        'debug' => $e->getMessage()
    ]);
} 