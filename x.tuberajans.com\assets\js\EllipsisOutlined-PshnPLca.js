import{r as a}from"./vendor-CnpYymF8.js";import{af as e}from"./antd-gS---Efz.js";import{I as i}from"./App-DhIV03Gw.js";function s(){return s=Object.assign?Object.assign.bind():function(r){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(r[o]=n[o])}return r},s.apply(this,arguments)}const f=(r,t)=>a.createElement(i,s({},r,{ref:t,icon:e})),m=a.forwardRef(f);export{m as R};
