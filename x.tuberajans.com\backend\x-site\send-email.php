<?php
// SendGrid Bulk Email API
ini_set('display_errors', 0);
error_reporting(E_ALL);
header('Content-Type: application/json; charset=utf-8');

require_once __DIR__ . '/../config/config.php';

// Get SendGrid API key from environment or config
$sendgridApiKey = getenv('SENDGRID_API_KEY') ?: (defined('SENDGRID_API_KEY') ? SENDGRID_API_KEY : null);
if (!$sendgridApiKey) {
    jsonResponse(['success' => false, 'error' => 'SendGrid API anahtarı yapılandırılmadı'], 500);
}

// Read input
$input = json_decode(file_get_contents('php://input'), true);
if (!$input || !isset($input['emails'], $input['subject'], $input['content'])) {
    jsonResponse(['success' => false, 'error' => 'Eksik parametre: emails, subject, content gerekli'], 400);
}

$emails = (array) $input['emails'];
$subject = trim($input['subject']);
$content = $input['content'];
$attachments = isset($input['attachments']) ? (array) $input['attachments'] : [];

// Build payload
$personalizations = [];
$toList = [];
foreach ($emails as $email) {
    $toList[] = ['email' => $email];
}
$personalizations[] = ['to' => $toList];

$payload = [
    'personalizations' => $personalizations,
    'from' => ['email' => '<EMAIL>', 'name' => 'Tuber Ajans'],
    'subject' => $subject,
    'content' => [['type' => 'text/html', 'value' => $content]]
];

// Add attachments if present
if (!empty($attachments)) {
    $payload['attachments'] = [];
    
    foreach ($attachments as $attachment) {
        if (isset($attachment['filename'], $attachment['content'], $attachment['contentType'])) {
            $payload['attachments'][] = [
                'filename' => $attachment['filename'],
                'content' => $attachment['content'],
                'type' => $attachment['contentType'],
                'disposition' => 'attachment'
            ];
        }
    }
}

// Send request
$ch = curl_init('https://api.sendgrid.com/v3/mail/send');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer ' . $sendgridApiKey,
    'Content-Type: application/json'
]);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
curl_close($ch);

if ($response === false || $httpCode >= 400) {
    $errorMsg = $curlError ?: "HTTP $httpCode";
    jsonResponse(['success' => false, 'error' => 'SendGrid hatası: ' . $errorMsg], 500);
}

jsonResponse(['success' => true]);
?> 