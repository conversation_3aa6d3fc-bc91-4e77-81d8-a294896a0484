<?php
header('Content-Type: application/json; charset=utf-8');

// POST isteğini kontrol et
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_SERVER['PATH_INFO']) && $_SERVER['PATH_INFO'] === '/mark-read') {
    // POST verilerini al
    $data = json_decode(file_get_contents('php://input'), true);
    
    if (!$data || !isset($data['messageIds']) || !is_array($data['messageIds']) || empty($data['messageIds'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Geçersiz istek formatı.']);
        exit;
    }
    
    $messageIds = $data['messageIds'];
    $readLog = __DIR__ . '/whatsapp_read_messages.txt';
    
    // Mesaj ID'lerini kaydet
    $current = file_exists($readLog) ? file_get_contents($readLog) : '';
    $file = fopen($readLog, 'a');
    
    foreach ($messageIds as $msgId) {
        // ID'nin zaten kaydedilmediğinden emin ol
        if (strpos($current, $msgId) === false) {
            fwrite($file, $msgId . "\n");
        }
    }
    
    fclose($file);
    
    echo json_encode(['success' => true]);
    exit;
}

// GET isteği için mesajları getir
$contactId = $_GET['contactId'] ?? '';
$inLog = __DIR__ . '/whatsapp_incoming_log.txt';
$outLog = __DIR__ . '/whatsapp_outgoing_log.txt';
$readLog = __DIR__ . '/whatsapp_read_messages.txt';
$inLines = file_exists($inLog) ? file($inLog) : [];
$outLines = file_exists($outLog) ? file($outLog) : [];
$readIds = [];

// Okunan mesaj ID'lerini yükle
if (file_exists($readLog)) {
    $readLines = file($readLog);
    foreach ($readLines as $line) {
        $readIds[] = trim($line);
    }
}

$messages = [];
foreach ($inLines as $line) {
    $json = trim(substr($line, 27));
    $data = json_decode($json, true);
    // Sadece gerçek mesajları ekle
    if ($data && isset($data['entry'][0]['changes'][0]['value']['messages'][0])) {
        $msgData = $data['entry'][0]['changes'][0]['value']['messages'][0];
        $from = $msgData['from'] ?? null;
        if ($contactId && $from !== $contactId) continue;
        $msgId = $msgData['id'] ?? uniqid('in_');
        $msg = [
            'id' => $msgId,
            'from' => $from,
            'timestamp' => isset($msgData['timestamp']) ? intval($msgData['timestamp']) : time(),
            'text' => $msgData['text'] ?? '',
            'type' => $msgData['type'] ?? '',
            'fromMe' => false,
            'read' => in_array($msgId, $readIds)
        ];
        $messages[] = $msg;
    }
}
foreach ($outLines as $line) {
    $json = trim(substr($line, 27));
    $data = json_decode($json, true);
    if ($data && isset($data['to'])) {
        $to = ltrim($data['to'], '+');
        $contactIdClean = ltrim($contactId, '+');
        if ($contactId && $to !== $contactIdClean) continue;
        $msgId = uniqid('out_');
        $msg = [
            'id' => $msgId,
            'fromMe' => true,
            'timestamp' => isset($data['timestamp']) ? intval($data['timestamp']) : time(),
            'text' => is_array($data['text']) && isset($data['text']['body']) ? $data['text']['body'] : ($data['text'] ?? $data['message'] ?? ''),
            'status' => 'sent',
            'read' => true // Gönderilen mesajlar otomatik olarak okunmuş kabul edilir
        ];
        $messages[] = $msg;
    }
}
// Tarihe göre sırala
usort($messages, function($a, $b) { return ($a['timestamp'] ?? 0) - ($b['timestamp'] ?? 0); });
echo json_encode($messages); 