import{j as e}from"./reactDnd-CIvPAkL_.js";import{u as a}from"./vendor-CnpYymF8.js";import{i as r}from"./antd-gS---Efz.js";const n=()=>{const t=a();return e.jsxs("div",{className:"flex flex-col items-center justify-center h-screen p-4",children:[e.jsx("h1",{className:"text-4xl font-bold mb-4 text-gray-800 dark:text-white",children:"<PERSON><PERSON>iz Erişim"}),e.jsx("p",{className:"text-lg mb-6 text-gray-700 dark:text-gray-300",children:"<PERSON><PERSON> sayfayı görüntüleme yetkiniz bulunmamaktadır."}),e.jsx(r,{type:"primary",onClick:()=>t("/"),size:"large",className:"bg-blue-600 hover:bg-blue-700 text-white border-0 shadow-md",style:{minWidth:"180px",height:"44px"},children:"Ana Sayfaya Dön"})]})};export{n as default};
