import{j as e}from"./reactDnd-CIvPAkL_.js";import{r as c,u as ti}from"./vendor-CnpYymF8.js";import{a5 as ni,m as b,G as p,v as y,y as W,z as Y,l as R,k as D,u as si,T as ze,s as Te,x as z,i as x,Z as I,A as ie,_ as ri,N as li,q as ae,I as se,K as be,j as oi,Y as ci,h as ke,o as mi}from"./antd-gS---Efz.js";import"./tr-CwGFhkM0.js";import{I as di,w as K,x as je,R as ui,l as ve,y as yi,z as pi,i as hi,h as Me,r as fi,A as gi,o as xi,p as bi,q as ki,v as ji}from"./App-DhIV03Gw.js";import{c as vi}from"./event-api-D6vrs219.js";import{R as _e}from"./FireOutlined-C-IULXbJ.js";import{a as we,R as Mi}from"./SaveOutlined-bQAn9DYL.js";import"./index-CVO3aNyS.js";import"./utils-CtuI0RRe.js";import"./charts-CXWFy-zF.js";function re(){return re=Object.assign?Object.assign.bind():function(k){for(var C=1;C<arguments.length;C++){var j=arguments[C];for(var P in j)Object.prototype.hasOwnProperty.call(j,P)&&(k[P]=j[P])}return k},re.apply(this,arguments)}const _i=(k,C)=>c.createElement(di,re({},k,{ref:C,icon:ni})),wi=c.forwardRef(_i);p.locale("tr");const{Option:Si}=Te,{Title:te,Text:U}=ze,{TextArea:ne}=se,Se="https://x.tuberajans.com",zi=()=>{const[k,C]=c.useState([]),[j,P]=c.useState([]),[G,E]=c.useState(!1),[le,oe]=c.useState(!1),[Ye,ce]=c.useState([]),[Ce,L]=c.useState(!1),[me]=b.useForm(),[u,F]=c.useState([]),[T,De]=c.useState(!0),[q,V]=c.useState([]),[Ti,Yi]=c.useState(()=>localStorage.getItem("pkMatcherInfoAlertClosed")!=="true"),[Ci,Di]=c.useState(!1),[Ei,$i]=c.useState({}),[Ii,Ee]=c.useState(!1),[Pi,$e]=c.useState(""),[Ni,Ie]=c.useState([]),[Pe,J]=c.useState(!1),[Z,N]=c.useState(!1),[A,de]=c.useState(!1),[M,Q]=c.useState([]),[X,ue]=c.useState(!1),Ne=ti(),[Ae,ye]=c.useState([]),pe=/[^\p{L}\p{N}\s.,!?:;*@#%&()\-=+\n\p{Emoji_Presentation}]/gu;c.useEffect(()=>{he()},[]),c.useEffect(()=>{Be()},[]);const Be=async()=>{try{let i=localStorage.getItem("x_tuber_token")||"";if(!i){const n=localStorage.getItem("x_tuber_user");if(n){const s=JSON.parse(n);s&&s.token&&(i=s.token)}}if(!i){K.error("Oturum bilgileriniz bulunamadı. Lütfen tekrar giriş yapın."),setTimeout(()=>{window.location.href="/login"},2e3);return}const a=await(await fetch("/backend/x-site/publisher_info.php?action=active_publishers",{headers:{Authorization:`Bearer ${i}`}})).json();a.success&&Array.isArray(a.data)&&ye(a.data.map(n=>n.trim().toLowerCase()))}catch(i){ye([])}};c.useEffect(()=>{Q(Z?[...q]:[])},[Z,q]);const he=async()=>{E(!0);try{if(window.location.hostname==="localhost"){const s=[{id:"test1",username:"mertcemusic",isim_soyisim:"Mert Ce",yeni_takipciler:11,elmaslar:131710,yayin_suresi:7454,canli_yayin_gunu:1,son_yayin_tarihi:p().subtract(2,"day").format("YYYY-MM-DD"),aboneler:0,maclar:0,telefon:"+905551234567",mail:"<EMAIL>",profile_image:null},{id:"test2",username:"richailesioffical",isim_soyisim:"Rich Ailesi",yeni_takipciler:136,elmaslar:25875,yayin_suresi:149303,canli_yayin_gunu:8,son_yayin_tarihi:p().subtract(1,"day").format("YYYY-MM-DD"),aboneler:9,maclar:150,telefon:"+905557654321",mail:"<EMAIL>",profile_image:null},{id:"test3",username:"seelliiins",isim_soyisim:"Selin",yeni_takipciler:92,elmaslar:16654,yayin_suresi:43760,canli_yayin_gunu:4,son_yayin_tarihi:p().subtract(1,"day").format("YYYY-MM-DD"),aboneler:0,maclar:46,telefon:"+905559876543",mail:"<EMAIL>",profile_image:null},{id:"test4",username:"fulyatemir_",isim_soyisim:"Fulya Temir",yeni_takipciler:45,elmaslar:16213,yayin_suresi:55214,canli_yayin_gunu:6,son_yayin_tarihi:p().subtract(3,"day").format("YYYY-MM-DD"),aboneler:5,maclar:54,telefon:"+905553456789",mail:"<EMAIL>",profile_image:null}];C(s),E(!1);return}let t=localStorage.getItem("x_tuber_token")||"";if(!t){const s=localStorage.getItem("x_tuber_user");if(s){const r=JSON.parse(s);r&&r.token&&(t=r.token)}}if(!t){K.error("Oturum bilgileriniz bulunamadı. Lütfen tekrar giriş yapın."),setTimeout(()=>{window.location.href="/login"},2e3);return}const n=await(await fetch("/backend/x-site/publishers.php",{headers:{Authorization:`Bearer ${t}`}})).json();C(Array.isArray(n.data)?n.data:[])}catch(i){y.error("Yayıncı verileri alınamadı")}finally{E(!1)}},He=async()=>{const i=k.filter(t=>Number(t.canli_yayin_gunu)>0||Number(t.yayin_suresi)>0);E(!0);try{if(i.length<2){y.warning("Ajansınızda son 14 günde aktif yayın açan yayıncı yok veya çok az. Lütfen yayıncılarınızın metriklerini kontrol edin."),E(!1);return}const t=d=>{const w=Number(d.elmaslar)||0,S=Number(d.yayin_suresi)||0;return w*1+S*.003},a=i.map(d=>({...d,skor:t(d)}));a.sort((d,w)=>w.skor-d.skor);const n=Math.floor(a.length/4),s=a.slice(-n),r=a.slice(0,a.length-n),m=[],l=new Set;for(let d=0;d<s.length-1;d+=2)!l.has(s[d].id)&&!l.has(s[d+1].id)&&(m.push({yayinci1_username:s[d].username,yayinci2_username:s[d+1].username,optimal_saat:`${18+Math.floor(d/2/2)}:${d/2%2*30===0?"00":"30"}`}),l.add(s[d].id),l.add(s[d+1].id));let f=0,_=r.length-1;for(;f<_;)!l.has(r[f].id)&&!l.has(r[_].id)&&(m.push({yayinci1_username:r[f].username,yayinci2_username:r[_].username,optimal_saat:`${18+Math.floor(m.length/2/2)}:${m.length/2%2*30===0?"00":"30"}`}),l.add(r[f].id),l.add(r[_].id)),f++,_--;ce(m);const h=m.map((d,w)=>{let S;if(d.optimal_saat){const[$,H]=d.optimal_saat.split(":").map(Number);S=p().add(1,"day").hour($).minute(H).second(0)}else{let $=p().add(1,"day").hour(20).minute(0).second(0);S=p($).add(w*30,"minutes")}return{...d,time:S}});F(h),P(Array.from(new Set(a.map(d=>d.id)))),y.success(`${m.length} stratejik eşleşme oluşturuldu (elmas ve yayın süresi ağırlıklı).`),E(!1)}catch(t){y.error("Otomatik eşleştirme yapılamadı.")}finally{E(!1)}},Re=async i=>{if(i.length<2||i.length%2!==0){y.warning("Eşleştirme için çift sayıda yayıncı gerekli");return}oe(!0);try{const t=k.filter(s=>i.includes(s.id));let a=[];a=Ke(t),ce(a);const n=a.map((s,r)=>{let m;if(s.optimal_saat){const[l,f]=s.optimal_saat.split(":").map(Number);m=p().add(1,"day").hour(l).minute(f).second(0)}else{let l=p().add(1,"day").hour(20).minute(0).second(0);m=p(l).add(r*30,"minutes")}return{...s,time:m}});F(n)}catch(t){y.error("PK eşleştirmeleri oluşturulamadı")}finally{oe(!1)}},Ke=i=>{const t=[],a=[...i].sort(()=>.5-Math.random());for(let n=0;n<a.length;n+=2)if(n+1<a.length){const r=n/2,m=20+Math.floor(r/2),l=r%2*30;t.push({yayinci1_username:a[n].username,yayinci2_username:a[n+1].username,optimal_saat:`${m}:${l.toString().padStart(2,"0")}`})}return t},Le=()=>{fe()},Oe=async()=>{T?await He():await Re(j)},B=i=>k.find(t=>t.username===i),We=async(i,t,a)=>{J(!0);try{const n=new Set;u.forEach(h=>{n.add(h.yayinci1_username),n.add(h.yayinci2_username)});const s=k.filter(h=>n.has(h.username)&&h.mail&&h.mail.includes("@")).map(h=>({email:h.mail,name:h.isim_soyisim}));if(s.length===0){y.error("Eşleşen yayıncıların geçerli e-posta adresi bulunamadı."),J(!1);return}const r=s.map(h=>h.email);Ie(r);const m=u.map((h,d)=>{const w=B(h.yayinci1_username),S=B(h.yayinci2_username),$=h.time?h.time.format("HH:mm"):"Belirsiz",H=h.time?h.time.format("DD MMMM YYYY"):"Belirsiz";return`
          <div style="border-bottom: 1px solid #eee; padding: 10px 0; margin-bottom: 10px; font-size: 0.95em;">
            <p style="margin: 0 0 5px 0; font-weight: bold; font-size: 1.05em;">
              <span style="background-color: #f0f0f0; padding: 2px 6px; border-radius: 4px; margin-right: 8px;">${d+1}</span> 
              ${(w==null?void 0:w.isim_soyisim)||"-"} <span style="color: #ff4d4f; font-weight: bold;">vs</span> ${(S==null?void 0:S.isim_soyisim)||"-"}
            </p>
            <p style="margin: 0 0 5px 0; color: #555; padding-left: 30px;">📅 ${H}</p>
            <p style="margin: 0; color: #555; padding-left: 30px;">⏰ ${$}</p>
          </div>
        `}).join(""),f=`<p style="margin-top: 25px; color: #888; font-size: 0.9em;">Saygılarımızla,<br>Tuber Ajans Ekibi<br>${p().format("DD.MM.YYYY")}</p>`,_=`
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Hızlı PK Eşleşme Bilgisi</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; line-height: 1.6; color: #333; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; margin: 0; padding: 0; background-color: #f4f4f4; }
            .email-wrapper { background-color: #f4f4f4; padding: 20px; }
            .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; border: 1px solid #e0e0e0; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.05); overflow: hidden; }
            .header { background-color: #1677ff; color: white; padding: 20px; text-align: center; border-top-left-radius: 9px; border-top-right-radius: 9px; }
            .header h1 { margin: 0; font-size: 1.7em; }
            .content { padding: 25px; }
            h2 { margin-top: 25px; color: #1677ff; border-bottom: 2px solid #f0f0f0; padding-bottom: 8px; font-size: 1.4em; }
            p { margin: 12px 0; font-size: 1em; color: #555; }
            .match-list { margin-top: 20px; }
            strong { color: #096dd9; font-weight: 600; }
            a { color: #1677ff; text-decoration: none; }
            a:hover { text-decoration: underline; }
            .highlight { background-color: #e6f7ff; border-left: 4px solid #1677ff; padding: 12px; border-radius: 4px; margin-top: 25px; font-size: 0.95em; }
            .footer { margin-top: 30px; text-align: center; font-size: 0.8em; color: #aaa; padding: 15px; background-color: #f9f9f9; border-bottom-left-radius: 9px; border-bottom-right-radius: 9px;}
          </style>
        </head>
        <body>
         <div class="email-wrapper">
          <div class="container">
            <div class="header">
              <h1>🔥 Hızlı PK Eşleşmeleri Hazır! 🔥</h1>
            </div>
            <div class="content">
              <p>Merhaba Değerli Yayıncılarımız,</p>
              <p>Sizler için heyecan dolu <strong>${i}</strong> etkinliğinin PK eşleşmelerini hazırladık! Aşağıda maç detaylarını ve saatlerini bulabilirsiniz.</p>
              <p>📅 Etkinlik Tarihleri: <strong>${t} - ${a}</strong> (Tahmini)</p>
              
              <h2>⚔️ Eşleşmeler ve Saatler ⚔️</h2>
              <div class="match-list">
                ${m}
              </div>
              
              <p class="highlight">Lütfen kendi maç saatinizden <strong>15 dakika önce</strong> yayında ve hazır olun. Rakibinizle iletişime geçmeyi unutmayın! 😉</p>
              <p>Herhangi bir sorunuz veya maç saati değişikliği talebiniz olursa lütfen yayıncı temsilcinizle (<a href="mailto:<EMAIL>"><EMAIL></a>) iletişime geçin.</p>
              <p>Herkese bol şans ve bol elmaslar dileriz! ✨</p>
              ${f}
            </div>
             <div class="footer">
               Bu e-posta Tuber Ajans tarafından otomatik olarak gönderilmiştir.
            </div>
          </div>
          </div>
        </body>
        </html>
      `;$e(_),Ee(!0)}catch(n){y.error("E-posta içeriği oluşturulurken bir hata oluştu.")}finally{J(!1)}},fe=async()=>{if(!k||k.length===0){y.error("Yayıncı listesi yüklenemedi veya boş. Lütfen sayfayı yenileyin veya yayıncıları yenileyin."),V([]),N(!1);return}if(u.length===0){y.warning("Önizleme oluşturmak için önce eşleşme oluşturmalısınız.");return}const i=[];let t=0,a=0;if(u.forEach(n=>{const s=B(n.yayinci1_username),r=B(n.yayinci2_username),m=Je(n.time);if(s&&r&&s.username&&r.username){const l=`Merhaba ${s.isim_soyisim||s.username} 👋

Hızlı PK etkinliğimiz için ${r.isim_soyisim||r.username} ile eşleştin! 🚀

📅 Tarih & Saat: ${m}

Rakibinin profiline göz atmayı unutma: https://www.tiktok.com/@${r.username} 👈

*Küçük bir hatırlatma:* Maçtan önce takipçilerine duyuru yapmayı ve ısınmak için yayına en az 30 dakika erken girmeyi unutma! 😉

Bol şans! ✨`;i.push({recipientName:s.isim_soyisim||s.username,recipientPhone:s.telefon,opponentName:r.isim_soyisim||r.username,dateTime:m,message:l.replace(pe,"")});const f=`Merhaba ${r.isim_soyisim||r.username} 👋

Hızlı PK etkinliğimiz için ${s.isim_soyisim||s.username} ile eşleştin! 🚀

📅 Tarih & Saat: ${m}

Rakibinin profiline göz atmayı unutma: https://www.tiktok.com/@${s.username} 👈

*Küçük bir hatırlatma:* Maçtan önce takipçilerine duyuru yapmayı ve ısınmak için yayına en az 30 dakika erken girmeyi unutma! 😉

Bol şans! ✨`;i.push({recipientName:r.isim_soyisim||r.username,recipientPhone:r.telefon,opponentName:s.isim_soyisim||s.username,dateTime:m,message:f.replace(pe,"")}),t++}else a++,s&&s.username,r&&r.username}),t>0)V(i),N(!0),a>0&&y.warning(`${a} eşleşme için eksik bilgi (isim, kullanıcı adı) nedeniyle mesaj oluşturulamadı.`);else{let n="Eşleşen yayıncılar için mesajlar oluşturulamadı. ";a>0?n+="Yayıncı ID'leri eşleşmiyor veya gerekli bilgiler (isim, kullanıcı adı) eksik olabilir.":u.length>0?n+="Beklenmeyen bir hata oluştu. Lütfen konsolu kontrol edin.":n="Önce eşleşme oluşturmalısınız.",y.error(n),V([]),N(!1)}},Ue=async(i,t)=>{try{return await new Promise(a=>setTimeout(a,300+Math.random()*400)),Math.random()>.1}catch(a){return!1}},ge=(i,t)=>{const a=[...M];a[i]&&(a[i].message=t,Q(a))},Ge=async()=>{if(M.length===0){y.warning("Gönderilecek mesaj bulunamadı.");return}de(!0);let i=0,t=0;M.length;for(const[a,n]of M.entries())n.recipientPhone&&n.recipientPhone.trim()!==""&&await Ue(n.recipientPhone,n.message)?i++:t++;de(!1),i>0&&K.success(`${i} mesaj başarıyla gönderildi (simülasyon).`),t>0&&K.error(`${t} mesaj gönderilemedi veya alıcının telefon numarası yok.`),t===0&&N(!1)},Fe=()=>{const[i,t]=c.useState([{id:"1",name:"PK Eşleşme Şablonu",content:`Merhaba {recipientName} 👋

Hızlı PK etkinliğimiz için {opponentName} ile eşleştin! 🚀

📅 Tarih & Saat: {dateTime}

Rakibinin profiline göz atmayı unutma: https://www.tiktok.com/@{opponentUsername} 👈

*Küçük bir hatırlatma:* Maçtan önce takipçilerine duyuru yapmayı ve ısınmak için yayına en az 30 dakika erken girmeyi unutma! 😉

Bol şans! ✨`},{id:"2",name:"Kısa PK Bilgilendirme",content:`Merhaba {recipientName}!
PK eşleşmen: {opponentName}
Tarih: {dateTime}
Lütfen zamanında hazır ol! 👍`}]),[a,n]=c.useState(!1),[s,r]=c.useState(""),[m,l]=c.useState(""),[f,_]=c.useState(null),h=()=>{if(!s.trim()||!m.trim()){y.error("Şablon adı ve içeriği boş olamaz!");return}const o={id:Date.now().toString(),name:s,content:m};t([...i,o]),n(!1),r(""),l(""),y.success("Yeni şablon eklendi!")},d=o=>{const g=i.find(v=>v.id===o);g&&(_(o),r(g.name),l(g.content),n(!0))},w=()=>{if(!s.trim()||!m.trim()){y.error("Şablon adı ve içeriği boş olamaz!");return}t(i.map(o=>o.id===f?{...o,name:s,content:m}:o)),n(!1),_(null),r(""),l(""),y.success("Şablon güncellendi!")},S=o=>{ae.confirm({title:"Şablonu Silmek İstediğinize Emin misiniz?",content:"Bu işlem geri alınamaz.",okText:"Evet, Sil",okType:"danger",cancelText:"İptal",onOk(){t(i.filter(g=>g.id!==o)),y.success("Şablon silindi!")}})},$=(o,g)=>{const v=i.find(ai=>ai.id===o);if(!v||!M[g])return;const O=M[g];let ee=v.content.replace(/{recipientName}/g,O.recipientName).replace(/{opponentName}/g,O.opponentName).replace(/{dateTime}/g,O.dateTime);const xe=H(O.opponentName);xe&&(ee=ee.replace(/{opponentUsername}/g,xe)),ge(g,ee),require("antd").message.success("Şablon uygulandı!")},H=o=>{const g=k.find(v=>v.isim_soyisim===o||v.username===o);return(g==null?void 0:g.username)||""},ii=[{key:"1",label:`Kişiselleştirilmiş Mesajlar (${M.length})`,children:e.jsxs("div",{style:{maxHeight:"55vh",overflowY:"auto",padding:"5px"},children:[" ",M.length>0?e.jsx(I,{grid:{gutter:16,xs:1,sm:1,md:2,lg:2,xl:2,xxl:2},dataSource:M,renderItem:(o,g)=>e.jsx(I.Item,{children:e.jsxs(R,{size:"small",title:e.jsx("span",{style:{fontSize:"1em",fontWeight:500},children:`Alıcı: ${o.recipientName}`}),extra:o.recipientPhone||e.jsx(z,{color:"warning",children:"Numara Yok"}),style:{marginBottom:"15px",border:"1px solid #e8e8e8"},headStyle:{backgroundColor:"#fafafa"},children:[e.jsx(ne,{value:o.message,onChange:v=>ge(g,v.target.value),autoSize:{minRows:4,maxRows:6},style:{background:"#fff",borderRadius:"6px",padding:"8px 12px",fontSize:"13.5px",lineHeight:"1.5",border:"1px solid #d9d9d9",marginBottom:"10px"}}),e.jsxs("div",{style:{textAlign:"right",display:"flex",justifyContent:"space-between"},children:[e.jsx(oi,{menu:{items:i.map(v=>({key:v.id,label:v.name,onClick:()=>$(v.id,g)}))},disabled:A||i.length===0,children:e.jsxs(x,{size:"small",icon:e.jsx(gi,{}),disabled:A||i.length===0,children:["Şablon Uygula ",e.jsx(wi,{})]})}),e.jsx(x,{size:"small",icon:e.jsx(Mi,{}),onClick:()=>{navigator.clipboard.writeText(o.message),K.success(`${o.recipientName} için mesaj kopyalandı!`)},disabled:A,children:"Kopyala"})]})]})},g)}):e.jsx(ci,{message:"Gönderilecek kişiselleştirilmiş mesaj bulunamadı.",type:"info",showIcon:!0})]})},{key:"2",label:"Şablon Yönetimi",children:e.jsxs("div",{style:{padding:"20px"},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:"16px"},children:[e.jsx(ze.Title,{level:5,style:{margin:0},children:"WhatsApp Mesaj Şablonları"}),e.jsx(x,{type:"primary",icon:e.jsx(xi,{}),onClick:()=>{n(!0),_(null),r(""),l("")},children:"Yeni Şablon"})]}),a&&e.jsx(R,{style:{marginBottom:"20px"},size:"small",children:e.jsxs(b,{layout:"vertical",children:[e.jsx(b.Item,{label:"Şablon Adı",required:!0,children:e.jsx(se,{value:s,onChange:o=>r(o.target.value),placeholder:"Örn: PK Eşleşme Bildirimi"})}),e.jsx(b.Item,{label:"Şablon İçeriği",required:!0,help:e.jsxs("span",{children:["Kullanılabilir değişkenler: ",e.jsx(z,{color:"blue",children:"{recipientName}"}),e.jsx(z,{color:"blue",children:"{opponentName}"}),e.jsx(z,{color:"blue",children:"{dateTime}"}),e.jsx(z,{color:"blue",children:"{opponentUsername}"})]}),children:e.jsx(ne,{value:m,onChange:o=>l(o.target.value),placeholder:"Merhaba {recipientName}! PK eşleşmen: {opponentName}, Tarih: {dateTime}",autoSize:{minRows:4,maxRows:8}})}),e.jsx(b.Item,{children:e.jsxs(D,{children:[e.jsx(x,{type:"primary",onClick:f?w:h,style:{backgroundColor:f?"#1890ff":"#52c41a",borderColor:f?"#1890ff":"#52c41a",color:"#ffffff",fontWeight:500},children:f?"Güncelle":"Ekle"}),e.jsx(x,{onClick:()=>n(!1),style:{border:"1px solid #d9d9d9",color:"#000000d9"},children:"İptal"})]})})]})}),i.length>0?e.jsx(I,{itemLayout:"vertical",dataSource:i,renderItem:o=>e.jsx(I.Item,{actions:[e.jsx(x,{icon:e.jsx(bi,{}),size:"small",onClick:()=>d(o.id),children:"Düzenle"},"edit"),e.jsx(x,{icon:e.jsx(ki,{}),danger:!0,size:"small",onClick:()=>S(o.id),children:"Sil"},"delete")],children:e.jsx(I.Item.Meta,{title:o.name,description:e.jsx("div",{style:{backgroundColor:"#f5f5f5",padding:"8px",borderRadius:"4px",marginTop:"8px",whiteSpace:"pre-wrap"},children:o.content})})},o.id)}):e.jsx(ke,{description:"Henüz şablon eklenmemiş",image:ke.PRESENTED_IMAGE_SIMPLE})]})}];return e.jsx(ae,{title:e.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[e.jsx(Me,{style:{fontSize:"24px",color:"#25D366",marginRight:"10px"}}),e.jsx("span",{children:"WhatsApp Mesaj Gönderimi"})," "]}),open:Z,onCancel:()=>N(!1),width:900,footer:[e.jsx(x,{onClick:()=>N(!1),disabled:A,children:"Kapat"},"close"),e.jsxs(x,{type:"primary",icon:e.jsx(ji,{}),style:{backgroundColor:"#25D366",borderColor:"#25D366"},loading:A,onClick:Ge,disabled:M.length===0||A,children:["Tüm Mesajları Gönder (",M.length,")"]},"sendAll")],bodyStyle:{padding:"0"},children:e.jsx(mi,{defaultActiveKey:"1",items:ii,style:{padding:"10px 20px"}})})},qe=(i,t)=>{if(!t)return;const a=[...u];a[i]&&(a[i]={...a[i],time:t},F(a),q.length>0&&Ve())},Ve=async()=>{var i;if(u.length>0){const t=u[0];if(t&&t.time){`${p(t.time).format("DD MMMM")}`;const a=p(t.time).format("DD MMMM YYYY, HH:mm");(i=u[u.length-1])!=null&&i.time&&p(u[u.length-1].time).add(30,"minute").format("DD MMMM YYYY, HH:mm"),await fe()}}},Je=i=>i?i.locale("tr").format("D MMMM dddd HH:mm"):"Belirtilmemiş Zaman",Ze=()=>{var i,t,a;if(u.length===0){y.error("Kaydedilecek eşleşme bulunmuyor.");return}me.setFieldsValue({etkinlik_adi:`Hızlı PK - ${p((i=u[0])==null?void 0:i.time).format("DD MMMM")||"Belirsiz Tarih"}`,baslangic_tarihi:((t=u[0])==null?void 0:t.time)||p().add(1,"day"),bitis_tarihi:((a=u[u.length-1])==null?void 0:a.time)||p().add(1,"day"),aciklama:`${u.length} maçlık hızlı PK etkinliği.`}),L(!0)},Qe=async i=>{ue(!0);try{const t=u.reduce((s,r)=>(r.yayinci1_username&&s.add(String(r.yayinci1_username)),r.yayinci2_username&&s.add(String(r.yayinci2_username)),s),new Set),a={etkinlik_adi:i.etkinlik_adi,etkinlik_tipi:"hizli_pk",baslangic_tarihi:p(i.baslangic_tarihi).format("YYYY-MM-DD"),bitis_tarihi:p(i.bitis_tarihi).format("YYYY-MM-DD"),aciklama:i.aciklama,kayit_uygunlugu:"ozel",puan_sistemi:"elmas",durum:"aktif",yayincilar:Array.from(t),eslesme_bilgileri:u.map(s=>({yayinci1_username:String(s.yayinci1_username),yayinci2_username:String(s.yayinci2_username),mac_zamani:s.time?s.time.format("YYYY-MM-DD HH:mm:ss"):null}))},n=await vi(a);n&&n.success?(y.success("PK Etkinliği başarıyla kaydedildi!"),L(!1),Ne("/events")):y.error(`Etkinlik kaydedilemedi: ${(n==null?void 0:n.error)||"Bilinmeyen hata"}`)}catch(t){y.error("Etkinlik kaydedilemedi. Lütfen tekrar deneyin.")}finally{ue(!1)}},Xe=Array.from(new Map(k.filter(i=>Ae.includes(i.username.trim().toLowerCase())).map(i=>[i.username.trim().toLowerCase(),i])).values()),ei=T?{backgroundColor:"#10B981",color:"#fff",border:"1px solid #10B981"}:{backgroundColor:"#fff",color:"#333",border:"1px solid #d9d9d9"};return e.jsxs("div",{className:"pk-matcher",children:[e.jsx(W,{gutter:[16,16],children:e.jsxs(Y,{xs:24,children:[" ",e.jsx(R,{title:e.jsxs("div",{className:"flex items-center",children:[e.jsx(je,{className:"mr-2 text-xl text-orange-500"}),e.jsx("span",{children:"PK Eşleştirme Sihirbazı"})]}),children:e.jsxs(b,{layout:"vertical",children:[e.jsx(b.Item,{label:"Mod Seçimi",className:"mb-4",children:e.jsxs(D,{align:"center",children:[e.jsx(si,{checked:T,onChange:De,checkedChildren:"Otomatik Mod",unCheckedChildren:"Manuel Mod",style:ei}),e.jsx(U,{type:"secondary",children:T?"Sistem en aktif ve performansı yüksek yayıncıları seçer.":"Eşleştirilecek yayıncıları manuel olarak seçin."})]})}),!T&&e.jsxs(b.Item,{label:"Eşleştirilecek Yayıncılar",required:!0,help:"Listeden çift sayıda yayıncı seçin (2, 4, 6...)",validateStatus:j.length===0||j.length%2!==0?"error":"",children:[e.jsx(Te,{mode:"multiple",allowClear:!0,style:{width:"100%"},placeholder:"Yayıncıları seçin",onChange:P,value:j,optionLabelProp:"label",loading:G,filterOption:(i,t)=>{var a;return String((a=t==null?void 0:t.label)!=null?a:"").toLowerCase().includes(i.toLowerCase())},children:Xe.slice().sort((i,t)=>{const a=(Number(i.elmaslar)||0)*1+(Number(i.yayin_suresi)||0)*.003;return(Number(t.elmaslar)||0)*1+(Number(t.yayin_suresi)||0)*.003-a}).map(i=>e.jsx(Si,{value:i.id,label:`${i.isim_soyisim} (@${i.username})`,children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("span",{children:[i.isim_soyisim," (@",i.username,")"]}),e.jsxs(D,{size:"small",children:[i.elmaslar!==void 0&&e.jsxs(z,{color:"gold",children:["💎 ",i.elmaslar.toLocaleString()]}),i.yeni_takipciler!==void 0&&e.jsxs(z,{color:"blue",children:["👥 ",i.yeni_takipciler]}),i.yayin_suresi!==void 0&&e.jsxs(z,{color:"purple",children:["⏱️ ",(i.yayin_suresi/3600).toFixed(1)," sa"]})]})]})},i.id))}),j.length>0&&e.jsxs(U,{type:"secondary",style:{marginTop:"8px",display:"block"},children:[j.length," yayıncı seçildi."]})]}),e.jsx(b.Item,{children:e.jsxs(D,{children:[e.jsx(x,{type:"primary",onClick:Oe,loading:le||G,icon:T?e.jsx(_e,{}):e.jsx(je,{}),style:T?{backgroundColor:"#10B981",borderColor:"#10B981"}:void 0,disabled:!T&&(j.length<2||j.length%2!==0),children:T?"Otomatik Eşleştirme Oluştur":`Manuel Eşleştir (${j.length} Seçili)`}),e.jsx(x,{icon:e.jsx(ui,{}),onClick:he,loading:G,disabled:le,children:"Yayıncıları Yenile"})]})})]})})]})}),Ye.length>0&&e.jsx(R,{title:e.jsx(te,{level:4,style:{marginBottom:0},children:"Oluşturulan PK Eşleştirmeleri"}),className:"mt-6",extra:e.jsxs(D,{children:[" ",e.jsx(x,{type:"primary",icon:e.jsx(Me,{}),style:{backgroundColor:"#25D366",borderColor:"#25D366"},onClick:Le,children:"Whatsapp Gönder"}),e.jsx(x,{type:"primary",icon:e.jsx(fi,{}),loading:Pe,style:{backgroundColor:"#1677ff",borderColor:"#1677ff"},onClick:()=>{var i;if(u.length>0){const t=u[0];if(t&&t.time){const a=`Hızlı PK - ${p(t.time).format("DD MMMM")}`,n=p(t.time).format("DD MMMM YYYY, HH:mm"),s=(i=u[u.length-1])!=null&&i.time?p(u[u.length-1].time).add(30,"minute").format("DD MMMM YYYY, HH:mm"):n;We(a,n,s)}else y.warning("E-posta içeriği hazırlanamadı: Maç zamanları eksik.")}else y.warning("E-posta içeriği hazırlamak için önce eşleşme oluşturun.")},children:"E-posta Gönder"}),e.jsx(x,{type:"primary",onClick:Ze,loading:X,icon:e.jsx(we,{}),className:"etkinligi-kaydet-btn",style:{backgroundColor:"#1b5e20",borderColor:"#1b5e20"},children:"Etkinliği Kaydet"})]}),bodyStyle:{padding:"16px"},children:e.jsx(I,{grid:{gutter:16,xs:1,sm:1,md:2,lg:3,xl:4,xxl:4},dataSource:u,renderItem:(i,t)=>{const a=B(i.yayinci1_username),n=B(i.yayinci2_username),s=l=>{if(l){if(l.profile_image)return`${Se}${l.profile_image}`;if(l.username)return`${Se}/uploads/profile_images/${l.username}.jpg`}},r=s(a),m=s(n);return e.jsx(I.Item,{style:{padding:0,border:"none"},children:e.jsx(R,{hoverable:!0,className:"mb-4 w-full shadow-sm border border-gray-200 dark:border-gray-700",bodyStyle:{padding:"20px"},children:e.jsxs(W,{gutter:[16,16],align:"middle",children:[e.jsx(Y,{xs:24,sm:10,className:"text-center sm:text-left",children:e.jsxs(D,{direction:"vertical",align:"center",className:"w-full",children:[e.jsx(ie,{size:48,src:r,icon:!r&&e.jsx(ve,{}),style:{backgroundColor:"#1890ff"},alt:(a==null?void 0:a.isim_soyisim)||(a==null?void 0:a.username)||"Yayıncı"}),e.jsx(te,{level:5,style:{marginBottom:0},children:(a==null?void 0:a.username)||"Hata: ID Bulunamadı"}),(a==null?void 0:a.isim_soyisim)&&e.jsx(U,{type:"secondary",style:{fontSize:"12px"},children:a.isim_soyisim})]})}),e.jsxs(Y,{xs:24,sm:4,className:"flex flex-col items-center justify-center",children:[" ",e.jsx(ie,{size:40,style:{backgroundColor:"#ff4d4f",color:"white",fontWeight:"bold"},children:"VS"})," "]}),e.jsx(Y,{xs:24,sm:10,className:"text-center sm:text-right",children:e.jsxs(D,{direction:"vertical",align:"center",className:"w-full",children:[e.jsx(ie,{size:48,src:m,icon:!m&&e.jsx(ve,{}),style:{backgroundColor:"#fa8c16"},alt:(n==null?void 0:n.isim_soyisim)||(n==null?void 0:n.username)||"Yayıncı"}),e.jsx(te,{level:5,style:{marginBottom:0},children:(n==null?void 0:n.username)||"Hata: ID Bulunamadı"}),(n==null?void 0:n.isim_soyisim)&&e.jsx(U,{type:"secondary",style:{fontSize:"12px"},children:n.isim_soyisim})]})}),e.jsx(Y,{xs:24,className:"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700",children:e.jsxs(W,{justify:"space-between",align:"middle",children:[e.jsx(Y,{children:e.jsx(ri,{locale:yi,children:e.jsx(li,{value:i.time,format:"HH:mm",minuteStep:15,placeholder:"Saat seçin",onChange:l=>qe(t,l),allowClear:!1,style:{width:130},suffixIcon:e.jsx(pi,{})})})}),e.jsx(Y,{children:e.jsxs(D,{direction:"vertical",align:"end",size:4,children:[e.jsx(z,{icon:e.jsx(hi,{}),color:"cyan",children:i.time?i.time.format("DD MMMM YYYY"):"Tarih Belirsiz"}),i.optimal_saat&&e.jsxs(z,{color:"magenta",icon:e.jsx(_e,{}),children:["Önerilen Saat: ",i.optimal_saat]})]})})]})})]})})},`${i.yayinci1_username}-${i.yayinci2_username}-${t}`)}})}),e.jsx(ae,{title:"PK Etkinliğini Kaydet",open:Ce,onCancel:()=>L(!1),footer:null,width:600,children:e.jsxs(b,{form:me,layout:"vertical",onFinish:Qe,children:[e.jsx(b.Item,{name:"etkinlik_adi",label:"Etkinlik Adı",rules:[{required:!0,message:"Etkinlik adı gereklidir"}],children:e.jsx(se,{placeholder:"Örn: Hızlı PK - 20 Nisan"})}),e.jsxs(W,{gutter:16,children:[e.jsx(Y,{span:12,children:e.jsx(b.Item,{name:"baslangic_tarihi",label:"Başlangıç Tarihi",rules:[{required:!0,message:"Başlangıç tarihi gereklidir"}],children:e.jsx(be,{style:{width:"100%"},format:"DD MMMM YYYY",placeholder:"Başlangıç tarihi seçin"})})}),e.jsx(Y,{span:12,children:e.jsx(b.Item,{name:"bitis_tarihi",label:"Bitiş Tarihi",rules:[{required:!0,message:"Bitiş tarihi gereklidir"}],children:e.jsx(be,{style:{width:"100%"},format:"DD MMMM YYYY",placeholder:"Bitiş tarihi seçin"})})})]}),e.jsx(b.Item,{name:"aciklama",label:"Açıklama",rules:[{required:!1}],children:e.jsx(ne,{rows:3,placeholder:"Etkinlik için kısa bir açıklama (opsiyonel)"})}),e.jsxs("div",{className:"flex justify-end space-x-2 mt-4",children:[e.jsx(x,{onClick:()=>L(!1),disabled:X,children:"İptal"}),e.jsx(x,{type:"primary",htmlType:"submit",loading:X,icon:e.jsx(we,{}),style:{backgroundColor:"#1677ff",borderColor:"#1677ff",color:"#fff",fontWeight:500},onMouseOver:i=>{i.currentTarget.style.backgroundColor="#1256b8"},onMouseOut:i=>{i.currentTarget.style.backgroundColor="#1677ff"},onFocus:i=>{i.currentTarget.style.backgroundColor="#1256b8"},onBlur:i=>{i.currentTarget.style.backgroundColor="#1677ff"},children:"Etkinliği Kaydet"})]})]})}),e.jsx(Fe,{})]})},qi=()=>e.jsx(e.Fragment,{children:e.jsx(zi,{})});export{qi as default};
