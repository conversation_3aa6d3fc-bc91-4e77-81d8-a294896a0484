<?php
header('Content-Type: application/json; charset=utf-8');

// JSON veya form-data desteği
$input = json_decode(file_get_contents('php://input'), true);
file_put_contents(__DIR__.'/debug_input.txt', date('c') . ' - INPUT: ' . print_r($input, true) . PHP_EOL, FILE_APPEND);
$to = $input['to'] ?? ($_POST['to'] ?? '');
$contactId = $input['contactId'] ?? ($_POST['contactId'] ?? '');
$message = $input['message'] ?? ($_POST['message'] ?? '');
$type = $input['type'] ?? ($_POST['type'] ?? 'text');

// 'to' veya 'contactId' zorunlu, en az biri olmalı
if ((!$to && !$contactId) || ($type !== 'template' && !$message)) {
    echo json_encode([
        'success' => false,
        'error' => 'Eksik parametre: Telefon numarası (to) veya contactId ve mesaj zorunludur.'
    ]);
    exit;
}

// Eğer contactId varsa, onu kullan (örnek: ileride rehberden çekmek için)
$finalTo = $to;
if (!$finalTo && $contactId) {
    // Burada contactId'den numara çekme işlemi eklenebilir (örnek: veritabanı sorgusu)
    // Şimdilik desteklenmiyor
    echo json_encode([
        'success' => false,
        'error' => 'contactId ile mesaj gönderme henüz desteklenmiyor, lütfen telefon numarası (to) kullanın.'
    ]);
    exit;
}

// WhatsApp Cloud API ayarları (örnek, kendi bilgilerinle değiştir)
$access_token = 'EAAOx5pJHA1oBO6SzKxxXYlISUGwgiIr6Gob7PF7XngGvJM2cUz6WTZAC8BF96Vykuw3BJAclP3zRvcxZC1E3gRB4b27vA2byilX3rnpFB3d3ZBRI3U1Cwem1BQiiZAV95EnT183umcA6zuUeivXagHGkRXfnZATjRDRBpiJST26zZCSX41nN5AZCEsfPHH1ZAAyzIsPh3qZAm8iW4oZBVQe1AjJ2E5NLN86Y3MhWh1d0zo';
$phone_number_id = '629378510262898';

$url = "https://graph.facebook.com/v18.0/$phone_number_id/messages";

if ($type === 'template') {
    $template_name = $input['template_name'] ?? ($_POST['template_name'] ?? '');
    $template_language = $input['template_language'] ?? ($_POST['template_language'] ?? 'tr');
    $template_parameters = $input['template_parameters'] ?? ($_POST['template_parameters'] ?? []);
    if (!$template_name) {
        echo json_encode([
            'success' => false,
            'error' => 'Eksik parametre: template_name zorunludur.'
        ]);
        exit;
    }
    $payload = [
        'messaging_product' => 'whatsapp',
        'to' => $finalTo,
        'type' => 'template',
        'template' => [
            'name' => $template_name,
            'language' => [ 'code' => $template_language ],
            'components' => [
                [
                    'type' => 'body',
                    'parameters' => $template_parameters
                ]
            ]
        ]
    ];
} else {
    $payload = [
        'messaging_product' => 'whatsapp',
        'to' => $finalTo,
        'type' => 'text',
        'text' => [ 'body' => $message ]
    ];
}

$ch = curl_init($url);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer ' . $access_token,
    'Content-Type: application/json'
]);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);
curl_close($ch);

// Hata log dosyasını oluşturmak için dosya yoksa oluştur
$logFile = __DIR__ . '/whatsapp_send_error_log.txt';
if (!file_exists($logFile)) {
    @touch($logFile);
    @chmod($logFile, 0666);
}

if ($http_code === 200 || $http_code === 201) {
    // Giden mesajı logla
    $payload['timestamp'] = time();
    file_put_contents(__DIR__ . '/whatsapp_outgoing_log.txt', date('c') . ' - ' . json_encode($payload) . PHP_EOL, FILE_APPEND);
    echo json_encode(['success' => true, 'response' => json_decode($response, true)]);
} else {
    // Hata logunu kaydet
    $logMsg = date('c') . ' - HTTP: ' . $http_code . ' - CURL: ' . $curl_error . ' - RESPONSE: ' . $response . ' - PAYLOAD: ' . json_encode($payload) . PHP_EOL;
    file_put_contents($logFile, $logMsg, FILE_APPEND);
    echo json_encode([
        'success' => false,
        'error' => 'WhatsApp API hatası',
        'http_code' => $http_code,
        'response' => $response,
        'curl_error' => $curl_error,
        'payload' => $payload
    ]);
} 