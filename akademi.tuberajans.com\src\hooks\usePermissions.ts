import { useState, useEffect } from 'react';
import axios from 'axios';

interface PermissionResult {
  hasAccess: boolean;
  isAgencyPublisher: boolean;
  message: string;
  userInfo?: {
    display_name: string;
    username: string;
    is_verified: boolean;
  };
  contactInfo?: {
    message: string;
    email: string;
    phone: string;
    website: string;
  };
  loading: boolean;
  error: string | null;
}

export const usePermissions = (endpoint: string): PermissionResult => {
  const [result, setResult] = useState<PermissionResult>({
    hasAccess: false,
    isAgencyPublisher: false,
    message: '',
    loading: true,
    error: null
  });

  useEffect(() => {
    const checkPermissions = async () => {
      try {
        setResult(prev => ({ ...prev, loading: true, error: null }));
        
        const response = await axios.get(`/backend/api/check_permissions.php?endpoint=${endpoint}`);
        
        if (response.data.status === 'success') {
          setResult({
            hasAccess: response.data.has_access,
            isAgencyPublisher: response.data.is_agency_publisher,
            message: response.data.message,
            userInfo: response.data.user_info,
            contactInfo: response.data.contact_info,
            loading: false,
            error: null
          });
        } else {
          setResult(prev => ({
            ...prev,
            loading: false,
            error: response.data.message || 'Yetki kontrolü başarısız'
          }));
        }
      } catch (error) {
        console.error('Permission check error:', error);
        setResult(prev => ({
          ...prev,
          loading: false,
          error: 'Yetki kontrolü sırasında hata oluştu'
        }));
      }
    };

    if (endpoint) {
      checkPermissions();
    }
  }, [endpoint]);

  return result;
};

export default usePermissions;
