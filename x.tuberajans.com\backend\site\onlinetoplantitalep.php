<?php
// Hata ayıklama ve raporlama ayarları
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/onlinetoplantitalep_errors.log');

// CORS başlıkları
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
header("Access-Control-Max-Age: 86400");
header("Content-Type: application/json; charset=utf-8");

// OPTIONS isteği için erken yanıt ver
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(204);
    exit();
}

try {
    // Config dosyasını dahil et
    require_once __DIR__ . '/../config/config.php';

    // Veritabanı bağlantısını kontrol et
    if (!isset($db) || !($db instanceof PDO)) {
        throw new Exception("Veritabanı bağlantısı kurulamadı");
    }

    // Tablo varlığını kontrol et ve gerekirse oluştur
    try {
        $tableCheckStmt = $db->query("SHOW TABLES LIKE 'onlinetoplantitalep'");
        if ($tableCheckStmt->rowCount() === 0) {
            $db->exec("CREATE TABLE IF NOT EXISTS onlinetoplantitalep (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                topic VARCHAR(255) NOT NULL,
                preferred_date DATE NOT NULL,
                preferred_time TIME NOT NULL,
                duration INT NOT NULL DEFAULT 30,
                status ENUM('requested', 'scheduled', 'completed', 'cancelled') NOT NULL DEFAULT 'requested',
                meeting_link VARCHAR(255) NULL,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci");
            error_log("onlinetoplantitalep tablosu oluşturuldu.");
        }
    } catch (PDOException $e) {
        error_log("Tablo kontrolü hatası: " . $e->getMessage());
    }

    // Veritabanı bağlantısı
    $pdo = $db;

    // İstek türüne göre işlem yap
    $action = isset($_GET['action']) ? $_GET['action'] : 'list';
    error_log("Online Toplantı Talep API - İstek türü: $action");

    // Okunmamış taleplerin sayısını getir
    if ($action === 'count') {
        try {
            // Önce tablonun yapısını kontrol et
            $tableInfoStmt = $pdo->query("DESCRIBE onlinetoplantitalep");
            $tableColumns = $tableInfoStmt->fetchAll(PDO::FETCH_COLUMN);

            // 'isRead' sütunu var mı kontrol et
            if (in_array('isRead', $tableColumns)) {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM onlinetoplantitalep WHERE isRead = 0 OR isRead IS NULL");
            } else {
                // 'isRead' sütunu yoksa, tüm kayıtları say
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM onlinetoplantitalep");
            }

            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            echo json_encode(['success' => true, 'count' => intval($result['count'])]);
            error_log("Okunmamış toplantı talebi sayısı: " . $result['count']);
        } catch (PDOException $e) {
            error_log("Sayım hatası: " . $e->getMessage());
            echo json_encode(['success' => false, 'message' => 'Okunmamış toplantı talebi sayısı alınamadı', 'error' => $e->getMessage()]);
        }
        exit;
    }

    // Tüm talepleri listele
    if ($action === 'list') {
        try {
            $stmt = $pdo->query("SELECT * FROM onlinetoplantitalep ORDER BY created_at DESC");
            $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo json_encode(['success' => true, 'data' => $data]);
            error_log("Listeleme başarılı: " . count($data) . " kayıt");
        } catch (PDOException $e) {
            error_log("Listeleme hatası: " . $e->getMessage());
            echo json_encode(['success' => false, 'message' => 'Online toplantı talepleri alınamadı', 'error' => $e->getMessage()]);
        }
        exit;
    }

    // Yeni talep ekle
    if ($action === 'add') {
        try {
            $data = json_decode(file_get_contents('php://input'), true);

            if (!isset($data['user_id']) || !isset($data['topic']) || !isset($data['preferred_date']) || !isset($data['preferred_time'])) {
                echo json_encode(['success' => false, 'message' => 'Eksik parametreler']);
                exit;
            }

            $stmt = $pdo->prepare("INSERT INTO onlinetoplantitalep
                (user_id, topic, preferred_date, preferred_time, duration, notes)
                VALUES (?, ?, ?, ?, ?, ?)");

            $duration = isset($data['duration']) ? $data['duration'] : 30;
            $notes = isset($data['notes']) ? $data['notes'] : null;

            $stmt->execute([$data['user_id'], $data['topic'], $data['preferred_date'], $data['preferred_time'], $duration, $notes]);

            echo json_encode(['success' => true, 'id' => $pdo->lastInsertId()]);
            error_log("Yeni toplantı talebi eklendi: ID " . $pdo->lastInsertId());
        } catch (PDOException $e) {
            error_log("Ekleme hatası: " . $e->getMessage());
            echo json_encode(['success' => false, 'message' => 'Online toplantı talebi eklenemedi', 'error' => $e->getMessage()]);
        }
        exit;
    }

    // Talep durumunu güncelle
    if ($action === 'update_status') {
        try {
            $data = json_decode(file_get_contents('php://input'), true);

            if (!isset($data['id']) || !isset($data['status'])) {
                echo json_encode(['success' => false, 'message' => 'Eksik parametreler']);
                exit;
            }

            $stmt = $pdo->prepare("UPDATE onlinetoplantitalep SET status = ? WHERE id = ?");
            $stmt->execute([$data['status'], $data['id']]);

            echo json_encode(['success' => true]);
            error_log("Durum güncellendi: ID " . $data['id'] . " -> " . $data['status']);
        } catch (PDOException $e) {
            error_log("Güncelleme hatası: " . $e->getMessage());
            echo json_encode(['success' => false, 'message' => 'Online toplantı talebi güncellenemedi', 'error' => $e->getMessage()]);
        }
        exit;
    }

    // Toplantı linkini güncelle
    if ($action === 'update_link') {
        try {
            $data = json_decode(file_get_contents('php://input'), true);

            if (!isset($data['id']) || !isset($data['meeting_link'])) {
                echo json_encode(['success' => false, 'message' => 'Eksik parametreler']);
                exit;
            }

            $stmt = $pdo->prepare("UPDATE onlinetoplantitalep SET meeting_link = ?, status = 'scheduled' WHERE id = ?");
            $stmt->execute([$data['meeting_link'], $data['id']]);

            echo json_encode(['success' => true]);
            error_log("Toplantı linki güncellendi: ID " . $data['id']);
        } catch (PDOException $e) {
            error_log("Link güncelleme hatası: " . $e->getMessage());
            echo json_encode(['success' => false, 'message' => 'Toplantı linki güncellenemedi', 'error' => $e->getMessage()]);
        }
        exit;
    }

    // Talep sil
    if ($action === 'delete') {
        try {
            $data = json_decode(file_get_contents('php://input'), true);

            if (!isset($data['id'])) {
                echo json_encode(['success' => false, 'message' => 'Eksik ID parametresi']);
                exit;
            }

            $stmt = $pdo->prepare("DELETE FROM onlinetoplantitalep WHERE id = ?");
            $stmt->execute([$data['id']]);

            echo json_encode(['success' => true]);
            error_log("Toplantı talebi silindi: ID " . $data['id']);
        } catch (PDOException $e) {
            error_log("Silme hatası: " . $e->getMessage());
            echo json_encode(['success' => false, 'message' => 'Online toplantı talebi silinemedi', 'error' => $e->getMessage()]);
        }
        exit;
    }

    // Okundu olarak işaretleme
    if ($action === 'mark_read') {
        $data = json_decode(file_get_contents('php://input'), true);
        $id = isset($data['id']) ? intval($data['id']) : 0;
        if ($id > 0) {
            $stmt = $pdo->prepare('UPDATE onlinetoplantitalep SET isRead = 1 WHERE id = ?');
            $stmt->execute([$id]);
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Geçersiz ID']);
        }
        exit;
    }

    // Geçersiz aksiyon
    echo json_encode(['success' => false, 'message' => 'Geçersiz action: ' . $action]);
    error_log("Geçersiz action: $action");

} catch (Exception $e) {
    error_log("Genel hata: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Bir hata oluştu', 'error' => $e->getMessage()]);
}
?>