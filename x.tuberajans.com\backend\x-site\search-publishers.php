<?php
// search-publishers.php
header('Content-Type: application/json');
require_once 'db.php'; // Veritabanı bağlantısı için

$q = isset($_GET['q']) ? trim($_GET['q']) : '';
if ($q === '') {
    echo json_encode(['success' => false, 'error' => 'Arama terimi boş olamaz.']);
    exit;
}

try {
    $pdo = getDb(); // db.php'de getDb() fonksiyonu ile PDO nesnesi alınmalı

    // Sadece telefon numarası olanlar ve arama kriterine uyanlar
    $sql = "SELECT id, isim_soyisim, telefon, mail, dogum_tarihi, sehir, meslek, kayit_tarihi, username
            FROM publisher_info
            WHERE telefon IS NOT NULL AND telefon != ''
              AND (
                isim_soyisim LIKE :q
                OR username LIKE :q
                OR telefon LIKE :q
              )
            LIMIT 20";
    $stmt = $pdo->prepare($sql);
    $likeQ = '%' . $q . '%';
    $stmt->bindParam(':q', $likeQ, PDO::PARAM_STR);
    $stmt->execute();

    $publishers = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode([
        'success' => true,
        'publishers' => $publishers
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Veritabanı hatası: ' . $e->getMessage()
    ]);
} 