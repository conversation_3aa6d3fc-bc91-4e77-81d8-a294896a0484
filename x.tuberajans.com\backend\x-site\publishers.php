<?php
/**
 * Publishers API - Gerçek Veritabanı ile Optimize Edilmiş
 * Frontend uyumlu, hızlı ve güvenilir yayıncı verileri
 */

// Hızlı ayarlar
set_time_limit(10); // 10 saniye max
ini_set('memory_limit', '128M');

// CORS ve Headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// OPTIONS kontrolü
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Config ve auth include
require_once __DIR__ . '/../config/config.php';

// Hata logları
error_log("Publishers API çağrıldı - " . date('Y-m-d H:i:s') . " - Method: " . $_SERVER['REQUEST_METHOD']);

// JSON yanıt fonksiyonu
if (!function_exists('jsonResponse')) {
    function jsonResponse($data, $status = 200) {
        http_response_code($status);
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
}

// Cache kontrol fonksiyonu
function getCachedData($key, $ttl = 60) {
    $cacheFile = sys_get_temp_dir() . "/publishers_$key.json";
    if (file_exists($cacheFile) && (time() - filemtime($cacheFile)) < $ttl) {
        return json_decode(file_get_contents($cacheFile), true);
    }
    return null;
}

function setCachedData($key, $data) {
    $cacheFile = sys_get_temp_dir() . "/publishers_$key.json";
    file_put_contents($cacheFile, json_encode($data, JSON_UNESCAPED_UNICODE));
}

try {
    // Auth kontrolü - checkAuth() fonksiyonunu kullan
    error_log("Publishers API: Auth kontrolü başlıyor...");
    $user = checkAuth();
    if (!$user) {
        error_log("Publishers API: Auth başarısız");
        jsonResponse([
            'success' => false,
            'error' => 'Yetkilendirme hatası',
            'message' => 'Oturum açmanız gerekiyor'
        ], 401);
    }
    
    error_log("Publishers API: Auth başarılı - User ID: " . $user['id']);

    // Veritabanı bağlantısını al
    error_log("Publishers API: Veritabanı bağlantısı alınıyor...");
    $db = getDB();
    if (!$db) {
        error_log("Publishers API: Veritabanı bağlantısı alınamadı");
        jsonResponse([
            'success' => false,
            'error' => 'Veritabanı hatası',
            'message' => 'Veritabanı bağlantısı kurulamadı'
        ], 500);
    }
    
    error_log("Publishers API: Veritabanı bağlantısı başarılı");

    // Publisher_info tablosunun hangi veritabanında olduğunu bul
    $databases_to_try = ['tuberaja_yayinci_takip', 'tuberaja_yayinci_akademi'];
    $publisher_database = null;
    
    foreach ($databases_to_try as $dbname) {
        try {
            error_log("Publishers API: $dbname veritabanı kontrol ediliyor...");
            
            // PDO ile doğrudan database.table notation kullan
            $testQuery = "SELECT COUNT(*) as count FROM $dbname.publisher_info LIMIT 1";
            $stmt = $db->prepare($testQuery);
            $stmt->execute();
            $result = $stmt->fetch();
            
            if ($result !== false) {
                $publisher_database = $dbname;
                error_log("Publishers API: publisher_info tablosu $dbname veritabanında bulundu (kayıt sayısı: " . $result['count'] . ")");
                break;
            }
        } catch (Exception $e) {
            error_log("Publishers API: $dbname veritabanı kontrol edilemedi: " . $e->getMessage());
            continue;
        }
    }
    
    if (!$publisher_database) {
        // Son çare: direkt tuberaja_yayinci_takip kullan
        error_log("Publishers API: Auto-detection başarısız, tuberaja_yayinci_takip kullanılacak");
        $publisher_database = 'tuberaja_yayinci_takip';
        
        // Test et
        try {
            $testQuery = "SELECT COUNT(*) as count FROM $publisher_database.publisher_info LIMIT 1";
            error_log("Publishers API: Test query: " . $testQuery);
            $stmt = $db->prepare($testQuery);
            $stmt->execute();
            $result = $stmt->fetch();
            error_log("Publishers API: Direct test başarılı - kayıt sayısı: " . $result['count']);
        } catch (Exception $e) {
            error_log("Publishers API: Direct test başarısız: " . $e->getMessage());
            error_log("Publishers API: SQL Error Code: " . $e->getCode());
            error_log("Publishers API: SQL Error Info: " . print_r($db->errorInfo(), true));
            
            // Basit bir test daha yapalım
            try {
                $simpleTest = $db->query("SELECT 1");
                error_log("Publishers API: Basit DB testi başarılı");
            } catch (Exception $e2) {
                error_log("Publishers API: Basit DB testi başarısız: " . $e2->getMessage());
            }
            
            jsonResponse([
                'success' => false,
                'error' => 'Tablo bulunamadı',
                'message' => 'Publisher verileri bulunamadı: ' . $e->getMessage(),
                'debug_info' => [
                    'tried_databases' => $databases_to_try,
                    'final_error' => $e->getMessage(),
                    'error_code' => $e->getCode(),
                    'db_error_info' => $db->errorInfo()
                ]
            ], 500);
        }
    }

    // GET: Yayıncıları listele
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $cacheKey = 'all_' . (isset($_GET['onlyActive']) ? 'active' : 'full');
        
        // Cache kontrolü
        $cachedData = getCachedData($cacheKey, 30);
        if ($cachedData) {
            error_log("Publishers API: Cache'den veri döndürülüyor");
            jsonResponse($cachedData);
        }

        try {
            // Database.table notation kullan (USE komutu yerine)
            $onlyActive = isset($_GET['onlyActive']) && $_GET['onlyActive'] == '1';
            
            // Ana yayıncı verilerini al
            $publisherQuery = "SELECT 
                id,
                isim_soyisim,
                username,
                mail,
                telefon,
                dogum_tarihi,
                sehir,
                meslek,
                profile_image,
                kayit_tarihi
            FROM $publisher_database.publisher_info 
            WHERE username IS NOT NULL AND username != ''
            ORDER BY id DESC";
            
            $publishersStmt = $db->prepare($publisherQuery);
            $publishersStmt->execute();
            $publishers = $publishersStmt->fetchAll(PDO::FETCH_ASSOC);
            
            error_log("Publishers API: " . count($publishers) . " yayıncı bulundu ($publisher_database veritabanından)");

            if (empty($publishers)) {
                jsonResponse([
                    'success' => true,
                    'data' => [],
                    'message' => 'Henüz yayıncı kaydı bulunmuyor',
                    'database' => $publisher_database
                ]);
            }

            // Basit yayıncı verilerini döndür (performans metriklerini şimdilik kaldırdık)
            $result = [];
            foreach ($publishers as $publisher) {
                // ID'yi string yap (frontend uyumluluğu için)
                $publisher['id'] = (string)$publisher['id'];
                
                // Null değerleri boş string yap
                foreach ($publisher as $key => $value) {
                    if ($value === null) {
                        $publisher[$key] = '';
                    }
                }
                
                    $result[] = $publisher;
            }

            $response = [
                'success' => true,
                'data' => $result,
                'total' => count($result),
                'database' => $publisher_database,
                'cached' => false,
                'timestamp' => date('Y-m-d H:i:s')
            ];
            
            // Cache'e kaydet
            setCachedData($cacheKey, $response);
            
            error_log("Publishers API: " . count($result) . " yayıncı verisi döndürülüyor");
            jsonResponse($response);

        } catch (PDOException $e) {
            error_log("Publishers API DB Hatası: " . $e->getMessage());
            jsonResponse([
                'success' => false,
                'error' => 'Veritabanı hatası',
                'message' => 'Yayıncı verileri alınırken hata oluştu',
                'database' => $publisher_database,
                'sql_error' => $e->getMessage()
            ], 500);
        }
    }

    // POST: Yeni yayıncı ekle
    elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                jsonResponse(['success' => false, 'error' => 'Geçersiz JSON verisi'], 400);
            }
            
            // Zorunlu alanları kontrol et
            $required = ['username'];
            foreach ($required as $field) {
                if (!isset($input[$field]) || empty($input[$field])) {
                    jsonResponse(['success' => false, 'error' => "$field alanı zorunludur"], 400);
                }
            }
            
            // Username benzersizliği kontrolü
            $checkStmt = $db->prepare("SELECT id FROM $publisher_database.publisher_info WHERE username = ?");
            $checkStmt->execute([$input['username']]);
            if ($checkStmt->rowCount() > 0) {
                jsonResponse(['success' => false, 'error' => 'Bu kullanıcı adı zaten kullanılıyor'], 400);
            }
            
            // Yeni yayıncı ekle (kayit_tarihi sütununu kullan, created_at yerine)
            $insertStmt = $db->prepare("
                INSERT INTO $publisher_database.publisher_info (isim_soyisim, username, mail, telefon, dogum_tarihi, sehir, meslek, kayit_tarihi) 
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $insertStmt->execute([
                $input['isim_soyisim'] ?? '',
                $input['username'],
                $input['mail'] ?? '',
                $input['telefon'] ?? '',
                $input['dogum_tarihi'] ?? null,
                $input['sehir'] ?? '',
                $input['meslek'] ?? ''
            ]);
            
            $newId = $db->lastInsertId();
            
            // Cache'i temizle
            array_map('unlink', glob(sys_get_temp_dir() . '/publishers_*.json'));
            
            jsonResponse([
                'success' => true,
                'id' => $newId,
                'message' => 'Yayıncı başarıyla eklendi',
                'database' => $publisher_database
            ]);
            
        } catch (PDOException $e) {
            error_log("Publishers API POST Hatası: " . $e->getMessage());
            jsonResponse([
                'success' => false, 
                'error' => 'Yayıncı eklenirken hata oluştu',
                'sql_error' => $e->getMessage(),
                'database' => $publisher_database
            ], 500);
        }
    }

    // PUT: Yayıncı güncelle
    elseif ($_SERVER['REQUEST_METHOD'] === 'PUT') {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input || !isset($input['id'])) {
                jsonResponse(['success' => false, 'error' => 'ID gerekli'], 400);
            }
            
            $id = (int)$input['id'];
            $allowedFields = ['isim_soyisim', 'username', 'mail', 'telefon', 'dogum_tarihi', 'sehir', 'meslek'];
            
            $updateFields = [];
            $params = [];
            
            foreach ($allowedFields as $field) {
                if (isset($input[$field])) {
                    $updateFields[] = "$field = ?";
                    $params[] = $input[$field];
                }
            }
            
            if (empty($updateFields)) {
                jsonResponse(['success' => false, 'error' => 'Güncellenecek alan yok'], 400);
            }
            
            $params[] = $id;
            $updateStmt = $db->prepare("UPDATE $publisher_database.publisher_info SET " . implode(', ', $updateFields) . " WHERE id = ?");
            $updateStmt->execute($params);
            
            // Cache'i temizle
            array_map('unlink', glob(sys_get_temp_dir() . '/publishers_*.json'));
            
            jsonResponse([
                'success' => true, 
                'message' => 'Yayıncı başarıyla güncellendi',
                'database' => $publisher_database
            ]);
            
        } catch (PDOException $e) {
            error_log("Publishers API PUT Hatası: " . $e->getMessage());
            jsonResponse([
                'success' => false, 
                'error' => 'Güncelleme sırasında hata oluştu',
                'sql_error' => $e->getMessage(),
                'database' => $publisher_database
            ], 500);
        }
    }

    // DELETE: Yayıncı sil
    elseif ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            $id = isset($_GET['id']) ? (int)$_GET['id'] : (isset($input['id']) ? (int)$input['id'] : null);
            
            if (!$id) {
                jsonResponse(['success' => false, 'error' => 'ID gerekli'], 400);
            }
            
            $deleteStmt = $db->prepare("DELETE FROM $publisher_database.publisher_info WHERE id = ?");
            $deleteStmt->execute([$id]);
            
            if ($deleteStmt->rowCount() === 0) {
                jsonResponse(['success' => false, 'error' => 'Yayıncı bulunamadı'], 404);
            }
            
            // Cache'i temizle
            array_map('unlink', glob(sys_get_temp_dir() . '/publishers_*.json'));
            
            jsonResponse([
                'success' => true, 
                'message' => 'Yayıncı başarıyla silindi',
                'database' => $publisher_database
            ]);
            
        } catch (PDOException $e) {
            error_log("Publishers API DELETE Hatası: " . $e->getMessage());
            jsonResponse([
                'success' => false, 
                'error' => 'Silme sırasında hata oluştu',
                'sql_error' => $e->getMessage(),
                'database' => $publisher_database
            ], 500);
        }
    }

    else {
        jsonResponse(['success' => false, 'error' => 'Desteklenmeyen HTTP metodu'], 405);
    }

} catch (Exception $e) {
    error_log("Publishers API Genel Hatası: " . $e->getMessage());
    error_log("Publishers API Genel Hata Trace: " . $e->getTraceAsString());
    error_log("Publishers API Genel Hata File: " . $e->getFile() . " Line: " . $e->getLine());
    
        jsonResponse([
            'success' => false,
            'error' => 'Sunucu hatası',
        'message' => 'Geçici bir hata oluştu: ' . $e->getMessage(),
        'debug_info' => [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
        ], 500);
}
?>