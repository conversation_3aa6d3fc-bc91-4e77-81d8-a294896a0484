<?php
// Hata raporlamasını etkinleştir
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// CORS başlıkları
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Origin, Content-Type, Accept, Authorization');

// OPTIONS isteği için erken yanıt
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(204);
    exit;
}

// Veritabanı bağlantısı
$host = 'localhost';
$dbname = 'tuberaja_yayinci_akademi';
$username = 'tuberaja_akademi';
$password = 'password123'; // Gerçek şifreyi kullanın

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Veritabanı bağlantı hatası: ' . $e->getMessage()
    ]);
    exit;
}

// İstek parametrelerini al
$endpoint = isset($_GET['endpoint']) ? $_GET['endpoint'] : '';
$method = $_SERVER['REQUEST_METHOD'];

// Endpoint'e göre işlem yap
switch ($endpoint) {
    case 'announcements':
        handleAnnouncements($db, $method);
        break;
    case 'feed':
        handleFeed($db, $method);
        break;
    case 'courses':
        handleCourses($db, $method);
        break;
    case 'events':
        handleEvents($db, $method);
        break;
    case 'like_post':
        handleLikePost($db, $method);
        break;
    case 'create_post':
        handleCreatePost($db, $method);
        break;
    default:
        http_response_code(404);
        echo json_encode([
            'status' => 'error',
            'message' => 'Endpoint bulunamadı: ' . $endpoint
        ]);
        break;
}

// Duyurular endpoint'i
function handleAnnouncements($db, $method) {
    if ($method === 'GET') {
        try {
            $query = "SELECT * FROM announcements WHERE status = 'active' ORDER BY created_at DESC";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $announcements = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'status' => 'success',
                'data' => $announcements
            ]);
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Duyurular alınırken hata oluştu: ' . $e->getMessage()
            ]);
        }
    } else {
        http_response_code(405);
        echo json_encode([
            'status' => 'error',
            'message' => 'Desteklenmeyen metod: ' . $method
        ]);
    }
}

// Akış endpoint'i
function handleFeed($db, $method) {
    if ($method === 'GET') {
        try {
            $query = "SELECT p.*, u.username, u.profile_image, u.role, 
                     (SELECT COUNT(*) FROM feed_likes WHERE post_id = p.id) as likes_count,
                     (SELECT COUNT(*) FROM feed_comments WHERE post_id = p.id) as comments_count
                     FROM feed_posts p
                     LEFT JOIN users u ON p.user_id = u.id
                     ORDER BY p.created_at DESC";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $posts = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'status' => 'success',
                'data' => $posts
            ]);
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Akış verileri alınırken hata oluştu: ' . $e->getMessage()
            ]);
        }
    } else {
        http_response_code(405);
        echo json_encode([
            'status' => 'error',
            'message' => 'Desteklenmeyen metod: ' . $method
        ]);
    }
}

// Eğitimler endpoint'i
function handleCourses($db, $method) {
    if ($method === 'GET') {
        try {
            $query = "SELECT * FROM courses WHERE status = 'active' ORDER BY created_at DESC";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'status' => 'success',
                'data' => $courses
            ]);
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Eğitimler alınırken hata oluştu: ' . $e->getMessage()
            ]);
        }
    } else {
        http_response_code(405);
        echo json_encode([
            'status' => 'error',
            'message' => 'Desteklenmeyen metod: ' . $method
        ]);
    }
}

// Etkinlikler endpoint'i
function handleEvents($db, $method) {
    if ($method === 'GET') {
        try {
            $query = "SELECT * FROM events WHERE status = 'active' ORDER BY start_date ASC";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $events = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'status' => 'success',
                'data' => $events
            ]);
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Etkinlikler alınırken hata oluştu: ' . $e->getMessage()
            ]);
        }
    } else {
        http_response_code(405);
        echo json_encode([
            'status' => 'error',
            'message' => 'Desteklenmeyen metod: ' . $method
        ]);
    }
}
