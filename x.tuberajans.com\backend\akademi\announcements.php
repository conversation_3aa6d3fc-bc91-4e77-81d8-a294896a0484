<?php
// <PERSON>a ayıklama
ini_set('display_errors', 1);
error_reporting(E_ALL);

// CORS ve JSON header'ları
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS, DELETE');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=utf-8');

// OPTIONS istekleri için hızlıca 200 dön
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Ortak config dosyasını dahil et
require_once __DIR__ . '/../config/config.php';

// JSON yanıt fonksiyonu
if (!function_exists('jsonResponse')) {
    function jsonResponse($data, $status = 200) {
        http_response_code($status);
header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}

// Auth kontrolü (checkAuth fonksiyonu - events.php'deki gibi)
if (!function_exists('checkAuth')) {
    function checkAuth() {
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
        
        if (strpos($authHeader, 'Bearer ') === 0) {
            $token = substr($authHeader, 7);
            if (strlen($token) > 10) { // Basit token kontrolü
                return true;
            }
        }
        return false;
    }
}

// Token doğrulaması
if ($_SERVER['REQUEST_METHOD'] !== 'OPTIONS') {
    if (!checkAuth()) {
        jsonResponse(['success' => false, 'message' => 'Bu işlemi gerçekleştirmek için giriş yapmalısınız.'], 401);
    exit;
    }
}

// Akademi veritabanı bağlantısı
try {
    $db_akademi = new PDO("mysql:host=$servername;dbname=tuberaja_yayinci_akademi;charset=utf8mb4", $db_username, $db_password);
    $db_akademi->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $db_akademi->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    jsonResponse(['success' => false, 'message' => 'Akademi veritabanı bağlantı hatası: ' . $e->getMessage()]);
}

// URL parametrelerini al
$action = $_GET['action'] ?? '';

// GET isteği - Duyuruları Listele
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {
        $query = "SELECT * FROM announcements ORDER BY created_at DESC";
        $stmt = $db_akademi->prepare($query);
        $stmt->execute();
        $announcements = $stmt->fetchAll(PDO::FETCH_ASSOC);

        jsonResponse(['success' => true, 'data' => $announcements]);
    } catch (PDOException $e) {
        error_log("Duyuru listeleme hatası: " . $e->getMessage());
        jsonResponse(['success' => false, 'message' => 'Duyurular listelenirken bir hata oluştu.']);
    }
}

// POST isteği - Duyuru Ekle/Güncelle/Sil
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Action'a göre işlem belirle
        switch ($action) {
            case 'add':
        // Duyuru Ekleme
            $input = json_decode(file_get_contents('php://input'), true);

            // Eğer JSON yoksa $_POST'u kontrol et
            if (!$input) {
                $input = $_POST;
            }

            $title = $input['title'] ?? '';
            $content = $input['content'] ?? '';
            $type = $input['type'] ?? 'general';
            $status = $input['status'] ?? 'active';
            $expire_date = !empty($input['expire_date']) ? $input['expire_date'] : null;

            // Debug için log ekle
            error_log("Duyuru ekleme - Title: " . $title . ", Content: " . $content . ", Type: " . $type . ", Status: " . $status);

            // Görsel yükleme
            $image_path = null;
            if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
                $uploads_dir = '../../uploads/announcements/';
                if (!is_dir($uploads_dir)) {
                    mkdir($uploads_dir, 0755, true);
                }

                $tmp_name = $_FILES['image']['tmp_name'];
                $name = basename($_FILES['image']['name']);
                $file_extension = strtolower(pathinfo($name, PATHINFO_EXTENSION));
                $new_file_name = uniqid() . '.' . $file_extension;

                if (move_uploaded_file($tmp_name, $uploads_dir . $new_file_name)) {
                    $image_path = 'uploads/announcements/' . $new_file_name;
                }
            }

                // Veritabanına ekle - user_id kısmını basitleştir
            $query = "INSERT INTO announcements
                      (title, content, type, status, image, expire_date, created_by, created_at, updated_at)
                          VALUES (?, ?, ?, ?, ?, ?, 1, NOW(), NOW())";
            $stmt = $db_akademi->prepare($query);
                $stmt->execute([$title, $content, $type, $status, $image_path, $expire_date]);

            $announcement_id = $db_akademi->lastInsertId();

                jsonResponse(['success' => true, 'message' => 'Duyuru başarıyla eklendi.', 'id' => $announcement_id]);
                break;

            case 'update':
        // Duyuru Güncelleme
            $input = json_decode(file_get_contents('php://input'), true);

            // Eğer JSON yoksa $_POST'u kontrol et
            if (!$input) {
                $input = $_POST;
            }

            $id = $input['id'] ?? 0;
            $title = $input['title'] ?? '';
            $content = $input['content'] ?? '';
            $type = $input['type'] ?? 'general';
            $status = $input['status'] ?? 'active';
            $expire_date = !empty($input['expire_date']) ? $input['expire_date'] : null;

            // Mevcut duyuruyu çek
            $query = "SELECT image FROM announcements WHERE id = ?";
            $stmt = $db_akademi->prepare($query);
            $stmt->execute([$id]);
            $announcement = $stmt->fetch(PDO::FETCH_ASSOC);

            // Görsel yükleme
            $image_path = $announcement['image'] ?? null;
            if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
                $uploads_dir = '../../uploads/announcements/';
                if (!is_dir($uploads_dir)) {
                    mkdir($uploads_dir, 0755, true);
                }

                // Eski görseli sil
                if ($image_path && file_exists('../../' . $image_path)) {
                    unlink('../../' . $image_path);
                }

                $tmp_name = $_FILES['image']['tmp_name'];
                $name = basename($_FILES['image']['name']);
                $file_extension = strtolower(pathinfo($name, PATHINFO_EXTENSION));
                $new_file_name = uniqid() . '.' . $file_extension;

                if (move_uploaded_file($tmp_name, $uploads_dir . $new_file_name)) {
                    $image_path = 'uploads/announcements/' . $new_file_name;
                }
            }

            // Veritabanını güncelle
            $query = "UPDATE announcements
                      SET title = ?, content = ?, type = ?, status = ?, image = ?, expire_date = ?, updated_at = NOW()
                      WHERE id = ?";
            $stmt = $db_akademi->prepare($query);
            $stmt->execute([$title, $content, $type, $status, $image_path, $expire_date, $id]);

                jsonResponse(['success' => true, 'message' => 'Duyuru başarıyla güncellendi.']);
                break;

            case 'delete':
        // Duyuru Silme
            $data = json_decode(file_get_contents('php://input'), true);
            $id = $data['id'] ?? 0;

            // Görseli silmek için mevcut duyuruyu çek
            $query = "SELECT image FROM announcements WHERE id = ?";
            $stmt = $db_akademi->prepare($query);
            $stmt->execute([$id]);
            $announcement = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($announcement && $announcement['image'] && file_exists('../../' . $announcement['image'])) {
                unlink('../../' . $announcement['image']);
            }

            // Duyuruyu sil
            $query = "DELETE FROM announcements WHERE id = ?";
            $stmt = $db_akademi->prepare($query);
            $stmt->execute([$id]);

                jsonResponse(['success' => true, 'message' => 'Duyuru başarıyla silindi.']);
                break;

            default:
                jsonResponse(['success' => false, 'message' => 'Geçersiz işlem.']);
                break;
        }

    } catch (PDOException $e) {
        error_log("Duyuru işlem hatası: " . $e->getMessage());
        jsonResponse(['success' => false, 'message' => 'İşlem sırasında bir hata oluştu.']);
    } catch (Exception $e) {
        error_log("Genel hata: " . $e->getMessage());
        jsonResponse(['success' => false, 'message' => 'Bir hata oluştu.']);
    }
}

jsonResponse(['success' => false, 'message' => 'Geçersiz istek metodu']);