<?php
/**
 * Weekly Archive API - Optimize Edilmiş
 * Haftalık performans arşivi için hızlı ve cache'li API
 */

// Hızlı ayarlar
set_time_limit(8);
ini_set('memory_limit', '128M');

// Headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// OPTIONS kontrolü
if (($_SERVER['REQUEST_METHOD'] ?? '') === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Config dosyalarını dahil et
require_once __DIR__ . '/../config/config.php';

// Hata logları
error_log("Weekly Archive API çağrıldı - " . date('Y-m-d H:i:s'));

// JSON yanıt fonksiyonu
if (!function_exists('jsonResponse')) {
    function jsonResponse($data, $status = 200) {
        http_response_code($status);
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
}

// Cache fonksiyonları
function getCachedArchive($key, $ttl = 300) { // 5 dakika cache
    $cacheFile = sys_get_temp_dir() . "/weekly_archive_$key.json";
    if (file_exists($cacheFile) && (time() - filemtime($cacheFile)) < $ttl) {
        return json_decode(file_get_contents($cacheFile), true);
    }
    return null;
}

function setCachedArchive($key, $data) {
    $cacheFile = sys_get_temp_dir() . "/weekly_archive_$key.json";
    file_put_contents($cacheFile, json_encode($data, JSON_UNESCAPED_UNICODE));
}

try {
    // GET: Haftalık arşiv verilerini getir
    $requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';
    if ($requestMethod === 'GET') {
        
        // Özel endpoint'ler
        if (isset($_GET['username'])) {
            // Belirli kullanıcının haftalık istatistikleri
            $username = $_GET['username'];
            $count = isset($_GET['count']) ? intval($_GET['count']) : 4;
            
            $cacheKey = md5("user_$username" . "_$count");
            $cachedData = getCachedArchive($cacheKey, 120);
            if ($cachedData) {
                jsonResponse($cachedData);
            }

            $stmt = $db->prepare("
                SELECT *
                FROM weekly_archive
                WHERE kullanici_adi = ?
                ORDER BY hafta_baslangici DESC
                LIMIT ?
            ");
            $stmt->bindValue(1, $username, PDO::PARAM_STR);
            $stmt->bindValue(2, $count, PDO::PARAM_INT);
            $stmt->execute();
            $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $response = [
                'success' => true,
                'data' => $rows,
                'username' => $username,
                'count' => count($rows)
            ];
            
            setCachedArchive($cacheKey, $response);
            jsonResponse($response);
        }
        
        if (isset($_GET['action']) && $_GET['action'] === 'active_publishers') {
            // Sadece aktif yayıncıların haftalık verileri
            $cacheKey = 'active_publishers';
            $cachedData = getCachedArchive($cacheKey, 180);
            if ($cachedData) {
                jsonResponse($cachedData);
            }

            $stmt = $db->prepare("
                SELECT wa.*
                FROM weekly_archive wa
                INNER JOIN publisher_info pi ON wa.kullanici_adi = pi.username
                WHERE wa.kullanici_adi IS NOT NULL AND wa.kullanici_adi != ''
                ORDER BY wa.hafta_baslangici DESC, wa.kullanici_adi ASC
            ");
            $stmt->execute();
            $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $response = [
                'success' => true,
                'data' => $rows,
                'total' => count($rows)
            ];
            
            setCachedArchive($cacheKey, $response);
            jsonResponse($response);
        }

        // Auth kontrolü (normal endpoint'ler için)
        if (!checkAuth()) {
            error_log("Weekly Archive API: Auth başarısız");
            jsonResponse(['success' => false, 'error' => 'Geçersiz token'], 401);
        }
        error_log("Weekly Archive API: Auth başarılı");

        try {
            // Parametreleri al ve normalize et
            $startDate = $_GET['start_date'] ?? date('Y-m-d', strtotime('monday this week'));
            $endDate = $_GET['end_date'] ?? date('Y-m-d', strtotime('sunday this week'));

            // Pazartesi-Pazar formatına getir
            $startDateTime = new DateTime($startDate);
            $dayOfWeek = $startDateTime->format('N'); // 1=Pazartesi, 7=Pazar

            if ($dayOfWeek != 1) {
                if ($dayOfWeek == 7) {
                    // Pazar günüyse bir sonraki Pazartesi'ye
                    $startDateTime->modify('+1 day');
                } else {
                    // Diğer günlerde o haftanın Pazartesi'sine
                    $startDateTime->modify('-' . ($dayOfWeek - 1) . ' days');
                }
            }

            $weekStartFormatted = $startDateTime->format('Y-m-d');
            $weekEndObj = clone $startDateTime;
            $weekEndObj->modify('+6 days');
            $weekEndFormatted = $weekEndObj->format('Y-m-d');

            error_log("Weekly Archive API: Normalized week: $weekStartFormatted to $weekEndFormatted");

            // Cache anahtarı
            $cacheKey = md5("archive_$weekStartFormatted");
            $cachedData = getCachedArchive($cacheKey, 240);
            if ($cachedData) {
                error_log("Weekly Archive API: Cache'den veri döndürülüyor");
                jsonResponse($cachedData);
            }

            // Veritabanı bağlantısı kontrolü
            if (!isset($db) || !$db) {
                throw new Exception("Veritabanı bağlantısı bulunamadı");
            }

            // Optimize edilmiş sorgu - sadece istenen hafta ve aktif yayıncılar
            $query = "SELECT 
                wa.id,
                wa.kullanici_adi,
                wa.canli_yayin_gunu,
                wa.yayin_suresi,
                wa.elmaslar,
                wa.yeni_takipciler,
                wa.aboneler,
                wa.maclar,
                wa.hafta_baslangici,
                wa.hafta_bitisi,
                pi.isim_soyisim,
                pi.mail,
                pi.telefon,
                pi.sehir
            FROM weekly_archive wa
            INNER JOIN publisher_info pi ON wa.kullanici_adi = pi.username
            WHERE DATE(wa.hafta_baslangici) = ?
            ORDER BY 
                wa.canli_yayin_gunu DESC,
                wa.yayin_suresi DESC,
                wa.kullanici_adi ASC";

            $stmt = $db->prepare($query);
            $stmt->execute([$weekStartFormatted]);
            $performanceData = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Veri işleme ve düzenleme
            $processedData = [];
            foreach ($performanceData as $record) {
                // Null değerleri düzenle
                foreach ($record as $key => $value) {
                    if ($value === null) {
                        $record[$key] = $key === 'isim_soyisim' || $key === 'mail' || $key === 'telefon' || $key === 'sehir' ? '' : 0;
                    }
                }

                // Numeric alanları integer'a çevir
                $numericFields = ['id', 'canli_yayin_gunu', 'yayin_suresi', 'elmaslar', 'yeni_takipciler', 'aboneler', 'maclar'];
                foreach ($numericFields as $field) {
                    $record[$field] = (int)($record[$field] ?? 0);
                }

                // Performans skorunu hesapla
                $record['performance_score'] = calculatePerformanceScore($record);
                
                $processedData[] = $record;
            }

            // İstatistikleri hesapla
            $stats = [
                'total_publishers' => count($processedData),
                'active_publishers' => count(array_filter($processedData, fn($p) => $p['canli_yayin_gunu'] > 0)),
                'total_live_days' => array_sum(array_column($processedData, 'canli_yayin_gunu')),
                'total_stream_time' => array_sum(array_column($processedData, 'yayin_suresi')),
                'total_diamonds' => array_sum(array_column($processedData, 'elmaslar')),
                'total_new_followers' => array_sum(array_column($processedData, 'yeni_takipciler')),
                'total_subscribers' => array_sum(array_column($processedData, 'aboneler')),
                'total_matches' => array_sum(array_column($processedData, 'maclar')),
                'avg_stream_time' => count($processedData) > 0 ? round(array_sum(array_column($processedData, 'yayin_suresi')) / count($processedData), 2) : 0
            ];

            // Hafta bilgileri
            $weekInfo = [
                'start_date' => $weekStartFormatted,
                'end_date' => $weekEndFormatted,
                'formatted_date' => $startDateTime->format('d M') . ' - ' . $weekEndObj->format('d M Y'),
                'week_name' => 'Hafta ' . $startDateTime->format('W'),
                'year' => $startDateTime->format('Y'),
                'has_next_week' => strtotime($weekStartFormatted . ' +7 days') <= strtotime(date('Y-m-d')),
                'has_previous_week' => true,
                'next_week_date' => date('Y-m-d', strtotime($weekStartFormatted . ' +7 days')),
                'previous_week_date' => date('Y-m-d', strtotime($weekStartFormatted . ' -7 days'))
            ];

            $response = [
                'success' => true,
                'data' => $processedData,
                'stats' => $stats,
                'week_info' => $weekInfo,
                'cached' => false,
                'timestamp' => date('Y-m-d H:i:s')
            ];

            // Cache'e kaydet
            setCachedArchive($cacheKey, $response);

            error_log("Weekly Archive API: " . count($processedData) . " performans kaydı döndürülüyor");
            jsonResponse($response);

        } catch (PDOException $e) {
            error_log("Weekly Archive API DB Hatası: " . $e->getMessage());
            jsonResponse([
                'success' => false,
                'error' => 'Veritabanı hatası',
                'message' => 'Haftalık veriler alınırken hata oluştu'
            ], 500);
        }
    }

    else {
        jsonResponse(['success' => false, 'error' => 'Desteklenmeyen HTTP metodu: ' . $requestMethod], 405);
    }

} catch (Exception $e) {
    error_log("Weekly Archive API Genel Hatası: " . $e->getMessage());
    
    jsonResponse([
        'success' => false,
        'error' => 'Sunucu hatası',
        'message' => 'Geçici bir hata oluştu, lütfen tekrar deneyin'
    ], 500);
}

// Performans skoru hesaplama yardımcı fonksiyonu
function calculatePerformanceScore($record) {
    $score = 0;
    
    // Canlı yayın günü (max 40 puan)
    $score += min($record['canli_yayin_gunu'] * 10, 40);
    
    // Yayın süresi (saniye cinsinden, max 25 puan)
    $hours = $record['yayin_suresi'] / 3600;
    $score += min($hours * 2, 25);
    
    // Elmaslar (max 20 puan)
    $score += min($record['elmaslar'] / 1000, 20);
    
    // Yeni takipçiler (max 10 puan)
    $score += min($record['yeni_takipciler'] / 100, 10);
    
    // Maçlar (max 5 puan)
    $score += min($record['maclar'] * 1, 5);
    
    return round($score, 1);
}
?>