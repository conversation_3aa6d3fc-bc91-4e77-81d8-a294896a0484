<?php
require_once __DIR__ . '/../config/config.php';

try {
    // Haftalık görevleri kontrol et
    $stmt = $db->prepare("SELECT COUNT(*) as total_tasks FROM weekly_tasks");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Toplam haftalık görev sayısı: " . $result['total_tasks'] . "\n";
    
    // Son 10 görevi göster
    $stmt = $db->prepare("SELECT * FROM weekly_tasks ORDER BY id DESC LIMIT 10");
    $stmt->execute();
    $tasks = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\nSon 10 görev:\n";
    foreach ($tasks as $task) {
        echo "ID: {$task['id']}, Kullanıcı: {$task['kullanici_adi']}, Hafta: {$task['hafta_baslangici']}, Görev: {$task['gorev_onerisi']}\n";
    }
    
    // Belirli bir kullanıcının görevlerini kontrol et
    if (isset($_GET['username'])) {
        $username = $_GET['username'];
        $stmt = $db->prepare("SELECT * FROM weekly_tasks WHERE kullanici_adi = ? ORDER BY hafta_baslangici DESC");
        $stmt->execute([$username]);
        $userTasks = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "\n{$username} kullanıcısının görevleri:\n";
        foreach ($userTasks as $task) {
            echo "Hafta: {$task['hafta_baslangici']}, Görev: {$task['gorev_onerisi']}, Zorluk: {$task['gorev_zorlugu']}\n";
        }
    }
    
} catch (PDOException $e) {
    echo "Hata: " . $e->getMessage();
}
?>
