# HTTP'den HTTPS'ye yönlendirme - Geçici olarak devre dışı
# <IfModule mod_rewrite.c>
#     RewriteEngine On
#     RewriteCond %{HTTPS} off
#     RewriteRule ^ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
# </IfModule>

# X-Tuber Ajans Backend .htaccess
<IfModule mod_rewrite.c>
    RewriteEngine On

    # Başlangıç dizinini belirle
    RewriteBase /backend/x-site/

    # site/ dizinindeki talep dosyalarını dashboard-metrics.php'ye yönlendir
    RewriteRule ^site/basvurular\.php$ dashboard-metrics.php [L]
    RewriteRule ^site/iletisimtalepleri\.php$ dashboard-metrics.php [L]
    RewriteRule ^site/geriaramatalep\.php$ dashboard-metrics.php [L]
    RewriteRule ^site/onlinetoplantitalep\.php$ dashboard-metrics.php [L]

    # ETSY isteklerini dashboard-metrics.php'ye yönlendir
    RewriteRule ^etsy-dashboard\.php$ dashboard-metrics.php [L]

    # WhatsApp isteklerini dashboard-metrics.php'ye yönlendir
    RewriteRule ^whatsapp-stats\.php$ dashboard-metrics.php [L]

    # Akademi isteklerini dashboard-metrics.php'ye yönlendir
    RewriteRule ^akademi-dashboard\.php$ dashboard-metrics.php [L]

    # OPTIONS isteklerini hemen yanıtla (CORS preflight)
    RewriteCond %{REQUEST_METHOD} OPTIONS
    RewriteRule ^(.*)$ $1 [R=204,L]

    # PHP dosyalarına doğrudan erişimi engelle (.php uzantılı dosyalar hariç)
    RewriteCond %{REQUEST_URI} \.(php|env|ini|log|sh|inc|bak)$ [NC]
    RewriteCond %{REQUEST_URI} !^/backend/x-site/.*\.php$
    RewriteRule .* - [F,L]

    # Gizli dosyalara erişimi engelle
    RewriteCond %{REQUEST_URI} "^\.|/\." [NC]
    RewriteRule .* - [F,L]
</IfModule>

# CORS Ayarları - Development için geçici olarak tüm originlere izin ver
<IfModule mod_headers.c>
    # CORS başlıkları - Development mode
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control"
    Header always set Access-Control-Max-Age "86400"

    # Güvenlik başlıkları
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"

    # Content Security Policy - Güncellenmiş
    Header always set Content-Security-Policy "default-src 'self' https://*.tuberajans.com; script-src 'self' 'unsafe-inline' 'unsafe-eval'; connect-src 'self' https://*.tuberajans.com wss://*.tuberajans.com; img-src 'self' data: https:; style-src 'self' 'unsafe-inline'; font-src 'self' data:; frame-ancestors 'self'; form-action 'self';"

    # Cache-Control
    <FilesMatch "\.(php)$">
        Header set Cache-Control "no-store, no-cache, must-revalidate, max-age=0"
        Header set Pragma "no-cache"
        Header set Expires "Thu, 1 Jan 1970 00:00:00 GMT"
    </FilesMatch>
</IfModule>

# PHP Ayarları - Development mode
php_flag display_errors on
php_value error_reporting E_ALL
php_value max_execution_time 60
php_value memory_limit 256M
php_value post_max_size 64M
php_value upload_max_filesize 64M
php_flag session.cookie_httponly on
php_flag session.use_only_cookies on
php_flag session.cookie_secure on

# Dizin listelemeyi devre dışı bırak
Options -Indexes

# Varsayılan karakter seti
AddDefaultCharset UTF-8

# Dosya türleri için MIME türlerini ayarla
<IfModule mod_mime.c>
    AddType application/json .json
    AddType application/javascript .js
</IfModule>

# Gzip sıkıştırma
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css application/javascript application/json application/xml
</IfModule>