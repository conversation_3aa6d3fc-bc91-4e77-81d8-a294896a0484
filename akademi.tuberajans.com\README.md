# Tuber X-Akademi React Projesi

Bu proje React, TypeScript, TailwindCSS ve Vite kullanılarak geliştirilmiştir.

## Kurulum

Projeyi kurmak için aşağıdaki adımları izleyin:

```bash
# Bağımlılıkları yükleyin
npm install

# Geliştirme sunucusunu başlatın
npm run dev
```

## Build

Projeyi derlemek için:

```bash
npm run build
```

## Önizleme

Build edilen projeyi önizlemek için:

```bash
npm run preview
```

## Proje Yapısı

- `src/` - <PERSON>nak kodlar
  - `App.tsx` - Ana <PERSON>ygulam<PERSON> bi<PERSON>
  - `main.tsx` - Uygulama başlangıç noktası
  - `index.css` - Genel CSS ve TailwindCSS
- `public/` - Statik dosyalar
