/**
 * DirectFetch - Axios'a alternatif, <PERSON><PERSON><PERSON><PERSON> fetch API kullanan basit HTTP istemcisi
 * 
 * Auth.php gibi kritik API'lerde sorun yaşandığında fallback olarak kullanılabilir
 */

import { API_CONFIG, SECURITY_CONFIG } from '../config';
import { toast } from 'react-hot-toast';

class DirectFetchClient {
  baseUrl: string;
  defaultHeaders: Record<string, string>;
  
  constructor() {
    this.baseUrl = API_CONFIG.BASE_URL;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Cache-Control': 'no-cache, no-store, must-revalidate'
    };
  }
  
  /**
   * GET isteği gönderir
   */
  async get(endpoint: string, options: RequestInit = {}) {
    return this.request(endpoint, {
      method: 'GET',
      ...options
    });
  }
  
  /**
   * POST isteği gönderir
   */
  async post(endpoint: string, data: any, options: RequestInit = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
      ...options
    });
  }
  
  /**
   * <PERSON><PERSON> fetch işlemi gerçekleştirir
   */
  async request(endpoint: string, options: RequestInit = {}) {
    try {
      // URL hazırla
      const url = endpoint.startsWith('http') ? endpoint : `${this.baseUrl}${endpoint}`;
      console.log(`[DirectFetch] İstek başlatılıyor: ${options.method} ${url}`);
      
      // Token'ı localStorage'dan al
      const token = localStorage.getItem(SECURITY_CONFIG.TOKEN_KEY);
      
      // Headers hazırla
      const customHeaders: Record<string, string> = {
        ...this.defaultHeaders,
        ...(options.headers as Record<string, string> || {})
      };
      
      // Token varsa ekle
      if (token) {
        customHeaders['Authorization'] = `Bearer ${token}`;
      }
      
      // Fetch isteği gönder
      const response = await fetch(url, {
        ...options,
        headers: customHeaders,
        credentials: 'omit' // CORS için önemli - cookies gönderme
      });
      
      // Yanıt tipini kontrol et
      const contentType = response.headers.get('content-type');
      
      // JSON yanıt
      if (contentType && contentType.includes('application/json')) {
        const data = await response.json();
        
        if (!response.ok) {
          throw new Error(data.error || `Sunucu hatası: ${response.status}`);
        }
        
        return { status: response.status, data };
      } 
      
      // Text yanıt
      if (contentType && contentType.includes('text')) {
        const text = await response.text();
        
        if (!response.ok) {
          throw new Error(`Sunucu hatası: ${response.status}`);
        }
        
        return { status: response.status, data: text };
      }
      
      // Diğer yanıt tipleri
      if (!response.ok) {
        throw new Error(`Sunucu hatası: ${response.status}`);
      }
      
      return { status: response.status, data: null };
      
    } catch (error: any) {
      console.error(`[DirectFetch] Hata:`, error);
      
      // Hata mesajını hazırla
      const errorMessage = error.message || 'Bilinmeyen hata';
      
      // Network hatası
      if (errorMessage.includes('NetworkError') || errorMessage.includes('Failed to fetch')) {
        toast.error('Ağ bağlantısı hatası. İnternet bağlantınızı kontrol edin.');
      } 
      // 401 Unauthorized 
      else if (errorMessage.includes('401')) {
        toast.error('Oturum süreniz dolmuş. Lütfen tekrar giriş yapın.');
        localStorage.removeItem(SECURITY_CONFIG.TOKEN_KEY);
        
        // Login sayfasına yönlendir
        setTimeout(() => {
          window.location.href = '/login';
        }, 1500);
      }
      
      throw error;
    }
  }
  
  /**
   * Auth.php için özel fetch işlemi
   */
  async login(email: string, password: string) {
    try {
      console.log("[DirectFetch] Login isteği gönderiliyor");
      
      // Direkt login endpoint'ine istek gönder
      const url = `${API_CONFIG.X_SITE_BASE_URL}/auth.php`;
      
      // AbortController tanımla (timeout için)
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 saniye timeout
      
      const response = await fetch(url, {
        method: 'POST',
        headers: this.defaultHeaders,
        body: JSON.stringify({ email, password }),
        credentials: 'omit',
        signal: controller.signal
      });
      
      // Timeout'u temizle
      clearTimeout(timeoutId);
      
      // Yanıtı işle
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || `Giriş başarısız: ${response.status}`);
      }
      
      console.log("[DirectFetch] Login başarılı");
      return { status: response.status, data };
      
    } catch (error: any) {
      console.error("[DirectFetch] Login hatası:", error);
      
      if (error.name === 'AbortError') {
        throw new Error('İstek zaman aşımına uğradı');
      }
      
      throw error;
    }
  }
  
  /**
   * Auth kontrolü için özel fetch işlemi
   */
  async checkAuth(token: string) {
    try {
      console.log("[DirectFetch] Auth kontrolü yapılıyor");
      
      // Auth check endpoint'ine istek gönder
      const url = `${API_CONFIG.X_SITE_BASE_URL}/auth.php?check=true`;
      
      // AbortController tanımla (timeout için)
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 saniye timeout
      
      const customHeaders: Record<string, string> = {
        ...this.defaultHeaders,
        'Authorization': `Bearer ${token}`
      };
      
      const response = await fetch(url, {
        method: 'GET',
        headers: customHeaders,
        credentials: 'omit',
        signal: controller.signal
      });
      
      // Timeout'u temizle
      clearTimeout(timeoutId);
      
      // Yanıtı işle
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || `Auth kontrolü başarısız: ${response.status}`);
      }
      
      console.log("[DirectFetch] Auth kontrolü başarılı");
      return { status: response.status, data };
      
    } catch (error: any) {
      console.error("[DirectFetch] Auth kontrolü hatası:", error);
      
      if (error.name === 'AbortError') {
        throw new Error('İstek zaman aşımına uğradı');
      }
      
      throw error;
    }
  }
}

// Singleton instance oluştur
const directFetch = new DirectFetchClient();

export default directFetch; 