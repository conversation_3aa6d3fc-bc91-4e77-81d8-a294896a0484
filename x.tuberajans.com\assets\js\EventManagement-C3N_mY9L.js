import{j as e}from"./reactDnd-uQSTYBkW.js";import{r as n,u as Je}from"./vendor-CnpYymF8.js";import{h as u}from"./utils-CtuI0RRe.js";import{o as b,x as r,X as oe,n as ce,q as G,j as de,a0 as y,A as me,z as g,k as o,I as Qe,$ as z,t as T,a7 as Xe,T as ei,G as ii,H as ue,m as ai,u as xe,J as je,O as si}from"./antd-BfejY-CV.js";import{c as ti,v as ni,i as he,h as pe,s as ye,r as li,V as M,w as ri}from"./App-C_UskXOj.js";import"./tr-DCp4gmgI.js";import{f as oi,a as ci,b as ke,d as di}from"./event-api-bN5CW_JB.js";import mi from"./AIEventAdvisor-dL74DSwF.js";import{R as ui}from"./FireOutlined-DqgQLVwZ.js";import{R as hi}from"./SaveOutlined-BlaarVwu.js";import{R as pi}from"./ExclamationCircleOutlined-D-Q2kGg7.js";import"./index-BVn_ohNQ.js";import"./charts-6B1FLgFz.js";const{Title:yi,Text:ge,Paragraph:Ji}=ei,{TabPane:F}=G,{RangePicker:Qi}=si,{Option:Xi}=xe,{TextArea:fe}=Qe;u.locale("tr");je.locale("tr");const ea=()=>{const[be,v]=n.useState([]),[Me,W]=n.useState(!1),[S,ve]=n.useState([]),[ki,gi]=n.useState(!1),[fi,xi]=n.useState(!1),[ji,bi]=n.useState(!1),[Mi,vi]=n.useState(null),[Se,E]=n.useState(!1),[q]=b.useForm(),[Si,Ei]=n.useState("lider_tablosu"),[wi,Ai]=n.useState([]),[Ci,_i]=n.useState(!1),[zi]=b.useForm(),[Ti,Di]=n.useState("whatsapp"),[$i,Ii]=n.useState(!1),[Ee,w]=n.useState(!1),[f,O]=n.useState(!1),[x,U]=n.useState(!1),[h,D]=n.useState([]),[we,Z]=n.useState(null),[Ae,$]=n.useState(!1),[Ce,I]=n.useState(!1),[l,_e]=n.useState(null),[Pi,Ri]=n.useState([]),[Bi,ze]=n.useState([]),[A,P]=n.useState(""),[k,R]=n.useState([]),[J,B]=n.useState([]),[Te,Q]=n.useState(!1),[De,K]=n.useState(!1),[j,Y]=n.useState(null),[$e,X]=n.useState(!1),Ie=Je();n.useEffect(()=>{ee(),ie()},[]);const ee=async()=>{W(!0);try{const i=await oi();Array.isArray(i)?v(i):i&&Array.isArray(i.data)?v(i.data):v([])}catch(i){r.error("Etkinlik verileri alınamadı"),v([])}finally{W(!1)}},ie=async()=>{try{const i=await ci();ve(Array.isArray(i)?i:[])}catch(i){r.error("Yayıncı verileri alınamadı")}},ae=async i=>{_e(i),I(!0),Q(!0);try{S.length===0&&await ie();const a=await ke(i.id);if(!Array.isArray(a)||a.length===0)r.info("Bu etkinlik için eşleşme bulunamadı"),B([]);else{const t=a.map(s=>({...s,yayinci1_username:String(s.yayinci1_username),yayinci2_username:String(s.yayinci2_username)}));B(t)}}catch(a){r.error("Etkinlik eşleşmeleri yüklenirken bir hata oluştu"),B([])}finally{Q(!1)}},se=i=>{Ie(`/tournament/${i}`)},Pe=i=>{let a=[],t=!1;i.yayincilar&&i.yayincilar.length>0?(a=i.yayincilar.map(c=>C(c)).filter(c=>c!==void 0),a.length===0&&(r.warning("Katılımcı yayıncı bilgileri bulunamadı. Örnek verilerle devam ediliyor."),t=!0)):(r.info("Etkinlikte katılımcı bulunmuyor. Örnek verilerle modal açılıyor."),t=!0),t&&(a=[{id:"mock1",isim_soyisim:"Ali Veli",username:"alive",telefon:"5551112233"},{id:"mock2",isim_soyisim:"Ayşe Yılmaz",username:"aysey",telefon:"5554445566"},{id:"mock3",isim_soyisim:"Test Kullanıcı",username:"testkullanici",telefon:void 0}]),ze(a),R(a.map(c=>c.id));const s=`📢 ETKİNLİK DUYURUSU: ${i.etkinlik_adi}

Etkinlikle ilgili önemli bir duyurumuz var: [Duyurunuzu buraya yazın]

🗓️ Hatırlatma - Başlangıç: ${u(i.baslangic_tarihi).format("DD MMMM YYYY, HH:mm")}
🏁 Hatırlatma - Bitiş: ${u(i.bitis_tarihi).format("DD MMMM YYYY, HH:mm")}

Başarılar dileriz!
TuberAjans Ekibi`;P(s.replace(N,"")),Z(i.id),q.setFieldsValue({eventName:i.etkinlik_adi}),E(!0)},Re=async()=>{if(k.length===0){r.warning("Gönderilecek alıcı seçilmedi.");return}if(!A||A.trim()===""){r.warning("Gönderilecek mesaj boş olamaz.");return}U(!0);let i=0,a=0;for(const t of k){const s=C(t);if(!s){a++;continue}`${s.isim_soyisim||s.username}`,s.telefon&&s.telefon.trim()!==""&&await ne(s.telefon)?i++:a++}U(!1),i>0&&M.success(`${i} duyuru başarıyla gönderildi (simülasyon).`),a>0&&M.error(`${a} duyuru gönderilemedi veya alıcının telefon numarası yok.`),a===0&&(E(!1),R([]),P(""))},V=i=>{const a=S.find(t=>t.username===i);return a?a.isim_soyisim||a.username:i},C=i=>S.find(a=>a.username===i),Be=u(),te=be.map(i=>{const a=u(i.bitis_tarihi).isBefore(Be,"day");return{...i,durum:a?"tamamlandi":i.durum}}),H=te.filter(i=>i.durum==="aktif"),L=te.filter(i=>i.durum==="tamamlandi"),N=/[^\p{L}\p{N}\s.,!?:;*@#%&()\-=+\n\p{Emoji_Presentation}]/gu,Ke=i=>i?i.locale("tr").format("D MMMM dddd HH:mm"):"Belirtilmemiş Zaman",Ye=(i,a)=>{const t=[...h];t[i]&&(t[i].message=a,D(t))},ne=async(i,a)=>{try{return await new Promise(t=>setTimeout(t,300+Math.random()*400)),Math.random()>.1}catch(t){return!1}},Ve=async()=>{if(h.length===0){r.warning("Gönderilecek mesaj bulunamadı.");return}O(!0);let i=0,a=0;for(const t of h)t.recipientPhone&&t.recipientPhone.trim()!==""&&await ne(t.recipientPhone,t.message)?i++:a++;O(!1),i>0&&M.success(`${i} mesaj başarıyla gönderildi (simülasyon).`),a>0&&M.error(`${a} mesaj gönderilemedi veya alıcının telefon numarası yok.`),a===0&&w(!1)},He=async i=>{Z(i),$(!0),D([]);try{const a=await ke(i);if(!a||a.length===0){r.info("Bu etkinlik için gönderilecek PK maçı bulunamadı."),$(!1);return}const t=[];let s=0,c=0;for(const p of a){const d=C(p.yayinci1_username),m=C(p.yayinci2_username),Oe=p.mac_zamani?je(p.mac_zamani):void 0,_=Ke(Oe);if(d&&m&&d.username&&m.username){const Ue=`Merhaba ${d.isim_soyisim||d.username} 👋

Hızlı PK etkinliğimiz için ${m.isim_soyisim||m.username} ile eşleştin! 🚀

📅 Tarih & Saat: ${_}

Rakibinin profiline göz atmayı unutma: https://www.tiktok.com/@${m.username} 👈

*Küçük bir hatırlatma:* Maçtan önce takipçilerine duyuru yapmayı ve ısınmak için yayına en az 30 dakika erken girmeyi unutma! 😉

Bol şans! ✨`;t.push({recipientName:d.isim_soyisim||d.username,recipientPhone:d.telefon,opponentName:m.isim_soyisim||m.username,dateTime:_,message:Ue.replace(N,"")});const Ze=`Merhaba ${m.isim_soyisim||m.username} 👋

Hızlı PK etkinliğimiz için ${d.isim_soyisim||d.username} ile eşleştin! 🚀

📅 Tarih & Saat: ${_}

Rakibinin profiline göz atmayı unutma: https://www.tiktok.com/@${d.username} 👈

*Küçük bir hatırlatma:* Maçtan önce takipçilerine duyuru yapmayı ve ısınmak için yayına en az 30 dakika erken girmeyi unutma! 😉

Bol şans! ✨`;t.push({recipientName:m.isim_soyisim||m.username,recipientPhone:m.telefon,opponentName:d.isim_soyisim||d.username,dateTime:_,message:Ze.replace(N,"")}),s++}else c++}s>0?(D(t),w(!0),c>0&&r.warning(`${c} eşleşme için eksik bilgi nedeniyle mesaj oluşturulamadı.`)):r.error("Bu etkinlikteki eşleşmeler için mesaj oluşturulamadı. Yayıncı bilgileri eksik olabilir.")}catch(a){r.error("Etkinlik maçları yüklenirken veya mesajlar hazırlanırken bir hata oluştu.")}finally{$(!1)}},le=i=>e.jsx(y,{itemLayout:"horizontal",dataSource:i,renderItem:a=>e.jsxs(y.Item,{actions:[e.jsx(o,{type:"link",onClick:()=>ae(a),children:"Detaylar"}),a.etkinlik_tipi==="pk_turnuva"&&e.jsx(o,{type:"link",onClick:()=>se(a.id),children:"Turnuva Braketi"}),a.etkinlik_tipi==="hizli_pk"&&e.jsx(o,{icon:e.jsx(pe,{}),onClick:()=>He(a.id),style:{color:"#25D366"},loading:Ae&&we===a.id,children:"Kişisel Mesajlar"}),e.jsx(o,{type:"dashed",icon:e.jsx(ye,{}),onClick:()=>Pe(a),children:"Toplu Duyuru Gönder"}),e.jsx(o,{danger:!0,icon:e.jsx(li,{}),onClick:()=>Ge(a),children:"Sil"})],children:[e.jsx(y.Item.Meta,{avatar:e.jsx(me,{icon:e.jsx(he,{})}),title:e.jsx("a",{onClick:()=>ae(a),children:a.etkinlik_adi}),description:`${u(a.baslangic_tarihi).format("LL")} - ${u(a.bitis_tarihi).format("LL")}`}),e.jsx("div",{children:e.jsx(g,{color:a.durum==="aktif"?"green":a.durum==="tamamlandi"?"blue":"orange",children:a.durum==="aktif"?"Aktif":a.durum==="tamamlandi"?"Tamamlandı":"Planlandı"})})]})}),Le=()=>{const i=[{key:"1",label:`Kişiselleştirilmiş Mesajlar (${h.length})`,children:e.jsx("div",{style:{maxHeight:"55vh",overflowY:"auto",padding:"5px"},children:h.length>0?e.jsx(y,{grid:{gutter:16,xs:1,sm:1,md:2},dataSource:h,renderItem:(a,t)=>e.jsx(y.Item,{children:e.jsxs(ce,{size:"small",title:e.jsx("span",{style:{fontSize:"1em",fontWeight:500},children:`Alıcı: ${a.recipientName}`}),extra:a.recipientPhone||e.jsx(g,{color:"warning",children:"Numara Yok"}),style:{marginBottom:"15px",border:"1px solid #e8e8e8"},headStyle:{backgroundColor:"#fafafa"},children:[e.jsx(fe,{value:a.message,onChange:s=>Ye(t,s.target.value),autoSize:{minRows:4,maxRows:6},style:{marginBottom:"10px"}}),e.jsx("div",{style:{textAlign:"right"},children:e.jsx(o,{size:"small",icon:e.jsx(hi,{}),onClick:()=>{navigator.clipboard.writeText(a.message),M.success(`${a.recipientName} için mesaj kopyalandı!`)},disabled:f,children:"Kopyala"})})]})},t)}):e.jsx(z,{message:"Gönderilecek kişiselleştirilmiş mesaj bulunamadı.",type:"info",showIcon:!0})})},{key:"2",label:"Şablon Yönetimi",children:e.jsx("div",{style:{padding:"20px"},children:e.jsx(z,{message:"Gelecek Özellik",description:"Bu alanda ileride WhatsApp mesaj şablonları oluşturabilir, düzenleyebilir ve seçerek hızlıca mesaj gönderebilirsiniz.",type:"info",showIcon:!0})}),disabled:f}];return e.jsx(T,{title:e.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[e.jsx(pe,{style:{fontSize:"24px",color:"#25D366",marginRight:"10px"}}),e.jsx("span",{children:"Kişiselleştirilmiş WhatsApp Mesajları"})]}),open:Ee,onCancel:()=>w(!1),width:900,footer:[e.jsx(o,{onClick:()=>w(!1),disabled:f,children:"Kapat"},"close"),e.jsxs(o,{type:"primary",icon:e.jsx(ri,{}),style:{backgroundColor:"#25D366",borderColor:"#25D366"},loading:f,onClick:Ve,disabled:h.length===0||f,children:["Tüm Kişisel Mesajları Gönder (",h.length,")"]},"sendAll")],bodyStyle:{padding:"0"},children:e.jsx(G,{defaultActiveKey:"1",items:i,style:{padding:"10px 20px"}})})},Ne=()=>{var i;if(!l)return null;const a=()=>!l.yayincilar||l.yayincilar.length===0?"Ali Veli (@alive), Ayşe Yılmaz (@aysey), Mock Katılımcı (@mock)":l.yayincilar.map(s=>V(s)).join(", "),t=[{key:"1",label:"Etkinlik ID",children:l.id},{key:"2",label:"Etkinlik Adı",children:l.etkinlik_adi},{key:"3",label:"Etkinlik Tipi",children:l.etkinlik_tipi},{key:"4",label:"Durum",children:e.jsx(g,{color:l.durum==="aktif"?"green":l.durum==="tamamlandi"?"blue":"orange",children:l.durum==="aktif"?"Aktif":l.durum==="tamamlandi"?"Tamamlandı":"Planlandı"})},{key:"5",label:"Başlangıç Tarihi",children:u(l.baslangic_tarihi).format("DD MMMM YYYY, HH:mm")},{key:"6",label:"Bitiş Tarihi",children:u(l.bitis_tarihi).format("DD MMMM YYYY, HH:mm")},{key:"7",label:"Açıklama",children:l.aciklama||"Açıklama yok",span:2},{key:"8",label:"Kurallar",children:l.kurallar||"Kurallar belirtilmemiş",span:2},{key:"9",label:`Katılımcılar (${((i=l.yayincilar)==null?void 0:i.length)||l.katilimci_sayisi||0})`,children:a(),span:2}];return e.jsxs(T,{title:`Etkinlik Detayları: ${l.etkinlik_adi}`,open:Ce,onCancel:()=>I(!1),footer:[e.jsx(o,{onClick:()=>I(!1),children:"Kapat"},"close"),l.etkinlik_tipi==="pk_turnuva"&&e.jsx(o,{type:"primary",onClick:()=>se(l.id),children:"Turnuva Braketini Görüntüle"},"bracket")],width:700,children:[e.jsx(Xe,{bordered:!0,column:{xxl:2,xl:2,lg:2,md:1,sm:1,xs:1},items:t}),e.jsxs("div",{style:{marginTop:24},children:[e.jsx(yi,{level:5,children:"Eşleşmeler"}),Te?e.jsx(oe,{}):J.length>0?e.jsx(y,{dataSource:J,renderItem:s=>e.jsx(y.Item,{children:e.jsx("div",{style:{width:"100%"},children:e.jsxs(ii,{align:"middle",gutter:8,children:[e.jsx(ue,{span:18,children:e.jsxs(ai,{align:"center",children:[e.jsx(me,{style:{backgroundColor:"#1677ff",marginRight:8},size:"small",children:s.round||1}),e.jsx("span",{style:{fontWeight:500},children:V(s.yayinci1_username)}),e.jsx(g,{color:"red",style:{margin:"0 4px"},children:"VS"}),e.jsx("span",{style:{fontWeight:500},children:V(s.yayinci2_username)})]})}),e.jsx(ue,{span:6,style:{textAlign:"right"},children:s.planlanan_zaman?e.jsx(g,{color:"green",icon:e.jsx(he,{}),children:u(s.planlanan_zaman).format("DD MMM HH:mm")}):e.jsx(g,{color:"orange",children:"Zaman Belirsiz"})})]})})})}):e.jsx(ge,{type:"secondary",children:"Eşleşme bulunamadı."})]})]})},Fe=()=>{const i=q.getFieldValue("eventName"),a=s=>{R(s)},t=s=>{P(s.target.value)};return e.jsxs(T,{title:e.jsxs("span",{children:[e.jsx(ye,{style:{marginRight:"8px"}}),`Toplu Duyuru Gönder: ${i||"Etkinlik"}`]}),open:Se,onCancel:()=>E(!1),footer:[e.jsx(o,{onClick:()=>E(!1),disabled:x,children:"İptal"},"back"),e.jsxs(o,{type:"primary",loading:x,onClick:Re,disabled:k.length===0||x||!A,children:["Tüm Duyuruları Gönder (",k.length,")"]},"submit")],width:700,bodyStyle:{maxHeight:"70vh",overflowY:"auto",padding:"20px"},children:[e.jsxs(b,{layout:"vertical",children:[e.jsx(b.Item,{label:`Alıcılar (${k.length})`,required:!0,help:"Listeden çıkarabilir veya ekleyebilirsiniz.",children:e.jsx(xe,{mode:"multiple",allowClear:!0,style:{width:"100%"},placeholder:"Lütfen alıcıları seçin",value:k,onChange:a,options:S.map(s=>({label:`${s.isim_soyisim} (@${s.username})`,value:s.id})),filterOption:(s,c)=>{var p;return((p=c==null?void 0:c.label)!=null?p:"").toLowerCase().includes(s.toLowerCase())},disabled:x})}),e.jsx(b.Item,{label:"Gönderilecek Ortak Mesaj İçeriği",help:e.jsx(ge,{type:"secondary",style:{fontSize:"0.8em"},children:'Not: Gönderirken her mesajın başına otomatik olarak "Merhaba [Alıcı Adı] 👋," eklenecektir.'}),required:!0,children:e.jsx(fe,{value:A,onChange:t,autoSize:{minRows:8,maxRows:15},placeholder:"Etkinliğe katılan tüm yayıncılara gönderilecek ortak duyuru metnini buraya yazın...",disabled:x})})]}),e.jsx(z,{message:"Bu mesaj, yukarıda seçilen tüm alıcılara kişiselleştirilmiş bir başlangıç ile gönderilecektir.",type:"info",showIcon:!0,style:{marginTop:"15px"}})]})},Ge=i=>{Y(i),K(!0)},re=()=>{K(!1),Y(null)},We=async()=>{if(j){X(!0);try{const i=await di(j.id);i&&i.success?(r.success(`${j.etkinlik_adi} etkinliği başarıyla silindi.`),K(!1),Y(null),ee()):r.error(`Etkinlik silinemedi: ${(i==null?void 0:i.error)||"Bilinmeyen hata"}`)}catch(i){r.error("Etkinlik silinirken bir hata oluştu. Lütfen tekrar deneyin.")}finally{X(!1)}}},qe=()=>e.jsx(T,{title:e.jsxs("span",{children:[e.jsx(pi,{style:{color:"#ff4d4f",marginRight:"8px"}}),"Etkinliği Sil"]}),open:De,onCancel:re,footer:[e.jsx(o,{onClick:re,children:"İptal"},"cancel"),e.jsx(o,{danger:!0,type:"primary",onClick:We,loading:$e,children:"Evet, Etkinliği Sil"},"delete")],width:480,children:e.jsx("div",{style:{padding:"12px 0"},children:j&&e.jsxs(e.Fragment,{children:[e.jsxs("p",{style:{fontSize:"16px",marginBottom:"16px"},children:[e.jsx("strong",{children:j.etkinlik_adi})," etkinliğini silmek istediğinize emin misiniz?"]}),e.jsx(z,{message:"Bu işlem geri alınamaz!",description:"Etkinlik ve tüm ilgili veriler (eşleşmeler, katılımcılar vb.) kalıcı olarak silinecektir.",type:"warning",showIcon:!0})]})})});return e.jsxs("div",{children:[Me?e.jsx("div",{className:"text-center py-10",children:e.jsx(oe,{size:"large"})}):e.jsx(ce,{bordered:!1,style:{margin:"0 24px 24px 24px"},children:e.jsxs(G,{defaultActiveKey:"1",children:[e.jsx(F,{tab:e.jsxs("span",{children:[e.jsx(ui,{})," Aktif Etkinlikler (",H.length,")"]}),children:H.length>0?le(H):e.jsx(de,{description:"Aktif etkinlik bulunmuyor."})},"1"),e.jsx(F,{tab:e.jsxs("span",{children:[e.jsx(ti,{})," Tamamlanan Etkinlikler (",L.length,")"]}),children:L.length>0?le(L):e.jsx(de,{description:"Tamamlanmış etkinlik bulunmuyor."})},"2"),e.jsx(F,{tab:e.jsxs("span",{children:[e.jsx(ni,{})," AI Etkinlik Danışmanı"]}),children:e.jsx(mi,{})},"3")]})}),e.jsx(Le,{}),e.jsx(Ne,{}),e.jsx(Fe,{}),e.jsx(qe,{})]})};export{ea as default};
