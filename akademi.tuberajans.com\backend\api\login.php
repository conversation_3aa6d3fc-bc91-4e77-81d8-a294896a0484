<?php
require_once dirname(__DIR__) . '/config/config.php';

// Hata raporlama
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// CORS başlıkları
header("Access-Control-Allow-Origin: https://akademi.tuberajans.com");
header("Access-Control-Allow-Credentials: true");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
header("Content-Type: application/json; charset=utf-8");

// Zaman aşımını önlemek için
set_time_limit(30); // 30 saniye

// Debug log
error_log("Login.php called with method: " . $_SERVER['REQUEST_METHOD']);

// OPTIONS isteği kontrolü
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Gelen JSON verisini al
$data = json_decode(file_get_contents('php://input'), true);

// Debug log
error_log('Received JSON data: ' . json_encode($data));
error_log('Parsed JSON data: ' . print_r($data, true));

// Kullanıcı adı ve şifre kontrolü
if (!isset($data['username']) || !isset($data['password'])) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => 'Kullanıcı adı ve şifre gerekli'
    ]);
    exit;
}

$identifier = $data['username'];
$password = $data['password'];

try {
    // Kullanıcıyı veritabanından bul (tüm TikTok alanları dahil)
    $stmt = $db->prepare("
        SELECT
            id, username, email, password, name, role, profile_image,
            tiktok_open_id, tiktok_union_id, tiktok_username, tiktok_display_name,
            tiktok_avatar_url, tiktok_bio, follower_count, following_count,
            likes_count, video_count, is_verified
        FROM users
        WHERE username = ? OR email = ?
    ");
    $stmt->execute([$identifier, $identifier]);
    $user = $stmt->fetch();

    if (!$user) {
        http_response_code(401);
        echo json_encode([
            'status' => 'error',
            'message' => 'Geçersiz kullanıcı adı veya şifre'
        ]);
        exit;
    }

    // Şifre kontrolü
    if (!password_verify($password, $user['password'])) {
        http_response_code(401);
        echo json_encode([
            'status' => 'error',
            'message' => 'Geçersiz kullanıcı adı veya şifre'
        ]);
        exit;
    }

    // Session başlat
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['user_name'] = $user['name'] ?? $user['username'];
    $_SESSION['username'] = $user['username'];

    // Eğer kullanıcının TikTok bağlantısı varsa, TikTok verilerini yükle
    if ($user['tiktok_open_id']) {
        $_SESSION['tiktok_user'] = [
            'user_id' => $user['id'],
            'open_id' => $user['tiktok_open_id'],
            'union_id' => $user['tiktok_union_id'],
            'username' => $user['tiktok_username'],
            'display_name' => $user['tiktok_display_name'] ?? $user['tiktok_username'],
            'avatar_url' => $user['tiktok_avatar_url'] ?? '',
            'bio_description' => $user['tiktok_bio'] ?? '',
            'is_verified' => (bool)($user['is_verified'] ?? false),
            'follower_count' => (int)($user['follower_count'] ?? 0),
            'following_count' => (int)($user['following_count'] ?? 0),
            'likes_count' => (int)($user['likes_count'] ?? 0),
            'video_count' => (int)($user['video_count'] ?? 0),
            'profile_deep_link' => '',
            'login_time' => time()
        ];

        error_log('Login: TikTok verileri session\'a yüklendi - user_id: ' . $user['id']);
    }

    // Avatar URL'sini ayarla
    $avatar_url = null;
    if (isset($_SESSION['tiktok_user']['avatar_url']) && $_SESSION['tiktok_user']['avatar_url']) {
        $avatar_url = $_SESSION['tiktok_user']['avatar_url'];
    } elseif ($user['profile_image']) {
        $avatar_url = '/backend/' . $user['profile_image'];
    }

    // Başarılı yanıt
    echo json_encode([
        'status' => 'success',
        'message' => 'Giriş başarılı',
        'user' => [
            'id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'name' => $user['name'],
            'role' => $user['role'],
            'avatar_url' => $avatar_url,
            'has_tiktok' => !empty($user['tiktok_open_id'])
        ]
    ]);

} catch (Exception $e) {
    error_log('Login error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Sunucu hatası'
    ]);
}
