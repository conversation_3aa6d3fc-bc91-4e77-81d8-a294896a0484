interface Window {
  mcp_puppeteer_puppeteer_connect_active_tab: (options?: { debugPort?: number; targetUrl?: string }) => Promise<void>;
  mcp_puppeteer_puppeteer_navigate: (options: { url: string }) => Promise<void>;
  mcp_puppeteer_puppeteer_screenshot: (options: { name: string; selector?: string; width?: number; height?: number }) => Promise<void>;
  mcp_puppeteer_puppeteer_click: (options: { selector: string }) => Promise<void>;
  mcp_puppeteer_puppeteer_fill: (options: { selector: string; value: string }) => Promise<void>;
  mcp_puppeteer_puppeteer_select: (options: { selector: string; value: string }) => Promise<void>;
  mcp_puppeteer_puppeteer_hover: (options: { selector: string }) => Promise<void>;
  mcp_puppeteer_puppeteer_evaluate: (options: { script: string }) => Promise<any>;
} 