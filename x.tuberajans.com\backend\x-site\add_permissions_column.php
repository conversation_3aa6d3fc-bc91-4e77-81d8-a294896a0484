<?php
// Hata göstermeyi tamamen kapat
ini_set('display_errors', 0); 
ini_set('display_startup_errors', 0);
error_reporting(0);

// Config dosyasını yükle
require_once __DIR__ . '/../config/config.php';

// Log dosyası
$logFile = __DIR__ . '/migration.log';
file_put_contents($logFile, date('Y-m-d H:i:s') . " - <PERSON><PERSON><PERSON><PERSON> başlatıldı\n", FILE_APPEND);

try {
    // Permissions sütunu var mı kontrol et
    $stmt = $db->prepare("SHOW COLUMNS FROM users LIKE 'permissions'");
    $stmt->execute();
    $column = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$column) {
        // Permissions sütunu yok, ekleyelim
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - Permissions sütunu bulunamadı, ekleniyor...\n", FILE_APPEND);
        
        $sql = "ALTER TABLE users ADD COLUMN permissions TEXT NULL AFTER role";
        $db->exec($sql);
        
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - Permissions sütunu başarıyla eklendi\n", FILE_APPEND);
        
        // Mevcut kullanıcıları güncelle
        $stmt = $db->prepare("SELECT id, role FROM users");
        $stmt->execute();
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Her kullanıcıya rol bazlı izinlerini ekle
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - Mevcut kullanıcılar güncelleniyor...\n", FILE_APPEND);
        
        foreach ($users as $user) {
            // Rol bazlı izinleri al
            if ($user['role'] === 'admin') {
                $permissions = [
                    'dashboard' => true,
                    'publishers' => true,
                    'influencers' => true,
                    'tasks' => true,
                    'reports' => true,
                    'ai_advisor' => true,
                    'events' => [
                        'view' => true,
                        'manage' => true,
                        'ai_advisor' => true,
                        'pk_matcher' => true
                    ],
                    'users' => [
                        'view' => true,
                        'manage' => true
                    ],
                    'performance' => true,
                    'tournament' => true
                ];
            } else if ($user['role'] === 'editor') {
                $permissions = [
                    'dashboard' => true,
                    'publishers' => true,
                    'influencers' => true,
                    'tasks' => true,
                    'reports' => true,
                    'events' => [
                        'view' => true,
                        'manage' => true
                    ],
                    'performance' => true
                ];
            } else { // viewer
                $permissions = [
                    'dashboard' => true,
                    'publishers' => true,
                    'reports' => true,
                    'events' => [
                        'view' => true
                    ]
                ];
            }
            
            // İzinleri JSON formatına dönüştür
            $permissionsJson = json_encode($permissions);
            
            // Kullanıcıyı güncelle
            $stmt = $db->prepare("UPDATE users SET permissions = ? WHERE id = ?");
            $stmt->execute([$permissionsJson, $user['id']]);
            
            file_put_contents($logFile, date('Y-m-d H:i:s') . " - Kullanıcı güncellendi: " . $user['id'] . "\n", FILE_APPEND);
        }
        
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - Tüm kullanıcılar başarıyla güncellendi\n", FILE_APPEND);
        
        echo "Permissions sütunu başarıyla eklendi ve tüm kullanıcılar güncellendi!";
    } else {
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - Permissions sütunu zaten var, işlem yapılmadı\n", FILE_APPEND);
        echo "Permissions sütunu zaten var!";
    }
} catch (PDOException $e) {
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - Hata: " . $e->getMessage() . "\n", FILE_APPEND);
    echo "Hata: " . $e->getMessage();
}
?>