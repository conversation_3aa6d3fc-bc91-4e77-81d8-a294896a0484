import React, { useState, useEffect, useContext } from 'react';
import { motion } from 'framer-motion';
import { FaClipboardList, FaPlus, FaRegClock, FaCheckCircle, FaPaperPlane } from 'react-icons/fa';
import axios from 'axios';
import { useAuth } from '../../contexts/AuthContext';
import { SidebarContext } from '../../contexts/SidebarContext';

const Requests: React.FC = () => {
  // Development mode kontrolü
  const isDevMode = import.meta.env.VITE_DISABLE_AUTH === 'true' || import.meta.env.DEV;

  // Sidebar context'inden isMobile ve isSidebarOpen değerlerini al
  const { isMobile } = useContext(SidebarContext);

  // AuthContext'i sadece production modunda kullan
  let user: any = null;
  if (!isDevMode) {
    try {
      const authContext = useAuth();
      user = authContext.user;
    } catch (error) {
      console.error('Auth context error in Requests:', error);
      // Fallback: localStorage'dan kullanıcı bilgisini al
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        user = JSON.parse(storedUser);
      }
    }
  } else {
    // Development modunda localStorage'dan kullanıcı bilgisini al
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      user = JSON.parse(storedUser);
    }
  }

  const [activeTab, setActiveTab] = useState<'açık' | 'tamamlanan'>('açık');
  const [modalOpen, setModalOpen] = useState(false);
  const [replyModalOpen, setReplyModalOpen] = useState(false);
  const [currentRequest, setCurrentRequest] = useState<number | null>(null);
  const [replyText, setReplyText] = useState('');
  // Request tipi tanımı
  interface Request {
    id: number;
    title: string;
    description: string;
    date: string;
    status: string;
    priority: string;
    category: string;
    assignee: string;
    responses: Array<{
      id: number;
      author: string;
      message: string;
      date: string;
    }>;
  }

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [requests, setRequests] = useState<Request[]>([]);
  const [formData, setFormData] = useState({
    category: 'technical',
    title: '',
    description: '',
    priority: 'medium'
  });

  // Destek taleplerini API'den çek
  const fetchRequests = async () => {
    setLoading(true);
    setError(null);

    try {
      // Development modunda mock veri kullan
      if (isDevMode) {
        console.log('Development mode: Mock destek talepleri kullanılıyor');

        const mockRequests: Request[] = [
          {
            id: 1,
            title: 'TikTok hesabım askıya alındı',
            description: 'Merhaba, TikTok hesabım sebepsiz yere askıya alındı. Bu durumla ilgili yardım alabilir miyim? Hesabımı nasıl geri alabilirim?',
            date: '12 Ocak 2025',
            status: 'açık',
            priority: 'high',
            category: 'account',
            assignee: 'Destek Ekibi',
            responses: [
              {
                id: 1,
                author: 'Destek Ekibi',
                message: 'Merhaba! Talebinizi aldık. TikTok hesap askıya alma durumları için gerekli adımları size ileteceğiz. Lütfen hesap bilgilerinizi paylaşın.',
                date: '12 Ocak 2025'
              }
            ]
          },
          {
            id: 2,
            title: 'Video düzenleme eğitimi talebi',
            description: 'Merhaba, video düzenleme konusunda daha detaylı eğitimler olabilir mi? Özellikle CapCut ve InShot uygulamaları hakkında.',
            date: '10 Ocak 2025',
            status: 'tamamlandı',
            priority: 'medium',
            category: 'content',
            assignee: 'Destek Ekibi',
            responses: [
              {
                id: 2,
                author: 'Destek Ekibi',
                message: 'Talebiniz için teşekkürler! Video düzenleme eğitimi talebinizi eğitim ekibimize ilettik. Yakında yeni eğitimler eklenecek.',
                date: '11 Ocak 2025'
              }
            ]
          }
        ];

        setRequests(mockRequests);
        setLoading(false);
        return;
      }

      // Production modunda gerçek API çağrısı
      const response = await axios.get('/backend/api/api_support.php', {
        params: {
          action: 'list',
          user_id: user?.id || 1 // Kullanıcı ID'si, varsayılan olarak 1
        }
      });

      if (response.data.status === 'success') {
        // API'den gelen verileri formatla
        const formattedRequests = response.data.data.map((ticket: any) => {
          return {
            id: ticket.id,
            title: ticket.subject,
            description: ticket.message,
            date: new Date(ticket.created_at).toLocaleDateString('tr-TR', {
              day: 'numeric',
              month: 'long',
              year: 'numeric'
            }),
            status: ticket.status === 'open' ? 'açık' : 'tamamlandı',
            priority: ticket.priority,
            category: ticket.category,
            assignee: 'Destek Ekibi',
            responses: [] // Yanıtlar ayrı bir API çağrısı ile alınacak
          };
        });

        setRequests(formattedRequests);
      } else {
        setError('Destek talepleri alınamadı: ' + response.data.message);
      }
    } catch (err) {
      console.error('Destek talepleri alınırken hata oluştu:', err);
      setError('Destek talepleri alınamadı. Lütfen daha sonra tekrar deneyin.');
    } finally {
      setLoading(false);
    }
  };

  // Talep yanıtlarını çek
  const fetchReplies = async (ticketId: number) => {
    try {
      const response = await axios.get('/backend/api/api_support.php', {
        params: {
          action: 'detail',
          ticket_id: ticketId
        }
      });

      if (response.data.status === 'success' && response.data.data.replies) {
        // Yanıtları formatla
        const formattedReplies = response.data.data.replies.map((reply: any) => {
          return {
            id: reply.id,
            author: reply.is_admin ? 'Destek Ekibi' : (reply.username || 'Kullanıcı'),
            message: reply.message,
            date: new Date(reply.created_at).toLocaleDateString('tr-TR', {
              day: 'numeric',
              month: 'long',
              year: 'numeric'
            })
          };
        });

        // Talepleri güncelle
        setRequests(prevRequests => {
          return prevRequests.map(request => {
            if (request.id === ticketId) {
              return { ...request, responses: formattedReplies };
            }
            return request;
          });
        });
      }
    } catch (err) {
      console.error('Yanıtlar alınırken hata oluştu:', err);
    }
  };

  // Sayfa yüklendiğinde talepleri çek
  useEffect(() => {
    fetchRequests();
  }, [user?.id]);

  // Form verilerini güncelle
  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { id, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [id.replace('request-', '')]: value
    }));
  };

  // Yeni talep oluştur
  const handleCreateRequest = async () => {
    try {
      const response = await axios.post('/backend/api/api_support.php', {
        action: 'create',
        user_id: user?.id || 1,
        subject: formData.title,
        message: formData.description,
        category: formData.category,
        priority: formData.priority
      });

      if (response.data.status === 'success') {
        // Talepleri yeniden çek
        fetchRequests();
        // Modalı kapat
        setModalOpen(false);
        // Form verilerini sıfırla
        setFormData({
          category: 'technical',
          title: '',
          description: '',
          priority: 'medium'
        });
      } else {
        setError('Talep oluşturulamadı: ' + response.data.message);
      }
    } catch (err) {
      console.error('Talep oluşturulurken hata oluştu:', err);
      setError('Talep oluşturulamadı. Lütfen daha sonra tekrar deneyin.');
    }
  };

  // Talebe yanıt gönder
  const handleSendReply = async () => {
    if (!currentRequest || !replyText.trim()) return;

    try {
      const response = await axios.post('/backend/api/api_support.php', {
        action: 'reply',
        ticket_id: currentRequest,
        user_id: user?.id || 1,
        message: replyText,
        is_admin: 0 // Kullanıcı yanıtı
      });

      if (response.data.status === 'success') {
        // Yanıtları yeniden çek
        await fetchReplies(currentRequest);
        // Modalı kapat ve state'i temizle
        setReplyModalOpen(false);
        setCurrentRequest(null);
        setReplyText('');
      } else {
        setError('Yanıt gönderilemedi: ' + response.data.message);
      }
    } catch (err) {
      console.error('Yanıt gönderilirken hata oluştu:', err);
      setError('Yanıt gönderilemedi. Lütfen daha sonra tekrar deneyin.');
    }
  };

  // Aktif sekmeye göre talepleri filtrele
  const filteredRequests = activeTab === 'açık'
    ? requests.filter(request => request.status === 'açık')
    : requests.filter(request => request.status === 'tamamlandı');

  // Durum göstergeleri için renkler ve ikonlar
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'açık':
        return {
          color: 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300',
          icon: <FaRegClock className="mr-1.5" />
        };
      case 'tamamlandı':
        return {
          color: 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300',
          icon: <FaCheckCircle className="mr-1.5" />
        };
      default:
        return {
          color: 'bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-300',
          icon: <FaRegClock className="mr-1.5" />
        };
    }
  };

  return (
    <div className="min-h-screen w-full">
      <div className="container" style={{
        maxWidth: isMobile ? '100%' : 'none',
        overflowX: 'hidden'
      }}>
        {/* Başlık ve Sekmeler */}
        <div className="bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden mb-2 sm:mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between px-2 sm:px-4 py-2 sm:py-3 border-b border-gray-100 dark:border-gray-700 gap-2 sm:gap-0">
            <div className="flex items-center">
              <FaClipboardList className="text-gray-700 dark:text-gray-300 mr-1 sm:mr-2" />
              <h2 className="text-sm sm:text-base font-semibold text-gray-800 dark:text-white">Taleplerim</h2>
            </div>
            <button
              onClick={() => setModalOpen(true)}
              className="inline-flex items-center px-2 sm:px-4 py-1.5 sm:py-2 border border-transparent rounded-full shadow-sm text-xs sm:text-sm font-medium text-white bg-gradient-to-r from-[#FF3E71] to-[#FF5F87] hover:from-[#FF5F87] hover:to-[#FF3E71] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF3E71] transition-all duration-300"
            >
              <FaPlus className="mr-1 sm:mr-2" /> Yeni Talep
            </button>
          </div>

          <div className="px-2 sm:px-4 py-2 sm:py-3 flex gap-1 sm:gap-4">
            <button
              className={`px-2 sm:px-4 py-1 sm:py-1.5 text-xs sm:text-sm font-medium rounded-full transition-all duration-200 ${
                activeTab === 'açık'
                  ? 'bg-[#FF3E71] text-white'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
              onClick={() => setActiveTab('açık')}
            >
              Açık
            </button>
            <button
              className={`px-2 sm:px-4 py-1 sm:py-1.5 text-xs sm:text-sm font-medium rounded-full transition-all duration-200 ${
                activeTab === 'tamamlanan'
                  ? 'bg-[#FF3E71] text-white'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
              onClick={() => setActiveTab('tamamlanan')}
            >
              Tamamlanan
            </button>
          </div>
        </div>

        {/* Arama kutusu kaldırıldı */}

        {/* Yükleniyor durumu */}
        {loading ? (
          <div className="text-center py-6 sm:py-10 bg-white dark:bg-[#16151c] rounded-lg shadow-sm px-2">
            <div className="w-12 h-12 sm:w-16 sm:h-16 mx-auto bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-300 rounded-full flex items-center justify-center mb-2 sm:mb-4 animate-pulse">
              <FaClipboardList className="text-base sm:text-xl" />
            </div>
            <h3 className="text-sm sm:text-lg font-medium text-gray-900 dark:text-white">Yükleniyor...</h3>
            <p className="mt-0.5 sm:mt-1 text-xs sm:text-sm text-gray-500 dark:text-gray-400">Bekleyin.</p>
          </div>
        ) : error ? (
          <div className="text-center py-6 sm:py-10 bg-white dark:bg-[#16151c] rounded-lg shadow-sm px-2">
            <div className="w-12 h-12 sm:w-16 sm:h-16 mx-auto bg-red-100 dark:bg-red-900/30 text-red-500 dark:text-red-300 rounded-full flex items-center justify-center mb-2 sm:mb-4">
              <FaClipboardList className="text-base sm:text-xl" />
            </div>
            <h3 className="text-sm sm:text-lg font-medium text-gray-900 dark:text-white">Hata</h3>
            <p className="mt-0.5 sm:mt-1 text-xs sm:text-sm text-gray-500 dark:text-gray-400">{error}</p>
            <button
              onClick={fetchRequests}
              className="mt-2 sm:mt-4 px-3 py-1.5 sm:px-4 sm:py-2 bg-[#FF3E71] text-white rounded-full text-xs sm:text-sm"
            >
              Tekrar Dene
            </button>
          </div>
        ) : (
          /* Talep Listesi */
          <div className="space-y-2 sm:space-y-4">
            {filteredRequests.map((request, index) => {
              const statusInfo = getStatusInfo(request.status);

              return (
                <motion.div
                  key={request.id}
                  className="bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <div className="p-2 sm:p-5">
                    <div className="flex flex-col gap-2 sm:gap-0 sm:flex-row sm:items-start justify-between">
                      <div className="flex-1">
                        <h3 className="text-sm sm:text-lg font-semibold text-gray-900 dark:text-white line-clamp-2">{request.title}</h3>
                        <div className="flex flex-col sm:flex-row sm:items-center text-xs sm:text-sm text-gray-500 dark:text-gray-400 mt-1 gap-1 sm:gap-2">
                          <span className="inline-flex items-center">
                            <FaRegClock className="mr-1 text-[#FF3E71]" />
                            {request.date}
                          </span>
                          <span className={`inline-flex items-center px-1.5 sm:px-2.5 py-0.5 sm:py-1 rounded-full text-xs font-medium ${statusInfo.color}`}> 
                            {statusInfo.icon}
                            {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                          </span>
                          <span className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-1.5 sm:px-2.5 py-0.5 sm:py-1 rounded-full text-xs">
                            {request.category}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="mt-4 bg-gray-50 dark:bg-gray-800/30 p-4 rounded-lg">
                      <p className="text-gray-600 dark:text-gray-300 text-sm">{request.description}</p>
                    </div>

                    {/* Son Yanıt Gösterimi */}
                    {request.responses.length > 0 && (
                      <div className="mt-4 bg-gray-50 dark:bg-gray-800/30 p-4 rounded-lg border-l-4 border-[#FF3E71]">
                        <div className="flex items-center mb-2">
                          <span className="font-medium text-sm text-gray-700 dark:text-gray-300">
                            {request.responses[request.responses.length - 1].author}
                          </span>
                          <span className="mx-2 text-gray-400">•</span>
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {request.responses[request.responses.length - 1].date}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-300">
                          {request.responses[request.responses.length - 1].message}
                        </p>
                      </div>
                    )}

                    <div className="mt-4 pt-4 border-t dark:border-gray-700 flex flex-wrap justify-end items-center">
                      <div className="mt-2 sm:mt-0 flex items-center space-x-3">
                        {request.status === 'açık' ? (
                          <>
                            <button
                              onClick={() => {
                                setCurrentRequest(request.id);
                                setReplyModalOpen(true);
                              }}
                              className="inline-flex items-center px-4 py-2 border border-transparent rounded-full shadow-sm text-sm font-medium text-white bg-gradient-to-r from-[#FF3E71] to-[#FF5F87] hover:from-[#FF5F87] hover:to-[#FF3E71] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF3E71] transition-all duration-300"
                            >
                              Yanıtla
                            </button>
                            <button className="inline-flex items-center px-4 py-2 border border-gray-200 dark:border-gray-700 rounded-full text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-[#16151c] hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200">
                              İptal Et
                            </button>
                          </>
                        ) : (
                          <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                            <FaCheckCircle className="mr-2 text-green-500" /> Bu talep tamamlanmıştır
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </motion.div>
              );
            })}

            {filteredRequests.length === 0 && (
              <div className="text-center py-10 bg-white dark:bg-[#16151c] rounded-lg shadow-sm">
                <div className="w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-800 text-[#FF3E71] rounded-full flex items-center justify-center mb-4">
                  <FaClipboardList size={24} />
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">Talep Bulunamadı</h3>
                <p className="mt-1 text-gray-500 dark:text-gray-400">Bu kriterlere uygun talep bulunmamaktadır.</p>
                <button
                  onClick={() => setModalOpen(true)}
                  className="mt-6 inline-flex items-center px-4 py-2 border border-transparent rounded-full shadow-sm text-sm font-medium text-white bg-gradient-to-r from-[#FF3E71] to-[#FF5F87] hover:from-[#FF5F87] hover:to-[#FF3E71] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF3E71] transition-all duration-300"
                >
                  <FaPlus className="mr-2" /> Yeni Talep Oluştur
                </button>
              </div>
            )}
          </div>
        )}

        {/* Yeni Talep Modal */}
        {modalOpen && (
          <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
              <div className="fixed inset-0 transition-opacity" aria-hidden="true">
                <div className="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
              </div>

              <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

              <div className="inline-block align-bottom bg-white dark:bg-[#16151c] rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div className="bg-white dark:bg-[#16151c] px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="flex items-center justify-between mb-4 pb-3 border-b border-gray-100 dark:border-gray-700">
                    <h3 className="text-lg leading-6 font-semibold text-gray-900 dark:text-white flex items-center" id="modal-title">
                      <FaPlus className="mr-2 text-[#FF3E71]" /> Yeni Talep Oluştur
                    </h3>
                    <button
                      type="button"
                      onClick={() => setModalOpen(false)}
                      className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
                    >
                      <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>

                  <div className="mt-4 space-y-4">
                    <div>
                      <label htmlFor="request-category" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Kategori</label>
                      <select
                        id="request-category"
                        value={formData.category}
                        onChange={handleFormChange}
                        className="block w-full px-4 py-2.5 border border-gray-200 dark:border-gray-700 bg-white dark:bg-[#1e1d26] rounded-lg shadow-sm focus:ring-[#FF3E71] focus:border-[#FF3E71] sm:text-sm text-gray-700 dark:text-gray-200"
                      >
                        <option value="account">Hesap Analizi</option>
                        <option value="technical">Teknik Destek</option>
                        <option value="content">Eğitim Talebi</option>
                        <option value="other">Danışmanlık</option>
                      </select>
                    </div>

                    <div>
                      <label htmlFor="request-title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Talep Başlığı</label>
                      <input
                        type="text"
                        id="request-title"
                        value={formData.title}
                        onChange={handleFormChange}
                        className="block w-full px-4 py-2.5 border border-gray-200 dark:border-gray-700 bg-white dark:bg-[#1e1d26] rounded-lg shadow-sm focus:ring-[#FF3E71] focus:border-[#FF3E71] sm:text-sm text-gray-700 dark:text-gray-200"
                        placeholder="Talebiniz için kısa bir başlık"
                      />
                    </div>

                    <div>
                      <label htmlFor="request-description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Açıklama</label>
                      <textarea
                        id="request-description"
                        rows={4}
                        value={formData.description}
                        onChange={handleFormChange}
                        className="block w-full px-4 py-2.5 border border-gray-200 dark:border-gray-700 bg-white dark:bg-[#1e1d26] rounded-lg shadow-sm focus:ring-[#FF3E71] focus:border-[#FF3E71] sm:text-sm text-gray-700 dark:text-gray-200"
                        placeholder="Talebinizi detaylı olarak açıklayın"
                      ></textarea>
                    </div>

                    <div>
                      <label htmlFor="request-priority" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Öncelik</label>
                      <select
                        id="request-priority"
                        value={formData.priority}
                        onChange={handleFormChange}
                        className="block w-full px-4 py-2.5 border border-gray-200 dark:border-gray-700 bg-white dark:bg-[#1e1d26] rounded-lg shadow-sm focus:ring-[#FF3E71] focus:border-[#FF3E71] sm:text-sm text-gray-700 dark:text-gray-200"
                      >
                        <option value="low">Düşük</option>
                        <option value="medium">Orta</option>
                        <option value="high">Yüksek</option>
                      </select>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 dark:bg-[#1e1d26] px-4 py-4 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button
                    type="button"
                    onClick={handleCreateRequest}
                    className="w-full inline-flex justify-center rounded-full border border-transparent shadow-sm px-4 py-2.5 bg-gradient-to-r from-[#FF3E71] to-[#FF5F87] hover:from-[#FF5F87] hover:to-[#FF3E71] text-base font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF3E71] transition-all duration-300 sm:ml-3 sm:w-auto sm:text-sm"
                  >
                    Talebi Gönder
                  </button>
                  <button
                    type="button"
                    onClick={() => setModalOpen(false)}
                    className="mt-3 w-full inline-flex justify-center rounded-full border border-gray-200 dark:border-gray-700 shadow-sm px-4 py-2.5 bg-white dark:bg-[#16151c] text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-300 transition-all duration-200 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  >
                    İptal
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Yanıt Modal */}
        {replyModalOpen && (
          <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
              <div className="fixed inset-0 transition-opacity" aria-hidden="true">
                <div className="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
              </div>

              <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

              <div className="inline-block align-bottom bg-white dark:bg-[#16151c] rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div className="bg-white dark:bg-[#16151c] px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="flex items-center justify-between mb-4 pb-3 border-b border-gray-100 dark:border-gray-700">
                    <h3 className="text-lg leading-6 font-semibold text-gray-900 dark:text-white flex items-center" id="modal-title">
                      <FaPaperPlane className="mr-2 text-[#FF3E71]" /> Talebe Yanıt Ver
                    </h3>
                    <button
                      type="button"
                      onClick={() => setReplyModalOpen(false)}
                      className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
                    >
                      <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>

                  <div className="mt-4 space-y-4">
                    <div>
                      <label htmlFor="reply-text" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Yanıtınız</label>
                      <textarea
                        id="reply-text"
                        rows={4}
                        value={replyText}
                        onChange={(e) => setReplyText(e.target.value)}
                        className="block w-full px-4 py-2.5 border border-gray-200 dark:border-gray-700 bg-white dark:bg-[#1e1d26] rounded-lg shadow-sm focus:ring-[#FF3E71] focus:border-[#FF3E71] sm:text-sm text-gray-700 dark:text-gray-200"
                        placeholder="Yanıtınızı buraya yazın..."
                      ></textarea>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 dark:bg-[#1e1d26] px-4 py-4 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button
                    type="button"
                    onClick={handleSendReply}
                    className="w-full inline-flex justify-center rounded-full border border-transparent shadow-sm px-4 py-2.5 bg-gradient-to-r from-[#FF3E71] to-[#FF5F87] hover:from-[#FF5F87] hover:to-[#FF3E71] text-base font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF3E71] transition-all duration-300 sm:ml-3 sm:w-auto sm:text-sm"
                  >
                    Yanıtı Gönder
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      setReplyModalOpen(false);
                      setCurrentRequest(null);
                      setReplyText('');
                    }}
                    className="mt-3 w-full inline-flex justify-center rounded-full border border-gray-200 dark:border-gray-700 shadow-sm px-4 py-2.5 bg-white dark:bg-[#16151c] text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-300 transition-all duration-200 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  >
                    İptal
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Requests;