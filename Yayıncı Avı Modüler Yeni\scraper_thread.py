import random
import time
import os
import threading
from datetime import datetime
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium import webdriver
import logging
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from utils import kill_chrome_processes, parse_viewer_count

logger = logging.getLogger(__name__)

class ScraperThread(threading.Thread):
    """
    TikTok canlı yayın sayfasından yayıncı verilerini toplar.
    Normal Python Thread olarak çalışır.
    """

    def __init__(self, db_manager, duration, chrome_binary_path, chrome_profile_path, chrome_profile_directory, headless=False, parent=None):
        super().__init__(daemon=True)
        self.db_manager = db_manager
        self.duration = float(duration)  # Her zaman float olarak sakla
        self.chrome_binary_path = chrome_binary_path
        self.chrome_profile_path = chrome_profile_path
        self.chrome_profile_directory = chrome_profile_directory
        self.headless = headless
        self._running = True
        # Doğru selektörleri kullan
        self.LIVE_CONTAINER_SELECTOR = "div.tiktok-k0jr40.e4lbyzh0"
        self.LIVE_VIDEO_LINK_SELECTOR = "a[href*='/@'][href*='/live']"
        self.VIEWER_COUNT_SELECTOR = "div[data-e2e='live-people-count']"
        self.found_usernames = []
        self.is_finished = False
        self.processed_urls = set()  # İşlenen URL'leri takip et

    def run(self):
        # Chrome temizle - BASIT
        try:
            import subprocess
            subprocess.run(['taskkill', '/f', '/im', 'chrome.exe'], capture_output=True, text=True)
            subprocess.run(['taskkill', '/f', '/im', 'chromedriver.exe'], capture_output=True, text=True)
            time.sleep(2)
        except:
            pass
        
        driver = self.setup_driver()
        
        if not driver:
            self.log("Chrome başlatılamadı! İşlem sonlandırılıyor.", level=logging.ERROR)
            self.finish([])
            return
            
        self.log("TikTok Live sayfasına gidiliyor...")
        try:
            # Ana TikTok Live sayfasına git
            driver.get("https://www.tiktok.com/live")
            
            # Sayfanın yüklenmesini bekle
            WebDriverWait(driver, 20).until(
                lambda d: d.execute_script("return document.readyState") == "complete"
            )
            time.sleep(3)
            
            # Pop-up'ları kapat
            self.close_popup_if_exists(driver)
            
            # Ana döngü
            start_time = time.time()
            visit_count = 0
            self.log(f"Scraper başladı, süre: {self.duration} dk")
            
            try:
                # İlk yayına tıkla
                live_links = WebDriverWait(driver, 10).until(
                    EC.presence_of_all_elements_located((By.CSS_SELECTOR, self.LIVE_VIDEO_LINK_SELECTOR))
                )
                
                if live_links:
                    # İlk yayına tıkla
                    live_links[0].click()
                    time.sleep(3)
                else:
                    self.log("Canlı yayın bulunamadı!", level=logging.ERROR)
                    driver.quit()
                    self.finish([])
                    return
            except Exception as e:
                self.log(f"İlk yayına erişim hatası: {e}", level=logging.ERROR)
                driver.quit()
                self.finish([])
                return
                
            # İlk yayından başlayarak hepsini tara
            while self._running and (time.time() - start_time < self.duration * 60):
                visit_count += 1
                
                try:
                    # URL'den kullanıcı adını al
                    current_url = driver.current_url
                    if "/@" in current_url and "/live" in current_url:
                        username = current_url.split("/@")[1].split("/")[0].split("?")[0]
                        
                        # URL daha önce işlendiyse atla
                        if current_url in self.processed_urls:
                            pass
                        else:
                            # İzleyici sayısını bul
                            try:
                                viewer_element = WebDriverWait(driver, 5).until(
                                    EC.visibility_of_element_located((By.CSS_SELECTOR, self.VIEWER_COUNT_SELECTOR))
                                )
                                viewer_text = viewer_element.text
                                viewer_count = parse_viewer_count(viewer_text)
                                
                                # Sadece kullanıcı adı ve izleyici sayısını logla
                                self.log(f"{username} | {viewer_count}")
                                
                                # Veritabanına kaydet
                                self.save_to_db(username, viewer_count)
                                if username not in self.found_usernames:
                                    self.found_usernames.append(username)
                            except Exception as e:
                                # İzleyici sayısı bulunamazsa 0 olarak kabul et
                                viewer_count = 0
                                self.log(f"{username} | {viewer_count}")
                                self.save_to_db(username, viewer_count)
                                if username not in self.found_usernames:
                                    self.found_usernames.append(username)
                                
                            # Bu URL'i işlenmiş olarak işaretle
                            self.processed_urls.add(current_url)
                
                except Exception as navigate_error:
                    pass  # Hataları gösterme
                
                # Süre kontrolü
                if time.time() - start_time >= self.duration * 60:
                    self.log(f"Süre doldu ({self.duration} dk)")
                    break
                
                # Sonraki yayına geç
                if not self._running:
                    break
                
                # Aşağı ok tuşu ile sonraki yayına geç
                actions = ActionChains(driver)
                actions.send_keys(Keys.ARROW_DOWN).perform()
                
                # Rastgele bekleme süresi (3-5 saniye)
                random_sleep = random.uniform(3, 5)
                time.sleep(random_sleep)
                
                # İlerleme durumunu güncelle (sinyal yerine basit hesaplama)
                progress = int(((time.time() - start_time) / (self.duration * 60)) * 100)
                # progressSignal.emit yerine sadece hesapla
                
        except Exception as e:
            self.log(f"Scraper döngüsünde hata: {e}", level=logging.ERROR)
            # Chrome session kaybı durumunda yeniden başlatmayı dene
            if "session deleted" in str(e) or "chrome not reachable" in str(e):
                self.log("Chrome session kaybedildi, yeniden başlatma deneniyor...", level=logging.WARNING)
                try:
                    driver.quit()
                except:
                    pass
                # Yeni driver oluşturmayı dene
                driver = self.setup_driver()
                if driver:
                    self.log("Chrome yeniden başlatıldı", level=logging.INFO)
                    # Kısa bir döngü daha dene
                    try:
                        driver.get("https://www.tiktok.com/live")
                        time.sleep(5)
                    except:
                        pass
        finally:
            unique_usernames = list(set(self.found_usernames))
            self.log(f"✅ Toplam {len(unique_usernames)} benzersiz kullanıcı bulundu")

            # Chrome'u kapat - TEK SEFER
            try:
                driver.quit()
                self.log("✅ Chrome kapatıldı")
            except:
                pass

            self.finish(unique_usernames)

    def setup_driver(self):
        try:
            options = Options()

            # Headless modu ayarla
            options.headless = self.headless

            # GELİŞMİŞ ANTİ-BOT ALGILAMA ÖNLEMELERİ
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_argument("--disable-web-security")
            options.add_argument("--disable-features=VizDisplayCompositor")
            options.add_argument("--disable-ipc-flooding-protection")
            options.add_experimental_option("excludeSwitches", ["enable-automation", "enable-logging"])
            options.add_experimental_option("useAutomationExtension", False)

            # User-Agent ayarla
            options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

            # Temel ayarlar
            options.add_argument('--disable-gpu')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-notifications')
            options.add_argument('--disable-popup-blocking')
            options.add_argument('--start-maximized')
            options.add_argument('--disable-logging')
            options.add_argument('--disable-extensions')
            options.add_argument('--ignore-certificate-errors')
            options.add_argument('--ignore-ssl-errors')

            # Profil ayarları
            if self.chrome_profile_path and self.chrome_profile_directory:
                options.add_argument(f'--user-data-dir={self.chrome_profile_path}')
                options.add_argument(f'--profile-directory={self.chrome_profile_directory}')

            # Chrome binary path ayarı
            if self.chrome_binary_path:
                options.binary_location = self.chrome_binary_path

            # Ek tercihler
            prefs = {
                "profile.default_content_setting_values": {
                    "notifications": 2,
                    "geolocation": 2,
                    "media_stream": 2,
                }
            }
            options.add_experimental_option("prefs", prefs)

            # Chrome driver'ı başlat
            service = Service('chromedriver.exe')
            driver = webdriver.Chrome(service=service, options=options)

            # JavaScript ile bot algılama önlemleri
            try:
                stealth_script = """
                Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
                Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
                Object.defineProperty(navigator, 'languages', {get: () => ['tr-TR', 'tr', 'en-US', 'en']});
                delete window.chrome;
                console.debug = () => {};
                """
                driver.execute_script(stealth_script)
                self.log("✅ Bot algılama önlemleri uygulandı")
            except Exception as e:
                self.log(f"⚠️ Bot algılama önlemleri uygulanamadı: {e}", level=logging.WARNING)

            return driver

        except Exception as e:
            self.log(f"❌ Chrome başlatma hatası: {e}", level=logging.ERROR)
            return None

    def close_popup_if_exists(self, driver):
        try:
            # Çerez kabul pop-up'ı
            cookie_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Kabul')]")
            for button in cookie_buttons:
                if button.is_displayed():
                    button.click()
                    time.sleep(1)
                    break
                    
            # "Şimdi değil" pop-up'ı
            not_now_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Şimdi değil')]")
            for button in not_now_buttons:
                if button.is_displayed():
                    button.click()
                    time.sleep(1)
                    break
        except:
            pass

    def save_to_db(self, username, viewer_count):
        try:
            # TikTok yayın URL'ini oluştur
            stream_url = f"https://www.tiktok.com/@{username}/live"
            
            # Veritabanına kaydet
            self.db_manager.execute_query(
                "INSERT INTO live_data (username, viewer_count, link, timestamp, status) VALUES (%s, %s, %s, NOW(), %s) "
                "ON DUPLICATE KEY UPDATE viewer_count=VALUES(viewer_count), timestamp=NOW(), link=VALUES(link), status=%s",
                (username, viewer_count, stream_url, "Bekleniyor", "Bekleniyor")
            )
        except Exception as e:
            self.log(f"❌ {username} veritabanına kaydedilemedi: {e}", level=logging.ERROR)

    def stop(self):
        self._running = False
        
    def finish(self, usernames):
        if not self.is_finished:
            self.is_finished = True
            # finishedSignal.emit yerine sadece işaretle
        
    def log(self, message, level=logging.INFO, show_in_ui=False):
        """Basit log sistemi - DUPLIKASYON ÖNLEME"""
        if level == logging.INFO:
            logger.info(f"[SCRAPER] {message}")
        elif level == logging.WARNING:
            logger.warning(f"[SCRAPER] {message}")
        elif level == logging.ERROR:
            logger.error(f"[SCRAPER] {message}")

        # UI sinyali sadece gerektiğinde
        if show_in_ui and hasattr(self, 'logSignal'):
            self.logSignal.emit(message, False)