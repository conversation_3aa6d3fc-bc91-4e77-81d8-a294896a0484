<?php
// Basitleştirilmiş auth sistemi - hızlı yanıt için
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// CORS başlıkları
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
header("Access-Control-Max-Age: 86400");
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(204);
    exit();
}

header('Content-Type: application/json; charset=utf-8');

// Hızlı yanıt için timeout
set_time_limit(5);

// Bearer token'ı header'dan çeken fonksiyon
function getBearerToken() {
    $headers = null;
    if (isset($_SERVER['Authorization'])) {
        $headers = trim($_SERVER["Authorization"]);
    } else if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers = trim($_SERVER["HTTP_AUTHORIZATION"]);
    } else if (isset($_SERVER['REDIRECT_HTTP_AUTHORIZATION'])) {
        $headers = trim($_SERVER["REDIRECT_HTTP_AUTHORIZATION"]);
    } elseif (function_exists('apache_request_headers')) {
        $requestHeaders = apache_request_headers();
        foreach ($requestHeaders as $key => $value) {
            if (strtolower($key) === 'authorization') {
                $headers = trim($value);
                break;
            }
        }
    }
    
    if (!empty($headers)) {
        if (preg_match('/Bearer\s(\S+)/', $headers, $matches)) {
            return $matches[1];
        }
    }
    return null;
}

// Auth kontrolü - basitleştirilmiş
if (isset($_GET['check'])) {
    error_log("Auth Simple: Oturum kontrolü isteği alındı");
    
    $token = getBearerToken();
    error_log("Auth Simple: Token: " . ($token ? substr($token, 0, 10) . "..." : "Yok"));
    
    if (!$token) {
        // Token yoksa hızlı yanıt
        http_response_code(200);
        echo json_encode([
            'authenticated' => false,
            'message' => 'Token bulunamadı. Lütfen giriş yapın.',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        exit;
    }
    
    // Config yüklemeyi dene - hızlı fail
    try {
        require_once __DIR__ . '/../config/config.php';
        error_log("Auth Simple: Config yüklendi");
    } catch (Exception $e) {
        error_log("Auth Simple: Config hatası: " . $e->getMessage());
        http_response_code(200);
        echo json_encode([
            'authenticated' => false,
            'message' => 'Sunucu yapılandırma hatası. Lütfen giriş yapın.',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        exit;
    }
    
    // Veritabanı kontrolü - hızlı fail
    if (!isset($db_takip) || !$db_takip) {
        error_log("Auth Simple: DB bağlantısı yok");
        http_response_code(200);
        echo json_encode([
            'authenticated' => false,
            'message' => 'Veritabanı bağlantısı bulunamadı. Lütfen giriş yapın.',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        exit;
    }
    
    // Token kontrolü - basit sorgu
    try {
        error_log("Auth Simple: Token kontrolü yapılıyor");
        $stmt = $db_takip->prepare("SELECT u.id, u.name, u.email, u.role FROM users u JOIN user_tokens t ON u.id = t.user_id WHERE t.token = ? AND t.expires_at > NOW() LIMIT 1");
        $stmt->execute([$token]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            error_log("Auth Simple: Kullanıcı bulundu: " . $user['id']);
            
            // Basit izinler - admin için tümü true
            $permissions = [
                'dashboard' => true,
                'publishers' => true,
                'influencers' => true,
                'tasks' => true,
                'reports' => true,
                'ai_advisor' => ($user['role'] === 'admin'),
                'events' => ['view' => true, 'manage' => ($user['role'] === 'admin')],
                'users' => ['view' => ($user['role'] === 'admin'), 'manage' => ($user['role'] === 'admin')],
                'performance' => true,
                'tournament' => ($user['role'] === 'admin'),
                'akademi' => [
                    'dashboard' => true,
                    'duyurular' => true,
                    'egitimler' => true,
                    'destek' => true,
                    'kullanicilar' => true,
                    'ai' => true,
                    'ayarlar' => true
                ],
                'site_yonetimi' => [
                    'anasayfa' => true,
                    'yayincilar' => true,
                    'basvurular' => true,
                    'iletisim' => true,
                    'geriarama' => true,
                    'toplanti' => true,
                    'sms' => true,
                    'blog' => true,
                    'ayarlar' => true
                ],
                'etsy_operasyonu' => [
                    'dashboard' => true,
                    'tasarim' => true,
                    'urunler' => true,
                    'ayarlar' => true
                ]
            ];
            
            // Token süresini uzat - hızlı
            try {
                $expiresAt = date('Y-m-d H:i:s', strtotime('+24 hours'));
                $updateStmt = $db_takip->prepare("UPDATE user_tokens SET expires_at = ? WHERE token = ?");
                $updateStmt->execute([$expiresAt, $token]);
            } catch (Exception $e) {
                error_log("Auth Simple: Token uzatma hatası: " . $e->getMessage());
                // Hata olsa bile devam et
            }
            
            http_response_code(200);
            echo json_encode([
                'authenticated' => true,
                'user' => [
                    'id' => $user['id'],
                    'name' => $user['name'],
                    'email' => $user['email'],
                    'role' => $user['role'],
                    'permissions' => $permissions
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            exit;
        } else {
            error_log("Auth Simple: Token geçersiz");
        }
        
    } catch (PDOException $e) {
        error_log("Auth Simple: DB hatası: " . $e->getMessage());
        http_response_code(200);
        echo json_encode([
            'authenticated' => false,
            'message' => 'Veritabanı hatası. Lütfen giriş yapın.',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        exit;
    }
    
    // Token geçersizse
    http_response_code(200);
    echo json_encode([
        'authenticated' => false,
        'message' => 'Oturum süresi doldu. Lütfen giriş yapın.',
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit;
}

// Diğer istekler için ana auth.php'ye yönlendir
http_response_code(200);
echo json_encode([
    'message' => 'Auth Simple - sadece check işlemi desteklenir',
    'timestamp' => date('Y-m-d H:i:s')
]);
?>
