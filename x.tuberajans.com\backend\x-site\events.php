<?php
// <PERSON>a ayıklama
ini_set('display_errors', 1);
error_reporting(E_ALL);

// CORS ve JSON header'ları
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS, DELETE');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=utf-8');

// OPTIONS istekleri için hızlıca 200 dön
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Ortak config dosyasını dahil et
require_once __DIR__ . '/../config/config.php';

// JSON yanıt fonksiyonu
if (!function_exists('jsonResponse')) {
    function jsonResponse($data, $status = 200) {
        http_response_code($status);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}

// Auth kontrolü (checkAuth fonksiyonu)
if (!function_exists('checkAuth')) {
    function checkAuth() {
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
        
        if (strpos($authHeader, 'Bearer ') === 0) {
            $token = substr($authHeader, 7);
            if (strlen($token) > 10) { // Basit token kontrolü
                return true;
            }
        }
        return false;
    }
}

// Token doğrulaması
if ($_SERVER['REQUEST_METHOD'] !== 'OPTIONS') {
    if (!checkAuth()) {
        jsonResponse(['error' => 'Unauthorized', 'message' => 'Bu işlemi gerçekleştirmek için giriş yapmalısınız.'], 401);
        exit;
    }
}

// $db artık config.php'den geliyor

// Database auto-detection
$databases = ['tuberaja_yayinci_takip', 'tuberaja_yayinci_akademi', 'tiktok_live_data'];
$eventsDatabase = null;

foreach ($databases as $database) {
    try {
        $stmt = $db->query("SHOW TABLES FROM $database LIKE 'etkinlikler'");
        $tables = $stmt->fetchAll();
        if (!empty($tables)) {
            $eventsDatabase = $database;
            error_log("Events API: etkinlikler tablosu bulundu: $database");
            break;
        }
    } catch (PDOException $e) {
        continue;
    }
}

if (!$eventsDatabase) {
    error_log("Events API: etkinlikler tablosu bulunamadı");
    jsonResponse(['success' => false, 'error' => 'Etkinlikler tablosu bulunamadı'], 500);
}

// URL parametrelerini al
$action = $_GET['action'] ?? '';
$eventId = $_GET['id'] ?? $_GET['eventId'] ?? '';
$tournamentId = $_GET['tournamentId'] ?? '';
$matchId = $_GET['matchId'] ?? '';

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // Farklı GET işlemleri için action parametresine göre dal
    switch ($action) {
        case 'detail':
            // Tek etkinlik detayı
            if (!$eventId) {
                jsonResponse(['success' => false, 'error' => 'Etkinlik ID\'si gerekli']);
            }
            
            try {
                $stmt = $db->prepare("SELECT * FROM $eventsDatabase.etkinlikler WHERE id = ?");
                $stmt->execute([$eventId]);
                $event = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (!$event) {
                    jsonResponse(['success' => false, 'error' => 'Etkinlik bulunamadı']);
                }
                
                // Frontend için eksik alanlar varsa default değerler ekle
                if (!isset($event['olusturulma_tarihi'])) {
                    $event['olusturulma_tarihi'] = date('Y-m-d H:i:s');
                }
                if (!isset($event['kurallar'])) {
                    $event['kurallar'] = 'Etkinlik kuralları belirtilmemiş.';
                }
                
                jsonResponse($event);
            } catch (PDOException $e) {
                error_log("Event Detail Error: " . $e->getMessage());
                jsonResponse(['success' => false, 'error' => 'Veritabanı hatası']);
            }
            break;
            
        case 'matches':
            // Etkinlik eşleşmeleri
            if (!$eventId) {
                jsonResponse(['success' => false, 'error' => 'Etkinlik ID\'si gerekli']);
            }
            
            try {
                // pk_eslesmeleri tablosundan çek
                $stmt = $db->prepare("SELECT * FROM $eventsDatabase.pk_eslesmeleri WHERE etkinlik_id = ? ORDER BY id ASC");
                $stmt->execute([$eventId]);
                $matches = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                jsonResponse($matches);
            } catch (PDOException $e) {
                error_log("Event Matches Error: " . $e->getMessage());
                // Tablo yoksa boş array döndür
                jsonResponse([]);
            }
            break;
            
        case 'tournament-matches':
            // Turnuva eşleşmeleri
            if (!$tournamentId) {
                jsonResponse(['success' => false, 'error' => 'Turnuva ID\'si gerekli']);
            }
            
            try {
                $stmt = $db->prepare("SELECT * FROM $eventsDatabase.pk_eslesmeleri WHERE etkinlik_id = ? ORDER BY id ASC");
                $stmt->execute([$tournamentId]);
                $matches = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                jsonResponse($matches);
            } catch (PDOException $e) {
                error_log("Tournament Matches Error: " . $e->getMessage());
                jsonResponse([]);
            }
            break;
            
        case 'publishers':
            // Yayıncı listesi - publishers.php'deki mantığı buraya taşı
            try {
                // Database auto-detection (publisher-discovery mantığı)
                $databases = ['tuberaja_yayinci_takip', 'tuberaja_yayinci_akademi', 'tiktok_live_data'];
                $publisherDatabase = null;
                
                foreach ($databases as $database) {
                    try {
                        $testQuery = "SELECT 1 FROM $database.live_data LIMIT 1";
                        $db->query($testQuery);
                        $publisherDatabase = $database;
                        break;
                    } catch (PDOException $e) {
                        continue;
                    }
                }
                
                if (!$publisherDatabase) {
                    jsonResponse(['success' => false, 'error' => 'Yayıncı tablosu bulunamadı']);
                }
                
                $stmt = $db->prepare("SELECT id, username, isim_soyisim, telefon, followers, elmaslar, yayin_suresi, son_yayin_tarihi FROM $publisherDatabase.live_data WHERE status = 'uygun' ORDER BY isim_soyisim ASC");
                $stmt->execute();
                $publishers = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                jsonResponse($publishers);
            } catch (PDOException $e) {
                error_log("Publishers Error: " . $e->getMessage());
                jsonResponse(['success' => false, 'error' => 'Yayıncı verileri alınamadı']);
            }
            break;
            
        default:
            // Varsayılan: tüm etkinlikler
            try {
                $stmt = $db->prepare("SELECT * FROM $eventsDatabase.etkinlikler ORDER BY id DESC");
    $stmt->execute();
    $events = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                // Frontend için eksik alanlar varsa default değerler ekle
                foreach ($events as &$event) {
                    if (!isset($event['olusturulma_tarihi'])) {
                        $event['olusturulma_tarihi'] = date('Y-m-d H:i:s');
                    }
                    if (!isset($event['kurallar'])) {
                        $event['kurallar'] = 'Etkinlik kuralları belirtilmemiş.';
                    }
                }
                
                jsonResponse($events);
            } catch (PDOException $e) {
                error_log("Events List Error: " . $e->getMessage());
                jsonResponse(['success' => false, 'error' => 'Etkinlik verileri alınamadı']);
            }
            break;
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        jsonResponse(['success' => false, 'error' => 'Geçersiz JSON']);
    }
    
    // matchId parametresi varsa maç güncelleme işlemi
    if ($matchId) {
        // Maç güncelleme
        try {
            $kazanan = $input['kazanan'] ?? null;
            $durum = $input['durum'] ?? null;
            $notlar = $input['notlar'] ?? null;
            
            $updateFields = [];
            $updateParams = [];
            
            if ($kazanan) {
                $updateFields[] = "kazanan_username = ?";
                $updateParams[] = $kazanan;
            }
            
            if ($durum) {
                $updateFields[] = "durum = ?";
                $updateParams[] = $durum;
            }
            
            $updateParams[] = $matchId;
            
            if (empty($updateFields)) {
                jsonResponse(['success' => false, 'error' => 'Güncellenecek alan belirtilmedi']);
            }
            
            $sql = "UPDATE $eventsDatabase.pk_eslesmeleri SET " . implode(', ', $updateFields) . " WHERE id = ?";
            $stmt = $db->prepare($sql);
            $result = $stmt->execute($updateParams);
            
            if ($result) {
                jsonResponse(['success' => true, 'message' => 'Maç başarıyla güncellendi']);
            } else {
                jsonResponse(['success' => false, 'error' => 'Maç güncellenemedi']);
            }
        } catch (PDOException $e) {
            error_log("Match Update Error: " . $e->getMessage());
            jsonResponse(['success' => false, 'error' => 'Veritabanı hatası: ' . $e->getMessage()]);
        }
    } else {
        // Etkinlik oluşturma
    $etkinlik_adi = $input['etkinlik_adi'] ?? null;
    $etkinlik_tipi = $input['etkinlik_tipi'] ?? 'pk_turnuva';
    $baslangic_tarihi = $input['baslangic_tarihi'] ?? null;
    $bitis_tarihi = $input['bitis_tarihi'] ?? null;
    $kayit_uygunlugu = $input['kayit_uygunlugu'] ?? 'herkese_acik';
        
        // Frontend değerlerini veritabanı enum değerlerine çevir
        if ($kayit_uygunlugu === 'otomatik') {
            $kayit_uygunlugu = 'herkese_acik';
        } elseif ($kayit_uygunlugu === 'kayit') {
            $kayit_uygunlugu = 'herkese_acik';
        } elseif ($kayit_uygunlugu === 'davet') {
            $kayit_uygunlugu = 'ozel';
        }
    $puan_sistemi = $input['puan_sistemi'] ?? 'elmas';
    $durum = $input['durum'] ?? 'aktif';
        $aciklama = $input['aciklama'] ?? null;
        $kurallar = $input['kurallar'] ?? null;
        $yayincilar = $input['yayincilar'] ?? [];
        $eslesme_bilgileri = $input['eslesme_bilgileri'] ?? [];

    if (!$etkinlik_adi || !$baslangic_tarihi || !$bitis_tarihi) {
            jsonResponse(['success' => false, 'error' => 'Eksik alanlar: etkinlik_adi, baslangic_tarihi, bitis_tarihi zorunludur']);
    }

        try {
            // Tarih formatını düzelt - array gelirse
            if (is_array($baslangic_tarihi)) {
                $baslangic_tarihi = $baslangic_tarihi[0] ?? $baslangic_tarihi;
            }
            if (is_array($bitis_tarihi)) {
                $bitis_tarihi = $bitis_tarihi[1] ?? $bitis_tarihi;
            }
            
            // Tarih aralığı array ise
            if (isset($input['tarih_araligi']) && is_array($input['tarih_araligi'])) {
                $baslangic_tarihi = $input['tarih_araligi'][0];
                $bitis_tarihi = $input['tarih_araligi'][1];
            }
            
            // Gerçek tablo yapısına göre tüm alanları kullan
            $stmt = $db->prepare("INSERT INTO $eventsDatabase.etkinlikler (etkinlik_adi, etkinlik_tipi, baslangic_tarihi, bitis_tarihi, kayit_uygunlugu, puan_sistemi, durum, aciklama) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
    $result = $stmt->execute([
        $etkinlik_adi,
        $etkinlik_tipi,
        $baslangic_tarihi,
        $bitis_tarihi,
        $kayit_uygunlugu,
        $puan_sistemi,
                $durum,
                $aciklama
    ]);
            
            if ($result) {
                $eventId = $db->lastInsertId();
                
                // Eşleşme bilgileri varsa pk_eslesmeleri tablosuna kaydet
                if (!empty($eslesme_bilgileri)) {
                    foreach ($eslesme_bilgileri as $match) {
                        try {
                            $stmt = $db->prepare("INSERT INTO $eventsDatabase.pk_eslesmeleri (etkinlik_id, yayinci1_username, yayinci2_username, round, planlanan_zaman, durum) VALUES (?, ?, ?, ?, ?, 'bekleniyor')");
                            $stmt->execute([
                                $eventId,
                                $match['yayinci1_username'],
                                $match['yayinci2_username'],
                                $match['round'] ?? 1,
                                $match['planlanan_zaman'] ?? $baslangic_tarihi
                            ]);
                        } catch (PDOException $e) {
                            error_log("PK Eşleşmesi kaydetme hatası: " . $e->getMessage());
                        }
                    }
                }
                
                jsonResponse(['success' => true, 'id' => $eventId, 'message' => 'Etkinlik başarıyla oluşturuldu']);
            } else {
                jsonResponse(['success' => false, 'error' => 'Etkinlik oluşturulamadı']);
}
        } catch (PDOException $e) {
            error_log("Events API Create Error: " . $e->getMessage());
            jsonResponse(['success' => false, 'error' => 'Veritabanı hatası: ' . $e->getMessage()]);
        }
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['id'])) {
        // URL'den ID'yi almaya çalış
        $pathParts = explode('/', trim($_SERVER['REQUEST_URI'], '/'));
        $eventId = end($pathParts);
        
        if (!$eventId || !is_numeric($eventId)) {
            jsonResponse(['success' => false, 'error' => 'Etkinlik ID\'si belirtilmemiş']);
        }
    } else {
        $eventId = $input['id'];
    }

    try {
        // Önce etkinliğin var olup olmadığını kontrol et
        $stmt = $db->prepare("SELECT etkinlik_adi FROM $eventsDatabase.etkinlikler WHERE id = ?");
        $stmt->execute([$eventId]);
        $event = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$event) {
            jsonResponse(['success' => false, 'error' => 'Etkinlik bulunamadı']);
        }
        
        // İlgili eşleşmeleri de sil
        $stmt = $db->prepare("DELETE FROM $eventsDatabase.pk_eslesmeleri WHERE etkinlik_id = ?");
        $stmt->execute([$eventId]);
        
        // Etkinliği sil
        $stmt = $db->prepare("DELETE FROM $eventsDatabase.etkinlikler WHERE id = ?");
        $result = $stmt->execute([$eventId]);
        
        if ($result) {
            jsonResponse(['success' => true, 'message' => 'Etkinlik başarıyla silindi']);
        } else {
            jsonResponse(['success' => false, 'error' => 'Etkinlik silinemedi']);
        }
    } catch (PDOException $e) {
        error_log("Events API Delete Error: " . $e->getMessage());
        jsonResponse(['success' => false, 'error' => 'Veritabanı hatası: ' . $e->getMessage()]);
    }
}

jsonResponse(['success' => false, 'error' => 'Geçersiz istek metodu']);