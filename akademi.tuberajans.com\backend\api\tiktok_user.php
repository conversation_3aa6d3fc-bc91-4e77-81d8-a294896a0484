<?php
// Session ayarları config.php'den önce yapılmamalı
require_once dirname(__DIR__) . '/config/config.php';

// Veritabanı bağlantısını kontrol et
if (!isset($conn) || !isset($db)) {
    error_log('TikTok user error: Database connection not available');
    die(json_encode([
        'status' => 'error',
        'message' => 'Database connection not available'
    ]));
}

// CORS başlıkları
header("Access-Control-Allow-Origin: https://akademi.tuberajans.com");
header("Access-Control-Allow-Credentials: true");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
header("Content-Type: application/json; charset=utf-8");

// OPTIONS isteği kontrolü
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // Auth sınıfını başlat
    if (!class_exists('Auth')) {
        require_once __DIR__ . '/auth.php';
    }
    
    $auth = new Auth($conn);
    
    // Mevcut kullanıcıyı al
    $currentUser = $auth->getCurrentUser();
    
    if (!$currentUser) {
        error_log('TikTok user error: Unauthorized access - no current user');
        http_response_code(401);
        echo json_encode([
            'status' => 'error',
            'message' => 'Unauthorized access'
        ]);
        exit;
    }

    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        if (isset($_GET['action']) && $_GET['action'] === 'check_session') {
            // TikTok oturumunu kontrol et
            $hasTikTokSession = !empty($currentUser['tiktok_open_id']);
            
            $response = [
                'status' => 'success',
                'has_tiktok_session' => $hasTikTokSession,
                'tiktok_user' => $hasTikTokSession ? [
                    'open_id' => $currentUser['tiktok_open_id'],
                    'union_id' => $currentUser['tiktok_union_id'],
                    'username' => $currentUser['tiktok_username'],
                    'display_name' => $currentUser['tiktok_display_name'],
                    'avatar_url' => $currentUser['tiktok_avatar_url'],
                    'bio_description' => $currentUser['tiktok_bio'],
                    'is_verified' => (bool)$currentUser['is_verified'],
                    'follower_count' => (int)$currentUser['follower_count'],
                    'following_count' => (int)$currentUser['following_count'],
                    'likes_count' => (int)$currentUser['likes_count'],
                    'video_count' => (int)$currentUser['video_count']
                ] : null
            ];
            
            echo json_encode($response);
                exit;
            }

        // TikTok bilgilerini getir
        if (!empty($currentUser['tiktok_open_id'])) {
            $tiktokUser = [
                'open_id' => $currentUser['tiktok_open_id'],
                'union_id' => $currentUser['tiktok_union_id'],
                'username' => $currentUser['tiktok_username'],
                'display_name' => $currentUser['tiktok_display_name'],
                'avatar_url' => $currentUser['tiktok_avatar_url'],
                'bio_description' => $currentUser['tiktok_bio'],
                'is_verified' => (bool)$currentUser['is_verified'],
                'follower_count' => (int)$currentUser['follower_count'],
                'following_count' => (int)$currentUser['following_count'],
                'likes_count' => (int)$currentUser['likes_count'],
                'video_count' => (int)$currentUser['video_count']
            ];

            echo json_encode([
                'status' => 'success',
                'tiktok_user' => $tiktokUser
            ]);
        } else {
            echo json_encode([
                'status' => 'error',
                'message' => 'No TikTok account linked'
            ]);
        }
    } else if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // TikTok bilgilerini güncelle
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            throw new Exception('Invalid request data');
        }

        $stmt = $conn->prepare("
            UPDATE users 
            SET 
                tiktok_open_id = ?,
                tiktok_union_id = ?,
                tiktok_username = ?,
                tiktok_display_name = ?,
                tiktok_avatar_url = ?,
                tiktok_bio = ?,
                is_verified = ?,
                follower_count = ?,
                following_count = ?,
                likes_count = ?,
                video_count = ?,
                access_token = ?,
                refresh_token = ?,
                token_expires_at = DATE_ADD(NOW(), INTERVAL ? SECOND),
                tiktok_linked_at = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ");

        $stmt->bind_param(
            'ssssssiiiiissis',
            $data['open_id'],
            $data['union_id'],
            $data['username'],
            $data['display_name'],
            $data['avatar_url'],
            $data['bio_description'],
            $data['is_verified'],
            $data['follower_count'],
            $data['following_count'],
            $data['likes_count'],
            $data['video_count'],
            $data['access_token'],
            $data['refresh_token'],
            $data['expires_in'],
            $currentUser['id']
        );

        if ($stmt->execute()) {
            echo json_encode([
                'status' => 'success',
                'message' => 'TikTok user info updated successfully'
            ]);
        } else {
            throw new Exception('Failed to update TikTok user info: ' . $stmt->error);
        }
    }
} catch (Exception $e) {
    error_log('TikTok user error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Internal server error',
        'debug' => $e->getMessage()
    ]);
}
?>
