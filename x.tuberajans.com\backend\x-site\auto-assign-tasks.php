<?php
require_once __DIR__ . '/../config/config.php';

/**
 * Görev metriklerini valide eden fonksiyonlar
 */

// Haftalık yayın günü validasyonu (1-7 gün arası)
function validateStreamDays($days) {
    return max(1, min(7, intval($days)));
}

// Haftalık yayın süresi validasyonu (1-168 saat arası, haftalık maksimum)
function validateStreamHours($hours) {
    return max(1, min(168, intval($hours)));
}

// Elmas hedefi validasyonu (makul sınırlar içinde)
function validateDiamonds($diamonds) {
    return max(100, min(50000, intval($diamonds)));
}

// Takipçi hedefi validasyonu (haftalık makul artış)
function validateFollowers($followers) {
    return max(10, min(5000, intval($followers)));
}

// PK mücadelesi validasyonu (günde maksimum 5 PK = haftalık 35)
function validateMatches($matches) {
    return max(1, min(35, intval($matches)));
}

// Video paylaşımı validasyonu (günde maksimum 3 video = haftalık 21)
function validateVideos($videos) {
    return max(1, min(21, intval($videos)));
}

// Veritabanı bağlantısını kontrol et
if (!isset($db) || $db === null) {
    // Veritabanı bağlantısı zaten config.php'de kurulmuş olmalı, eğer yoksa hata göndeririz
    error_log("Veritabanı bağlantısı bulunamadı ($db değişkeni tanımlı değil)");
    jsonResponse(['error' => 'Veritabanı bağlantısı kurulamadı. Lütfen daha sonra tekrar deneyin.'], 500);
    exit;
}

// POST isteği kontrolü
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['error' => 'Geçersiz istek metodu'], 405);
    exit;
}

// JSON verisini al
$data = json_decode(file_get_contents('php://input'), true);

// Başlangıç ve bitiş tarihlerini al
$startDate = $data['start_date'] ?? $data['startDate'] ?? date('Y-m-d');
$endDate = $data['end_date'] ?? $data['endDate'] ?? null;

// Eğer bitiş tarihi yoksa, başlangıç tarihine 7 gün ekle
if (!$endDate) {
    $startDateTime = new DateTime($startDate);
    $endDateTime = clone $startDateTime;
    $endDateTime->modify('+7 days');
    $endDate = $endDateTime->format('Y-m-d');
}

error_log("Auto Assign Tasks: Başlangıç tarihi: $startDate, Bitiş tarihi: $endDate");

try {
    // Veritabanı bağlantısını son kez kontrol edelim
    if (!isset($db) || $db === null) {
        throw new Exception("Veritabanı bağlantısı kurulamadı");
    }

    // Önceki 4 haftanın başlangıcı
    $startDateObj = new DateTime($startDate);
    $prev4WeekStart = clone $startDateObj;
    $prev4WeekStart->modify('-28 days');
    $prev4WeekStartStr = $prev4WeekStart->format('Y-m-d');

    // publisher_info tablosundan tüm yayıncıları çek
    $stmt = $db->prepare("SELECT username FROM publisher_info");
    $stmt->execute();
    $publishers = $stmt->fetchAll(PDO::FETCH_COLUMN);

    $users = $publishers; // Artık kullanıcılar güncel yayıncılar

    $assignCount = 0;
    $errorCount = 0;
    $errorDetails = [];

    // Önce bu hafta için mevcut görevleri sil (yeniden atama için)
    $deleteQuery = "DELETE FROM weekly_tasks WHERE hafta_baslangici = ? AND hafta_bitisi = ?";
    $stmt = $db->prepare($deleteQuery);
    $stmt->execute([$startDate, $endDate]);
    $deletedCount = $stmt->rowCount();
    error_log("Auto Assign Tasks: $deletedCount mevcut görev silindi (hafta: $startDate - $endDate)");

    // 2. Her kullanıcı için performans metriklerini çek ve görevleri oluştur
    foreach ($users as $username) {
        // Kullanıcı metriklerini weekly_archive'den çek (önceki 4 hafta)
        $metricsQuery = "SELECT
            AVG(yayin_suresi) as avg_stream_time,
            AVG(canli_yayin_gunu) as avg_stream_days,
            AVG(elmaslar) as avg_diamonds,
            AVG(yeni_takipciler) as avg_followers,
            AVG(maclar) as avg_pk_battles
          FROM weekly_archive
          WHERE kullanici_adi = ?
          AND hafta_baslangici >= ? AND hafta_baslangici < ?";
        $stmt = $db->prepare($metricsQuery);
        $stmt->execute([$username, $prev4WeekStartStr, $startDate]);
        $metrics = $stmt->fetch(PDO::FETCH_ASSOC);

        // Eğer metrik yoksa, default değerlerle görev ata
        if (!$metrics || empty(array_filter($metrics))) {
            $metrics = [
                'avg_stream_time' => 0,
                'avg_stream_days' => 0,
                'avg_diamonds' => 0,
                'avg_followers' => 0,
                'avg_pk_battles' => 0
            ];
        }

        // Metriklerden değerler - TikTok algoritması için iyileştirmeler ve validasyon fonksiyonları kullanılarak
        // Haftalık yayın günleri: Validasyon fonksiyonu ile kontrol edilir (maksimum 7 gün)
        $calculatedStreamDays = ceil($metrics['avg_stream_days'] * 1.1);
        $streamDays = validateStreamDays(max(3, min(7, $calculatedStreamDays)));

        // Haftalık toplam yayın süresi: Validasyon fonksiyonu ile kontrol edilir
        $calculatedStreamTime = ceil($metrics['avg_stream_time'] * 1.2 / 3600);
        $streamTime = validateStreamHours(max(4, $calculatedStreamTime));

        // Elmas hedefi: Validasyon fonksiyonu ile kontrol edilir
        $calculatedDiamonds = ceil($metrics['avg_diamonds'] * 1.15);
        $diamonds = validateDiamonds(max(500, $calculatedDiamonds));

        // Takipçi hedefi: Validasyon fonksiyonu ile kontrol edilir
        $calculatedFollowers = ceil($metrics['avg_followers'] * 1.2);
        $followers = validateFollowers(max(50, $calculatedFollowers));

        // PK mücadelesi: Validasyon fonksiyonu ile kontrol edilir
        $calculatedMatches = ceil($metrics['avg_pk_battles'] * 1.3);
        $matches = validateMatches(max(2, $calculatedMatches));

        // Video paylaşımı: Validasyon fonksiyonu ile kontrol edilir
        $calculatedVideos = ceil($metrics['avg_stream_days'] * 0.8);
        $videos = validateVideos(max(4, $calculatedVideos));

        // Debug bilgisi (log'a yazdır)
        error_log("Kullanıcı: $username - Hesaplanan değerler: Gün:$streamDays, Saat:$streamTime, Elmas:$diamonds, Takipçi:$followers, PK:$matches, Video:$videos");

        // Görevleri ekle - TikTok algoritması için optimize edilmiş ve mantıklı zorluk seviyeleri
        $tasks = [
            [
                "title" => "$streamDays Gün Yayın Yap",
                "description" => "Bu hafta en az $streamDays farklı günde yayın aç. TikTok algoritması düzenli içerik üretimini ödüllendirir.",
                "difficulty" => $streamDays >= 6 ? "Zor" : ($streamDays >= 4 ? "Orta" : "Kolay")
            ],
            [
                "title" => "Bu Hafta Toplamda En Az $streamTime Saat Canlı Yayın Yap",
                "description" => "Bu hafta toplamda en az $streamTime saat canlı yayın yap. Uzun yayınlar TikTok algoritmasında avantaj sağlar.",
                "difficulty" => $streamTime >= 25 ? "Zor" : ($streamTime >= 12 ? "Orta" : "Kolay")
            ],
            [
                "title" => "$diamonds Elmas Kazan",
                "description" => "Yayınlarında izleyici etkileşimini artırarak $diamonds elmas kazanmayı hedefle.",
                "difficulty" => $diamonds >= 5000 ? "Zor" : ($diamonds >= 2000 ? "Orta" : "Kolay")
            ],
            [
                "title" => "$followers Takipçi Kazan",
                "description" => "Sosyal medya etkileşimlerini artırarak $followers yeni takipçi kazanmayı hedefle.",
                "difficulty" => $followers >= 1000 ? "Zor" : ($followers >= 300 ? "Orta" : "Kolay")
            ],
            [
                "title" => "$matches PK Mücadelesi Yap",
                "description" => "Bu hafta $matches farklı yayıncıyla PK yap. PK'lar daha fazla keşfete çıkmanı sağlar.",
                "difficulty" => $matches >= 10 ? "Zor" : ($matches >= 5 ? "Orta" : "Kolay")
            ],
            [
                "title" => "$videos Video Paylaş",
                "description" => "Bu hafta en az $videos video paylaş. TikTok algoritması düzenli içerik üretimini ödüllendirir.",
                "difficulty" => $videos >= 10 ? "Orta" : "Kolay"
            ]
        ];

        // Görevleri veritabanına ekle (önceden sildiğimiz için sadece INSERT)
        $insertQuery = "INSERT INTO weekly_tasks (kullanici_adi, hafta_baslangici, hafta_bitisi, gorev_onerisi, gorev_zorlugu, tamamlandi, puan, durum)
            VALUES (?, ?, ?, ?, ?, 0, ?, 'beklemede')";
        $stmt = $db->prepare($insertQuery);
        foreach ($tasks as $task) {
            // Zorluk seviyesine göre puan belirle
            $points = 0;
            switch($task["difficulty"]) {
                case "Kolay": $points = 10; break;
                case "Orta": $points = 20; break;
                case "Zor": $points = 30; break;
                default: $points = 15;
            }
            try {
                $stmt->execute([
                    $username,
                    $startDate,
                    $endDate,
                    $task["title"],
                    $task["difficulty"],
                    $points
                ]);
            } catch (Exception $e) {
                $errorCount++;
                $errorDetails[] = "Görev eklenirken hata: " . $e->getMessage() . " (Kullanıcı: $username)";
                error_log("Görev ekleme hatası: " . $e->getMessage() . " (Kullanıcı: $username)");
                continue;
            }
        }

        $assignCount++;
    }

    // Eğer hiç kullanıcı bulunamadıysa hata döndür
    if (count($users) === 0) {
        jsonResponse([
            "success" => false,
            "message" => "Seçili haftada hiç kullanıcı kaydı bulunamadı.",
            "date_range" => ["start" => $startDate, "end" => $endDate]
        ]);
        exit;
    }

    jsonResponse([
        "success" => true,
        "message" => "$assignCount kullanıcıya görev atandı.",
        "success_count" => $assignCount,
        "date_range" => ["start" => $startDate, "end" => $endDate],
        "errors" => $errorCount,
        "error_details" => $errorDetails
    ]);

} catch (PDOException $e) {
    error_log("Otomatik görev atama hatası: " . $e->getMessage());
    jsonResponse([
        "success" => false,
        "errors" => 1,
        "error" => "Görevler atanırken bir hata oluştu: " . $e->getMessage()
    ], 500);
} catch (Exception $e) {
    error_log("Genel hata: " . $e->getMessage());
    jsonResponse([
        "success" => false,
        "errors" => 1,
        "error" => "İşlem sırasında bir hata oluştu: " . $e->getMessage()
    ], 500);
}