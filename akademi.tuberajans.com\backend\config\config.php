<?php
/**
 * Akademi Backend Yapılandırma Dosyası
 *
 * <PERSON>u dosya, akademi.tuberajans.com sitesi için veritabanı bağlantı bilgilerini ve
 * diğer yapılandırma ayarlarını içerir.
 */

// Kök dizini tanımla
define('BASE_PATH', realpath(dirname(__DIR__)));

// Session ayarları (session başlamadan önce yapılmalı)
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 1);

// Session cookie parametreleri
session_set_cookie_params([
    'lifetime' => 86400, // 24 saat
    'path' => '/',
    'domain' => '.tuberajans.com',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Strict'
]);

// Şimdi session'ı başlat
session_start();

// Hata raporlama
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', BASE_PATH . '/logs/error.log');

// Zaman dilimi
date_default_timezone_set('Europe/Istanbul');

// Veritabanı bağlantı bilgileri
define('DB_HOST', '**************');
define('DB_USER', 'root');
define('DB_PASS', 'Bebek845396!');
define('DB_NAME', 'tuberaja_yayinci_akademi');

// TikTok API bilgileri
define('TIKTOK_CLIENT_KEY', 'awfw8k9nim1e8dmu');
define('TIKTOK_CLIENT_SECRET', getenv('TIKTOK_CLIENT_SECRET'));
define('TIKTOK_REDIRECT_URI', 'https://akademi.tuberajans.com/backend/api/tiktok-callback.php');

// CORS ayarları
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Allow-Origin: https://akademi.tuberajans.com');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// UTF-8
header('Content-Type: application/json; charset=utf-8');

// Veritabanı bağlantısı
try {
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    $conn->set_charset("utf8mb4");
    
    if ($conn->connect_error) {
        throw new Exception("Veritabanı bağlantı hatası: " . $conn->connect_error);
    }

    // PDO bağlantısı
    $db = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $db->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

    // Takip veritabanı bağlantısı
    $db_takip = new PDO("mysql:host=" . DB_HOST . ";dbname=tuberaja_yayinci_takip;charset=utf8mb4", DB_USER, DB_PASS);
    $db_takip->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $db_takip->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

} catch (Exception $e) {
    error_log($e->getMessage());
    http_response_code(500);
    die(json_encode(['status' => 'error', 'message' => 'Veritabanı bağlantı hatası']));
}

// CSRF token oluştur
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Site URL'si
$site_url = "https://akademi.tuberajans.com";

// Yükleme dizini
$upload_dir = BASE_PATH . "/../uploads/";

// İzin verilen dosya türleri
$allowed_file_types = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'];

// Maksimum dosya boyutu (5MB)
$max_file_size = 5 * 1024 * 1024;

/**
 * Veritabanı bağlantısını döndüren yardımcı fonksiyon
 */
function getDB() {
    global $db;
    return $db;
}

/**
 * Takip veritabanı bağlantısını döndüren yardımcı fonksiyon
 */
function getDBTakip() {
    global $db_takip;
    return $db_takip;
}

// Helpers dosyasını yükle
$helpers_file = BASE_PATH . '/includes/helpers.php';
if (file_exists($helpers_file)) {
    require_once $helpers_file;
}
