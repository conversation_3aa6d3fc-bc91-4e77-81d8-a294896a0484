<?php
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");
header("Expires: 0");
// Buffer'ı temizle
if (ob_get_level()) ob_end_clean();
ob_start();

header('Content-Type: application/json; charset=utf-8');

// CORS başlıkları
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once __DIR__ . '/../config/config.php';

// OPTIONS isteklerini hemen yanıtla
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Content-Type: application/json; charset=utf-8');
    http_response_code(200);
    exit;
}

// Tüm tablo ve sütunları veritabanından al
try {
    $schema = [];
    
    // INFORMATION_SCHEMA'dan tüm tabloları ve sütunları çek
    $query = "
    SELECT 
        t.TABLE_SCHEMA AS veritabani_adi,
        t.TABLE_NAME AS tablo_adi, 
        c.COLUMN_NAME AS sutun_adi, 
        c.DATA_TYPE AS veri_tipi, 
        c.COLUMN_COMMENT AS aciklama,
        c.COLUMN_KEY AS anahtar,
        c.IS_NULLABLE AS bos_olabilir,
        c.COLUMN_DEFAULT AS varsayilan
    FROM 
        INFORMATION_SCHEMA.TABLES t
    JOIN 
        INFORMATION_SCHEMA.COLUMNS c ON t.TABLE_NAME = c.TABLE_NAME AND t.TABLE_SCHEMA = c.TABLE_SCHEMA
    WHERE 
        t.TABLE_SCHEMA IN ('tuberaja_yayinci_takip', 'tuberaja_yayinci_akademi')
    ORDER BY 
        t.TABLE_SCHEMA, t.TABLE_NAME, c.ORDINAL_POSITION;
    ";
    
    $stmt = $db->query($query);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Sonuçları tablolara göre gruplandır
    $tables = [];
    foreach ($results as $row) {
        $tableName = $row['tablo_adi'];
        $dbName = $row['veritabani_adi'];
        $fullTableName = $dbName . '.' . $tableName;
        
        if (!isset($tables[$fullTableName])) {
            $tables[$fullTableName] = [
                'database' => $dbName,
                'tableName' => $tableName,
                'description' => getTableDescription($tableName),
                'columns' => []
            ];
        }
        
        // Tekrarlanan sütunları önlemek için kontrol
        $columnExists = false;
        foreach ($tables[$fullTableName]['columns'] as $col) {
            if ($col['name'] === $row['sutun_adi']) {
                $columnExists = true;
                break;
            }
        }
        
        if (!$columnExists) {
            $tables[$fullTableName]['columns'][] = [
                'name' => $row['sutun_adi'],
                'type' => $row['veri_tipi'],
                'description' => !empty($row['aciklama']) ? $row['aciklama'] : getColumnDescription($tableName, $row['sutun_adi']),
                'key' => $row['anahtar'],
                'nullable' => $row['bos_olabilir'] === 'YES'
            ];
        }
    }
    
    // Tabloları diziye çevir
    $finalTables = array_values($tables);
    
    // Başarılı yanıt
    echo json_encode([
        'success' => true,
        'tables' => $finalTables
    ]);
    
} catch (Exception $e) {
    error_log("DB Schema API Hatası: " . $e->getMessage());
    
    // Hata yanıtı
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

// Tablo açıklaması tahmini
function getTableDescription($tableName) {
    // Burada tablo ismine göre açıklama yapıyoruz
    $descriptions = [
        'users' => 'Sistem kullanıcıları',
        'yayinci' => 'Yayıncı temel bilgileri',
        'publisher_info' => 'Yayıncı detaylı bilgileri',
        'performans' => 'Yayıncı performans metrikleri',
        'weekly_archive' => 'Haftalık performans arşivi',
        'etkinlikler' => 'Ajans etkinlikleri',
        'etkinlik_katilimcilar' => 'Etkinlik katılımcıları',
        'gorevler' => 'Yayıncı görevleri',
        'pk_eslesmeleri' => 'PK maçları ve eşleştirmeleri',
        'task_settings' => 'Görev sistemi ayarları',
        'influencer_info' => 'Influencer detayları',
        'login_attempts' => 'Giriş denemeleri kaydı',
        'notifications' => 'Bildirimler',
        'user_tokens' => 'Kullanıcı token bilgileri',
        'weekly_tasks' => 'Haftalık görevler',
        'ai_learning_logs' => 'AI öğrenme kayıtları',
        'email_send_logs' => 'E-posta gönderim kayıtları'
    ];
    
    return isset($descriptions[$tableName]) ? $descriptions[$tableName] : "Tablo: $tableName";
}

// Sütun açıklaması tahmini
function getColumnDescription($tableName, $columnName) {
    // Ortak sütun tipleri
    if ($columnName === 'id') return 'Kayıt ID';
    if ($columnName === 'user_id') return 'Kullanıcı ID';
    if ($columnName === 'yayinci_id') return 'Yayıncı ID';
    if ($columnName === 'kullanici_adi') return 'TikTok kullanıcı adı';
    if ($columnName === 'email') return 'E-posta adresi';
    if ($columnName === 'created_at') return 'Oluşturulma tarihi';
    if ($columnName === 'updated_at') return 'Güncelleme tarihi';
    if (str_contains($columnName, 'tarih')) return 'Tarih bilgisi';
    if (str_contains($columnName, 'durum')) return 'Durum bilgisi';
    
    // Tabloya özel sütunlar
    if ($tableName === 'yayinci') {
        switch ($columnName) {
            case 'elmaslar': return 'Kazanılan elmas miktarı';
            case 'aboneler': return 'Toplam abone sayısı';
            case 'yeni_takipciler': return 'Yeni takipçi sayısı';
            case 'maclar': return 'Yapılan PK maç sayısı';
            case 'yayin_suresi': return 'Toplam yayın süresi';
        }
    }
    
    if ($tableName === 'performans') {
        switch ($columnName) {
            case 'elmaslar': return 'Haftalık elmas miktarı';
            case 'yayin_suresi': return 'Haftalık yayın süresi';
            case 'canli_yayin_gunu': return 'Canlı yayın yapılan gün sayısı';
        }
    }
    
    // Genel sütun adından açıklama oluştur
    return ucfirst(str_replace('_', ' ', $columnName));
} 