<?php
/**
 * Users tablosunu kontrol etmek için debug script
 */

ini_set('display_errors', 1);
error_reporting(E_ALL);

require_once __DIR__ . '/../config/config.php';

// CORS ayarları
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json');

try {
    // Tablo var mı kontrol et
    $stmt = $db_takip->query("SHOW TABLES LIKE 'users'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        echo json_encode([
            'success' => false,
            'message' => 'users tablosu bulunamadı!',
            'action_needed' => 'Veritabanında users tablosunu oluşturmanız gerekiyor.'
        ]);
        exit;
    }
    
    // Tablo yapısını kontrol et
    $stmt = $db_takip->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // <PERSON>llanı<PERSON><PERSON> sayısını kontrol et
    $stmt = $db_takip->query("SELECT COUNT(*) as count FROM users");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // Örnek bir kullanıcı al
    $sampleUser = null;
    if ($count > 0) {
        $stmt = $db_takip->query("SELECT * FROM users LIMIT 1");
        $sampleUser = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Hassas bilgileri gizle
        if (isset($sampleUser['password'])) {
            $sampleUser['password'] = '***HIDDEN***';
        }
    }
    
    echo json_encode([
        'success' => true,
        'table_exists' => true,
        'columns' => $columns,
        'user_count' => $count,
        'sample_user' => $sampleUser
    ]);
    
} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Veritabanı hatası: ' . $e->getMessage()
    ]);
}
