import{j as e}from"./reactDnd-CIvPAkL_.js";import{r as t,u as q}from"./vendor-CnpYymF8.js";import{m as c,l as f,Y as J,o as w,a3 as N,B as S,s as z,I as G,i as m,T as Q,V as U,n as Y,x as _,H as K,a4 as o}from"./antd-gS---Efz.js";import{I as X,s as D,h as Z,r as ee,c as ae}from"./App-DHTOkUiy.js";import{R as $,a as M}from"./SaveOutlined-JjyO-d99.js";import"./index-Bso3pfdw.js";import"./utils-CtuI0RRe.js";import"./charts-CXWFy-zF.js";var ie={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M864 736c0-111.6-65.4-208-160-252.9V317.3c0-15.1-5.3-29.7-15.1-41.2L536.5 95.4C530.1 87.8 521 84 512 84s-18.1 3.8-24.5 11.4L335.1 276.1a63.97 63.97 0 00-15.1 41.2v165.8C225.4 528 160 624.4 160 736h156.5c-2.3 7.2-3.5 15-3.5 23.8 0 22.1 7.6 43.7 21.4 60.8a97.2 97.2 0 0043.1 30.6c23.1 54 75.6 88.8 134.5 88.8 29.1 0 57.3-8.6 81.4-24.8 23.6-15.8 41.9-37.9 53-64a97 97 0 0043.1-30.5 97.52 97.52 0 0021.4-60.8c0-8.4-1.1-16.4-3.1-23.8H864zM762.3 621.4c9.4 14.6 17 30.3 22.5 46.6H700V558.7a211.6 211.6 0 0162.3 62.7zM388 483.1V318.8l124-147 124 147V668H388V483.1zM239.2 668c5.5-16.3 13.1-32 22.5-46.6 16.3-25.2 37.5-46.5 62.3-62.7V668h-84.8zm388.9 116.2c-5.2 3-11.2 4.2-17.1 3.4l-19.5-2.4-2.8 19.4c-5.4 37.9-38.4 66.5-76.7 66.5-38.3 0-71.3-28.6-76.7-66.5l-2.8-19.5-19.5 2.5a27.7 27.7 0 01-17.1-3.5c-8.7-5-14.1-14.3-14.1-24.4 0-10.6 5.9-19.4 14.6-23.8h231.3c8.8 4.5 14.6 13.3 14.6 23.8-.1 10.2-5.5 19.6-14.2 24.5zM464 400a48 48 0 1096 0 48 48 0 10-96 0z"}}]},name:"rocket",theme:"outlined"};function A(){return A=Object.assign?Object.assign.bind():function(l){for(var r=1;r<arguments.length;r++){var p=arguments[r];for(var x in p)Object.prototype.hasOwnProperty.call(p,x)&&(l[x]=p[x])}return l},A.apply(this,arguments)}const se=(l,r)=>t.createElement(X,A({},l,{ref:r,icon:ie})),le=t.forwardRef(se),{Option:u}=z,{TextArea:re}=G,{TabPane:g}=w,{Title:h,Text:n,Paragraph:R}=Q;async function te(){return[{id:1,isim_soyisim:"Ahmet Yılmaz",username:"ahmetyilmaz",followers:12e3,elmaslar:500},{id:2,isim_soyisim:"Ayşe Demir",username:"aysedemir",followers:8e3,elmaslar:300},{id:3,isim_soyisim:"Mehmet Kaya",username:"mehmetkaya",followers:15e3,elmaslar:700}]}const ne={async getAdvice(l,r){return JSON.stringify({etkinlik_adi:"Örnek Etkinlik",aciklama:"Bu bir örnek açıklamadır.",baslangic_tarihi:"2024-06-01",bitis_tarihi:"2024-06-07",kurallar:"Kurallar burada yer alacak.",puanlama_sistemi:"Elmas ve yayın süresi bazlı puanlama.",stratejiler:["Düzenli yayın yapın","Takipçileri teşvik edin"],whatsapp_mesaji:"WhatsApp için örnek mesaj",email_mesaji:"E-posta için örnek mesaj",eslesme_bilgileri:[{yayinci1:"Ahmet Yılmaz",yayinci2:"Ayşe Demir"},{yayinci1:"Mehmet Kaya",yayinci2:"Ahmet Yılmaz"}]})}},je=()=>{const[l,r]=t.useState(!1),[p,x]=t.useState([]),[a,v]=t.useState(null),[y]=c.useForm(),[O,B]=t.useState("event"),[ce,de]=t.useState("whatsapp"),[me,E]=t.useState(!1),C=q(),V=async()=>{try{const i=await te();x(i)}catch(i){o.error({message:"Veri Yükleme Hatası",description:"Yayıncı verileri yüklenirken bir hata oluştu."})}};t.useEffect(()=>{V()},[]);const H=i=>{const{event_type:s,publishers:k,goal:j,goal_detail:P}=i,F=k.map(d=>{const b=k.find(W=>W.id===d);return b?{name:b.isim_soyisim,username:b.username,followers:b.followers||0,diamonds:b.elmaslar||0}:null}).filter(Boolean);return`TikTok yayıncı ajansı için bir ${s==="leaderboard"?"Lider Tablosu Etkinliği":"PK Turnuvası"} planlamak istiyorum.

    Hedefim: ${j}
    ${P?`Ek Detaylar: ${P}`:""}
    
    Aşağıdaki yayıncılarımız için en iyi eşleştirmeleri, zamanlama önerilerini ve stratejileri öner:
    ${F.map(d=>`- ${d.name} (@${d.username}): ${d.followers} takipçi, haftalık ortalama ${d.diamonds||0} elmas`).join(`
`)}
    
    Aşağıdakileri içeren kapsamlı bir TÜRKÇE etkinlik planı oluştur:
    1. Etkinlik adı önerisi
    2. Optimal başlangıç ve bitiş tarihleri/saatleri (önümüzdeki 1-2 hafta içinde)
    3. ${s==="leaderboard"?"Puan hesaplama yöntemi (Elmas vs. Yayın Süresi)":"Tur sayısı ve eşleştirme stratejisi"}
    4. Yayıncılar için performans hedefleri
    5. Etkinliğin başarısını artıracak özel stratejiler ve ipuçları
    6. Yayıncılara gönderilecek WhatsApp duyuru mesajı
    7. E-posta ile gönderilecek duyuru metni
    
    JSON formatında dön:
    {
      "etkinlik_adi": "Etkinlik adı",
      "aciklama": "Kısa açıklama",
      "baslangic_tarihi": "YYYY-MM-DD",
      "bitis_tarihi": "YYYY-MM-DD",
      "kurallar": "Detaylı kurallar",
      ${s==="leaderboard"?'"puanlama_sistemi": "Puanlama sistemi açıklaması",':'"tur_sayisi": "Tur sayısı","eslesme_bilgileri": [{ "yayinci1": "isim1", "yayinci2": "isim2" }],'}
      "stratejiler": ["Strateji 1", "Strateji 2"],
      "whatsapp_mesaji": "WhatsApp için duyuru mesajı",
      "email_mesaji": "E-posta için duyuru mesajı"
    }`},L=async i=>{r(!0);try{const s=H(i),k=await ne.getAdvice(s,"event_planning");try{const j=JSON.parse(k);v(j)}catch(j){v({raw_response:k})}}catch(s){o.error({message:"AI Hatası",description:"Öneri oluşturulurken bir hata meydana geldi."})}finally{r(!1)}},T=()=>{if(a)try{const i=y.getFieldValue("publishers"),s={etkinlik_adi:a.etkinlik_adi,etkinlik_tipi:y.getFieldValue("event_type")==="leaderboard"?"lider_tablosu":"pk_turnuva",aciklama:a.aciklama,kurallar:a.kurallar,baslangic_tarihi:a.baslangic_tarihi,bitis_tarihi:a.bitis_tarihi,yayincilar:i,puanlama_sistemi:a.puanlama_sistemi,eslesme_bilgileri:a.eslesme_bilgileri};C("/event/create",{state:{prefillData:s}}),o.success({message:"Etkinlik Taslağı Hazır",description:"AI önerileri doğrultusunda etkinlik taslağı oluşturuldu. Şimdi detayları düzenleyebilirsiniz."})}catch(i){o.error({message:"Hata",description:"Etkinlik taslağı oluşturulurken bir hata meydana geldi."})}},I=i=>{navigator.clipboard.writeText(i).then(()=>{E(!0),o.success({message:"Kopyalandı",description:"Mesaj panoya kopyalandı"}),setTimeout(()=>E(!1),3e3)}).catch(s=>{o.error({message:"Kopyalama Hatası",description:"Mesaj panoya kopyalanamadı"})})};return e.jsxs("div",{className:"ai-event-advisor-container",children:[e.jsxs(f,{title:e.jsxs("div",{className:"flex items-center",children:[e.jsx(le,{className:"text-xl text-purple-500 mr-2"}),e.jsx("span",{className:"text-xl",children:"AI Etkinlik Planlama Danışmanı"})]}),className:"mb-6",children:[e.jsx(J,{message:"AI ile Etkinlik Planlama",description:"AI destekli etkinlik danışmanı, yayıncılarınıza özel Lider Tablosu Etkinlikleri veya PK Turnuvaları planlamanıza yardımcı olur. Yayıncıların performans verilerini analiz ederek en uygun etkinlik planını, kuralları ve duyuru mesajlarını önerir.",type:"info",showIcon:!0,closable:!0,className:"mb-6"}),e.jsxs(w,{activeKey:O,onChange:B,children:[e.jsx(g,{tab:"Etkinlik Oluştur",children:e.jsxs(c,{form:y,layout:"vertical",onFinish:L,className:"mt-4",children:[e.jsx(c.Item,{name:"event_type",label:"Etkinlik Türü",initialValue:"leaderboard",rules:[{required:!0}],children:e.jsxs(N.Group,{children:[e.jsx(N.Button,{value:"leaderboard",className:"bg-white dark:bg-gray-800",children:e.jsx("div",{className:"px-2",children:e.jsx(S,{color:"blue",text:"Lider Tablosu"})})}),e.jsx(N.Button,{value:"pk_tournament",className:"bg-white dark:bg-gray-800",children:e.jsx("div",{className:"px-2",children:e.jsx(S,{color:"orange",text:"PK Turnuvası"})})})]})}),e.jsx(c.Item,{name:"publishers",label:"Katılacak Yayıncılar",rules:[{required:!0,message:"Lütfen en az bir yayıncı seçin!"}],children:e.jsx(z,{mode:"multiple",placeholder:"Yayıncıları seçin",optionFilterProp:"children",style:{width:"100%"},showSearch:!0,filterOption:(i,s)=>(s==null?void 0:s.children).toLowerCase().includes(i.toLowerCase()),children:p.map(i=>e.jsxs(u,{value:i.id,children:[i.isim_soyisim," (@",i.username,")",i.followers&&` - ${i.followers} takipçi`,i.elmaslar&&` - ${i.elmaslar} elmas/hafta`]},i.id))})}),e.jsx(c.Item,{name:"goal",label:"Etkinlik Hedefi",rules:[{required:!0,message:"Lütfen bir hedef belirtin!"}],children:e.jsxs(z,{placeholder:"Etkinlik ana hedefini seçin",children:[e.jsx(u,{value:"elmas_artisi",children:"Elmas gelirlerini artırmak"}),e.jsx(u,{value:"takipci_artisi",children:"Takipçi sayısını artırmak"}),e.jsx(u,{value:"izleyici_artisi",children:"İzleyici sayısını artırmak"}),e.jsx(u,{value:"etkisi_artisi",children:"Platform etkileşimini artırmak"}),e.jsx(u,{value:"ozel",children:"Özel (aşağıya açıklayın)"})]})}),e.jsx(c.Item,{name:"goal_detail",label:"Hedef Detayı (İsteğe Bağlı)",children:e.jsx(re,{rows:3,placeholder:"Etkinlik hedefiyle ilgili ek detaylar..."})}),e.jsx(c.Item,{children:e.jsx(m,{type:"primary",htmlType:"submit",loading:l,icon:e.jsx(D,{}),style:{backgroundColor:"#722ed1"},className:"hover:bg-purple-700",size:"large",children:"AI Danışmanına Sor"})})]})},"event"),e.jsx(g,{tab:"Nasıl Çalışır?",children:e.jsxs("div",{className:"space-y-4 mt-4",children:[e.jsx(h,{level:4,children:"AI Etkinlik Planlama Nasıl Çalışır?"}),e.jsxs("div",{className:"p-4 bg-blue-50 dark:bg-blue-900 dark:bg-opacity-30 rounded-lg",children:[e.jsxs("div",{className:"flex items-start mb-4",children:[e.jsx("div",{className:"flex-shrink-0 h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-800 flex items-center justify-center mr-3",children:e.jsx("span",{className:"text-blue-600 dark:text-blue-300 font-bold",children:"1"})}),e.jsxs("div",{children:[e.jsx(n,{strong:!0,className:"text-blue-700 dark:text-blue-300",children:"Yayıncıları ve Etkinlik Türünü Seçin"}),e.jsx(n,{className:"block text-blue-600 dark:text-blue-400",children:"Etkinliğe katılacak yayıncılarınızı ve oluşturmak istediğiniz etkinlik türünü (Lider Tablosu veya PK Turnuvası) seçin."})]})]}),e.jsxs("div",{className:"flex items-start mb-4",children:[e.jsx("div",{className:"flex-shrink-0 h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-800 flex items-center justify-center mr-3",children:e.jsx("span",{className:"text-blue-600 dark:text-blue-300 font-bold",children:"2"})}),e.jsxs("div",{children:[e.jsx(n,{strong:!0,className:"text-blue-700 dark:text-blue-300",children:"AI Analizi"}),e.jsx(n,{className:"block text-blue-600 dark:text-blue-400",children:"AI, yayıncılarınızın performans verilerini (takipçi sayıları, elmas gelirleri, canlı yayın süreleri) analiz eder ve en uygun etkinlik planını oluşturur."})]})]}),e.jsxs("div",{className:"flex items-start mb-4",children:[e.jsx("div",{className:"flex-shrink-0 h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-800 flex items-center justify-center mr-3",children:e.jsx("span",{className:"text-blue-600 dark:text-blue-300 font-bold",children:"3"})}),e.jsxs("div",{children:[e.jsx(n,{strong:!0,className:"text-blue-700 dark:text-blue-300",children:"Öneriler"}),e.jsx(n,{className:"block text-blue-600 dark:text-blue-400",children:"AI size etkinlik adı, kurallar, zamanlama, PK eşleştirmeleri veya puanlama sistemi önerir. Ayrıca yayıncılara gönderebileceğiniz duyuru mesajları oluşturur."})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"flex-shrink-0 h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-800 flex items-center justify-center mr-3",children:e.jsx("span",{className:"text-blue-600 dark:text-blue-300 font-bold",children:"4"})}),e.jsxs("div",{children:[e.jsx(n,{strong:!0,className:"text-blue-700 dark:text-blue-300",children:"Etkinlik Oluşturma"}),e.jsx(n,{className:"block text-blue-600 dark:text-blue-400",children:"AI'nın önerdiği etkinliği doğrudan oluşturabilir ve düzenleyebilirsiniz. Duyuru mesajlarını da hızlıca kopyalayıp WhatsApp veya e-posta ile paylaşabilirsiniz."})]})]})]})]})},"how")]})]}),l&&e.jsxs("div",{className:"ai-loading bg-white dark:bg-gray-800 p-8 text-center shadow rounded-lg",children:[e.jsx(U,{size:"large"}),e.jsx("p",{className:"mt-4 text-lg",children:"AI etkinlik analizi yapıyor, lütfen bekleyin..."}),e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Bu işlem 15-30 saniye sürebilir"})]}),a&&!l&&e.jsx(f,{className:"ai-response-card",title:e.jsxs("div",{className:"flex items-center",children:[e.jsx(ae,{className:"text-green-500 mr-2"}),e.jsx("span",{children:"AI Etkinlik Önerisi"})]}),extra:e.jsx(m,{type:"primary",onClick:T,icon:e.jsx(M,{}),children:"Bu Öneriyi Kullan"}),children:e.jsx("div",{className:"ai-response-content",children:a.raw_response?e.jsx("pre",{style:{whiteSpace:"pre-wrap",wordBreak:"break-word"},children:a.raw_response}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6",children:[e.jsx(h,{level:3,className:"text-indigo-700 dark:text-indigo-400 mb-2",children:a.etkinlik_adi}),e.jsx(R,{className:"text-lg",children:a.aciklama}),e.jsx(Y,{}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mt-4",children:[e.jsxs("div",{children:[e.jsx(h,{level:5,children:"Etkinlik Detayları"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Başlangıç:"})," ",a.baslangic_tarihi]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Bitiş:"})," ",a.bitis_tarihi]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Tür:"}),e.jsx(_,{color:y.getFieldValue("event_type")==="leaderboard"?"blue":"orange",className:"ml-2",children:y.getFieldValue("event_type")==="leaderboard"?"Lider Tablosu":"PK Turnuvası"})]}),a.puanlama_sistemi&&e.jsxs("p",{children:[e.jsx("strong",{children:"Puanlama:"})," ",a.puanlama_sistemi]}),a.tur_sayisi&&e.jsxs("p",{children:[e.jsx("strong",{children:"Tur Sayısı:"})," ",a.tur_sayisi]})]})]}),e.jsxs("div",{children:[e.jsx(h,{level:5,children:"Önerilen Stratejiler"}),a.stratejiler&&Array.isArray(a.stratejiler)?e.jsx("ul",{className:"list-disc pl-5",children:a.stratejiler.map((i,s)=>e.jsx("li",{className:"mb-1",children:i},s))}):e.jsx("p",{children:"Strateji önerisi bulunamadı."})]})]}),e.jsx(Y,{}),e.jsxs("div",{className:"mt-4",children:[e.jsx(h,{level:5,children:"Etkinlik Kuralları"}),e.jsx(R,{className:"whitespace-pre-line",children:a.kurallar})]}),a.eslesme_bilgileri&&Array.isArray(a.eslesme_bilgileri)&&a.eslesme_bilgileri.length>0&&e.jsxs("div",{className:"mt-6",children:[e.jsx(h,{level:5,children:"Önerilen PK Eşleşmeleri"}),e.jsx("ul",{className:"list-disc pl-5",children:a.eslesme_bilgileri.map((i,s)=>e.jsxs("li",{className:"mb-2",children:[e.jsx(_,{color:"blue",children:i.yayinci1})," vs ",e.jsx(_,{color:"orange",children:i.yayinci2})]},s))})]})]}),e.jsx(f,{title:"Duyuru Mesajları",className:"mb-6",children:e.jsxs(w,{defaultActiveKey:"whatsapp",children:[e.jsx(g,{tab:e.jsxs("span",{children:[e.jsx(Z,{className:"text-green-600 mr-1"}),"WhatsApp Mesajı"]}),children:e.jsxs("div",{className:"relative bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700",children:[e.jsx("pre",{className:"whitespace-pre-wrap",children:a.whatsapp_mesaji}),e.jsx(K,{title:"Kopyala",children:e.jsx(m,{type:"primary",shape:"circle",icon:e.jsx($,{}),size:"small",className:"absolute top-2 right-2 bg-green-600 hover:bg-green-700",onClick:()=>I(a.whatsapp_mesaji)})})]})},"whatsapp"),e.jsx(g,{tab:e.jsxs("span",{children:[e.jsx(ee,{className:"text-blue-600 mr-1"}),"E-posta Mesajı"]}),children:e.jsxs("div",{className:"relative bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700",children:[e.jsx("pre",{className:"whitespace-pre-wrap",children:a.email_mesaji}),e.jsx(K,{title:"Kopyala",children:e.jsx(m,{type:"primary",shape:"circle",icon:e.jsx($,{}),size:"small",className:"absolute top-2 right-2 bg-blue-600 hover:bg-blue-700",onClick:()=>I(a.email_mesaji)})})]})},"email")]})}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(m,{onClick:()=>v(null),icon:e.jsx(D,{}),children:"Yeni Öneri Oluştur"}),e.jsx(m,{type:"primary",onClick:T,icon:e.jsx(M,{}),size:"large",children:"Bu Etkinliği Oluştur"})]})]})})})]})};export{je as default};
