import requests
from bs4 import BeautifulSoup
import re
import json
import os
import pickle
from datetime import datetime, timedelta
from flask import Flask, render_template, request, jsonify
import time
import pandas as pd
import plotly
import plotly.express as px
import plotly.graph_objects as go
import numpy as np
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from collections import Counter
import logging

app = Flask(__name__)

# Loglama ayarları
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Chrome profil ve çerez dizinleri
CHROME_PROFILE_DIR = os.path.join(os.path.expanduser("~"), "tiktok_analyzer_profile")
os.makedirs(CHROME_PROFILE_DIR, exist_ok=True)

def format_number(text):
    """
    TikTok formatındaki sayıları (1.2K, 4.5M gibi) sayısal değerlere dönüştürür
    """
    if not text or not isinstance(text, str):
        return 0
    
    text = text.strip()
    if not text or text == '0':
        return 0
    
    multiplier = 1
    if 'K' in text or 'k' in text:
        multiplier = 1000
        text = text.replace('K', '').replace('k', '')
    elif 'M' in text or 'm' in text:
        multiplier = 1000000
        text = text.replace('M', '').replace('m', '')
    
    try:
        return int(float(text) * multiplier)
    except:
        return 0

def format_date_turkish(date_string):
    """
    Tarihi Türkçe formata dönüştürür (gün/ay/yıl)
    """
    months_en = {
        'January': '01', 'February': '02', 'March': '03', 'April': '04',
        'May': '05', 'June': '06', 'July': '07', 'August': '08',
        'September': '09', 'October': '10', 'November': '11', 'December': '12'
    }
    
    months_en_short = {
        'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04',
        'May': '05', 'Jun': '06', 'Jul': '07', 'Aug': '08',
        'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
    }
    
    # Zaten gün/ay/yıl formatındaysa döndür
    if re.match(r'\d{1,2}/\d{1,2}/\d{4}', date_string):
        return date_string
    
    # 2023-12-25 formatını dönüştür
    if re.match(r'\d{4}-\d{1,2}-\d{1,2}', date_string):
        y, m, d = date_string.split('-')
        return f"{d}/{m}/{y}"
    
    # Dec 25, 2023 formatını dönüştür
    month_match = re.search(r'(\w+)\s+(\d{1,2}),\s*(\d{4})', date_string)
    if month_match:
        month, day, year = month_match.groups()
        if month in months_en:
            month_num = months_en[month]
        elif month in months_en_short:
            month_num = months_en_short[month]
        else:
            month_num = month
        return f"{day}/{month_num}/{year}"
    
    # 25/12/2023 (İngilizce format) -> 25/12/2023 (Türkçe format)
    if re.match(r'\d{1,2}/\d{1,2}/\d{4}', date_string):
        return date_string
    
    # Relatif tarihleri dönüştür (3 days ago -> bugünden 3 gün önceki tarih)
    if 'ago' in date_string.lower() or 'önce' in date_string.lower():
        now = datetime.now()
        
        if 'minute' in date_string or 'dakika' in date_string:
            match = re.search(r'(\d+)', date_string)
            if match:
                minutes = int(match.group(1))
                target_date = now - timedelta(minutes=minutes)
                return target_date.strftime('%d/%m/%Y')
        
        elif 'hour' in date_string or 'saat' in date_string:
            match = re.search(r'(\d+)', date_string)
            if match:
                hours = int(match.group(1))
                target_date = now - timedelta(hours=hours)
                return target_date.strftime('%d/%m/%Y')
        
        elif 'day' in date_string or 'gün' in date_string:
            match = re.search(r'(\d+)', date_string)
            if match:
                days = int(match.group(1))
                target_date = now - timedelta(days=days)
                return target_date.strftime('%d/%m/%Y')
        
        elif 'week' in date_string or 'hafta' in date_string:
            match = re.search(r'(\d+)', date_string)
            if match:
                weeks = int(match.group(1))
                target_date = now - timedelta(weeks=weeks)
                return target_date.strftime('%d/%m/%Y')
        
        elif 'month' in date_string or 'ay' in date_string:
            match = re.search(r'(\d+)', date_string)
            if match:
                months = int(match.group(1))
                target_date = now - timedelta(days=months*30)
                return target_date.strftime('%d/%m/%Y')
        
        elif 'year' in date_string or 'yıl' in date_string:
            match = re.search(r'(\d+)', date_string)
            if match:
                years = int(match.group(1))
                target_date = now - timedelta(days=years*365)
                return target_date.strftime('%d/%m/%Y')
    
    return date_string  # Dönüştürülemezse orijinal halini döndür

def get_profile_data(username):
    """
    TikTok profil verilerini çeker
    """
    try:
        logging.info(f"{username} için veri çekme işlemi başlatılıyor...")
        
        # Chrome sürücüsünü başlat
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        chrome_options.add_argument("--mute-audio")  # Sesi tamamen kapat
        chrome_options.add_argument("--autoplay-policy=no-user-gesture-required")  # Otomatik oynatmaları devre dışı bırak
        chrome_options.add_experimental_option("prefs", {
            "profile.managed_default_content_settings.media_stream": 2,  # Mikrofon ve kamera erişimini engelle
            "profile.default_content_setting_values.notifications": 2,  # Bildirimleri engelle
            "profile.managed_default_content_settings.sound": 2,  # Ses çalmasını engelle
        })
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option("useAutomationExtension", False)
        
        service = Service()
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # JavaScript ile navigator.webdriver özelliğini değiştirmeyi düzeltelim
        # Hata: Cannot redefine property: webdriver
        # Bu hatanın çözümü için farklı bir yöntem kullanalım
        driver.execute_script("""
            // Tarayıcının otomasyon tespitini önlemek için
            Object.defineProperties(navigator, {
              webdriver: {
                get: () => false,
                configurable: true
              }
            });
            
            // Chrome otomasyon özelliklerini gizle
            window.navigator.chrome = {
              runtime: {},
              loadTimes: function() {},
              csi: function() {},
              app: {
                isInstalled: false
              }
            };
            
            // Dil listesini gerçek bir kullanıcı gibi ayarla
            window.navigator.languages = ['tr-TR', 'tr', 'en-US', 'en'];
            
            // Permissions API'yi değiştir
            if (navigator.permissions) {
              navigator.permissions.query = (function(original) {
                return function(parameters) {
                  if (parameters.name === 'notifications') {
                    return Promise.resolve({ state: Notification.permission });
                  }
                  return original.apply(this, arguments);
                };
              })(navigator.permissions.query);
            }
            
            // WebGL parmak izini değiştir
            const getParameter = WebGLRenderingContext.prototype.getParameter;
            WebGLRenderingContext.prototype.getParameter = function(parameter) {
              if (parameter === 37445) {
                return 'Intel Inc.';
              }
              if (parameter === 37446) {
                return 'Intel Iris OpenGL Engine';
              }
              return getParameter.apply(this, arguments);
            };
            
            // Plugin ve mimeType özelliklerini ekle
            Object.defineProperty(navigator, 'plugins', {
              get: () => {
                return [1, 2, 3, 4, 5];
              }
            });
            
            Object.defineProperty(navigator, 'mimeTypes', {
              get: () => {
                return [1, 2, 3, 4, 5];
              }
            });
        """)
        
        # Profil sayfasını ziyaret et
        profile_url = f"https://www.tiktok.com/@{username}"
        logger.info(f"Profil sayfasına gidiliyor: {profile_url}")
        driver.get(profile_url)
        
        # Sayfanın yüklenmesini bekle
        try:
            WebDriverWait(driver, 15).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "[data-e2e='user-post-item'], .tiktok-x6y88p-DivItemContainerV2"))
            )
            logger.info("Profil sayfası yüklendi")
        except:
            # Doğrulama ekranı olabilir
            verification_texts = ["verify", "captcha", "puzzle", "slide", "drag", "human"]
            page_source = driver.page_source.lower()
            
            if any(text in page_source for text in verification_texts):
                logger.warning("Doğrulama ekranı tespit edildi")
                # Kullanıcıya 30 saniye süre ver
                logger.info("Kullanıcının doğrulamayı tamamlaması bekleniyor...")
                time.sleep(30)
                
                try:
                    WebDriverWait(driver, 15).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "[data-e2e='user-post-item'], .tiktok-x6y88p-DivItemContainerV2"))
                    )
                    logger.info("Doğrulama sonrası profil sayfası yüklendi")
                except:
                    logger.error("Doğrulama sonrası profil sayfası yüklenemedi")
                    return {"error": "Profil sayfası yüklenemedi. Doğrulama yapılamamış olabilir."}
            else:
                logger.error("Profil sayfası yüklenemedi")
                return {"error": "Profil sayfası yüklenemedi. Kullanıcı adı yanlış veya sayfa erişilemez."}
                
        # Biraz aşağı kaydır, tüm videoların yüklenmesi için
        driver.execute_script("window.scrollBy(0, 700)")
        time.sleep(2)
        
        # Profil bilgilerini çek
        profile_data = {}
        
        # Kullanıcı adı
        try:
            username_element = driver.find_element(By.CSS_SELECTOR, 'h1[data-e2e="user-title"], .tiktok-qpyus6-H1ShareTitle')
            profile_data["username"] = username_element.text.strip()
        except Exception as e:
            logger.warning(f"Kullanıcı adı çekilemedi: {e}")
            profile_data["username"] = username
        
        # Profil ismi/nick
        try:
            nickname_element = driver.find_element(By.CSS_SELECTOR, 'h2[data-e2e="user-subtitle"], .tiktok-qpyus6-H2ShareSubTitle')
            profile_data["nickname"] = nickname_element.text.strip()
        except Exception as e:
            logger.warning(f"Profil adı çekilemedi: {e}")
            profile_data["nickname"] = ""
        
        # Bio
        try:
            bio_element = driver.find_element(By.CSS_SELECTOR, 'h2[data-e2e="user-bio"], .tiktok-1n8z7yd-H2ShareDesc, .tiktok-1n9lhmd-SpanText')
            profile_data["bio"] = bio_element.text.strip()
        except Exception as e:
            logger.warning(f"Bio çekilemedi: {e}")
            profile_data["bio"] = ""
        
        # Takipçi, takip edilen ve beğeni sayısı
        try:
            followers_element = driver.find_element(By.CSS_SELECTOR, '[data-e2e="followers-count"]')
            profile_data["followers"] = followers_element.text.strip()
            profile_data["followers_count"] = format_number(profile_data["followers"])
        except Exception as e:
            logger.warning(f"Takipçi sayısı çekilemedi: {e}")
            profile_data["followers"] = "0"
            profile_data["followers_count"] = 0
            
        try:
            following_element = driver.find_element(By.CSS_SELECTOR, '[data-e2e="following-count"]')
            profile_data["following"] = following_element.text.strip()
            profile_data["following_count"] = format_number(profile_data["following"])
        except Exception as e:
            logger.warning(f"Takip edilen sayısı çekilemedi: {e}")
            profile_data["following"] = "0"
            profile_data["following_count"] = 0
            
        try:
            likes_element = driver.find_element(By.CSS_SELECTOR, '[data-e2e="likes-count"]')
            profile_data["likes"] = likes_element.text.strip()
            profile_data["likes_count"] = format_number(profile_data["likes"])
        except Exception as e:
            logger.warning(f"Beğeni sayısı çekilemedi: {e}")
            profile_data["likes"] = "0"
            profile_data["likes_count"] = 0
            
        # Profil fotoğrafı
        try:
            avatar_element = driver.find_element(By.CSS_SELECTOR, '.tiktok-uha12h-DivContainer img, [data-e2e="user-avatar"] img')
            profile_data["avatar_url"] = avatar_element.get_attribute("src")
        except Exception as e:
            logger.warning(f"Profil fotoğrafı çekilemedi: {e}")
            profile_data["avatar_url"] = ""
            
        # Video kartlarını topla
        video_cards = driver.find_elements(By.CSS_SELECTOR, "[data-e2e='user-post-item'], .tiktok-x6y88p-DivItemContainerV2")
        
        if not video_cards:
            logger.warning("Video kartları bulunamadı")
            return {"error": "Video kartları bulunamadı. Profil gizli veya videosuz olabilir."}
        
        # Sadece ilk 10 videoyu analiz et (performans için)
        max_videos = min(10, len(video_cards))
        
        logger.info(f"Toplam {len(video_cards)} video bulundu, {max_videos} video analiz edilecek")
        
        # İlk olarak ana sayfadaki bilgileri topla
        video_list = []
        
        for idx, card in enumerate(video_cards[:max_videos]):
            try:
                # Video URL ve ID
                link_element = card.find_element(By.TAG_NAME, "a")
                video_url = link_element.get_attribute("href")
                video_id = re.search(r'/video/(\d+)', video_url).group(1) if re.search(r'/video/(\d+)', video_url) else ""
                
                # Video görüntülenme sayısı
                views_element = card.find_element(By.TAG_NAME, "strong")
                views_text = views_element.text.strip()
                views_count = format_number(views_text)
                
                # Video thumbnail'i
                thumbnail_url = None
                try:
                    # TikTok'un video thumbnail yapısı
                    thumbnail_selectors = [
                        "img.tiktok-1itcwxg-ImgPoster",
                        "img[mode='0']",
                        "img[mode='1']",
                        "img[mode='2']",
                        "img[class*='ImgPoster']",
                        "img[src*='image']",
                        ".tiktok-1jxhpnd-ImgPoster",
                        "[data-e2e='user-post-item'] img",
                        ".tiktok-x6y88p-DivItemContainerV2 img"
                    ]
                    
                    for selector in thumbnail_selectors:
                        try:
                            thumbnail_element = card.find_element(By.CSS_SELECTOR, selector)
                            thumbnail_url = thumbnail_element.get_attribute("src")
                            if thumbnail_url and "http" in thumbnail_url:
                                break
                        except:
                            continue
                except Exception as e:
                    logger.warning(f"Video {idx+1} thumbnail çekilemedi: {e}")
                
                video_list.append({
                    "id": video_id,
                    "url": video_url,
                    "views": views_text,
                    "views_count": views_count,
                    "thumbnail": thumbnail_url
                })
            except Exception as e:
                logger.warning(f"Video {idx+1} bilgileri çekilirken hata: {e}")
                continue
        
        # Her video için ayrı ayrı etkileşim verilerini çekme
        logger.info("Her video için etkileşim verileri toplanıyor...")
        
        videos = []
        total_views = 0
        total_likes = 0
        total_comments = 0
        total_shares = 0
        total_saves = 0  # Kaydetme sayısı için
        
        for idx, video_data in enumerate(video_list):
            try:
                # Video sayfasına git
                logger.info(f"Video sayfasına gidiliyor: {video_data['url']}")
                driver.get(video_data["url"])
                
                # Videoyu yüklenmesi için bekle
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, ".tiktok-1hskgat-DivPlayerContainer, video"))
                )
                
                # Sayfa yüklendikten sonra videoları sustur
                driver.execute_script("""
                    // Tüm video elementlerini bul ve sesini kapat
                    const videos = document.querySelectorAll('video');
                    for (let video of videos) {
                        video.muted = true;
                        video.volume = 0;
                        
                        // Video elementine event listener ekleyerek sesin otomatik açılmasını engelle
                        video.addEventListener('volumechange', function(e) {
                            e.preventDefault();
                            video.muted = true;
                            video.volume = 0;
                        }, true);
                    }
                    
                    // Ses kontrollerini devre dışı bırak
                    const audioContexts = window.AudioContext || window.webkitAudioContext;
                    if (audioContexts) {
                        audioContexts.prototype.createMediaElementSource = function() {
                            return {
                                connect: function() {},
                                disconnect: function() {}
                            };
                        };
                    }
                """)
                
                # Sayfa biraz yüklensin diye bekle
                time.sleep(3)
                
                # Video detayları
                video_details = {
                    "id": video_data["id"],
                    "url": video_data["url"],
                    "views": video_data["views"],
                    "views_count": video_data["views_count"],
                    "thumbnail": video_data.get("thumbnail")
                }
                
                # TARİH BİLGİSİNİ ÇEK - DAHA GELİŞMİŞ SELEKTÖRLER
                try:
                    date_selectors = [
                        # Öncelikle meta verisini dene
                        "meta[property='og:video:release_date']",
                        "meta[name='twitter:data2']",
                        
                        # Video sayfası selektörleri
                        "[data-e2e='browser-nickname'] + span",
                        "[data-e2e='browser-nickname'] ~ span:last-of-type",
                        ".tiktok-1w7cv3q-SpanOtherInfos span:last-child",
                        ".tiktok-1iysm4m-SpanOtherInfos span:last-child",
                        ".tiktok-6f3gwq-SpanOtherInfos span:last-child",
                        
                        # Genel tarih selektörleri
                        "time",
                        "[datetime]",
                        "[data-e2e='video-timestamp']",
                        ".video-timestamp",
                        ".video-date",
                        
                        # Relatif tarih selektörleri
                        "span:contains('ago')",
                        "span:contains('önce')",
                        "span:contains('·'):last-child",
                        
                        # Spesifik sınıflar
                        ".tiktok-j6dmhd-StyledDivContainer span",
                        ".tiktok-1s2k5gx-DivInfoContainer span",
                        ".tiktok-1vp4tdd-DivActionContainer + span",
                        
                        # Alt bölümden tarih arama
                        "[data-e2e='browse-video-info'] span:last-child",
                        "[data-e2e='browser-nickname'] + * + span",
                        ".tiktok-1xk2ze8-DivText",
                        
                        # JSON-LD schema bilgisi
                        "script[type='application/ld+json']"
                    ]
                    
                    published_date = None
                    
                    # Önce meta verilerden dene
                    for selector in date_selectors[:2]:
                        try:
                            meta_element = driver.find_element(By.CSS_SELECTOR, selector)
                            date_content = meta_element.get_attribute("content")
                            if date_content:
                                published_date = date_content
                                break
                        except:
                            continue
                    
                    # Meta verisi yoksa diğer selektörleri dene
                    if not published_date:
                        for selector in date_selectors[2:]:
                            try:
                                if selector == "script[type='application/ld+json']":
                                    script_elements = driver.find_elements(By.CSS_SELECTOR, selector)
                                    for script in script_elements:
                                        try:
                                            json_data = json.loads(script.get_attribute("innerHTML"))
                                            if "uploadDate" in json_data:
                                                published_date = json_data["uploadDate"]
                                                break
                                        except:
                                            continue
                                else:
                                    date_elements = driver.find_elements(By.CSS_SELECTOR, selector)
                                    for element in date_elements:
                                        date_text = element.text.strip()
                                        
                                        # Tarih formatlarını kontrol et
                                        if date_text and (
                                            re.search(r'\d{4}-\d{1,2}-\d{1,2}', date_text) or  # 2023-12-25
                                            re.search(r'\d{1,2}/\d{1,2}/\d{4}', date_text) or  # 12/25/2023
                                            re.search(r'\d{1,2}\.\d{1,2}\.\d{4}', date_text) or  # 25.12.2023
                                            re.search(r'\d+[smhdgay]\s?ago', date_text.lower()) or  # 3h ago, 2d ago
                                            re.search(r'\d+\s?(saniye|dakika|saat|gün|hafta|ay|yıl|second|minute|hour|day|week|month|year)', date_text.lower()) or
                                            ('ago' in date_text.lower()) or
                                            ('since' in date_text.lower()) or
                                            ('önce' in date_text.lower()) or
                                            ('yıl' in date_text.lower()) or
                                            ('ay' in date_text.lower()) or
                                            ('gün' in date_text.lower()) or
                                            ('saat' in date_text.lower()) or
                                            ('dakika' in date_text.lower())
                                        ):
                                            published_date = date_text
                                            break
                            except:
                                continue
                            
                            if published_date:
                                break
                    
                    # JavaScript'ten tarih bilgisi al
                    if not published_date:
                        try:
                            published_date = driver.execute_script("""
                                // Video detay verisini bul
                                try {
                                    const app = document.querySelector('#SIGI_STATE');
                                    if (app && app.textContent) {
                                        const data = JSON.parse(app.textContent);
                                        const videoId = window.location.pathname.split('/').pop();
                                        const video = data.ItemModule[videoId];
                                        if (video && video.createTime) {
                                            return new Date(video.createTime * 1000).toLocaleDateString();
                                        }
                                    }
                                } catch (e) {}
                                return null;
                            """)
                        except:
                            pass
                    
                    if not published_date:
                        published_date = datetime.now().strftime('%d/%m/%Y')
                    else:
                        # Tarihi Türkçe formata dönüştür
                        published_date = format_date_turkish(published_date)
                    
                    video_details["published_date"] = published_date
                    logger.info(f"Video tarihi: {published_date}")
                
                except Exception as e:
                    logger.warning(f"Tarih bilgisi çekilemedi: {e}")
                    video_details["published_date"] = datetime.now().strftime('%d/%m/%Y')
                
                # Görüntülenme sayısını topla
                total_views += video_data["views_count"]
                
                # BEĞENİ SAYISI - Güncellenmiş selektörler
                try:
                    like_selectors = [
                        "[data-e2e='like-count']",
                        "[data-e2e='browse-like-count']",
                        ".tiktok-1n8iyy3-StrongText",
                        ".tiktok-1xikrbg-StrongText",
                        "[aria-label*='like'] + strong"
                    ]
                    
                    found_likes = False
                    for selector in like_selectors:
                        try:
                            like_element = driver.find_element(By.CSS_SELECTOR, selector)
                            if like_element.text.strip():
                                likes_text = like_element.text.strip()
                                likes_count = format_number(likes_text)
                                video_details["likes"] = likes_text
                                video_details["likes_count"] = likes_count
                                total_likes += likes_count
                                found_likes = True
                                logger.info(f"Beğeni bulundu: {likes_text}")
                                break
                        except:
                            continue
                    
                    if not found_likes:
                        video_details["likes"] = "0"
                        video_details["likes_count"] = 0
                        logger.warning("Beğeni sayısı bulunamadı")
                
                except Exception as e:
                    logger.warning(f"Beğeni sayısı çekilemedi: {e}")
                    video_details["likes"] = "0"
                    video_details["likes_count"] = 0
                
                # YORUM SAYISI - Güncellenmiş selektörler
                try:
                    comment_selectors = [
                        "[data-e2e='comment-count']",
                        "[data-e2e='browse-comment-count']",
                        ".tiktok-1n8iyy3-StrongText",
                        ".tiktok-1xikrbg-StrongText",
                        "[aria-label*='comment'] + strong"
                    ]
                    
                    found_comments = False
                    for selector in comment_selectors:
                        try:
                            comment_element = driver.find_element(By.CSS_SELECTOR, selector)
                            if comment_element.text.strip():
                                comments_text = comment_element.text.strip()
                                comments_count = format_number(comments_text)
                                video_details["comments"] = comments_text
                                video_details["comments_count"] = comments_count
                                total_comments += comments_count
                                found_comments = True
                                logger.info(f"Yorum bulundu: {comments_text}")
                                break
                        except:
                            continue
                    
                    if not found_comments:
                        video_details["comments"] = "0"
                        video_details["comments_count"] = 0
                        logger.warning("Yorum sayısı bulunamadı")
                
                except Exception as e:
                    logger.warning(f"Yorum sayısı çekilemedi: {e}")
                    video_details["comments"] = "0"
                    video_details["comments_count"] = 0
                
                # KAYDETME SAYISI - Yeni eklendi
                try:
                    save_selectors = [
                        "[data-e2e='undefined-count']",  # TikTok bazen kaydetme için 'undefined' kullanıyor
                        "[data-e2e='save-count']",
                        "[data-e2e='browser-save-count']",
                        ".tiktok-1n8iyy3-StrongText",
                        ".tiktok-1xikrbg-StrongText",
                        "[aria-label*='bookmark'] + strong",
                        "[aria-label*='save'] + strong",
                        "[aria-label*='Favorilere ekle'] + strong",
                        "[aria-label*='Add to Favorites'] + strong"
                    ]
                    
                    found_saves = False
                    for selector in save_selectors:
                        try:
                            save_elements = driver.find_elements(By.CSS_SELECTOR, selector)
                            for save_element in save_elements:
                                if save_element.text.strip():
                                    saves_text = save_element.text.strip()
                                    saves_count = format_number(saves_text)
                                    video_details["saves"] = saves_text
                                    video_details["saves_count"] = saves_count
                                    total_saves += saves_count
                                    found_saves = True
                                    logger.info(f"Kaydetme bulundu: {saves_text}")
                                    break
                            if found_saves:
                                break
                        except:
                            continue
                    
                    if not found_saves:
                        video_details["saves"] = "0"
                        video_details["saves_count"] = 0
                        logger.warning("Kaydetme sayısı bulunamadı")
                
                except Exception as e:
                    logger.warning(f"Kaydetme sayısı çekilemedi: {e}")
                    video_details["saves"] = "0"
                    video_details["saves_count"] = 0
                
                # PAYLAŞIM SAYISI - Güncellenmiş selektörler
                try:
                    share_selectors = [
                        "[data-e2e='share-count']",
                        "[data-e2e='browse-share-count']",
                        ".tiktok-1n8iyy3-StrongText",
                        ".tiktok-1xikrbg-StrongText",
                        "[aria-label*='share'] + strong"
                    ]
                    
                    found_shares = False
                    for selector in share_selectors:
                        try:
                            share_element = driver.find_element(By.CSS_SELECTOR, selector)
                            if share_element.text.strip():
                                shares_text = share_element.text.strip()
                                shares_count = format_number(shares_text)
                                video_details["shares"] = shares_text
                                video_details["shares_count"] = shares_count
                                total_shares += shares_count
                                found_shares = True
                                logger.info(f"Paylaşım bulundu: {shares_text}")
                                break
                        except:
                            continue
                    
                    if not found_shares:
                        video_details["shares"] = "0"
                        video_details["shares_count"] = 0
                        logger.warning("Paylaşım sayısı bulunamadı")
                
                except Exception as e:
                    logger.warning(f"Paylaşım sayısı çekilemedi: {e}")
                    video_details["shares"] = "0"
                    video_details["shares_count"] = 0
                
                # Video açıklaması ve hashtag'ler - Güncellenmiş selektörler
                try:
                    desc_elements = driver.find_elements(By.CSS_SELECTOR, ".tiktok-j2a19r-SpanText, [data-e2e='video-desc'], .tiktok-1ejylhp-DivContainer")
                    found_desc = False
                    
                    for element in desc_elements:
                        desc_text = element.text.strip()
                        if desc_text:
                            video_details["description"] = desc_text
                            
                            # Hashtag'leri bul
                            hashtags = re.findall(r'#(\w+)', desc_text)
                            if hashtags:
                                video_details["hashtags"] = hashtags
                            
                            found_desc = True
                            logger.info(f"Açıklama bulundu: {desc_text[:50]}...")
                            break
                    
                    if not found_desc:
                        video_details["description"] = ""
                        logger.warning("Açıklama bulunamadı")
                        
                except Exception as e:
                    logger.warning(f"Açıklama çekilemedi: {e}")
                    video_details["description"] = ""
                
                # Her video için doğru etkileşim oranını hesapla
                if video_details["views_count"] > 0:
                    video_engagement = 0
                    if "likes_count" in video_details:
                        video_engagement += video_details["likes_count"]
                    if "comments_count" in video_details:
                        video_engagement += video_details["comments_count"]
                    if "shares_count" in video_details:
                        video_engagement += video_details["shares_count"]
                    if "saves_count" in video_details:
                        video_engagement += video_details["saves_count"]
                    
                    engagement_rate = (video_engagement / video_details["views_count"]) * 100
                    video_details["engagement_rate"] = round(engagement_rate, 1)
                    video_details["engagement"] = f"%{round(engagement_rate, 1)} Etkileşim"
                    logger.info(f"Video etkileşim oranı: %{engagement_rate:.1f}")
                
                videos.append(video_details)
                logger.info(f"Video {idx+1} bilgileri başarıyla çekildi")
                
                # Ana sayfaya fazla sık gitme, TikTok şüphelenebilir
                if idx < len(video_list) - 1:
                    time.sleep(2)
                
            except Exception as e:
                logger.error(f"Video sayfası işlenirken hata: {e}")
                # Videoyu işleyemediysek, temel bilgilerle ekle
                if "id" in video_data and "url" in video_data:
                    videos.append(video_data)
        
        profile_data["videos"] = videos
        
        # Toplam profil özet verileri
        if videos:
            # Toplam değerler
            profile_data["total_views"] = total_views
            profile_data["total_likes"] = total_likes
            profile_data["total_comments"] = total_comments
            profile_data["total_shares"] = total_shares
            profile_data["total_saves"] = total_saves
            
            # Toplam etkileşim (artık kaydetmeler de dahil)
            total_engagement = total_likes + total_comments + total_shares + total_saves
            profile_data["total_engagement"] = total_engagement
            
            # Toplam etkileşim oranı
            if total_views > 0:
                profile_data["total_engagement_rate"] = round((total_engagement / total_views) * 100, 1)
            else:
                profile_data["total_engagement_rate"] = 0
            
            # Ortalama değerler
            video_count = len(videos)
            profile_data["average_views"] = round(total_views / video_count) if video_count > 0 else 0
            profile_data["average_likes"] = round(total_likes / video_count) if video_count > 0 else 0
            profile_data["average_comments"] = round(total_comments / video_count) if video_count > 0 else 0
            profile_data["average_shares"] = round(total_shares / video_count) if video_count > 0 else 0
            profile_data["average_saves"] = round(total_saves / video_count) if video_count > 0 else 0
            
            # En çok kullanılan hashtag'ler
            all_hashtags = []
            for video in videos:
                if "hashtags" in video:
                    all_hashtags.extend(video["hashtags"])
            
            if all_hashtags:
                hashtag_counts = Counter(all_hashtags)
                most_used_hashtags = hashtag_counts.most_common(10)
                profile_data["most_used_hashtags"] = most_used_hashtags
        
        logger.info("Profil verilerinin çekilmesi tamamlandı")
        return profile_data
    
    except Exception as e:
        logger.error(f"Veri çekme işlemi sırasında hata: {str(e)}")
        return {"error": f"Veri çekilirken hata oluştu: {str(e)}"}
    
    finally:
        try:
            driver.quit()
            logger.info("Tarayıcı kapatıldı")
        except:
            pass

def prepare_visualization_data(profile_data):
    """
    Görselleştirme için veri hazırlar
    """
    viz_data = {}
    
    if "videos" in profile_data and profile_data["videos"]:
        videos = profile_data["videos"]
        
        # Video ID'lerini ve tarihlerini birleştir
        viz_data["dates"] = []
        for video in videos:
            vid_id = video.get("id", "")
            date = video.get("published_date", "")
            if vid_id:
                viz_data["dates"].append(f"{date} - ID: {vid_id[-6:]}")
            else:
                viz_data["dates"].append(date)
        
        viz_data["views"] = [v.get("views_count", 0) for v in videos]
        viz_data["likes"] = [v.get("likes_count", 0) for v in videos]
        viz_data["comments"] = [v.get("comments_count", 0) for v in videos]
        viz_data["shares"] = [v.get("shares_count", 0) for v in videos]
        viz_data["saves"] = [v.get("saves_count", 0) for v in videos]
        viz_data["engagement_rates"] = [v.get("engagement_rate", 0) for v in videos]
        viz_data["video_ids"] = [v.get("id", f"video_{idx}") for idx, v in enumerate(videos)]
        viz_data["descriptions"] = [v.get("description", f"Video {idx+1}") for idx, v in enumerate(videos)]
        viz_data["thumbnails"] = [v.get("thumbnail") for v in videos]
        
        # En popüler 5 video (görüntülenmeye göre)
        videos_with_views = [v for v in videos if "views_count" in v]
        if videos_with_views:
            popular_videos_views = sorted(videos_with_views, key=lambda x: x.get("views_count", 0), reverse=True)[:5]
            viz_data["popular_videos_views"] = popular_videos_views
        else:
            viz_data["popular_videos_views"] = []
            
        # En popüler 5 video (etkileşime göre)
        videos_with_engagement = [v for v in videos if "engagement_rate" in v]
        if videos_with_engagement:
            popular_videos_engagement = sorted(videos_with_engagement, key=lambda x: x.get("engagement_rate", 0), reverse=True)[:5]
            viz_data["popular_videos_engagement"] = popular_videos_engagement
        else:
            viz_data["popular_videos_engagement"] = []
            
        # Hashtag verileri
        if "most_used_hashtags" in profile_data:
            viz_data["hashtags"] = profile_data["most_used_hashtags"]
    else:
        # Veri yoksa boş değerler döndür
        viz_data = {
            "dates": [],
            "views": [],
            "likes": [],
            "comments": [],
            "shares": [],
            "saves": [],
            "engagement_rates": [],
            "video_ids": [],
            "descriptions": [],
            "thumbnails": [],
            "popular_videos_views": [],
            "popular_videos_engagement": []
        }
        logger.warning("Görselleştirme için yeterli veri bulunamadı")
    
    return viz_data

def create_graphs(viz_data, profile_data):
    """
    Plotly ile grafikleri oluşturur
    """
    graphs = {}
    
    # Video görüntülenme grafiği
    if "dates" in viz_data and "views" in viz_data and viz_data["dates"] and viz_data["views"]:
        # Zaman serisi görüntülenme grafiği
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=viz_data["dates"], 
            y=viz_data["views"],
            mode='lines+markers',
            name='Görüntülenme',
            line=dict(color='#FE2C55', width=3),
            marker=dict(size=10, color='#25F4EE'),
            text=viz_data["descriptions"],  # Açıklamaları hover'da göster
            hovertemplate="%{text}<br>Video: %{x}<br>Görüntülenme: %{y}<extra></extra>"
        ))
        
        fig.update_layout(
            title='Video Görüntülenmeleri',
            xaxis_title='Video ID ve Tarihi',
            yaxis_title='Görüntülenme Sayısı',
            template='plotly_dark',
            hovermode='closest',
            paper_bgcolor='rgba(40, 40, 40, 1)',
            plot_bgcolor='rgba(30, 30, 30, 1)',
            font=dict(color='white'),
            xaxis=dict(
                tickangle=45,
                tickfont=dict(size=10)
            ),
            margin=dict(b=150, l=50, r=50, t=50)  # Alt margin'i daha fazla artır, diğer kenarlar için de değer belirle
        )
        
        graphs["views_timeline"] = json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)
        
        # En yüksek etkileşimli videolar grafiği
        if "popular_videos_engagement" in viz_data and viz_data["popular_videos_engagement"]:
            eng_videos = viz_data["popular_videos_engagement"]
            
            # Video açıklamalarını ve tarihlerini hazırla
            descriptions = []
            for video in eng_videos:
                desc = video.get("description", "")
                date = video.get("published_date", "")
                vid_id = video.get("id", "")
                
                # Açıklamayı kısalt
                if len(desc) > 30:
                    desc = desc[:27] + "..."
                
                # ID, tarih ve açıklamayı birleştir
                if vid_id:
                    label = f"{date} - ID: {vid_id[-6:]}\n{desc}"
                else:
                    label = f"{date}\n{desc}"
                
                descriptions.append(label)
            
            fig5 = go.Figure()
            fig5.add_trace(go.Bar(
                x=[p.get("engagement_rate", 0) for p in eng_videos],
                y=descriptions,
                orientation='h',
                marker=dict(color='#1890FF'),
                text=[f"Görüntülenme: {p.get('views', '0')}<br>Beğeni: {p.get('likes', '0')}<br>Yorum: {p.get('comments', '0')}<br>Paylaşım: {p.get('shares', '0')}" for p in eng_videos],
                hovertemplate="%{y}<br>Etkileşim Oranı: %{x}%<br>%{text}<extra></extra>"
            ))
            
            fig5.update_layout(
                title='En Yüksek Etkileşimli Videolar (%)',
                xaxis_title='Etkileşim Oranı (%)',
                yaxis_title='',
                template='plotly_dark',
                paper_bgcolor='rgba(40, 40, 40, 1)',
                plot_bgcolor='rgba(30, 30, 30, 1)',
                font=dict(color='white'),
                height=600,
                margin=dict(l=300, r=50, t=50, b=50),
                yaxis=dict(
                    tickfont=dict(size=12),
                    automargin=True,
                    tickmode='array',
                    tickvals=list(range(len(descriptions))),
                    ticktext=descriptions,
                    autorange="reversed"
                ),
                bargap=0.3,
                bargroupgap=0.1
            )
            
            graphs["popular_videos_engagement"] = json.dumps(fig5, cls=plotly.utils.PlotlyJSONEncoder)
    
    return graphs

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/analyze', methods=['POST'])
def analyze():
    username = request.form.get('username')
    if not username:
        return jsonify({"error": "Kullanıcı adı gerekli"})
    
    # Kullanıcı adından @ işaretini kaldır (eğer varsa)
    username = username.strip().replace('@', '')
    
    # Profil verileri çek
    profile_data = get_profile_data(username)
    
    if "error" in profile_data:
        return jsonify({"error": profile_data["error"]})
    
    # Görselleştirme verilerini hazırla
    viz_data = prepare_visualization_data(profile_data)
    
    # Grafikleri oluştur
    graphs = create_graphs(viz_data, profile_data)
    
    # Tüm verileri bir araya getir
    result = {
        "profile": profile_data,
        "graphs": graphs
    }
    
    return jsonify(result)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5001)
