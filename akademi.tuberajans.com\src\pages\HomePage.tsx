import React, { useState, useEffect } from 'react';
import {
  FaTik<PERSON>,
  FaEye,
  FaEyeSlash,
  FaSignInAlt,
  FaWhatsapp
} from 'react-icons/fa';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useTikTok } from '../contexts/TikTokContext';

const ModernLoginPortal = () => {
  // State management
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [showForgotModal, setShowForgotModal] = useState(false);
  const [forgotEmail, setForgotEmail] = useState('');
  const [resetSent, setResetSent] = useState(false);
  const [showRegisterModal, setShowRegisterModal] = useState(false);
  const [registerEmail, setRegisterEmail] = useState('');
  const [registerUsername, setRegisterUsername] = useState('');
  const [registerPassword, setRegisterPassword] = useState('');
  const [registerSuccess, setRegisterSuccess] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Simplified background animation
  const navigate = useNavigate();

  // TikTok Context
  const { refreshTikTokUser } = useTikTok();

  // Auth context - Development modunda devre dışı
  // Development mode kontrolü - production'da false olacak
  const isDevMode = import.meta.env.VITE_DISABLE_AUTH === 'true' || import.meta.env.DEV;

  // Debug: Environment değişkenlerini kontrol et (sadece development'ta)
  if (import.meta.env.DEV) {
    console.log('Environment variables:', {
      VITE_DISABLE_AUTH: import.meta.env.VITE_DISABLE_AUTH,
      VITE_DEV_MODE: import.meta.env.VITE_DEV_MODE,
      DEV: import.meta.env.DEV,
      isDevMode: isDevMode
    });
  }

  let authContext: any = null;
  let loading = false;

  if (!isDevMode) {
    try {
      authContext = useAuth();
      loading = authContext.loading;
    } catch (error) {
      console.error('Auth context error:', error);
      authContext = { login: () => Promise.resolve({ success: false }), loading: false };
      loading = false;
    }
  }

  // Handle login submission
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    if (e) e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      if (isDevMode) {
        // Development modunda backend'e gitmeden direkt login
        console.log('Development mode: Bypassing API call');

        // Test kullanıcısı kontrolü
        if (username === 'tuberadmin' && password === 'Tuber123!') {
          const testUser = {
            id: 1,
            username: 'tuberadmin',
            email: '<EMAIL>',
            name: 'Tuber Admin',
            role: 'admin',
            profile_image: null
          };

          // Development modunda basit login
          localStorage.setItem('user', JSON.stringify(testUser));
          localStorage.setItem('token', 'dev-token-' + Date.now());
          navigate('/dashboard');
          return;
        } else {
          setError('Geliştirici modunda sadece tuberadmin/Tuber123! kullanılabilir.');
          setIsLoading(false);
          return;
        }
      }

      if (authContext) {
        // Production modunda AuthContext kullan
        const result = await authContext.login(username, password);
        if (result.success) {
          navigate('/dashboard');
        } else {
          setError(result.message || 'Giriş işlemi tamamlanamadı.');
        }
      } else {
        setError('Kimlik doğrulama sistemi başlatılamadı.');
      }
    } catch (error) {
      console.error('Login error:', error);
      setError('Giriş yapılırken bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle forgot password
  const handleForgotPassword = () => {
    setShowForgotModal(true);
  };
  // Handle sending reset email
  const handleSendReset = () => {
    if (!forgotEmail) return;
    setIsLoading(true);
    setTimeout(() => {
      setResetSent(true);
      setIsLoading(false);
      setTimeout(() => {
        setShowForgotModal(false);
        setResetSent(false);
        setForgotEmail('');
      }, 3000);
    }, 1500);
  };

  // Handle register button click
  const handleRegisterClick = () => {
    setShowRegisterModal(true);
  };

  // Handle register form submission
  const handleRegister = () => {
    if (!registerEmail || !registerUsername || !registerPassword) return;
    setIsLoading(true);
    setTimeout(() => {
      setRegisterSuccess(true);
      setIsLoading(false);
      setTimeout(() => {
        setShowRegisterModal(false);
        setRegisterSuccess(false);
        setRegisterEmail('');
        setRegisterUsername('');
        setRegisterPassword('');
      }, 3000);
    }, 1500);
  };

  // TikTok ile giriş yap
  const handleTikTokLogin = () => {
    try {
      const clientKey = 'awfw8k9nim1e8dmu';
      const redirectUri = encodeURIComponent('https://akademi.tuberajans.com/backend/api/tiktok-callback.php');
      const state = Math.random().toString(36).substring(2, 15);
      localStorage.setItem('tiktok_oauth_state', state);
      localStorage.setItem('tiktok_redirect_after', 'homepage'); // Anasayfaya dönmek için işaret
      const scope = 'user.info.basic,user.info.profile,user.info.stats,video.list';
      const url = `https://www.tiktok.com/v2/auth/authorize/?client_key=${clientKey}&response_type=code&scope=${scope}&redirect_uri=${redirectUri}&state=${state}`;
      window.location.href = url;
    } catch (error) {
      console.error('TikTok login error:', error);
      setError('TikTok ile giriş yapılırken bir hata oluştu.');
    }
  };

  // TikTok login success kontrolü
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('tiktok_login') === 'success') {
      console.log('Homepage: TikTok login success parametresi algılandı');
      // URL'den parametreyi temizle
      window.history.replaceState({}, document.title, window.location.pathname);

      // TikTok session'ını kontrol et
      const checkTikTokSession = async () => {
        try {
          const response = await fetch('/backend/api/tiktok_user.php?action=check_session');
          const data = await response.json();

          if (data.status === 'success' && data.has_tiktok_session) {
            console.log('Homepage: TikTok session bulundu, kullanıcı bilgileri yenileniyor...');
            // TikTok kullanıcı bilgilerini yenile
            await refreshTikTokUser();
            // Dashboard'a yönlendir
            navigate('/dashboard');
          } else {
            console.log('Homepage: TikTok session bulunamadı:', data);
            // Session yoksa da kullanıcı bilgilerini yenile (cache temizleme için)
            await refreshTikTokUser();
            setError('TikTok ile giriş tamamlanamadı. Lütfen tekrar deneyin.');
          }
        } catch (error) {
          console.error('Homepage: TikTok session kontrolü hatası:', error);
          setError('Giriş kontrolü yapılırken bir hata oluştu.');
        }
      };

      // Session'ın oluşması için biraz bekle
      setTimeout(checkTikTokSession, 2000);
    }
  }, [navigate]);

  return (
    <div className="h-screen relative bg-black overflow-hidden">
      {/* WhatsApp Button */}
      <a
        href="https://api.whatsapp.com/send?phone=+905309157188&text=Merhaba"
        className="fixed w-[45px] h-[45px] bottom-5 left-2.5 bg-[#25d366] text-white rounded-full text-center z-50 shadow-md flex items-center justify-center"
        rel="nofollow"
        target="_blank"
        aria-label="WhatsApp üzerinden Tuber Ajans ile iletişime geçin"
        title="WhatsApp üzerinden Tuber Ajans ile iletişime geçin"
      >
        <FaWhatsapp className="text-[28px]" />
      </a>

      {/* Background with office image overlay */}
      <div className="absolute inset-0 z-0">
        {/* Base dark background */}
        <div className="absolute inset-0 bg-black" />

        {/* Office background image with overlay */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-30"
          style={{
            backgroundImage: `url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800"><defs><pattern id="office" patternUnits="userSpaceOnUse" width="100" height="100"><rect width="100" height="100" fill="%23111111"/><rect x="10" y="10" width="80" height="80" fill="%23222222" opacity="0.3"/><rect x="20" y="20" width="60" height="60" fill="%23333333" opacity="0.2"/></pattern></defs><rect width="100%" height="100%" fill="url(%23office)"/></svg>')`
          }}
        />

        {/* Dark overlay to maintain readability */}
        <div className="absolute inset-0 bg-black/60" />

        {/* Subtle gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-red-900/10 via-transparent to-gray-900/20" />
      </div>
      {/* Forgot Password Modal */}
      {showForgotModal && (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-md flex items-center justify-center z-50">
          <div className="relative bg-gray-900 rounded-xl overflow-hidden border border-gray-700 p-6 max-w-md w-full mx-4 shadow-2xl">
            <div className="absolute inset-0 overflow-hidden">
              <div className="absolute -right-20 -top-20 w-40 h-40 bg-red-500/10 rounded-full filter blur-3xl"></div>
              <div className="absolute -left-20 -bottom-20 w-40 h-40 bg-gray-500/10 rounded-full filter blur-3xl"></div>
            </div>
            <div className="relative z-10">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-bold text-white">Şifremi Unuttum</h3>
                <button
                  onClick={() => {
                    setShowForgotModal(false);
                    setResetSent(false);
                    setForgotEmail('');
                  }}
                  className="text-gray-400 hover:text-white"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              {resetSent ? (
                <div className="text-center p-4">
                  <div className="bg-emerald-500/10 text-emerald-400 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <h4 className="text-lg font-medium text-white mb-2">Sıfırlama Bağlantısı Gönderildi</h4>
                  <p className="text-gray-400">
                    {forgotEmail} adresine şifre sıfırlama bağlantısı gönderdik.
                    Lütfen e-postanızı kontrol edin.
                  </p>
                </div>
              ) : (
                <>
                  <p className="text-gray-400 mb-6">
                    E-posta adresinizi girin, size şifre sıfırlama bağlantısı göndereceğiz.
                  </p>
                  <div className="mb-6">
                    <label htmlFor="email" className="block text-sm font-medium text-gray-200 mb-2">
                      E-posta Adresi
                    </label>
                    <input
                      id="email"
                      type="email"
                      value={forgotEmail}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => setForgotEmail(e.target.value)}
                      className="block w-full px-4 py-3 rounded-lg text-white bg-white/5 border border-white/10 focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all outline-none"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <button
                    onClick={handleSendReset}
                    disabled={isLoading || loading || !forgotEmail}
                    className="w-full flex items-center justify-center py-3 px-4 rounded-lg font-semibold text-white focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed transition-all transform hover:scale-105 shadow-lg"
                    style={{
                      backgroundColor: '#ff0e2a',
                      borderColor: '#ff0e2a'
                    }}
                    onMouseEnter={(e) => {
                      if (!isLoading && !loading) {
                        (e.target as HTMLButtonElement).style.backgroundColor = '#e80000';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (!isLoading && !loading) {
                        (e.target as HTMLButtonElement).style.backgroundColor = '#ff0e2a';
                      }
                    }}
                  >
                    {isLoading || loading ? (
                      <svg className="animate-spin h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    ) : (
                      "Sıfırlama Bağlantısı Gönder"
                    )}
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Register Modal */}
      {showRegisterModal && (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-md flex items-center justify-center z-50">
          <div className="relative bg-gray-900 rounded-xl overflow-hidden border border-gray-700 p-6 max-w-md w-full mx-4 shadow-2xl">
            <div className="absolute inset-0 overflow-hidden">
              <div className="absolute -right-20 -top-20 w-40 h-40 bg-red-500/10 rounded-full filter blur-3xl"></div>
              <div className="absolute -left-20 -bottom-20 w-40 h-40 bg-gray-500/10 rounded-full filter blur-3xl"></div>
            </div>
            <div className="relative z-10">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-bold text-white">Yeni Hesap Oluştur</h3>
                <button
                  onClick={() => {
                    setShowRegisterModal(false);
                    setRegisterSuccess(false);
                    setRegisterEmail('');
                    setRegisterUsername('');
                    setRegisterPassword('');
                  }}
                  className="text-gray-400 hover:text-white"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              {registerSuccess ? (
                <div className="text-center p-4">
                  <div className="bg-emerald-500/10 text-emerald-400 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <h4 className="text-lg font-medium text-white mb-2">Kayıt Başarılı!</h4>
                  <p className="text-gray-400">
                    Hesabınız başarıyla oluşturuldu. Şimdi giriş yapabilirsiniz.
                  </p>
                </div>
              ) : (
                <>
                  <p className="text-gray-400 mb-6">
                    Yeni bir hesap oluşturmak için aşağıdaki bilgileri doldurun.
                  </p>
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="register-email" className="block text-sm font-medium text-gray-200 mb-1">
                        E-posta Adresi
                      </label>
                      <input
                        id="register-email"
                        type="email"
                        value={registerEmail}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setRegisterEmail(e.target.value)}
                        className="block w-full px-4 py-2 rounded-lg text-white bg-white/5 border border-white/10 focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all outline-none text-sm"
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div>
                      <label htmlFor="register-username" className="block text-sm font-medium text-gray-200 mb-1">
                        Kullanıcı Adı
                      </label>
                      <input
                        id="register-username"
                        type="text"
                        value={registerUsername}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setRegisterUsername(e.target.value)}
                        className="block w-full px-4 py-2 rounded-lg text-white bg-white/5 border border-white/10 focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all outline-none text-sm"
                        placeholder="Kullanıcı adınızı girin"
                      />
                    </div>
                    <div>
                      <label htmlFor="register-password" className="block text-sm font-medium text-gray-200 mb-1">
                        Şifre
                      </label>
                      <input
                        id="register-password"
                        type="password"
                        value={registerPassword}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setRegisterPassword(e.target.value)}
                        className="block w-full px-4 py-2 rounded-lg text-white bg-white/5 border border-white/10 focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all outline-none text-sm"
                        placeholder="Güçlü bir şifre oluşturun"
                      />
                    </div>
                  </div>
                  <button
                    onClick={handleRegister}
                    disabled={isLoading || loading || !registerEmail || !registerUsername || !registerPassword}
                    className="w-full flex items-center justify-center py-3 px-4 rounded-lg font-semibold text-white focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed transition-all transform hover:scale-105 shadow-lg mt-6"
                    style={{
                      backgroundColor: '#ff0e2a',
                      borderColor: '#ff0e2a'
                    }}
                    onMouseEnter={(e) => {
                      if (!isLoading && !loading) {
                        (e.target as HTMLButtonElement).style.backgroundColor = '#e80000';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (!isLoading && !loading) {
                        (e.target as HTMLButtonElement).style.backgroundColor = '#ff0e2a';
                      }
                    }}
                  >
                    {isLoading || loading ? (
                      <svg className="animate-spin h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    ) : (
                      "Kayıt Ol"
                    )}
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      )}
      {/* Arka plan görseli - Ana sitedeki slider görseli */}
      <div className="absolute inset-0">
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `url('/images/arkaplannew.webp')`,
            backgroundPosition: 'center center',
            backgroundSize: 'cover'
          }}
        ></div>
        {/* Overlay gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/80 via-black/60 to-red-900/40"></div>
        {/* Subtle pattern */}
        <div className="absolute inset-0 opacity-20" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.03) 1px, transparent 1px)`,
          backgroundSize: '50px 50px'
        }}></div>
      </div>

      {/* Split screen layout - Altın oran (3:2) */}
      <div className="relative z-10 flex h-screen flex-col lg:flex-row">
        {/* Sol bölüm - Portal tanıtımı (3/5 genişlik) */}
        <div className="relative lg:w-3/5 overflow-hidden lg:flex flex-col justify-center items-start hidden">
          {/* Content - Login formu ile orta hizalı */}
          <div className="relative z-10 p-12 lg:p-24 lg:ml-8 flex flex-col items-start justify-center h-full max-w-4xl lg:-mt-16">
            {/* Logo */}
            <div className="mb-2 mt-0">
              <img
                src="/images/logotuber1.png"
                alt="Tuber Ajans Logo"
                className="h-20 w-auto select-none"
                style={{objectFit: 'contain'}}
              />
            </div>

            {/* Main heading - Yayıncı Portalı için özelleştirilmiş */}
            <div className="text-left">
              <p className="text-base font-semibold mb-3 tracking-wide uppercase" style={{ color: '#ff0e2a' }}>
                Tuber Akademi Yayıncı Portalı
              </p>
              <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-4" style={{ lineHeight: '1.4' }}>
                <span className="text-white">Yayıncılık</span><br />
                <span className="text-white">yolculuğunuz burada.</span>
              </h1>
              <p className="text-lg text-gray-300 max-w-2xl mb-6 leading-relaxed">
                Özel eğitimlerimizle becerilerinizi geliştirin, diğer yayıncılarla beyin fırtınası yapın,
                teknik destek alın ve topluluk ortamında birlikte büyüyün.
              </p>

              {/* Features List */}
              <div className="space-y-3 mb-4">
                <div className="flex items-center text-gray-300 text-base">
                  <div className="w-2 h-2 rounded-full mr-3" style={{ backgroundColor: '#ff0e2a' }}></div>
                  <span>Özel eğitim içerikleri ve canlı webinarlar</span>
                </div>
                <div className="flex items-center text-gray-300 text-base">
                  <div className="w-2 h-2 rounded-full mr-3" style={{ backgroundColor: '#ff0e2a' }}></div>
                  <span>Yayıncı topluluğu ve beyin fırtınası ortamı</span>
                </div>
                <div className="flex items-center text-gray-300 text-base">
                  <div className="w-2 h-2 rounded-full mr-3" style={{ backgroundColor: '#ff0e2a' }}></div>
                  <span>7/24 teknik destek ve ihlal yönetimi</span>
                </div>
                <div className="flex items-center text-gray-300 text-base">
                  <div className="w-2 h-2 rounded-full mr-3" style={{ backgroundColor: '#ff0e2a' }}></div>
                  <span>Özel etkinlikler ve networking fırsatları</span>
                </div>
              </div>


            </div>
          </div>
        </div>
        {/* Sağ bölüm - Login formu (2/5 genişlik) */}
        <div className="lg:w-2/5 flex items-center justify-center p-4 lg:p-12 lg:pr-8 lg:mt-8 relative z-10 h-full">
          <div className="w-full max-w-md lg:-ml-40 lg:mt-12">
            {/* Mobile logo */}
            <div className="lg:hidden mb-6 text-center">
              <img
                src="/images/logotuber1.png"
                alt="Tuber Ajans Logo"
                className="h-24 w-auto select-none mx-auto"
                style={{objectFit: 'contain'}}
              />
            </div>
            {/* Login container */}
            <div className="bg-gray-900/60 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-2xl p-6 lg:p-8 transition-all duration-300">
              <div className="text-center mb-4">
                <h2 className="text-xl lg:text-2xl font-bold text-white mb-2">Yayıncı Portalına Giriş</h2>
                <p className="text-gray-300 text-xs lg:text-sm">Özel eğitimlere, topluluk ortamına, teknik desteğe ve etkinliklere erişim sağlayın</p>
              </div>
              {/* Error message */}
              {error && (
                <div className="mb-6 p-4 rounded-lg text-sm bg-red-900/30 border border-red-500/30 text-red-400">
                  <div className="flex">
                    <svg className="h-5 w-5 mr-2 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                    {error}
                  </div>
                </div>
              )}
              {/* Login form */}
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label htmlFor="username" className="block text-sm font-medium text-gray-200 mb-1">
                    Kullanıcı Adı
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <FaTiktok className="h-4 w-4 text-gray-500" />
                    </div>
                    <input
                      id="username"
                      type="text"
                      value={username}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => setUsername(e.target.value)}
                      className="block w-full pl-10 pr-3 py-3 rounded-lg text-white bg-gray-800/50 border border-gray-600 focus:ring-2 focus:border-transparent transition-all outline-none text-sm"
                      style={{
                        '--tw-ring-color': '#ff0e2a'
                      } as React.CSSProperties}
                      onFocus={(e) => {
                        e.target.style.borderColor = '#ff0e2a';
                        e.target.style.boxShadow = '0 0 0 2px rgba(255, 14, 42, 0.2)';
                      }}
                      onBlur={(e) => {
                        e.target.style.borderColor = '#4b5563';
                        e.target.style.boxShadow = 'none';
                      }}
                      placeholder="Kullanıcı adınızı girin"
                      autoComplete="username"
                    />
                  </div>
                </div>
                <div>
                  <div className="flex items-center justify-between mb-1">
                    <label htmlFor="password" className="block text-sm font-medium text-gray-200">
                      Şifre
                    </label>
                    <button
                      type="button"
                      onClick={handleForgotPassword}
                      className="text-xs font-medium transition-colors"
                      style={{ color: '#ff0e2a' }}
                      onMouseEnter={(e) => (e.target as HTMLButtonElement).style.color = '#e80000'}
                      onMouseLeave={(e) => (e.target as HTMLButtonElement).style.color = '#ff0e2a'}
                    >
                      Şifremi Unuttum
                    </button>
                  </div>
                  <div className="relative">
                    <input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      value={password}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => setPassword(e.target.value)}
                      className="block w-full pr-10 px-3 py-3 rounded-lg text-white bg-gray-800/50 border border-gray-600 focus:ring-2 focus:border-transparent transition-all outline-none text-sm"
                      style={{
                        '--tw-ring-color': '#ff0e2a'
                      } as React.CSSProperties}
                      onFocus={(e) => {
                        e.target.style.borderColor = '#ff0e2a';
                        e.target.style.boxShadow = '0 0 0 2px rgba(255, 14, 42, 0.2)';
                      }}
                      onBlur={(e) => {
                        e.target.style.borderColor = '#4b5563';
                        e.target.style.boxShadow = 'none';
                      }}
                      placeholder="Şifrenizi girin"
                      autoComplete="current-password"
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="text-gray-400 hover:text-gray-300 focus:outline-none"
                      >
                        {showPassword ? <FaEyeSlash className="h-4 w-4" /> : <FaEye className="h-4 w-4" />}
                      </button>
                    </div>
                  </div>
                </div>
                <div className="flex items-center">
                  <input
                    id="remember-me"
                    name="remember-me"
                    type="checkbox"
                    checked={rememberMe}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => setRememberMe(e.target.checked)}
                    className="h-4 w-4 rounded bg-gray-800 border-gray-600 focus:ring-offset-gray-900"
                    style={{
                      accentColor: '#ff0e2a'
                    }}
                  />
                  <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-300">
                    Beni Hatırla
                  </label>
                </div>
                <button
                  type="submit"
                  disabled={loading}
                  className="w-full flex items-center justify-center py-3 px-4 rounded-lg font-semibold text-white transition-all duration-300 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                  style={{
                    backgroundColor: '#ff0e2a',
                    borderColor: '#ff0e2a'
                  }}
                  onMouseEnter={(e) => {
                    if (!loading) {
                      (e.target as HTMLButtonElement).style.backgroundColor = '#e80000';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!loading) {
                      (e.target as HTMLButtonElement).style.backgroundColor = '#ff0e2a';
                    }
                  }}
                >
                  {loading ? (
                    <svg className="animate-spin h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  ) : (
                    <>
                      <FaSignInAlt className="mr-2 h-5 w-5" />
                      Giriş Yap
                    </>
                  )}
                </button>
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-600"></div>
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="px-3 bg-gray-900 text-gray-400 text-xs">veya</span>
                  </div>
                </div>
                <button
                  type="button"
                  onClick={handleTikTokLogin}
                  className="w-full flex items-center justify-center py-3 px-4 rounded-lg font-medium bg-gray-800/50 hover:bg-gray-700/50 text-white border border-gray-600 transition-all text-sm"
                >
                  <FaTiktok className="mr-2 h-5 w-5" />
                  TikTok ile Giriş Yap
                </button>
              </form>
            </div>
            {/* Sign up link */}
            <div className="mt-4 text-center">
              <p className="text-gray-400 text-sm">
                Henüz bir hesabın yok mu?{' '}
                <button
                  onClick={handleRegisterClick}
                  className="font-medium transition-colors bg-transparent border-none cursor-pointer p-0"
                  style={{ color: '#ff0e2a' }}
                  onMouseEnter={(e) => (e.target as HTMLButtonElement).style.color = '#e80000'}
                  onMouseLeave={(e) => (e.target as HTMLButtonElement).style.color = '#ff0e2a'}
                >
                  Kayıt Ol
                </button>
              </p>
            </div>
            {/* Footer */}
            <div className="mt-4 text-center">
              <p className="text-xs text-gray-500">
                © 2025 Tuber Ajans. Tüm hakları saklıdır.
              </p>
              <div className="mt-1 flex justify-center space-x-3">
                <Link to="/privacy" className="text-xs text-gray-500 hover:text-gray-400 transition-colors">
                  Gizlilik Politikası
                </Link>
                <Link to="/terms" className="text-xs text-gray-500 hover:text-gray-400 transition-colors">
                  Kullanım Şartları
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModernLoginPortal;
